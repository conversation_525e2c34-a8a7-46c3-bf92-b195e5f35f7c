# pkg
pkg/

# src
src/github.com/
src/golang.org/
src/google.golang.org/
src/kit/
src/app/protos
src/app/logic/service/version.go
src/app/logic/goxml/*_info.go
src/app/goxml/*_info_[0-9]*.go
src/app/goxml/*_info.go
src/app/logic/db/redisop/
src/app/cross/db/redisop/
src/app/crossmaster/db/redisop/
src/app/knil
src/app/knil_config.yaml

# deps
deps/rapidjson/
deps/protobuf/lib/libprotoc.a
deps/zebra/include/
deps/zebra/deps/
deps/zebra/libzebra.a

# bin
bin/repair
bin/mockRobot
bin/gateway
bin/service
bin/eagle
bin/robot
bin/parrot
bin/version
bin/mirror
bin/start_logic.sh
bin/stop_logic.sh
bin/asmfmt
bin/dlv
bin/errcheck
bin/fillstruct
bin/gocode
bin/gocode-gomod
bin/godef
bin/gogetdoc
bin/goimports
bin/golangci-lint
bin/golint
bin/gomodifytags
bin/gopls
bin/gorename
bin/gotags
bin/guru
bin/iferr
bin/impl
bin/keyify
bin/motion
bin/protoc-gen-go
bin/protoc-gen-gofast
bin/wrobot
bin/battle
bin/redis-pb-cli
bin/.liner_history
bin/data-check.exe
bin/cross-robot
bin/data-check-linux
bin/benchmark
bin/crossRepair
bin/link-summon-simulation.exe
bin/treerobot
bin/kafka-consumer

# tools

# config
config/gateway.xml
config/debug.changetime

# protos
protos/out

# data
data/

# others
*.swp
tmpdata*/
src/app/.vscode/settings.json
src/app/service/log/
src/app/version/version.go
src/app/goxml/beans.go
bin_win/
