#!/bin/bash

export PATH=$PROJECT_PATH/tools:$PATH

# git clone or pull
if [ -n "$1" ]; then
  if [ ! -d $PROJECT_PATH/data ]; then
    <NAME_EMAIL>:ngame-backend/data.git $PROJECT_PATH/data
  else
    cd $PROJECT_PATH/data
    echo "git pull $PROJECT_PATH/data"
    <NAME_EMAIL>:ngame-backend/data.git
  fi
fi
# rm new files in goxml
git ls-files $PROJECT_PATH/src/app/goxml/ --others |xargs -i rm {}

xmlc -simple=false -in $PROJECT_PATH/data/config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/resource_type_info.xml -out $PROJECT_PATH/src/app/goxml/
#xmlc -simple=false -in $PROJECT_PATH/data/bag_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/item_selective_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/item_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/fragment_info.xml  -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/item_token_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/task_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/task_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 value
xmlc -simple=false -in $PROJECT_PATH/data/buy_price_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/function_info.xml -out $PROJECT_PATH/src/app/goxml/ -i32 season_id
xmlc -simple=false -in $PROJECT_PATH/data/dungeon_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/dungeon_first_drop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/player_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/new_drop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/skill_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 dec_attr_value_1
xmlc -simple=false -in $PROJECT_PATH/data/skill_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/buff_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/buff_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 eff1_param_1,eff1_param_2,eff1_param_3,eff2_param_1,eff2_param_2,eff2_param_3,eff3_param_1,eff3_param_2,eff3_param_3
xmlc -simple=false -in $PROJECT_PATH/data/battle_para_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 value
xmlc -in $PROJECT_PATH/data/passive_skill_trigger_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/passive_skill_effect_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/passive_skill_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 eff_param_1,eff_param_2,eff_param_3,eff_param2_1,eff_param2_2,eff_param2_3,eff_param3_1,eff_param3_2,eff_param3_3,eff_param4_1,eff_param4_2,eff_param4_3
xmlc -simple=false -in $PROJECT_PATH/data/hero_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_data_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_stage_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 target_stage
xmlc -simple=false -in $PROJECT_PATH/data/hero_star_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 hp_1,attack_1,phy_def_1,mag_def_1,hp_2,attack_2,phy_def_2,mag_def_2,hp_3,attack_3,phy_def_3,mag_def_3,hp_4,attack_4,phy_def_4,mag_def_4,hp_pct,attack_pct,phy_def_pct,mag_def_pct,fixed_dam_pct,fixed_dam_reduce_pct,speed_pct
xmlc -in $PROJECT_PATH/data/game_initial_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monster_group_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 power
xmlc -simple=false -in $PROJECT_PATH/data/monster_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monster_info_7.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monster_info_40.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monster_info_55.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/race_buff_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 buff_value_1,buff_value_2,buff_value_3
xmlc -simple=false -in $PROJECT_PATH/data/precious_res_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/summon_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/summon_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/summon_class_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/summon_special_class_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/mail_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/shop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/shop_random_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/shop_random_goods_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/shop_regular_goods_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/equip_attr_coe_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_enchant_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_enchant_rand_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_enchant_master_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_evolution_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_refine_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_refine_technique_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/equip_strength_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/ranking_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_info.xml -out $PROJECT_PATH/src/app/goxml/  -i64 jump_need_power
xmlc -simple=false -in $PROJECT_PATH/data/arena_match_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 high_min_add,high_max_add,mid_min_add,mid_max_add,low_min_add,low_max_add
xmlc -simple=false -in $PROJECT_PATH/data/arena_fight_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_bot_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_praise_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/number_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_skill_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 st1_damage_threshold,st2_damage_threshold,st3_damage_threshold,st4_damage_threshold,st5_damage_threshold
xmlc -simple=false -in $PROJECT_PATH/data/artifact_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_star_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_strength_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_forge_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_talent_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/artifact_fragment_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/gem_compose_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/gem_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/gem_attr_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 attr_value_1,attr_value_2
xmlc -simple=false -in $PROJECT_PATH/data/avatar_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/avatar_item_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/trial_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/trial_copy_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/trial_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_hero_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_magic_skill_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_suit_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_fragment_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/gold_buy_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/gold_buy_refresh_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/dispatch_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/dispatch_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/dispatch_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_awake_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/ranking_achievement_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 goal
xmlc -simple=false -in $PROJECT_PATH/data/maze_map_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_difficulty_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 attr_value_1,attr_value_2,attr_value_3,attr_value_4,attr_value_5
xmlc -simple=false -in $PROJECT_PATH/data/maze_buff_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 dec_attr_value_1
xmlc -simple=false -in $PROJECT_PATH/data/maze_question_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_answer_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/maze_scroll_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_constant_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_bot_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monster_data_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 attack,hp,phy_def,mag_def -i64 power

xmlc -simple=false -in $PROJECT_PATH/data/mirage_copy_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/mirage_affix_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 dec_attr_value_1
xmlc -simple=false -in $PROJECT_PATH/data/mirage_affix_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/mirage_victory_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/mirage_star_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/mirage_hurdle_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 pass_power
xmlc -simple=false -in $PROJECT_PATH/data/guild_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/belltower_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/tales_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tales_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tales_elite_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/guild_log_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/guild_badge_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/xor_key.xml -out $PROJECT_PATH/src/app/goxml/ -bytes key
xmlc -simple=false -in $PROJECT_PATH/data/memory_chip_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_reward_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 damage_node
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_chapter_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 damage
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_chapter_reward.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_boss_hp_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 class1_damage,class2_damage,class3_damage,class4_damage,class5_damage,class6_damage,class7_damage,class8_damage,class9_damage,class10_damage
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/bot_head_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_talent_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_donate_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_collective_donate_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/master_data_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guidance_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guidance_skip_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/formation_info.xml -out $PROJECT_PATH/src/app/goxml/ -u32 nonempty
xmlc -simple=false -in $PROJECT_PATH/data/carnival_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/carnival_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/god_present_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/god_present_class_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/god_present_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/god_present_recovery_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/god_present_change_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/god_present_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_seven_day_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monster_artifact_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_archive_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_fetter_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/medal_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/medal_task_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 value
xmlc -simple=false -in $PROJECT_PATH/data/maze_buy_revive_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/crystal_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/crystal_emblem_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/crystal_gem_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/chat_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/forecast_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/forecast_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/recharge_product_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/recharge_goods_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/link_skill_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/vip_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/vip_privilege_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_recharge_shop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_recharge_gift_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/crystal_achieve_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 attr_value_1,attr_value_2,attr_value_3
xmlc -simple=false -in $PROJECT_PATH/data/firstrecharge_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/towerstar_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/towerstar_chapter_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 unlock_power
xmlc -simple=false -in $PROJECT_PATH/data/towerstar_condition_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/towerstar_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/towerstar_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_guardian_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_task_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_cures_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/shop_box_random_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_box_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/monthlycard_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/pass_info.xml -out $PROJECT_PATH/src/app/goxml/ -u32 vip_value,vip2_value
xmlc -simple=false -in $PROJECT_PATH/data/pass_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_pushgift_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_pushgift_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_pushgift_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/bot_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_division_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_division_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/arena_division_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/share_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/skill_target_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wishlist_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wishlist_range_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wishlist_guarantee_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/pass_cycle_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_task_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/chat_admin_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/chat_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/battle_suppress_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 dmg_rate_1,dmg_rate_2
xmlc -simple=false -in $PROJECT_PATH/data/attribute_info.xml -out $PROJECT_PATH/src/app/goxml/	-i64 value
xmlc -in $PROJECT_PATH/data/old_emblem_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_init_data_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/conversion_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_level_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_config_info.xml -out $PROJECT_PATH/src/app/goxml/ -str str_value
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_bless_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_trust_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_gifts_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_elite_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_skin_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_contract_treat_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_tales_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_goddess_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/raise_passive_skill_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/vip_product_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_talent_condition_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/maze_model_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 unlock_power
xmlc -simple=false -in $PROJECT_PATH/data/flower_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_lv_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_occupy_timber_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_occupy_base_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_occupy_jungle_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_plant_goblin_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_plant_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_plant_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_plant_score_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_plant_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/flower_assist_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/flower_message_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/gem_level_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 attr_value1,attr_value2
# xmlc -simple=false -in $PROJECT_PATH/data/hero_star_awake_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/mirage_fight_effect_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/trial_hero_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/passive_skill_probability_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/team_init_data_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_month_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_month_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_month_tasks_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/daily_wish_weight_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/daily_wish_open_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/daily_wish_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_bot_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_customize_hero_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_customize_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/emblem_customize_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/link_summon_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_link_summon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/link_summon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_exchange_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_time_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_draw_category_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_draw_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_draw_drop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_draw_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_login_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_puzzle_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_activity_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_draw_optional_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_task_summon_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 value
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_task_active_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 value
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_task_star_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 value
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_default_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 uniq_id
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/link_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/number_max_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/number_max_add_task_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 value
xmlc -in $PROJECT_PATH/data/round_activity_sum_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/round_activity_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/round_activity_open_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_collection_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/goddess_collection_box_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 param1,param2,param3
xmlc -simple=false -in $PROJECT_PATH/data/divine_demon_summon_guaranteed_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_web_gift_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_debut_guarantee_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/link_book_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/wrestle_buff_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_season_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_season_fight_effect_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_season_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_season_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_exchange_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/buff_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/summon_activity_count_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/summon_activity_guarantee_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/chat_language_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_label_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_division_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_level_up_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_season_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_point_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_weekly_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/medal_skin_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/web_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/artifact_link_skill_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_weekly_base_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_activity_function_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_activity_open_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_activity_open_exchange_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_activity_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/drop_activity_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/daily_attendance_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/daily_attendance_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_daily_special_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_chest_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_sum_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/worldboss_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/worldboss_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/worldboss_difficulty_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/worldboss_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/worldboss_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/link_book_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/rite_type_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/rite_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/rite_rare_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/rite_mark_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 value,power
xmlc -simple=false -in $PROJECT_PATH/data/rite_power_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/rite_monster_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_season_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/skin_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_week_strategy_info.xml -out $PROJECT_PATH/src/app/goxml/ -u32 week_group
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_strategy_skill_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_season_strategy_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_story_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_story_shop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_story_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_story_layer_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_story_login_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/buff_condition_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/assistance_activity_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/assistance_activity_open_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_level_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 power
xmlc -simple=false -in $PROJECT_PATH/data/season_level_award_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_level_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_dungeon_layer_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_dungeon_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/formation_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_return_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_return_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_return_login_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_map_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_stone_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_box_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_drop_add_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_drop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_drop_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_buff_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/peak_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/peak_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/peak_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_countdown_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_balance_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_data_record_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/recharge_coupon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hero_awaken_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_return_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/ -out $PROJECT_PATH/src/app/goxml/ -config_file=$PROJECT_PATH/tools/config.yaml
xmlc -simple=false -in $PROJECT_PATH/data/emblem_ascend_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_quit_cd_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_blessing_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_contribution_award_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_fatigue_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_goddess_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_home_award_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/guild_sand_table_land_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_map_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_map_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_point_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_task_award_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_monster_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_link_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_hero_add_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_first_drop_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_season_quick_privilege_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/disorderland_boss_weak_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/new_year_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_artifact_add_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_build_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_build_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_hero_work_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/artifact_recycle_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_flash_back_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_flash_back_data_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_flash_back_params_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/remain_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/remain_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/remain_bless_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/remain_fragment_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/remain_recycle_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/remain_star_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_add_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_add_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_medal_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_medal_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_division_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_division_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_match_info.xml -out $PROJECT_PATH/src/app/goxml/ -i64 high_min_add,high_max_add,mid_min_add,mid_max_add,low_min_add,low_max_add
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_arena_bot_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_turntable_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_turntable_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_turntable_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_turntable_buff_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_turntable_reward_show_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hot_rank_score_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/hot_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_mobilization_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_mobilization_score_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_mobilization_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/guild_mobilization_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_mobilization_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/boss_rush_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_puzzle_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_puzzle_copy_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_puzzle_aim_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_compliance_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_compliance_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_ore_boss_award_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_ore_boss_hp_info.xml -out $PROJECT_PATH/src/app/goxml/ -u64 damage_range_1,damage_range_2,damage_range_3,damage_range_4,damage_range_5,damage_range_6,damage_range_7,damage_range_8,damage_range_9,damage_range_10
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_ore_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/guild_sand_table_ore_monster_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_tech_base_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_tech_level_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_sand_table_tech_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_feeding_condition_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_feeding_gifts_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_feeding_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/select_summon_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/select_summon_hero_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/select_summon_group_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/select_summon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_lifelong_gift_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/guild_dungeon_server_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_start_tower_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_tower_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_mirage_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_compliance_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_compliance_rank_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_compliance_stage_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_compliance_emblem_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_compliance_stage_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_door_buff_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_door_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_door_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_door_rest_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_door_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/recharge_refund_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_web_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/shoot_game_stage_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_rank_timelimit_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/activity_coupon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/daily_attendance_hero_reward_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_altar_buff_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_altar_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_connect_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_master_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_monster_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_position_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_trade_event_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_trade_goods_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_trade_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/season_map_trigger_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_pokemon_dungeon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_pokemon_task_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_pokemon_fight_effect_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/tower_pokemon_rank_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/pokemon_summon_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -simple=false -in $PROJECT_PATH/data/pokemon_summon_config_info.xml -out $PROJECT_PATH/src/app/goxml/
xmlc -in $PROJECT_PATH/data/tower_pokemon_goods_info.xml -out $PROJECT_PATH/src/app/goxml/
#########################################################################################
cd $PROJECT_PATH/src/app/goxml/ && gofmt -w -s *.go