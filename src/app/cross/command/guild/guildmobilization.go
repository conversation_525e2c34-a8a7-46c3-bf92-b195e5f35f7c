package guild

import (
	"app/cross/activity/guild"
	ag "app/cross/activity/guild"
	"app/cross/command/base"
	"app/goxml"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/ret"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitGuildMobilization(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationGetData), &L2CSGuildMobilizationGetDataCommand{}, state)               // 获取数据
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationAcceptTask), &L2CSGuildMobilizationAcceptTaskCommand{}, state)         // 接取任务
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationSignTask), &L2CSGuildMobilizationSignTaskCommand{}, state)             // 标记任务
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationFinishTaskLogs), &L2CSGuildMobilizationFinishTaskLogsCommand{}, state) // 积分日志
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationPersonalRank), &L2CSGuildMobilizationPersonalRankCommand{}, state)     // 个人排行
	//cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationRecvScoreLevel), &L2CSGuildMobilizationRecvScoreLevelCommand{}, state)     // 领取积分等级奖励
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationFreshTask), &L2CSGuildMobilizationFreshTaskCommand{}, state)               // 刷新任务
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationBuyTimes), &L2CSGuildMobilizationBuyTimesCommand{}, state)                 // 购买次数
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationSysFinishedTask), &L2CSGuildMobilizationSysFinishedTaskCommand{}, state)   // 完成任务
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationEditMessageBoard), &L2CSGuildMobilizationEditMessageBoardCommand{}, state) // 编辑公告
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationGiveUpTask), &L2CSGuildMobilizationGiveUpTaskCommand{}, state)             // 放弃任务
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationScoreAward), &L2CSGuildMobilizationScoreAwardCommand{}, state)             // 积分领奖
	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationCancelTask), &L2CSGuildMobilizationCancelTaskCommand{}, state)             // 取消任务

	cmds.Register(uint32(l2c.ID_MSG_L2CS_GuildMobilizationAddScoreForGM), &L2CS_GuildMobilizationAddScoreForGMCommand{}, state)
}

type L2CSGuildMobilizationGetDataCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationGetDataCommand) Return(msg *l2c.CS2L_GuildMobilizationGetData, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationGetData, msg)
	return true
}

func (c *L2CSGuildMobilizationGetDataCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationGetData{
		Rsp: &cl.L2C_GuildMobilizationGetData{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGetData: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGetData. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGetData. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGetData. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	data := guild.GetMobilizationData(roundInfo)
	smsg.Rsp.Score = data.Score
	smsg.Rsp.CanFreshTimes = data.CanFreshTimes
	smsg.Rsp.BoughtFreshTimes = data.BoughtFreshTimes
	for _, task := range data.Tasks {
		smsg.Rsp.Tasks = append(smsg.Rsp.Tasks, task.Clone())
	}
	if data.Message != nil {
		smsg.Rsp.Message = data.Message.Clone()
	}

	userM := guildM.GetUserManager()
	user := userM.GetUserWithInit(c.Msg.UID, c.ServerID())
	userData := user.GetMobilizationData(roundInfo)
	smsg.Rsp.ScoreLevels = userData.ScoreLevels
	smsg.Rsp.CanAcceptTaskTimes = userData.CanAcceptTaskTimes
	smsg.Rsp.BoughtTaskTimes = userData.BoughtTaskTimes
	smsg.Rsp.UserScore = user.MobCalcTotalScore(userData.GuildScore)
	smsg.Rsp.ReceivedIds = userData.ReceivedIds
	return c.Return(smsg, uint32(ret.RET_OK))
}

type L2CSGuildMobilizationAcceptTaskCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationAcceptTaskCommand) Return(msg *l2c.CS2L_GuildMobilizationAcceptTask, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationAcceptTask, msg)
	return true
}

func (c *L2CSGuildMobilizationAcceptTaskCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationAcceptTask{
		Rsp: &cl.L2C_GuildMobilizationAcceptTask{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationAcceptTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationAcceptTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationAcceptTask: cmsg:%s", c.Msg.UID, cmsg)
	// edit here

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAcceptTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAcceptTask. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAcceptTask. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAcceptTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("%d cant find guild user %d", guild.GetID(), c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}
	guild.MobAcceptTask(roundInfo, guildUser, cmsg, smsg.Rsp)
	return c.Return(smsg, smsg.Rsp.Ret)
}

type L2CSGuildMobilizationSignTaskCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationSignTaskCommand) Return(msg *l2c.CS2L_GuildMobilizationSignTask, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationSignTask, msg)
	return true
}

func (c *L2CSGuildMobilizationSignTaskCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationSignTask{
		Rsp: &cl.L2C_GuildMobilizationSignTask{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationSignTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationSignTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationSignTask: cmsg:%s", c.Msg.UID, cmsg)

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSignTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSignTask. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSignTask. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSignTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}

	if !guild.IsGuildManager(c.Msg.UID) {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSignTask. not manager. partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}
	guild.MobSignTask(cmsg, roundInfo, smsg.Rsp)
	return c.Return(smsg, smsg.Rsp.Ret)
}

type L2CSGuildMobilizationFinishTaskLogsCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationFinishTaskLogsCommand) Return(msg *l2c.CS2L_GuildMobilizationFinishTaskLogs, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationFinishTaskLogs, msg)
	return true
}

func (c *L2CSGuildMobilizationFinishTaskLogsCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationFinishTaskLogs{
		Rsp: &cl.L2C_GuildMobilizationFinishTaskLogs{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationFinishTaskLogs{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationFinishTaskLogs Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationFinishTaskLogs: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFinishTaskLogs: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFinishTaskLogs. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFinishTaskLogs. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFinishTaskLogs. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	smsg.Rsp.Logs = guild.MobGetFinishTaskLogs(roundInfo)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type L2CSGuildMobilizationPersonalRankCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationPersonalRankCommand) Return(msg *l2c.CS2L_GuildMobilizationPersonalRank, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationPersonalRank, msg)
	return false
}

func (c *L2CSGuildMobilizationPersonalRankCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationPersonalRank{
		Rsp: &cl.L2C_GuildMobilizationPersonalRank{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationPersonalRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationPersonalRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationPersonalRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationPersonalRank: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationPersonalRank. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationPersonalRank. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationPersonalRank. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	smsg.Rsp.Ranks = guild.MobGetPersonalRankData(roundInfo)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type L2CSGuildMobilizationRecvScoreLevelCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationRecvScoreLevelCommand) Return(msg *l2c.CS2L_GuildMobilizationRecvScoreLevel, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationRecvScoreLevel, msg)
	return true
}

func (c *L2CSGuildMobilizationRecvScoreLevelCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationRecvScoreLevel{
		Rsp: &cl.L2C_GuildMobilizationRecvScoreLevel{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationRecvScoreLevel{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationRecvScoreLevel Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationRecvScoreLevel: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationRecvScoreLevel: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationRecvScoreLevel. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationRecvScoreLevel. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationRecvScoreLevel. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("%d cant find guild user %d", guild.GetID(), c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}
	guildUser.MobRecvScoreLevel(roundInfo, smsg.Rsp)
	return c.Return(smsg, uint32(smsg.Rsp.Ret))
}

type L2CSGuildMobilizationFreshTaskCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationFreshTaskCommand) Return(msg *l2c.CS2L_GuildMobilizationFreshTask, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationFreshTask, msg)
	return true
}

func (c *L2CSGuildMobilizationFreshTaskCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationFreshTask{
		Rsp: &cl.L2C_GuildMobilizationFreshTask{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationFreshTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationFreshTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationFreshTask: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFreshTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFreshTask. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFreshTask. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFreshTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !guild.IsGuildManager(c.Msg.UID) {
		l4g.Errorf("user:%d L2CS_GuildMobilizationFreshTask. not manager. partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}
	guild.MobFreshTask(roundInfo, cmsg, smsg.Rsp)
	return c.Return(smsg, uint32(smsg.Rsp.Ret))
}

type L2CSGuildMobilizationBuyTimesCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationBuyTimesCommand) Return(msg *l2c.CS2L_GuildMobilizationBuyTimes, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationBuyTimes, msg)
	return true
}

func (c *L2CSGuildMobilizationBuyTimesCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationBuyTimes{
		Rsp: &cl.L2C_GuildMobilizationBuyTimes{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationBuyTimes{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationBuyTimes Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Infof("user: %d L2CS_GuildMobilizationBuyTimes: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationBuyTimes: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationBuyTimes. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationBuyTimes. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	smsg.Rsp.Type = cmsg.Type
	smsg.Rsp.NeedDiamond = cmsg.NeedDiamond
	smsg.Rsp.BuyTimes = cmsg.BuyTimes
	if cmsg.Type == 1 {
		guild := guildM.GetGuildByUser(c.Msg.UID)
		if guild == nil {
			l4g.Errorf("user:%d L2CS_GuildMobilizationFreshTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
				c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
			return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
		}
		guild.MobBuyFreshTimes(roundInfo, cmsg, smsg.Rsp)
	} else if cmsg.Type == 2 {
		guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
		if guildUser == nil {
			l4g.Errorf("cant find guild user %d", c.Msg.UID)
			return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
		}
		guildUser.MobBuyAcceptTimes(roundInfo, cmsg, smsg.Rsp)
	} else {
		l4g.Errorf("user:%d L2CS_GuildMobilizationBuyTimes.type err, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	return c.Return(smsg, smsg.Rsp.Ret)
}

type L2CSGuildMobilizationSysFinishedTaskCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationSysFinishedTaskCommand) Return(msg *l2c.CS2L_GuildMobilizationSysFinishedTask, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationSysFinishedTask, msg)
	return true
}

func (c *L2CSGuildMobilizationSysFinishedTaskCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationSysFinishedTask{
		Rsp: &cl.L2C_GuildMobilizationUpdateTaskProgress{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationSysFinishedTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationSysFinishedTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationSysFinishedTask: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSysFinishedTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSysFinishedTask. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSysFinishedTask. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSysFinishedTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("cant find guild user %d", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}
	for _, taskId := range cmsg.FinishedTasks {
		smsg.Rsp.FinishedTasks = append(smsg.Rsp.FinishedTasks, &cl.GuildMobilizationTaskProgress{
			TaskId:   taskId,
			IsFinish: true,
		})
	}
	guild.MobSysFinishedTasks(roundInfo, guildUser, now, cmsg.FinishedTasks)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type L2CS_GuildMobilizationAddScoreForGMCommand struct {
	base.Command
}

func (l *L2CS_GuildMobilizationAddScoreForGMCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_GuildMobilizationAddScoreForGM{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationAddScoreForGMCommand unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CS_GuildMobilizationAddScoreForGMCommand. recv from logic:%+v", l.Msg.UID, msg)

	guildM, ok := l.GetModule().(*ag.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAddScoreForGMCommand. get guildM error, partition:%d, logicSid:%d",
			l.Msg.UID, l.Partition(), l.ServerID())
		return false
	}

	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAddScoreForGMCommand. guildM not running, partition:%d, logicSid:%d msg:%+v",
			l.Msg.UID, l.Partition(), l.ServerID(), msg)
		return false
	}

	guild := guildM.GetGuildByUser(l.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationAddScoreForGMCommand. get guild error, partition:%d, logicSid:%d",
			l.Msg.UID, l.Partition(), l.ServerID())
		return false
	}
	guild.MobAddScoreForGM(l.Msg.UID, msg.Score)
	return true
}

type L2CSGuildMobilizationEditMessageBoardCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationEditMessageBoardCommand) Return(msg *l2c.CS2L_GuildMobilizationEditMessageBoard, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationEditMessageBoard, msg)
	return true
}

func (c *L2CSGuildMobilizationEditMessageBoardCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationEditMessageBoard{
		Rsp: &cl.L2C_GuildMobilizationEditMessageBoard{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationEditMessageBoard{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationEditMessageBoard Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationEditMessageBoard: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationEditMessageBoard: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationEditMessageBoard. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationEditMessageBoard. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationEditMessageBoard. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !guild.IsGuildManager(c.Msg.UID) {
		l4g.Errorf("user:%d L2CS_GuildMobilizationEditMessageBoard. not manager. partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}
	guild.MobEditMessageBoard(roundInfo, cmsg, smsg.Rsp)
	return c.Return(smsg, uint32(smsg.Rsp.Ret))
}

type L2CSGuildMobilizationGiveUpTaskCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationGiveUpTaskCommand) Return(msg *l2c.CS2L_GuildMobilizationGiveUpTask, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationGiveUpTask, msg)
	return true
}

func (c *L2CSGuildMobilizationGiveUpTaskCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationGiveUpTask{
		Rsp: &cl.L2C_GuildMobilizationGiveUpTask{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationGiveUpTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationGiveUpTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationGiveUpTask: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGiveUpTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGiveUpTask. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGiveUpTask. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGiveUpTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("%d cant find guild user %d", guild.GetID(), c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}

	guild.MobGiveUpTask(guildUser, roundInfo, c.Msg.UID, cmsg, smsg.Rsp)

	return c.Return(smsg, uint32(smsg.Rsp.Ret))
}

type L2CSGuildMobilizationScoreAwardCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationScoreAwardCommand) Return(msg *l2c.CS2L_GuildMobilizationScoreAward, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationScoreAward, msg)
	return true
}

func (c *L2CSGuildMobilizationScoreAwardCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationScoreAward{
		Rsp: &cl.L2C_GuildMobilizationScoreAward{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationScoreAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationScoreAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationScoreAward: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationScoreAward: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationScoreAward. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationScoreAward. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationScoreAward. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("%d cant find guild user %d", guild.GetID(), c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}

	// 判断是否能够领奖
	if !guildUser.MobCheckScoreIdsCanReceive(cmsg.Ids) {
		l4g.Infof("user:%d L2CS_GuildMobilizationScoreAward. invalid recv ids. partition %d logicSid %d ids %+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg.Ids)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	// 记录领奖状态
	guildUser.MobReceiveScoreIds(cmsg.Ids, smsg.Rsp)

	return c.Return(smsg, uint32(smsg.Rsp.Ret))
}

type L2CSGuildMobilizationCancelTaskCommand struct {
	base.Command
}

func (c *L2CSGuildMobilizationCancelTaskCommand) Return(msg *l2c.CS2L_GuildMobilizationCancelTask, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_GuildMobilizationCancelTask, msg)
	return true
}

func (c *L2CSGuildMobilizationCancelTaskCommand) Execute(ctx context.Context) bool {
	smsg := &l2c.CS2L_GuildMobilizationCancelTask{
		Rsp: &cl.L2C_GuildMobilizationCancelTask{},
	}
	cmsg := &l2c.L2CS_GuildMobilizationCancelTask{}
	beCmsg := &l2c.CS2L_GuildMobilizationBeCancelTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2CS_GuildMobilizationCancelTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	l4g.Debugf("user: %d L2CS_GuildMobilizationCancelTask: cmsg:%s", c.Msg.UID, cmsg)
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationCancelTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.GetModule().(*guild.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CS_GuildMobilizationCancelTask. get guildM error, partition:%d, logicSid:%d",
			c.Msg.UID, c.Partition(), c.ServerID())
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if !guildM.CheckRunning() {
		l4g.Errorf("user:%d L2CS_GuildMobilizationCancelTask. guildM not running, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationCancelTask. guild not exist, partition:%d, logicSid:%d msg:%+v",
			c.Msg.UID, c.Partition(), c.ServerID(), cmsg)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}

	beCancelUser := guildM.GetUserManager().GetUser(cmsg.Uid)
	if beCancelUser == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationCancelTask guild be cancel user:%d not exist",
			c.Msg.UID, cmsg.Uid)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}

	guildUser := guildM.GetUserManager().GetUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationCancelTask cant find guild user %d", guild.GetID(), c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}

	guild.MobCancelTask(beCancelUser, now, roundInfo, cmsg, smsg.Rsp, beCmsg)
	if beCmsg.Ret == uint32(ret.RET_OK) {
		c.SendCmdToLogic(beCancelUser.Sid(), beCancelUser.ID(), l2c.ID_MSG_CS2L_GuildMobilizationBeCancelTask, beCmsg)
	}
	return c.Return(smsg, uint32(smsg.Rsp.Ret))
}
