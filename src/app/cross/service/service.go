package service

import (
	"app/cross/activity/disorderland"
	"app/cross/activity/gst"
	"app/cross/activity/hotrank"
	"app/cross/activity/seasoncompliance"
	"app/cross/activity/seasonmap"
	"app/logic/helper/log"
	"context"
	"flag"
	"path/filepath"
	"strconv"
	"strings"
	"sync"

	"gitlab.qdream.com/platform/proto/da"

	"app/cross/activity"
	"app/cross/activity/guild"
	"app/cross/activity/peak"
	"app/cross/activity/rank"
	"app/cross/activity/router"
	"app/cross/activity/seasonarena"
	"app/cross/activity/worldboss"
	"app/cross/activity/wrestle"
	cdb "app/cross/command/db"
	mdisorderland "app/cross/command/disorderland"
	mguild "app/cross/command/guild"
	mhotrank "app/cross/command/hotrank"
	"app/cross/command/master"
	mpeak "app/cross/command/peak"
	mrank "app/cross/command/rank"
	rdisorderland "app/cross/command/redis/disorderland"
	rguild "app/cross/command/redis/guild"
	rhotrank "app/cross/command/redis/hotrank"
	rpeak "app/cross/command/redis/peak"
	rrank "app/cross/command/redis/rank"
	rseasonarena "app/cross/command/redis/seasonarena"
	rseasoncompliance "app/cross/command/redis/seasoncompliance"
	rseasonmap "app/cross/command/redis/seasonmap"
	rworldboss "app/cross/command/redis/worldboss"
	rwrestle "app/cross/command/redis/wrestle"
	mrouter "app/cross/command/router"
	mseasonarena "app/cross/command/seasonarena"
	mseasoncompliance "app/cross/command/seasoncompliance"
	mseasonmap "app/cross/command/seasonmap"
	mworldboss "app/cross/command/worldboss"
	mwrestle "app/cross/command/wrestle"
	"app/cross/db"
	"app/cross/session"
	"app/cross/tool"
	"app/goxml"
	"app/protos/in/cm2c"
	"app/protos/in/config"
	"app/protos/in/l2c"
	appsrv "app/service"

	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/discovery"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
	"gitlab.qdream.com/kit/sea/zebra/tcp"

	l4g "github.com/ivanabc/log4go"

	mgst "app/cross/command/gst"
	rgst "app/cross/command/redis/gst"
	plog "app/protos/in/log"
)

// const commandMaxExecuteTime = 10000000

var crossDcLogPath = flag.String("cdc", "../log/", "data center log path")

// TODO:打印出当前正在运行的module情况
// TODO:数据库连接不是一个分区一个链接。而是一个node，一个活动一个链接
type CrossService struct {
	sync.RWMutex
	cfg *goxml.CrossServiceConfig

	group       *ctx.Group
	moduleGroup *ctx.Group
	srvCtx      context.Context

	netAddr string
	// dbCmds    *parse.CommandM
	dbs           map[uint32]*db.DBM              // 活动id=>数据库
	modules       map[uint64]*activity.BaseModule // actID | partId
	sessionM      *session.Manager
	logicSessionM *session.Manager

	masterCmds     *parse.CommandM
	masterMsgQ     chan *session.Message
	masterServerID uint64

	resourceLogCollect *log.Collect
	logCollect         *log.Collect

	timeZone   string
	zoneOffset int

	etcdCrossPartition  *config.ETCDCrossPartition
	etcdCrossPartitionC chan *config.ETCDCrossPartition
	crossPartitionMutex sync.RWMutex

	logCollectManager *log.CollectManger
}

func NewService() *CrossService {
	zone, offset := time.Now().Zone()
	return &CrossService{
		dbs:                 make(map[uint32]*db.DBM),
		modules:             make(map[uint64]*activity.BaseModule),
		masterCmds:          parse.NewCommandM(uint32(cm2c.ID_MSG_MIN), uint32(cm2c.ID_MSG_MAX), commandMaxExecuteTime),
		masterMsgQ:          make(chan *session.Message, 2048), //nolint:mnd
		timeZone:            zone,
		zoneOffset:          offset / 3600,                             //nolint:mnd
		etcdCrossPartitionC: make(chan *config.ETCDCrossPartition, 10), //nolint:mnd
	}
}

func (c *CrossService) LogicSessionM() *session.Manager { return c.logicSessionM }
func (c *CrossService) SessionM() *session.Manager      { return c.sessionM }

func (c *CrossService) LoadConfig() bool {
	show := true
	c.cfg = new(goxml.CrossServiceConfig)
	c.cfg.Load(*appsrv.ServiceConfig, show)
	goxml.Load(*appsrv.DataPath, show, false)
	return true
}

func (c *CrossService) ReloadConfig() {
	show := true
	go goxml.Load(*appsrv.DataPath, show, false)
}

func (c *CrossService) Init(serviceID string) bool {
	ip := strings.Split(serviceID, ":")[0]
	c.netAddr = ip + c.cfg.Server.Port

	c.registerCmds()

	return true
}

func (c *CrossService) registerCmds() {
	master.Init(c.masterCmds, true)
}

func (c *CrossService) Run(group *ctx.Group) {
	l4g.Info("CrossService prepare Run")
	parse.NewReadBufferPool(128) //nolint:mnd
	srvCtx := appsrv.CreateServiceContext(c)
	c.srvCtx = srvCtx
	c.group = group
	c.moduleGroup = group.CreateChild()

	c.resourceLogCollect = log.NewCollect(group.CreateChild(), 2048, filepath.Join(*crossDcLogPath, "resource.log"), uint(*appsrv.RotationCount)) //nolint:mnd
	c.logCollect = log.NewCollect(group.CreateChild(), 2048, filepath.Join(*crossDcLogPath, "dc.log"), uint(*appsrv.RotationCount))               //nolint:mnd
	c.logCollectManager = log.NewCollectManager(group.CreateChild())
	kafkaHandlerLog := log.NewLogCollect(&log.LogCollectParams{
		ChanSize:      2048,
		LogPath:       filepath.Join(*crossDcLogPath, "kafka_handler.log"),
		RotationCount: uint(*appsrv.RotationCount),
		Pool: &sync.Pool{
			New: func() interface{} {
				return &plog.KafkaLogHandlerData{}
			},
		},
		Group: c.logCollectManager.NewGroup(),
	})
	c.logCollectManager.AddNewCollect(plog.LOG_TYPE_KAFKA_LOG, kafkaHandlerLog)
	c.logCollectManager.Run()
	//dbCtx := ctx.NewGroup(context.Background()) //这个ctx会独立于goroutine的ctx.Stop不会影响到这个

	//初始化sessionM
	sessionMCtx := group.CreateChild()
	c.sessionM = session.NewManager(sessionMCtx, c.cfg.GetActorConfig(actorKindCrossMaster), NewCrossMasterSessionManager(c))
	mSvrCfg := c.cfg.GetServerConfig()
	mSvrCfg.Address = tool.GetMasterTcpAddr(*appsrv.DebugAddr)
	if goxml.GetData().ServerInfoM.IsGM() {
		mSvrCfg.ReadTimeOut = goxml.LongTimeout
		mSvrCfg.WriteTimeOut = goxml.LongTimeout
	}
	l4g.Info("cross begin listern master svrCfg.Address:%s", mSvrCfg.Address)
	go tcp.Serve(sessionMCtx, c.sessionM, mSvrCfg)
	defer c.sessionM.CloseListener()

	logicSessionMCtx := group.CreateChild()
	c.logicSessionM = session.NewManager(logicSessionMCtx, c.cfg.GetActorConfig(actorKindLogic), NewLogicSessionManager(c))
	svrCfg := c.cfg.GetServerConfig()
	svrCfg.Address = tool.GetLogicTcpAddr(*appsrv.DebugAddr)
	if goxml.GetData().ServerInfoM.IsGM() {
		svrCfg.ReadTimeOut = goxml.LongTimeout
		svrCfg.WriteTimeOut = goxml.LongTimeout
	}
	l4g.Info("cross begin listern logic svrCfg.Address:%s", svrCfg.Address)
	go tcp.Serve(logicSessionMCtx, c.logicSessionM, svrCfg)
	defer c.logicSessionM.CloseListener()

	//ticker
	ticker := time.NewTicker(30 * time.Millisecond) //nolint:mnd
	defer ticker.Stop()
	l4g.Info("[service] cross running...")
	for {
		select {
		case <-group.Done():
			//关闭之后第一步收到这个信号
			//这个执行完之后才会执行Close方法
			//close执行完之后才会继续往后面走.前面两步是有顺序的，一定会等待
			l4g.Info("[service] goruntines manager close...")
			return
		case handler := <-appsrv.Ctrls():
			handler(srvCtx)
		case msg := <-c.masterMsgQ:
			if err, _ := c.masterCmds.Dispatcher(srvCtx, msg); err != nil {
				l4g.Errorf("process command(%+v)  error: %s", msg.PackHead, err)
			}
		case cfg := <-c.etcdCrossPartitionC:
			c.changeEtcdCrossPartition(cfg)
		case now := <-ticker.C:
			c.oneTicker(srvCtx, now.Unix())
		}
	}
}

func (c *CrossService) Close() {
	l4g.Info("cross service close...")
	l4g.Info("cross service module close...")
	modules := make([]*activity.BaseModule, 0, 10) //nolint:mnd
	c.Lock()
	for _, v := range c.modules {
		modules = append(modules, v)
	}
	c.Unlock()
	for _, v := range modules {
		//这个close可能没有执行
		v.Close("CrossService")
	}
	c.moduleGroup.Stop()
	c.moduleGroup.Wait()
	c.moduleGroup.Finish()
	l4g.Info("cross service close modules end")

	l4g.Info("cross service prepare close dbs...")
	for _, v := range c.dbs {
		v.Close()
	}
	c.resourceLogCollect.Close()
	c.logCollect.Close()
	c.logCollectManager.Close()
	l4g.Info("cross service close dbs end")
}

func (c *CrossService) oneTicker(_ context.Context, _ int64) {
	c.sessionM.ForceWrite()
	c.logicSessionM.ForceWrite()
}

func (c *CrossService) SendCmdToMaster(cmd cm2c.ID, data interface{}) {
	head := &parse.PackHead{
		Cmd: uint32(cmd),
	}
	c.sessionM.Write(head, data, c.masterServerID)
}

func (c *CrossService) SendCmdToLogic(cseq, sseq, flags uint32, sid, uid uint64, cmd l2c.ID, data interface{}) {
	head := &parse.PackHead{
		Cmd:   uint32(cmd),
		UID:   uid,
		CSeq:  cseq,
		SSeq:  sseq,
		Flags: flags,
	}
	c.logicSessionM.Write(head, data, sid)
}

func (c *CrossService) SendTransformMsgToNode(msg *cm2c.C2C_CrossNodeTransform) {
	module := c.GetModule(msg.GetActId(), msg.GetPartition())
	if module != nil {
		module.AddTransformMsg(msg)
		return
	}
	cmd := cm2c.ID_MSG_C2C_CrossNodeTransform
	c.SendCmdToMaster(cmd, msg)
}

func (c *CrossService) GetRealServerID(sid uint64) uint64 {
	client := c.logicSessionM.GetSession(sid)
	if client != nil {
		return client.ID()
	} else {
		return uint64(0)
	}
}

func (c *CrossService) GetCurrentServerID(sid uint64) uint64 {
	return c.LogicSessionM().GetCurrentSID(sid)
}

func (c *CrossService) AddMasterMsg(message *session.Message) {
	c.masterMsgQ <- message
}

func UniqModuleID(actId, partition uint32) uint64 {
	return uint64(actId)<<32 | uint64(partition)
}

func (c *CrossService) CreateNewModule(actId, partition, opGroup uint32, resetTime uint64, servers []uint64, sessionID uint32, actDB *config.CrossActDb, normalArea map[uint64]uint32) *activity.BaseModule {
	l4g.Info("CrossService begin CreateNewModule. actID:%d partition:%d resetTime:%d, sids:%+v, sessionID:%d dbConfig:%+v", actId, partition, resetTime, servers, sessionID, actDB)
	id := UniqModuleID(actId, partition)
	c.Lock()
	defer c.Unlock()
	if mod, exist := c.modules[id]; exist {
		//重新通知一下主服务器
		//TODO 重置resettime.
		//TODO 因为crossmaster有可能会修改服务器的配置情况，这里不会完全同步, 但是多一个服会有问题。所以这里一定要重启
		realMsg := &activity.UpdateMasterRepeatedCreateCtrlMsg{
			ResetTime: resetTime,
			Servers:   servers,
			SessionID: sessionID,
		}
		msg := &activity.CtrlMessage{
			Msg:    realMsg,
			Module: mod,
		}
		mod.AddMessage(msg)
		return mod
	}
	dbm := c.createNewDB(actId, actDB)
	if dbm == nil {
		l4g.Errorf("[FATAL] create db fail. actid:%d partition:%d", actId, partition)
		return nil
	}
	realModule, err := c.CreateRealModule(actId, partition, actDB)
	if err != nil {
		l4g.Errorf("[FATAL] CreateRealModule fail. actid:%d partition:%d err:%s", actId, partition, err)
		return nil
	}
	actorCfg := c.cfg.GetActorConfig(actorKindModule)
	//TODO 根据不同的module获取不同的队列长度。目前默认是4096
	baseModule := activity.NewBaseModule(c, c.moduleGroup.CreateChild(), actId, partition, opGroup,
		actorCfg, realModule, resetTime, servers, sessionID, dbm, normalArea)
	c.modules[id] = baseModule
	c.RegisterModuleCmd(actId, baseModule)
	baseModule.Init()
	go baseModule.Start()
	return baseModule
}

// TODO 这两个函数放到一个单独的文件
//
//nolint:exhaustive
func (c *CrossService) CreateRealModule(actId, partition uint32, actDB *config.CrossActDb) (activity.Moduler, error) {
	switch l2c.ACTIVITYID(actId) {
	case l2c.ACTIVITYID_ROUTER:
		return router.NewRouter(), nil
	case l2c.ACTIVITYID_WRESTLE:
		return wrestle.NewWrestle(), nil
	case l2c.ACTIVITYID_GUILD:
		return guild.NewManager(), nil
	case l2c.ACTIVITYID_RANK:
		return rank.NewManager(), nil
	case l2c.ACTIVITYID_WORLDBOSS:
		return worldboss.NewManager(), nil
	case l2c.ACTIVITYID_DISORDER_LAND:
		return disorderland.NewManager(), nil
	case l2c.ACTIVITYID_PEAK:
		return peak.NewManager(), nil
	case l2c.ACTIVITYID_GST:
		return gst.NewManager(actDB)
	case l2c.ACTIVITYID_SEASON_ARENA:
		return seasonarena.NewManager(), nil
	case l2c.ACTIVITYID_HOT_RANK:
		return hotrank.NewManager(goxml.GetData().ServerInfoM.ResourceLogTime), nil
	case l2c.ACTIVITYID_SEASON_COMPLIANCE:
		return seasoncompliance.NewManager(), nil
	case l2c.ACTIVITYID_SEASON_MAP:
		return seasonmap.NewManager(), nil
	default:
		l4g.Errorf("activity id not exist, id:%d", actId)
	}
	return nil, nil
}

//nolint:exhaustive
func (c *CrossService) RegisterModuleCmd(actId uint32, module *activity.BaseModule) {
	switch l2c.ACTIVITYID(actId) {
	case l2c.ACTIVITYID_ROUTER:
		mrouter.Init(module.GetCommandM(activity.L2CCommand), true)
	case l2c.ACTIVITYID_WRESTLE:
		mwrestle.Init(module.GetCommandM(activity.L2CCommand), true)
		rwrestle.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_GUILD:
		mguild.Init(module.GetCommandM(activity.L2CCommand), true)
		rguild.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_RANK:
		mrank.Init(module.GetCommandM(activity.L2CCommand), true)
		rrank.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_WORLDBOSS:
		mworldboss.Init(module.GetCommandM(activity.L2CCommand), true)
		rworldboss.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_DISORDER_LAND:
		mdisorderland.Init(module.GetCommandM(activity.L2CCommand), true)
		rdisorderland.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_PEAK:
		mpeak.Init(module.GetCommandM(activity.L2CCommand), true)
		rpeak.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_GST:
		mgst.Init(module.GetCommandM(activity.L2CCommand), true)
		rgst.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_SEASON_ARENA:
		mseasonarena.Init(module.GetCommandM(activity.L2CCommand), true)
		rseasonarena.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_HOT_RANK:
		mhotrank.Init(module.GetCommandM(activity.L2CCommand), true)
		rhotrank.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_SEASON_COMPLIANCE:
		mseasoncompliance.Init(module.GetCommandM(activity.L2CCommand), true)
		rseasoncompliance.Init(module.GetCommandM(activity.R2CCommand), true)
	case l2c.ACTIVITYID_SEASON_MAP:
		mseasonmap.Init(module.GetCommandM(activity.L2CCommand), true)
		rseasonmap.Init(module.GetCommandM(activity.R2CCommand), true)
	default:
		l4g.Errorf("activity id not exist, id:%d", actId)
	}

}

func (c *CrossService) createNewDB(actId uint32, actDB *config.CrossActDb) *db.DBM {
	l4g.Info("CrossService prepare CreateNewMDB. actID:%d dbConfig:%+v", actId, actDB)
	if dbm, exist := c.dbs[actId]; exist {
		return dbm
	}

	l4g.Info("CrossService begin CreateNewMDB. actID:%d dbConfig:%+v", actId, actDB)
	dbcfg := &db.DBConfig{
		RedisAddr:  actDB.RedisAddr,
		RedisIndex: int(actDB.RedisIndex),
		Config:     c.cfg.GetActorConfig(actorKindRedis),
	}
	dbm := db.NewDBM(c.srvCtx, c.group.CreateChild(), actId, dbcfg)
	if dbm == nil {
		l4g.Errorf("create db error %d", actId)
		return nil
	}
	//这个注册必须在外面调用 因为cdb包的互相引用的问题。
	cdb.Init(dbm.GetRedisActor().GetCommandM(), true)
	go dbm.Run()
	c.dbs[actId] = dbm
	return dbm
}

func (c *CrossService) SetMasterServerID(sid uint64) {
	var needInit bool
	if c.masterServerID == 0 {
		needInit = true
	}
	c.masterServerID = sid
	if needInit {
		c.initEtcdCrossPartition(c.group)
	}
}

func (c *CrossService) GetModule(actId, partition uint32) *activity.BaseModule {
	c.RLock()
	defer c.RUnlock()
	id := UniqModuleID(actId, partition)
	return c.modules[id]
}

func (c *CrossService) DeleteModule(actId, partition uint32) {
	l4g.Info("CrossService DeleteModule.actID:%d, partition:%d", actId, partition)
	c.Lock()
	defer c.Unlock()
	id := UniqModuleID(actId, partition)
	delete(c.modules, id)
}

func (c *CrossService) UpdateModuleMasterReset(actId, partition uint32, resetTime uint64, servers []uint64, sessionID uint32) {
	module := c.GetModule(actId, partition)
	if module != nil {
		realMsg := &activity.UpdateMasterResetCtrlMsg{
			ResetTime: resetTime,
			Servers:   servers,
			SessionID: sessionID,
		}
		msg := &activity.CtrlMessage{
			Msg:    realMsg,
			Module: module,
		}
		module.AddMessage(msg)
	}
}

func (c *CrossService) UpdateModuleMasterNormalArea(actId, partition uint32, normalArea map[uint64]uint32) {
	module := c.GetModule(actId, partition)
	if module != nil {
		realMsg := &activity.UpdateMasterNormalAreaCtrlMsg{
			NormalArea: normalArea,
		}
		msg := &activity.CtrlMessage{
			Msg:    realMsg,
			Module: module,
		}
		module.AddMessage(msg)
	}
}

func (c *CrossService) CloseModule(actId, partition uint32, isDel bool) {
	module := c.GetModule(actId, partition)
	if module != nil {
		if isDel {
			module.Delete()
		}
		module.Close("crossMaster")
	}
}

func (c *CrossService) CheckModuleCanResetPart(actId, partition uint32, resetTime int64) bool {
	module := c.GetModule(actId, partition)
	if module == nil {
		return false
	}
	return module.GetRealModule().CheckCanResetPart(resetTime)
}

func (c *CrossService) NewResourceLogMessage() *da.Log {
	return c.resourceLogCollect.NewMessage()
}
func (c *CrossService) WriteResourceLogMessage(msg *da.Log) {
	c.resourceLogCollect.Write(msg)
	//l4g.Debugf("[ResourceLogCollect] write resource log message: %+v", msg)
}

// 日志topic
func (c *CrossService) LogTopicResource() string {
	defaultTopic := "resource-ngame"
	cfg := c.cfg.GetAheadConfig()
	if cfg == nil || cfg.LogTopicResource == "" {
		return defaultTopic
	}
	return cfg.LogTopicResource
}
func (c *CrossService) LogTopicDC() string {
	defaultTopic := "log-ngame"
	cfg := c.cfg.GetAheadConfig()
	if cfg == nil || cfg.LogTopicDc == "" {
		return defaultTopic
	}
	return cfg.LogTopicDc
}

func (c *CrossService) NewLogMessage() *da.Log {
	return c.logCollect.NewMessage()
}
func (c *CrossService) WriteLogMessage(msg *da.Log) {
	c.logCollect.Write(msg)
}

func (c *CrossService) GetCrossServiceConfig() *goxml.CrossServiceConfig {
	return c.cfg
}
func (c *CrossService) NewDCLogMessage() *da.Log {
	log := c.logCollect.NewMessage()
	log.Topic = c.LogTopicDC()
	log.LogType = uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC)
	now := time.Now().Unix()
	log.EventTime = uint64(now)
	log.SeasonID = goxml.GetLogFormatSeasonId(now)
	log.Param9 = c.GetTimeZoneAndOffest()
	return log
}
func (c *CrossService) GetTimeZoneAndOffest() string {
	return c.timeZone + "|" + strconv.FormatInt(int64(c.zoneOffset), 10)
}

func (c *CrossService) initEtcdCrossPartition(group *ctx.Group) {
	cfgW := &CrossPartitionWatcher{
		C: c.etcdCrossPartitionC,
	}
	go func() {
		cfg := c.cfg.GetDiscoveryConfig(false)
		cfg.Target = c.cfg.Etcd.CrossPartition + strconv.FormatUint(c.masterServerID, 10)
		discovery.Watch(group.CreateChild(), cfg, cfgW)
		group.Stop()
		l4g.Infof("service discovery CrossPartition goruntine closed")
	}()
}

func (c *CrossService) changeEtcdCrossPartition(config *config.ETCDCrossPartition) {
	c.crossPartitionMutex.Lock()
	defer c.crossPartitionMutex.Unlock()
	c.etcdCrossPartition = config
}

//nolint:exhaustive
func (c *CrossService) GetBigAreaIDByAreaID(actID, areaID uint32) uint32 {
	if c.etcdCrossPartition == nil {
		return 0
	}
	switch l2c.ACTIVITYID(actID) {
	case l2c.ACTIVITYID_GST:
		actID = uint32(l2c.CROSS_ACT_AREA_ID_GST_AREA)
	case l2c.ACTIVITYID_SEASON_ARENA:
		actID = uint32(l2c.CROSS_ACT_AREA_ID_SEASON_AREA)
	case l2c.ACTIVITYID_SEASON_COMPLIANCE:
		actID = uint32(l2c.CROSS_ACT_AREA_ID_SEASON_COMPLIANCE_AREA)
	default:
		l4g.Errorf("activity BigAreaID not exist, id:%d", actID)
		return 0
	}
	c.crossPartitionMutex.RLock()
	defer c.crossPartitionMutex.RUnlock()
	if crossActivityPartition, exist1 := c.etcdCrossPartition.ActPartition[actID]; !exist1 {
		return 0
	} else {
		if partition, exist2 := crossActivityPartition.Partition[uint64(areaID)]; exist2 {
			return partition.NowPart
		}
	}
	return 0
}

func (c *CrossService) NewLog(logType plog.LOG_TYPE) interface{} {
	return c.logCollectManager.GetPoolLog(logType)
}

func (c *CrossService) WriteLog(logType plog.LOG_TYPE, log log.LogI) {
	c.logCollectManager.WriteLog(logType, log)
}
