package seasonmap

import (
	"app/cross/activity"
	"app/goxml"
	"app/logic/helper"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/r2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"math"
	"slices"
	"sync/atomic"

	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/sea/math/rand"

	l4g "github.com/ivanabc/log4go"
)

const (
	stateInit int32 = iota
	stateFinishLoad
	stateRunning
)

type Manager struct {
	state      int32
	baseModule activity.BaseModuler

	rd      *rand.Rand
	mgrData *cr.SeasonMapManager

	change     bool
	incr, freq int64
}

func NewManager() *Manager {
	return &Manager{
		freq: 120,
		rd:   rand.New(time.Now().UnixNano()),
	}
}

func (m *Manager) DeletePartition(moduler activity.BaseModuler)                              {}
func (m *Manager) CheckCanResetPart(i int64) bool                                            { return false }
func (m *Manager) ProcessGrpcRequest(request *activity.GrpcRequest) error                    { return nil }
func (m *Manager) TransformMsg(moduler activity.BaseModuler, msg *activity.TransformCtrlMsg) {}

func (m *Manager) SetChange(change bool) {
	m.change = change
}

func (m *Manager) LoadData(data *r2c.R2C_SeasonMapLoad) {
	if data.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("SeasonMapManager Load Failed, ret:%d, data:%v", data.Ret, data)
		return
	}
	if data.Mgr != nil {
		m.mgrData = data.Mgr.Clone()
	} else {
		m.mgrData = &cr.SeasonMapManager{}
	}
	m.SetState(stateFinishLoad)
}

func (m *Manager) Init(baseModule activity.BaseModuler) bool {
	m.baseModule = baseModule
	l4g.Infof("seasonmap.Init: start load data. actID:%d, partID:%d, sids:%v",
		baseModule.GetActivityID(), baseModule.Partition(), baseModule.GetServerIDList())
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_SeasonMapLoad, &r2c.C2R_SeasonMapLoad{
		Partition: baseModule.Partition(),
	})
	return true
}

func (m *Manager) SetState(state int32) {
	atomic.StoreInt32(&m.state, state)
}

func (m *Manager) GetState() int32 {
	return atomic.LoadInt32(&m.state)
}

func (m *Manager) Close(moduler activity.BaseModuler) {
	m.DbSave(moduler)
}

func (m *Manager) save(baseModule activity.BaseModuler) {
	msg := &r2c.C2R_SeasonMapSave{
		Partition: baseModule.Partition(),
		Mgr:       m.mgrData.Clone(),
	}
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_SeasonMapSave, msg)
	m.SetChange(false)
}

func (m *Manager) CheckRunning() bool {
	return m.GetState() == stateRunning
}

func (m *Manager) Update(moduler activity.BaseModuler, now int64) {
	if m.GetState() < stateFinishLoad {
		return
	}
	m.logicRun(now)
	m.incr++
	if m.incr%m.freq == 0 {
		m.DbSave(moduler)
	}
	m.SetState(stateRunning)
}

func (m *Manager) DbSave(baseModule activity.BaseModuler) {
	if !m.change {
		return
	}
	m.save(baseModule)
}

func (m *Manager) logicRun(now int64) {
	m.checkReset(now)
	if m.GetData().Season == 0 {
		return
	}
	m.checkNewSystemEvent(now)
	m.checkSystemEvent(now)
	m.checkResetGoods(now)
}

func (m *Manager) GetData() *cr.SeasonMap {
	if m.mgrData.Data == nil {
		m.mgrData.Data = &cr.SeasonMap{}
	}
	return m.mgrData.Data
}

func (m *Manager) GetPositionLogs() map[uint32]*cl.SeasonMapPositionLog {
	data := m.GetData()
	if data.PositionLogs == nil {
		data.PositionLogs = make(map[uint32]*cl.SeasonMapPositionLog)
	}
	return data.PositionLogs
}

func (m *Manager) checkReset(now int64) {
	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(now)
	if seasonInfo == nil {
		return
	}
	if seasonInfo.Id == m.GetData().GetSeason() {
		return
	}
	l4g.Infof("seasonmap init new season %d", seasonInfo.Id)
	m.newSeason(seasonInfo, now)
}

func (m *Manager) newSeason(seasonInfo *goxml.SeasonInfoExt, now int64) {
	m.mgrData = &cr.SeasonMapManager{
		Data: &cr.SeasonMap{
			Season:              seasonInfo.Id,
			NextSystemEventTime: seasonInfo.StartTm + goxml.GetData().SeasonMapConfigInfoM.FirstSystemEventTime,
			GoodsResetTime:      helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now),
		},
	}
	m.SetChange(true)
}

func (m *Manager) checkNewSystemEvent(now int64) {
	if m.GetData().NextSystemEventTime > now {
		return
	}
	eventGroup := goxml.GetData().SeasonMapTradeEventInfoM.GroupIndex(goxml.GetData().SeasonMapConfigInfoM.SystemEventGroup)
	if eventGroup == nil || len(eventGroup.Events) == 0 {
		l4g.Errorf("cant find eventGroup %d", goxml.GetData().SeasonMapConfigInfoM.SystemEventGroup)
		return
	}
	events, err := goxml.RandomSomeNoRepeat(m.rd, 1, eventGroup.TotalWeight, eventGroup.Events)
	if err != nil {
		l4g.Errorf("checkNewSystemEvent error %s", err.Error())
		return
	}
	for _, event := range events {
		m.GetData().Events = append(m.GetData().Events, &cl.SeasonMapSystemEvent{
			EventId:    event.EventId,
			EffectTime: m.GetData().NextSystemEventTime + int64(event.BeforeTime),
		})
	}
	m.GetData().NextSystemEventTime += goxml.GetData().SeasonMapConfigInfoM.SystemEventTime
	m.SetChange(true)
}

func (m *Manager) checkSystemEvent(now int64) {
	m.GetData().Events = slices.DeleteFunc(m.GetData().Events, func(event *cl.SeasonMapSystemEvent) bool {
		if now >= event.EffectTime {
			m.triggerSystemEvent(event, now)
			return true
		}
		return false
	})
}

func (m *Manager) checkResetGoods(now int64) {
	dailyRefreshTime := helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now)
	if m.GetData().GoodsResetTime == dailyRefreshTime {
		return
	}
	m.GetData().GoodsResetTime = dailyRefreshTime
	for _, goodsInfo := range goxml.GetData().SeasonMapTradeGoodsInfoM.ResetsGoods {
		// 重置价格
		goods := m.GetGoods(goodsInfo)
		goods.BuyNum = 0
		goods.SellNum = 0
		goods.PriceChange = goxml.BaseUInt32

		changePriceLog := &cl.SeasonMapPriceChangeLog{
			Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_System_Reset,
			GoodsId:     goodsInfo.Id,
			ChangeValue: goodsInfo.FirstPrice,
			Tm:          now,
		}
		m.changeGoodsPrice(changePriceLog, false)
	}
	m.SetChange(true)
}

func (m *Manager) triggerSystemEvent(event *cl.SeasonMapSystemEvent, now int64) {
	eventInfo := goxml.GetData().SeasonMapTradeEventInfoM.Index(event.EventId)
	if eventInfo == nil {
		l4g.Errorf("cant find eventInfo %d", event.EventId)
		return
	}
	changePriceLog := &cl.SeasonMapPriceChangeLog{
		Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_System,
		GoodsId:     eventInfo.GoodsId,
		ChangeType:  eventInfo.PriceType,
		ChangeValue: eventInfo.PriceValue,
		EventId:     event.EventId,
		Tm:          now,
	}
	m.changeGoodsPrice(changePriceLog, true)
	m.SetChange(true)
}

func (m *Manager) changeGoodsPriceByEvent(goods *cr.SeasonMapGood, changeType, changeValue uint32) {
	switch changeType {
	case uint32(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_Up_Pct):
		goods.PriceChange = uint32(float64(goods.PriceChange) * float64((goxml.BaseUInt32 + changeValue)) / goxml.BaseFloat)
	case uint32(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_Down_Pct):
		if changeValue >= goxml.BaseUInt32 {
			l4g.Errorf("changeValue too much %d", changeValue)
			return
		}
		goods.PriceChange = uint32(float64(goods.PriceChange) * float64(goxml.BaseUInt32-changeValue) / goxml.BaseFloat)
	default:
		l4g.Errorf("cant find ChangeType %d", changeType)
		return
	}
	m.SetChange(true)
}

func (m *Manager) changeGoodsPrice(changeLog *cl.SeasonMapPriceChangeLog, isEvent bool) {
	goodsInfo := goxml.GetData().SeasonMapTradeGoodsInfoM.Index(changeLog.GoodsId)
	if goodsInfo == nil {
		l4g.Errorf("cant find goodsInfo. goodsId %d", changeLog.GoodsId)
		return
	}
	goods := m.GetGoods(goodsInfo)
	if isEvent {
		m.changeGoodsPriceByEvent(goods, changeLog.ChangeType, changeLog.ChangeValue)
	}
	price := goodsInfo.FirstPrice
	globalAdd := (goods.BuyNum/goxml.GetData().SeasonMapConfigInfoM.ChangePriceGoodsNum - goods.SellNum/goxml.GetData().SeasonMapConfigInfoM.ChangePriceGoodsNum) * goxml.GetData().SeasonMapConfigInfoM.ChangePriceNum
	globalPct := int32(goxml.BaseInt) + globalAdd
	if globalPct < 0 {
		globalPct = 0
	}
	price = uint32(math.Floor(float64(price) * float64(globalPct) / goxml.BaseFloat * float64(goods.PriceChange) / goxml.BaseFloat))
	if price == 0 {
		price = 1
	}
	m.addGoodsPrice(goods, price, changeLog)
	m.addPriceChangeLog(changeLog)
}

func (m *Manager) addPriceChangeLog(changeLog *cl.SeasonMapPriceChangeLog) {
	logNum := len(m.GetData().PriceChangeLogs)
	if logNum >= goxml.GetData().SeasonMapConfigInfoM.TotalPriceLogMax {
		delete := logNum - goxml.GetData().SeasonMapConfigInfoM.TotalPriceLogMax + 1
		m.GetData().PriceChangeLogs = slices.Delete(m.GetData().PriceChangeLogs, 0, delete)
	}
	m.GetData().PriceChangeLogs = append(m.GetData().PriceChangeLogs, changeLog)
	m.SetChange(true)
}

func (m *Manager) addGoodsPrice(goods *cr.SeasonMapGood, newPrice uint32, changeLog *cl.SeasonMapPriceChangeLog) {
	priceLogNum := len(goods.PriceChangeLogs)
	if priceLogNum >= goxml.GetData().SeasonMapConfigInfoM.GoodsPriceLogMax {
		delete := priceLogNum - goxml.GetData().SeasonMapConfigInfoM.GoodsPriceLogMax + 1
		goods.PriceChangeLogs = slices.Delete(goods.PriceChangeLogs, 0, delete)
	}
	goods.PriceChangeLogs = append(goods.PriceChangeLogs, changeLog)
	priceMaxNum := goxml.GetData().SeasonMapConfigInfoM.GoodsPriceLogMax + 1
	priceNum := len(goods.Prices.Prices)
	if priceNum >= priceMaxNum {
		delete := priceNum - priceMaxNum + 1
		goods.Prices.Prices = slices.Delete(goods.Prices.Prices, 0, delete)
	}
	goods.Prices.Prices = append(goods.Prices.Prices, newPrice)
	m.SetChange(true)
}

func (m *Manager) GetGoodsPrice(goods *cr.SeasonMapGood) uint32 {
	return goods.Prices.Prices[len(goods.Prices.Prices)-1]
}

func (m *Manager) GetGoods(goodsInfo *goxml.SeasonMapTradeGoodsInfo) *cr.SeasonMapGood {
	data := m.GetData()
	if data.Goods == nil {
		data.Goods = make(map[uint32]*cr.SeasonMapGood)
	}
	goods := data.Goods[goodsInfo.Id]
	if goods == nil {
		goods = &cr.SeasonMapGood{
			Prices: &cl.SeasonMapPrice{
				Prices: []uint32{goodsInfo.FirstPrice},
			},
			PriceChange: goxml.BaseUInt32,
		}
		data.Goods[goodsInfo.Id] = goods
		m.SetChange(true)
	}
	return goods
}

func (m *Manager) Trade(msg *l2c.L2CS_SeasonMapTrade, goods *cr.SeasonMapGood) {
	switch msg.Req.TradeType {
	case cl.SeasonMapTradeType_TradeType_Buy:
		beforeBuyNum := goods.BuyNum / goxml.GetData().SeasonMapConfigInfoM.ChangePriceGoodsNum
		goods.BuyNum += int32(msg.Req.Count)
		afterBuyNum := goods.BuyNum / goxml.GetData().SeasonMapConfigInfoM.ChangePriceGoodsNum
		if beforeBuyNum != afterBuyNum {
			changePriceLog := &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_BuyX,
				GoodsId:     msg.Req.GoodsId,
				ChangeType:  uint32(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_Up_Pct),
				ChangeValue: uint32((afterBuyNum - beforeBuyNum) * goxml.GetData().SeasonMapConfigInfoM.ChangePriceNum),
				Tm:          time.Now().Unix(),
			}
			m.changeGoodsPrice(changePriceLog, false)
		}
	case cl.SeasonMapTradeType_TradeType_Sell:
		beforeSellNum := goods.SellNum / goxml.GetData().SeasonMapConfigInfoM.ChangePriceGoodsNum
		goods.SellNum += int32(msg.Req.Count)
		afterSellNum := goods.SellNum / goxml.GetData().SeasonMapConfigInfoM.ChangePriceGoodsNum
		if beforeSellNum != afterSellNum {
			changePriceLog := &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_SellX,
				GoodsId:     msg.Req.GoodsId,
				ChangeType:  uint32(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_Down_Pct),
				ChangeValue: uint32((afterSellNum - beforeSellNum) * goxml.GetData().SeasonMapConfigInfoM.ChangePriceNum),
				Tm:          time.Now().Unix(),
			}
			m.changeGoodsPrice(changePriceLog, false)
		}
	case cl.SeasonMapTradeType_TradeType_None:
		return
	default:
		l4g.Errorf("cant find tradeType %d", msg.Req.TradeType)
		return
	}
	m.SetChange(true)
}

func (m *Manager) SyncPositionLog(log *l2c.SeasonMapSyncPositionLog) {
	data := m.GetPositionLogs()
	positionLog := data[log.Position]
	if positionLog == nil {
		positionLog = &cl.SeasonMapPositionLog{}
		data[log.Position] = positionLog
	}
	switch log.LogType {
	case l2c.SeasonMapPositionLogType_Move1:
		logNum := len(positionLog.MoveLogs)
		if logNum >= goxml.GetData().SeasonMapConfigInfoM.PositionMoveLogMax {
			delete := logNum - goxml.GetData().SeasonMapConfigInfoM.PositionMoveLogMax + 1
			positionLog.MoveLogs = slices.Delete(positionLog.MoveLogs, 0, delete)
		}
		positionLog.MoveLogs = append(positionLog.MoveLogs, log.MoveLog.Clone())
	case l2c.SeasonMapPositionLogType_Event1:
		logNum := len(positionLog.EventLogs)
		if logNum >= goxml.GetData().SeasonMapConfigInfoM.PositionEventLogMax {
			delete := logNum - goxml.GetData().SeasonMapConfigInfoM.PositionEventLogMax + 1
			positionLog.EventLogs = slices.Delete(positionLog.EventLogs, 0, delete)
		}
		positionLog.EventLogs = append(positionLog.EventLogs, log.EventLog.Clone())
	case l2c.SeasonMapPositionLogType_None1:
		return
	default:
		l4g.Errorf("cant find type %d", log.LogType)
		return
	}
	m.SetChange(true)
}

func (m *Manager) TryTrigger(trigger *l2c.SeasonMapTrigger) {
	now := time.Now().Unix()
	if now < m.GetData().UserEventTime+goxml.GetData().SeasonMapConfigInfoM.TriggerCD {
		return
	}
	triggerInfo := goxml.GetData().SeasonMapTriggerInfoM.Index(uint32(trigger.Type), trigger.Position)
	if triggerInfo == nil {
		return
	}
	if m.rd.SelectByTenTh(triggerInfo.Trigger) {
		m.triggerUserEvent(trigger, triggerInfo.EventGroup, now)
	}
}

func (m *Manager) triggerUserEvent(trigger *l2c.SeasonMapTrigger, eventGroup uint32, now int64) {
	eventGroupInfo := goxml.GetData().SeasonMapTradeEventInfoM.GroupIndex(eventGroup)
	if eventGroupInfo == nil || len(eventGroupInfo.Events) == 0 {
		l4g.Errorf("cant find eventGroupInfo %d", eventGroup)
		return
	}
	events, err := goxml.RandomSomeNoRepeat(m.rd, 1, eventGroupInfo.TotalWeight, eventGroupInfo.Events)
	if err != nil {
		l4g.Errorf("triggerUserEvent error %s", err.Error())
		return
	}
	var changePriceLog *cl.SeasonMapPriceChangeLog
	for _, event := range events {
		eventInfo := goxml.GetData().SeasonMapTradeEventInfoM.Index(event.EventId)
		if eventInfo == nil {
			l4g.Errorf("cant find eventInfo %d", event.EventId)
			continue
		}
		switch trigger.Type {
		case cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Buy:
			changePriceLog = &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Buy,
				GoodsId:     eventInfo.GoodsId,
				ChangeType:  eventInfo.PriceType,
				ChangeValue: eventInfo.PriceValue,
				EventId:     event.EventId,
				Tm:          now,
				OpGoodId:    trigger.TradeGoods,
				Name:        trigger.Name,
				Position:    trigger.Position,
			}
		case cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Sell:
			changePriceLog = &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Sell,
				GoodsId:     eventInfo.GoodsId,
				ChangeType:  eventInfo.PriceType,
				ChangeValue: eventInfo.PriceValue,
				EventId:     event.EventId,
				Tm:          now,
				OpGoodId:    trigger.TradeGoods,
				Name:        trigger.Name,
			}
		case cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Master:
			changePriceLog = &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Master,
				GoodsId:     eventInfo.GoodsId,
				ChangeType:  eventInfo.PriceType,
				ChangeValue: eventInfo.PriceValue,
				EventId:     event.EventId,
				Tm:          now,
				Name:        trigger.Name,
				Position:    trigger.Position,
			}
		case cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Altar:
			changePriceLog = &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Altar,
				GoodsId:     eventInfo.GoodsId,
				ChangeType:  eventInfo.PriceType,
				ChangeValue: eventInfo.PriceValue,
				EventId:     event.EventId,
				Tm:          now,
				Name:        trigger.Name,
				Position:    trigger.Position,
			}
		case cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Fight:
			changePriceLog = &cl.SeasonMapPriceChangeLog{
				Type:        cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Fight,
				GoodsId:     eventInfo.GoodsId,
				ChangeType:  eventInfo.PriceType,
				ChangeValue: eventInfo.PriceValue,
				EventId:     event.EventId,
				Tm:          now,
				Name:        trigger.Name,
				Position:    trigger.Position,
			}
		case cl.SeasonMapPriceLogType_SeasonMapPriceLogType_None:
			return
		default:
			l4g.Errorf("cant find type %d", trigger.Type)
			return
		}
	}
	if changePriceLog != nil {
		m.changeGoodsPrice(changePriceLog, true)
	}
	m.GetData().UserEventTime = now
	m.SetChange(true)
}
