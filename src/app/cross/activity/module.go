package activity

import (
	"app/cross/db"
	"app/protos/in/cm2c"
	"app/protos/in/l2c"
	"app/protos/in/r2c"
	"app/protos/out/ret"
	appsrv "app/service"
	"context"
	"runtime/debug"
	"strconv"
	"sync/atomic"

	"gitlab.qdream.com/platform/proto/da"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

const (
	ModuleCloseStateInit int32 = iota
	ModuleCloseStateClosed
	maxEventProcessNum = 10
)

type BaseModule struct {
	*actor.Actor
	srv    Servicer
	ctx    *ctx.Group //控制流程的
	srvCtx context.Context

	actId     uint32
	partition uint32 //分区id
	opGroup   uint32

	resetTime  int64             //活动的当前的重置时间, activity同步过来
	servers    []uint64          //当前分区的所有服务器id, activity同步过来
	normalArea map[uint64]uint32 //所有的普通分区信息 crossmaster同步过来
	session    uint32

	cmds      *parse.CommandM
	redisCmds *parse.CommandM

	moduler Moduler //具体的module

	dbm *db.DBM

	noticeMasterCreateSuccess bool //是否通知master创建成功

	closeState int32

	lastFlushTime int64

	asyncMsgID    uint32
	asyncMessages map[uint32]interface{} //缓存异步消息

	eventM *event.Manager
	//msgQ chan *db.Response
}

func NewBaseModule(srv Servicer, group *ctx.Group, actId, partition, opGroup uint32, cfg *actor.Config, moduler Moduler, resetTime uint64, ss []uint64, sessionID uint32, dbm *db.DBM, normalArea map[uint64]uint32) *BaseModule {
	act := actor.NewActor(group.CreateChild(), cfg)
	act.StopGlobalStop()     //禁止全局退出
	act.SetCheckGoLock(true) //开启暂停业务检查, 这个开启就开启了所有业务和定时器的检查了
	baseModule := &BaseModule{
		srv:        srv,
		Actor:      act,
		ctx:        group,
		actId:      actId,
		partition:  partition,
		opGroup:    opGroup,
		resetTime:  int64(resetTime),
		servers:    ss,
		normalArea: normalArea,
		session:    sessionID,
		cmds:       parse.NewCommandM(uint32(l2c.ID_MSG_MIN), uint32(l2c.ID_MSG_MAX), CommandMaxExecuteTime),
		redisCmds:  parse.NewCommandM(uint32(r2c.ID_MSG_MIN), uint32(r2c.ID_MSG_MAX), CommandMaxExecuteTime),
		moduler:    moduler,
		//msgQ:      make(chan *db.Response, 256),
		dbm:           dbm,
		closeState:    ModuleCloseStateInit,
		asyncMessages: make(map[uint32]interface{}),
		eventM:        event.NewManager(),
	}
	srvCtx := appsrv.CreateServiceContext(baseModule)
	baseModule.srvCtx = srvCtx
	baseModule.Actor.TickerCB = func(now int64) {
		baseModule.Update(now)
	}
	baseModule.cmds.OpenRecoverPanic()
	baseModule.redisCmds.OpenRecoverPanic()
	return baseModule
}

func (bm *BaseModule) Load(data *r2c.R2C_Load) {
	bm.moduler.Init(bm)
}

func (bm *BaseModule) Init() {
	bm.moduler.Init(bm)
}

func (bm *BaseModule) Update(now int64) {
	defer func() {
		if err := recover(); err != nil {
			l4g.Error("baseModule:%d %d update panic: %s %s\n", bm.actId, bm.Partition, err, debug.Stack())
		}

	}()
	//nolint:mnd
	if now-bm.lastFlushTime >= 10 {
		bm.Flush()
		bm.lastFlushTime = now
	}
	if !bm.noticeMasterCreateSuccess {
		if bm.moduler.CheckRunning() {
			bm.SendCreateSuccessMsg()
			bm.noticeMasterCreateSuccess = true
		}
	}
	bm.moduler.Update(bm, now)
}

func (bm *BaseModule) ResetNotice() {
	bm.noticeMasterCreateSuccess = false
}

func (bm *BaseModule) CheckRunning() bool {
	return bm.moduler.CheckRunning()
}

func (bm *BaseModule) GetActivityID() uint32 {
	return bm.actId
}

func (bm *BaseModule) GetActivityIDStr() string {
	return strconv.FormatUint(uint64(bm.GetActivityID()), 10)
}

func (bm *BaseModule) Partition() uint32 {
	return bm.partition
}

func (bm *BaseModule) OpGroup() uint32 {
	return bm.opGroup
}

func (bm *BaseModule) PartitionStr() string {
	return strconv.FormatUint(uint64(bm.Partition()), 10)
}

func (bm *BaseModule) GetSrv() Servicer {
	return bm.srv
}

func (bm *BaseModule) Start() {
	defer bm.Close("BaseModuleStart")
	bm.Actor.Run(bm)
}

func (bm *BaseModule) GetAsyncMessage(id uint32) (interface{}, bool) {
	ret, exist := bm.asyncMessages[id]
	return ret, exist
}

func (bm *BaseModule) DeleteAsyncMessage(id uint32) {
	delete(bm.asyncMessages, id)
}

func (bm *BaseModule) DeleteTimerID(id uint32) {
	bm.RemoveTimer(id)
}

func (bm *BaseModule) CreateAsyncMsgID() uint32 {
	bm.asyncMsgID++
	return bm.asyncMsgID
}

func (bm *BaseModule) CreateAsyncMessage(msg interface{}, timeout int64) uint32 {
	id := bm.CreateAsyncMsgID()
	bm.asyncMessages[id] = msg
	if timeout > 0 {
		if amsg, ok := msg.(AsyncMessager); ok {
			tid, tok := bm.AddTimer(&CrossMsgTimeOut{id, bm}, time.Now().Unix()+timeout, 0)
			if tok {
				amsg.AddTimeoutID(tid)
			} else {
				bm.DeleteAsyncMessage(id)
				return uint32(0)
			}
		}
	}
	return id
}

type CrossMsgTimeOut struct {
	AsyncMsgID uint32
	Module     *BaseModule
}

func (cm *CrossMsgTimeOut) TimeOut(now int64) {
	if msg, ok := cm.Module.GetAsyncMessage(cm.AsyncMsgID); ok {
		cm.Module.DeleteAsyncMessage(cm.AsyncMsgID)
		if amsg, ok := msg.(AsyncMessager); ok {
			amsg.Work(cm.Module, uint32(ret.RET_CROSS_REQ_TIMEOUT), nil)
		}
	}
}

func (m *BaseModule) UpdateMasterReset(resetTime uint64, servers []uint64, sessionID uint32) {
	m.resetTime = int64(resetTime)
	m.servers = servers
	m.session = sessionID
}

func (m *BaseModule) UpdateMasterNormalArea(normalArea map[uint64]uint32) {
	m.normalArea = normalArea
}

// Stop和Close的区别
// stop是外面的人主动来停止我这个服务。
// close是内部的服务器结束

func (m *BaseModule) Close(from string) {
	if !atomic.CompareAndSwapInt32(&m.closeState, ModuleCloseStateInit, ModuleCloseStateClosed) {
		return
	}
	m.ctx.Stop()
	//这里的Close模块，会保存数据。导致错误的数据被保存。但是正常panic掉也会保存
	m.moduler.Close(m)
	m.ctx.Wait()
	m.ctx.Finish()
	l4g.Infof("[BaseModule] id:%d partition:%d close...from:%s", m.GetActivityID(), m.Partition(), from)
	m.srv.DeleteModule(m.GetActivityID(), m.Partition())
}

/*
func (m *BaseModule) closeDB() {
	l4g.Info("cross service close...")
	c.SendCmdToDB(r2c.ID_MSG_C2R_Finish, 0, &r2c.C2R_Finish{})

	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
QUIT:
	for {
		select {
		case msg := <-c.redisActor.MsgQ:
			if msg.GetCmd() == uint32(r2c.ID_MSG_R2C_Finish) {
				l4g.Infof("close cross master db success")
				break QUIT
			}
		case <-ticker.C:
			l4g.Errorf("close cross master db timeout")
			break QUIT
		}
	}
	c.redisActor.Close()
}
*/

func (bm *BaseModule) Delete() {
	bm.moduler.DeletePartition(bm)
}

func (bm *BaseModule) GetCommandM(tp uint32) *parse.CommandM {
	switch tp {
	case L2CCommand:
		return bm.cmds
	case R2CCommand:
		return bm.redisCmds
	}
	return nil
}

func (bm *BaseModule) GetRealModule() Moduler {
	return bm.moduler
}

func (bm *BaseModule) ProcessLogicRequest(msg *LogicRequest) {
	sessionID := bm.GetSessionID()
	if sessionID != 0 && sessionID != msg.Msg.Flags {
		l4g.Errorf("Cross process logic msg module session wrong.actId:%d module:%d,local sessionID:%d logic session:%d", bm.actId, bm.partition, sessionID, msg.Msg.Flags)
		return
	}

	if err, _ := bm.GetCommandM(L2CCommand).Dispatcher(bm.srvCtx, msg); err != nil {
		l4g.Errorf("Act:%d BaseModule:%d ProcessLogicRequest cmd:%d error:%v", bm.actId, bm.partition, msg.GetCmd(), err)
	}

	l4g.Debugf("BaseModule ProcessLogicRequest")

	events, process := bm.eventM.Process(bm)
	if process > maxEventProcessNum {
		l4g.Infof("event process pre loop: %d -> %d", events, process)
	}
}

func (bm *BaseModule) ProcessRedisResponse(msg *DBResponse) {
	if err, _ := bm.GetCommandM(R2CCommand).Dispatcher(bm.srvCtx, msg); err != nil {
		l4g.Errorf("Act:%d BaseModule:%d ProcessRedisResponse cmd:%d error:%v", bm.GetActivityID(), bm.partition, msg.GetCmd(), err)
	}
}

type UpdateMasterResetCtrlMsg struct {
	ResetTime uint64
	Servers   []uint64
	SessionID uint32
}

type UpdateMasterNormalAreaCtrlMsg struct {
	NormalArea map[uint64]uint32
}

type TransformCtrlMsg struct {
	FromActId     uint32
	FromPartition uint32
	ProtoId       uint32
	Data          []byte
}

type UpdateMasterRepeatedCreateCtrlMsg struct {
	ResetTime uint64
	Servers   []uint64
	SessionID uint32
}

func (bm *BaseModule) AddTransformMsg(msg *cm2c.C2C_CrossNodeTransform) {
	realMsg := &TransformCtrlMsg{
		FromActId:     msg.GetFromActId(),
		FromPartition: msg.GetFromPartition(),
		ProtoId:       msg.GetProtoId(),
		Data:          msg.GetData(),
	}
	ctrlMsg := &CtrlMessage{
		Msg:    realMsg,
		Module: bm,
	}
	bm.AddMessage(ctrlMsg)
}

func (bm *BaseModule) ProcessCtrlMessage(msg *CtrlMessage) bool {
	switch m := msg.Msg.(type) {
	case *UpdateMasterResetCtrlMsg:
		l4g.Info("baseActivity:%d, parititon:%d UpdateMasterResetCtrlMsg:%+v", bm.GetActivityID(), bm.partition, m)
		bm.UpdateMasterReset(m.ResetTime, m.Servers, m.SessionID)
		return true
	case *TransformCtrlMsg:
		bm.GetRealModule().TransformMsg(bm, m)
		return true
	case *UpdateMasterRepeatedCreateCtrlMsg:
		l4g.Info("baseActivity:%d, parititon:%d UpdateMasterRepeatedCreateCtrlMsg:%+v", bm.GetActivityID(), bm.partition, m)
		bm.UpdateMasterReset(m.ResetTime, m.Servers, m.SessionID)
		bm.ResetNotice()
		return true
	case *UpdateMasterNormalAreaCtrlMsg:
		l4g.Info("baseActivity:%d, parititon:%d UpdateMasterNormalAreaCtrlMsg:%+v", bm.GetActivityID(), bm.partition, m)
		bm.UpdateMasterNormalArea(m.NormalArea)
		return true

	default:
		l4g.Errorf("baseActivity:%d parititon:%d PorcesCtrlRequest:%+v", bm.GetActivityID(), bm.partition, m)
	}
	return false
}

func (bm *BaseModule) ProcessActResetMessage(msg *ActResetMessage) {
	l4g.Infof("baseModule:%d process ActResetMessage:+%v", bm.partition, msg)
	bm.resetTime = msg.ResetTime
	bm.servers = msg.Servers
}

func (bm *BaseModule) SendCmdToMaster(cmd cm2c.ID, msg interface{}) {
	bm.srv.SendCmdToMaster(cmd, msg)
}

func (bm *BaseModule) SendCmdToDB(cmd r2c.ID, msg interface{}) {
	bm.dbm.SendCmdToDB(bm.partition, cmd, 0, msg)
}

func (bm *BaseModule) SendCmdToLogic(sid, uid uint64, cmd l2c.ID, msg interface{}) {
	bm.srv.SendCmdToLogic(bm.GetActivityID(), bm.Partition(), bm.GetSessionID(), sid, uid, cmd, msg)
}

func (bm *BaseModule) SendTransformMsgToNode(actId, partition uint32, protoId uint32, data []byte) {
	msg := &cm2c.C2C_CrossNodeTransform{
		FromActId:     bm.GetActivityID(),
		FromPartition: bm.Partition(),
		ActId:         actId,
		Partition:     partition,
		ProtoId:       protoId,
		Data:          data,
	}
	bm.srv.SendTransformMsgToNode(msg)
}

func (bm *BaseModule) GetRealServerID(sid uint64) uint64 {
	return bm.srv.GetRealServerID(sid)
}

func (bm *BaseModule) GetCurrentServerID(sid uint64) uint64 {
	return bm.srv.GetCurrentServerID(sid)
}

// 判断是否可以重置为新赛季
func (bm *BaseModule) CheckIsNewSeason(nowResetTime int64) (canReset bool, newResetTime int64, servers []uint64) {

	baseResetTime := bm.resetTime
	if baseResetTime == 0 {
		canReset = false
		return
	}
	if nowResetTime == int64(baseResetTime) {
		canReset = false
		return
	}
	canReset = true
	newResetTime = int64(baseResetTime)
	servers = append(servers, bm.servers...)
	return
}

// 获取服务器id列表
func (bm *BaseModule) GetServerIDList() []uint64 {
	return bm.servers
}

func (bm *BaseModule) AddDBMsg(msg *db.Response) {
	l4g.Debugf("basemodule: actid:%d partition:%d AddDBMsg :%+v", bm.actId, bm.partition, msg)
	amsg := &DBResponse{
		Msg:    msg,
		Module: bm,
	}
	bm.AddMessage(amsg)
	ActMetrics.MsgsCount.WithLabelValues("redis_total", bm.GetActivityIDStr(), bm.PartitionStr()).Inc()
	//bm.msgQ <- msg
}

func (m *BaseModule) SendCreateSuccessMsg() {
	msg := &cm2c.C2M_CrossNodeInit{
		Ret:       uint32(ret.RET_OK),
		ActId:     m.actId,
		Partition: m.partition,
	}
	m.SendCmdToMaster(cm2c.ID_MSG_C2M_CrossNodeInit, msg)
}

func (m *BaseModule) GetSessionID() uint32 {
	return m.session
}

// TODO 这里是并发操作. 和下面的flush形成了并发竞争
func (m *BaseModule) UpdateSessionID(sessionID uint32) {
	m.session = sessionID
}

func (m *BaseModule) Flush() {
	l4g.Info("Flush BaseModule act_id:%d partition:%d resetTime:%d servers:%+v, session:%d, running:%v", m.GetActivityID(), m.Partition(), m.resetTime, m.servers, m.session, m.CheckRunning())
	l4g.Debug("Flush BaseModule act_id:%d partition:%d normalArea:%+v", m.GetActivityID(), m.Partition(), m.normalArea)
}

func (m *BaseModule) NewResourceLogMessage() *da.Log {
	return m.srv.NewResourceLogMessage()
}
func (m *BaseModule) WriteResourceLogMessage(msg *da.Log) {
	m.srv.WriteResourceLogMessage(msg)
	//l4g.Debugf("[ResourceLogCollect] write resource log message: %+v", msg)
}

func (m *BaseModule) LogTopicResource() string {
	return m.srv.LogTopicResource()
}

func (m *BaseModule) GetEventM() *event.Manager {
	if m.eventM == nil {
		m.eventM = event.NewManager()
	}
	return m.eventM
}

func (m *BaseModule) GetGroup() *ctx.Group {
	return m.ctx
}

func (m *BaseModule) GetNormalArea(sid uint64) uint32 {
	return m.normalArea[sid]
}
