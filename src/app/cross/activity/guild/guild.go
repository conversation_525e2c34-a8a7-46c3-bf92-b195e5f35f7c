package guild

import (
	"app/cross/activity"
	"app/cross/dclog"
	"app/goxml"
	"app/logic/helper"
	"app/protos/in/cr"
	"app/protos/in/gm"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"slices"
	"sort"

	"gitlab.qdream.com/kit/sea/util"
	"golang.org/x/exp/maps"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type Guild struct {
	data *cr.Guild
	// CandidateLeader *CandidateLeader

	ActivityPoints uint32
	ActivityNum    uint32
	YesterdayNum   uint32

	m *Manager
}

func (g *Guild) GetData() *cr.Guild {
	return g.data
}

func (g *Guild) init() {
	nowDailyZero := int64(util.DailyZeroByTime(time.Now().Unix()))
	for _, member := range g.data.Members {
		memberActivity := g.GetMemberActivityPoint(member)
		if memberActivity > 0 {
			g.ActivityPoints += memberActivity
			g.ActivityNum++
		}
		if g.IsMemberYesterdayActivity(nowDailyZero, member) {
			g.YesterdayNum++
		}
	}
	g.checkInitMedal()
}

/*func (g *Guild) NewCandidateLeader(id uint64, tm, power int64) {
	g.CandidateLeader = &CandidateLeader{
		ID:      id,
		LoginTm: tm,
		Power:   power,
	}
}

type CandidateLeader struct { //会长候选人，会长长时间不在线，就任命为会长
	ID      uint64
	Power   int64
	LoginTm int64
}

func (c *CandidateLeader) CheckAndUpdate(g *Guild, member *cr.GuildMember, power int64) {
	if member.Grade == activity.GuildGradeLeader {
		return
	}
	if g.CandidateLeader == nil {
		g.NewCandidateLeader(member.Id, time.Now().Unix(), power)
		return
	}
	newCandidate := &CandidateLeader{
		ID:      member.Id,
		LoginTm: time.Now().Unix(),
		Power:   power,
	}
	if c.needNewCandidate(g, newCandidate) { // true：将会长候选人设置为newCandidate
		g.CandidateLeader = newCandidate
	}
}

// 判断是否要设置新的会长候选人
func (c *CandidateLeader) needNewCandidate(g *Guild, newCandidate *CandidateLeader) bool {
	oldCandidate := g.CandidateLeader
	if oldCandidate.LoginTm+goxml.GetData().GuildConfigInfoM.MemberCheckActiveDays < time.Now().Unix() { // 旧的候选人过期了
		return true
	}

	// 比较职位
	if oldCandidateMem, newCandidateMem := g.GetMember(oldCandidate.ID), g.GetMember(newCandidate.ID); oldCandidateMem != nil && newCandidateMem != nil {
		if activity.CmpGuildGrade(oldCandidateMem.GetGrade(), newCandidateMem.GetGrade()) < 0 {
			return false
		} else if activity.CmpGuildGrade(oldCandidateMem.GetGrade(), newCandidateMem.GetGrade()) > 0 {
			return true
		}
	}

	if oldCandidate.Power < newCandidate.Power { // 旧的候选人战力没有新候选人高
		return true
	}
	return false
}*/

func (g *Guild) GetID() uint64 {
	return g.data.Id
}

func (g *Guild) GetName() string {
	return g.data.Name
}

func (g *Guild) GetLevel() uint32 {
	return g.data.Level
}

func (g *Guild) setLevel(newLv uint32) {
	g.data.Level = newLv

	if g.data.Level >= goxml.GetData().GuildConfigInfoM.GetGuildMedalOpenLevel() && g.getMedal() == nil {
		g.initMedal() // 功勋解锁，初始化功勋
	}
}

func (g *Guild) GetExp() uint32 {
	return g.data.Exp
}

func (g *Guild) GetBadge() uint32 {
	return g.data.Badge
}

func (g *Guild) GetMembers() map[uint64]*cr.GuildMember {
	return g.data.Members
}

func (g *Guild) GetSidMembers(baseModule activity.BaseModuler) map[uint64]*cr.GuildMembers {
	if g.data == nil || g.data.Members == nil {
		return nil
	}

	members := make(map[uint64]*cr.GuildMembers, len(g.data.Members))

	for _, member := range g.data.Members {
		realSid := member.Sid
		// todo 这里对于合服玩家的真实SID待处理
		//realSid := baseModule.GetRealServerID(member.Sid)
		guildMembersBySid := members[realSid]
		if guildMembersBySid == nil {
			guildMembersBySid = &cr.GuildMembers{}
			members[realSid] = guildMembersBySid
		}
		guildMembersBySid.Members = append(guildMembersBySid.Members, member.Clone())
	}
	return members
}

func (g *Guild) GetLeader() uint64 {
	return g.data.Leader
}

func (g *Guild) IsGuildMember(id uint64) bool {
	if g.data.Members == nil {
		return false
	}
	if g.data.Members[id] == nil {
		return false
	}
	return true
}

func (g *Guild) GetMember(id uint64) *cr.GuildMember {
	if g.data.Members == nil {
		return nil
	}
	return g.data.Members[id]
}

func (g *Guild) GetUniqID() uint64 {
	return g.data.Id
}

func (g *Guild) GetLogIndex() uint32 {
	return g.data.LogIndexId
}

func (g *Guild) SetLogIndex(index uint32) {
	g.data.LogIndexId = index
}

// 转让会长
func (g *Guild) Transfer(guildM *Manager, oldLeader, newLeader *cr.GuildMember) {
	g.setNewLeader(newLeader) // 设置新会长

	newLeader.Grade = activity.GuildGradeLeader // 设置新职位
	oldLeader.Grade = activity.GuildGradeMember

	// 取消我方公会发起的所有有效申请
	guildM.CancelAllSourceCombineApply(g)
}

// 设置会长
func (g *Guild) setNewLeader(newLeader *cr.GuildMember) {
	if newLeader.Grade == activity.GuildGradeDeputy {
		g.removeDeputy(newLeader.Id)
	}
	if newLeader.Grade == activity.GuildGradeRegiment {
		g.removeRegiment(newLeader.Id)
	}
	g.setLeader(newLeader.Id)
}

func (g *Guild) removeDeputy(id uint64) {
	for i := 0; i < len(g.data.Deputy); i++ {
		if g.data.Deputy[i] == id {
			g.data.Deputy = append(g.data.Deputy[:i], g.data.Deputy[i+1:]...)
			break
		}
	}
}

func (g *Guild) removeRegiment(id uint64) {
	for i := 0; i < len(g.data.Regiments); i++ {
		if g.data.Regiments[i] == id {
			g.data.Regiments = append(g.data.Regiments[:i], g.data.Regiments[i+1:]...)
			break
		}
	}
}

func (g *Guild) setLeader(id uint64) {
	g.data.Leader = id
	g.data.LeaderLoginTm = time.Now().Unix() //会长被转让后，会长的登录时间暂时设置为当前时间，新会长登录会刷新
}

// 获取需要发邮件的成员
func (g *Guild) GetMailMembersWithExcludeId(excludeIds []uint64) map[uint64][]uint64 {
	mailMembers := make(map[uint64][]uint64)
	exclude := make(map[uint64]struct{}, len(excludeIds))
	for _, id := range excludeIds {
		exclude[id] = struct{}{}
	}
	for _, member := range g.data.Members {
		if member == nil {
			continue
		}
		if _, exist := exclude[member.Id]; exist {
			continue
		}
		mailMembers[member.Sid] = append(mailMembers[member.Sid], member.Id)
	}
	return mailMembers
}

func (g *Guild) GetMailMembers() map[uint64][]uint64 {
	mailMembers := make(map[uint64][]uint64)
	for _, member := range g.data.Members {
		mailMembers[member.Sid] = append(mailMembers[member.Sid], member.Id)
	}
	return mailMembers
}

func (g *Guild) MemberCnt() uint32 {
	return uint32(len(g.GetMembers()))
}

// 罢免副会长
func (g *Guild) RecallDeputy(member *cr.GuildMember) {
	g.removeDeputy(member.Id)
	member.Grade = activity.GuildGradeMember
}

// 罢免团长
func (g *Guild) RecallRegiment(member *cr.GuildMember) {
	g.removeRegiment(member.Id)
	member.Grade = activity.GuildGradeMember
}

func (g *Guild) GetDeputy() []uint64 {
	return g.data.Deputy
}

func (g *Guild) GetRegiment() []uint64 {
	return g.data.Regiments
}

func (g *Guild) AddDeputy(id uint64) {
	g.data.Deputy = append(g.data.Deputy, id)
}

func (g *Guild) AddRegimen(id uint64) {
	g.data.Regiments = append(g.data.Regiments, id)
}

// 任命副会长
func (g *Guild) AppointDeputy(member *cr.GuildMember) {
	g.removeDeputy(member.Id)   //如果是副会长，就去掉
	g.removeRegiment(member.Id) //如果是团长，就去掉
	g.AddDeputy(member.Id)
	member.Grade = activity.GuildGradeDeputy
}

// 任命团长
func (g *Guild) AppointRegiment(member *cr.GuildMember) {
	g.removeDeputy(member.Id)   //如果是副会长，就去掉
	g.removeRegiment(member.Id) //如果是团长，就去掉
	g.AddRegimen(member.Id)
	member.Grade = activity.GuildGradeRegiment
}

// 成员退出
func (g *Guild) MemberQuit(member *cr.GuildMember) {
	//减去自己的活跃度
	memberActivity := g.GetMemberActivityPoint(member)
	if memberActivity > 0 {
		g.ActivityPoints -= memberActivity
		g.ActivityNum--
	}
	nowDailyZero := int64(util.DailyZeroByTime(time.Now().Unix()))
	if g.IsMemberYesterdayActivity(nowDailyZero, member) {
		g.YesterdayNum--
	}

	g.removeDeputy(member.Id)   //如果是副会长，就去掉
	g.removeRegiment(member.Id) //如果是团长，就去掉
	g.deleteMember(member)
	/*if g.CandidateLeader != nil && g.CandidateLeader.ID == member.Id {
		g.CandidateLeader = nil
	}*/
	g.MobOnMemberQuit(member.Id)
}

func (g *Guild) deleteMember(targetMember *cr.GuildMember) {
	delete(g.data.Members, targetMember.Id)
}

// 判断是不是公会的管理者
func (g *Guild) IsGuildManager(id uint64) bool {
	if id == g.GetLeader() {
		return true
	}
	for _, deputy := range g.GetDeputy() {
		if deputy == id {
			return true
		}
	}
	return false
}

// 判断是不是公会会长
func (g *Guild) IsGuildLeader(id uint64) bool {
	return id == g.GetLeader()
}

func (g *Guild) ModifyInfo(msg *l2c.L2C_GuildModifyInfo) {
	g.data.Badge = msg.Badge
	g.data.JoinType = msg.JoinType
	g.data.Declaration = msg.Declaration
	g.data.Language = msg.Language
	g.data.LvLimit = msg.LvLimit
	g.data.PowerLimit = msg.PowerLimit
	g.data.Label = msg.Label
}

func (g *Guild) CheckSetNameTm() bool {
	return g.data.UpdateNameTm+goxml.GetData().GuildConfigInfoM.NameChangeDelay < time.Now().Unix()
}

func (g *Guild) SetName(name string, now int64) {
	g.data.Name = name
	g.data.UpdateNameTm = now
	logData := &log.ESGuildInfo{
		XId:      g.GetID(),
		Name:     g.GetName(),
		ServerId: g.GetSid(),
	}
	dclog.LogGuildInfo(g.m.baseModule.GetSrv(), logData)
}

func (g *Guild) ModifyNotice(newNotice string) {
	g.data.Notice = newNotice
}

func (g *Guild) getNotice() string {
	return g.data.Notice
}

func (g *Guild) AddNoticeId() uint64 {
	g.data.NoticeId++
	return g.data.NoticeId
}

func (g *Guild) GetApplyList() []*cr.GuildApplyInfo {
	return g.data.ApplyInfos
}

func (g *Guild) GetApplyInfo(id uint64) *cr.GuildApplyInfo {
	for _, info := range g.data.ApplyInfos {
		if info.Uid == id {
			return info
		}
	}
	return nil
}

func (g *Guild) newMember(id, sid uint64, name string, grade uint32, lastLeaveTm int64, leaveCount []*cl.GuildLeaveCount) *cr.GuildMember {
	return &cr.GuildMember{
		Id:            id,
		Name:          name,
		Grade:         grade,
		Sid:           sid,
		LastLeaveTm:   lastLeaveTm,
		GuildLeaveCnt: leaveCount,
	}
}

func (g *Guild) addMember(member *cr.GuildMember) {
	g.data.Members[member.Id] = member
}

func (g *Guild) Refuse(uid uint64) *cr.GuildApplyInfo {
	for i := 0; i < len(g.data.ApplyInfos); i++ {
		if g.data.ApplyInfos[i].Uid == uid {
			applyInfo := g.data.ApplyInfos[i].Clone()
			g.data.ApplyInfos = append(g.data.ApplyInfos[:i], g.data.ApplyInfos[i+1:]...)
			return applyInfo
		}
	}
	return nil
}

func (g *Guild) IsCanJoin() uint32 {
	if g.data.JoinType == goxml.GuildJoinOpen {
		return uint32(ret.RET_OK)
	}
	if g.data.JoinType == goxml.GuildJoinNeedApply {
		return uint32(ret.RET_GUILD_NEED_APPLY)
	}
	if g.data.JoinType == goxml.GuildJoinClose {
		return uint32(ret.RET_GUILD_REFUSE_JOIN)
	}
	return uint32(ret.RET_ERROR)
}

func (g *Guild) IsCanApply() uint32 {
	if g.data.JoinType == goxml.GuildJoinNeedApply {
		return uint32(ret.RET_OK)
	}
	if g.data.JoinType == goxml.GuildJoinClose {
		return uint32(ret.RET_GUILD_REFUSE_JOIN)
	}
	return uint32(ret.RET_ERROR)
}

func (g *Guild) ApplyListCnt() uint32 {
	return uint32(len(g.data.ApplyInfos))
}

func (g *Guild) DeleteApplyID(id uint64) {
	for i := 0; i < len(g.data.ApplyInfos); i++ {
		if g.data.ApplyInfos[i].Uid != id {
			continue
		}
		g.data.ApplyInfos = append(g.data.ApplyInfos[:i], g.data.ApplyInfos[i+1:]...)
		break
	}
}

func (g *Guild) AddNewApplicant(applyInfo *cr.GuildApplyInfo) {
	g.data.ApplyInfos = append(g.data.ApplyInfos, applyInfo)
}

func (g *Guild) AddSourceCombineApplies(apply *cr.GuildCombineApplyInfo) {
	g.data.SourceCombineApplyInfos = append(g.data.SourceCombineApplyInfos, apply)
}

func (g *Guild) AddTargetCombineApplies(apply *cr.GuildCombineApplyInfo) {
	g.data.TargetCombineApplyInfos = append(g.data.TargetCombineApplyInfos, apply)
}

func (g *Guild) DelSourceCombineApply(targetGid uint64) {
	for i := 0; i < len(g.data.SourceCombineApplyInfos); i++ {
		if g.data.SourceCombineApplyInfos[i].Gid == targetGid {
			g.data.SourceCombineApplyInfos = append(g.data.SourceCombineApplyInfos[:i], g.data.SourceCombineApplyInfos[i+1:]...)
			i--
		}
	}
}

// 需要保证请求列表中只存在一个公会的一个请求
func (g *Guild) DelTargetCombineApply(sourceGid uint64) {
	for i := 0; i < len(g.data.TargetCombineApplyInfos); i++ {
		if g.data.TargetCombineApplyInfos[i].Gid == sourceGid {
			g.data.TargetCombineApplyInfos = append(g.data.TargetCombineApplyInfos[:i], g.data.TargetCombineApplyInfos[i+1:]...)
			i--
		}
	}
}

func (g *Guild) FlushCombineAppliedList(guildM *Manager) []*cl.GuildCombineApplyItem {
	var res []*cl.GuildCombineApplyItem
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, v := range g.data.TargetCombineApplyInfos { //nolint:varnamelen
		expireTime := v.ApplyTime + duration
		if time.Now().Unix() >= expireTime {
			continue
		}
		targetGuild := guildM.GetGuildByID(v.Gid)
		if targetGuild == nil {
			continue
		}
		res = append(res, &cl.GuildCombineApplyItem{
			SourceGid:  v.Gid, // 发起公会为对方公会的id
			Guild:      guildM.GenerateSnapshot(targetGuild),
			Type:       v.Type,
			ExpireTime: expireTime,
		})
	}
	return res
}

func (g *Guild) GetValidCombineAppliedListNum() uint32 {
	var num uint32
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, v := range g.data.TargetCombineApplyInfos {
		if time.Now().Unix() >= v.ApplyTime+duration {
			continue
		}
		num++
	}
	return num
}

func (g *Guild) FlushCombineApplyList(guildM *Manager) []*cl.GuildCombineApplyItem {
	var res []*cl.GuildCombineApplyItem
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, v := range g.data.SourceCombineApplyInfos { //nolint:varnamelen
		expireTime := v.ApplyTime + duration
		if time.Now().Unix() >= expireTime {
			continue
		}
		targetGuild := guildM.GetGuildByID(v.Gid)
		if targetGuild == nil {
			continue
		}
		res = append(res, &cl.GuildCombineApplyItem{
			SourceGid:  g.data.Id, // 发起公会为自己公会的id
			Guild:      guildM.GenerateSnapshot(targetGuild),
			Type:       v.Type,
			ExpireTime: expireTime,
		})
	}
	return res
}

func (g *Guild) GetValidCombineApplyListNum() uint32 {
	var num uint32
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, v := range g.data.SourceCombineApplyInfos {
		if time.Now().Unix() >= v.ApplyTime+duration {
			continue
		}
		num++
	}
	return num
}

func (g *Guild) IsSourceValidCombineApplyExist(targetGid uint64) bool {
	now := time.Now().Unix()
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, apply := range g.data.SourceCombineApplyInfos {
		if apply.Gid == targetGid && now <= apply.ApplyTime+duration {
			return true
		}
	}
	return false
}

func (g *Guild) IsTargetValidCombineApplyExist(sourceGid uint64) bool {
	now := time.Now().Unix()
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, apply := range g.data.TargetCombineApplyInfos {
		if apply.Gid == sourceGid && now <= apply.ApplyTime+duration {
			return true
		}
	}
	return false
}

func (g *Guild) GetSourceValidCombineApply(targetGid uint64) *cr.GuildCombineApplyInfo {
	now := time.Now().Unix()
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, apply := range g.data.SourceCombineApplyInfos {
		if apply.Gid == targetGid && now <= apply.ApplyTime+duration {
			return apply
		}
	}
	return nil
}

func (g *Guild) GetTargetValidCombineApply(sourceGid uint64) *cr.GuildCombineApplyInfo {
	now := time.Now().Unix()
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for _, apply := range g.data.TargetCombineApplyInfos {
		if apply.Gid == sourceGid && now <= apply.ApplyTime+duration {
			return apply
		}
	}
	return nil
}

// 最后一个请求必然是最新的请求
func (g *Guild) GetLastTargetCombineApplyTime() int64 {
	if len(g.data.TargetCombineApplyInfos) == 0 {
		return 0
	}
	lastApply := g.data.TargetCombineApplyInfos[len(g.data.TargetCombineApplyInfos)-1]
	if lastApply != nil {
		return lastApply.ApplyTime
	}
	return 0
}

func (g *Guild) GetManagersIds() map[uint64][]uint64 {
	managers := make(map[uint64][]uint64, 3) //nolint:mnd
	leaderId := g.GetLeader()
	memberInfo := g.GetMember(leaderId)
	if memberInfo != nil {
		managers[memberInfo.Sid] = append(managers[memberInfo.Sid], memberInfo.Id)
	}
	for _, id := range g.GetDeputy() {
		memberInfo = g.GetMember(id)
		if memberInfo != nil {
			managers[memberInfo.Sid] = append(managers[memberInfo.Sid], memberInfo.Id)
		}
	}
	return managers
}

func (g *Guild) setLastAccessTm(now int64) {
	g.data.LastAccessTime = now
}

// addExp
// @Description:
// @receiver g
// @param add
// @return bool ： 返回是否等级提升
func (g *Guild) addExp(add uint32) bool {
	levelInfo := goxml.GetData().GuildLevelInfoM.Index(g.GetLevel())
	if levelInfo == nil {
		l4g.Errorf("guild.addExp: levelInfo is nil. level:%d", g.GetLevel())
		return false
	}
	if g.data.Exp+add >= levelInfo.Exp {
		nextLevel := goxml.GetData().GuildLevelInfoM.Index(g.GetLevel() + 1)
		if nextLevel == nil {
			g.data.Exp = levelInfo.Exp // 升到最大级，还可以签到，不再增加经验和等级
			return false
		}
		g.setLevel(g.GetLevel() + 1)
		g.data.Exp = g.data.Exp + add - levelInfo.Exp
		return true
	}
	g.data.Exp += add
	return false
}

func (g *Guild) AddDonatePerson(userID uint64) {
	for _, v := range g.data.DonatePersonIds {
		if v == userID {
			return
		}
	}
	g.data.DonatePersonIds = append(g.data.DonatePersonIds, userID)
}

func (g *Guild) AddDonatePointNum(donatePoint uint32) uint32 {
	g.data.DonatePointNum += donatePoint
	return g.data.DonatePointNum
}

func (g *Guild) GetDonateNum() uint32 {
	return g.data.DonatePointNum
}

// 增加活跃度
func (g *Guild) addWeeklyActivityPoint(activityPoint uint32) uint32 {
	g.ActivityPoints += activityPoint
	return g.ActivityPoints
}

func (g *Guild) AddMemberActivityPoint(uid uint64, count uint32) {
	member := g.GetMember(uid)
	if member == nil {
		return
	}

	now := time.Now().Unix()
	weekDay := helper.WeekdayNoTransform(now)
	if len(member.ActivityPoint) != activity.GuildActivityPointLen {
		tmp := make([]uint32, activity.GuildActivityPointLen)
		copy(tmp, member.ActivityPoint)
		member.ActivityPoint = tmp
	}
	member.ActivityPoint[weekDay] += count
}

// 获取活跃数据
func (g *Guild) CheckResetActivityData() {
	if g.timerResetDaily(int64(util.DailyZeroByTime(time.Now().Unix()))) {
		dungeon := g.m.dungeonM.GetDungeonByID(g.GetID())
		if dungeon != nil {
			dungeon.resetDaily()
		}
	}
}

func (g *Guild) GetDonatePersonNum() uint32 {
	return uint32(len(g.data.DonatePersonIds))
}

func (g *Guild) CheckDonateAwardCondition(point uint32) bool {
	return g.data.DonatePointNum >= point
}

func (g *Guild) GetLanguage() string {
	return g.data.Language
}

func (g *Guild) GetLabel() uint32 {
	return g.data.Label
}

func (g *Guild) GetJoinType() uint32 {
	return g.data.JoinType
}

func (g *Guild) GetLvLimit() uint32 {
	return g.data.LvLimit
}

func (g *Guild) GetPowerLimit() int64 {
	return g.data.PowerLimit
}

func (g *Guild) GetDeclaration() string {
	return g.data.Declaration
}

func (g *Guild) GetSid() uint64 {
	return g.data.Sid
}

func (g *Guild) GenerateUpdateInfo() *l2c.C2L_GuildUpdateInfo {
	return &l2c.C2L_GuildUpdateInfo{
		Ret:            uint32(ret.RET_OK),
		Gid:            g.GetID(),
		Name:           g.GetName(),
		Level:          g.GetLevel(),
		Exp:            g.GetExp(),
		DonatePointNum: g.GetDonateNum(),
		//DonatePersonNum: g.GetDonatePersonNum(),
	}
}

func (g *Guild) CheckJoinLimit(level uint32, power int64) uint32 {
	if level < g.data.LvLimit {
		return uint32(ret.RET_GUILD_JOIN_LEVEL_LIMIT)
	}
	if power < g.data.PowerLimit {
		return uint32(ret.RET_GUILD_JOIN_POWER_LIMIT)
	}
	return uint32(ret.RET_OK)
}

func (g *Guild) UpdateMemberInfo(id uint64, self *cr.GuildMember, power int64) {
	member := g.GetMember(id)

	if member == nil {
		l4g.Errorf("Guild.UpdateMemberInfo: error. id:%d", id)
		return
	}

	if member != nil && self.Name != "" {
		member.Name = self.Name
	}

	// g.CandidateLeader.CheckAndUpdate(g, member, power)
	if member.Grade == activity.GuildGradeLeader {
		g.data.LeaderLoginTm = time.Now().Unix()
	}
	member.Sid = self.Sid
	member.ActivityPoint = self.ActivityPoint
	member.BaseId = self.BaseId
	member.ExpireTime = self.ExpireTime
}

func (g *Guild) SortMembersByWeeklyDungeonDamage() []*cr.GuildMember {
	memberSlice := make([]*cr.GuildMember, 0, goxml.GetData().GuildLevelInfoM.GetMaxMemberCount())
	for _, member := range g.data.Members {
		memberSlice = append(memberSlice, member)
	}
	sort.Slice(memberSlice, func(i, j int) bool {
		if memberSlice[i].WeeklyDamage > memberSlice[j].WeeklyDamage {
			return true
		} else if memberSlice[i].WeeklyDamage == memberSlice[j].WeeklyDamage {
			if memberSlice[i].Id > memberSlice[j].Id {
				return true
			}
		}
		return false
	})
	return memberSlice
}

func (g *Guild) timerResetDaily(dailyZero int64) bool {
	if dailyZero == g.data.DailyZero {
		return false
	}

	g.SetKickCount(0)
	g.data.DonatePointNum = 0
	g.data.DonatePersonIds = g.data.DonatePersonIds[:0]
	g.membersDailyReset(dailyZero)
	g.medalReset()
	g.delExpiredCombineApply()
	g.mobilizationDailyReset()
	g.data.DailyZero = dailyZero
	return true
}

func (g *Guild) membersDailyReset(nowDailyZero int64) {
	newGuildActivity := uint32(0)
	newActivityNum := uint32(0)
	newYesterdayNum := uint32(0)
	for _, member := range g.data.Members {
		g.memberResetWeeklyActivity(nowDailyZero, member)
		g.memberResetWeeklyDamage(nowDailyZero, member)
		g.memberResetWeeklyMedalLike(nowDailyZero, member)
		point := g.GetMemberActivityPoint(member)
		if point > 0 {
			newGuildActivity += point
			newActivityNum++
		}
		if g.IsMemberYesterdayActivity(nowDailyZero, member) {
			newYesterdayNum++
		}
	}
	g.ActivityPoints = newGuildActivity
	g.ActivityNum = newActivityNum
	g.YesterdayNum = newYesterdayNum
}

func (g *Guild) memberResetWeeklyDamage(nowDailyZero int64, member *cr.GuildMember) {
	if goxml.GetGuildDungeonResetDayZero(nowDailyZero) == goxml.GetGuildDungeonResetDayZero(g.data.DailyZero) {
		return
	}
	member.WeeklyDamage = 0
	member.WeeklyFightTimes = 0
}

func (g *Guild) memberResetWeeklyActivity(nowDailyZero int64, member *cr.GuildMember) {
	//大于等于7天重制
	day := uint32((nowDailyZero - g.data.DailyZero) / util.DaySecs)
	if day >= 7 { //nolint:mnd
		member.ActivityPoint = make([]uint32, activity.GuildActivityPointLen)
		return
	}
	weekDay := helper.WeekdayNoTransform(nowDailyZero)
	index := weekDay
	if len(member.ActivityPoint) < activity.GuildActivityPointLen {
		tmp := make([]uint32, activity.GuildActivityPointLen)
		copy(tmp, member.ActivityPoint)
		member.ActivityPoint = tmp
	}
	for i := day; i > 0; i-- {
		member.ActivityPoint[index] = 0
		if index == 0 {
			index = activity.GuildActivityPointLen
		}
		index--
	}
}

func (g *Guild) memberResetWeeklyMedalLike(nowDailyZero int64, member *cr.GuildMember) {
	if helper.GetResetTime(uint32(common.RESET_TYPE_WEEKLY_FRIDAY), nowDailyZero) ==
		helper.GetResetTime(uint32(common.RESET_TYPE_WEEKLY_FRIDAY), g.data.DailyZero) {
		return
	}
	if member.MedalLike == nil {
		return
	}
	member.MedalLike.LastWeeklyLikeCount = member.MedalLike.WeeklyLikeCount
	member.MedalLike.LastWeeklyLikeName = make([]string, len(member.MedalLike.GetLikedName()))
	copy(member.MedalLike.LastWeeklyLikeName, member.MedalLike.GetLikedName())
	member.MedalLike.WeeklyLikeCount = 0
	member.MedalLike.LikedName = member.MedalLike.LikedName[0:0]
}

func (g *Guild) GetMemberActivityPoint(member *cr.GuildMember) uint32 {
	if member == nil {
		return 0
	}
	points := uint32(0)
	for _, point := range member.ActivityPoint {
		points += point
	}
	return points
}

/*
func (g *Guild) GetMembersActivityPoint() uint32 {
	points := uint32(0)
	for _, member := range g.data.Members {
		points += g.GetMemberActivityPoint(member)
	}
	return points
}
*/

func (g *Guild) UpdateMemberActivity(gm *Manager) {
	nowDailyZero := int64(util.DailyZeroByTime(time.Now().Unix()))
	var newActivityPoints, newActivityNum, newYesterdayNum uint32
	for _, member := range g.data.Members {
		point := g.GetMemberActivityPoint(member)
		if point > 0 {
			newActivityPoints += point
			newActivityNum++
		}
		if g.IsMemberYesterdayActivity(nowDailyZero, member) {
			newYesterdayNum++
		}
	}
	transferChange := false
	if g.ActivityNum != newActivityNum {
		g.ActivityNum = newActivityNum
		transferChange = true
	}
	if g.ActivityPoints != newActivityPoints {
		g.ActivityPoints = newActivityPoints
		gm.ActivityRank().Update(g)
		transferChange = true
	}
	if transferChange {
		gm.transferM.updateRank(g)
		gm.combineListM.updateRank(g)
	}
	if g.YesterdayNum != newYesterdayNum {
		g.YesterdayNum = newYesterdayNum
		gm.YesterdayRank().Update(g)
		gm.UpdateYesterdayNum(g)
	}
}

func (g *Guild) IsMemberYesterdayActivity(nowDailyZero int64, member *cr.GuildMember) bool {
	if member == nil {
		return false
	}
	yesterday := int(helper.WeekdayNoTransform(nowDailyZero)) - 1
	if yesterday < 0 {
		yesterday = activity.GuildActivityPointLen - 1
	}
	if len(member.ActivityPoint) <= yesterday || member.ActivityPoint[yesterday] <= 0 {
		return false
	}
	return true
}

func (g *Guild) addBadge(id uint32) {
	info := goxml.GetData().GuildBadgeInfoM.Index(id)
	if info == nil {
		l4g.Errorf("guild.addBadge: badgeInfo not exist. id:%d", id)
		return
	}
	if info.IsUnlock == 0 { // 默认获得
		return
	}
	if g.data.Badges == nil {
		g.data.Badges = make(map[uint32]*cl.Avatar)
	}
	now := time.Now().Unix()
	avatar := g.data.Badges[id]
	if avatar == nil {
		avatar = &cl.Avatar{
			SysId:     id,
			StartTime: now,
		}
	}
	switch info.DurationType {
	case goxml.AvatarTimeTypeForever:

	case goxml.AvatarTimeTypeDuration:
		if now <= avatar.EndTime {
			avatar.EndTime += int64(info.Duration * goxml.SecondPerMinute)
		} else {
			avatar.StartTime = now
			avatar.EndTime = now + int64(info.Duration*goxml.SecondPerMinute)
		}
	case goxml.AvatarTimeTypeEndTime:
		avatar.StartTime = now
		avatar.EndTime = now + int64(info.Duration*goxml.SecondPerMinute)
	}
	g.data.Badges[id] = avatar
}

func (g *Guild) SetLevelForGm(level uint32) {
	levelInfo := goxml.GetData().GuildLevelInfoM.Index(level)
	if levelInfo == nil {
		return
	}
	g.setLevel(level)
	g.data.Exp = levelInfo.Exp
}

func (g *Guild) getCurrentBadgeExpireTm() []int64 {
	expireTm := make([]int64, 2)
	background := g.getBadgeBackground()
	bInfo := goxml.GetData().GuildBadgeInfoM.Index(background)
	if bInfo != nil {
		if bInfo.IsUnlock == 1 {
			bData := g.data.Badges[background]
			if bData != nil {
				expireTm[1] = bData.EndTime
			}
		}
	}
	icon := g.getBadgeIcon()
	iInfo := goxml.GetData().GuildBadgeInfoM.Index(icon)
	if iInfo != nil {
		if iInfo.IsUnlock == 1 {
			iData := g.data.Badges[icon]
			if iData != nil {
				expireTm[0] = iData.EndTime
			}
		}
	}
	return expireTm
}

func (g *Guild) getBadgeIcon() uint32 {
	icon := g.GetBadge() & 0xFFFF //nolint:mnd
	return icon
}

func (g *Guild) getBadgeBackground() uint32 {
	background := g.GetBadge() >> 16 //nolint:mnd
	return background
}

func (g *Guild) GetBadgeList() []*cl.Avatar {
	list := make([]*cl.Avatar, 0, len(g.data.Badges))
	for _, avatar := range g.data.Badges {
		list = append(list, avatar.Clone())
	}
	return list
}

func (g *Guild) CheckBadgeUnlock(id uint32) bool {
	if g.data.Badges == nil {
		return false
	}
	badgeData := g.data.Badges[id]
	if badgeData == nil {
		return false
	}
	if badgeData.EndTime != 0 && time.Now().Unix() > badgeData.EndTime {
		l4g.Errorf("guild.CheckBadgeUnlock: badge expired. id:%d expireTm:%d", badgeData.SysId, badgeData.EndTime)
		return false
	}
	return true
}

func (g *Guild) CheckApplyRepeat(newApply *cr.GuildApplyInfo) bool {
	for _, info := range g.data.ApplyInfos {
		if info == nil {
			continue
		}
		if info.Uid == newApply.Uid {
			return true
		}
	}
	return false
}

func (g *Guild) DelExpireChestsAndFlush2Logic(now int64, uid uint64) ([]*cl.GuildChestData, []*cr.GuildChest, bool) {
	ret := make([]*cl.GuildChestData, 0, len(g.data.GuildChests))
	deleteData := make([]*cr.GuildChest, 0, len(g.data.GuildChests)/2)
	var change bool
	for id, guildChest := range g.data.GuildChests {
		if guildChest == nil {
			continue
		}
		if now >= guildChest.ExpireTime {
			deleteData = append(deleteData, guildChest)
			delete(g.data.GuildChests, id)
			//todo 发送对应消息给GuildUser
			change = true
		}

		ret = append(ret, (*CrGuildChest)(guildChest).Convert2Data(uid))
	}
	return ret, deleteData, change
}

func (g *Guild) GetChest(id uint64, now int64) (*CrGuildChest, bool) {
	var change bool
	if g.data.GuildChests == nil {
		return nil, change
	}
	guildChest, exist := g.data.GuildChests[id]
	if !exist {
		return nil, change
	}
	if guildChest.ExpireTime < now {
		delete(g.data.GuildChests, id)
		change = true
		return nil, change
	}
	return (*CrGuildChest)(guildChest), change
}

func (g *Guild) CheckBeforeActivateChest(chestId uint32, expiredTime, now int64, id, uid uint64) (uint32, *goxml.GuildChestInfo) {
	if now > expiredTime {
		l4g.Errorf("uid:%d CheckBeforeActivateChest guildChest Item:%d is expired time:%d now:%d", uid, chestId, expiredTime, now)
		return uint32(ret.RET_GUILD_CHEST_ITEM_IS_EXPIRE), nil
	}
	chestInfo := goxml.GetData().GuildChestInfoM.Index(chestId)
	if chestInfo == nil {
		l4g.Errorf("uid:%d CheckBeforeActivateChest guildChest Item:%d chestId:%d is error", uid, id, chestId)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	if uint32(len(g.data.GuildChests)) >= goxml.GetData().GuildConfigInfoM.GetGuildChestMaxNum() {
		l4g.Errorf("uid:%d CheckBeforeActivateChest guildChest guild:%d attach chest limit", uid, g.data.Id)
		return uint32(ret.RET_GUILD_CHEST_ATTACH_LIMIT), nil
	}

	if g.data.GuildChests != nil {
		_, exist := g.data.GuildChests[id]
		if exist {
			l4g.Errorf("uid:%d CheckBeforeActivateChest guildChest has activate", uid)
			return uint32(ret.RET_GUILD_CHEST_CHEST_IS_EXIST), nil
		}
	}

	return uint32(ret.RET_OK), chestInfo
}

func (g *Guild) ActivateChest(chest *cr.GuildChest) {
	if g.data.GuildChests == nil {
		g.data.GuildChests = make(map[uint64]*cr.GuildChest)
	}
	g.data.GuildChests[chest.Id] = chest
}

func (g *Guild) BuildExpiredMessage(baseModule activity.BaseModuler, guildChests []*cr.GuildChest) map[uint64][]*l2c.GuildChestExpired {
	if g.data == nil || g.data.Members == nil {
		return nil
	}

	serverExpireds := make(map[uint64][]*l2c.GuildChestExpired)

	for _, guildChest := range guildChests {
		if guildChest == nil {
			continue
		}
		member, exist := g.data.Members[guildChest.Uid]
		if !exist {
			continue
		}
		realSid := member.Sid
		// todo 这里对于合服玩家的真实SID待处理
		//realSid := baseModule.GetRealServerID(member.Sid)
		_, exist = serverExpireds[realSid]
		if !exist {
			serverExpireds[realSid] = make([]*l2c.GuildChestExpired, 0, 2)
		}
		serverExpireds[realSid] = append(serverExpireds[realSid], (*CrGuildChest)(guildChest).Convert2Expired())
	}

	return serverExpireds
}

func (g *Guild) IsMemberFull() bool {
	lvInfo := goxml.GetData().GuildLevelInfoM.Index(g.GetLevel())
	if lvInfo == nil {
		return true
	}

	return g.MemberCnt() >= lvInfo.Member
}

func (g *Guild) GetCreateTm() int64 {
	return g.data.CreateTm
}

func (g *Guild) SetKickCount(count uint32) {
	g.data.KickCount = count
}

func (g *Guild) GetKickCount() uint32 {
	return g.data.KickCount
}

// 获取官员的服务器id与uid数据
// @param uint64 avoidID 要过滤的id，为0时即返回全部官员
// @return map[uint64][]uint64
func (g *Guild) GetOfficialSid2Uids(avoidID uint64) map[uint64][]uint64 {
	idList := make([]uint64, 0, 1)
	if g.GetLeader() != avoidID {
		idList = append(idList, g.GetLeader())
	}
	for _, id := range g.GetDeputy() {
		if id != avoidID {
			idList = append(idList, id)
		}
	}

	ret := make(map[uint64][]uint64)
	for _, id := range idList {
		member := g.GetMember(id)
		if member == nil {
			continue
		}

		if _, exist := ret[member.Sid]; !exist {
			ret[member.Sid] = make([]uint64, 0, 1)
		}
		ret[member.Sid] = append(ret[member.Sid], member.Id)
	}
	return ret
}

func (g *Guild) GenerateLogTextInfo(changeType log.GUILD_LOG_TEXT_INFO) *log.GuildLogText {
	info := &log.GuildLogText{
		Gid:        g.GetID(),
		Name:       g.GetName(),
		CreateTm:   g.GetCreateTm(),
		Language:   g.GetLanguage(),
		JoinType:   g.GetJoinType(),
		Label:      g.GetLabel(),
		LvLimit:    g.GetLvLimit(),
		PowerLimit: g.GetPowerLimit(),
		Type:       changeType,
		Icon:       g.getBadgeIcon(),
		Background: g.getBadgeBackground(),
	}

	switch changeType {
	case log.GUILD_LOG_TEXT_INFO_GLTI_MODIFY_INFO:
		info.Content = g.GetDeclaration()
	case log.GUILD_LOG_TEXT_INFO_GLTI_SET_NAME:
		info.Content = g.GetName()
	case log.GUILD_LOG_TEXT_INFO_GLTI_MODIFY_NOTICE:
		info.Content = g.getNotice()
	}

	return info
}

func (g *Guild) generateGmGuildBaseInfo(area uint32) *gm.GmGuildBaseInfo {
	base := &gm.GmGuildBaseInfo{
		Area:          area,
		Name:          g.GetName(),
		Id:            g.GetID(),
		Leader:        g.GetLeader(),
		MemberNum:     g.MemberCnt(),
		Language:      g.GetLanguage(),
		ActivityPoint: g.ActivityPoints,
		Level:         g.GetLevel(),
		CreateTm:      g.GetCreateTm(),
	}
	leader := g.GetMember(base.Leader)
	if leader != nil {
		base.LeaderName = leader.Name
	}
	return base
}

func (g *Guild) generateGmGuildInfo(area uint32) *gm.GmGuildInfo {
	info := &gm.GmGuildInfo{
		Base: g.generateGmGuildBaseInfo(area),
	}
	info.Notice = g.getNotice()
	info.LvLimit = g.GetLvLimit()
	info.PowerLimit = g.GetPowerLimit()
	info.JoinType = g.GetJoinType()
	members := g.GetMembers()
	info.Members = make([]*gm.GmGuildMemberInfo, 0, len(members))
	for id, member := range members {
		gmMember := &gm.GmGuildMemberInfo{
			Uid:   id,
			Sid:   member.Sid,
			Name:  member.Name,
			Grade: member.Grade,
		}
		for _, point := range member.ActivityPoint {
			gmMember.ActivityPoint += point
		}
		info.Members = append(info.Members, gmMember)
	}
	return info
}

func (g *Guild) GetLeaders() []uint64 {
	leaders := make([]uint64, 0, 6) //nolint:mnd
	leaders = append(leaders, g.data.Leader)
	leaders = append(leaders, g.data.Deputy...)
	leaders = append(leaders, g.data.Regiments...)
	return leaders
}

type mostActivityMember struct {
	id    uint64
	point uint32
	grade uint32
}

func (g *Guild) FilterActivityMember(selfId uint64) *cr.GuildMember {
	mostMembers := make([]*mostActivityMember, 0, len(g.data.Members))
	for _, member := range g.data.Members {
		if member.Id == selfId {
			continue
		}
		mostMembers = append(mostMembers, &mostActivityMember{
			id:    member.Id,
			point: g.GetMemberActivityPoint(member),
			grade: member.Grade,
		})
	}
	l4g.Debugf("FiterActivityMember sort before selfId:%d mostMembers:%v", selfId, mostMembers)
	sort.Slice(mostMembers, func(i, j int) bool { //nolint:varnamelen
		if mostMembers[i].point > 0 && mostMembers[j].point <= 0 {
			return true
		} else if mostMembers[i].point <= 0 && mostMembers[j].point > 0 {
			return false
		}
		value := activity.CmpGuildGrade(mostMembers[i].grade, mostMembers[j].grade)
		if value < 0 {
			return true
		} else if value == 0 {
			if mostMembers[i].point > mostMembers[j].point {
				return true
			} else if mostMembers[i].point == mostMembers[j].point {
				if mostMembers[i].id > mostMembers[j].id {
					return true
				}
			}
		}

		/*		if mostMembers[i].grade < mostMembers[j].grade {
					return true
				} else if mostMembers[i].grade == mostMembers[j].grade {
					if mostMembers[i].point > mostMembers[j].point {
						return true
					} else if mostMembers[i].point == mostMembers[j].point {
						if mostMembers[i].id > mostMembers[j].id {
							return true
						}
					}
				}
		*/
		return false
	})
	l4g.Debugf("FiterActivityMember sort end selfId:%d mostMembers:%v", selfId, mostMembers)

	mid := mostMembers[0].id
	return g.data.Members[mid]
}

func (g *Guild) GetDungeon() *Dungeon {
	return g.m.dungeonM.GetDungeonByID(g.GetID())
}

// 是否开启公会合并功能
func (g *Guild) IsCombineOpen() bool {
	return util.DaysBetweenTimes(g.GetCreateTm(), time.Now().Unix())+1 > goxml.GetData().GuildConfigInfoM.GetGuildCombineOpenDay()
}

// 获取公会成员活跃人数
func (g *Guild) GetActiveNum() uint32 {
	var activeNum uint32
	for _, member := range g.data.Members {
		if point := g.GetMemberActivityPoint(member); point > 0 {
			activeNum++
		}
	}
	return activeNum
}

// 获取公会活跃成员
func (g *Guild) GetActiveMembers() ([]*cr.GuildMember, []*cr.GuildMember) {
	activeMembers := make([]*cr.GuildMember, 0, len(g.data.Members))
	noActiveMembers := make([]*cr.GuildMember, 0, len(g.data.Members))
	for _, member := range g.data.Members {
		if point := g.GetMemberActivityPoint(member); point > 0 {
			activeMembers = append(activeMembers, member)
		} else {
			noActiveMembers = append(noActiveMembers, member)
		}
	}
	return activeMembers, noActiveMembers
}

// 获取公会活跃成员, 并排除我方公会加入目标公会有cd的成员
func (g *Guild) GetActiveMembersWithCd(targetGuild *Guild) ([]*cr.GuildMember, []*cr.GuildMember) {
	now := time.Now().Unix()
	activeMembers := make([]*cr.GuildMember, 0, len(g.data.Members))
	noActiveMembers := make([]*cr.GuildMember, 0, len(g.data.Members))
	for _, member := range g.data.Members {
		if point := g.GetMemberActivityPoint(member); point > 0 && !goxml.GetData().GuildQuitCdInfoM.IsInQuitCd(member.GuildLeaveCnt, targetGuild.GetID(), member.LastLeaveTm, now) {
			activeMembers = append(activeMembers, member)
		} else {
			noActiveMembers = append(noActiveMembers, member)
		}
	}
	return activeMembers, noActiveMembers
}

// 获取公会活跃信息，并排除我方公会加入目标公会有cd的成员
func (g *Guild) GetActiveInfo(targetGuild *Guild, considerCd bool) ([]*cr.GuildMember, uint32, bool) {
	leaderId := g.GetLeader()
	if leaderId == 0 {
		l4g.Errorf("GetActiveInfo: leaderId == 0, gid %d", g.GetID())
		return nil, 0, false
	}

	var active []*cr.GuildMember
	if considerCd {
		active, _ = g.GetActiveMembersWithCd(targetGuild)
	} else {
		active, _ = g.GetActiveMembers()
	}

	activeNum := uint32(len(active))
	var isLeaderActive bool
	for _, active := range active {
		if active.Id == leaderId {
			isLeaderActive = true
		}
	}
	return active, activeNum, isLeaderActive
}

func (g *Guild) SetMemberWithCd(uid uint64, lastLeaveTm int64, leaveCount []*cl.GuildLeaveCount) {
	member := g.GetMember(uid)
	if member == nil {
		l4g.Errorf("SetMemberWithCd: no member. uid %d", uid)
		return
	}
	member.LastLeaveTm = lastLeaveTm
	member.GuildLeaveCnt = leaveCount
}

// 定时删除过期请求
func (g *Guild) delExpiredCombineApply() {
	now := time.Now().Unix()
	duration := goxml.GetData().GuildConfigInfoM.GetGuildCombineApplyDuration()
	for i := 0; i < len(g.data.TargetCombineApplyInfos); i++ {
		if now > g.data.TargetCombineApplyInfos[i].ApplyTime+duration {
			g.data.TargetCombineApplyInfos = append(g.data.TargetCombineApplyInfos[:i], g.data.TargetCombineApplyInfos[i+1:]...)
			i--
		}
	}
	for i := 0; i < len(g.data.SourceCombineApplyInfos); i++ {
		if now > g.data.SourceCombineApplyInfos[i].ApplyTime+duration {
			g.data.SourceCombineApplyInfos = append(g.data.SourceCombineApplyInfos[:i], g.data.SourceCombineApplyInfos[i+1:]...)
			i--
		}
	}
}

func (g *Guild) SetChange() {
	g.m.SetChange(g)
}

func (g *Guild) mobilizationDailyReset() {
	data := g.getMobilizationData()
	if data != nil {
		data.BoughtFreshTimes = 0
		data.CanFreshTimes = goxml.GetData().GuildConfigInfoM.GuildMobTaskFreshTimes
		g.SetChange()
	}
}

func (g *Guild) GetMobilizationData(roundInfo *goxml.GuildMobilizationInfoExt) *cr.GuildMobilizationData {
	if g.GetData().GuildMobilizationData == nil || g.GetData().GuildMobilizationData.Round != roundInfo.Info.Round {
		g.initMobilizationData(roundInfo)
	}
	g.refreshMobilizationTask()
	return g.GetData().GuildMobilizationData
}

func (g *Guild) getMobilizationData() *cr.GuildMobilizationData {
	return g.GetData().GuildMobilizationData
}

func (g *Guild) initMobilizationData(roundInfo *goxml.GuildMobilizationInfoExt) {
	g.GetData().GuildMobilizationData = &cr.GuildMobilizationData{
		Round:         roundInfo.Info.Round,
		CanFreshTimes: goxml.GetData().GuildConfigInfoM.GuildMobTaskFreshTimes,
	}
	g.SetChange()
}

func (g *Guild) getExceptTaskIds() []uint32 {
	data := g.getMobilizationData()
	except := slices.Clone(data.FinishedTasks)
	for _, task := range data.Tasks {
		except = append(except, task.TaskId)
	}
	return except
}

func (g *Guild) addNewMobTask(num int) {
	data := g.getMobilizationData()
	except := g.getExceptTaskIds()
	newTasks := goxml.GetData().GuildMobilizationTaskInfoM.GetRandDatas(g.m.GetRand(), except, num)
	if len(newTasks) != num {
		l4g.Errorf("guild %d rand %d task. target %d", g.GetID(), len(newTasks), num)
		return
	}
	for _, newTask := range newTasks {
		task := &cl.GuildMobilizationGuildTask{
			TaskId: newTask.Id,
		}
		data.Tasks = append(data.Tasks, task)
	}
	g.SetChange()
}

func (g *Guild) resetMobTask(index int) {
	data := g.getMobilizationData()
	if index >= len(data.Tasks) {
		return
	}
	except := g.getExceptTaskIds()
	newTasks := goxml.GetData().GuildMobilizationTaskInfoM.GetRandDatas(g.m.GetRand(), except, 1)
	if len(newTasks) != 1 {
		l4g.Errorf("guild %d rand %d task. target %d", g.GetID(), len(newTasks), 1)
		return
	}
	data.Tasks[index] = &cl.GuildMobilizationGuildTask{
		TaskId: newTasks[0].Id,
	}
	g.SetChange()
}

func (g *Guild) refreshMobilizationTask() {
	data := g.getMobilizationData()
	for index, task := range data.Tasks {
		if g.isMobilizationTaskNeedReset(task) {
			g.resetMobTask(index)
		}
	}
	guildLevelInfo := goxml.GetData().GuildLevelInfoM.Index(g.GetLevel())
	if guildLevelInfo == nil {
		l4g.Errorf("cant find level %d", g.GetLevel())
		return
	}
	needUnlockNum := int(guildLevelInfo.TaskNumber)
	hadUnlockNum := len(data.Tasks)
	if needUnlockNum > hadUnlockNum {
		g.addNewMobTask(needUnlockNum - hadUnlockNum)
	}
}

func (g *Guild) isMobilizationTaskNeedReset(task *cl.GuildMobilizationGuildTask) bool {
	taskInfo := goxml.GetData().GuildMobilizationTaskInfoM.Index(task.TaskId)
	if taskInfo == nil {
		l4g.Errorf("cant find task %d", task.TaskId)
		return false
	}
	if len(task.Members) != int(taskInfo.AcceptNum) {
		return false
	}
	now := time.Now().Unix()
	for _, user := range task.Members {
		if user.IsFinish {
			continue
		}
		if now >= user.EndTime {
			continue
		}
		return false
	}
	return true
}

func (g *Guild) GetMobilizationTask(roundInfo *goxml.GuildMobilizationInfoExt) []*cl.GuildMobilizationGuildTask {
	mobData := g.GetMobilizationData(roundInfo)
	return mobData.Tasks
}

func (g *Guild) MobAcceptTask(roundInfo *goxml.GuildMobilizationInfoExt, guildUser *User, req *l2c.L2CS_GuildMobilizationAcceptTask, rsp *cl.L2C_GuildMobilizationAcceptTask) {
	now := time.Now().Unix()
	var useTimes uint32 = 1
	uid := guildUser.GetData().GetId()
	if !guildUser.CheckMobAcceptTaskTimes(roundInfo, useTimes) {
		l4g.Errorf("user: %d times not enough", uid)
		rsp.Ret = uint32(ret.RET_GUILD_MOB_ACCEPT_TIMES_NOT_ENOUGH)
		return
	}
	member := g.GetMember(uid)
	if member == nil {
		l4g.Errorf("%d cant find member %d", g.GetID(), uid)
		rsp.Ret = uint32(ret.RET_NOT_GUILD_MEMBER)
		return
	}
	taskInfo := goxml.GetData().GuildMobilizationTaskInfoM.Index(req.TaskId)
	if taskInfo == nil {
		l4g.Errorf("cant find task %d", req.TaskId)
		rsp.Ret = uint32(ret.RET_ERROR)
		return
	}
	var hadTaskNum uint32 = 0
	tasks := g.GetMobilizationTask(roundInfo)
	for _, task := range tasks {
		for _, memberTask := range task.Members {
			if memberTask.Uid != uid {
				continue
			}
			if task.TaskId == req.TaskId {
				l4g.Errorf("%d had accept task %d", uid, req.TaskId)
				rsp.Ret = uint32(ret.RET_GUILD_MOB_ACCEPT_TASK_REPEATED)
				return
			}
			if memberTask.IsFinish {
				continue
			}
			if now > memberTask.GetEndTime() {
				continue
			}
			hadTaskNum++
		}
	}
	if hadTaskNum >= goxml.GetData().GuildConfigInfoM.GuildMobTaskLimit {
		l4g.Errorf("%d task num %d over %d", uid, hadTaskNum, goxml.GetData().GuildConfigInfoM.GuildMobTaskLimit)
		rsp.Ret = uint32(ret.RET_GUILD_MOB_SELF_ACCEPT_TASK_LIMIT)
		return
	}
	for _, task := range tasks {
		if task.TaskId != req.TaskId {
			continue
		}
		if len(task.Members) >= int(taskInfo.AcceptNum) {
			l4g.Errorf("%d task num %d over %d", g.GetID(), len(task.Members), taskInfo.AcceptNum)
			rsp.Ret = uint32(ret.RET_GUILD_MOB_GUILD_ACCEPT_TASK_LIMIT)
			return
		}
		memberTask := &cl.GuildMobilizationMemberTask{
			Uid:     uid,
			EndTime: now + goxml.GetData().GuildConfigInfoM.GuildMobTaskLimitTime,
			Name:    member.GetName(),
		}
		task.Members = append(task.Members, memberTask)
		g.SetChange()
		guildUser.ReduceMobAcceptTaskTimes(useTimes)
		rsp.TaskId = req.TaskId
		rsp.AcceptTaskTimes = guildUser.GetMobAcceptTaskTimes()
		rsp.Task = memberTask.Clone()
		rsp.Ret = uint32(ret.RET_OK)
		return
	}

	l4g.Errorf("%d dont find task %d", uid, req.TaskId)
	rsp.Ret = uint32(ret.RET_GUILD_MOB_NO_TASK)
}

func (g *Guild) MobSignTask(msg *l2c.L2CS_GuildMobilizationSignTask, roundInfo *goxml.GuildMobilizationInfoExt, rsp *cl.L2C_GuildMobilizationSignTask) {
	tasks := g.GetMobilizationTask(roundInfo)
	for _, task := range tasks {
		if task.TaskId != msg.TaskId {
			continue
		}
		task.IsHighPriority = msg.IsSign
		g.SetChange()
		rsp.TaskId = msg.TaskId
		rsp.IsSign = msg.IsSign
		rsp.Ret = uint32(ret.RET_OK)
		return
	}
	l4g.Errorf("%d dont find task %d", g.GetID(), msg.TaskId)
	rsp.Ret = uint32(ret.RET_ERROR)
}

func (g *Guild) MobGetFinishTaskLogs(roundInfo *goxml.GuildMobilizationInfoExt) []*cl.GuildMobilizationTaskLog {
	mobData := g.GetMobilizationData(roundInfo)
	data := make([]*cl.GuildMobilizationTaskLog, 0)
	for _, log := range mobData.Logs {
		data = append(data, log.Clone())
	}
	return data
}

func (g *Guild) MobGetPersonalRankData(roundInfo *goxml.GuildMobilizationInfoExt) []*cl.GuildMobilizationPersonalRank {
	rankData := make([]*cl.GuildMobilizationPersonalRank, 0)
	mobData := g.GetMobilizationData(roundInfo)
	for uid, score := range mobData.UsersScore {
		member := g.GetMember(uid)
		if member != nil {
			rankData = append(rankData, &cl.GuildMobilizationPersonalRank{
				Uid:        uid,
				Score:      score,
				Name:       member.GetName(),
				Sid:        member.Sid,
				BaseId:     member.GetBaseId(),
				ExpireTime: slices.Clone(member.GetExpireTime()),
			})
		}
	}
	return rankData
}

func (g *Guild) CheckMobFreshTaskTimes(roundInfo *goxml.GuildMobilizationInfoExt, num uint32) bool {
	data := g.GetMobilizationData(roundInfo)
	return data.CanFreshTimes >= num
}

func (g *Guild) ReduceMobFreshTaskTimes(num uint32) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}
	data.CanFreshTimes -= num
	g.SetChange()
}

func (g *Guild) MobGetFreshTaskTimes() uint32 {
	data := g.getMobilizationData()
	if data == nil {
		return 0
	}
	return data.CanFreshTimes
}

func (g *Guild) MobFreshTask(roundInfo *goxml.GuildMobilizationInfoExt, req *l2c.L2CS_GuildMobilizationFreshTask, rsp *cl.L2C_GuildMobilizationFreshTask) {
	tasks := g.GetMobilizationTask(roundInfo)
	var useTimes uint32 = 1
	if !g.CheckMobFreshTaskTimes(roundInfo, useTimes) {
		l4g.Errorf("guild: %d times not enough", g.GetID())
		rsp.Ret = uint32(ret.RET_GUILD_MOB_FRESH_TIMES_NOT_ENOUGH)
		return
	}
	for index, task := range tasks {
		if task.TaskId != req.TaskId {
			continue
		}
		now := time.Now().Unix()
		for _, member := range task.Members {
			if member.IsFinish {
				continue
			}
			if now > member.EndTime {
				continue
			}
			rsp.Ret = uint32(ret.RET_GUILD_MOB_TASKING)
			return
		}
		g.resetMobTask(index)
		g.ReduceMobFreshTaskTimes(useTimes)
		rsp.Ret = uint32(ret.RET_OK)
		rsp.TaskId = req.TaskId
		rsp.NewTask = tasks[index].Clone()
		rsp.CanFreshTimes = g.MobGetFreshTaskTimes()
		return
	}
	l4g.Errorf("%d dont find task %d", g.GetID(), req.TaskId)
	rsp.Ret = uint32(ret.RET_GUILD_MOB_NO_TASK)
}

func (g *Guild) MobBuyFreshTimes(roundInfo *goxml.GuildMobilizationInfoExt, req *l2c.L2CS_GuildMobilizationBuyTimes, rsp *cl.L2C_GuildMobilizationBuyTimes) {
	mobData := g.GetMobilizationData(roundInfo)
	defer func() {
		rsp.CanUseTimes = mobData.CanFreshTimes
		rsp.HadBoughtTimes = mobData.BoughtFreshTimes
	}()
	numInfo := goxml.GetData().NumberTypeInfoM.Index(goxml.GetData().GuildConfigInfoM.GuildMobFreshBuyGroup)
	if numInfo == nil {
		l4g.Errorf("NumberTypeInfoM cant find %d", goxml.GetData().GuildConfigInfoM.GuildMobFreshBuyGroup)
		rsp.Ret = uint32(ret.RET_ERROR)
		return
	}
	if mobData.BoughtFreshTimes+req.BuyTimes > numInfo.BuyLimit {
		l4g.Errorf("MobBuyFreshTimes limit max:%d now:%d buy:%d", numInfo.BuyLimit, mobData.BoughtFreshTimes, req.BuyTimes)
		rsp.Ret = uint32(ret.RET_GUILD_MOB_BUY_LIMIT)
		return
	}
	success, price := goxml.GetData().BuyPriceInfoM.GetPrice(numInfo.BuyGroup, mobData.BoughtFreshTimes, req.BuyTimes)
	if !success || price != req.NeedDiamond {
		l4g.Errorf("MobBuyFreshTimes err. %t price:%d pass:%d", success, price, req.NeedDiamond)
		rsp.Ret = uint32(ret.RET_GUILD_MOB_BUY_FAILED)
		return
	}
	mobData.CanFreshTimes += req.BuyTimes
	mobData.BoughtFreshTimes += req.BuyTimes
	rsp.Ret = uint32(ret.RET_OK)
	g.SetChange()
}

func (g *Guild) MobOnMemberQuit(uid uint64) {
	mobData := g.getMobilizationData()
	if mobData == nil {
		return
	}
	for index, task := range mobData.Tasks {
		if task == nil {
			l4g.Errorf("guild %d task %dis nil", g.GetID(), index)
			continue
		}
		for _, memberTask := range task.Members {
			if memberTask.Uid == uid {
				//设为过期
				memberTask.EndTime = 0
				g.SetChange()
			}
		}
	}
}

func (g *Guild) MobSysFinishedTasks(roundInfo *goxml.GuildMobilizationInfoExt, guildUser *User, now int64, finishedTasks []uint32) {
	g.GetMobilizationData(roundInfo)
	for _, finishTaskID := range finishedTasks {
		g.mobSysFinishedTask(finishTaskID, guildUser, now)
	}
}

func (g *Guild) mobSysFinishedTask(taskID uint32, guildUser *User, now int64) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}
	for _, task := range data.Tasks {
		if task.TaskId != taskID {
			continue
		}
		for _, memberTask := range task.Members {
			if memberTask.Uid != guildUser.ID() {
				continue
			}
			if memberTask.IsFinish {
				return
			}
			if now > memberTask.EndTime {
				return
			}
			memberTask.IsFinish = true
			g.SetChange()
			g.mobFinishedTask(taskID, guildUser)
			return
		}
	}
}

func (g *Guild) mobFinishedTask(taskID uint32, guildUser *User) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}
	taskInfo := goxml.GetData().GuildMobilizationTaskInfoM.Index(taskID)
	if taskInfo == nil {
		l4g.Errorf("mobCheckTaskFinish cant find task %d", taskID)
		return
	}
	g.mobAddScore(guildUser.ID(), taskInfo.GetPoints)
	g.mobAddTaskFinishLog(guildUser.ID(), taskID)
	g.mobCheckGuildTaskFinish(taskInfo)
	// 为玩家记录积分
	guildUser.MobAddScore(g.GetID(), taskInfo.GetPoints)
}

func (g *Guild) mobAddTaskFinishLog(uid uint64, taskID uint32) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}
	member := g.GetMember(uid)
	if member != nil {
		logFinishTask := &cl.GuildMobilizationTaskLog{
			TaskId:  taskID,
			Name:    member.GetName(),
			GenTime: time.Now().Unix(),
		}
		data.Logs = append(data.Logs, logFinishTask)
		if len(data.Logs) > int(goxml.GetData().GuildConfigInfoM.GuildMobTaskLogMax) {
			data.Logs = slices.Delete(data.Logs, 0, 1)
		}
	}
}

func (g *Guild) mobAddScore(uid uint64, score uint32) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}
	if data.UsersScore == nil {
		data.UsersScore = make(map[uint64]uint32)
	}
	beforeScore := data.Score
	data.ScoreChangeTime = time.Now().Unix()
	data.UsersScore[uid] += score
	data.Score += score
	guildLevelInfo := goxml.GetData().GuildLevelInfoM.Index(g.GetLevel())
	if guildLevelInfo != nil {
		if len(data.UsersScore) > int(guildLevelInfo.Member) {
			values := maps.Values(data.UsersScore)
			slices.Sort(values)
			startIndex := len(values) - 1
			endIndex := startIndex - int(guildLevelInfo.Member)
			data.Score = 0
			for ; startIndex > endIndex; startIndex-- {
				data.Score += values[startIndex]
			}
		}
	}
	log := &log.LogGuildMobScore{
		Partition:    g.m.baseModule.Partition(),
		CurrentScore: data.Score,
		Score:        score,
		GuildId:      g.GetID(),
		GuildLevel:   g.GetLevel(),
	}
	dclog.LogGuildMobScore(g.m.baseModule.GetSrv(), log)
	g.mobOnScoreChange(beforeScore, data.Score)
	g.m.SyncGuildMobToGST(g)
	g.SetChange()
}

func (g *Guild) mobOnScoreChange(beforeScore, afterScore uint32) {
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("guild:%d mob not open", g.GetID())
		return
	}
	unlockRewards := make([]*goxml.GuildMobilizationRewardInfo, 0)
	for _, info := range goxml.GetData().GuildMobilizationRewardInfoM.Datas {
		if beforeScore < info.Exp && afterScore >= info.Exp {
			unlockRewards = append(unlockRewards, info)
		}
	}
	for _, info := range unlockRewards {
		g.mobOnLevelUp(info.Level, roundInfo)
	}
}

func (g *Guild) mobOnLevelUp(level uint32, roundInfo *goxml.GuildMobilizationInfoExt) {
	userM := g.m.GetUserManager()
	for _, member := range g.GetMembers() {
		guildUser := userM.GetUserWithInit(member.Id, member.Sid)
		guildUser.MobOnLevelUp(level, roundInfo)
	}
}

func (g *Guild) MobAddScoreForGM(uid uint64, score uint32) {
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSysFinishedTask: not open", uid)
		return
	}
	g.mobAddScore(uid, score)
}

func (g *Guild) mobCheckGuildTaskFinish(taskInfo *goxml.GuildMobilizationTaskInfo) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}

	for _, task := range data.Tasks {
		if task.TaskId != taskInfo.Id {
			continue
		}
		if len(task.Members) != int(taskInfo.AcceptNum) {
			return
		}
		//所有接取任务的人都要完成
		for _, user := range task.Members {
			if !user.IsFinish {
				return
			}
		}
		g.mobAddTaskFinishTimes(taskInfo)
		return
	}
}

func (g *Guild) mobAddTaskFinishTimes(taskInfo *goxml.GuildMobilizationTaskInfo) {
	data := g.getMobilizationData()
	if data == nil {
		return
	}
	if data.TaskFinishedTimes == nil {
		data.TaskFinishedTimes = make(map[uint32]uint32)
	}
	finishedTimes := data.TaskFinishedTimes[taskInfo.Id]
	finishedTimes++
	data.TaskFinishedTimes[taskInfo.Id] = finishedTimes
	if finishedTimes >= taskInfo.FinishMax {
		data.FinishedTasks = append(data.FinishedTasks, taskInfo.Id)
	}
	g.SetChange()
}

func (g *Guild) MobEditMessageBoard(roundInfo *goxml.GuildMobilizationInfoExt, req *l2c.L2CS_GuildMobilizationEditMessageBoard, rsp *cl.L2C_GuildMobilizationEditMessageBoard) {
	mobData := g.GetMobilizationData(roundInfo)
	if mobData.Message == nil {
		mobData.Message = &cl.MessageBoard{}
	}
	mobData.Message.Message = req.Message
	mobData.Message.MessageId++
	g.SetChange()
	rsp.Message = mobData.Message.Clone()
	rsp.Ret = uint32(ret.RET_OK)
}

func (g *Guild) MobGiveUpTask(guildUser *User, roundInfo *goxml.GuildMobilizationInfoExt, uid uint64, req *l2c.L2CS_GuildMobilizationGiveUpTask, rsp *cl.L2C_GuildMobilizationGiveUpTask) {
	rsp.Ret = uint32(ret.RET_GUILD_MOB_BE_TASK_CANCEL)
	for _, task := range g.GetMobilizationTask(roundInfo) {
		if task.TaskId != req.TaskId {
			continue
		}

		if len(task.Members) == 0 { // 玩家未接取任务，不可被放弃
			return
		}

		for index, member := range task.Members {
			if member == nil {
				continue
			}
			if member.Uid != uid {
				continue
			}
			if member.IsFinish { // 任务已完成，不可被放弃
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}
			task.Members = slices.Delete(task.Members, index, index+1)

			rsp.Task = task.Clone()
			rsp.Ret = uint32(ret.RET_OK)
			g.SetChange()

			guildUser.AddMobAcceptTaskTimes(1) // 放弃任务后加回次数
			rsp.AcceptTaskTimes = guildUser.GetMobAcceptTaskTimes()
			return
		}
	}
}

func (g *Guild) MobCancelTask(guildUser *User, now int64, roundInfo *goxml.GuildMobilizationInfoExt,
	req *l2c.L2CS_GuildMobilizationCancelTask, rsp *cl.L2C_GuildMobilizationCancelTask, crsp *l2c.CS2L_GuildMobilizationBeCancelTask) {
	for _, task := range g.GetMobilizationTask(roundInfo) {
		if task.TaskId != req.TaskId {
			continue
		}

		if len(task.Members) == 0 { // 玩家未接取任务，不可被放弃
			rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return
		}

		for index, member := range task.Members {
			if member == nil {
				continue
			}
			if member.Uid != guildUser.ID() {
				continue
			}
			if member.IsFinish { // 任务已完成，不可被放弃
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}

			if now > member.EndTime {
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}

			if member.EndTime-now > goxml.GetData().GuildConfigInfoM.GetGuildMobDeleteTimes() {
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}

			task.Members = slices.Delete(task.Members, index, index+1)

			rsp.Task = task.Clone()
			rsp.TaskId = task.TaskId
			rsp.Ret = uint32(ret.RET_OK)
			rsp.Uid = guildUser.ID()
			g.SetChange()

			crsp.Ret = uint32(ret.RET_OK)
			crsp.TaskId = task.TaskId
			crsp.Uid = guildUser.ID()
			guildUser.AddMobAcceptTaskTimes(1) // 放弃任务后加回次数
			return
		}
	}
}
