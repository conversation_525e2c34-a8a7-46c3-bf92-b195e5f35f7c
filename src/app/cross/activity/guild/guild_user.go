package guild

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"golang.org/x/exp/maps"
)

// User 用来保存公会跨服的玩家数据，注意这里的User玩家不一定在公会中
type User struct {
	Data *cr.GuildCrossUser
	um   *UserManager
}

func newUser(data *cr.GuildCrossUser, um *UserManager) *User {
	return &User{
		Data: data,
		um:   um,
	}
}

func (u *User) ID() uint64 {
	return u.Data.GetId()
}

func (u *User) GetData() *cr.GuildCrossUser {
	return u.Data
}

func (u *User) UpdateTowerSeason(floor uint32, tm int64) {
	u.Data.TowerSeason = &cr.GuildMedalTowerSeason{
		SeasonTowerFloor: floor,
		Tm:               tm,
	}
	u.SetChange()
}

func (u *User) SetChange() {
	u.um.SetChange(u)
}

func (u *User) GetMobilizationData(roundInfo *goxml.GuildMobilizationInfoExt) *cr.GuildMobilizationGuildMember {
	if u.GetData().MobilizationData == nil || u.GetData().MobilizationData.Round != roundInfo.Info.Round {
		u.initMobilizationData(roundInfo)
	}
	return u.GetData().MobilizationData
}

func (u *User) getMobilizationData() *cr.GuildMobilizationGuildMember {
	return u.GetData().MobilizationData
}

func (u *User) initMobilizationData(roundInfo *goxml.GuildMobilizationInfoExt) {
	u.GetData().MobilizationData = &cr.GuildMobilizationGuildMember{
		Round:              roundInfo.Info.Round,
		CanAcceptTaskTimes: goxml.GetData().GuildConfigInfoM.GuildMobTaskAcceptTimes,
		GuildScore:         make(map[uint64]uint32),
	}
	u.SetChange()
}

func (u *User) CheckMobAcceptTaskTimes(roundInfo *goxml.GuildMobilizationInfoExt, num uint32) bool {
	data := u.GetMobilizationData(roundInfo)
	return data.CanAcceptTaskTimes >= num
}

func (u *User) ReduceMobAcceptTaskTimes(num uint32) {
	data := u.getMobilizationData()
	if data == nil {
		return
	}
	data.CanAcceptTaskTimes -= num
	u.SetChange()
}

func (u *User) AddMobAcceptTaskTimes(num uint32) {
	data := u.getMobilizationData()
	if data == nil {
		return
	}
	data.CanAcceptTaskTimes += num
	u.SetChange()
}

func (u *User) GetMobAcceptTaskTimes() uint32 {
	data := u.getMobilizationData()
	if data == nil {
		return 0
	}
	return data.CanAcceptTaskTimes
}

func (u *User) GetMobScoreLevels(roundInfo *goxml.GuildMobilizationInfoExt) map[uint32]cl.GuildMobilizationTaskStatus {
	data := u.GetMobilizationData(roundInfo)
	if data.ScoreLevels == nil {
		data.ScoreLevels = make(map[uint32]cl.GuildMobilizationTaskStatus)
	}
	return data.ScoreLevels
}

func (u *User) MobRecvScoreLevel(roundInfo *goxml.GuildMobilizationInfoExt, rsp *cl.L2C_GuildMobilizationRecvScoreLevel) {
	allAwards := make([]*cl.Resource, 0)
	data := u.GetMobScoreLevels(roundInfo)
	for level, status := range data {
		if status == cl.GuildMobilizationTaskStatus_Finished {
			rewardConfig := goxml.GetData().GuildMobilizationRewardInfoM.Index(level)
			if rewardConfig == nil {
				l4g.Errorf("GuildMobilizationRewardInfoM get %d err", level)
				rsp.Ret = uint32(ret.RET_ERROR)
				return
			}
			allAwards = append(allAwards, rewardConfig.RewardClRes...)
		}
	}
	if len(allAwards) == 0 {
		l4g.Errorf("%d ScoreLevelReward no rewards", u.GetData().Id)
		rsp.Ret = uint32(ret.RET_ERROR)
		return
	}

	for level := range data {
		data[level] = cl.GuildMobilizationTaskStatus_GetReward
	}
	u.SetChange()
	rsp.Rewards = allAwards
	rsp.ScoreLevels = maps.Clone(data)
	rsp.Ret = uint32(ret.RET_OK)
	l4g.Infof("%d Get ScoreLevelReward %+v", u.GetData().Id, allAwards)
}

func (u *User) MobBuyAcceptTimes(roundInfo *goxml.GuildMobilizationInfoExt, req *l2c.L2CS_GuildMobilizationBuyTimes, rsp *cl.L2C_GuildMobilizationBuyTimes) {
	mobData := u.GetMobilizationData(roundInfo)
	defer func() {
		rsp.CanUseTimes = mobData.CanAcceptTaskTimes
		rsp.HadBoughtTimes = mobData.BoughtTaskTimes
	}()
	numInfo := goxml.GetData().NumberTypeInfoM.Index(goxml.GetData().GuildConfigInfoM.GuildMobAcceptBuyGroup)
	if numInfo == nil {
		l4g.Errorf("NumberTypeInfoM cant find %d", goxml.GetData().GuildConfigInfoM.GuildMobAcceptBuyGroup)
		rsp.Ret = uint32(ret.RET_ERROR)
		return
	}
	if mobData.BoughtTaskTimes+req.BuyTimes > numInfo.BuyLimit {
		l4g.Errorf("MobBuyAcceptTimes limit max:%d now:%d buy:%d", numInfo.BuyLimit, mobData.BoughtTaskTimes, req.BuyTimes)
		rsp.Ret = uint32(ret.RET_GUILD_MOB_BUY_LIMIT)
		return
	}
	success, price := goxml.GetData().BuyPriceInfoM.GetPrice(numInfo.BuyGroup, mobData.BoughtTaskTimes, req.BuyTimes)
	if !success || price != req.NeedDiamond {
		l4g.Errorf("MobBuyAcceptTimes err. %t price:%d pass:%d", success, price, req.NeedDiamond)
		rsp.Ret = uint32(ret.RET_GUILD_MOB_BUY_FAILED)
		return
	}
	mobData.BoughtTaskTimes += req.BuyTimes
	mobData.CanAcceptTaskTimes += req.BuyTimes
	rsp.Ret = uint32(ret.RET_OK)
	u.SetChange()
}

func (u *User) MobOnLevelUp(level uint32, roundInfo *goxml.GuildMobilizationInfoExt) {
	data := u.GetMobScoreLevels(roundInfo)
	if _, exist := data[level]; !exist {
		data[level] = cl.GuildMobilizationTaskStatus_Finished
		u.SetChange()
	}
}

func (u *User) MobAddScore(guildId uint64, score uint32) {
	data := u.getMobilizationData()
	if data == nil {
		return
	}
	if data.GuildScore == nil {
		data.GuildScore = make(map[uint64]uint32)
	}
	_, exist := data.GuildScore[guildId]
	if !exist {
		data.GuildScore[guildId] = score
	} else {
		data.GuildScore[guildId] += score
	}
	u.SetChange()
}

func (u *User) MobCalcTotalScore(guildScore map[uint64]uint32) uint32 {
	var totalScore uint32
	for _, score := range guildScore {
		totalScore += score
	}
	return totalScore
}

func (u *User) MobCheckScoreIdsCanReceive(ids []uint32) bool {
	data := u.getMobilizationData()
	if data == nil {
		return false
	}

	visited := make(map[uint32]struct{})
	userTotalScore := u.MobCalcTotalScore(data.GuildScore)
	for _, id := range ids {
		// 检查是否重复
		if _, exist := visited[id]; exist {
			l4g.Errorf("MobCheckScoreIdsCanReceive: repeated ids. user %d ids %+v", u.ID(), ids)
			return false
		}
		visited[id] = struct{}{}
		// 检查是否已领奖
		if util.InUint32s(data.ReceivedIds, id) {
			l4g.Errorf("MobCheckScoreIdsCanReceive: score has received. user %d id %d", u.ID(), id)
			return false
		}
		// 检查是否满足领取条件
		scoreRewardInfo := goxml.GetData().GuildMobilizationScoreRewardInfoM.Index(id)
		if scoreRewardInfo == nil {
			l4g.Errorf("MobCheckScoreIdsCanReceive: no scoreRewardInfo. user %d id %d", u.ID(), id)
			return false
		}
		if userTotalScore < scoreRewardInfo.Score {
			l4g.Errorf("MobCheckScoreIdsCanReceive: not enough score. user %d id %d score %d need %d",
				u.ID(), id, userTotalScore, scoreRewardInfo.Score)
			return false
		}
	}

	return true
}

func (u *User) MobReceiveScoreIds(ids []uint32, rsp *cl.L2C_GuildMobilizationScoreAward) {
	allAwards := make([]*cl.Resource, 0)
	data := u.getMobilizationData()
	if data == nil {
		return
	}

	for _, id := range ids {
		rewardConfig := goxml.GetData().GuildMobilizationScoreRewardInfoM.Index(id)
		if rewardConfig == nil {
			l4g.Errorf("%d MobReceiveScoreIds: no ScoreRewardInfo. id %d", u.ID(), id)
			rsp.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
			return
		}
		allAwards = append(allAwards, rewardConfig.RewardClRes...)
	}
	if len(allAwards) == 0 {
		l4g.Errorf("%d MobReceiveScoreIds: no rewards. ids %+v", u.ID(), ids)
		rsp.Ret = uint32(ret.RET_ERROR)
		return
	}

	data.ReceivedIds = append(data.ReceivedIds, ids...)
	u.SetChange()

	rsp.Awards = allAwards
	if dataClone := data.Clone(); dataClone != nil {
		rsp.ReceivedIds = dataClone.ReceivedIds
	}
	rsp.Ids = ids
	rsp.Ret = uint32(ret.RET_OK)
	l4g.Infof("user %d Get MobReceiveScoreIds %+v", u.GetData().Id, allAwards)
}

func (u *User) Sid() uint64 {
	return u.Data.Sid
}
