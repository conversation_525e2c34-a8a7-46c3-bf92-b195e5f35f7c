package gst

import (
	"app/cross/dclog"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/log"
	"app/protos/out/cl"
	"cmp"
	"slices"
	"sort"
	"time"

	l4g "github.com/ivanabc/log4go"
	"golang.org/x/exp/maps"
)

func newGroup(mgr *GroupManager, data *cr.GSTGroupInfo) *Group {
	group := &Group{
		mgr:           mgr,
		data:          data,
		grounds:       make(map[uint32]IGround),
		taskInfoCache: make(map[uint64]map[uint32][]IGround),
		mainBases:     make(map[uint64]IGround),
	}
	group.loadMap(data.MapInfo)
	return group
}

type Group struct {
	mgr           *GroupManager
	data          *cr.GSTGroupInfo
	grounds       map[uint32]IGround              //把ground独立处理
	taskInfoCache map[uint64]map[uint32][]IGround //Key:公会ID key:地块类型 []地块信息
	mainBases     map[uint64]IGround              // guildID => 主基地
}

func (g *Group) GetData() *cr.GSTGroupInfo {
	return g.data
}

func (g *Group) SetChange() {
	g.GetManager().GetGroupM().SetChange(g)
}

func (g *Group) loadMap(mapInfo *cr.GSTMapInfo) {
	if mapInfo == nil {
		return
	}
	for id, ground := range mapInfo.GSTGroundInfos {
		g.buildTaskCache(g.addGround(id, ground))
	}
}

func (g *Group) NewGround(id uint32, data *cr.GSTGroundInfo) IGround {
	g.GetData().MapInfo.GSTGroundInfos[id] = data
	g.SetChange()
	return g.addGround(id, data)
}

func (g *Group) addGround(id uint32, data *cr.GSTGroundInfo) IGround {
	iGround := newGround(g, id, data)
	if iGround == nil {
		return nil
	}
	g.GetGrounds()[id] = iGround
	if iGround.GetGroundType() == goxml.GuildSandTableLandTypeMainBase && data.GuildId != 0 {
		g.mainBases[data.GuildId] = iGround
	}
	return iGround
}

func (g *Group) GetGrounds() map[uint32]IGround {
	return g.grounds
}

func (g *Group) GetGround(id uint32) IGround {
	return g.grounds[id]
}

func (g *Group) initGroup() {
	g.GetData().GuildRank = &cl.GSTLastLRoundGuildRank{}
	g.SetChange()
	g.initMap()
}

func (g *Group) initMap() {
	mapID := g.GetMapIDByRoomQuality(g.GetData().RoomQuality)
	if mapID == 0 {
		l4g.Errorf("getGroupMapInfo RoomQuality:%d", g.GetData().RoomQuality)
		return
	}
	g.GetData().MapInfo = &cr.GSTMapInfo{
		MapConfigId:    mapID,
		GSTGroundInfos: make(map[uint32]*cr.GSTGroundInfo),
	}
	g.SetChange()
}

func (g *Group) GetMapIDByRoomQuality(quality uint32) uint32 {
	sta := g.GetManager().GetStaM().GetSta().State
	mapGroup := goxml.GetData().GuildSandTableInfoM.GetMapGroup(sta.SeasonId, sta.Round)
	if mapGroup == 0 {
		l4g.Errorf("GuildSandTableInfo cant find. seasonID:%d round:%d", sta.SeasonId, sta.Round)
		return 0
	}
	mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.GetMapInfo(mapGroup, quality)
	if mapInfo == nil {
		l4g.Errorf("GuildSandTableMapGroupInfo cant find. mapGroup:%d quality:%d", mapGroup, quality)
		return 0
	}
	return mapInfo.Id
}

func (g *Group) GetManager() *Manager {
	return g.mgr.mgr
}

func (g *Group) GetMatchIndex() int {
	if g.GetData().MatchedGuild == nil {
		g.GetData().MatchedGuild = make([]uint64, goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum)
	} else if uint32(len(g.GetData().MatchedGuild)) < goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum {
		add := make([]uint64, int(goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum)-len(g.GetData().MatchedGuild))
		g.GetData().MatchedGuild = append(g.GetData().MatchedGuild, add...)
	}
	emptyIndexs := make([]int, 0)
	for index, guildID := range g.GetData().MatchedGuild {
		if guildID == 0 {
			emptyIndexs = append(emptyIndexs, index)
		}
	}
	if len(emptyIndexs) == 0 {
		l4g.Errorf("%d get match index error", g.GetData().GetId())
		return -1
	}
	rdIndex := g.GetManager().rd.Intn(len(emptyIndexs))
	return emptyIndexs[rdIndex]
}

func (g *Group) AddMatchGuild(guild *Guild, index int) {
	g.GetData().MatchedGuild[index] = guild.GetData().Guild.GuildId
	guildRankInfo := &cl.GSTSimpleGuild{
		GuildId:   guild.GetData().GetGuild().GuildId,
		Score:     maps.Clone(guild.GetData().GetGuild().Score),
		GroundNum: guild.GetData().GetGuild().GroundNum,
	}
	g.GetData().GuildRank.GuildRank = append(g.GetData().GuildRank.GuildRank, guildRankInfo)
	g.SetChange()
}

func (g *Group) GetMatchedNum() uint32 {
	var matchedNum uint32
	for _, guildID := range g.GetData().MatchedGuild {
		if guildID != 0 {
			matchedNum++
		}
	}
	return matchedNum
}

func (g *Group) AddGuilds(guilds NotMatchedGuildHeap) {
	if g.GetData().MapInfo == nil {
		return
	}
	mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(g.GetData().MapInfo.MapConfigId)
	if mapInfo == nil {
		l4g.Errorf("GuildSandTableMapGroupInfo cant find.%d", g.GetData().MapInfo.MapConfigId)
		return
	}
	mapConfigInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapInfoExt(mapInfo.MapId)
	if mapConfigInfo == nil {
		l4g.Errorf("GuildSandTableMapInfoM cant find. mapID:%d", mapInfo.MapId)
		return
	}
	for i := 0; i < guilds.Len(); i++ {
		guild := guilds[i]
		guildID := guild.GetData().Guild.GuildId
		matchIndex := g.GetMatchIndex()
		if matchIndex < 0 {
			l4g.Errorf("%d match error", guildID)
			continue
		}
		mainBase := mapConfigInfo.MainBases[matchIndex]
		groundInfo := &cr.GSTGroundInfo{
			LandId: mainBase.Stages[0].LandID,
		}
		ground := g.NewGround(mainBase.ID, groundInfo)
		if ground == nil {
			l4g.Errorf("AddGuilds init mainBase %d err", mainBase.ID)
			continue
		}

		g.mainBases[guildID] = ground
		g.AddMatchGuild(guild, matchIndex)
		g.GetManager().GetGuildM().OnControlGround(guildID, ground, g)
		g.GetManager().GetGuildM().SetGuildMatched(guild, g.GetData().Id)
	}
	if g.GetMatchedNum() == goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum {
		g.GetManager().GetGroupM().delNotFullGroup(g)
	}
	g.SetChange()
}

func (g *Group) FightMatch() {
	for _, guildID := range g.GetData().MatchedGuild {
		if guildID == 0 {
			continue
		}
		guild := g.GetManager().GetGuildM().GetGuild(guildID)
		if guild != nil {
			guild.InitLRoundSettle(g.GetGuildRank(guildID), g.GetData().GetRoomQuality())
		}
	}
	for _, ground := range g.GetGrounds() {
		ground.Fight(nil)
	}
}

func (g *Group) OnFinishFight() {
	blessAdd := g.buildBlessGuildMap()
	firstItems := make(map[uint64][]uint32)
	for _, ground := range g.grounds {
		firstItem := g.SettleControl(ground)
		if firstItem {
			firstItems[ground.GetControlGuildID()] = append(firstItems[ground.GetControlGuildID()], ground.GetData().LandId)
		}
		ground.OnFinishFight(blessAdd)
	}
	guildM := g.mgr.mgr.GetGuildM()
	for _, guildId := range g.data.MatchedGuild {
		if guildId == 0 {
			continue
		}
		guild := guildM.GetGuild(guildId)
		if guild == nil {
			continue
		}
		guild.AddBuildScore(blessAdd)
	}
	g.genRankInfo(firstItems)
}

func (g *Group) SettleControl(ground IGround) bool {
	tempId := ground.GetData().TempControlGuildId
	if tempId == 0 {
		return false
	}
	firstItem := g.GetManager().GetGuildM().OnControlGround(tempId, ground, g)
	return firstItem
}

func (g *Group) genRankInfo(firstItems map[uint64][]uint32) {
	g.GetData().GuildRank = &cl.GSTLastLRoundGuildRank{
		GuildRank: make(GSTGuildRankSort, 0),
	}
	guildM := g.GetManager().GetGuildM()
	var dropInfo *goxml.GuildSandTableHomeAwardInfo
	if g.GetData().MapInfo == nil {
		return
	}
	mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(g.GetData().MapInfo.MapConfigId)
	if mapInfo != nil {
		dropInfo = goxml.GetData().GuildSandTableHomeAwardInfoM.GetDropInfo(mapInfo.HomeAwardGroup)
	}

	for _, guildID := range g.GetData().MatchedGuild {
		if guildID == 0 {
			continue
		}
		guild := guildM.GetGuild(guildID)
		if guild != nil {
			guildRankInfo := &cl.GSTSimpleGuild{
				GuildId:        guild.GetData().GetGuild().GuildId,
				Score:          maps.Clone(guild.GetData().GetGuild().Score),
				GroundNum:      guild.GetData().GetGuild().GroundNum,
				FirstItemLands: firstItems[guildID],
			}
			g.GetData().GuildRank.GuildRank = append(g.GetData().GuildRank.GuildRank, guildRankInfo)
			guild.addUsersRankAndGenBoxReward(dropInfo)
		}
	}
	sort.Sort(GSTGuildRankSort(g.GetData().GuildRank.GuildRank))
	g.addGuildRankLog()
}

func (g *Group) addGuildRankLog() {
	sta := g.GetManager().GetStaM().GetSta().State
	logInfo := &cl.GSTLogInfo{
		LogType:  cl.GSTLogType_LogType_LRoundGuildRank,
		LRound:   sta.LRound,
		LogInfo:  &cl.GSTLogData{},
		GroupId:  g.GetData().Id,
		Round:    sta.Round,
		SeasonId: sta.SeasonId,
	}
	logData := &cl.GSTLastLRoundGuildRank{
		GuildRank: make([]*cl.GSTSimpleGuild, 0),
	}
	logInfo.LogInfo.GuildRank = logData
	guildM := g.GetManager().GetGuildM()
	for _, guildRank := range g.GetData().GuildRank.GuildRank {
		guild := guildM.GetGuild(guildRank.GuildId)
		if guild != nil {
			guildRankInfo := &cl.GSTSimpleGuild{
				GuildId:    guild.GetData().GetGuild().GuildId,
				Score:      maps.Clone(guild.GetData().GetGuild().Score),
				Name:       guild.GetData().GetGuild().Name,
				Badge:      guild.GetData().GetGuild().Badge,
				ExpireTime: guild.GetData().GetGuild().ExpireTime,
				Partition:  guild.GetData().GetGuild().Partition,
				GroundNum:  guild.GetData().GetGuild().GroundNum,
			}
			logData.GuildRank = append(logData.GuildRank, guildRankInfo)
		}
	}

	for _, guildRank := range g.GetData().GuildRank.GuildRank {
		//每个公会都要存一份
		logInfo.GuildId = guildRank.GuildId
		g.GetManager().GetGuildLogM().AddLog(logInfo.Clone(), true)
	}
}

func (g *Group) OnResetLRound() {
	for _, ground := range g.grounds {
		ground.OnResetLRound()
	}
	for _, guildID := range g.GetData().MatchedGuild {
		if guildID == 0 {
			continue
		}
		guild := g.GetManager().GetGuildM().GetGuild(guildID)
		if guild != nil {
			guild.UpdateLRoundSettle(g.GetGuildRank(guildID), guildID)
		}
	}
	if g.GetArenaRound() != 0 {
		g.GetData().ArenaInfo.GuildsInfo = nil
	}
}

func (g *Group) OnGuildDissolve(guild *Guild) {
	guildID := guild.GetData().Guild.GuildId
	for index, matchedGuildID := range g.GetData().MatchedGuild {
		if matchedGuildID == guildID {
			g.GetData().MatchedGuild[index] = 0
			break
		}
	}
	for id, ground := range g.GetData().MapInfo.GSTGroundInfos {
		if ground.GuildId == guildID {
			ground.GuildId = 0
			// 删除这块地的所有龙技
			for _, matchedGuildID := range g.GetData().MatchedGuild {
				dragonGuild := g.GetManager().GetDragonM().GetDragonGuild(matchedGuildID)
				if dragonGuild != nil && dragonGuild.GetDragonSkill().TargetGroundId == id {
					dragonGuild.SetDragonSkillGround(g.GetData().GetId(), 0, nil)
				}
			}
		}
		for index, guildTeam := range ground.FightTeams {
			if guildTeam.GuildId == guildID {
				ground.FightTeams = append(ground.FightTeams[:index], ground.FightTeams[:index+1]...)
				break
			}
		}
	}
	for index, guildRank := range g.GetData().GuildRank.GuildRank {
		if guildRank.GuildId == guildID {
			g.GetData().GuildRank.GuildRank = append(g.GetData().GuildRank.GuildRank[:index], g.GetData().GuildRank.GuildRank[index+1:]...)
			break
		}
	}
	if g.GetData().ArenaInfo != nil {
		for index, guildInfo := range g.GetData().ArenaInfo.GuildsInfo {
			if guildInfo.GuildId == guildID {
				g.GetData().ArenaInfo.GuildsInfo = append(g.GetData().ArenaInfo.GuildsInfo[:index], g.GetData().ArenaInfo.GuildsInfo[index+1:]...)
				break
			}
		}
	}
	g.SetChange()
}

func (g *Group) OnGuildUserQuit(guildUser *GuildUser) {
	for _, team := range guildUser.GetData().Teams {
		if team.Id != 0 {
			ground := g.GetGround(team.Id)
			if ground != nil {
				ground.OnGuildUserTeamQuit(guildUser.GetData().Info.Id, guildUser.GetGuildId(), team.TeamIndex)
			}
			team.Id = 0
			guildUser.SetChange()
		}
	}
}

func (g *Group) GetGuildRank(guildID uint64) uint32 {
	for index, guildRank := range g.GetData().GetGuildRank().GuildRank {
		if guildID == guildRank.GuildId {
			return uint32(index + 1)
		}
	}
	return 0
}

func (g *Group) GetMoveGroundID(guildID uint64, groundIDs []uint32) uint32 {
	if len(groundIDs) < 2 || len(groundIDs) >= 1000 {
		l4g.Error("move num err")
		return 0
	}
	lround := g.GetManager().GetStaM().GetSta().State.LRound
	groundInfos := make([]*goxml.MapGroundInfo, len(groundIDs))
	mapID := g.GetMapID()
	for index, groundID := range groundIDs {
		groundInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(groundID)
		if groundInfo == nil {
			l4g.Errorf("GuildSandTableMapInfo cant find ground %d", groundID)
			return 0
		}
		if groundInfo.MapID != mapID {
			l4g.Errorf("not same map %d %d", groundInfo.MapID, mapID)
			return 0
		}
		if !groundInfo.CanUnlock(lround) {
			l4g.Errorf("%d ground lock %d", groundID, lround)
			return 0
		}
		groundInfos[index] = groundInfo
	}
	var selfMainBaseId uint32
	for index, fromInfo := range groundInfos {
		if index == len(groundIDs)-1 {
			//目的地不能为其他公会基地
			if g.IsOtherMainBase(selfMainBaseId, fromInfo) {
				l4g.Errorf("target %d IsOtherMainBase", fromInfo.ID)
				return 0
			}
			return g.InitMoveGround(fromInfo)
		}
		fromGroundID := fromInfo.ID
		if fromInfo.MasterID != 0 {
			fromGroundID = fromInfo.MasterID
		}
		toInfo := groundInfos[index+1]
		toGroundID := toInfo.ID
		if toInfo.MasterID != 0 {
			toGroundID = toInfo.MasterID
		}
		if fromGroundID == toGroundID {
			//同一个主从地块内可以随意移动
			continue
		}
		fromGround := g.GetGround(fromGroundID)
		if fromGround == nil {
			l4g.Errorf("%d cant find ground %d", guildID, fromGroundID)
			return 0
		}
		if fromGround.GetControlGuildID() != guildID {
			l4g.Errorf("%d dont control ground %d", guildID, fromGroundID)
			return 0
		}
		if selfMainBaseId == 0 {
			// 一定要从自己主基地为起点去其他地块
			if fromGround.GetGroundType() != goxml.GuildSandTableLandTypeMainBase {
				l4g.Errorf("start from err ground %d", fromGroundID)
				return 0
			}
			selfMainBaseId = fromGroundID
		}
		toGround := g.GetGround(toGroundID)
		if toGround != nil && toGround.GetGroundType() == goxml.GuildSandTableLandTypeArena && g.GetArenaRound() != 0 {
			return toGround.GetID()
		}
		fromHex := NewHex(int(fromInfo.X), int(fromInfo.Y))
		toHex := NewHex(int(toInfo.X), int(toInfo.Y))
		if HexDistance(fromHex, toHex) != 1 {
			l4g.Errorf("%+v %+v neighbor", fromHex, toHex)
			return 0
		}
	}
	return 0
}

func (g *Group) IsOtherMainBase(selfMainBase uint32, info *goxml.MapGroundInfo) bool {
	if info.MasterID != 0 {
		masterInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(info.MasterID)
		if masterInfo == nil {
			l4g.Errorf("GuildSandTableMapInfo cant find ground %d", info.MasterID)
			return true
		}
		info = masterInfo
	}

	mapConfigInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapInfoExt(info.MapID)
	if mapConfigInfo == nil {
		l4g.Errorf("GuildSandTableMapInfoM cant find. mapID:%d", info.MapID)
		return true
	}

	for _, mainBase := range mapConfigInfo.MainBases {
		if info.ID == mainBase.ID && info.ID != selfMainBase {
			// 不能到其他基地去
			return true
		}
	}
	return false
}

func (g *Group) InitMoveGround(info *goxml.MapGroundInfo) uint32 {
	//从地块直接移动到主地块上
	if info.MasterID != 0 {
		masterInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(info.MasterID)
		if masterInfo == nil {
			l4g.Errorf("GuildSandTableMapInfo cant find ground %d", info.MasterID)
			return 0
		}
		info = masterInfo
	}
	ground := g.GetGround(info.ID)
	if ground == nil {
		stage := info.Stages[0]
		groundInfo := &cr.GSTGroundInfo{
			LandId: stage.LandID,
		}
		if stage.MonsterSort != 0 {
			groundInfo.MonsterInfo = &cl.GSTFightTeam{}
		}
		ground = g.NewGround(info.ID, groundInfo)
		if ground == nil {
			l4g.Errorf("InitMoveGround %d err", info.ID)
			return 0
		}
	}
	if !ground.CheckMove() {
		return 0
	}
	return ground.GetID()
}

func (g *Group) GetMatchedGuild() []uint64 {
	return g.GetData().GetMatchedGuild()
}

func (g *Group) GetGroupGroundCount() uint64 {
	var totalGround uint32
	for _, typeGrounds := range g.taskInfoCache {
		for _, grounds := range typeGrounds {
			for _, ground := range grounds {
				if ground == nil {
					continue
				}
				totalGround += ground.GetGroundNum()
			}
		}
	}
	return uint64(totalGround)
}

func (g *Group) GetTypeGroupGroundCount(groundType uint32) uint64 {
	var totalCount uint64
	for _, typeGrounds := range g.taskInfoCache {
		grounds, exist := typeGrounds[groundType]
		if !exist {
			continue
		}
		totalCount += uint64(len(grounds))
	}
	return totalCount
}

func (g *Group) GetMapID() uint32 {
	mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(g.GetData().MapInfo.MapConfigId)
	if mapInfo == nil {
		l4g.Errorf("GuildSandTableMapGroupInfo cant find.%d", g.GetData().MapInfo.MapConfigId)
		return 0
	}
	return mapInfo.MapId
}

func (g *Group) buildTaskCache(ground IGround) {
	if ground == nil {
		return
	}
	guildID := ground.GetControlGuildID()
	if guildID == 0 {
		return
	}
	_, exist := g.taskInfoCache[guildID]
	if !exist {
		g.taskInfoCache[guildID] = make(map[uint32][]IGround)
	}
	_, exist = g.taskInfoCache[guildID][ground.GetGroundType()]
	if !exist {
		g.taskInfoCache[guildID][ground.GetGroundType()] = make([]IGround, 0, 10)
	}
	var isFind bool
	for index, iGround := range g.taskInfoCache[guildID][ground.GetGroundType()] {
		if iGround == nil {
			continue
		}
		if iGround.GetID() == ground.GetID() {
			isFind = true
			g.taskInfoCache[guildID][ground.GetGroundType()][index] = ground
		}
	}
	if !isFind {
		g.taskInfoCache[guildID][ground.GetGroundType()] = append(g.taskInfoCache[guildID][ground.GetGroundType()], ground)
	}
}

type guildBless struct {
	totalCount uint32
	topBlessId []uint32
}

type guildBlessSort struct {
	guildId    uint64
	goddessId  uint32
	blessCount uint32
	donateTime int64
}

func (g *Group) buildBlessGuildMap() map[uint64]*guildBless {
	ret := make(map[uint64]*guildBless)
	goddessDonateSort := make(map[uint32]guildBlessSortS) //goddessId
	for _, guildId := range g.GetData().MatchedGuild {
		if guildId == 0 {
			continue
		}
		gstGuild := g.mgr.mgr.GetGuildM().GetGuild(guildId)
		if gstGuild == nil {
			l4g.Errorf("buildBlessGuildMap find Guild:%d failed", guildId)
			continue
		}
		var totalCount uint32
		for _, blessInfo := range gstGuild.GetData().GetBlessInfo() {
			if blessInfo == nil {
				continue
			}
			_, exist := goddessDonateSort[blessInfo.GoddessId]
			if !exist {
				goddessDonateSort[blessInfo.GoddessId] = make([]*guildBlessSort, 0, 6) //nolint:mnd
			}
			goddessDonateSort[blessInfo.GoddessId] = append(goddessDonateSort[blessInfo.GoddessId], &guildBlessSort{
				guildId:    guildId,
				goddessId:  blessInfo.GoddessId,
				blessCount: blessInfo.BlessCount,
				donateTime: blessInfo.DonateTime,
			})
			totalCount += blessInfo.BlessCount
		}
		ret[guildId] = &guildBless{totalCount: totalCount}
	}

	for _, goddessDonate := range goddessDonateSort {
		sort.Sort(goddessDonate)
		if len(goddessDonate) > 0 {
			topInfo := goddessDonate[0]
			_, exist := ret[topInfo.guildId]
			if !exist {
				ret[topInfo.guildId] = &guildBless{}
			}
			ret[topInfo.guildId].topBlessId = append(ret[topInfo.guildId].topBlessId, topInfo.goddessId)
		}
	}

	return ret
}

func (g *Group) GetTopGuildByBlessId(blessId uint32) uint64 {
	var blessSortS guildBlessSortS

	for _, guildId := range g.GetData().MatchedGuild {
		if guildId == 0 {
			continue
		}
		gstGuild := g.mgr.mgr.GetGuildM().GetGuild(guildId)
		if gstGuild == nil {
			l4g.Errorf("buildBlessGuildMap find Guild:%d failed", guildId)
			continue
		}
		for _, blessInfo := range gstGuild.GetData().GetBlessInfo() {
			if blessInfo == nil {
				continue
			}
			if blessInfo.GoddessId != blessId {
				continue
			}

			blessSortS = append(blessSortS, &guildBlessSort{
				guildId:    guildId,
				goddessId:  blessInfo.GoddessId,
				blessCount: blessInfo.BlessCount,
				donateTime: blessInfo.DonateTime,
			})
			break
		}
	}

	if len(blessSortS) > 0 {
		sort.Sort(blessSortS)
		topInfo := blessSortS[0]
		return topInfo.guildId
	}

	return 0
}

func (g *Group) GetGuildMainBase(guildID uint64) IGround {
	return g.mainBases[guildID]
}

func (g *Group) CheckGuildCanAddMatch(guild *Guild) bool {
	if guild.GetData().LastGroupId == 0 {
		return true
	}
	var sameRoomNum uint32
	for _, guildID := range g.GetMatchedGuild() {
		if guildID == 0 {
			continue
		}
		matchedGuild := g.GetManager().GetGuildM().GetGuild(guildID)
		if matchedGuild != nil && matchedGuild.GetData().LastGroupId == guild.GetData().LastGroupId {
			sameRoomNum++
			if sameRoomNum+1 > goxml.GetData().GuildSandTableConfigInfoM.RepeatMatchLimitNum {
				return false
			}
		}
	}
	return true
}

func (g *Group) updateArenaState() {
	// 屏蔽老的擂台赛
	/*if g.GetData().ArenaInfo == nil {
		g.GetData().ArenaInfo = &cl.GSTArenaInfo{}
	}
	openArenaRound := g.GetData().ArenaUnlockedRound
	state := g.GetManager().GetStaM().GetSta().State
	for _, condition := range goxml.GetData().GuildSandTableConfigInfoM.ArenaOpenConditions {
		if openArenaRound >= condition.Value {
			continue
		}
		if condition.Type == 1 {
			if state.GetLRound() > condition.Count {
				g.AddArenaRound(condition.Count, state.GetLRound(), true)
			} else if state.GetLRound() == condition.Count {
				openArenaRound = condition.Value
			}
		}
	}
	var opened bool
	for ; openArenaRound > 0; openArenaRound-- {
		if !opened {
			g.AddArenaRound(openArenaRound, state.GetLRound(), false)
			opened = true
		} else {
			g.AddArenaRound(openArenaRound, state.GetLRound(), true)
		}
	}
	for _, guildID := range g.GetData().MatchedGuild {
		if guildID == 0 {
			continue
		}
		guild := g.GetManager().GetGuildM().GetGuild(guildID)
		if guild != nil {
			guild.UpdateArenaVoteTimes()
		}
	}
	if opened {
		g.SetChange()
	}
	*/
}

func (g *Group) AddArenaRound(round, lRound uint32, isMiss bool) {
	for _, arenaState := range g.GetData().ArenaInfo.ArenaStates {
		if arenaState.Round == round {
			return
		}
	}
	arenaState := &cl.GSTArenaState{
		Round:  round,
		Lround: lRound,
	}
	if !isMiss {
		mapConfigInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapInfoExt(g.GetMapID())
		if mapConfigInfo == nil || mapConfigInfo.Arena == nil {
			l4g.Errorf("GuildSandTableMapInfoM error. mapID:%d", g.GetMapID())
			return
		}
		arenaState.IsOpen = true
		g.InitMoveGround(mapConfigInfo.Arena)
	}
	g.GetData().ArenaInfo.ArenaStates = append(g.GetData().ArenaInfo.ArenaStates, arenaState)
}

func (g *Group) GetArenaRound() uint32 {
	if g.GetData().ArenaInfo == nil {
		return 0
	}
	for _, state := range g.GetData().ArenaInfo.ArenaStates {
		if state.IsOpen && state.Lround == g.GetManager().GetStaM().GetSta().State.LRound {
			return state.Round
		}
	}
	return 0
}

func (g *Group) GetMaxArenaRound() uint32 {
	if g.GetData().ArenaInfo == nil {
		return 0
	}
	var maxRound uint32
	for _, state := range g.GetData().ArenaInfo.ArenaStates {
		if state.Round > maxRound {
			maxRound = state.Round
		}
	}
	return maxRound
}

func (g *Group) ArenaUnlockedRound(round uint32) {
	if g.GetData().ArenaUnlockedRound < round {
		g.GetData().ArenaUnlockedRound = round
		g.SetChange()
	}
}

func (g *Group) GetArenaGuildInfo(guildID uint64) *cl.GSTArenaGuildInfo {
	for _, guildInfo := range g.GetData().ArenaInfo.GuildsInfo {
		if guildInfo.GuildId == guildID {
			return guildInfo
		}
	}
	guildInfo := &cl.GSTArenaGuildInfo{
		GuildId: guildID,
	}
	g.GetData().ArenaInfo.GuildsInfo = append(g.GetData().ArenaInfo.GuildsInfo, guildInfo)
	g.SetChange()
	return guildInfo
}

func (g *Group) GetArenaGuildPopularScore(guildID uint64) int32 {
	for _, guildInfo := range g.GetData().ArenaInfo.GuildsInfo {
		if guildInfo.GuildId == guildID {
			return guildInfo.FavourTimes - guildInfo.OpposeTimes
		}
	}
	return 0
}

func (g *Group) ArenaVote(user *GuildUser, guild, targetGuild *Guild, isFavour bool) *cl.GSTArenaVoteRecord {
	var totalCount int32
	guildInfo := g.GetArenaGuildInfo(targetGuild.GetData().Guild.GuildId)
	if isFavour {
		guildInfo.FavourTimes++
		totalCount = guildInfo.FavourTimes
	} else {
		guildInfo.OpposeTimes++
		totalCount = guildInfo.OpposeTimes
	}
	voteRecord := &cl.GSTArenaVoteRecord{
		Time:      time.Now().Unix(),
		GuildName: guild.GetName(),
		UserName:  user.GetData().GetInfo().Name,
		IsFavour:  isFavour,
	}
	if len(guildInfo.VoteRecords) >= GuildArenaRecordNum {
		guildInfo.VoteRecords = slices.Clone(guildInfo.VoteRecords[1:])
	}
	guildInfo.VoteRecords = append(guildInfo.VoteRecords, voteRecord)

	logData := &log.LogGSTVote{
		Base:       user.BuildGstLogGeneral(),
		ArenaRound: g.GetArenaRound(),
		Partition:  g.GetManager().baseModule.Partition(),
		Uid:        user.GetData().Info.Id,
		VoteDetail: &log.LogGSTVoteDetail{
			GuildId:    targetGuild.GetData().Guild.GuildId,
			Count:      1,
			TotalCount: totalCount,
			IsFavour:   isFavour,
		},
	}
	dclog.LogGSTVote(g.GetManager().baseModule.GetSrv(), g.GetManager().baseModule.OpGroup(), logData)
	g.SetChange()
	return voteRecord.Clone()
}

func (g *Group) GetArenaState() *cl.GSTArenaState {
	if g.GetData().ArenaInfo == nil {
		return nil
	}
	for _, state := range g.GetData().ArenaInfo.ArenaStates {
		if state.IsOpen && state.Lround == g.GetManager().GetStaM().GetSta().State.LRound {
			return state
		}
	}
	return nil
}

func (g *Group) GetArenaStates() []*cl.GSTArenaState {
	if g.GetData().ArenaInfo == nil {
		return nil
	}
	return g.GetData().ArenaInfo.ArenaStates
}

func (g *Group) AddArenaWin(user *GuildUser) {
	state := g.GetArenaState()
	if state == nil {
		return
	}
	if state.FightRecord == nil {
		state.FightRecord = &cl.GSTArenaFightRecord{}
	}
	var updateUserRecord *cl.GSTArenaGuildUserRank
	for _, userRecord := range state.FightRecord.UserRank {
		if userRecord.User.Id == user.GetData().Info.Id {
			updateUserRecord = userRecord
		}
	}
	if updateUserRecord == nil {
		updateUserRecord = &cl.GSTArenaGuildUserRank{
			User: &cl.GSTGuildUserBase{
				Id:         user.GetData().Info.Id,
				Name:       user.GetData().Info.Name,
				GuildId:    user.GetData().Info.GuildId,
				BaseId:     user.GetData().Info.BaseId,
				ExpireTime: user.GetData().Info.ExpireTime,
				SeasonLv:   user.GetData().Info.SeasonLv,
				Sid:        user.GetData().Info.Sid,
			},
		}
		state.FightRecord.UserRank = append(state.FightRecord.UserRank, updateUserRecord)
	}
	updateUserRecord.Wins++
	g.SetChange()
}

type GSTArenaGuildRank struct {
	data  *cl.GSTArenaGuildRank
	guild *Guild
}

func (g *Group) OnArenaFinish(guildFightTeams []*cr.GSTGuildFightTeam) {
	state := g.GetArenaState()
	if state == nil {
		return
	}
	if state.FightRecord == nil {
		state.FightRecord = &cl.GSTArenaFightRecord{}
	}
	guildNum := len(guildFightTeams)
	guildSlice := make([]*GSTArenaGuildRank, guildNum)
	for index, guildFightTeam := range guildFightTeams {
		guildRank := &cl.GSTArenaGuildRank{
			Guild: &cl.GSTSimpleGuild{
				GuildId: guildFightTeam.GuildId,
			},
			Wins:      guildFightTeam.Win,
			MissMatch: uint32(len(guildFightTeam.Teams)) - guildFightTeam.MatchIndex,
			TeamNum:   uint32(len(guildFightTeam.Teams)),
		}
		for i := guildFightTeam.MatchIndex; i < uint32(len(guildFightTeam.Teams)); i++ {
			uid := guildFightTeam.Teams[i].User.Id
			isFind := false
			for _, userRank := range state.FightRecord.UserRank {
				if userRank.User.Id == uid {
					isFind = true
					userRank.MissMatch++
					break
				}
			}
			if !isFind {
				user := g.GetManager().GetGuildUserM().GetUser(uid)
				if user != nil {
					updateUserRecord := &cl.GSTArenaGuildUserRank{
						User: &cl.GSTGuildUserBase{
							Id:         user.GetData().Info.Id,
							Name:       user.GetData().Info.Name,
							GuildId:    user.GetData().Info.GuildId,
							BaseId:     user.GetData().Info.BaseId,
							ExpireTime: user.GetData().Info.ExpireTime,
							SeasonLv:   user.GetData().Info.SeasonLv,
							Sid:        user.GetData().Info.Sid,
						},
						MissMatch: 1,
					}
					state.FightRecord.UserRank = append(state.FightRecord.UserRank, updateUserRecord)

				}
			}
		}
		guild := g.GetManager().GetGuildM().GetGuild(guildFightTeam.GuildId)
		if guild != nil {
			guildRank.Guild.Name = guild.GetData().Guild.Name
			guildRank.Guild.Badge = guild.GetData().Guild.Badge
			guildRank.Guild.ExpireTime = guild.GetData().Guild.ExpireTime
			guildRank.Guild.Partition = guild.GetData().Guild.Partition
		}

		guildSlice[guildNum-index-1] = &GSTArenaGuildRank{
			data:  guildRank,
			guild: guild,
		}
	}

	slices.SortStableFunc(guildSlice, func(a, b *GSTArenaGuildRank) int {
		return -cmp.Compare(a.data.Wins+a.data.MissMatch, b.data.Wins+b.data.MissMatch)
	})
	for rank, rankData := range guildSlice {
		state.FightRecord.GuildRank = append(state.FightRecord.GuildRank, rankData.data)
		if rankData.guild != nil {
			mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(g.GetData().MapInfo.MapConfigId)
			if mapInfo == nil {
				l4g.Errorf("cant find map:%d", g.GetData().MapInfo.MapConfigId)
				continue
			}
			arenaInfo := goxml.GetData().GuildSandTableArenaInfoM.GetRecordByArenaGroupMatchIndexRank(mapInfo.ArenaGroup, state.Round, uint32(rank+1))
			if arenaInfo == nil {
				l4g.Errorf("cant find arenaInfo:%d %d %d", mapInfo.ArenaGroup, state.Round, uint32(rank+1))
				continue
			}
			rankData.guild.AddScore(cl.GSTGuildScoreType_GSTYPE_ARENA_RANK, arenaInfo.LandScore)
		}
	}
	slices.SortFunc(state.FightRecord.UserRank, func(a, b *cl.GSTArenaGuildUserRank) int {
		return -cmp.Compare(a.Wins+a.MissMatch, b.Wins+b.MissMatch)
	})
	g.SetChange()
}

//func (g *Group) updateBossLRound() {
//	//公会战频道战斗失败
//	var targetSid uint64
//	pushMsg := &l2c.CS2L_GSTPushChatMessage{
//		Ret:  uint32(ret.RET_OK),
//		Type: goxml.ChatGstBossAllFail,
//	}
//	for _, guildID := range g.GetData().MatchedGuild {
//		if guildID == 0 {
//			continue
//		}
//		guild := g.GetManager().GetGuildM().GetGuild(guildID)
//		if guild != nil {
//			if guild.updateBossLRound(g.GetData().MapInfo.MapConfigId) {
//				if targetSid == 0 {
//					targetSid = guild.GetData().Guild.GuildSid
//					pushMsg.ChatRoomId = guild.GetChatRoomID()
//				}
//				pushMsg.Params = append(pushMsg.Params, guild.GetName())
//			}
//		}
//	}
//	if targetSid != 0 {
//		g.GetManager().GetBaseModule().SendCmdToLogic(targetSid, 0, l2c.ID_MSG_CS2L_GSTPushChatMessage, pushMsg)
//	}
//}

func (g *Group) GetGuildScorePreview(guildID uint64) uint32 {
	blessAdd := g.buildBlessGuildMap()
	score := g.CalGuildScorePreview(guildID, blessAdd[guildID])
	return score
}

func (g *Group) CalGuildScorePreview(guildID uint64, guildBless *guildBless) uint32 {
	guild := g.GetManager().GetGuildM().GetGuild(guildID)
	if guild == nil {
		return 0
	}
	controlGroundScore := guild.CalGuildControlGroundScorePreview(g, guildBless)
	buildScore := guild.CalGuildBuildScorePreview(guildBless)
	return controlGroundScore + buildScore
}

func (g *Group) getChallengeUserAndGuildRankList(isTotalScore bool) ([]*cl.GSTChallengeGuildUserRank, []*cl.GSTChallengeGuildRank) {
	userList := make([]*cl.GSTChallengeGuildUserRank, 0, len(g.GetData().MatchedGuild)*int(goxml.GetData().GuildLevelInfoM.GetMaxMemberCount()))
	guildList := make([]*cl.GSTChallengeGuildRank, 0, len(g.GetData().MatchedGuild))
	for _, guildID := range g.GetData().MatchedGuild {
		guild := g.mgr.mgr.GetGuildM().GetGuild(guildID)
		if guild == nil {
			l4g.Errorf("getChallengeGuildUserRankList: GetGuild is nil. guildID:%d", guildID)
			continue
		}
		guildScore, guildScoreTm := uint32(0), int64(0)
		for _, uid := range guild.GetData().GetGuild().UserIds {
			guildUser := g.mgr.mgr.GetGuildUserM().GetUser(uid)
			if guildUser == nil {
				// l4g.Errorf("getChallengeGuildUserRankList: get guildUser is nil. uid:%d", uid)
				continue
			}
			challenge := guildUser.GetData().GetChallenge()
			if challenge == nil {
				continue
			}
			score := challenge.Score
			if isTotalScore {
				score = challenge.TotalScore
			}
			if score == 0 {
				continue
			}
			guildScore += score
			if challenge.ScoreTm > guildScoreTm {
				guildScoreTm = challenge.ScoreTm
			}
			rankInfo := &cl.GSTChallengeGuildUserRank{
				User:    guildUser.FlushSimpleUserSnapshot(),
				Score:   score,
				ScoreTm: challenge.ScoreTm,
			}
			userList = append(userList, rankInfo)
		}
		guildList = append(guildList, &cl.GSTChallengeGuildRank{
			Guild: &cl.GSTSimpleGuild{
				GuildId:    guild.GetData().Guild.GuildId,
				Name:       guild.GetData().Guild.Name,
				Badge:      guild.GetData().Guild.Badge,
				ExpireTime: guild.GetData().Guild.ExpireTime,
				Partition:  guild.GetData().Guild.GetPartition(),
			},
			Score:   guildScore,
			ScoreTm: guildScoreTm,
		})
	}
	return userList, guildList
}

func (g *Group) AddChallengeShowLog(log *cl.GSTChallengeShowLog) {
	g.GetData().ChallengeShow = append(g.GetData().ChallengeShow, log)
	if len(g.GetData().ChallengeShow) > 10 {
		g.GetData().ChallengeShow = g.GetData().ChallengeShow[1:]
	}
	g.SetChange()
}

func (g *Group) challengeReset(info *goxml.GuildSandTableChallengeInfo) {
	userList, guildList := g.getChallengeUserAndGuildRankList(false)
	slices.SortFunc(userList, sortChallengeUserScoreRank)
	slices.SortFunc(guildList, sortChallengeGuildScoreRank)
	logInfo := &cl.GSTLogInfo{
		LogType:  cl.GSTLogType_LogType_ChallengeRecord,
		LRound:   info.MatchIndex,
		LogInfo:  &cl.GSTLogData{},
		GroupId:  g.GetData().GetId(),
		Round:    g.mgr.mgr.GetStaM().GetRound(),
		SeasonId: g.GetManager().GetSeasonID(),
	}
	logInfo.LogInfo.ChallengeRecord = &cl.GSTChallengeRecord{}

	g.GetData().ChallengeTop = &cl.GSTChallengeLastTop{}
	for i, userInfo := range userList {
		if i < ChallengeLastTopUserNumber {
			g.GetData().ChallengeTop.UserTop_3 = append(g.GetData().ChallengeTop.UserTop_3, userInfo)
		}
		if i < ChallengeGSTLogRecordUserNumber {
			logInfo.LogInfo.ChallengeRecord.UserRank = append(logInfo.LogInfo.ChallengeRecord.UserRank, userInfo)
		}
		gstUser := g.mgr.mgr.GetGuildUserM().GetUser(userInfo.User.Id)
		if gstUser != nil {
			logData := gstUser.GetLogTeamsInfo(g)
			dclog.LogGSTChallengeTeam(g.mgr.mgr.baseModule.GetSrv(), g.mgr.mgr.baseModule.OpGroup(), gstUser.GetData().Info.Id,
				gstUser.GetData().Info.Sid, logData)
		}
	}
	lastRankInfo := goxml.GetData().GuildSandTableChallengeGuildRankInfoM.GetMaxRankRecordByMatchIndex(info.MatchIndex)
	for i, guildRankInfo := range guildList {
		addLandScore := uint32(0)
		if guildRankInfo.Score > 0 {
			rank := uint32(i + 1)
			if rank <= ChallengeLastTopGuildNumber {
				g.GetData().ChallengeTop.GuildTop_3 = append(g.GetData().ChallengeTop.GuildTop_3, guildRankInfo)
			}
			rankInfo := goxml.GetData().GuildSandTableChallengeGuildRankInfoM.GetRecordByMatchIndexRank(info.MatchIndex, rank)
			if rankInfo == nil {
				l4g.Errorf("challengeReset: get challenge guild rank info nil. matchIndex:%d, rank:%d", info.MatchIndex, rank)
				continue
			}
			addLandScore = rankInfo.LandScore
			logInfo.LogInfo.ChallengeRecord.GuildRank = append(logInfo.LogInfo.ChallengeRecord.GuildRank, guildRankInfo)
		} else {
			if lastRankInfo != nil {
				addLandScore = lastRankInfo.LandScore
			}
		}

		guild := g.mgr.mgr.GetGuildM().GetGuild(guildRankInfo.GetGuild().GetGuildId())
		if guild == nil {
			l4g.Errorf("challengeReset: get guild nil. gid:%d", guildRankInfo.GetGuild().GetGuildId())
			continue
		}
		guild.AddScore(cl.GSTGuildScoreType_GSTYPE_CHALLENGE_RANK, addLandScore)
	}
	g.GetManager().GetGuildLogM().AddLog(logInfo, true)
	g.GetData().ChallengeShow = nil
	g.SetChange()
}
