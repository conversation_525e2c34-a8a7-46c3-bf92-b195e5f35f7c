package gst

import (
	"app/cross/activity"
	"app/goxml"
	"app/protos/in/config"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/r2c"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"slices"
	"sync/atomic"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"golang.org/x/exp/maps"
)

type Manager struct {
	state      int32
	baseModule activity.BaseModuler

	staM            *StaManager
	guildM          *GuildManager
	guildUserM      *GuildUserManager
	groupM          *GroupManager
	guildLogM       *GuildLogManager
	guildRankBakM   *GuildRankBakManager
	mongoLogManager *MongoLogManager
	dragonM         *DragonManager
	commonBakM      *CommonBakManager
	guildMobRankM   *GuildMobRankManager

	rd                  *rand.Rand
	lastSaveTime        int64
	syncGuildTime       int64
	waitSyncPartition   map[uint32]util.None
	waitSettlePartition map[uint32]util.None

	settleTime int64

	resourceLogCollect *ResourceLogCollect
}

func NewManager(actDB *config.CrossActDb) (*Manager, error) {
	m := new(Manager)
	m.staM = newStaManager(m)
	m.guildM = newGuildManager(m)
	m.guildUserM = newGuildUserManager(m)
	m.groupM = newGroupManager(m)
	m.guildLogM = newGuildLogManager(m)
	m.guildRankBakM = newGuildRankBakManager(m)
	m.dragonM = newDragonManager(m)
	m.commonBakM = newCommonBakManager(m)
	m.InitGuildMobRankM()
	m.lastSaveTime = time.Now().Unix()
	m.rd = rand.New(time.Now().UnixNano())
	m.resourceLogCollect = newResourceLogCollect(m)
	var err error
	m.mongoLogManager, err = newMongoLogManager(m, actDB.MongoAddr)
	return m, err
}

func (m *Manager) Init(baseModule activity.BaseModuler) bool {
	m.baseModule = baseModule
	InitEventWatcher(m.baseModule.GetEventM())

	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GSTLoad, &r2c.C2R_GSTLoad{
		Partition: m.baseModule.Partition(),
	})
	return true
}

func (m *Manager) GetBaseModule() activity.BaseModuler {
	return m.baseModule
}

func (m *Manager) Rand() *rand.Rand {
	return m.rd
}

func (m *Manager) CheckRunning() bool {
	return m.GetState() == stateRunning
}

func (m *Manager) GetSeasonID() uint32 {
	return m.GetStaM().GetSta().State.SeasonId
}

func (m *Manager) Close(baseModule activity.BaseModuler) {
	m.DbSave(0, true)
	m.GetGuildLogM().OnClose()
}
func (m *Manager) DeletePartition(baseModule activity.BaseModuler) {}
func (m *Manager) CheckCanResetPart(lastResetTime int64) bool {
	return false
}

func (m *Manager) SetState(state int32) {
	atomic.StoreInt32(&m.state, state)
}

func (m *Manager) GetState() int32 {
	return atomic.LoadInt32(&m.state)
}

func (m *Manager) LoadData(data *r2c.R2C_GSTLoad) {
	if data.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("GSTM.LoadData: err, actID:%d, partID:%d", m.baseModule.GetActivityID(), m.baseModule.Partition())
		return
	}
	m.mongoLogManager.InitActor()
	m.staM.Load(data.Mgr)
	m.groupM.Load(data.GroupMgr)
	m.guildUserM.Load(data.GuildUserMgr)
	m.guildM.Load(data.GuildMgr)
	m.guildLogM.Load(data.GuildLogMgr)
	m.guildRankBakM.Load(data.GuildRankBak)
	m.dragonM.Load(data.DragonGlobal, data.DragonGuild)
	m.commonBakM.Load(data.CommonBak)
	m.guildMobRankM.Load(data.GuildMobRank)
	m.SetRuning()
}

func (m *Manager) GetGroupM() *GroupManager {
	return m.groupM
}
func (m *Manager) GetGuildM() *GuildManager {
	return m.guildM
}
func (m *Manager) GetGuildUserM() *GuildUserManager {
	return m.guildUserM
}
func (m *Manager) GetGuildLogM() *GuildLogManager {
	return m.guildLogM
}
func (m *Manager) GetStaM() *StaManager {
	return m.staM
}
func (m *Manager) GetMongoLogM() *MongoLogManager {
	return m.mongoLogManager
}

func (m *Manager) GetGuildRankBakM() *GuildRankBakManager {
	return m.guildRankBakM
}

func (m *Manager) GetDragonM() *DragonManager {
	return m.dragonM
}

func (m *Manager) GetCommonBakM() *CommonBakManager {
	return m.commonBakM
}

func (m *Manager) GetGuildMobRankM() *GuildMobRankManager {
	return m.guildMobRankM
}

func (m *Manager) CanSyncGuild(msg *l2c.GUILD2GST_GSTSyncGuilds, partition uint32) bool {
	staM := m.GetStaM()
	if staM.GetSta().State.Stage >= cl.GST_STAGE_RoundFightState_Fight &&
		staM.GetSta().State.Stage <= cl.GST_STAGE_RoundFightState_Reward {
		//结算和奖励期间,不同步公会信息
		return false
	}
	if msg.FullSync {
		if staM.GetSta().State.Stage == cl.GST_STAGE_RoundFightState_SyncGuild {
			delete(m.waitSyncPartition, partition)
			if len(m.waitSyncPartition) == 0 {
				staM.SetStage(cl.GST_STAGE_RoundFightState_SyncGuildFinish)
				staM.PushSta()
			}
		}
	}
	return true
}

func (m *Manager) SetRuning() {
	m.SetState(stateFinishLoad)
	m.SetState(stateRunning)
}

// 回合重置
func (m *Manager) OnResetLRound() {
	m.GetGuildUserM().OnResetLRound()
	m.GetGroupM().OnResetLRound()
}

// 轮次重置
func (m *Manager) OnResetRound() {
	m.baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GSTResetRound, &r2c.C2R_GSTResetRound{
		Partition: m.baseModule.Partition(),
		SeasonId:  m.GetSeasonID(),
	})
	//重置
	m.groupM = newGroupManager(m)
	m.guildLogM = newGuildLogManager(m)

	m.GetStaM().OnResetRound()
	m.GetGuildM().OnResetRound()
	m.GetGuildUserM().OnResetRound()
}

// 赛季关闭
func (m *Manager) CloseSeason() {
	m.baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GSTResetSeason, &r2c.C2R_GSTResetSeason{
		Partition: m.baseModule.Partition(),
		SeasonId:  m.GetSeasonID(),
	})
	m.staM = newStaManager(m)
	m.guildM = newGuildManager(m)
	m.guildUserM = newGuildUserManager(m)
	m.groupM = newGroupManager(m)
	m.guildLogM = newGuildLogManager(m)
	m.dragonM = newDragonManager(m)
	m.staM.PushSta()
}

func (m *Manager) DbSave(now int64, force bool) {
	sta := m.GetStaM().GetSta()
	if !force {
		if now-m.lastSaveTime < DbSaveTime {
			return
		}
		//战斗结算期,修改太频繁不建议落地
		if sta.State.Stage == cl.GST_STAGE_RoundFightState_Fight {
			return
		}
	}

	needSave := false
	msg := &r2c.C2R_GSTSave{
		Partition: m.baseModule.Partition(),
		SeasonId:  m.GetSeasonID(),
	}

	msg.Mgr = m.GetStaM().DbSave()
	if msg.Mgr != nil {
		needSave = true
	}

	msg.GroupMgr = m.GetGroupM().DbSave(force)
	if msg.GroupMgr != nil {
		needSave = true
	}
	msg.GuildMgr = m.GetGuildM().DbSave(force)
	if msg.GuildMgr != nil {
		needSave = true
	}
	msg.GuildUserMgr = m.GetGuildUserM().DbSave(force)
	if msg.GuildUserMgr != nil {
		needSave = true
	}

	msg.GuildRankBak = m.GetGuildRankBakM().DbSave(force)
	if len(msg.GuildRankBak) != 0 {
		needSave = true
	}

	msg.DragonGlobal = m.GetDragonM().DbSaveGlobal()
	if msg.DragonGlobal != nil {
		needSave = true
	}

	msg.DragonGuild = m.GetDragonM().DbSaveGuild()
	if msg.DragonGuild != nil {
		needSave = true
	}

	msg.CommonBak = m.GetCommonBakM().DbSave()
	if len(msg.CommonBak) != 0 {
		needSave = true
	}

	if sta.MobRound != 0 {
		msg.GuildMobRank = m.GetGuildMobRankM().DbSave(force)
		if msg.GuildMobRank != nil {
			msg.GuildMobRound = sta.MobRound
			needSave = true
		}
	}

	if needSave {
		m.baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GSTSave, msg)
		m.lastSaveTime = now
	}
}

// 玩家报名
func (m *Manager) OnUserSign(signUser *l2c.GSTSignUser) ret.RET {
	sta := m.GetStaM().GetSta().State
	if sta.SeasonId == 0 {
		l4g.Errorf("season not open %d", signUser.Id)
		return ret.RET_GST_USER_CANT_SIGN
	}
	if sta.Stage >= cl.GST_STAGE_RoundFightState_Fight &&
		sta.Stage <= cl.GST_STAGE_RoundFightState_Reward {
		//结算和奖励期间,不让报名
		l4g.Errorf("stage%d cant sign %d", sta.Stage, signUser.Id)
		return ret.RET_GST_USER_CANT_SIGN
	}
	guildUser := m.GetGuildUserM().GetUser(signUser.Id)
	if guildUser != nil {
		//只更新展示数据
		guildUser.GetData().Info.Name = signUser.Name
		guildUser.GetData().Info.SeasonLv = signUser.SeasonLv
		guildUser.GetData().Info.BaseId = signUser.BaseId
		guildUser.GetData().Info.Sid = signUser.Sid
		guildUser.GetData().Info.SeasonLinkPower = signUser.SeasonLinkPower
		guildUser.GetData().Info.ExpireTime = signUser.ExpireTime
		guildUser.GetData().Info.Image = signUser.Image
		guildUser.GetData().Info.ShowHero = signUser.ShowHero
		guildUser.GetData().SeasonlinkActived = signUser.SeasonlinkActived
		guildUser.GetData().Info.TalentTreeLv = signUser.TalentTreeLv
		guildUser.GetData().Info.Title = signUser.Title
		guildUser.GetData().Info.SeasonAdd = signUser.SeasonAdd
		guildUser.SetChange()

		if guildUser.GetGuildId() != signUser.GuildId {
			nowGuild := m.GetGuildM().GetGuild(signUser.GuildId)
			if nowGuild != nil {
				nowGuild.AddUser(signUser.Id)
			}
		}
	} else {
		user := &cl.GSTGuildUser{
			Info: &cl.GSTGuildUserBase{
				Id:              signUser.Id,
				Name:            signUser.Name,
				SeasonLv:        signUser.SeasonLv,
				BaseId:          signUser.BaseId,
				ExpireTime:      signUser.ExpireTime,
				Sid:             signUser.Sid,
				SeasonLinkPower: signUser.SeasonLinkPower,
				Image:           signUser.Image,
				ShowHero:        signUser.ShowHero,
				TalentTreeLv:    signUser.TalentTreeLv,
				Title:           signUser.Title,
				SeasonAdd:       signUser.SeasonAdd,
			},
			SeasonlinkActived: signUser.SeasonlinkActived,
			AddFightTimes:     signUser.AddFightTimes,
		}
		for _, team := range signUser.Teams {
			formationTeam := &cl.GSTFormationTeamInfo{
				Heros:     team.Heros,
				TeamIndex: team.TeamIndex,
				HangUp:    true,
				Power:     team.Power,
			}
			user.Teams = append(user.Teams, formationTeam)
		}
		m.GetGuildUserM().NewUser(user)
		nowGuild := m.GetGuildM().GetGuild(signUser.GuildId)
		if nowGuild != nil {
			nowGuild.AddUser(user.Info.Id)
		}
	}

	return ret.RET_OK
}

func (m *Manager) Update(baseModule activity.BaseModuler, now int64) {
	if !m.CheckRunning() {
		return
	}
	m.LogicRun(now)
	m.DbSave(now, false)
	m.mongoLogManager.Update(now)
	m.resourceLogCollect.checkLogHandle()
}

func (m *Manager) CheckNewSeason(now int64) {
	info := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(now)
	if info == nil {
		return
	}
	if now < info.StartTm {
		return
	}
	//超过这个时间,赛季就不能开启了
	minTime := info.StartTm + goxml.GetData().GuildSandTableConfigInfoM.SeasonOpenTimeLimit
	if now >= minTime {
		return
	}

	staM := m.GetStaM()
	sta := goxml.GetData().GuildSandTableConfigInfoM.CalStaOnOpenNewSeason(now, info)
	staM.SetSta(sta)
	if sta.Stage == cl.GST_STAGE_RoundFightState_Operate {
		m.NewFightOperateRound(sta.LRound)
	} else {
		staM.PushSta()
	}
}

func (m *Manager) CheckSignEnd(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		l4g.Errorf("公会战 cant find seasonInfo %d", sta.SeasonId)
		return
	}
	endTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		int64(goxml.GetData().GuildSandTableConfigInfoM.SignTime)
	if now < endTime {
		l4g.Debugf("公会战 now %d, time %d", now, endTime)
		return
	}
	staM.SetStage(cl.GST_STAGE_Round0State_Matching)
	staM.PushSta()
}

func (m *Manager) Match(now int64) {
	m.GetGroupM().Match()
	m.GetGuildUserM().CheckUserHangUp(now)
	m.GetStaM().SetStage(cl.GST_STAGE_Round0State_Finish)
	m.GetStaM().PushSta()
}

func (m *Manager) CheckFightOperate(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		return
	}
	startTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		goxml.GetData().GuildSandTableConfigInfoM.SignLRoundTime +
		int64(sta.LRound)*goxml.GetData().GuildSandTableConfigInfoM.FightLRoundTime
	if now < startTime {
		l4g.Debugf("公会战 now %d, time %d", now, startTime)
		return
	}
	m.NewFightOperateRound(staM.mgr.GetStaM().GetSta().State.LRound + 1)
}

func (m *Manager) NewFightOperateRound(newLRound uint32) {
	staM := m.GetStaM()
	// 如果CheckMatch中断，只要状态没有被设置成功，那么这里还会被重复执行。
	m.dragonM.CheckMatch(staM.GetRound(), newLRound) // 新的公会战回合开始时，龙战检查匹配
	staM.SetStage(cl.GST_STAGE_RoundFightState_Operate)
	staM.SetLRound(newLRound)
	staM.PushSta()
	m.GetGroupM().updateState()
}

func (m *Manager) NewSignRound() {
	staM := m.GetStaM()
	staM.SetRound(staM.GetSta().State.Round + 1)
	staM.SetLRound(0)
	staM.SetStage(cl.GST_STAGE_Round0State_Sign)
	staM.PushSta()
}

func (m *Manager) CheckFightSettle(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		return
	}
	startTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		goxml.GetData().GuildSandTableConfigInfoM.SignLRoundTime +
		int64(sta.LRound-1)*goxml.GetData().GuildSandTableConfigInfoM.FightLRoundTime
	if sta.LRound == goxml.GetData().GuildSandTableConfigInfoM.MaxLRound {
		startTime += goxml.GetData().GuildSandTableConfigInfoM.FinalFightOperateTime
	} else {
		startTime += goxml.GetData().GuildSandTableConfigInfoM.FightOperateTime
	}
	if now < startTime {
		l4g.Debugf("公会战 now %d, time %d", now, startTime)
		return
	}
	// 战斗行动期结束，开始进行战斗结算
	m.GetGroupM().FightMatch()
	staM.SetStage(cl.GST_STAGE_RoundFightState_Fight)
	staM.PushSta()
}

func (m *Manager) CheckFightFinish() {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		return
	}

	// 获取战斗结算终止时间
	endTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		int64(goxml.GetData().GuildSandTableConfigInfoM.SignLRoundTime) +
		int64(sta.LRound-1)*goxml.GetData().GuildSandTableConfigInfoM.FightLRoundTime
	if sta.LRound == goxml.GetData().GuildSandTableConfigInfoM.MaxLRound {
		endTime += int64(goxml.GetData().GuildSandTableConfigInfoM.FinalFightOperateTime)
		endTime += int64(goxml.GetData().GuildSandTableConfigInfoM.FinalFightSettleTime)
	} else {
		endTime += int64(goxml.GetData().GuildSandTableConfigInfoM.FightOperateTime)
		endTime += int64(goxml.GetData().GuildSandTableConfigInfoM.FightSettleTime)
	}
	endTime *= 1000
	now := time.Now().UnixMilli()

	// 进行战斗结算
	isFinish := m.GetGroupM().CheckFightMatch(now, endTime)

	// 完成战斗结算
	if isFinish && now > endTime {
		m.GetGroupM().OnFinishFight()
		m.GetGuildRankBakM().BackUpRank()
		m.GetGuildUserM().OnFinishFight()
		if sta.LRound == goxml.GetData().GuildSandTableConfigInfoM.MaxLRound {
			staM.SetStage(cl.GST_STAGE_RoundFightState_RewardGuild)
		} else {
			staM.SetStage(cl.GST_STAGE_RoundFightState_Finish)
		}
		m.resourceLogCollect.OnFinishFight()
		staM.PushSta()
	} else {
		l4g.Debugf("公会战 now %d, time %d", now, endTime)
	}
}

func (m *Manager) SettleDivisionScore() {
	m.GetGuildRankBakM().SettleDivisionScore()
}

func (m *Manager) OnSettleDivisionScoreFinish(partition uint32) {
	delete(m.waitSettlePartition, partition)
	if len(m.waitSettlePartition) == 0 {
		m.GetStaM().SetRewardGuild()
	}
}

func (m *Manager) CheckRewardGuildFinish(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	if now-m.settleTime > 60 { //nolint:mnd
		//可重复执行
		m.SettleDivisionScore() // 公会战积分结算
		m.settleTime = now
	}
	if sta.RewardGuild {
		//只执行一次
		// 公会Boss战发奖
		//m.GetGuildUserM().OnRewardFight()
		staM.SetStage(cl.GST_STAGE_RoundFightState_Reward)
		staM.PushSta()
	}
}

func (m *Manager) CheckRewardFinish(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		return
	}
	endTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		goxml.GetData().GuildSandTableConfigInfoM.SignLRoundTime +
		int64(sta.LRound-1)*goxml.GetData().GuildSandTableConfigInfoM.FightLRoundTime +
		goxml.GetData().GuildSandTableConfigInfoM.FinalFightOperateTime +
		int64(goxml.GetData().GuildSandTableConfigInfoM.FinalFightSettleTime) +
		int64(goxml.GetData().GuildSandTableConfigInfoM.FinalSettleTime)

	if now < endTime {
		l4g.Debugf("公会战 now %d, time %d", now, endTime)
		return
	}
	staM.SetStage(cl.GST_STAGE_RoundFightState_Finish)
	staM.PushSta()
}

func (m *Manager) OnLRoundFinish(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		return
	}
	endTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		int64(goxml.GetData().GuildSandTableConfigInfoM.SignLRoundTime) +
		int64(sta.LRound)*goxml.GetData().GuildSandTableConfigInfoM.FightLRoundTime
	if now < endTime {
		l4g.Debugf("公会战 now %d, time %d", now, endTime)
		return
	}
	m.OnResetLRound()                 // 回合重置
	m.dragonM.CheckResetDragonRound() // 龙战场次重置

	if sta.Round == goxml.GetData().GuildSandTableConfigInfoM.MaxRound &&
		sta.LRound == goxml.GetData().GuildSandTableConfigInfoM.MaxLRound { // 赛季结束
		m.CloseSeason()
	} else if sta.LRound == goxml.GetData().GuildSandTableConfigInfoM.MaxLRound { // 轮次结束
		m.OnResetRound()
		m.NewSignRound()
	} else { // 进入下一回合
		m.NewFightOperateRound(staM.mgr.GetStaM().GetSta().State.LRound + 1)
	}
}

func (m *Manager) LogicRun(now int64) {
	sta := m.GetStaM().GetSta().State
	if sta.GetSeasonId() == 0 {
		m.CheckNewSeason(now)
	} else if sta.Stage == cl.GST_STAGE_Round0State_Sign {
		m.CheckSignEnd(now)
	} else if sta.Stage == cl.GST_STAGE_Round0State_Matching {
		m.Match(now)
	} else if sta.Stage == cl.GST_STAGE_Round0State_Finish {
		m.CheckFightOperate(now)
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_Operate {
		m.SyncGuildBeforeFight(now)
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_SyncGuild {
		m.CheckSyncGuild(now, 10)
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_SyncGuildFinish {
		m.CheckFightSettle(now)
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_Fight {
		m.CheckFightFinish()
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_RewardGuild {
		m.CheckRewardGuildFinish(now)
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_Reward {
		m.CheckRewardFinish(now)
	} else if sta.Stage == cl.GST_STAGE_RoundFightState_Finish {
		m.OnLRoundFinish(now)
	}
	m.CheckSyncGuild(now, 3600) //nolint:mnd
	m.GetGuildMobRankM().Update(now)
}

func (m *Manager) SyncGuildBeforeFight(now int64) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	seasonInfo := goxml.GetData().SeasonInfoM.Index(sta.SeasonId)
	if seasonInfo == nil {
		return
	}
	//和战斗时间同样,开始战斗的时候先同步公会信息
	startTime := seasonInfo.StartTm +
		int64(sta.Round-1)*goxml.GetData().GuildSandTableConfigInfoM.RoundTime +
		goxml.GetData().GuildSandTableConfigInfoM.SignLRoundTime +
		int64(sta.LRound-1)*goxml.GetData().GuildSandTableConfigInfoM.FightLRoundTime

	if sta.LRound == goxml.GetData().GuildSandTableConfigInfoM.MaxLRound {
		startTime += goxml.GetData().GuildSandTableConfigInfoM.FinalFightOperateTime
	} else {
		startTime += goxml.GetData().GuildSandTableConfigInfoM.FightOperateTime
	}
	if now < startTime {
		l4g.Debugf("公会战 now %d, time %d", now, startTime)
		m.GetDragonM().RunBotFight(now)
		return
	}
	m.GetDragonM().CheckSettlement()                                         // 每4个公会战回合1场龙战结算
	m.GetGuildRankBakM().BackUpDragon(m.GetCommonBakM().CheckBackUpDragon()) // 备份龙战结算结果
	m.GetDragonM().UseDragonSkill()                                          // 龙攻城技能结算
	m.CheckChallengeSettlement()                                             // 新擂台赛结算
	staM.SetStage(cl.GST_STAGE_RoundFightState_SyncGuild)
	staM.PushSta()
}

func (m *Manager) CheckSyncGuild(now int64, limitTime int64) {
	if now-m.syncGuildTime < limitTime {
		return
	}
	m.SyncGuild(now)
}

func (m *Manager) SyncGuild(now int64) {
	m.waitSyncPartition = make(map[uint32]util.None)
	sendMsgs := make(map[uint32]*l2c.GST2GUILD_GSTSyncGuilds, 0)
	for _, guild := range m.GetGuildM().guilds {
		partition := guild.GetData().GetGuild().GetPartition()
		sendMsg := sendMsgs[partition]
		if sendMsg == nil {
			sendMsg = &l2c.GST2GUILD_GSTSyncGuilds{
				FullSync: true,
			}
			sendMsgs[partition] = sendMsg
		}
		sendMsg.GuildId = append(sendMsg.GuildId, guild.GetData().Guild.GuildId)
	}

	for partition, sendMsg := range sendMsgs {
		sendData, err := proto.Marshal(sendMsg)
		if err != nil {
			l4g.Errorf("SyncGuildTimer marshal error: %s", err)
			return
		}
		m.baseModule.SendTransformMsgToNode(uint32(l2c.ACTIVITYID_GUILD), partition, uint32(l2c.ID_MSG_GST2GUILD_GSTSyncGuilds), sendData)
		m.waitSyncPartition[partition] = util.None{}
	}

	m.syncGuildTime = now
	staM := m.GetStaM()
	if len(m.waitSyncPartition) == 0 && staM.GetSta().State.Stage == cl.GST_STAGE_RoundFightState_SyncGuild {
		staM.SetStage(cl.GST_STAGE_RoundFightState_SyncGuildFinish)
		staM.PushSta()
	}
}

func (m *Manager) UpdateFightResult(msg *l2c.L2CS_GSTFight) {
	guildUser := m.GetGuildUserM().GetUser(msg.Fight.Attack.User.Id)
	if guildUser == nil {
		l4g.Errorf("cant find guildUser%d", msg.Fight.Attack.User.Id)
		return
	}
	guild := m.GetGuildM().GetGuild(guildUser.GetGuildId())
	if guild == nil {
		l4g.Errorf("cant find guild%d", guildUser.GetGuildId())
		return
	}
	group := m.GetGroupM().GetGroup(guild.GetData().GroupId)
	if group == nil {
		l4g.Errorf("cant find group%d", guild.GetData().GroupId)
		return
	}
	ground := group.GetGround(msg.Fight.GroundId)
	if ground == nil {
		l4g.Errorf("cant find ground%d", msg.Fight.GroundId)
		return
	}
	if msg.Ret != uint32(ret.RET_OK) {
		msg.Fight.IsWin = true
	}
	ground.UpdateFightResult(msg.Fight)
}

func (m *Manager) GetGroundData(uid uint64, req *l2c.L2CS_GSTGetGroundData, rsp *l2c.CS2L_GSTGetGroundData) {
	guildUser := m.GetGuildUserM().GetUser(uid)
	if guildUser == nil {
		l4g.Errorf("cant find user %d", uid)
		rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
		return
	}
	guild := m.GetGuildM().GetGuild(guildUser.GetGuildId())
	if guild == nil {
		l4g.Errorf("cant find guild %d", uid)
		rsp.Ret = uint32(ret.RET_GST_GUILD_NIL)
		return
	}
	group := m.GetGroupM().GetGroup(guild.GetData().GroupId)
	if group == nil {
		l4g.Errorf("cant find group %d", uid)
		rsp.Ret = uint32(ret.RET_GST_GROUP_NIL)
		return
	}
	ground := group.GetGround(req.Id)
	if ground == nil {
		return
	}
	ground.GetGSTGroundData(rsp, guildUser)
}

func (m *Manager) GetTeamsData(uid uint64, req *l2c.L2CS_GSTGetTeamsData, rsp *l2c.CS2L_GSTGetTeamsData) {
	guildUser := m.GetGuildUserM().GetUser(uid)
	if guildUser == nil {
		l4g.Errorf("cant find user %d", uid)
		rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
		return
	}
	if req.Type == 1 {
		rsp.TeamsData = m.getSelfTeamsData(guildUser)
	} else if req.Type == 2 || req.Type == 3 {
		rsp.TeamsData = m.getGuildAllTeamsData(req.Type, guildUser)
	}
}

func (m *Manager) getSelfTeamsData(guildUser *GuildUser) []*cl.GSTTeamData {
	teamsData := make([]*cl.GSTTeamData, 1)
	teamsData[0] = &cl.GSTTeamData{}
	for _, team := range guildUser.GetData().Teams {
		teamClone := team.Clone()
		for _, heroInfo := range teamClone.Heros {
			heroInfo.ActivedSeasonLink = guildUser.IsSeasonLinkActived(heroInfo.SysId)
		}
		teamsData[0].TeamInfo = append(teamsData[0].TeamInfo, teamClone)
	}
	return teamsData
}

func (m *Manager) getGuildAllTeamsData(reqType uint32, guildUser *GuildUser) []*cl.GSTTeamData {
	guild := m.GetGuildM().GetGuild(guildUser.GetGuildId())
	if guild == nil {
		l4g.Errorf("%d cant find guild %d", guildUser.GetData().Info.Id, guildUser.GetGuildId())
		return nil
	}
	teamsData := make([]*cl.GSTTeamData, 0)
	now := time.Now().Unix()
	for _, userID := range guild.GetData().Guild.UserIds {
		guildUser := m.GetGuildUserM().GetUser(userID)
		if guildUser == nil {
			continue
		}
		if now < guildUser.GetData().CanSignTime {
			continue
		}
		data := &cl.GSTTeamData{}
		data.UserInfo = &cl.GSTGuildUserBase{
			Id:              guildUser.GetData().Info.Id,
			Name:            guildUser.GetData().Info.Name,
			SeasonLv:        guildUser.GetData().Info.SeasonLv,
			BaseId:          guildUser.GetData().Info.BaseId,
			ExpireTime:      guildUser.GetData().Info.ExpireTime,
			Sid:             guildUser.GetData().Info.Sid,
			Score:           maps.Clone(guildUser.GetData().Info.Score),
			GuildId:         guildUser.GetData().Info.Sid,
			Wins:            guildUser.GetData().Info.Wins,
			SeasonLinkPower: guildUser.GetData().Info.SeasonLinkPower,
			RemainBookLevel: guildUser.GetData().Info.RemainBookLevel,
			TalentTreeLv:    guildUser.GetData().Info.TalentTreeLv,
			Title:           guildUser.GetData().Info.Title,
			SeasonAdd:       guildUser.GetData().Info.SeasonAdd,
		}
		for _, team := range guildUser.GetData().Teams {
			if team.AskHangUpTime != 0 && now-team.AskHangUpTime >= goxml.GetData().GuildSandTableConfigInfoM.HangUpTime {
				team.AskHangUpTime = 0
				team.HangUp = true
				guildUser.SetChange()
			}
			teamClone := team.Clone()
			for _, heroInfo := range teamClone.Heros {
				heroInfo.ActivedSeasonLink = guildUser.IsSeasonLinkActived(heroInfo.SysId)
			}
			data.TeamInfo = append(data.TeamInfo, teamClone)
			if reqType == 2 && team.ChangeManager { //只有管理的请求才需要
				team.ChangeManager = false
				guildUser.SetChange()
			}
		}
		teamsData = append(teamsData, data)
	}
	return teamsData
}

func (m *Manager) ExchangeGroundTeam(uid uint64, req *l2c.L2CS_GSTExchangeGroundTeam, rsp *l2c.CS2L_GSTExchangeGroundTeam) {
	guildUser := m.GetGuildUserM().GetUser(uid)
	if guildUser == nil {
		l4g.Errorf("cant find user %d", uid)
		rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
		return
	}
	rsp.LogGeneral = guildUser.BuildGstLogGeneral()
	guild := m.GetGuildM().GetGuild(guildUser.GetGuildId())
	if guild == nil {
		l4g.Errorf("cant find guild %d", uid)
		rsp.Ret = uint32(ret.RET_GST_GUILD_NIL)
		return
	}
	group := m.GetGroupM().GetGroup(guild.GetData().GroupId)
	if group == nil {
		l4g.Errorf("cant find group %d", uid)
		rsp.Ret = uint32(ret.RET_GST_GROUP_NIL)
		return
	}
	ground := group.GetGround(req.Id)
	if ground == nil {
		l4g.Errorf("%d cant find ground %d", uid, req.Id)
		rsp.Ret = uint32(ret.RET_GST_GROUND_NIL)
		return
	}
	ground.ExchangeTeam(guildUser.GetGuildId(), req, rsp)
}

//nolint:mnd
func (m *Manager) TeamOperate(uid uint64, req *l2c.L2CS_GSTTeamOperate, rsp *l2c.CS2L_GSTTeamOperate) map[uint64]struct{} {
	if m.GetStaM().GetSta().State.Stage != cl.GST_STAGE_RoundFightState_Operate {
		rsp.Ret = uint32(ret.RET_GST_USER_CANT_OPERATE)
		return nil
	}
	opUser := m.GetGuildUserM().GetUser(uid)
	if opUser == nil {
		l4g.Errorf("cant find user %d", uid)
		rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
		return nil
	}
	var taskUpdateUID map[uint64]struct{}
	switch req.Type {
	case 1: //移动队伍
		taskUpdateUID = make(map[uint64]struct{})
		m.MoveTeam(opUser, req, rsp, taskUpdateUID)
	case 2: //撤回队伍
		m.BackTeam(opUser, req, rsp)
	case 3: //托管队伍
		m.HangUpTeam(opUser, req, rsp)
	case 4: //取消托管
		m.CancelHangUpTeam(opUser, req, rsp)
	default:
		l4g.Errorf("type err %d", req.Type)
		rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		rsp.Type = req.Type
	}
	return taskUpdateUID
}

func (m *Manager) MoveTeam(opUser *GuildUser, req *l2c.L2CS_GSTTeamOperate, rsp *l2c.CS2L_GSTTeamOperate, taskUpdateUID map[uint64]struct{}) {
	opGuild := m.GetGuildM().GetGuild(opUser.GetGuildId())
	if opGuild == nil {
		l4g.Errorf("cant find guild %d", opUser.GetData().Info.Id)
		rsp.Ret = uint32(ret.RET_GST_GUILD_NIL)
		return
	}
	group := m.GetGroupM().GetGroup(opGuild.GetData().GroupId)
	if group == nil {
		l4g.Errorf("cant find group %d", opUser.GetData().Info.Id)
		rsp.Ret = uint32(ret.RET_GST_GROUP_NIL)
		return
	}
	rsp.GstDispatchTeams = make([]*log.GSTDispatchTeam, 0, len(req.Operates))
	for _, operate := range req.Operates {
		moveGroundID := group.GetMoveGroundID(opUser.GetGuildId(), operate.Ids)
		if moveGroundID == 0 {
			l4g.Errorf("%d cant move %+v", opUser.GetData().Info.Id, operate.Ids)
			rsp.Ret = uint32(ret.RET_GST_MOVE_LINE_ILLEGAL)
			return
		}
		successOperate := &cl.GSTTeamOperate{
			Ids: operate.Ids,
		}
		rsp.Operates = append(rsp.Operates, successOperate)
		for _, team := range operate.Teams {
			guildUser := m.GetGuildUserM().GetUser(team.User.Id)
			if guildUser == nil {
				l4g.Errorf("cant find user %d", opUser.GetData().Info.Id)
				rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
				return
			}
			if guildUser.GetGuildId() != opUser.GetGuildId() {
				l4g.Errorf("opUser %d not the same guild  user %d", opUser.GetData().Info.Id, team.User.Id)
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}
			result, operateTeam := guildUser.MoveTeam(opUser.GetData().Info.Id, team.TeamIndex, moveGroundID, group, rsp, taskUpdateUID)
			if result != ret.RET_OK {
				rsp.Ret = uint32(result)
				return
			}
			successOperate.Teams = append(successOperate.Teams, operateTeam)
		}
	}
	rsp.LogGeneral = opUser.BuildGstLogGeneral()
}

func (m *Manager) BackTeam(opUser *GuildUser, req *l2c.L2CS_GSTTeamOperate, rsp *l2c.CS2L_GSTTeamOperate) {
	for _, operate := range req.Operates {
		successOperate := &cl.GSTTeamOperate{
			Ids: operate.Ids,
		}
		rsp.Operates = append(rsp.Operates, successOperate)
		for _, team := range operate.Teams {
			guildUser := m.GetGuildUserM().GetUser(team.User.Id)
			if guildUser == nil {
				l4g.Errorf("cant find user %d", opUser.GetData().Info.Id)
				rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
				return
			}
			if guildUser.GetGuildId() != opUser.GetGuildId() {
				l4g.Errorf("opUser %d not the same guild  user %d", opUser.GetData().Info.Id, team.User.Id)
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}
			result, operateTeam := guildUser.BackTeam(opUser.GetData().Info.Id, team.TeamIndex, rsp)
			if result != ret.RET_OK {
				rsp.Ret = uint32(result)
				return
			}
			successOperate.Teams = append(successOperate.Teams, operateTeam)
		}
	}
}

func (m *Manager) HangUpTeam(opUser *GuildUser, req *l2c.L2CS_GSTTeamOperate, rsp *l2c.CS2L_GSTTeamOperate) {
	for _, operate := range req.Operates {
		successOperate := &cl.GSTTeamOperate{
			Ids: operate.Ids,
		}
		rsp.Operates = append(rsp.Operates, successOperate)
		for _, team := range operate.Teams {
			guildUser := m.GetGuildUserM().GetUser(team.User.Id)
			if guildUser == nil {
				l4g.Errorf("cant find user %d", opUser.GetData().Info.Id)
				rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
				return
			}
			if guildUser.GetGuildId() != opUser.GetGuildId() {
				l4g.Errorf("opUser %d not the same guild  user %d", opUser.GetData().Info.Id, team.User.Id)
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}
			result, operateTeam := guildUser.HangUpTeam(opUser.GetData().Info.Id, team.TeamIndex)
			if result != ret.RET_OK {
				rsp.Ret = uint32(result)
				return
			}
			if operateTeam != nil {
				successOperate.Teams = append(successOperate.Teams, operateTeam)
			}
		}
	}
	rsp.LogGeneral = opUser.BuildGstLogGeneral()
}

func (m *Manager) CancelHangUpTeam(opUser *GuildUser, req *l2c.L2CS_GSTTeamOperate, rsp *l2c.CS2L_GSTTeamOperate) {
	for _, operate := range req.Operates {
		successOperate := &cl.GSTTeamOperate{
			Ids: operate.Ids,
		}
		rsp.Operates = append(rsp.Operates, successOperate)
		for _, team := range operate.Teams {
			guildUser := m.GetGuildUserM().GetUser(team.User.Id)
			if guildUser == nil {
				l4g.Errorf("cant find user %d", opUser.GetData().Info.Id)
				rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
				return
			}
			if guildUser.GetGuildId() != opUser.GetGuildId() {
				l4g.Errorf("opUser %d not the same guild  user %d", opUser.GetData().Info.Id, team.User.Id)
				rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return
			}
			result, operateTeam := guildUser.CancelHangUpTeam(opUser.GetData().Info.Id, team.TeamIndex)
			if result != ret.RET_OK {
				rsp.Ret = uint32(result)
				return
			}
			successOperate.Teams = append(successOperate.Teams, operateTeam)
		}
	}
	rsp.LogGeneral = opUser.BuildGstLogGeneral()
}

//nolint:funlen,mnd
func (m *Manager) BuildTaskData(userID uint64) *cl.GSTTaskInfo {
	user := m.GetGuildUserM().GetUser(userID)
	if user == nil {
		l4g.Errorf("user:%d buildTaskData get Gst User failed", userID)
		return nil
	}
	guild := m.GetGuildM().GetGuild(user.GetGuildId())
	if guild == nil {
		l4g.Errorf("user:%d buildTaskData get guild:%d failed", userID, user.GetGuildId())
		return nil
	}

	group := m.GetGroupM().GetGroup(guild.GetGroupID())
	if group == nil {
		l4g.Errorf("user:%d buildTaskData get group:%d failed", userID, guild.GetGroupID())
		return nil
	}

	temp := &cl.GSTTaskInfo{
		TaskTypeProgress: make(map[uint32]*cl.TaskTypeProgress),
	}

	temp.TaskTypeProgress[2302001] = &cl.TaskTypeProgress{
		TaskTypeId: 2302001,
		Progress:   uint64(user.data.RecvHangOutCount),
	}

	temp.TaskTypeProgress[2303001] = &cl.TaskTypeProgress{
		TaskTypeId: 2303001,
		Progress:   uint64(user.data.TeamOperateCount),
	}

	temp.TaskTypeProgress[2304001] = &cl.TaskTypeProgress{
		TaskTypeId: 2304001,
		Progress:   user.GetUserOccupyGround(),
	}

	temp.TaskTypeProgress[2305001] = &cl.TaskTypeProgress{
		TaskTypeId: 2305001,
		Progress:   user.GetUserOccupyTypeGround(2),
	}

	temp.TaskTypeProgress[2305002] = &cl.TaskTypeProgress{
		TaskTypeId: 2305002,
		Progress:   user.GetUserOccupyTypeGround(4),
	}

	temp.TaskTypeProgress[2305003] = &cl.TaskTypeProgress{
		TaskTypeId: 2305003,
		Progress:   user.GetUserOccupyTypeGround(6),
	}

	temp.TaskTypeProgress[2305004] = &cl.TaskTypeProgress{
		TaskTypeId: 2305004,
		Progress:   user.GetUserOccupyTypeGround(7),
	}

	temp.TaskTypeProgress[2306001] = &cl.TaskTypeProgress{
		TaskTypeId: 2306001,
		Progress:   user.GetBattleCount(),
	}

	temp.TaskTypeProgress[2307001] = &cl.TaskTypeProgress{
		TaskTypeId: 2307001,
		Progress:   user.GetBattleWinCount(),
	}

	temp.TaskTypeProgress[2308001] = &cl.TaskTypeProgress{
		TaskTypeId: 2308001,
		Progress:   uint64(user.GetTotalScore()),
	}

	temp.TaskTypeProgress[2309001] = &cl.TaskTypeProgress{
		TaskTypeId: 2309001,
		Progress:   uint64(guild.GetMaxGround()),
	}

	temp.TaskTypeProgress[2310001] = &cl.TaskTypeProgress{
		TaskTypeId: 2310001,
		Progress:   uint64(guild.GetGuildScore()),
	}

	temp.TaskTypeProgress[2311001] = &cl.TaskTypeProgress{
		TaskTypeId: 2311001,
		Progress:   group.GetGroupGroundCount(),
	}

	temp.TaskTypeProgress[2312001] = &cl.TaskTypeProgress{
		TaskTypeId: 2312001,
		Progress:   group.GetTypeGroupGroundCount(uint32(4)),
	}

	temp.TaskTypeProgress[2313001] = &cl.TaskTypeProgress{
		TaskTypeId: 2313001,
		Progress:   user.GetSetTeam(),
	}

	temp.TaskTypeProgress[2314001] = &cl.TaskTypeProgress{
		TaskTypeId: 2314001,
		Progress:   user.GetDonateNum(),
	}

	temp.TaskTypeProgress[2315001] = &cl.TaskTypeProgress{
		TaskTypeId: 2315001,
		Progress:   user.GetPVEBattleCount(),
	}

	temp.TaskTypeProgress[2316001] = &cl.TaskTypeProgress{
		TaskTypeId: 2316001,
		Progress:   user.GetPVEBattleWinCount(),
	}

	return temp
}

func (m *Manager) L2CSGstGetCommonData(req *l2c.L2CS_GSTGetData, rsp *l2c.CS2L_GSTGetData) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	rsp.Sta = sta.Clone()
	user := m.GetGuildUserM().GetUser(req.User.Id)
	if user != nil {
		user.UpdateLoginTime()
		rsp.Signed = true
		rsp.BoxHaveReward = user.HaveBoxReward()
		guild := m.GetGuildM().GetGuild(user.GetGuildId())
		if guild != nil {
			rsp.ChatRoomId = guild.GetChatRoomID()
		}
	}
}

//nolint:funlen
func (m *Manager) L2CSGstGetComplexData(req *l2c.L2CS_GSTGetData, rsp *l2c.CS2L_GSTGetData) {
	user := m.GetGuildUserM().GetUser(req.User.Id)
	if user == nil {
		l4g.Errorf("cant get guildUser %d ", req.User.Id)
		rsp.Ret = uint32(ret.RET_GST_USER_NOT_SIGN)
		return
	}
	if user.GetGuildId() != req.User.GuildId {
		l4g.Errorf("%d guildID not the same %d : %d ", req.User.Id, user.GetGuildId(), req.User.GuildId)
		rsp.Ret = uint32(ret.RET_GST_GUILD_CHANGED)
		return
	}
	guild := m.GetGuildM().GetGuild(user.GetGuildId())
	if guild == nil {
		l4g.Errorf("cant get guild. User %d ", req.User.Id)
		rsp.Ret = uint32(ret.RET_GST_GUILD_NIL)
		return
	}
	rsp.Complex = &cl.GSTComplexData{
		LastUserLroundInfo: user.GetData().LastLroundSettleInfo.Clone(),
		//GuildTech:          guild.TechGetTechData().Clone(),
	}
	user.CheckSign()
	rsp.Complex.CanSignTime = user.GetData().CanSignTime
	if user.GetData().CanSignTime != 0 {
		return
	}
	guild.CheckCanAddMatch()
	rsp.Complex.GuildCanMatchTime = guild.GetData().GuildCanMatchTime
	if rsp.Complex.GuildCanMatchTime != 0 {
		return
	}
	dragonShowPos := guild.GetDragonShowPos()
	rsp.Complex.GuildMessage = guild.GetData().MessageBoard
	rsp.Complex.MessageBoardId = guild.GetData().MessageBoardId
	rsp.Complex.Builds = guild.BuildFlush2Logic()
	rsp.Complex.GuildInfo = &cl.GSTSimpleGuild{
		Name:         guild.GetData().Guild.Name,
		Badge:        guild.GetData().Guild.Badge,
		ExpireTime:   guild.GetData().Guild.ExpireTime,
		Division:     guild.GetData().Guild.Division,
		Partition:    guild.GetData().Guild.Partition,
		DragonPos:    dragonShowPos,
		DragonShowId: guild.GetDragonIdByPos(dragonShowPos, m.GetSeasonID()),
	}
	staM := m.GetStaM()
	sta := staM.GetSta().State
	if sta.LRound != 0 {
		if guild.GetData().LroundInfo != nil {
			rsp.Complex.LastRoundInfo = append(rsp.Complex.LastRoundInfo, guild.GetData().LroundInfo.Clone())
		}
		if guild.GetData().GroupId == 0 {
			if sta.Stage >= cl.GST_STAGE_RoundFightState_Fight &&
				sta.Stage <= cl.GST_STAGE_RoundFightState_Reward {
				rsp.Ret = uint32(ret.RET_GST_GROUP_NOT_MATCH)
				return
			}
			rsp.Ret = uint32(ret.RET_GST_GROUND_NIL)
			return
		} else {
			group := m.GetGroupM().GetGroup(guild.GetData().GroupId)
			if group == nil {
				l4g.Errorf("cant get group. User %d ", req.User.Id)
				rsp.Ret = uint32(ret.RET_GST_GROUP_NIL)
				return
			}
			rsp.Complex.GroupId = guild.GetData().GroupId
			rsp.Complex.LastLroundRank = m.GetGroupM().GetGroupRank(guild.GetGroupID())

			rsp.Complex.MapInfo = &cl.GSTMapInfo{
				MapConfigId:    group.GetData().MapInfo.MapConfigId,
				Quality:        group.GetData().RoomQuality,
				GSTGroundInfos: make(map[uint32]*cl.GSTGroundInfo),
			}
			for id, ground := range group.GetData().MapInfo.GSTGroundInfos {
				groundData := &cl.GSTGroundInfo{
					GuildId:           ground.GuildId,
					LandId:            ground.LandId,
					Fighting:          ground.Fighted,
					DragonSkillLround: ground.DragonSkillLround,
				}
				for _, guildTeam := range ground.FightTeams {
					if guildTeam.GuildId == user.GetGuildId() {
						groundData.FightNum = uint32(len(guildTeam.Teams))
						break
					}
				}
				for _, dragonSkillGuild := range ground.DragonSkillGuilds {
					groundData.DragonSkillGuilds = append(groundData.DragonSkillGuilds, dragonSkillGuild.GuildId)
				}
				rsp.Complex.MapInfo.GSTGroundInfos[id] = groundData
			}

			//cmsg.Complex.BossGroup = make(map[uint32]*cl.GSTBossGroup)
			rsp.Complex.MapInfo.GuildIds = append(rsp.Complex.MapInfo.GuildIds, group.GetData().MatchedGuild...)
			seasonId := m.GetSeasonID()
			for _, matchGuildID := range group.GetData().MatchedGuild {
				if matchGuildID == 0 {
					continue
				}
				matchGuild := m.GetGuildM().GetGuild(matchGuildID)
				if matchGuild == nil {
					continue
				}
				dragonShowPos := matchGuild.GetDragonShowPos()
				rsp.Complex.MapInfo.GuildsInfo = append(rsp.Complex.MapInfo.GuildsInfo, &cl.GSTSimpleGuild{
					GuildId:       matchGuild.GetData().Guild.GetGuildId(),
					Name:          matchGuild.GetData().Guild.Name,
					Badge:         matchGuild.GetData().Guild.Badge,
					ExpireTime:    matchGuild.GetData().Guild.ExpireTime,
					MainBaseLevel: matchGuild.GetMainBaseBuildLevel(),
					Division:      matchGuild.GetData().Guild.Division,
					Partition:     matchGuild.GetData().Guild.Partition,
					DragonPos:     dragonShowPos,
					DragonShowId:  matchGuild.GetDragonIdByPos(dragonShowPos, seasonId),
				})
				if matchGuild.GetData().LroundInfo != nil && matchGuildID != user.GetGuildId() {
					rsp.Complex.LastRoundInfo = append(rsp.Complex.LastRoundInfo, matchGuild.GetData().LroundInfo.Clone())
				}
				//cmsg.Complex.BossGroup[uint32(index)] = matchGuild.GetData().GetBossGroup().Clone()
			}

			for _, arenaState := range group.GetArenaStates() {
				rsp.Complex.ArenaStates = append(rsp.Complex.ArenaStates, &cl.GSTArenaState{
					IsOpen: arenaState.IsOpen,
					Round:  arenaState.Round,
					Lround: arenaState.Lround,
				})
			}
			//cmsg.Complex.BossAward = user.GetData().GetBossAward()
			//cmsg.Complex.MaxDamage = user.GetData().GetMaxDamage()
			dragonM := m.GetDragonM()
			dragonGuild := dragonM.GetDragonGuild(user.GetGuildId())
			if dragonGuild != nil {
				data := dragonGuild.GetData()
				if data != nil && data.DragonSkill != nil {
					rsp.Complex.DragonSkill = data.DragonSkill.Clone()
				}
			}
		}
	}
	rsp.Complex.GuildLeaderDonateFreeCost = !guild.CheckFreeDonateTime(1)
	rsp.Complex.MaxGround = guild.GetMaxGround()
	rsp.Complex.Top_3 = guild.GetUserTop3()

	// 龙养成
	build := guild.GetBuild(goxml.GstGuildBuildMain)
	if build != nil {
		round := m.GetStaM().GetSta().State.Round
		lround := m.GetStaM().GetSta().State.LRound
		rsp.Complex.DragonCultivation = &cl.GSTDragonCultivation{
			Evolution: guild.GetDragonCultivation(round, lround),
			Level:     build.BuildLevel,
		}
	}

	if rsp.Complex.DragonSkill == nil {
		rsp.Complex.DragonSkill = &cl.GSTDragonSkill{}
	}
	rsp.Complex.DragonSkill.SkillPower = uint32(guild.GetDragonSeason().DragonSkillPower)

	rsp.Complex.ChallengeFinish = user.IsMatchRoundEnd() // 擂台赛是否打完了
}

func (m *Manager) L2CSGstGetSimpleData(req *l2c.L2CS_GSTGetData, rsp *l2c.CS2L_GSTGetData) {
	staM := m.GetStaM()
	sta := staM.GetSta().State
	rsp.Sta = sta.Clone()
	rsp.Simple = &cl.GSTSimpleData{}
	user := m.GetGuildUserM().GetUser(req.User.Id)
	if user != nil {
		rsp.Simple.TeamNotMove = user.IsTeamNotMove()
		rsp.Simple.GstTaskInfo = m.BuildTaskData(req.User.Id)
		rsp.Simple.GstBuildTaskInfo = user.GetData().BuildTask.Clone()
		rsp.Simple.GstDragonTaskInfo = user.GetData().DragonTask.Clone()
		rsp.Simple.DragonLeftCount = user.DragonGetTotalCount()
		//rsp.Simple.GstTechTaskData = user.TechGetTaskData().Clone()
		rsp.Simple.ChallengeTaskData = user.ChallengeGetTaskData().Clone()
		guild := m.GetGuildM().GetGuild(user.GetGuildId())
		if guild != nil {
			if buildMain := guild.GetBuild(goxml.GstGuildBuildMain); buildMain != nil {
				rsp.Simple.BuildMainLevel = buildMain.BuildLevel
			}
			rsp.Simple.DragonShowId = guild.GetDragonIdByPos(guild.GetDragonShowPos(), m.GetSeasonID())
			group := m.GetGroupM().GetGroup(guild.GetGroupID())
			if group != nil {
				mapConfigId := group.GetData().MapInfo.MapConfigId
				mapGroupInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(mapConfigId)
				if mapGroupInfo != nil {
					rsp.Simple.TaskAwardGroup = mapGroupInfo.TaskAwardGroup
				}
			}
		}
	}
}

//func (m *Manager) AddGSTBossFightRecord(record *cl.GSTBossFightRecord, guildID uint64) {
//	gstGuild := m.GetGuildM().GetGuild(guildID)
//	if gstGuild == nil {
//		l4g.Errorf("AddGSTBossFightRecord get guildID:%d failed", guildID)
//		return
//	}
//	logInfo := &cl.GSTLogInfo{
//		LogType: cl.GSTLogType_LogType_GSTBossFight,
//		GroupId: gstGuild.GetGroupID(),
//		GuildId: guildID,
//		Round:   m.GetStaM().GetSta().State.Round,
//		LogInfo: &cl.GSTLogData{
//			BossFightRecord: record,
//		},
//	}
//	m.GetGuildLogM().AddLog(logInfo, true)
//}

func (m *Manager) AddGSTDragonFightRecord(record *cl.GSTDragonFightRecord, guildID uint64, dragonRound uint32) {
	logInfo := &cl.GSTLogInfo{
		LogType:  cl.GSTLogType_LogType_GSTDragonFight,
		SeasonId: m.GetSeasonID(),
		GuildId:  guildID,
		LRound:   dragonRound, // 复用公会战回合表示龙战场次
		LogInfo: &cl.GSTLogData{
			DragonFightRecord: record,
		},
	}
	m.GetGuildLogM().AddLog(logInfo, true)
}

func (m *Manager) InitGuildMobRankM() {
	m.guildMobRankM = NewGuildMobRankManager(m)
}

func (m *Manager) CheckChallengeSettlement() {
	sta := m.GetStaM().GetSta().State
	info := goxml.GetData().GuildSandTableChallengeInfoM.GetRecordByGSTRoundAndLRound(sta.Round, sta.LRound)
	if info == nil {
		l4g.Errorf("CheckChallengeReset: get challengeInfo failed. round:%d lRound:%d", sta.Round, sta.LRound)
		return
	}
	if sta.LRound != info.End {
		return
	}

	m.challengeSettlement(info)
}

func (m *Manager) challengeSettlement(info *goxml.GuildSandTableChallengeInfo) {
	sta := m.GetStaM().GetSta().State
	challengeBaks := make(map[uint64]*cr.GSTChallengeBak)
	// 是不是公会战当前轮次的最后一场擂台赛
	roundLastChallenge := sta.LRound == goxml.GetData().GuildSandTableChallengeInfoM.GetCurrentGSTRoundMaxEndLRound(sta.GetRound())
	for _, group := range m.GetGroupM().groups {
		group.challengeReset(info)
		if roundLastChallenge {
			m.generateChallengeBakData(group, challengeBaks)
		}
	}
	if roundLastChallenge {
		// 这里不用判断challengeBak是否没有数据
		m.GetCommonBakM().BackUpChallenge(challengeBaks)
	}
}

func (m *Manager) generateChallengeBakData(group *Group, challengeBaks map[uint64]*cr.GSTChallengeBak) {
	sta := m.GetStaM().GetSta().State
	userTotalScoreList, _ := group.getChallengeUserAndGuildRankList(true)
	slices.SortFunc(userTotalScoreList, sortChallengeUserScoreRank)
	for i, userRank := range userTotalScoreList {
		rank := uint32(i + 1)
		bakByServer := challengeBaks[userRank.GetUser().GetSid()]
		if bakByServer == nil {
			bakByServer = &cr.GSTChallengeBak{
				SeasonId: sta.SeasonId,
				Round:    sta.GetRound(),
				Lround:   sta.GetLRound(),
			}
			challengeBaks[userRank.GetUser().GetSid()] = bakByServer
		}
		if bakByServer.UserRank == nil {
			bakByServer.UserRank = make(map[uint32]*cl.MultipleUserIds)
		}
		userIds := bakByServer.UserRank[rank]
		if userIds == nil {
			userIds = &cl.MultipleUserIds{
				Ids: make([]uint64, 0, len(userTotalScoreList)),
			}
			bakByServer.UserRank[rank] = userIds
		}
		userIds.Ids = append(userIds.Ids, userRank.User.GetId())
	}
}
