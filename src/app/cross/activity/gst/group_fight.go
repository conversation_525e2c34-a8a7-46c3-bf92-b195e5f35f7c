package gst

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"container/heap"
	"math"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

func (m *GroupManager) FightMatch() {
	for _, group := range m.groups {
		group.FightMatch()
	}
}

func (m *GroupManager) InitSendFightNum(now, endTime int64) {
	var totalMaxFightNum int
	for _, fightingMap := range m.fightingCache {
		for _, ground := range fightingMap {
			totalMaxFightNum += ground.GetMaxFightTimes()
		}
	}
	l4g.Infof("%d公会战预计需要处理的战斗 %d", m.GetManager().baseModule.Partition(), totalMaxFightNum)
	//根据战区,开始时间做偏移
	m.startSendTime = now + int64(m.GetManager().baseModule.Partition()%10)*UpdateRate*10

	leftTime := endTime - m.startSendTime - int64(FightMaxReSendTimes)*FightNeedResendTime
	if leftTime <= 0 {
		m.sendFightNum = MaxSendFightNum
		return
	}
	rateTimes := leftTime / UpdateRate
	m.sendFightNum = int(math.Ceil(float64(totalMaxFightNum) / float64(rateTimes)))
	if m.sendFightNum < MinSendFightNum {
		l4g.Infof("%d修正公会战每秒需要处理的战斗%d %d", m.GetManager().baseModule.Partition(), m.sendFightNum, MinSendFightNum)
		m.sendFightNum = MinSendFightNum
	}
	if m.sendFightNum > MaxSendFightNum {
		l4g.Infof("%d修正公会战每秒需要处理的战斗%d %d", m.GetManager().baseModule.Partition(), m.sendFightNum, MaxSendFightNum)
		m.sendFightNum = MaxSendFightNum
	}
}

func (m *GroupManager) CheckFightMatch(now, endTime int64) bool {
	if len(m.fightingCache) == 0 {
		l4g.Infof("%d公会战结束战斗", m.GetManager().baseModule.Partition())
		return true
	}
	if m.sendFightNum == 0 {
		m.InitSendFightNum(now, endTime)
		l4g.Infof("%d公会战每帧需要处理的战斗 %d", m.GetManager().baseModule.Partition(), m.sendFightNum)
	}
	if now < m.startSendTime {
		return false
	}
	sendFightNum := m.sendFightNum
	if sendFightNum > len(m.waitSendFight) {
		sendFightNum = len(m.waitSendFight)
	}
	sendFight := m.waitSendFight[:sendFightNum]
	for _, sendGroud := range sendFight {
		sendGroud.UpdateMatchTime(now)
		m.SendFightToLogic(sendGroud)
	}
	l4g.Infof("%d公会战发出的战斗数量 %d", m.GetManager().baseModule.Partition(), len(sendFight))
	m.waitSendFight = m.waitSendFight[sendFightNum:]
	l4g.Infof("%d公会战剩余的战斗数量 %d", m.GetManager().baseModule.Partition(), len(m.waitSendFight))
	//检测超时的战斗
	for _, fightingMap := range m.fightingCache {
		for _, ground := range fightingMap {
			if ground.GetData().Fighting.MatchTime == 0 {
				continue
			}
			if now-ground.GetData().Fighting.MatchTime > FightNeedResendTime {
				if ground.GetFightSendTimes()-1 >= FightMaxReSendTimes {
					//异常情况下攻击方赢
					errorFight, err := json.Marshal(ground.GetData().Fighting)
					if err != nil {
						l4g.Errorf("marshal error %s", err.Error())
					} else {
						l4g.Errorf("[FATAL] partition %d gst fight resend too much %s",
							ground.GetManager().baseModule.Partition(), util.String(errorFight))
					}
					ground.GetData().Fighting.IsWin = true
					ground.UpdateFightResult(ground.GetData().Fighting)
				} else {
					ground.UpdateMatchTime(now)
					m.SendFightToLogic(ground)
					errorFight, err := json.Marshal(ground.GetData().Fighting)
					if err != nil {
						l4g.Errorf("marshal error %s", err.Error())
					} else {
						l4g.Errorf("[FATAL] partition %d gst fight resend %s",
							m.GetManager().baseModule.Partition(), util.String(errorFight))
					}
				}
			}
		}
	}
	return false
}

func (m *GroupManager) SendFightToLogic(ground IGround) {
	fight := ground.GetData().Fighting
	msg := &l2c.CS2L_GSTFight{
		Fight:   fight,
		SpaceId: fight.GroundId,
	}
	group := ground.GetGroup()
	if group != nil && group.data != nil {
		ground := m.groups[group.data.Id].grounds[fight.GroundId]
		if ground != nil {
			msg.SpaceType = ground.GetGroundType()
		}
	}

	gstUser := m.mgr.GetGuildUserM().GetUser(fight.Attack.User.Id)
	if gstUser != nil {
		msg.LogGeneral = gstUser.BuildGstLogGeneral()
	}
	m.mgr.baseModule.SendCmdToLogic(fight.Attack.User.Sid, 0, l2c.ID_MSG_CS2L_GSTFight, msg)
}

func (m *GroupManager) OnFinishFight() {
	//战斗回合结算
	for _, group := range m.groups {
		group.OnFinishFight()
	}
}

func (m *GroupManager) Match() {
	notMatchedGuild := m.mgr.GetGuildM().GetNotMatchedGuild()
	l4g.Debugf("公会战开始匹配.未匹配数量%d", notMatchedGuild.Len())
	var waitList NotMatchedGuildHeap
	for notMatchedGuild.Len() > 0 {
		guild := heap.Pop(&notMatchedGuild).(*Guild)
		roomQuality := m.getRoomQuality(guild.GetData().Guild.Division)
		if roomQuality == 0 {
			l4g.Errorf("guild %d cant find roomQuality. division:%d", guild.GetData().GetGuild().GuildId, guild.GetData().Guild.Division)
			continue
		}
		nowMatchGroup := m.getNotFullGroupOnMatch(roomQuality)
		if nowMatchGroup == nil {
			l4g.Errorf("guild %d cant find matchGroup. roomQuality:%d", guild.GetData().GetGuild().GuildId, roomQuality)
			continue
		}
		if roomQuality != nowMatchGroup.GetData().RoomQuality && len(waitList) != 0 {
			heap.Push(&notMatchedGuild, guild)
			//当待匹配公会的段位和房间段位不相符时,优先匹配完wait队列中的
			canMatchNum := goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum - nowMatchGroup.GetMatchedNum()
			guilds := make(NotMatchedGuildHeap, 0)
			for index, waitGuild := range waitList {
				if index < int(canMatchNum) {
					guilds = append(guilds, waitGuild)
				} else {
					heap.Push(&notMatchedGuild, waitGuild)
				}
			}
			nowMatchGroup.AddGuilds(guilds)
			waitList = nil
			continue
		}
		if nowMatchGroup.CheckGuildCanAddMatch(guild) {
			guilds := make(NotMatchedGuildHeap, 1)
			guilds[0] = guild
			nowMatchGroup.AddGuilds(guilds)
			if len(waitList) != 0 && nowMatchGroup.GetMatchedNum() == goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum {
				for _, waitGuild := range waitList {
					heap.Push(&notMatchedGuild, waitGuild)
				}
				waitList = nil
				continue
			}
		} else {
			waitList = append(waitList, guild)
		}
		if notMatchedGuild.Len() == 0 && len(waitList) != 0 {
			canMatchNum := goxml.GetData().GuildSandTableConfigInfoM.MapMatchNum - nowMatchGroup.GetMatchedNum()
			guilds := make(NotMatchedGuildHeap, 0)
			for index, waitGuild := range waitList {
				if index < int(canMatchNum) {
					guilds = append(guilds, waitGuild)
				} else {
					heap.Push(&notMatchedGuild, waitGuild)
				}
			}
			nowMatchGroup.AddGuilds(guilds)
			waitList = nil
		}
	}
}

func (m *GroupManager) MatchSingleGuild(guild *Guild) {
	roomQuality := m.getRoomQuality(guild.GetData().Guild.Division)
	if roomQuality == 0 {
		l4g.Errorf("guild %d cant find roomQuality. division:%d", guild.GetData().GetGuild().GuildId, guild.GetData().Guild.Division)
		return
	}
	matchGroup := m.getNotFullGroupOnHalfwayMatch(roomQuality)
	if matchGroup == nil {
		l4g.Errorf("guild %d cant find matchGroup. roomQuality:%d", guild.GetData().GetGuild().GuildId, roomQuality)
		return
	}
	//初始化boss
	// guild.updateBossLRound(matchGroup.GetData().MapInfo.MapConfigId)

	guilds := make(NotMatchedGuildHeap, 1)
	guilds[0] = guild
	matchGroup.AddGuilds(guilds)
}

func (m *GroupManager) getRoomQuality(division uint32) uint32 {
	divisionInfo := goxml.GetData().GuildDungeonDivisionInoM.Index(division)
	if divisionInfo == nil {
		l4g.Errorf("GuildDungeonDivisionInoM cant find division:%d", division)
		return 0
	}
	return divisionInfo.GuildSandTableRoom
}

func (m *GroupManager) addFightingGround(groupID uint64, id uint32, ground IGround) {
	groundFightingMap := m.fightingCache[groupID]
	if groundFightingMap == nil {
		groundFightingMap = make(map[uint32]IGround)
		m.fightingCache[groupID] = groundFightingMap
	}
	groundFightingMap[id] = ground
	m.waitSendFight = append(m.waitSendFight, ground)
}

func (m *GroupManager) delFightingGround(groupID uint64, id uint32, _ IGround) {
	groundFightingMap := m.fightingCache[groupID]
	if groundFightingMap != nil {
		delete(groundFightingMap, id)
		if len(groundFightingMap) == 0 {
			delete(m.fightingCache, groupID)
		}
	}
}

func (m *GroupManager) getNotFullGroupOnHalfwayMatch(roomQuality uint32) *Group {
	for i := roomQuality; i >= LowestRoomQuality; i-- {
		groups := m.notFullGroupCache[i]
		if len(groups) > 0 {
			return groups[0]
		}
	}
	newGroup := m.newMatchGroup(LowestRoomQuality)
	if newGroup == nil {
		return nil
	}
	newGroup.updateArenaState()
	return newGroup
}

func (m *GroupManager) getNotFullGroupOnMatch(roomQuality uint32) *Group {
	for _, groups := range m.notFullGroupCache {
		if len(groups) == 0 {
			continue
		}
		return groups[0]
	}
	return m.newMatchGroup(roomQuality)
}

func (m *GroupManager) delNotFullGroup(group *Group) {
	for index, groupCache := range m.notFullGroupCache[group.GetData().RoomQuality] {
		if groupCache.GetData().Id == group.GetData().Id {
			m.notFullGroupCache[group.GetData().RoomQuality] =
				append(m.notFullGroupCache[group.GetData().RoomQuality][:index],
					m.notFullGroupCache[group.GetData().RoomQuality][index+1:]...)
			break
		}
	}
}

func (m *GroupManager) newMatchGroup(roomQuality uint32) *Group {
	if !m.checkRoom(roomQuality) {
		l4g.Errorf("GuildSandTableMapGroupInfo cant find roomQuality:%d", roomQuality)
		return nil
	}
	if m.minGroupID == 0 {
		m.minGroupID = uint64(m.GetManager().baseModule.Partition())<<48 |
			uint64(m.GetManager().GetStaM().GetSta().State.Round)<<32 | 1
	} else {
		m.minGroupID++
	}
	groupInfo := &cr.GSTGroupInfo{
		Id:          m.minGroupID,
		RoomQuality: roomQuality,
	}
	group := m.addGroup(groupInfo)
	group.initGroup()
	m.SetChange(group)
	return group
}

func (m *GroupManager) checkRoom(roomQuality uint32) bool {
	sta := m.GetManager().GetStaM().GetSta().State
	mapGroup := goxml.GetData().GuildSandTableInfoM.GetMapGroup(sta.SeasonId, sta.Round)
	if mapGroup == 0 {
		l4g.Errorf("GuildSandTableInfo cant find. seasonID:%d round:%d", sta.SeasonId, sta.Round)
		return false
	}
	mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.GetMapInfo(mapGroup, roomQuality)
	if mapInfo == nil {
		l4g.Errorf("GuildSandTableMapGroupInfo cant find. mapGroup:%d quality:%d", mapGroup, roomQuality)
		return false
	}
	return true
}
