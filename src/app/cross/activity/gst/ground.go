package gst

import (
	"app/cross/dclog"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"cmp"
	"fmt"
	"slices"
	"strconv"

	l4g "github.com/ivanabc/log4go"
)

type IGround interface {
	SetChange()
	GetData() *cr.GSTGroundInfo
	GetGroup() *Group
	Fight(*GuildUser)
	UpdateFightResult(*cl.GSTFightResult)
	GetManager() *Manager
	GetGroundNum() uint32
	NextStage()
	GetControlGuildID() uint64
	SetControlGuildID(uint64)
	GetOnceScore() uint32 //占领的一次性积分
	HaveFirstItem() bool
	GetSettleScore() uint32 //结算时增加的积分
	GetID() uint32
	OnResetLRound()
	OnGuildUserTeamQuit(uint64, uint64, uint32)
	GetGSTTeamData(uint64) *cl.GSTGroundTeamData
	ExchangeTeam(uint64, *l2c.L2CS_GSTExchangeGroundTeam, *l2c.CS2L_GSTExchangeGroundTeam)
	GetGroundType() uint32
	CheckMove() bool //屏障在初始化之后,需要判断能不能解锁为新地块
	MoveTeam(uint64, uint64, uint32) ret.RET
	UpdateMatchTime(int64)
	UpdateControlGuildID(*GuildUser, *GuildUser, *cl.GSTFightResult)
	GetMaxFightTimes() int
	GetFightSendTimes() uint32
	SetFirstOccupyGuildName(string)
	addMatchScore()
	getUserFightTeam(*cr.GSTGuildFightTeam) *cl.GSTFightTeam
	getPveFightTeam() *cl.GSTFightTeam
	GetNewFightID() uint32
	getFightResultTeam(*cl.GSTFightTeam) *cl.GSTFightTeam
	addFighing(*cl.GSTFightResult)
	isFighting() bool
	addResultUserInfo(*GuildUser, *GuildUser, *cl.GSTFightResult)
	updateFightUser(*GuildUser, *GuildUser, *cl.GSTFightResult, bool)
	updateFightTeams(*GuildUser, *cl.GSTFightTeam, bool, bool)
	updateFightLog(*GuildUser, *GuildUser, *cl.GSTFightResult)
	updateFightGround(*GuildUser, *GuildUser, *cl.GSTFightResult)
	delFighing()
	OnFinishFight(map[uint64]*guildBless)
	GetGSTGroundData(*l2c.CS2L_GSTGetGroundData, *GuildUser)
	AddDragonSkill(*cr.GSTDragonSkillGuild, uint32)
	checkPushWinsToChat()
}

func newGround(group *Group, id uint32, data *cr.GSTGroundInfo) IGround {
	groundInfo := goxml.GetData().GuildSandTableLandInfoM.Index(data.LandId)
	if groundInfo == nil {
		l4g.Errorf("GuildSandTableLandInfoM cant find:%d", data.LandId)
		return nil
	}
	groundBase := &GroundBase{
		group: group,
		id:    id,
		data:  data,
	}
	switch groundInfo.LandType {
	case goxml.GuildSandTableLandTypeMainBase:
		ground := &MainBaseGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeEmpty:
		ground := &EmptyGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeFence:
		ground := &FenceGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeStronghold:
		ground := &StrongholdGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeBarrier:
		ground := &BarrierGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeMonster:
		ground := &MonsterGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeSenior:
		ground := &SeniorGround{
			IGround: groundBase,
		}
		return ground
	case goxml.GuildSandTableLandTypeArena:
		ground := &ArenaGround{
			IGround: groundBase,
		}
		return ground
	default:
		l4g.Errorf("LandType err:%d", data.LandId)
		return nil
	}
}

type GroundBase struct {
	group          *Group
	id             uint32
	data           *cr.GSTGroundInfo
	fightSendTimes uint32
}

func (g *GroundBase) CheckMove() bool {
	return true
}

func (g *GroundBase) SetChange() {
	g.group.SetChange()
}

func (g *GroundBase) GetManager() *Manager {
	return g.group.GetManager()
}

func (g *GroundBase) GetData() *cr.GSTGroundInfo {
	return g.data
}

func (g *GroundBase) GetID() uint32 {
	return g.id
}

func (g *GroundBase) GetGroup() *Group {
	return g.group
}

func (g *GroundBase) GetControlGuildID() uint64 {
	return g.GetData().GuildId
}

func (g *GroundBase) SetControlGuildID(newGuildID uint64) {
	g.GetData().GuildId = newGuildID
	if g.GetData().MonsterInfo != nil {
		g.GetData().MonsterInfo = &cl.GSTFightTeam{}
	}
	g.GetData().TempControlGuildId = 0
	g.SetChange()
}

func (g *GroundBase) SetFirstOccupyGuildName(name string) {
	g.GetData().FirstOccupyGuildName = name
	g.SetChange()
}

func (g *GroundBase) GetGroundNum() uint32 {
	groundInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(g.GetID())
	if groundInfo != nil {
		return uint32(1 + len(groundInfo.SlaveIDs))
	}
	return 0
}

func (g *GroundBase) addMatchScore() {
	if g.GetData().SendMatchScore {
		return
	}
	for _, fightTeams := range g.GetData().FightTeams {
		for _, team := range fightTeams.Teams {
			user := g.GetManager().GetGuildUserM().GetUser(team.User.Id)
			addScore := goxml.GetData().GuildSandTableConfigInfoM.TeamMatchScore
			user.AddScore(cl.GSTUserScoreType_USTYPE_SEND_TEAM, addScore)
			user.InitTeamFightInfo(team.TeamIndex, g.GetID(), g.GetData().LandId, addScore)
			logInfo := &cl.GSTLogInfo{
				LogType: cl.GSTLogType_LogType_FightTeam,
				LRound:  g.GetManager().GetStaM().GetSta().State.LRound,
				GroupId: g.GetGroup().GetData().GetId(),
			}
			logData := &cl.GSTLogFightTeam{
				FightTeam: team.Clone(),
				GroundId:  g.GetID(),
			}
			logData.FightTeam.Score = addScore
			logInfo.LogInfo = &cl.GSTLogData{
				FightTeam: logData,
			}
			g.GetManager().GetGuildLogM().AddLog(logInfo, true)
		}
	}
	g.GetData().SendMatchScore = true
	g.SetChange()
}

func (g *GroundBase) Fight(winUser *GuildUser) {
	if g.isFighting() {
		return
	}
	if len(g.GetData().FightTeams) == 0 { //地块上没有玩家队伍,不需要战斗
		return
	}
	if !g.GetData().SendMatchScore {
		//第一次公会打乱顺序
		g.GetManager().rd.Shuffle(len(g.GetData().FightTeams), func(i, j int) {
			g.GetData().FightTeams[i], g.GetData().FightTeams[j] = g.GetData().FightTeams[j], g.GetData().FightTeams[i]
		})
	}
	g.addMatchScore()
	if len(g.GetData().FightTeams) == 1 &&
		g.GetData().FightTeams[0].GuildId == g.GetControlGuildID() { //只有一个公会驻扎,如果这个公会和地块的拥有者一样,不需要战斗
		return
	}

	fightingTeams := make([]*cl.GSTFightTeam, 0)
	var hadGuildID uint64
	if winUser != nil {
		//赢的公会要出一队
		for _, fightTeams := range g.GetData().FightTeams {
			if fightTeams.GuildId == winUser.GetGuildId() {
				fightTeam := g.getUserFightTeam(fightTeams)
				if fightTeam != nil {
					fightingTeams = append(fightingTeams, fightTeam)
					hadGuildID = fightTeams.GuildId
				}
				break
			}
		}
	}
	guildsNum := len(g.GetData().FightTeams)
	for i := 0; i < guildsNum; i++ { //pvp对战
		fightTeams := g.GetData().FightTeams[g.GetData().FightIndex]
		if hadGuildID == 0 || fightTeams.GuildId != hadGuildID {
			fightTeam := g.getUserFightTeam(fightTeams)
			if fightTeam != nil {
				fightingTeams = append(fightingTeams, fightTeam)
			}
		}
		g.GetData().FightIndex = (g.GetData().FightIndex + 1) % uint32(guildsNum)
		if len(fightingTeams) == 2 { //nolint:mnd
			if g.GetManager().rd.SelectByPercent(50) { //nolint:mnd
				fightingTeams[0], fightingTeams[1] = fightingTeams[1], fightingTeams[0]
			}
			break
		}
	}

	if len(fightingTeams) == 0 { //没有可战斗的队伍了,地块的控制权无法争夺
		return
	}
	if len(fightingTeams) == 1 { //进行pve战斗
		guildUser := g.GetManager().GetGuildUserM().GetUser(fightingTeams[0].User.Id)
		if guildUser.GetGuildId() == g.GetControlGuildID() {
			return
		}
		pveTeam := g.getPveFightTeam()
		if pveTeam == nil {
			return
		}
		fightingTeams = append(fightingTeams, pveTeam)
	}
	fightResult := &cl.GSTFightResult{
		GroundId: g.GetID(),
		FightId:  g.GetNewFightID(),
	}
	fightResult.Attack = g.getFightResultTeam(fightingTeams[0])
	fightResult.Defense = g.getFightResultTeam(fightingTeams[1])
	g.addFighing(fightResult)
}

func (g *GroundBase) GetFightSendTimes() uint32 {
	return g.fightSendTimes
}

func (g *GroundBase) UpdateMatchTime(time int64) {
	g.GetData().Fighting.MatchTime = time
	g.fightSendTimes++
	g.SetChange()
}

func (g *GroundBase) isFighting() bool {
	return g.GetData().Fighting != nil
}

func (g *GroundBase) getFightResultTeam(fightTeam *cl.GSTFightTeam) *cl.GSTFightTeam {
	fightResultTeam := &cl.GSTFightTeam{}
	if fightTeam.MonsterId != 0 { //怪物
		fightResultTeam.MonsterId = fightTeam.MonsterId
		fightResultTeam.MonsterTeamIndex = fightTeam.MonsterTeamIndex
		fightResultTeam.HpPct = fightTeam.HpPct
	} else { //真人
		user := g.GetManager().GetGuildUserM().GetUser(fightTeam.User.Id)
		if user == nil {
			return nil
		}
		guild := g.GetManager().GetGuildM().GetGuild(user.GetGuildId())
		if guild == nil {
			return nil
		}
		fightResultTeam.User = &cl.GSTGuildUserBase{
			Id:  user.GetData().Info.Id,
			Sid: user.GetData().Info.Sid,
		}
		// passives := guild.TechGetPassive(goxml.GuildTechAddTypeGlobalPassive)
		// if len(passives) != 0 {
		// 	fightResultTeam.AltPassives = make(map[uint32]*cl.AltPassives)
		// 	fightResultTeam.AltPassives[battle.TeamUniterBattlePos] = &cl.AltPassives{
		// 		Passives: passives,
		// 	}
		// }
		fightResultTeam.TeamIndex = fightTeam.TeamIndex
		fightResultTeam.Wins = fightTeam.Wins
		fightResultTeam.FatigueValue = fightTeam.FatigueValue
		fightResultTeam.HpPct = fightTeam.HpPct
		fightResultTeam.Morale = g.getFightMorale(user.GetGuildId())
		var groundID uint32
		ground, exist := g.group.mainBases[user.GetData().Info.GuildId]
		if exist {
			groundID = ground.GetID()
		}
		fightResultTeam.SpaceId = groundID
		l4g.Debugf("公会战,战斗场次(%d),地块(%d): 公会(%d)名字(%s)队伍(%d)",
			g.GetData().FightId, g.GetID(), user.GetGuildId(), user.GetData().Info.Name, fightTeam.TeamIndex)
	}
	return fightResultTeam
}

func (g *GroundBase) getFightMorale(guildID uint64) uint32 {
	distace := g.getDistanceFromMainBase(guildID)
	seasonID := g.GetManager().GetStaM().GetSta().State.SeasonId
	moraleConfig := goxml.GetData().GuildSandTableMoraleInfoM.GetRecordBySeasonIdDistanceMaxLe(seasonID, uint32(distace))
	if moraleConfig == nil {
		return 0
	}
	guild := g.GetManager().GetGuildM().GetGuild(guildID)
	if guild == nil {
		return 0
	}
	totalMorale := int32(moraleConfig.Morale)
	//bossMorale := guild.GetBossGroup().GetBossMorale()
	//totalMorale = totalMorale + bossMorale
	if totalMorale < 0 {
		totalMorale = 0
	}
	return uint32(totalMorale)
}

func (g *GroundBase) getDistanceFromMainBase(guildID uint64) int {
	mainBase := g.GetGroup().GetGuildMainBase(guildID)
	if mainBase == nil {
		return 0
	}
	mainBaseGroundInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(mainBase.GetID())
	if mainBaseGroundInfo == nil {
		l4g.Errorf("GuildSandTableMapInfo cant find ground %d", mainBase.GetID())
		return 0
	}
	groundInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(g.GetID())
	if groundInfo == nil {
		l4g.Errorf("GuildSandTableMapInfo cant find ground %d", g.GetID())
		return 0
	}
	fromHex := NewHex(int(mainBaseGroundInfo.X), int(mainBaseGroundInfo.Y))
	toHex := NewHex(int(groundInfo.X), int(groundInfo.Y))
	return HexDistance(fromHex, toHex)
}

func (g *GroundBase) getUserFightTeam(teams *cr.GSTGuildFightTeam) *cl.GSTFightTeam {
	if int(teams.MatchIndex) < len(teams.Teams) {
		team := teams.Teams[teams.MatchIndex].Clone()
		return team
	}
	return nil
}

func (g *GroundBase) getPveFightTeam() *cl.GSTFightTeam {
	if g.GetData().MonsterInfo == nil {
		return nil
	}
	groundExt := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundExt(
		g.GetID(), g.GetData().LandId)
	if groundExt == nil {
		return nil
	}
	if groundExt.MonsterSort == 0 {
		return nil
	}
	monsters := goxml.GetData().GuildSandTableMonsterGroupInfoM.GetMonstersByMonsterSort(groundExt.MonsterSort)
	if monsters == nil {
		l4g.Errorf("GuildSandTableMonsterGroupInfo cant find monstersort %d", groundExt.MonsterSort)
		return nil
	}
	totalMonsters := uint32(len(monsters)) + g.GetData().MonsterInfo.MonsterTeamIndexExt*goxml.GetData().GuildSandTableConfigInfoM.AssembleAddPerNum
	if g.GetData().MonsterInfo.MonsterTeamIndex >= totalMonsters {
		l4g.Errorf(" %d more than monsters len %d", g.GetData().MonsterInfo.MonsterTeamIndex, totalMonsters)
		return nil
	}
	var monsterIndex uint32
	if g.GetData().MonsterInfo.MonsterTeamIndex < uint32(len(monsters)) {
		monsterIndex = g.GetData().MonsterInfo.MonsterTeamIndex
	} else {
		monsterIndex = g.GetData().MonsterInfo.MonsterTeamIndex % uint32(len(monsters))
	}
	monsterId := monsters[monsterIndex].Id
	fightTeam := &cl.GSTFightTeam{
		MonsterId:        monsterId,
		MonsterTeamIndex: g.GetData().MonsterInfo.MonsterTeamIndex,
		HpPct:            make(map[uint32]int64),
	}
	for pos, hpPct := range g.GetData().MonsterInfo.HpPct {
		fightTeam.HpPct[pos] = hpPct
	}
	g.SetChange()
	return fightTeam
}

func (g *GroundBase) UpdateFightResult(fightResult *cl.GSTFightResult) {
	if !g.isFighting() {
		return
	}
	if g.GetData().Fighting.FightId != fightResult.FightId {
		l4g.Errorf("gst not the same fight %d:%d", g.GetData().Fighting.FightId, fightResult.FightId)
		return
	}
	guildUserM := g.GetManager().GetGuildUserM()
	var attack, defense *GuildUser
	attack = guildUserM.GetUser(fightResult.Attack.GetUser().Id)
	if attack == nil {
		l4g.Errorf("UpdateFightResult cant find user:%d", fightResult.Attack.GetUser().Id)
		return
	}
	isPVP := fightResult.Defense.MonsterId == 0
	if isPVP {
		defense = guildUserM.GetUser(fightResult.Defense.GetUser().Id)
		if defense == nil {
			l4g.Errorf("UpdateFightResult cant find user:%d", fightResult.Defense.GetUser().Id)
			return
		}
	}
	if fightResult.IsWin {
		if !isPVP {
			fightResult.Attack.Score = goxml.GetData().GuildSandTableConfigInfoM.PVEWinScore
		} else {
			fightResult.Attack.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPWinScore
			fightResult.Defense.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPLoseScore
		}
	} else {
		if !isPVP {
			fightResult.Attack.Score = goxml.GetData().GuildSandTableConfigInfoM.PVELoseScore
		} else {
			fightResult.Attack.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPLoseScore
			fightResult.Defense.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPWinScore
		}
	}
	g.addResultUserInfo(attack, defense, fightResult)
	g.updateFightUser(attack, defense, fightResult, isPVP)
	g.updateFightTeams(attack, fightResult.Attack, fightResult.IsWin, isPVP)
	g.updateFightTeams(defense, fightResult.Defense, !fightResult.IsWin, isPVP)
	g.updateFightLog(attack, defense, fightResult)
	g.updateFightGround(attack, defense, fightResult)
}

func (g *GroundBase) UpdateControlGuildID(attack, defense *GuildUser, fightResult *cl.GSTFightResult) {
	groundExt := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundExt(
		g.GetID(), g.GetData().LandId)
	if groundExt == nil {
		return
	}
	if groundExt.MonsterSort != 0 {
		//有pve
		if defense != nil {
			//打的是真人
			return
		}
		if !fightResult.IsWin {
			return
		}
		if g.GetData().MonsterInfo.MonsterTeamIndex < groundExt.MonsterNum+g.GetData().MonsterInfo.MonsterTeamIndexExt*goxml.GetData().GuildSandTableConfigInfoM.AssembleAddPerNum {
			//没有打完pve,不能占领
			return
		}
		g.GetData().TempControlGuildId = attack.GetGuildId()
	} else {
		//没有pve
		if fightResult.IsWin {
			g.GetData().TempControlGuildId = attack.GetGuildId()
		} else {
			g.GetData().TempControlGuildId = defense.GetGuildId()
		}
	}
	g.SetChange()
}

func (g *GroundBase) addFighing(fighting *cl.GSTFightResult) {
	g.GetData().Fighting = fighting
	g.GetData().Fighted = true
	g.GetManager().GetGroupM().addFightingGround(g.GetGroup().GetData().Id, g.GetID(), g)
	g.SetChange()
}

func (g *GroundBase) delFighing() {
	g.GetData().Fighting = nil
	g.fightSendTimes = 0
	g.GetManager().GetGroupM().delFightingGround(g.GetGroup().GetData().Id, g.GetID(), g)
	g.SetChange()
}

func (g *GroundBase) addResultUserInfo(attack, defense *GuildUser, fightResult *cl.GSTFightResult) {
	g.addResultGuildUserBase(fightResult.Attack.User, attack)
	g.addResultGuildUserBase(fightResult.Defense.User, defense)
}

func (g *GroundBase) addResultGuildUserBase(guildUser *cl.GSTGuildUserBase, user *GuildUser) {
	if user == nil {
		return
	}
	guildUser.Name = user.GetData().Info.Name
	guildUser.SeasonLv = user.GetData().Info.SeasonLv
	guildUser.BaseId = user.GetData().Info.BaseId
	guildUser.ExpireTime = user.GetData().Info.ExpireTime
}

func (g *GroundBase) updateFightUser(attack, defense *GuildUser, fightResult *cl.GSTFightResult, isPvP bool) {
	g.addScore(attack, fightResult.Attack.Score, isPvP)
	g.addScore(defense, fightResult.Defense.Score, isPvP)
	if fightResult.IsWin {
		if attack != nil {
			attack.AddWin()
			if defense != nil {
				attack.AddBattleCount()
				attack.AddBattleWinCount()
				defense.AddBattleCount()
			} else {
				attack.AddBattlePveCount()
				attack.AddBattlePveWinCount()
			}
		}
	} else {
		//攻击方一定为玩家
		if defense != nil {
			defense.AddWin()
			defense.AddBattleCount()
			defense.AddBattleWinCount()
			attack.AddBattleCount()
		} else {
			attack.AddBattlePveCount()
		}
	}
}

func (g *GroundBase) addScore(user *GuildUser, score uint32, isPVP bool) {
	if user == nil {
		return
	}
	if isPVP {
		user.AddScore(cl.GSTUserScoreType_USTYPE_PVP_FIGHT, score)
	} else {
		user.AddScore(cl.GSTUserScoreType_USTYPE_PVE_FIGHT, score)
	}
}

func (g *GroundBase) updateFightLog(attack, defense *GuildUser, fightResult *cl.GSTFightResult) {
	logInfo := &cl.GSTLogInfo{
		LogType:  cl.GSTLogType_LogType_FightResult,
		LRound:   g.GetManager().GetStaM().GetSta().State.LRound,
		GroundId: fightResult.GroundId,
		GroupId:  g.GetGroup().GetData().GetId(),
	}
	logInfo.LogInfo = &cl.GSTLogData{}
	logFightResult := &cl.GSTFightResult{
		IsWin:     fightResult.IsWin,
		ReportId:  fightResult.ReportId,
		MatchTime: fightResult.MatchTime,
		GroundId:  fightResult.GroundId,
		FightId:   fightResult.FightId,
	}
	logFightResult.AttackGuildName = g.getFightUserGuildName(attack)
	logFightResult.DefenseGuildName = g.getFightUserGuildName(defense)
	logFightResult.Attack = g.getLogFightTeam(fightResult.Attack, attack)
	logFightResult.Defense = g.getLogFightTeam(fightResult.Defense, defense)
	if fightResult.IsWin {
		logFightResult.Win = g.getUserTeamWinTimes(attack, fightResult.Attack)
	} else {
		logFightResult.Win = g.getUserTeamWinTimes(defense, fightResult.Defense)
	}

	logInfo.LogInfo.FightResult = logFightResult
	g.GetManager().GetGuildLogM().AddLog(logInfo, true)
}

func (g *GroundBase) getFightUserGuildName(user *GuildUser) string {
	if user != nil {
		guildID := user.GetGuildId()
		guild := g.GetManager().GetGuildM().GetGuild(guildID)
		if guild != nil {
			return guild.GetData().GetGuild().Name
		}
	}
	return ""
}

func (g *GroundBase) getLogFightTeam(fightTeam *cl.GSTFightTeam, user *GuildUser) *cl.GSTFightTeam {
	logFightTeam := &cl.GSTFightTeam{
		TeamIndex:        fightTeam.TeamIndex,
		Score:            fightTeam.Score,
		MonsterId:        fightTeam.MonsterId,
		MonsterTeamIndex: fightTeam.MonsterTeamIndex,
		Power:            fightTeam.Power,
		Morale:           fightTeam.Morale,
		SpaceId:          fightTeam.SpaceId,
	}
	if user != nil {
		logFightTeam.User = &cl.GSTGuildUserBase{
			Id:         user.GetData().Info.Id,
			Name:       user.GetData().Info.Name,
			SeasonLv:   user.GetData().Info.SeasonLv,
			Sid:        user.GetData().Info.Sid,
			BaseId:     user.GetData().Info.BaseId,
			ExpireTime: user.GetData().Info.ExpireTime,
			GuildId:    user.GetData().Info.GuildId,
		}
		fightTeam := user.GetFightTeam(fightTeam.TeamIndex)
		if fightTeam != nil {
			for _, hero := range fightTeam.Heros {
				logFightTeam.Heros = append(logFightTeam.Heros, hero.Clone())
			}
		}
	}
	return logFightTeam
}

// 战斗后更新地块状态
func (g *GroundBase) updateFightGround(attack, defense *GuildUser, fightResult *cl.GSTFightResult) {
	g.delFighing()
	//能不能继续战斗
	var winUser *GuildUser
	if fightResult.IsWin {
		winUser = attack
	} else if defense != nil {
		winUser = defense
	}
	g.Fight(winUser)
	if !g.isFighting() {
		logInfo := &cl.GSTLogInfo{
			LogType:  cl.GSTLogType_LogType_GroupSettle,
			LRound:   g.GetManager().GetStaM().GetSta().State.LRound,
			GroundId: fightResult.GroundId,
			GroupId:  g.GetGroup().GetData().GetId(),
		}
		logInfo.LogInfo = &cl.GSTLogData{}
		logGroupSettle := &cl.GSTLogGroupSettle{
			BeforeLandId: g.GetData().GetLandId(),
		}
		logInfo.LogInfo.GroupSettle = logGroupSettle
		if g.GetControlGuildID() != 0 {
			beforeGuild := g.GetManager().GetGuildM().GetGuild(g.GetControlGuildID())
			if beforeGuild != nil {
				logGroupSettle.BeforeControlGuild = beforeGuild.GetData().GetGuild().Name
				logGroupSettle.BeforeGuildId = beforeGuild.GetData().Guild.GuildId
				logGroupSettle.BeforeGuildPartition = beforeGuild.GetData().Guild.Partition
			}
		}

		//没有战斗了,更新占领的公会id
		g.UpdateControlGuildID(attack, defense, fightResult)

		controlGuildFightCount := uint32(0)
		for _, guildFight := range g.GetData().FightTeams {
			guild := g.GetManager().GetGuildM().GetGuild(guildFight.GuildId)
			if guild != nil {
				guildInfo := &cl.GSTLogGroupGuildSettle{
					GuildId:        guild.GetData().Guild.GuildId,
					GuildName:      guild.GetData().Guild.Name,
					TeamNum:        uint32(len(guildFight.Teams)),
					Win:            guildFight.Win,
					Lose:           guildFight.Lose,
					GuildPartition: guild.GetData().Guild.Partition,
				}
				logGroupSettle.GuildInfo = append(logGroupSettle.GuildInfo, guildInfo)
				if g.GetData().TempControlGuildId == guildInfo.GuildId {
					controlGuildFightCount += guildFight.Win + guildFight.Lose
				}
			}
		}

		if g.GetData().TempControlGuildId != 0 {
			afterGuild := g.GetManager().GetGuildM().GetGuild(g.GetData().TempControlGuildId)
			if afterGuild != nil {
				logGroupSettle.AfterControlGuild = afterGuild.GetData().GetGuild().Name
				logGroupSettle.AfterGuildId = afterGuild.GetData().Guild.GuildId
				logGroupSettle.AfterGuildPartition = afterGuild.GetData().Guild.Partition
			}
			g.handlePushMessage(logGroupSettle, controlGuildFightCount)
		}

		g.GetManager().GetGuildLogM().AddLog(logInfo, true)
	}
}

func (g *GroundBase) handlePushMessage(logGroupSettle *cl.GSTLogGroupSettle, fightCount uint32) {
	if logGroupSettle.BeforeGuildId == logGroupSettle.AfterGuildId {
		return
	}
	landInfo := goxml.GetData().GuildSandTableLandInfoM.Index(logGroupSettle.BeforeLandId)
	if landInfo == nil || landInfo.LandType != goxml.GuildSandTableLandTypeStronghold {
		return
	}
	guild := g.GetManager().GetGuildM().GetGuild(logGroupSettle.AfterGuildId)
	if guild == nil {
		return
	}
	g.GetManager().baseModule.SendCmdToLogic(guild.GetData().Guild.GuildSid, 0, l2c.ID_MSG_CS2L_GSTPushChatMessage, &l2c.CS2L_GSTPushChatMessage{
		Ret:        uint32(ret.RET_OK),
		Type:       goxml.ChatGstGuildGetStronghold,
		Params:     []string{fmt.Sprintf("%d", fightCount), logGroupSettle.AfterControlGuild, fmt.Sprintf("%d", g.GetID())},
		ChatRoomId: guild.GetChatRoomID(),
	})
}

func (g *GroundBase) updateFightTeams(user *GuildUser, fightResult *cl.GSTFightTeam, win bool, isPvP bool) {
	if user == nil {
		//处理怪物信息更新
		g.updateMonsterFightTeam(fightResult, win)
	} else {
		guildID := user.GetData().Info.GuildId
		for _, guildFightTeam := range g.GetData().FightTeams {
			if guildFightTeam.GuildId == guildID {
				for _, fightTeam := range guildFightTeam.Teams {
					if fightTeam.User.Id == fightResult.User.Id &&
						fightTeam.TeamIndex == fightResult.TeamIndex {
						g.updateUserFightTeam(guildFightTeam, fightTeam, fightResult, win, user, isPvP)
						break
					}
				}
				break
			}
		}
	}
}

func (g *GroundBase) getUserTeamWinTimes(user *GuildUser, fightResult *cl.GSTFightTeam) uint32 {
	if user == nil {
		return 0
	}
	guildID := user.GetData().Info.GuildId
	for _, guildFightTeam := range g.GetData().FightTeams {
		if guildFightTeam.GuildId == guildID {
			for _, fightTeam := range guildFightTeam.Teams {
				if fightTeam.User.Id == fightResult.User.Id &&
					fightTeam.TeamIndex == fightResult.TeamIndex {
					return fightTeam.Wins
				}
			}
			return 0
		}
	}
	return 0
}

func (g *GroundBase) updateUserFightTeam(guildFight *cr.GSTGuildFightTeam, fightTeam *cl.GSTFightTeam, fightResult *cl.GSTFightTeam, win bool, user *GuildUser, isPvP bool) {
	if win {
		guildFight.Win++
		fightTeam.HpPct = fightResult.HpPct
		fightTeam.FatigueValue += goxml.GetData().GuildSandTableConfigInfoM.WinFatigueValue
		fightTeam.Wins++
		if isPvP {
			fightTeam.PvpWins++
			g.logGSTStreak(user, fightTeam)
		}

		if fightTeam.Wins >= goxml.GetData().GuildSandTableConfigInfoM.MaxWins+user.GetData().AddFightTimes {
			//达到最大连胜,从队列中剔除
			guildFight.MatchIndex++
		} else {
			//特殊情况,如果赢得情况下血量都是0,则无法继续战斗
			canFight := false
			for _, hpPct := range fightTeam.HpPct {
				if hpPct != 0 {
					canFight = true
				}
			}
			if !canFight {
				l4g.Infof("公会战 user:%d team:%d win but cant fight", fightTeam.GetUser().Id, fightTeam.TeamIndex)
				guildFight.MatchIndex++
			}
		}
		user.AddContinueWinCount(isPvP, fightResult.TeamIndex)
	} else {
		guildFight.MatchIndex++
		guildFight.Lose++
	}

	user.UpdateTeamFightInfo(fightTeam.TeamIndex, win, fightResult.Score, isPvP)
	g.SetChange()
}

func (g *GroundBase) logGSTStreak(user *GuildUser, fightTeam *cl.GSTFightTeam) {
	sta := g.GetManager().GetStaM().GetSta().GetState()
	userInfo := user.GetData().Info
	logData := &log.LogGSTStreak{
		GstGuildId:     strconv.FormatUint(userInfo.GuildId, 10),
		GstTurn:        sta.LRound,
		GstTeamIndex:   fightTeam.TeamIndex,
		GstTeamStreak:  fightTeam.PvpWins,
		GstRound:       sta.Round,
		GstBattleZone:  g.GetManager().GetBaseModule().Partition(),
		GstRoom:        strconv.FormatUint(g.GetGroup().GetData().Id, 10),
		GstRoomQuality: g.GetGroup().GetData().RoomQuality,
	}
	dclog.LogGSTStreak(g.GetManager().baseModule.GetSrv(), logData)
}

func (g *GroundBase) pushWinsToChat(user *GuildUser, fightTeam *cl.GSTFightTeam) {
	guild := g.GetManager().GetGuildM().GetGuild(user.GetGuildId())
	if guild == nil {
		l4g.Errorf("user %d cant find guild", user.GetData().Info.Id)
		return
	}
	var teamParam string
	teamInfo := user.GetFightTeam(fightTeam.TeamIndex)
	if teamInfo != nil {
		newTeamInfo := &cl.GSTFormationTeamInfo{
			Heros: teamInfo.Heros,
		}
		teamParam, _ = json.MarshalToString(newTeamInfo)
	}

	g.GetManager().baseModule.SendCmdToLogic(user.GetData().Info.Sid, 0, l2c.ID_MSG_CS2L_GSTPushChatMessage, &l2c.CS2L_GSTPushChatMessage{
		Ret:  uint32(ret.RET_OK),
		Type: goxml.ChatGstFightWins,
		Params: []string{user.GetData().Info.Name, fmt.Sprintf("%d", user.GetData().Info.BaseId), fmt.Sprintf("%d", fightTeam.PvpWins),
			fmt.Sprintf("%d", g.GetID()), guild.GetName(), teamParam},
		ChatRoomId: guild.GetChatRoomID(),
	})
}

func (g *GroundBase) updateMonsterFightTeam(fightResult *cl.GSTFightTeam, win bool) {
	fightTeam := g.GetData().MonsterInfo
	if win {
		fightTeam.HpPct = fightResult.HpPct
	} else {
		fightTeam.MonsterTeamIndex++
		fightTeam.HpPct = nil
	}
	g.SetChange()
}

// 屏障类的转变,在第一次有行动的时候就变成下一阶段的地块了
func (g *GroundBase) NextStage() {
	nextStage := goxml.GetData().GuildSandTableMapInfoM.GetNextStage(g.GetID(), g.GetData().GetLandId())
	if nextStage != nil {
		g.GetData().LandId = nextStage.LandID
		if nextStage.MonsterSort != 0 {
			g.GetData().MonsterInfo = &cl.GSTFightTeam{}
		}
		g.SetChange()
		//直接用新的地块类型覆盖
		g.GetGroup().addGround(g.GetID(), g.GetData())
	}
}

func (g *GroundBase) GetOnceScore() uint32 {
	groundInfo := goxml.GetData().GuildSandTableLandInfoM.Index(g.GetData().LandId)
	if groundInfo != nil {
		return groundInfo.FirstScore
	}
	return 0
}
func (g *GroundBase) GetSettleScore() uint32 {
	groundInfo := goxml.GetData().GuildSandTableLandInfoM.Index(g.GetData().LandId)
	if groundInfo != nil {
		return groundInfo.ScoreOutput
	}
	return 0
}

func (g *GroundBase) HaveFirstItem() bool {
	groundInfo := goxml.GetData().GuildSandTableLandInfoM.Index(g.GetData().LandId)
	if groundInfo != nil {
		return len(groundInfo.FirstItemClRes) > 0
	}
	return false
}

func (g *GroundBase) OnResetLRound() {
	g.GetData().SendMatchScore = false
	g.GetData().FightTeams = nil
	g.GetData().FightIndex = 0
	g.GetData().Fighted = false
	g.GetData().FightId = 0
	g.GetData().DragonSkillGuilds = nil
	g.SetChange()
}

func (g *GroundBase) OnGuildUserTeamQuit(uid, guildID uint64, teamIndex uint32) {
	for guildTeamIndex, guildTeam := range g.GetData().FightTeams {
		if guildTeam.GuildId == guildID {
			for index, fightTeam := range guildTeam.Teams {
				if fightTeam.User.Id == uid && fightTeam.TeamIndex == teamIndex {
					guildTeam.Teams = append(guildTeam.Teams[:index], guildTeam.Teams[index+1:]...)
					g.SetChange()
					break
				}
			}
			if len(guildTeam.Teams) == 0 {
				g.GetData().FightTeams = append(g.GetData().FightTeams[:guildTeamIndex], g.GetData().FightTeams[guildTeamIndex+1:]...)
				g.SetChange()
			}
			break
		}
	}
}

func (g *GroundBase) MoveTeam(uid, guildID uint64, teamIndex uint32) ret.RET {
	var guildFightTeam *cr.GSTGuildFightTeam
	for _, guildTeam := range g.GetData().FightTeams {
		if guildTeam.GuildId == guildID {
			guildFightTeam = guildTeam
			break
		}
	}
	if guildFightTeam == nil {
		guildFightTeam = &cr.GSTGuildFightTeam{
			GuildId: guildID,
		}
		g.GetData().FightTeams = append(g.GetData().FightTeams, guildFightTeam)
	}
	guildFightTeam.Teams = append(guildFightTeam.Teams, &cl.GSTFightTeam{
		User: &cl.GSTGuildUserBase{
			Id: uid,
		},
		TeamIndex: teamIndex,
	})
	g.SetChange()
	return ret.RET_OK
}

func (g *GroundBase) GetGSTTeamData(guildID uint64) *cl.GSTGroundTeamData {
	teamData := &cl.GSTGroundTeamData{}
	userSet := make(map[uint64]*GuildUser)
	for _, guildTeam := range g.GetData().FightTeams {
		if guildTeam.GuildId != guildID {
			continue
		}
		for _, team := range guildTeam.Teams {
			guildUser := userSet[team.User.Id]
			if guildUser == nil {
				guildUser = g.GetManager().GetGuildUserM().GetUser(team.User.Id)
				if guildUser == nil {
					l4g.Errorf("cant find user %d", team.User.Id)
					continue
				}
				userSet[team.User.Id] = guildUser
				teamData.UserInfo = append(teamData.UserInfo, &cl.GSTGuildUserBase{
					Id:              guildUser.GetData().Info.Id,
					Name:            guildUser.GetData().Info.Name,
					BaseId:          guildUser.GetData().Info.BaseId,
					ExpireTime:      guildUser.GetData().Info.ExpireTime,
					SeasonLv:        guildUser.GetData().Info.SeasonLv,
					SeasonLinkPower: guildUser.GetData().Info.SeasonLinkPower,
					RemainBookLevel: guildUser.GetData().Info.RemainBookLevel,
					TalentTreeLv:    guildUser.GetData().Info.TalentTreeLv,
					Title:           guildUser.GetData().Info.Title,
					SeasonAdd:       guildUser.GetData().Info.SeasonAdd,
				})
			}
			fightTeam := guildUser.GetFightTeam(team.TeamIndex)
			if fightTeam != nil {
				teamInfo := &cl.GSTGroundTeamInfo{
					Uid:       team.User.Id,
					TeamIndex: team.TeamIndex,
					HangUp:    fightTeam.HangUp,
					Power:     fightTeam.Power,
				}
				for _, heroInfo := range fightTeam.Heros {
					heroClone := heroInfo.Clone()
					heroClone.ActivedSeasonLink = guildUser.IsSeasonLinkActived(heroInfo.SysId)
					teamInfo.Heros = append(teamInfo.Heros, heroClone)
				}
				teamData.TeamInfo = append(teamData.TeamInfo, teamInfo)
			}
		}
		break
	}
	return teamData
}

func (g *GroundBase) ExchangeTeam(guildID uint64, req *l2c.L2CS_GSTExchangeGroundTeam, rsp *l2c.CS2L_GSTExchangeGroundTeam) {
	exchangeTeams := req.Teams
	if len(exchangeTeams) != 2 {
		l4g.Errorf("ground exchangeTeams num err")
		rsp.Ret = uint32(ret.RET_GST_TEAMS_NUM_ERR)
		return
	}
	dispatchTeam := make([]*log.GSTReorderTeam, 0, len(exchangeTeams))
	for _, guildTeam := range g.GetData().GetFightTeams() {
		if guildTeam.GuildId != guildID {
			continue
		}
		indexs := make([]int, 0)
		for _, exchangeTeam := range exchangeTeams {
			for index, fightTeam := range guildTeam.Teams {
				if fightTeam.User.Id == exchangeTeam.User.Id &&
					fightTeam.TeamIndex == exchangeTeam.TeamIndex {
					indexs = append(indexs, index)
					break
				}
			}
		}
		if len(indexs) != 2 {
			l4g.Errorf("ground cant find team")
			rsp.Ret = uint32(ret.RET_GST_CANT_FIND_TEAM)
			return
		}
		team1 := guildTeam.Teams[indexs[0]]
		team2 := guildTeam.Teams[indexs[1]]
		dispatchTeam = append(dispatchTeam, &log.GSTReorderTeam{
			OldIndex:  uint32(indexs[0]),
			NewIndex:  uint32(indexs[1]),
			OwnerId:   strconv.FormatUint(team1.User.Id, 10),
			SpaceId:   g.GetID(),
			SpaceType: g.GetGroundType(),
			TeamIndex: team1.TeamIndex,
		})
		dispatchTeam = append(dispatchTeam, &log.GSTReorderTeam{
			OldIndex:  uint32(indexs[1]),
			NewIndex:  uint32(indexs[0]),
			OwnerId:   strconv.FormatUint(team2.User.Id, 10),
			SpaceId:   g.GetID(),
			SpaceType: g.GetGroundType(),
			TeamIndex: team2.TeamIndex,
		})
		guildTeam.Teams[indexs[0]], guildTeam.Teams[indexs[1]] = guildTeam.Teams[indexs[1]], guildTeam.Teams[indexs[0]]
		g.SetChange()
		break
	}
	rsp.Teams = exchangeTeams
	rsp.GstReorderTeams = dispatchTeam
}

func (g *GroundBase) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeNone
}

func (g *GroundBase) OnFinishFight(blessAdd map[uint64]*guildBless) {
	g.useDragonSkill()
	guildID := g.GetControlGuildID()
	if guildID != 0 {
		guild := g.GetManager().GetGuildM().GetGuild(guildID)
		if guild != nil {
			guild.AddGroundScore(g, blessAdd)
		}
	}
	g.checkPushWinsToChat()
}

func (g *GroundBase) checkPushWinsToChat() {
	round := g.GetManager().GetStaM().GetRound()
	for _, guildFightTeam := range g.GetData().FightTeams {
		for _, fightTeam := range guildFightTeam.Teams {
			if fightTeam.PvpWins >= goxml.GetData().GuildSandTableConfigInfoM.GetBattlePushNum(round) {
				user := g.GetManager().GetGuildUserM().GetUser(fightTeam.User.Id)
				if user != nil {
					g.pushWinsToChat(user, fightTeam)
				}
			}
		}
	}
}

func (g *GroundBase) useDragonSkill() {
	data := g.GetData()
	if len(data.DragonSkillGuilds) == 0 {
		return
	}
	manager := g.GetManager()
	controlGuildID := g.GetControlGuildID()
	if controlGuildID == 0 {
		return
	}
	slices.SortFunc(data.DragonSkillGuilds, func(a, b *cr.GSTDragonSkillGuild) int {
		return cmp.Compare(b.OpTime, a.OpTime)
	})

	logInfo := &cl.GSTLogInfo{
		LogType:  cl.GSTLogType_LogType_GSTDragonSkill,
		LRound:   g.GetManager().GetStaM().GetSta().State.LRound,
		GroundId: g.GetID(),
		GroupId:  g.GetGroup().GetData().GetId(),
	}
	logInfo.LogInfo = &cl.GSTLogData{}
	logDragonSkill := &cl.GSTLogDragonSkill{
		LandId: g.GetData().GetLandId(),
	}
	logInfo.LogInfo.DragonSkill = logDragonSkill
	needSendLog := false
	guildM := manager.GetGuildM()
	for _, dragonSkillGuild := range data.DragonSkillGuilds {
		guildId := dragonSkillGuild.GuildId
		if guildId != controlGuildID {
			//释放龙战技能
			guild := guildM.GetGuild(guildId)
			if guild == nil {
				l4g.Errorf("guild %d get guild failed", guildId)
				continue
			}
			dragonID := guild.GetDragonIdByPos(guild.GetDragonShowPos(), manager.GetSeasonID())
			buildLv := guild.GetMainBaseBuildLevel()
			dragonSkillGuildInfo := &cl.GSTLogDragonSkillGuildInfo{
				GuildName:   guild.GetName(),
				DragonId:    dragonID,
				DragonLevel: buildLv,
			}
			logDragonSkill.GuildInfo = append(logDragonSkill.GuildInfo, dragonSkillGuildInfo)
			needSendLog = true
		}
	}
	if needSendLog {
		g.GetManager().GetGuildLogM().AddLog(logInfo, true)
		g.GetManager().GetGuildM().OnControlGround(0, g, nil)
	}
}

func (g *GroundBase) AddDragonSkill(guild *cr.GSTDragonSkillGuild, dragonSkillLround uint32) {
	g.GetData().DragonSkillGuilds = append(g.GetData().DragonSkillGuilds, guild)
	g.GetData().DragonSkillLround = dragonSkillLround
	g.SetChange()
}

/*----------------------------------------------------------------------------




















-----------------------------------------------------------------------------*/
// 主基地
type MainBaseGround struct {
	IGround
}

func (g *MainBaseGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeMainBase
}

func (g *MainBaseGround) NextStage() {
}

// 空地
type EmptyGround struct {
	IGround
}

func (g *EmptyGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeEmpty
}

// 栅栏
type FenceGround struct {
	IGround
}

func (g *FenceGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeFence
}

// 据点
type StrongholdGround struct {
	IGround
}

func (g *StrongholdGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeStronghold
}

// 屏障
type BarrierGround struct {
	IGround
}

func (g *BarrierGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeBarrier
}

func (g *BarrierGround) CheckMove() bool {
	groundInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(g.GetID())
	if groundInfo == nil {
		l4g.Errorf("GuildSandTableMapInfo cant find ground %d", g.GetData().LandId)
		return false
	}
	for _, unlockGroundID := range groundInfo.UnlockGrounds {
		ground := g.GetGroup().GetGround(unlockGroundID)
		if ground == nil || ground.GetControlGuildID() == 0 {
			return false
		}
	}
	g.NextStage()
	return true
}

// 怪物地
type MonsterGround struct {
	IGround
}

func (g *MonsterGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeMonster
}

// 高级地
type SeniorGround struct {
	IGround
}

func (g *SeniorGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeSenior
}

func (g *GroundBase) GetMaxFightTimes() int {
	var allTeamsNum int
	for _, guildFightTeams := range g.GetData().FightTeams {
		allTeamsNum += len(guildFightTeams.Teams) - int(guildFightTeams.MatchIndex)
	}
	groundExt := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundExt(
		g.GetID(), g.GetData().LandId)
	if groundExt != nil && groundExt.MonsterSort != 0 {
		allTeamsNum += int(groundExt.MonsterNum) + int(g.GetData().MonsterInfo.MonsterTeamIndexExt*goxml.GetData().GuildSandTableConfigInfoM.AssembleAddPerNum) - int(g.GetData().MonsterInfo.MonsterTeamIndex)
	}
	return allTeamsNum
}

func (g *GroundBase) GetNewFightID() uint32 {
	g.GetData().FightId++
	g.SetChange()
	return g.GetData().FightId
}

func (g *GroundBase) GetGSTGroundData(rsp *l2c.CS2L_GSTGetGroundData, guildUser *GuildUser) {
	rsp.TeamData = g.GetGSTTeamData(guildUser.GetGuildId())
	rsp.FirstOccupyGuildName = g.GetData().FirstOccupyGuildName
	if g.GetData().MonsterInfo != nil {
		rsp.MonsterTeam = g.GetData().MonsterInfo.Clone()
	}
}

// 擂台
type ArenaGround struct {
	IGround
}

func (g *ArenaGround) GetGroundType() uint32 {
	return goxml.GuildSandTableLandTypeArena
}

func (g *ArenaGround) MoveTeam(uid, guildID uint64, teamIndex uint32) ret.RET {
	// 屏蔽旧擂台赛
	return ret.RET_GST_USER_CANT_OPERATE
	/*mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(g.GetGroup().GetData().MapInfo.MapConfigId)
	if mapInfo == nil {
		l4g.Errorf("cant find map:%d", g.GetGroup().GetData().MapInfo.MapConfigId)
		return ret.RET_SERVER_ERROR
	}
	var guildFightTeam *cr.GSTGuildFightTeam
	for _, guildTeam := range g.GetData().FightTeams {
		if guildTeam.GuildId == guildID {
			guildFightTeam = guildTeam
			break
		}
	}
	if guildFightTeam == nil {
		guildFightTeam = &cr.GSTGuildFightTeam{
			GuildId: guildID,
		}
		g.GetData().FightTeams = append(g.GetData().FightTeams, guildFightTeam)
	}
	if len(guildFightTeam.Teams)+1 > int(mapInfo.ArenaTeamNum) {
		l4g.Errorf("guild %d teamNum limited", guildID)
		return ret.RET_GST_ARENA_TEAM_LIMIT
	}
	var userHadNum uint32
	for _, team := range guildFightTeam.Teams {
		if team.User.Id == uid {
			userHadNum++
		}
	}
	if userHadNum+1 > goxml.GetData().GuildSandTableConfigInfoM.ArenaUserTeamLimit {
		l4g.Errorf("user %d teamNum limited", uid)
		return ret.RET_GST_ARENA_TEAM_LIMIT
	}
	guildFightTeam.Teams = append(guildFightTeam.Teams, &cl.GSTFightTeam{
		User: &cl.GSTGuildUserBase{
			Id: uid,
		},
		TeamIndex: teamIndex,
	})
	g.SetChange()
	return ret.RET_OK
	*/
}

func (g *ArenaGround) Fight(winUser *GuildUser) {
	if g.isFighting() {
		return
	}
	if len(g.GetData().FightTeams) == 0 {
		return
	}
	if !g.GetData().SendMatchScore {
		//第一次依据公会积分排序
		for _, guildFightTeam := range g.GetData().FightTeams {
			guild := g.GetManager().GetGuildM().GetGuild(guildFightTeam.GuildId)
			if guild != nil {
				guildFightTeam.GuildScore = guild.GetGuildScore()
			}
		}
		slices.SortFunc(g.GetData().FightTeams, func(a, b *cr.GSTGuildFightTeam) int {
			return -cmp.Compare(a.GuildScore, b.GuildScore)
		})
	}
	g.addMatchScore()
	if len(g.GetData().FightTeams) == 1 {
		return
	}
	fightingTeams := make([]*cl.GSTFightTeam, 0)
	var hadGuildID uint64
	if winUser != nil {
		//赢的公会要出一队
		for _, fightTeams := range g.GetData().FightTeams {
			if fightTeams.GuildId == winUser.GetGuildId() {
				fightTeam := g.getUserFightTeam(fightTeams)
				if fightTeam != nil {
					fightingTeams = append(fightingTeams, fightTeam)
					hadGuildID = fightTeams.GuildId
					fightTeam.PopularValue = g.GetGroup().GetArenaGuildPopularScore(fightTeams.GuildId)
				}
				break
			}
		}
	}
	guildsNum := len(g.GetData().FightTeams)
	for i := 0; i < guildsNum; i++ { //pvp对战
		fightTeams := g.GetData().FightTeams[g.GetData().FightIndex]
		if hadGuildID == 0 || fightTeams.GuildId != hadGuildID {
			fightTeam := g.getUserFightTeam(fightTeams)
			if fightTeam != nil {
				fightingTeams = append(fightingTeams, fightTeam)
				fightTeam.PopularValue = g.GetGroup().GetArenaGuildPopularScore(fightTeams.GuildId)
			}
		}
		g.GetData().FightIndex = (g.GetData().FightIndex + 1) % uint32(guildsNum)
		if len(fightingTeams) == 2 {
			break
		}
	}

	if len(fightingTeams) != 2 {
		return
	}
	if g.GetManager().rd.SelectByPercent(50) { //nolint:mnd
		fightingTeams[0], fightingTeams[1] = fightingTeams[1], fightingTeams[0]
	}

	fightResult := &cl.GSTFightResult{
		GroundId:    g.GetID(),
		FightId:     g.GetNewFightID(),
		MapConfigId: g.GetGroup().GetData().MapInfo.MapConfigId,
		ArenaRound:  g.GetGroup().GetArenaRound(),
	}
	fightResult.Attack = g.getFightResultTeam(fightingTeams[0])
	fightResult.Defense = g.getFightResultTeam(fightingTeams[1])
	g.addFighing(fightResult)
}

func (g *ArenaGround) getFightResultTeam(fightTeam *cl.GSTFightTeam) *cl.GSTFightTeam {
	fightResultTeam := &cl.GSTFightTeam{}
	user := g.GetManager().GetGuildUserM().GetUser(fightTeam.User.Id)
	if user == nil {
		return nil
	}
	fightResultTeam.User = &cl.GSTGuildUserBase{
		Id:  user.GetData().Info.Id,
		Sid: user.GetData().Info.Sid,
	}

	guild := g.GetManager().GetGuildM().GetGuild(user.GetGuildId())
	if guild == nil {
		return nil
	}
	// passives := guild.TechGetPassive(goxml.GuildTechAddTypeGlobalPassive)
	// if len(passives) != 0 {
	// 	fightResultTeam.AltPassives = make(map[uint32]*cl.AltPassives)
	// 	fightResultTeam.AltPassives[battle.TeamUniterBattlePos] = &cl.AltPassives{
	// 		Passives: passives,
	// 	}
	// }
	fightResultTeam.TeamIndex = fightTeam.TeamIndex
	fightResultTeam.Wins = fightTeam.Wins
	fightResultTeam.FatigueValue = fightTeam.FatigueValue
	fightResultTeam.HpPct = fightTeam.HpPct
	fightResultTeam.SpaceId = g.GetID()
	l4g.Debugf("公会擂台战,战斗场次(%d),地块(%d): 公会(%d)名字(%s)队伍(%d)",
		g.GetData().FightId, g.GetID(), user.GetGuildId(), user.GetData().Info.Name, fightTeam.TeamIndex)
	return fightResultTeam
}

func (g *ArenaGround) UpdateFightResult(fightResult *cl.GSTFightResult) {
	if !g.isFighting() {
		return
	}
	if g.GetData().Fighting.FightId != fightResult.FightId {
		l4g.Errorf("gst not the same fight %d:%d", g.GetData().Fighting.FightId, fightResult.FightId)
		return
	}
	guildUserM := g.GetManager().GetGuildUserM()
	attack := guildUserM.GetUser(fightResult.Attack.GetUser().Id)
	if attack == nil {
		l4g.Errorf("UpdateFightResult cant find user:%d", fightResult.Attack.GetUser().Id)
		return
	}

	defense := guildUserM.GetUser(fightResult.Defense.GetUser().Id)
	if defense == nil {
		l4g.Errorf("UpdateFightResult cant find user:%d", fightResult.Defense.GetUser().Id)
		return
	}

	if fightResult.IsWin {
		fightResult.Attack.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPWinScore
		fightResult.Defense.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPLoseScore
	} else {
		fightResult.Attack.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPLoseScore
		fightResult.Defense.Score = goxml.GetData().GuildSandTableConfigInfoM.PVPWinScore
	}
	g.addResultUserInfo(attack, defense, fightResult)
	g.updateFightUser(attack, defense, fightResult, true)
	g.updateFightTeams(attack, fightResult.Attack, fightResult.IsWin, true)
	g.updateFightTeams(defense, fightResult.Defense, !fightResult.IsWin, true)
	g.updateFightLog(attack, defense, fightResult)
	g.updateFightGround(attack, defense, fightResult)
}

func (g *ArenaGround) updateFightGround(attack, defense *GuildUser, fightResult *cl.GSTFightResult) {
	g.delFighing()
	//能不能继续战斗
	var winUser *GuildUser
	if fightResult.IsWin {
		winUser = attack
	} else if defense != nil {
		winUser = defense
	}
	g.GetGroup().AddArenaWin(winUser)
	g.Fight(winUser)
}

func (g *ArenaGround) OnFinishFight(_ map[uint64]*guildBless) {
	g.checkPushWinsToChat()
	if g.GetGroup().GetArenaRound() == 0 || len(g.GetData().FightTeams) == 0 {
		return
	}
	g.GetGroup().OnArenaFinish(g.GetData().FightTeams)
	logInfo := &cl.GSTLogInfo{
		LogType:  cl.GSTLogType_LogType_ArenaFightRecord,
		LRound:   g.GetManager().GetStaM().GetSta().State.LRound,
		LogInfo:  &cl.GSTLogData{},
		GroundId: g.GetID(),
		GroupId:  g.GetGroup().GetData().GetId(),
	}
	logData := g.GetGroup().GetArenaState().FightRecord.Clone()
	logInfo.LogInfo.ArenaFightRecord = logData
	g.GetManager().GetGuildLogM().AddLog(logInfo, true)
}

func (g *ArenaGround) GetGSTGroundData(rsp *l2c.CS2L_GSTGetGroundData, guildUser *GuildUser) {

	rsp.TeamData = g.GetGSTTeamData(guildUser.GetGuildId())
	rsp.FirstOccupyGuildName = g.GetData().FirstOccupyGuildName
	if g.GetData().MonsterInfo != nil {
		rsp.MonsterTeam = g.GetData().MonsterInfo.Clone()
	}
	guildUser.UpdateArenaVoteTimes()
	arenaInfo := g.GetGroup().GetData().ArenaInfo
	if arenaInfo != nil {
		rsp.ArenaInfo = &cl.GSTArenaInfo{}
		for _, arenaState := range arenaInfo.ArenaStates {
			if arenaState.FightRecord == nil {
				continue
			}
			arenaGuildRank := &cl.GSTArenaState{
				IsOpen:      arenaState.IsOpen,
				Round:       arenaState.Round,
				Lround:      arenaState.Lround,
				FightRecord: &cl.GSTArenaFightRecord{},
			}
			for _, guildRank := range arenaState.FightRecord.GuildRank {
				arenaGuildRank.FightRecord.GuildRank = append(arenaGuildRank.FightRecord.GuildRank, guildRank.Clone())
			}
			rsp.ArenaInfo.ArenaStates = append(rsp.ArenaInfo.ArenaStates, arenaGuildRank)
		}
		for _, guildInfo := range arenaInfo.GuildsInfo {
			rsp.ArenaInfo.GuildsInfo = append(rsp.ArenaInfo.GuildsInfo, &cl.GSTArenaGuildInfo{
				GuildId:     guildInfo.GuildId,
				FavourTimes: guildInfo.FavourTimes,
				OpposeTimes: guildInfo.OpposeTimes,
			})
		}
		rsp.ArenaVoteTimes = guildUser.GetData().GetArenaVoteTimes()
	}
}
