package activity

import (
	"app/cross/session"
	"app/goxml"
	"app/logic/helper/log"
	"app/protos/in/cm2c"
	"app/protos/in/l2c"
	plog "app/protos/in/log"
	"app/protos/in/r2c"

	"gitlab.qdream.com/platform/proto/da"

	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

type Servicer interface {
	SendCmdToMaster(cm2c.ID, interface{})
	SendCmdToLogic(uint32, uint32, uint32, uint64, uint64, l2c.ID, interface{})
	SendTransformMsgToNode(*cm2c.C2C_CrossNodeTransform)
	GetRealServerID(uint64) uint64
	LogicSessionM() *session.Manager
	DeleteModule(uint32, uint32)
	NewResourceLogMessage() *da.Log
	WriteResourceLogMessage(msg *da.Log)
	GetCurrentServerID(uint64) uint64
	LogTopicResource() string
	NewLogMessage() *da.Log
	WriteLogMessage(msg *da.Log)
	GetCrossServiceConfig() *goxml.CrossServiceConfig
	NewDCLogMessage() *da.Log
	GetBigAreaIDByAreaID(uint32, uint32) uint32
	NewLog(plog.LOG_TYPE) interface{}
	WriteLog(plog.LOG_TYPE, log.LogI)
}

type LogicSessioner interface {
	Write(*parse.PackHead, interface{})
	ID() uint64
}

type BaseModuler interface {
	GetActivityID() uint32
	Partition() uint32
	OpGroup() uint32
	CheckIsNewSeason(int64) (bool, int64, []uint64) //imodule调用，在需要重置的时候检查一下这个，判断一下主manager是否重置好了，如果重置好了，就开始重置
	SendCmdToDB(r2c.ID, interface{})
	SendCmdToLogic(uint64, uint64, l2c.ID, interface{})
	SendTransformMsgToNode(uint32, uint32, uint32, []byte)
	GetRealServerID(uint64) uint64
	GetServerIDList() []uint64 //获取当前分区，对应的服务器id列表
	NewResourceLogMessage() *da.Log
	WriteResourceLogMessage(msg *da.Log)
	GetCurrentServerID(uint64) uint64
	LogTopicResource() string
	GetAsyncMessage(uint32) (interface{}, bool)
	DeleteAsyncMessage(uint32)
	CreateAsyncMessage(interface{}, int64) uint32
	GetSrv() Servicer
	GetEventM() *event.Manager
	GetGroup() *ctx.Group
	GetNormalArea(uint64) uint32
}

type Moduler interface {
	Init(BaseModuler) bool
	CheckRunning() bool //是否开始运行了
	Update(BaseModuler, int64)
	Close(BaseModuler)
	DeletePartition(BaseModuler)  //删除跨服组
	CheckCanResetPart(int64) bool //检查是否可以重置分区。baseManager调用所有的, 并发访问
	//每个人实际重置的时候，自己去数据库里去找对应的数据
	ProcessGrpcRequest(*GrpcRequest) error
	TransformMsg(BaseModuler, *TransformCtrlMsg)
}

type BaseActivityer interface {
	SendCmdToDB(r2c.ID, uint32, interface{})
	ID() uint32
	//GetResetTime() int64
	CheckAllCanReset(int64) bool
	Reset() int64
}

type Activityer interface {
	NeedPartition() bool
	NeedResetPartition() bool
	//	NeedResetPart(lastResetTime, now int64) bool //是否需要重置分区, baseManager调用, 每个心跳的时候都判断一下
	//Reset(lastResetTime, newResetTime int64, actPart []*cr.ActPart) //实际重置要做的事， baseManager调用, 上面的检查通过，就调用这个方法
	CreateRealModule(uint32) Moduler
	GetNextResetTime(int64) int64
	GetInitLastResetTime() int64
}
