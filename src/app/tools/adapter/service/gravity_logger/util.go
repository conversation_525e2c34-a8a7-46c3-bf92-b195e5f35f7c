package gravitylogger

import (
	"adapter/service/config"
	"adapter/service/helper"
	"adapter/service/kafka_consumer"
	"bytes"
	"fmt"
	"io"
	"net/http"

	l4g "github.com/ivanabc/log4go"
)

func GetLogger(mode string) kafka_consumer.MessageHandlerCreator {
	return func() kafka_consumer.MessageHandler {
		return NewLogger()
	}
}

type GameReportRsp struct {
	Code uint32 `json:"code" url:"-"`
	//Data string `json:"data" url:"-"`
	//Msg  string `json:"msg" url:"-"`
}

func GameReport(cfg *config.GravityConfig, logs []*BaseMessage) error {
	for _, log := range logs {
		jsonData, _ := helper.Json.Marshal(&log)
		os := log.Os
		accessToken := cfg.AccessTokens[os]
		if cfg.Debug {
			if accessToken == "" {
				accessToken = cfg.AccessTokens["1"]
			}
		}
		if accessToken == "" {
			l4g.Errorf("gravity no found access token: os:%s", os)
			continue
		}
		url := fmt.Sprintf("%s?access_token=%s", cfg.Url, accessToken)
		httpReq, err := http.NewRequest("POST", url, bytes.NewReader(jsonData))
		if err != nil {
			l4g.Errorf("new request failed:%v", err)
			continue
		}
		if cfg.Debug {
			httpReq.Header.Set("Turbo-Debug-Mode", "1")
		}
		resp, err := http.DefaultClient.Do(httpReq)
		if err != nil {
			l4g.Errorf("GameReport fail. http request error, req:%s,err:%s", jsonData, err)
			continue
		}
		defer resp.Body.Close()
		result, _ := io.ReadAll(resp.Body)
		l4g.Debugf("gravity url:%s send:%s resp:%s", url, jsonData, string(result))
		var reportResp GameReportRsp
		err = helper.Json.Unmarshal(result, &reportResp)
		if err != nil {
			l4g.Errorf("GameReport fail. req:%s,resp:%v", jsonData, reportResp)
			continue
		}
		if reportResp.Code != 0 {
			l4g.Errorf("GameReport fail. req:%s,resp:%v", jsonData, reportResp)
			continue
		}
	}
	return nil
}
