package divinedemon

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	l4g "github.com/ivanabc/log4go"
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DIVINE_DEMON_SUMMON),
		&DebutHeroSummonMessage{}, "divine_demon_summon", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DIVINE_DEMON_RECEIVE_TASK_AWARD),
		&DebutReceiveTaskAwardMessage{}, "divine_demon_receive_task_award", 12, 0, 0, 0)
}

type DebutHeroSummonMessage struct {
	helper.BaseMessage
}

func (m *DebutHeroSummonMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var guaranteeType []uint64
	var idList []string
	if log.Param3 != "" && log.Param3 != "null" {
		err := helper.Json.Unmarshal([]byte(log.Param3), &guaranteeType)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		idList = make([]string, 0, len(guaranteeType))
		for _, id := range guaranteeType {
			idList = append(idList, strconv.FormatUint(id, 10))
		}
	}
	var wishHeroes []uint64
	var wishHeroIdList []string
	if log.Param4 != "" && log.Param4 != "null" {
		err := helper.Json.Unmarshal([]byte(log.Param4), &wishHeroes)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		wishHeroIdList = make([]string, 0, len(wishHeroes))
		for _, id := range wishHeroes {
			wishHeroIdList = append(wishHeroIdList, strconv.FormatUint(id, 10))
		}
	}

	r.SetTrack("activity_id", log.Param10)        // 活动系统id
	r.SetTrack("summon_type", log.Param11)        // 抽卡类型
	r.SetTrack("summon_total_count", log.Param12) // 抽卡总次数
	r.SetTrack("sc_status", log.Param13)          // sc状态
	r.SetTrack("up_red_hero_count", log.Param14)  // 心愿将次数
	//r.SetTrack("no_up_red_hero_count", log.Param15) // 非up概率红卡次数
	r.SetTrack("divine_demon_activity_type", log.Param15)     // 活动类型
	r.SetTrack("divine_demon_pool_id", log.Param16)           // 卡池id
	r.SetTrack("divine_demon_red_hero_count", log.Param17)    // 红卡数量
	r.SetTrack("divine_demon_cost_type", log.Param18)         // 抽卡消耗资源类型
	r.SetResourceContentMessage("summon_content", log.Param1) // 抽取到的资源
	if len(guaranteeType) > 0 {
		r.SetTrack("divine_demon_guarantee_types", idList) //保底类型
	}
	if len(wishHeroes) > 0 {
		r.SetTrack("divine_demon_wish_hero", wishHeroIdList) // 是否是心愿英雄 0否 1是
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type DebutReceiveTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *DebutReceiveTaskAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("activity_id", log.Param10)     // 活动系统id
	r.SetTrack("task_type", log.Param11)       // 任务类型
	r.SetTrack("divine_demon_id", log.Param12) // 活动唯一id
	r.SetTrack("task_ids", log.Param1)         // 任务id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
