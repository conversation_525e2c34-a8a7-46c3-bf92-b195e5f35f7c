package talentTreeFix

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath           = flag.String("talentTreeFixConfigData", "../data/", "service data path")
	fixTalentTreeHotFailed uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	fixTalentTreeHot(redisClient)
	if fixTalentTreeHotFailed == 0 {
		fmt.Printf("[SUCCESS] fixTalentTreeHot Success!\n")
	} else {
		fmt.Printf("[FAILED] fixTalentTreeHot Failed, need check error log! failedCount:%d\n", fixTalentTreeHotFailed)
	}
	l4g.Infof("finish repair")
}

func fixTalentTreeHot(r *redis.Redis) {
	hotData, err := r.RopCr.GetAllTalentTreeHot()
	if err != nil {
		l4g.Errorf("fixTalentTreeHot: get TalentTreeHot error. err:%s", err)
		fixTalentTreeHotFailed++
		return
	}
	if hotData == nil {
		return
	}
	changeData := make([]*cr.TalentTreeHot, 0, len(hotData))
	for _, data := range hotData {
		var change bool
		for _, node := range data.Nodes {
			if node.Id == 1105101 {
				for i, level := range node.Levels {
					if level.Type >= 5 {
						node.Levels = append(node.Levels[0:i], node.Levels[i+1:]...)
						change = true
						break
					}
				}
			} else if node.Id == 1105103 {
				for i, level := range node.Levels {
					if level.Type >= 5 {
						node.Levels = append(node.Levels[0:i], node.Levels[i+1:]...)
						change = true
						break
					}
				}
			} else if node.Id == 1105114 {
				for i, level := range node.Levels {
					if level.Type > 3 {
						node.Levels = append(node.Levels[0:i], node.Levels[i+1:]...)
						change = true
						break
					}
				}
			}
		}
		if change {
			changeData = append(changeData, data)
		}
	}
	if len(changeData) == 0 {
		return
	}

	err = r.RopCr.SetSomeTalentTreeHot(changeData)
	if err != nil {
		l4g.Errorf("fixTalentTreeHot: set TalentTreeHot error. err:%s", err)
		fixTalentTreeHotFailed++
		return
	}
}
