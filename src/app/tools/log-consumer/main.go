package main

import (
	"app/tools/log-consumer/service/consum_service"
	"app/tools/log-consumer/service/cron_service"
	"app/tools/log-consumer/service/db_schema_service"
	"flag"

	"gitlab.qdream.com/kit/sea/micro/restful"
)

func main() {
	flag.Parse()

	switch *ServiceName {
	case "consum":
		restful.Main(consum_service.New)
	case "db_schema":
		if err := db_schema_service.LoadConfig(*ConfigDir + *CfgFile); err != nil {
			panic(err)
		}
		service := db_schema_service.New()
		service.Run()
	case "cron":
		restful.Main(cron_service.New)
	}
}
