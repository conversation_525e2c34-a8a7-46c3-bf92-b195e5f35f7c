log: ../log/log-consumer.log

restful:
 addr: :31001
 mode: release

mongo:
  server: mongodb://127.0.0.1:27018
  # username:
  # password:
  database: log # 数据库名称
  workers: 0 # 默认runtime.NumCPU()
  collections:
    - collname: 1            # 集合名称 log.MONGO_LOG_COLL的小写
      indexes:                   # 集合的索引
        # - keys: [formation_id:1, target_id:1, power:1]   # 索引涉及的字段，这里是有顺序的
        #   options:
        #     name: formation_id_1_target_id_1_power_1    # 索引的名称，   默认是上面的key使用“_”连起来的
        # - keys: [formation_id:1, target_id:1]
        #   options:
        #     name: formation_id_1_target_id_1
        - keys: [power:1]
          # options:
          #   name: power_1
        - keys: [createdAt:1]
          options:
            # name: createdAt_1
            expireAfterSeconds: 2592000            #   过期索引，多少秒后过期    

es:
  hosts: [http://************:9200]
  workers: 0 # 默认runtime.NumCPU()
  indexs:
    - index: 1 # log.ES_LOG_INDEX 的小写
      mappings:
        properties:
          _id: 
            type: "long"
          name: 
            type: "text"
          server_id: 
            type: "integer"
    - index: 2 # log.ES_LOG_INDEX 的小写
      mappings:
        properties:
          _id: 
            type: "long"
          name: 
            type: "text"
          server_id: 
            type: "integer"

kafkas:
  - version: "2.4.0"
    brokers: [************:9092]
    topics: [1] # log.KAFKA_TOPIC 要消费的topics
    handler_type: 1 # log.KAFKA_HANDLER_TYPE 怎么处理日志(outputs注册的handler)
  # - version: "2.4.0"
  #   brokers: [************:9092]
  #   topics: [2] # log.KAFKA_TOPIC
  #   handler_type: 2 # log.KAFKA_HANDLER_TYPE

cron:
  tasks:
    - name: "定时删除大地图日志"
      spec: "*/5 * * * *" # cron表达式
      function: "deleteSeasonMapRecord" # 任务处理函数
      enabled: true # 是否启用

