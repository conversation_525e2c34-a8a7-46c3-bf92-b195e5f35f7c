package config

import (
	"app/protos/in/log"

	"github.com/spf13/viper"
)

type ESConfig struct {
	Hosts   []string         `mapstructure:"hosts"`
	Indexs  []*ESIndexConfig `mapstructure:"indexs"`
	Workers int              `mapstructure:"workers"`
}

type ESIndexConfig struct {
	Index    log.ES_LOG_INDEX `mapstructure:"index"`
	Mappings struct {
		Properties map[string]struct {
			Type string `mapstructure:"type" json:"type"`
		} `mapstructure:"properties" json:"properties"`
	} `mapstructure:"mappings" json:"mappings"`
}

func NewESConfig() *ESConfig {
	var esCfg ESConfig
	if err := viper.UnmarshalKey("es", &esCfg); err != nil {
		panic(err)
	}
	return &esCfg
}
