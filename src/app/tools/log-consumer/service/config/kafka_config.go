package config

import (
	"app/protos/in/log"

	"github.com/spf13/viper"
)

type KafkaConfig struct {
	Kafkas []*Kafka
}

type Kafka struct {
	Version     string                 `mapstructure:"version"`
	Brokers     []string               `mapstructure:"brokers"`
	Topics      []log.KAFKA_TOPIC      `mapstructure:"topics"`
	HandlerType log.KAFKA_HANDLER_TYPE `mapstructure:"handler_type"`
}

func NewKafkaConfig() *KafkaConfig {
	var kafkas []*Kafka
	if err := viper.UnmarshalKey("kafkas", &kafkas); err != nil {
		panic(err)
	}
	return &KafkaConfig{
		Kafkas: kafkas,
	}
}

func (c *Kafka) GetTopics() []string {
	var topics []string
	for _, topic := range c.Topics {
		topics = append(topics, topic.String())
	}
	return topics
}
