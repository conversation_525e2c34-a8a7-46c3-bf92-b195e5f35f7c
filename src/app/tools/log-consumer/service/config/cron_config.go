package config

import "github.com/spf13/viper"

type CronConfig struct {
	Tasks []TaskConfig `mapstructure:"tasks"`
}

type TaskConfig struct {
	Name     string `mapstructure:"name"`
	Spec     string `mapstructure:"spec"`
	Function string `mapstructure:"function"`
	Enabled  bool   `mapstructure:"enabled"`
}

func NewCronConfig() *CronConfig {
	var cronCfg CronConfig
	if err := viper.UnmarshalKey("cron", &cronCfg); err != nil {
		panic(err)
	}
	return &cronCfg
}
