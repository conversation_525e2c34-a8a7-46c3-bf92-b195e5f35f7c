package config

import (
	"app/protos/in/log"

	"github.com/spf13/viper"
	"go.mongodb.org/mongo-driver/mongo"
)

type MongoConfig struct {
	Server      string        `mapstructure:"server"`
	Database    string        `mapstructure:"database"`
	Username    string        `mapstructure:"username"`
	Password    string        `mapstructure:"password"`
	Workers     int           `mapstructure:"workers"`
	Collections []*Collection `mapstructure:"collections"`
}

type Collection struct {
	CollName log.MONGO_LOG_COLL `mapstructure:"collname"`
	Indexes  []mongo.IndexModel `mapstructure:"indexes"`
}

func NewMongoConfig() *MongoConfig {
	var mongoCfg MongoConfig
	if err := viper.UnmarshalKey("mongo", &mongoCfg); err != nil {
		panic(err)
	}
	return &mongoCfg
}
