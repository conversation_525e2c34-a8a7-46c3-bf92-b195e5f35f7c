package cron_service

import (
	"app/tools/log-consumer/service/config"
	"app/tools/log-consumer/service/cron_service/cron_task"

	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/micro/restful"

	"github.com/gin-gonic/gin"
)

type Service struct {
	config.ConfigInterface
	cronConfig *config.CronConfig
}

func New(group *ctx.Group) restful.Servicer {
	return &Service{}
}

func (s *Service) Register(r *gin.Engine) {
	// 注册http
	//r.Handle("GET", "/metrics", gin.WrapH(promhttp.Handler()))
}

func (s *Service) Run() {
	s.initConfig()
	cron_task.CronTasks(s)
}

func (s *Service) Close() {}

func (s *Service) initConfig() {
	s.cronConfig = config.NewCronConfig()
}

func (s *Service) GetCronConfig() *config.CronConfig {
	return s.cronConfig
}
