package cron_task

import (
	"app/tools/log-consumer/service/config"

	"github.com/robfig/cron/v3"
)

type FunctionRegistry map[string]func()

var registry FunctionRegistry

func init() {
	registry = FunctionRegistry{
		"deleteSeasonMapRecord": deleteSeasonMapRecord,
	}
}

func CronTasks(configI config.ConfigInterface) {
	c := cron.New(cron.WithSeconds())
	cronCfg := configI.GetCronConfig()
	for _, task := range cronCfg.Tasks {
		if !task.Enabled {
			continue
		}
		if fn, exists := registry[task.Function]; exists {
			_, err := c.AddFunc(task.Spec, fn)
			if err != nil {
				panic(err)
			}
		} else {
			panic("Function not found: " + task.Function)
		}
	}
	c.Start()
}
