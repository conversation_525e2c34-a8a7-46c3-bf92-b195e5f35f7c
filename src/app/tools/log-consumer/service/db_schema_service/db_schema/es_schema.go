package db_schema

import (
	"app/tools/log-consumer/service/config"
	"bytes"
	"context"
	"encoding/json"
	"strings"

	l4g "github.com/ivanabc/log4go"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
)

func initEsSchema() {
	esConfig := config.NewESConfig()
	if len(esConfig.Hosts) == 0 {
		return
	}
	client := connectES(esConfig)
	checkESIndex(esConfig, client)
	l4g.Infof("EsSchema successfully")
}

func connectES(esCfg *config.ESConfig) *elasticsearch.Client {
	client, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: esCfg.Hosts,
	})
	if err != nil {
		panic(err)
	}
	// 验证连接
	infoReq := esapi.InfoRequest{
		Pretty: true,
	}
	info, err := infoReq.Do(context.Background(), client)
	if err != nil {
		panic(err)
	}
	l4g.Debugf("Elastic Info: %s", info.String())
	return client
}

func checkESIndex(esCfg *config.ESConfig, client *elasticsearch.Client) {
	for _, indexInfo := range esCfg.Indexs {
		index := strings.ToLower(indexInfo.Index.String())
		existIndexReq := esapi.IndicesExistsRequest{
			Index: []string{index},
		}
		existIndexRsp, existIndexErr := existIndexReq.Do(context.Background(), client)
		if existIndexErr != nil {
			panic(existIndexErr)
		}
		if existIndexRsp.IsError() {
			l4g.Debugf("Creating index %s", index)
			var buf bytes.Buffer
			if err := json.NewEncoder(&buf).Encode(indexInfo.Mappings); err != nil {
				panic(err)
			}
			createIndexReq := esapi.IndicesCreateRequest{
				Index: index,
				Body:  &buf,
			}
			createIndexRsp, createIndexErr := createIndexReq.Do(context.Background(), client)
			if createIndexErr != nil {
				panic(createIndexErr)
			}
			if createIndexRsp.IsError() {
				panic(createIndexRsp.String())
			}
		}
	}
}
