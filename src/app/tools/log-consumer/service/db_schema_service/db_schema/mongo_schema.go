package db_schema

import (
	"app/tools/log-consumer/service/config"
	"context"
	"strings"

	l4g "github.com/ivanabc/log4go"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func initMongoSchema() {
	mongoConfig := config.NewMongoConfig()
	if len(mongoConfig.Server) == 0 {
		return
	}
	db := connectMongo(mongoConfig)
	checkMongoIndex(mongoConfig, db)
	l4g.Infof("MongoSchema successfully")
}

func connectMongo(mongoConfig *config.MongoConfig) *mongo.Database {
	credential := options.Credential{
		Username:   mongoConfig.Username,
		Password:   mongoConfig.Password,
		AuthSource: mongoConfig.Database,
	}

	clientOptions := options.Client().ApplyURI(mongoConfig.Server).SetAuth(credential)

	// 连接数据库
	client, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		panic(err)
	}

	// 验证连接
	if err := client.Ping(context.TODO(), nil); err != nil {
		panic(err)
	}
	return client.Database(mongoConfig.Database)
}

func checkMongoIndex(mongoConfig *config.MongoConfig, db *mongo.Database) {
	for _, collection := range mongoConfig.Collections {
		collName := strings.ToLower(collection.CollName.String())
		coll := db.Collection(collName)
		_, err := coll.Indexes().CreateMany(context.Background(), collection.Indexes)
		if err != nil {
			panic(err)
		}
	}
}
