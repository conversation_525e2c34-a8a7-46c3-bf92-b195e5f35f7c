package mongo

import (
	"app/protos/in/log"
	"app/tools/log-consumer/service/config"
	"context"
	"encoding/json"
	"hash/fnv"
	"runtime"
	"strconv"

	"github.com/Shopify/sarama"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
	"go.mongodb.org/mongo-driver/mongo"
)

type MongoConsumerHandler struct {
	group  *ctx.Group
	client *mongo.Client
	actors []*CollActor
}

func MakeMongo(group *ctx.Group, configI config.ConfigInterface) sarama.ConsumerGroupHandler {
	mongoConfig := configI.GetMongoConfig()
	handler := &MongoConsumerHandler{
		group:  group,
		client: NewMongoClient(mongoConfig),
	}
	handler.initMongoActors(mongoConfig)
	return handler
}

func (c *MongoConsumerHandler) initMongoActors(mongoConfig *config.MongoConfig) {
	workers := mongoConfig.Workers
	if workers == 0 {
		workers = runtime.NumCPU()
	}
	actors := make([]*CollActor, 0)
	for i := 0; i < workers; i++ {
		actor := NewCollActor(c.group.CreateChild(), strconv.Itoa(i), c.client, mongoConfig.Database)
		actors = append(actors, actor)
		go actor.Run(actor)
	}
	c.actors = actors
}

func (c *MongoConsumerHandler) Setup(ses sarama.ConsumerGroupSession) error {
	l4g.Infof("setup consumer group: %+v", ses.Claims())
	return nil
}

func (c *MongoConsumerHandler) Cleanup(ses sarama.ConsumerGroupSession) error {
	l4g.Infof("cleanup consumer group: %+v", ses.Claims())
	c.group.Stop()
	for _, actor := range c.actors {
		actor.Close()
	}
	c.group.Wait()
	c.group.Finish()
	c.client.Disconnect(context.TODO())
	return nil
}

func (c *MongoConsumerHandler) ConsumeClaim(ses sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		// Process the message
		l4g.Debugf("Consumed message: topic=%s, partition=%d, offset=%d, key=%s, value=%s\n",
			message.Topic, message.Partition, message.Offset, string(message.Key), string(message.Value))
		var data log.KafkaLogHandlerData
		err := json.Unmarshal(message.Value, &data)
		if err != nil {
			l4g.Error(err)
			continue
		}
		if data.MongoData == nil {
			l4g.Errorf("no MongoData. data %+v", data)
			continue
		}
		actor := c.getActor(data.MongoData.CollName)
		messager := &CollMessager{
			Data: data.MongoData,
		}
		actor.AddMessage(messager)
		ses.MarkMessage(message, "")
	}
	return nil
}

func (c *MongoConsumerHandler) getActor(coll string) *CollActor {
	h := fnv.New32a()
	h.Write([]byte(coll))
	index := int(h.Sum32()) % len(c.actors)
	return c.actors[index]
}
