package mongo

import (
	"app/protos/in/log"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/actor"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Messager interface {
	actor.Messager
}

type CollMessager struct {
	Messager
	Data *log.MongoLogHandlerData
}

func (messager *CollMessager) Process(param actor.Receiver) error {
	collActor := param.(*CollActor)
	writeModel, err := messager.getWriteModel()
	if err == nil {
		collActor.AddWriteModel(messager.Data.CollName, writeModel)
	}

	return nil
}

func (messager *CollMessager) getWriteModel() (mongo.WriteModel, error) {
	var log interface{}
	err := bson.Unmarshal(messager.Data.Data, log)
	if err != nil {
		l4g.Errorf("%s: bson unmarshal error:%+v", messager.Data.CollName, err)
		return nil, err
	}
	var doc bson.M
	err = bson.Unmarshal(messager.Data.Data, doc)
	if err != nil {
		l4g.Errorf("%s: bson unmarshal error:%+v", messager.Data.CollName, err)
		return nil, err
	}

	if id, exists := doc["_id"]; exists {
		replaceModel := mongo.NewReplaceOneModel().
			SetFilter(bson.M{"_id": id}).
			SetReplacement(log).
			SetUpsert(true)
		return replaceModel, nil
	} else {
		insertModel := mongo.NewInsertOneModel().SetDocument(log)
		return insertModel, nil
	}
}
