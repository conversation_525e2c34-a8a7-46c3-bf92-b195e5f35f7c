package mongo

import (
	"context"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	logsMaxNum = 1000
)

type CollActor struct {
	*actor.Actor
	dataBase *mongo.Database
	group    *ctx.Group
	models   map[string][]mongo.WriteModel
	name     string
}

func NewCollActor(group *ctx.Group, name string, client *mongo.Client, dataBase string) *CollActor {
	actConfig := &actor.Config{
		KindName: name,
		MsgQSize: logsMaxNum,
		Rate:     1000,
	}
	act := actor.NewActor(group.CreateChild(), actConfig)
	actor := &CollActor{
		Actor:    act,
		group:    group,
		name:     name,
		models:   make(map[string][]mongo.WriteModel),
		dataBase: client.Database(dataBase),
	}
	act.TickerCB = func(int64) {
		actor.ConsumeLog()
	}
	return actor
}

func (actor *CollActor) ConsumeLog() {
	for coll, models := range actor.models {
		collDb := actor.dataBase.Collection(coll)
		if collDb == nil {
			l4g.Errorf("get coll error: %s", coll)
			continue
		}
		for i := 0; i < len(actor.models); i += logsMaxNum {
			end := min(i+logsMaxNum, len(actor.models))
			batch := models[i:end]

			_, err := collDb.BulkWrite(context.TODO(), batch)
			if err != nil {
				l4g.Errorf("%s BulkWrite Error %s", coll, err.Error())
				continue
			}
		}
		actor.models[coll] = actor.models[coll][:0]
	}
}

func (actor *CollActor) AddWriteModel(coll string, model mongo.WriteModel) {
	actor.models[coll] = append(actor.models[coll], model)
	if len(actor.models) >= logsMaxNum {
		l4g.Errorf("%s mongo log queue in highwater : %d", coll, len(actor.models))
		actor.ConsumeLog()
	}
}

func (actor *CollActor) Close() {
	if len(actor.models) > 0 {
		actor.ConsumeLog()
	}
	actor.group.Stop()
	actor.group.Wait()
	actor.group.Finish()
	l4g.Infof("mongo actor %s close finish", actor.name)
}
