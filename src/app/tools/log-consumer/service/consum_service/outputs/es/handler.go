package es

import (
	"app/protos/in/log"
	"app/tools/log-consumer/service/config"
	"bytes"
	"encoding/json"
	"strings"

	"github.com/Shopify/sarama"
	"github.com/elastic/go-elasticsearch/v7/esutil"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
)

type ESConsumerHandler struct {
	group  *ctx.Group
	esBulk *ESBulk
}

func MakeES(group *ctx.Group, configI config.ConfigInterface) sarama.ConsumerGroupHandler {
	return &ESConsumerHandler{
		group:  group,
		esBulk: NewESBulk(configI.GetEsConfig()),
	}
}

func (c *ESConsumerHandler) Setup(ses sarama.ConsumerGroupSession) error {
	l4g.Infof("setup consumer group: %+v", ses.Claims())
	return nil
}

func (c *ESConsumerHandler) Cleanup(ses sarama.ConsumerGroupSession) error {
	l4g.Infof("cleanup consumer group: %+v", ses.Claims())
	c.esBulk.Close()
	c.group.Finish()
	return nil
}

func (c *ESConsumerHandler) ConsumeClaim(ses sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		// Process the message
		l4g.Debugf("Consumed message: topic=%s, partition=%d, offset=%d, key=%s, value=%s\n",
			message.Topic, message.Partition, message.Offset, string(message.Key), string(message.Value))
		var data log.KafkaLogHandlerData
		err := json.Unmarshal(message.Value, &data)
		if err != nil {
			l4g.Error(err)
			continue
		}
		if data.EsData == nil {
			l4g.Errorf("no EsData. data %+v", data)
			continue
		}
		item := &esutil.BulkIndexerItem{
			Index:  strings.ToLower(data.EsData.Index),
			Action: "index",
			Body:   bytes.NewBuffer(data.EsData.Data),
		}
		c.esBulk.Add(item)
		// Mark the message as processed
		ses.MarkMessage(message, "")
	}
	return nil
}
