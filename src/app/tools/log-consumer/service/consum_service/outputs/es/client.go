package es

import (
	"app/tools/log-consumer/service/config"
	"context"
	"net"
	"net/http"
	"time"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	l4g "github.com/ivanabc/log4go"
)

func NewESClient(esConfig *config.ESConfig) *elasticsearch.Client {
	if len(esConfig.Hosts) == 0 {
		l4g.Error("es no hosts")
		return nil
	}
	esClient, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: esConfig.Hosts,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			IdleConnTimeout: 90 * time.Second,
		},
	})
	if err != nil {
		panic(err)
	}
	// 验证连接
	infoReq := esapi.InfoRequest{
		Pretty: true,
	}
	info, err := infoReq.Do(context.Background(), esClient)
	if err != nil {
		panic(err)
	}
	if info.IsError() {
		panic(info.String())
	}
	return esClient
}
