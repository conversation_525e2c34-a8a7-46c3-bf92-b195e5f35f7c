package es

import (
	"app/protos/in/log"
	"bytes"
	"context"
	"encoding/json"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	l4g "github.com/ivanabc/log4go"
)

func Search(req *log.EsQueryReq, esClient *elasticsearch.Client) ([]string, error) {
	searchReq := esapi.SearchRequest{
		Index:  []string{req.Index},
		Body:   bytes.NewReader(req.Query),
		Pretty: true,
	}
	searchRsp, err := searchReq.Do(context.Background(), esClient)
	if err != nil {
		l4g.Errorf("search error :%s", err.Error())
		return nil, err
	}
	defer searchRsp.Body.Close()
	// 解析响应数据
	var response map[string]interface{}
	if err := json.NewDecoder(searchRsp.Body).Decode(&response); err != nil {
		l4g.<PERSON><PERSON><PERSON>("Decode error :%s", err.Error())
		return nil, err
	}

	// 提取 hits 数据
	hits, ok := response["hits"].(map[string]interface{})
	if !ok {
		l4g.Debug("hits not found")
		return nil, nil
	}

	// 提取文档数组
	hitsData, ok := hits["hits"].([]interface{})
	if !ok {
		l4g.Debug(" hits array not found")
		return nil, nil
	}
	var datas []string
	for _, hit := range hitsData {
		hitData, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}
		sourceData, ok := hitData["_source"].(map[string]interface{})
		if !ok {
			continue
		}
		jsonData, err := json.Marshal(sourceData)
		if err != nil {
			l4g.Errorf("Marshal error :%s", err.Error())
			continue
		}
		datas = append(datas, string(jsonData))
	}
	return datas, nil
}
