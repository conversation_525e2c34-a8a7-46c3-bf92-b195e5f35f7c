package mongo

import (
	"app/protos/in/log"
	"app/tools/log-consumer/service/config"
	"context"
	"encoding/json"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func Search(req *log.MongoQueryReq, mongoConfig *config.MongoConfig, mongoClient *mongo.Client) ([]string, error) {
	dataBase := mongoClient.Database(mongoConfig.Database)
	col := dataBase.Collection(req.CollName)
	if col == nil {
		return nil, fmt.Errorf("get col is nil. col:%s", req.CollName)
	}
	var filter interface{}
	if err := bson.Unmarshal(req.Filter, &filter); err != nil {
		return nil, fmt.Errorf("解析过滤条件失败: %v", err)
	}
	// 创建查询选项
	findOptions := options.Find()
	// 处理排序选项
	if len(req.Options.Sort) > 0 {
		sortDoc := bson.D{}
		for _, sortField := range req.Options.Sort {
			direction := 1
			if sortField.Direction == log.SortDirection_DESC {
				direction = -1
			}
			sortDoc = append(sortDoc, bson.E{Key: sortField.Field, Value: direction})
		}
		findOptions.SetSort(sortDoc)
	}

	// 处理分页选项
	if req.Options.Limit > 0 {
		findOptions.SetLimit(req.Options.Limit)
	}
	if req.Options.Skip > 0 {
		findOptions.SetSkip(req.Options.Skip)
	}

	cursor, err := col.Find(context.TODO(), filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询失败: %v", err)
	}
	defer cursor.Close(context.TODO())
	// 收集结果
	var results []bson.M
	for cursor.Next(context.TODO()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("解码结果失败: %v", err)
		}
		results = append(results, result)
	}
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("游标错误: %v", err)
	}
	var datas []string
	for _, result := range results {
		jsonResult, err := json.Marshal(result)
		if err != nil {
			return nil, fmt.Errorf("转换结果为JSON失败: %v", err)
		}
		datas = append(datas, string(jsonResult))
	}

	return datas, nil
}
