package kafka_consumer

import (
	"app/tools/log-consumer/service/config"
	"app/tools/log-consumer/service/consum_service/outputs/es"
	"app/tools/log-consumer/service/consum_service/outputs/mongo"
	"context"

	"app/protos/in/log"

	"github.com/Shopify/sarama"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
)

type KafkaConsumer struct {
	consumerGroup   sarama.ConsumerGroup
	consumerHandler sarama.ConsumerGroupHandler
	cancel          context.CancelFunc
	config          *config.Kafka
	group           *ctx.Group
}

func newKafkaConsumer(kafkaConfig *config.Kafka, group *ctx.Group, configI config.ConfigInterface) *KafkaConsumer {
	config := sarama.NewConfig()
	config.Consumer.Return.Errors = true
	config.Consumer.Offsets.Initial = sarama.OffsetOldest
	version, err := sarama.ParseKafkaVersion(kafkaConfig.Version)
	if err != nil {
		panic(err)
	}
	config.Version = version
	config.ClientID = kafkaConfig.HandlerType.String()
	consumerGroup, err := sarama.NewConsumerGroup(kafkaConfig.Brokers, kafkaConfig.HandlerType.String(), config)
	if err != nil {
		panic(err)
	}
	return &KafkaConsumer{
		consumerGroup:   consumerGroup,
		consumerHandler: getConsumerHandler(kafkaConfig, group, configI),
		config:          kafkaConfig,
		group:           group,
	}
}

func getConsumerHandler(kafkaConfig *config.Kafka, group *ctx.Group, configI config.ConfigInterface) sarama.ConsumerGroupHandler {
	switch kafkaConfig.HandlerType {
	case log.KAFKA_HANDLER_TYPE_KAFKA_HANDLER_TYPE_ES:
		return es.MakeES(group, configI)
	case log.KAFKA_HANDLER_TYPE_KAFKA_HANDLER_TYPE_MONGO:
		return mongo.MakeMongo(group, configI)
	default:
		panic("unsupported kafka handler type: " + kafkaConfig.HandlerType.String())
	}
}

func (c *KafkaConsumer) Run() {
	go func() {
		defer func() {
			c.consumerGroup.Close()
		}()
		ctx, cancel := context.WithCancel(context.Background())
		c.cancel = cancel
		for {
			if err := c.consumerGroup.Consume(ctx, c.config.GetTopics(), c.consumerHandler); err != nil {
				if err == context.Canceled {
					l4g.Infof("Context cancelled, stopping consumer group %s", c.config.HandlerType.String())
				} else {
					l4g.Errorf("Error consuming messages: %s", err)
				}
				break
			}
		}
	}()
}

func (c *KafkaConsumer) Stop() {
	l4g.Info("kafka consumer closing")
	c.cancel()
	c.group.Stop()
	c.group.Wait()
	c.group.Finish()
	l4g.Info("kafka consumer close finish")
}
