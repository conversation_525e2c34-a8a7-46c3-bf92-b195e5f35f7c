package kafka_consumer

import (
	"app/protos/in/log"
	"app/tools/log-consumer/service/config"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
)

type Manager struct {
	group          *ctx.Group
	consumerGroups map[log.KAFKA_HANDLER_TYPE]*KafkaConsumer
	configI        config.ConfigInterface
}

func NewManager(group *ctx.Group, configI config.ConfigInterface) *Manager {
	return &Manager{
		group:          group,
		configI:        configI,
		consumerGroups: make(map[log.KAFKA_HANDLER_TYPE]*KafkaConsumer),
	}
}

func (m *Manager) Init() {
	kafkaConfig := m.configI.GetKafkaConfig()
	for _, kafka := range kafkaConfig.Kafkas {
		m.consumerGroups[kafka.HandlerType] = newKafkaConsumer(kafka, m.group.CreateChild(), m.configI)
	}
}

func (m *Manager) Run() {
	for _, consumerGroup := range m.consumerGroups {
		consumerGroup.Run()
	}
}

func (m *Manager) Close() {
	l4g.Info("kafka consumer manager closing")
	for _, consumerGroup := range m.consumerGroups {
		consumerGroup.Stop()
	}
	m.group.Stop()
	m.group.Wait()
	m.group.Finish()
	l4g.Info("kafka consumer manager close finish")
}
