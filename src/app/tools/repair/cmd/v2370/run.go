package v2370

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
	"sort"
)

var (
	flagDataPath               = flag.String("v2370ConfigData", "../data/", "service data path")
	fixSelectSummonFailedCount uint32
	fixDropFailedCount         uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		/*summonInfo := xmlcfg.V2370SelectSummonInfoM.Index(uid)
		if summonInfo != nil {
			fixSelectSummon(uid, redisClient, idFactory, summonInfo.RedGurantee)
		}*/

		DropInfo := xmlcfg.V2370DropInfoM.Index(uid)
		if DropInfo != nil {
			fixDrop(uid, redisClient, idFactory, DropInfo)
		}

	})

	if fixDropFailedCount == 0 {
		l4g.Infof("[SUCCESS] fix DropFailed Success!")
	} else {
		l4g.Infof("[FAILED] DropFailed Failed, need check error log! fixDropFailedCount:%d", fixDropFailedCount)
	}
	l4g.Infof("finish repair")
}

func fixSelectSummon(uid string, r *redis.Redis, idFactory *id.Factory, redGuarantee uint32) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] fixSelectSummon get user error: %s %s", uid, err)
		fixSelectSummonFailedCount++
		return
	}

	if userData == nil || userData.Module7 == nil || userData.Module7.SelectSummon == nil || userData.Module7.SelectSummon.Summon == nil {
		fixSelectSummonFailedCount++
		return
	}

	var isChange bool
	if len(userData.Module7.SelectSummon.Summon.ColorGuarantee) >= 3 {
		userData.Module7.SelectSummon.Summon.ColorGuarantee[2] = redGuarantee
		isChange = true
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save fixSelectSummon error: %s %s", uid, userData, reply.Err)
			fixSelectSummonFailedCount++
		}
	}
	return
}

type HeroUidCount struct {
	HeroUid uint64
	Count   uint32
}

func fixDrop(uid string, r *redis.Redis, idFactory *id.Factory, dropInfo *xmlcfg.DropInfo) {
	u := base.LoadUser(uid, r)
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] fixDrop get user error: %s %s", uid, err)
		fixDropFailedCount++
		return
	}

	//hero
	r.RopCl.GetAllHeroStarUpCostsMCallSKs(uid)
	//英雄升星消耗
	heroesStarUpData, err := r.RopCl.GetSomeHeroStarUpCostsByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("user: %s, get heroes start cost error: %s", uid, err)
		fixDropFailedCount++
		return
	}

	heroBodys, err := r.RopCl.GetAllHeroBodySKs(uid)
	if err != nil {
		l4g.Errorf("user: %s, get heroes error: %s", uid, err)
		fixDropFailedCount++
		return
	}

	heroStarUpData := make(map[uint64]map[uint32]uint32)
	HeroSysIDHeroUidCount := make(map[uint32][]*HeroUidCount)
	for _, hsdata := range heroesStarUpData {
		hdata, exist := heroBodys[hsdata.Id]
		if !exist {
			l4g.Errorf("user:%s get hero:%d not exist", uid, hsdata.Id)
			continue
		}
		heroCount := calcReturnHeroes(hdata, hsdata)
		heroStarUpData[hsdata.Id] = heroCount
	}
	l4g.Infof("user:%s heroStarUpData:%v", uid, heroStarUpData)

	for heroUid, heroSysIDCount := range heroStarUpData {
		for heroSysID, Count := range heroSysIDCount {
			_, exist := HeroSysIDHeroUidCount[heroSysID]
			if !exist {
				HeroSysIDHeroUidCount[heroSysID] = []*HeroUidCount{}
			}
			HeroSysIDHeroUidCount[heroSysID] = append(HeroSysIDHeroUidCount[heroSysID], &HeroUidCount{
				HeroUid: heroUid,
				Count:   Count,
			})
		}
	}

	deleteHero := make(map[uint64]struct{})
	needReviveHero := make(map[uint64]struct{})
	var bagChange bool
	for heroID, reduceCount := range dropInfo.FixHero {
		if reduceCount == 0 {
			continue
		}
		rCount := reduceCount
		// 先检查背包碎片
		heroInfo := goxml.GetData().HeroInfoM.Index(heroID)
		if heroInfo != nil {
			fragmentInfo := goxml.GetData().FragmentInfoM.Index(heroInfo.FragmentId)
			if fragmentInfo != nil {
				var fragmentReduceCount uint32
				for i := 0; i < int(rCount); i++ {
					if userData.Bag.Fragments[fragmentInfo.Id] >= fragmentInfo.Count {
						fragmentReduceCount++
						userData.Bag.Fragments[fragmentInfo.Id] -= fragmentInfo.Count
					} else {
						break
					}
				}
				rCount -= fragmentReduceCount
				l4g.Infof("user:%s reduce hero:%d by fragment Hero count:%d ", uid, heroID, fragmentReduceCount)
				bagChange = true
			}
		}

		if rCount == 0 {
			continue
		}

		// 过滤出指定英雄星级从低到高排列
		needDeleteHero := make([]*cl.HeroBody, 0, 1)
		for _, heroBody := range heroBodys {
			if heroBody.SysId == heroID {
				needDeleteHero = append(needDeleteHero, heroBody)
			}
		}

		sort.Slice(needDeleteHero, func(i, j int) bool {
			return needDeleteHero[i].Star < needDeleteHero[j].Star
		})

		defaultStar := uint32(5)
		// 从默认星级里找
		for _, heroBody := range needDeleteHero {
			// 默认星级直接删除
			if heroBody.Star == defaultStar {
				deleteHero[heroBody.Id] = struct{}{}
				rCount--
				//非材料英雄回退养成和上阵数据
				if heroBody.Tag != uint32(common.HERO_TAG_HT_MARTERIAL) {
					needReviveHero[heroBody.Id] = struct{}{}
				}
				l4g.Infof("user:%s reduce hero:%d by default Hero id:%d ", uid, heroID, heroBody.Id)
				if rCount == 0 {
					break
				}
			}
			if heroBody.Star > defaultStar {
				break
			}
		}

		if rCount == 0 {
			continue
		}

		for _, heroUidCount := range HeroSysIDHeroUidCount[heroID] {
			if rCount == 0 {
				break
			}
			heroData := u.HeroManager().Get(heroUidCount.HeroUid)
			var heroStar uint32
			if heroData != nil {
				heroStar = heroData.GetStar()
			}
			if heroUidCount.Count >= rCount {
				l4g.Infof("user:%s reduce heroID:%d star:%d hero:%d count:%d", uid, heroUidCount.HeroUid, heroStar, heroID, rCount)
				heroUidCount.Count -= rCount
				rCount = 0
				heroStarUpData[heroUidCount.HeroUid][heroID] = heroUidCount.Count
			} else {
				l4g.Infof("user:%s reduce heroID:%d star:%d hero:%d count:%d", uid, heroUidCount.HeroUid, heroStar, heroID, heroUidCount.Count)
				rCount -= heroUidCount.Count
				heroUidCount.Count = 0
				heroStarUpData[heroUidCount.HeroUid][heroID] = heroUidCount.Count
			}
			needReviveHero[heroUidCount.HeroUid] = struct{}{}
		}

		// 这种情况应该不存在了
		if rCount > 0 {
			l4g.Errorf("user:%s delete hero:%d failed left count:%d", uid, heroID, rCount)
		}
	}

	var retres []*cl.Resource
	var changeEmblem []*cl.EmblemInfo
	var changeEquipment []*cl.Equipment
	var changeJewelry []*cl.SeasonJewelry
	var deleteContract uint32
	for hid := range needReviveHero {
		heroData := heroBodys[hid]
		hero := &Hero{heroData}
		retres = append(retres, hero.calcReviveReturnRes(true)...)
		//脱下装备
		equipIDs := hero.RemoveAllEquip()
		for _, v := range equipIDs {
			equip := u.EquipManager().Get(v)
			if equip == nil {
				continue
			}
			equip.SetHid(0)
			changeEquipment = append(changeEquipment, equip.Data.Clone())
		}

		//脱下纹章
		var emblemIds []uint64
		emblemIds = hero.RemoveAllEmblem()
		for _, v := range emblemIds {
			emblem := u.EmblemManager().Get(v)
			if emblem == nil {
				continue
			}
			emblem.SetEmblemHid(0)
			changeEmblem = append(changeEmblem, emblem.Data.Clone())
		}

		//脱赛季装备。
		var jids []uint64
		jids = hero.RemoveAllSeasonJewelry()
		for _, v := range jids {
			je := u.SeasonJewelryManager().Get(v)
			if je == nil {
				continue
			}
			je.SetHid(0)
			changeJewelry = append(changeJewelry, je.Data.Clone())
		}

		for heroID, Count := range heroStarUpData[hid] {
			if Count > 0 {
				fragmentRes := goxml.GetData().FragmentInfoM.Hero2Fragment(heroID)
				if fragmentRes != nil {
					fragmentRes.Count *= Count
				}
				retres = append(retres, fragmentRes)
			}
		}

		u.HeroManager().Delete(hid, nil)
		if heroData.Tag == uint32(common.HERO_TAG_HT_CONTRACT) {
			deleteContract++
		}
	}

	updateFormations := make([]*cl.Formation, 0, 1)
	changedFids := fixFormation(u, needReviveHero)
	for fid := range changedFids {
		updateFormations = append(updateFormations, u.FormationManager().Get(fid).Clone())
	}

	var changeHero []*cl.HeroBody
	if deleteContract > 0 {
		heroes := u.HeroManager().GetAllCommonHero()
		sortHeroes := make([]*character.Hero, 0, len(heroes))
		for _, hero := range heroes {
			sortHeroes = append(sortHeroes, hero)
		}
		sort.Slice(sortHeroes, func(i, j int) bool {
			return sortHeroes[i].GetData().Star > sortHeroes[i].GetData().Star
		})
		for _, hero := range sortHeroes {
			if deleteContract == 0 {
				break
			}
			if hero.IsTagBlessed() {
				hero.SetTag(common.HERO_TAG_HT_CONTRACT)
				deleteContract--
				changeHero = append(changeHero, hero.GetData().Clone())
			}
		}
	}

	// 写入阵容数据
	if len(updateFormations) > 0 {
		r.RopCl.SetSomeFormationMCallSK(u.ID(), updateFormations)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s save formation error, count:%d, err:%s", uid, len(updateFormations), reply.Err)
			return
		}
	}

	// 写入装备数据
	if len(changeEquipment) > 0 {
		r.RopCl.SetSomeEquipmentMCallSK(u.ID(), changeEquipment)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s save equipment error, count:%d, err:%s", uid, len(changeEquipment), reply.Err)
			return
		}
	}

	// 写入符文数据
	if len(changeEmblem) > 0 {
		r.RopCl.SetSomeEmblemInfoMCallSK(u.ID(), changeEmblem)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s save emblem error, count:%d, err:%s", uid, len(changeEmblem), reply.Err)
			return
		}
	}

	if len(changeJewelry) > 0 {
		r.RopCl.SetSomeSeasonJewelryMCallSK(u.ID(), changeJewelry)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s save jewelry error, count:%d, err:%s", uid, len(changeJewelry), reply.Err)
			return
		}
	}

	if len(changeHero) > 0 {
		r.RopCl.SetSomeHeroBodyMCallSK(u.ID(), changeHero)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s save changeHero error, count:%d, err:%s", uid, len(changeHero), reply.Err)
			return
		}
	}

	allDeleteHid := make(map[uint64]struct{})
	for hid := range deleteHero {
		allDeleteHid[hid] = struct{}{}
	}

	for hid := range needReviveHero {
		allDeleteHid[hid] = struct{}{}
	}
	dHids := make([]uint64, 0, len(allDeleteHid))
	for hid := range allDeleteHid {
		dHids = append(dHids, hid)
	}

	if len(dHids) > 0 {
		r.RopCl.RemSomeHeroBodyMCallSK(u.ID(), dHids)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s del dHids error, count:%d, err:%s", uid, len(dHids), reply.Err)
			return
		}
	}

	if bagChange {
		r.RopDB.SetUserMCallSK(u.ID(), userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDropFailedCount++
			l4g.Errorf("user %s save user error, err:%s", uid, reply.Err)
			return
		}
	}

	if len(retres) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "英雄回退")
		params = append(params, fmt.Sprintf("以下是为您英雄回退的资源，请您及时查收。"))
		ret := sendMail(uid, r, idFactory, character.MailIDGM, retres, params)
		if !ret {
			fixDropFailedCount++
			l4g.Errorf("user: %s fixDrop: send mail failed.", uid)
		}
		l4g.Info("user %s save fixDrop.sendMail. success", uid)
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}

func fixFormation(user *character.User, deleteHeroes map[uint64]struct{}) map[uint32]struct{} {
	fm := user.FormationManager()
	changefids := make(map[uint32]struct{})
	for fid, formation := range fm.GetAll() {
		if formation == nil {
			l4g.Errorf("fixFormation, no formation, fid:%d, uid:%d", fid, user.ID())
			continue
		}

		emptyIndexs := make([]int, 0, 1)
		formationLeftHero := make([]uint64, 0, character.FormationMaxPos)
		for index, team := range formation.Teams {
			tmpInfo := make([]*cl.FormationInfo, 0, 1)
			for _, info := range team.Info {
				_, exist := deleteHeroes[info.Hid]
				if exist {
					//有删除的英雄不拷贝
					changefids[fid] = struct{}{}
					continue
				}
				tmpInfo = append(tmpInfo, info)
				formationLeftHero = append(formationLeftHero, info.Hid)
			}

			if len(team.Info) != len(tmpInfo) {
				formation.Teams[index].Info = tmpInfo
			}
			if len(tmpInfo) == 0 {
				emptyIndexs = append(emptyIndexs, index)
			}
		}

		// 处理不能为空阵容的自动上阵
		if goxml.GetData().FormationInfoM.IsNonemptyFormation(fid) && len(emptyIndexs) > 0 {
			heroM := user.HeroManager()
			for _, teamIndex := range emptyIndexs {
				team := user.GetFormationTeam(fid, teamIndex)
				if team == nil {
					continue
				}

				hero := heroM.FindOneBattleHero(formationLeftHero)
				if hero == nil {
					team.Info = nil
				} else {
					team.Info = make([]*cl.FormationInfo, 1)
					team.Info[0] = &cl.FormationInfo{
						Pos: 1,
						Hid: hero.GetHid(),
					}
					formationLeftHero = append(formationLeftHero, hero.GetHid())
				}
			}
		}
	}
	return changefids
}

func calcReturnHeroes(hData *cl.HeroBody, startCosts *cl.HeroStarUpCosts) map[uint32]uint32 {
	ret := make(map[uint32]uint32)
	ret[hData.SysId]++
	for heroSysID, Count := range startCosts.Star5Costs {
		ret[heroSysID] += Count
	}
	if len(startCosts.OtherStarCosts) > 0 {
		for _, subCosts := range startCosts.OtherStarCosts {
			calcStarUpCosts(subCosts, ret)
		}
	}
	return ret
}

func calcStarUpCosts(startUpCosts *cl.StarUpCosts, heroCount map[uint32]uint32) {
	heroCount[startUpCosts.SysId]++
	for heroSysID, Count := range startUpCosts.Star5Costs {
		heroCount[heroSysID] += Count
	}
	if len(startUpCosts.OtherStarCosts) > 0 {
		for _, subCosts := range startUpCosts.OtherStarCosts {
			calcStarUpCosts(subCosts, heroCount)
		}
	}
}

type Hero struct {
	data *cl.HeroBody
}

// 计算重生返还资源
func (h *Hero) calcReviveReturnRes(resetAwakenLevel bool) []*cl.Resource {
	var retRes []*cl.Resource
	if h.data.Stage > goxml.GetData().HeroStageInfoM.GetDefaultStage() {
		for i := goxml.GetData().HeroStageInfoM.GetDefaultStage(); i < h.data.Stage; i++ {
			sInfo := goxml.GetData().HeroStageInfoM.Index(i)
			if len(sInfo.Costs) > 0 {
				retRes = append(retRes, sInfo.Costs...)
			}
		}
	}

	if h.data.Level > goxml.GetData().HeroLevelInfoM.GetDefaultLv() {
		for i := goxml.GetData().HeroLevelInfoM.GetDefaultLv(); i < h.data.Level; i++ {
			lInfo := goxml.GetData().HeroLevelInfoM.Index(i)
			if len(lInfo.Costs) > 0 {
				retRes = append(retRes, lInfo.Costs...)
			}
		}
	}
	if resetAwakenLevel && h.data.AwakenLevel > 0 {
		var totalCount uint32
		heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
		if heroInfo != nil && heroInfo.AwakenValue > 0 {
			for i := uint32(1); i <= h.data.AwakenLevel; i++ {
				heroAwakenLevelInfo := goxml.GetData().HeroAwakenLevelInfoM.Index(i)
				if heroAwakenLevelInfo == nil {
					continue
				}
				totalCount += heroAwakenLevelInfo.Count
			}
			if totalCount > 0 {
				retRes = append(retRes, &cl.Resource{
					Type:  uint32(common.RESOURCE_ITEM),
					Value: heroInfo.AwakenValue,
					Count: totalCount,
				})
			}
		}
	}

	for _, gem := range h.data.Gems {
		for l := uint32(0); l < gem.Level; l++ {
			upgradeInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(gem.Slot, l)
			if upgradeInfo == nil {
				continue
			}
			retRes = append(retRes, upgradeInfo.CostClRes...)
		}
	}

	return retRes
}

func (h *Hero) RemoveAllEmblem() []uint64 {
	retEmblemID := make([]uint64, 0, len(h.data.Emblem))
	for k, v := range h.data.Emblem {
		retEmblemID = append(retEmblemID, v)
		delete(h.data.Emblem, k)
	}

	return retEmblemID
}

func (h *Hero) RemoveAllEquip() []uint64 {
	retEquipID := make([]uint64, 0, len(h.data.Equipment))
	for k, v := range h.data.Equipment {
		retEquipID = append(retEquipID, v)
		delete(h.data.Equipment, k)
	}

	return retEquipID
}

func (h *Hero) RemoveAllSeasonJewelry() []uint64 {
	retjID := make([]uint64, 0, len(h.data.SeasonJewelry))
	for pos, jid := range h.data.SeasonJewelry {
		retjID = append(retjID, jid)
		delete(h.data.Equipment, pos)
	}
	return retjID
}
