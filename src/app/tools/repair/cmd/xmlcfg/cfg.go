package xmlcfg

import (
	"embed"
	"fmt"
)

//go:embed v2370_drop_info.xml
//go:embed v2370_select_summon_info.xml
//go:embed v2380_season_map_master_info.xml

var f embed.FS
var OldEmblem *OldEmblemInfoManager
var OldEmblemLevel *OldEmblemLevelInfoManager
var OldEmblemBless *OldEmblemBlessingInfoManager
var V090Emblem *V090EmblemInfoManager
var V090EmblemLevel *V090EmblemLevelInfoManager
var V090EmblemBless *V090EmblemBlessingInfoManager
var GuildTalentTestInfoM *GuildTalentTestInfoManager
var V093HeroStageInfoM *V093HeroStageInfoManager
var V093EmblemLevelInfoM *V093EmblemLevelInfoManager
var V093EquipRefineInfoM *V093EquipRefineInfoManager
var V093EmblemInfoM *V093EmblemInfoManager
var V093EmblemBless *V093EmblemBlessingInfoManager
var V098GoddessContractBlessInfoM *V098GoddessContractBlessInfoManager
var V098EmblemInfoM *V098EmblemInfoManager
var TrialTaskInfoM *TrialTaskInfoManager
var V0140PassTaskM *V0130PassTaskInfoManager
var FixMiragePlayerListM *FixMiragePlayerListManager
var V015TowerSeasonTaskInfo *V150TowerSeasonTaskInfoManager
var V200TrialAchieveTaskInfoM *TrialAchieveTaskInfoManager
var V260TaskInfoM *TaskInfoManager
var FixTrialPlayerListM *FixTrialPlayerListManager
var V2220CrystalInfoM *V2220CrystalInfoManager
var V2240PassInfoM *V2240PassInfoManager
var V2240PassTaskInfoM *V2240PassTaskInfoManager
var V2350SeasonJewelryInfoM *V2350SeasonJewelryInfoManager
var V2380SeasonMapMasterInfoM *V2380SeasonMapMasterInfoManager
var V2370DropInfoM *V2370DropInfoManager
var V2370SelectSummonInfoM *V2370SelectSummonInfoManager

func Load() {
	v2370Drop, err := f.ReadFile("v2370_drop_info.xml")
	if err != nil {
		panic(fmt.Sprintf("read v2370_drop_info.xml failed. err: %s", err.Error()))
	}
	V2370DropInfoM = &V2370DropInfoManager{}
	V2370DropInfoM.Load(v2370Drop)

	v2370SelectSummon, err := f.ReadFile("v2370_select_summon_info.xml")
	if err != nil {
		panic(fmt.Sprintf("read v2370_select_summon_info.xml failed. err: %s", err.Error()))
	}
	V2370SelectSummonInfoM = &V2370SelectSummonInfoManager{}
	V2370SelectSummonInfoM.Load(v2370SelectSummon)
	v2380SeasonMapMaster, err := f.ReadFile("v2380_season_map_master_info.xml")
	if err != nil {
		panic(fmt.Sprintf("read v2380_season_map_master_info.xml failed. err: %s", err.Error()))
	}
	V2380SeasonMapMasterInfoM = &V2380SeasonMapMasterInfoManager{}
	V2380SeasonMapMasterInfoM.Load(v2380SeasonMapMaster)
}
