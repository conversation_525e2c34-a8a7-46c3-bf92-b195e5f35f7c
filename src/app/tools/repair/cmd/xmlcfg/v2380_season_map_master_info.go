package xmlcfg

import (
	"app/protos/out/cl"
	"encoding/xml"
)

type _ = cl.Resource

type SeasonMapMasterInfo struct {
	Id          uint32 `xml:"id,attr"`            //int:唯一id
	MasterId    uint32 `xml:"master_id,attr"`     //int:大师事件id
	TaskId      uint32 `xml:"task_id,attr"`       //int:大师任务id
	NextTaskId  uint32 `xml:"next_task_id,attr"`  //int:后置任务id
	GoodsType1  uint32 `xml:"goods_type_1,attr"`  //int:捐献货物1
	GoodsValve1 uint32 `xml:"goods_valve_1,attr"` //int:捐献货物1
	GoodsCount1 uint32 `xml:"goods_count_1,attr"` //int:捐献货物1
	GoodsType2  uint32 `xml:"goods_type_2,attr"`  //int:捐献货物2
	GoodsValve2 uint32 `xml:"goods_valve_2,attr"` //int:捐献货物2
	GoodsCount2 uint32 `xml:"goods_count_2,attr"` //int:捐献货物2
	GoodsType3  uint32 `xml:"goods_type_3,attr"`  //int:捐献货物3
	GoodsValve3 uint32 `xml:"goods_valve_3,attr"` //int:捐献货物3
	GoodsCount3 uint32 `xml:"goods_count_3,attr"` //int:捐献货物3
	TechId      uint32 `xml:"tech_id,attr"`       //int:奖励科技id
	TechLv      uint32 `xml:"tech_lv,attr"`       //int:奖励科技等级

	// goods_type_1 goods_valve_1 goods_count_1
	// goods_type_2 goods_valve_2 goods_count_2
	// goods_type_3 goods_valve_3 goods_count_3
	GoodsClRes []*cl.Resource `xml:"-"` //非xml原始字段
}

func (m *SeasonMapMasterInfo) prepare() {
	// 资源
	if m.GoodsType1 > 0 && m.GoodsCount1 > 0 {
		m.GoodsClRes = append(m.GoodsClRes, &cl.Resource{
			Type:  m.GoodsType1,
			Value: m.GoodsValve1,
			Count: m.GoodsCount1,
		})
	}
	if m.GoodsType2 > 0 && m.GoodsCount2 > 0 {
		m.GoodsClRes = append(m.GoodsClRes, &cl.Resource{
			Type:  m.GoodsType2,
			Value: m.GoodsValve2,
			Count: m.GoodsCount2,
		})
	}
	if m.GoodsType3 > 0 && m.GoodsCount3 > 0 {
		m.GoodsClRes = append(m.GoodsClRes, &cl.Resource{
			Type:  m.GoodsType3,
			Value: m.GoodsValve3,
			Count: m.GoodsCount3,
		})
	}
}

/*
func (m *SeasonMapMasterInfo) Check() error {
return nil
}
*/
type SeasonMapMasterInfos struct {
	Datas []*SeasonMapMasterInfo `xml:"data"`
}

type SeasonMapMasterInfoExt struct {
	levelRecordMap map[uint32]*SeasonMapMasterInfo
}

type V2380SeasonMapMasterInfoManager struct {
	Datas map[uint32]*SeasonMapMasterInfoExt
}

func (m *V2380SeasonMapMasterInfoManager) Load(datas []byte) {
	tmp := &SeasonMapMasterInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("v2380_season_map_master_info.xml Unmarshal failed")
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*SeasonMapMasterInfoExt, len(tmp.Datas))
	}

	for _, data := range tmp.Datas {
		data.prepare()
		if dataExt, exist := m.Datas[data.TechId]; exist {
			dataExt.levelRecordMap[data.TechLv] = data
		} else {
			dataExt = &SeasonMapMasterInfoExt{
				levelRecordMap: make(map[uint32]*SeasonMapMasterInfo),
			}
			dataExt.levelRecordMap[data.TechLv] = data
			m.Datas[data.TechId] = dataExt
		}
	}
}

func (m *V2380SeasonMapMasterInfoManager) GetTechLevelResources(techId, techLevel uint32) []*cl.Resource {
	levelMap, exist := m.Datas[techId]
	if !exist {
		return nil
	}
	info := levelMap.levelRecordMap[techLevel]
	if info == nil {
		return nil
	}
	return info.GoodsClRes
}
