package xmlcfg

//import "fmt"
import (
	"app/protos/out/cl"
	"encoding/xml"
)

// Reference imports to suppress errors if they are not otherwise used.
type _ = cl.Resource

const hero1sysID = 42020
const hero2sysID = 53017
const hero3sysID = 52016

type DropInfo struct {
	Id         string `xml:"id,attr"`          //
	Hero1Count uint32 `xml:"hero1_count,attr"` //
	Hero2Count uint32 `xml:"hero2_count,attr"` //
	Hero3Count uint32 `xml:"hero3_count,attr"` //
	FixHero    map[uint32]uint32
}

func (m *DropInfo) prepare() {
	m.FixHero = make(map[uint32]uint32)
	if m.Hero1Count > 0 {
		m.FixHero[hero1sysID] = m.Hero1Count
	}
	if m.Hero2Count > 0 {
		m.FixHero[hero2sysID] = m.Hero2Count
	}
	if m.Hero3Count > 0 {
		m.FixHero[hero3sysID] = m.Hero3Count
	}
}

func (m *DropInfo) Check() error {
	return nil
}

type DropInfos struct {
	Datas []*DropInfo `xml:"data"`
}

type V2370DropInfoManager struct {
	Datas map[string]*DropInfo
}

func (m *V2370DropInfoManager) Load(datas []byte) {
	tmp := &DropInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("v2370_drop_info.xml Unmarshal failed")
	}
	if m.Datas == nil {
		m.Datas = make(map[string]*DropInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
}

func (m *V2370DropInfoManager) Index(id string) *DropInfo {
	return m.Datas[id]
}
