package xmlcfg

//import "fmt"
import (
	"app/protos/out/cl"
	"encoding/xml"
)

// Reference imports to suppress errors if they are not otherwise used.
type _ = cl.Resource

type SelectSummonInfo struct {
	Id          string `xml:"id,attr"`           //装备id
	RedGurantee uint32 `xml:"red_gurantee,attr"` //回收评分
}

func (m *SelectSummonInfo) prepare() {}

func (m *SelectSummonInfo) Check() error {
	return nil
}

type SelectSummonInfos struct {
	Datas []*SelectSummonInfo `xml:"data"`
}

type V2370SelectSummonInfoManager struct {
	Datas map[string]*SelectSummonInfo
}

func (m *V2370SelectSummonInfoManager) Load(datas []byte) {
	tmp := &SelectSummonInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("v2370_select_summon_info.xml Unmarshal failed")
	}
	if m.Datas == nil {
		m.Datas = make(map[string]*SelectSummonInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
}

func (m *V2370SelectSummonInfoManager) Index(id string) *SelectSummonInfo {
	return m.Datas[id]
}
