package v2380

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/crossRedis"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"
	"slices"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath              = flag.String("v2380ConfigData", "../data/", "service data path")
	fixTalentTreeFailedCount  uint32
	fixSeasonMapFailedCount   uint32
	fixSeasonPowerFailedCount uint32
	talentTreeDeleteIds       = []uint32{1103101, 1103102, 1103501, 1103201, 1103301, 1103401, 1103402, 1103403, 1103404, 1103405, 1103406, 1103407, 1103408, 1103601, 1103701, 1103702, 1103703, 1103704, 1103705}
	currentSeasonID           uint32
	gstSufKey                 uint64
	gstDefaultFormationNum    = 6
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, gstPartition, arenaPartition uint32) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	gstCrossRedisAddr, err := cmd.Flags().GetString("gstCrossRedisAddr")
	if err != nil {
		panic(fmt.Sprintf("gstCrossRedisAddr err:%s", err))
	}
	gstCrossRedisIndex, err := cmd.Flags().GetInt("gstCrossRedisIndex")
	if err != nil {
		panic(fmt.Sprintf("gstCrossRedisIndex err:%s", err))
	}
	gstCrossRedis := crossRedis.OpenCrossRedis(gstCrossRedisAddr, uint32(gstCrossRedisIndex))
	if gstCrossRedis == nil {
		panic(fmt.Sprintf("open gst cross redis failed crossRedisAddr:%s crossRedisIndex:%d", gstCrossRedisAddr, gstCrossRedisIndex))
	}
	seasonArenaCrossRedisAddr, err := cmd.Flags().GetString("seasonArenaCrossRedisAddr")
	if err != nil {
		panic(fmt.Sprintf("gstCrossRedisAddr err:%s", err))
	}
	seasonArenaCrossRedisIndex, err := cmd.Flags().GetInt("seasonArenaCrossRedisIndex")
	if err != nil {
		panic(fmt.Sprintf("gstCrossRedisAddr err:%s", err))
	}
	seasonArenaCrossRedis := crossRedis.OpenCrossRedis(seasonArenaCrossRedisAddr, uint32(seasonArenaCrossRedisIndex))
	if seasonArenaCrossRedis == nil {
		panic(fmt.Sprintf("open seasonArena cross redis failed crossRedisAddr:%s crossRedisIndex:%d", seasonArenaCrossRedisAddr, seasonArenaCrossRedisIndex))
	}

	now := time.Now().Unix()
	currentSeasonID = goxml.GetCurrentSeasonID(goxml.GetData(), now)
	gstSufKey = uint64(currentSeasonID)<<32 | uint64(gstPartition)
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixTalentTree(uid, redisClient, idFactory, gstCrossRedis)
		fixSeasonMap(uid, redisClient, idFactory)
		fixSeasonPower(uid, redisClient, idFactory, gstCrossRedis, seasonArenaCrossRedis, arenaPartition)
	})

	fixTalentTreeHot(redisClient, idFactory)

	if fixTalentTreeFailedCount == 0 {
		l4g.Infof("[SUCCESS] fix fixTalentTree Success!")
	} else {
		l4g.Infof("[FAILED] fixTalentTree Failed, need check error log! fixTalentTreeFailedCount:%d", fixTalentTreeFailedCount)
	}
	if fixSeasonMapFailedCount == 0 {
		l4g.Infof("[SUCCESS] fix fixSeasonMap Success!")
	} else {
		l4g.Infof("[FAILED] fixSeasonMap Failed, need check error log! fixSeasonMapFailedCount:%d", fixSeasonMapFailedCount)
	}
	if fixSeasonPowerFailedCount == 0 {
		l4g.Infof("[SUCCESS] fix fixSeasonPower Success!")
	} else {
		l4g.Infof("[FAILED] fixSeasonPower Failed, need check error log! fixSeasonPowerFailedCount:%d", fixSeasonPowerFailedCount)
	}
	l4g.Infof("finish repair")
}

func fixTalentTree(uid string, r *redis.Redis, idFactory *id.Factory, gstCrossR *crossRedis.CrossRedis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixTalentTreeFailedCount++
		return
	}
	if userData == nil {
		l4g.Error("user %s fixTalentTree: data corrupt", uid)
		fixTalentTreeFailedCount++
		return
	}

	formations, err := r.RopCl.GetAllFormationSKs(uid)
	if err != nil {
		l4g.Error("user %s fixTalentTree: get formations data error:%s", uid, err)
		fixTalentTreeFailedCount++
		return
	}

	awards := make(map[uint32]uint32)
	if userData.ModuleGlobalAttr != nil && userData.ModuleGlobalAttr.TalentTreeCul != nil {
		cul := userData.ModuleGlobalAttr.TalentTreeCul
		for _, deleteId := range talentTreeDeleteIds {
			level, exist := cul.Levels[deleteId]
			if !exist {
				continue
			}
			for i := uint32(1); i <= level; i++ {
				resources := xmlcfg.V2380SeasonMapMasterInfoM.GetTechLevelResources(deleteId, i)
				if resources == nil || len(resources) == 0 {
					l4g.Error("user %s fixTalentTree: resource corrupt. nodeId:%d level:%d", uid, deleteId, i)
					fixTalentTreeFailedCount++
					return
				}
				for _, resource := range resources {
					if resource.Type != uint32(common.RESOURCE_SEASON_MAP_GOODS) {
						l4g.Error("user %s fixTalentTree: resource type error. nodeId:%d level:%d type:%d", uid, deleteId, i, resource.Type)
						fixTalentTreeFailedCount++
						return
					}
					awards[resource.Value] += resource.Count
				}
			}
			delete(cul.Levels, deleteId)
		}
	}

	var taskAwardNum uint32
	if userData.Module6 != nil && userData.Module6.TalentTree != nil {
		taskNum := len(userData.Module6.TalentTree.Awarded)
		taskAwardNum = uint32(taskNum * 16)
	}

	if len(awards) == 0 && taskAwardNum == 0 {
		return
	}

	// 返还升级消耗
	if len(awards) > 0 {
		if userData.Bag.SeasonMapBag == nil {
			userData.Bag.SeasonMapBag = &db.SeasonMapBag{}
		}
		if userData.Bag.SeasonMapBag.Goods == nil {
			userData.Bag.SeasonMapBag.Goods = make(map[uint32]uint32)
		}
		for awardId, count := range awards {
			userData.Bag.SeasonMapBag.Goods[awardId] += count
		}
	}

	// 返还任务补偿
	if taskAwardNum > 0 {
		userData.Bag.Items[10153] += uint32(taskAwardNum)
	}

	if formation, exist := formations[uint32(common.FORMATION_ID_FI_GST)]; exist {
		if len(formation.Teams) > gstDefaultFormationNum {
			formation.Teams = formation.Teams[0:gstDefaultFormationNum]
			gstGuildUser, err := gstCrossR.RopCr.GetGSTGuildUserManagerSK(gstSufKey, userData.Id)
			if err != nil {
				fixTalentTreeFailedCount++
				l4g.Errorf("user %s fixTalentTree: getGstGuildUser err: %s", uid, err)
				return
			}
			if gstGuildUser != nil && gstGuildUser.User != nil {
				totalTeams := len(gstGuildUser.User.Teams)
				if totalTeams > gstDefaultFormationNum {
					if gstGuildUser.User.Info.GuildId != 0 {
						userGuild, err := gstCrossR.RopCr.GetGSTGuildManagerSK(gstSufKey, gstGuildUser.User.Info.GuildId)
						if err != nil {
							fixTalentTreeFailedCount++
							l4g.Errorf("user %s fixTalentTree: getGstGuild err: %s", uid, err)
							return
						}
						if userGuild.Guild != nil && userGuild.Guild.GetGroupId() != 0 {
							userGroup, err := gstCrossR.RopCr.GetGSTGroupManagerSK(gstSufKey, userGuild.Guild.GetGroupId())
							if err != nil {
								fixTalentTreeFailedCount++
								l4g.Errorf("user %s fixTalentTree: getGstGuild err: %s", uid, err)
								return
							}
							if userGroup.Info != nil && userGroup.Info.MapInfo != nil &&
								userGroup.Info.MapInfo.GSTGroundInfos != nil {
								groupChanged := false
								for i := totalTeams; i > gstDefaultFormationNum; i-- {
									team := gstGuildUser.User.Teams[i-1]
									if team.Id != 0 {
										// 从地块中移除队伍
										ground := userGroup.Info.MapInfo.GSTGroundInfos[team.Id]
										if ground != nil {
											for _, fightTeams := range ground.FightTeams {
												fightTeams.Teams = slices.DeleteFunc(fightTeams.Teams, func(fightTeam *cl.GSTFightTeam) bool {
													if fightTeam.User.Id == userData.Id && fightTeam.TeamIndex == team.TeamIndex {
														groupChanged = true
														return true
													}
													return false
												})
											}
										}
									}
								}
								if groupChanged {
									err := gstCrossR.RopCr.SetSomeGSTGroupManagerSK(gstSufKey, []*cr.GSTGroupManager{userGroup})
									if err != nil {
										fixTalentTreeFailedCount++
										l4g.Errorf("user %s fixTalentTree: SetSomeGSTGroup err: %s", uid, err)
										return
									}
								}
							}
						}
					}

					gstGuildUser.User.Teams = gstGuildUser.User.Teams[0:gstDefaultFormationNum]
					err := gstCrossR.RopCr.SetSomeGSTGuildUserManagerSK(gstSufKey, []*cr.GSTGuildUserManager{gstGuildUser})
					if err != nil {
						fixTalentTreeFailedCount++
						l4g.Errorf("user %s fixTalentTree: set getGstGuildUser err: %s", uid, err)
						return
					}
				}
			}
			err = r.RopCl.SetSomeFormationSK(userData.Id, []*cl.Formation{formation})
			if err != nil {
				fixTalentTreeFailedCount++
				l4g.Errorf("user %s fixTalentTree: set SetSomeFormationSK err: %s", uid, err)
				return
			}
		}
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixTalentTreeFailedCount++
		l4g.Errorf("user %s fixTalentTree: save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s fixTalentTree: save success", uid)
}

func fixTalentTreeHot(r *redis.Redis, idFactory *id.Factory) {
	hotData, err := r.RopDB.GetLogicTalentTreeHot()
	if err != nil {
		l4g.Error("fixTalentTreeHot: get hot data err:%s")
		fixTalentTreeFailedCount++
		return
	}
	if hotData.Data == nil || len(hotData.Data.Nodes) == 0 {
		return
	}

	for _, deleteId := range talentTreeDeleteIds {
		for i, node := range hotData.Data.Nodes {
			if deleteId == node.Id {
				hotData.Data.Nodes = append(hotData.Data.Nodes[:i], hotData.Data.Nodes[i+1:]...)
				break
			}
		}
	}

	err = r.RopDB.SetLogicTalentTreeHot(hotData)
	if err != nil {
		l4g.Error("fixTalentTreeHot: set hot data err:%s")
		fixTalentTreeFailedCount++
		return
	}
}

func fixSeasonMap(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixSeasonMapFailedCount++
		return
	}
	if userData == nil {
		l4g.Error("user %s fixSeasonMap: data corrupt", uid)
		fixSeasonMapFailedCount++
		return
	}
	if userData.Module8 == nil || userData.Module8.SeasonMap == nil {
		return
	}
	var change bool
	for position, data := range userData.Module8.SeasonMap.PositionData {
		if data.EventData == nil {
			continue
		}
		eventData, exist := data.EventData[uint32(cl.SeasonMapEventType_EventType_Master)]
		if !exist {
			continue
		}
		if eventData == nil || eventData.MasterEvent == nil {
			continue
		}
		info := goxml.GetData().SeasonMapPositionInfoM.Index(position)
		if info == nil {
			l4g.Error("user %s fixSeasonMap: position:%d info is nil.", uid, position)
			fixSeasonMapFailedCount++
			return
		}
		var eventTypeId uint32
		for _, event := range info.Events {
			if event.EventType == cl.SeasonMapEventType_EventType_Master {
				eventTypeId = event.EventTypeId
				break
			}
		}
		if eventTypeId == 0 {
			continue
		}

		masterInfo := goxml.GetData().SeasonMapMasterInfoM.Index(eventTypeId)
		if masterInfo == nil {
			l4g.Error("user %s fixSeasonMap: masterInfo is nil. eventTypeId:%d", uid, eventTypeId)
			fixSeasonMapFailedCount++
			return
		}
		eventData.MasterEvent.TaskId = masterInfo.Tasks[0].TaskId
		change = true
	}
	if !change {
		return
	}
	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixSeasonMapFailedCount++
		l4g.Errorf("user %s fixSeasonMap: save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s fixSeasonMap: save success", uid)
}

func fixSeasonPower(uid string, r *redis.Redis, idFactory *id.Factory, gstCrossR *crossRedis.CrossRedis,
	seasonArenaCrossR *crossRedis.CrossRedis, arenaPartition uint32) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixSeasonPowerFailedCount++
		return
	}
	if userData == nil {
		l4g.Error("user %s fixSeasonPower: data corrupt", uid)
		fixSeasonPowerFailedCount++
		return
	}

	seasonID := userData.Base.SeasonId
	if seasonID == 0 {
		return
	}
	if seasonID != currentSeasonID {
		return
	}

	heroesData, err := r.RopCl.GetAllHeroBodySKs(uid)
	if err != nil {
		l4g.Error("user %s fixSeasonPower: get hero data error:%s", uid, err)
		fixSeasonPowerFailedCount++
		return
	}

	seasonJewelry, err := r.RopCl.GetAllSeasonJewelrySKs(uid)
	if err != nil {
		l4g.Error("user %s fixSeasonPower: get seasonJewelry data error:%s", uid, err)
		fixSeasonPowerFailedCount++
		return
	}

	artifacts, err := r.RopCl.GetAllArtifactSKs(uid)
	if err != nil {
		l4g.Error("user %s fixSeasonPower: get artifacts data error:%s", uid, err)
		fixSeasonPowerFailedCount++
		return
	}

	formations, err := r.RopCl.GetAllFormationSKs(uid)
	if err != nil {
		l4g.Error("user %s fixSeasonPower: get formations data error:%s", uid, err)
		fixSeasonPowerFailedCount++
		return
	}

	seasonPowerStract := &seasonPowerCalc{
		heroes:        heroesData,
		artifacts:     artifacts,
		seasonJewelry: seasonJewelry,
		userData:      userData,
	}

	seasonPowerStract.init()

	seasonPower := seasonPowerStract.calcSeasonPower()
	if userData.Base.SeasonPower != seasonPower {
		userData.Base.SeasonPower = seasonPower
		if seasonPower > userData.Base.SeasonTopPower {
			userData.Base.SeasonTopPower = seasonPower
		}
	}

	gstLogicUser, err := r.RopDB.GetGstLogicUser(userData.Id)
	if gstLogicUser != nil {
		gstUser, err := gstCrossR.RopCr.GetGSTGuildUserManagerSK(gstSufKey, userData.Id)
		if err != nil {
			l4g.Error("user %s fixSeasonPower: get gstGuildUser data error:%s", uid, err)
			fixSeasonPowerFailedCount++
			return
		}
		if gstUser != nil && gstUser.User != nil {
			gstUser.User.Info.SeasonAdd = seasonPowerStract.GetSeasonAddData()
			formation := formations[uint32(common.FORMATION_ID_FI_GST)]
			if formation != nil {
				indexToPower := make(map[int]int64, len(formation.Teams))
				for index, team := range formation.Teams {
					power := int64(0)
					for _, info := range team.Info {
						power += seasonPowerStract.calcHeroSeasonPower(info.Hid)
					}
					if power > 0 {
						indexToPower[index] = power
					}
				}
				for _, team := range gstUser.User.Teams {
					power, exist := indexToPower[int(team.TeamIndex)]
					if exist {
						team.Power = power
					}
				}
			}
			err := gstCrossR.RopCr.SetSomeGSTGuildUserManagerSK(gstSufKey, []*cr.GSTGuildUserManager{gstUser})
			if err != nil {
				l4g.Error("user %s fixSeasonPower: set gstGuildUser data error:%s", uid, err)
				fixSeasonPowerFailedCount++
				return
			}
		}
	}

	seasonArenLogicUser, err := r.RopDB.GetSeasonArenaUser(userData.Id)
	if seasonArenLogicUser != nil {
		seasonArenaUser, err := seasonArenaCrossR.RopCr.GetCrossSeasonArenaUserSK(arenaPartition, userData.Id)
		if err != nil {
			l4g.Error("user %s fixSeasonPower: get crossSeasonArenaUser data error:%s", uid, err)
			fixSeasonPowerFailedCount++
			return
		}
		if seasonArenaUser != nil && seasonArenaUser.Snapshot != nil {
			seasonArenaUser.Snapshot.SeasonPower = userData.Base.SeasonPower
			seasonArenaUser.Snapshot.SeasonTopPower = userData.Base.SeasonTopPower
			formationID := uint32(0)
			divisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(seasonArenLogicUser.GetScore(), seasonArenLogicUser.GetRank())
			if divisionInfo == nil {
				l4g.Errorf("user:%d SeasonArena: score:%d rank:%d division info is nil.", userData.Id, seasonArenLogicUser.GetScore(), seasonArenLogicUser.GetRank())
			} else {
				formationID = divisionInfo.TeamNum
			}
			if formationID > 0 {
				defenseFormation := formations[formationID]
				if defenseFormation != nil {
					defensePower := int64(0)
					for _, team := range defenseFormation.Teams {
						for _, info := range team.Info {
							defensePower += seasonPowerStract.calcHeroSeasonPower(info.Hid)
						}
					}
					if defensePower > 0 {
						seasonArenaUser.Snapshot.DefensePower = defensePower
						if userData.Base != nil && userData.Base.DefensePower != nil {
							userData.Base.DefensePower[formationID] = defensePower
						}
					}
				}
			}
			err := seasonArenaCrossR.RopCr.SetSomeCrossSeasonArenaUserSK(arenaPartition, []*cr.CrossSeasonArenaUser{seasonArenaUser})
			if err != nil {
				l4g.Error("user %s fixSeasonPower: set crossSeasonArenaUser data error:%s", uid, err)
				fixSeasonPowerFailedCount++
				return
			}
		}
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixSeasonMapFailedCount++
		l4g.Errorf("user %s fixSeasonPower: save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s fixSeasonPower: save success", uid)
}

func (c *seasonPowerCalc) GetSeasonAddData() []*cl.SeasonAddInfo {
	seasonID := c.userData.Base.SeasonId
	if seasonID == 0 {
		return nil
	}
	oneSeasonAddInfo := goxml.GetData().SeasonAddInfoM.GetOneSeasonAddInfo(seasonID)
	if oneSeasonAddInfo == nil {
		return nil
	}

	seasonAddInfos := make([]*cl.SeasonAddInfo, 0, len(oneSeasonAddInfo.SysIdData))

	for handbookType, sysIds := range oneSeasonAddInfo.SysIdData {
		for _, sysId := range sysIds {
			addInfo := c.generateSeasonAddInfo(handbookType, sysId)
			if addInfo == nil {
				continue
			}
			seasonAddInfos = append(seasonAddInfos, addInfo)
		}
	}
	return seasonAddInfos
}

func (c *seasonPowerCalc) generateSeasonAddInfo(handbookType, sysId uint32) *cl.SeasonAddInfo {
	star, emblemExclusiveLv := c.seasonAddGetMaxParams(handbookType, sysId)
	switch handbookType {
	case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar:
		if star == 0 {
			return nil
		}
		return &cl.SeasonAddInfo{Type: handbookType, SysId: sysId, Star: star}
	case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
		if emblemExclusiveLv == 0 {
			return nil
		}
		return &cl.SeasonAddInfo{Type: handbookType, SysId: sysId, EmblemExclusiveLv: emblemExclusiveLv}
	default:
		return nil
	}
}

type seasonPowerCalc struct {
	heroes           map[uint64]*cl.HeroBody
	seasonJewelry    map[uint64]*cl.SeasonJewelry
	artifacts        map[uint32]*cl.Artifact
	userData         *db.User
	seasonLevelPower int64
	remainBookPower  int64
	talentTreePower  int64
	seasonAddPower   int64
}

func (c *seasonPowerCalc) init() {

	c.seasonLevelPower = c.getSeasonLevelPower()
	c.remainBookPower = c.getRemainBookPower()
	c.talentTreePower = c.getTalentTreePower()
	c.seasonAddPower = c.getSeasonAddPower()
}

func (c *seasonPowerCalc) getSeasonLevelPower() int64 {
	levelInfo := goxml.GetData().SeasonLevelInfoM.GetLevelInfo(c.userData.Base.SeasonId, c.userData.Base.SeasonLv)

	if levelInfo == nil {
		l4g.Errorf("user: %d levelInfo not exist. seasonID:%d level:%d",
			c.userData.Id, c.userData.Base.SeasonId, c.userData.Base.SeasonLv)
		return 0
	}
	return levelInfo.Power
}

func (c *seasonPowerCalc) getRemainBookPower() int64 {
	if c.userData.ModuleGlobalAttr == nil || c.userData.ModuleGlobalAttr.RemainBook == nil {
		return 0
	}
	remainBook := c.userData.ModuleGlobalAttr.RemainBook
	blessInfo := goxml.GetData().RemainBlessInfoM.Index(remainBook.Level)
	if blessInfo != nil {
		return blessInfo.Power
	}
	return 0
}

func (c *seasonPowerCalc) getTalentTreePower() int64 {
	if c.userData.ModuleGlobalAttr == nil || c.userData.ModuleGlobalAttr.TalentTreeCul == nil {
		return 0
	}

	rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(c.userData.Base.SeasonId)
	lv := c.userData.ModuleGlobalAttr.TalentTreeCul.Levels[rootId]
	info := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(rootId, lv)
	if info == nil {
		return 0
	}
	return int64(info.Power)
}

func (c *seasonPowerCalc) calcSeasonPower() int64 {
	power := int64(0)
	for _, body := range c.heroes {
		if body.Tag == uint32(common.HERO_TAG_HT_CONTRACT) || body.Tag == uint32(common.HERO_TAG_HT_BLESSED) {
			power += c.calcHeroSeasonPower(body.Id)
		}
	}
	return power
}

func (c *seasonPowerCalc) calcHeroSeasonPower(hid uint64) int64 {
	heroBody := c.heroes[hid]
	if heroBody == nil {
		return 0
	}
	heroBalanceInfo := goxml.GetData().HeroBalanceInfoM.Index(heroBody.SysId, heroBody.Star)
	if heroBalanceInfo == nil {
		l4g.Errorf("user:%d calcHeroSeasonPower: getHeroBalanceInfo nil. sysId:%d hero star:%d",
			c.userData.GetId(), heroBody.SysId, heroBody.Star)
		return 0
	}
	monsterInfo := goxml.GetData().MonsterInfoM.Index(heroBalanceInfo.MonsterId)
	if monsterInfo == nil {
		l4g.Errorf("user:%d GetHeroSeasonPower: get MonsterInfo nil. id:%d", c.userData.GetId(), heroBalanceInfo.MonsterId)
		return 0
	}
	power := int64(monsterInfo.GetPower(goxml.GetData()))
	power += int64(c.GetSeasonSkillPower(heroBody))
	power += int64(c.GetHeroSeasonJewelryPower(heroBody))
	power += c.seasonLevelPower
	power += c.remainBookPower
	power += c.talentTreePower
	power += c.seasonAddPower
	return power
}

func (c *seasonPowerCalc) GetHeroSeasonJewelryPower(heroBody *cl.HeroBody) uint32 {
	if c.seasonJewelry == nil {
		return 0
	}
	addPower := uint32(0)
	for _, jewelryId := range heroBody.SeasonJewelry {
		jewelry := c.seasonJewelry[jewelryId]
		if jewelry == nil {
			continue
		}
		info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelry.GetSysId())
		if info == nil {
			continue
		}
		addPower += info.Power
	}
	return addPower
}

func (c *seasonPowerCalc) GetSeasonSkillPower(heroBody *cl.HeroBody) uint32 {
	info := goxml.GetData().HeroInfoM.Index(heroBody.GetSysId())
	if info == nil {
		return 0
	}

	if currentSeasonID != info.SeasonIdForSkill {
		return 0
	}

	if info.SeasonSkill1 == 0 {
		return 0
	}

	levelInfo := goxml.GetData().SkillLevelInfoM.Index(info.SkillLevelID, heroBody.GetStar())
	if levelInfo == nil {
		return 0
	}

	skill := goxml.GetData().SkillInfoM.GroupLevel(info.SeasonSkill1, levelInfo.SeasonSkill1Level)
	if skill == nil {
		return 0
	}
	return skill.Power
}

func (c *seasonPowerCalc) getSeasonAddPower() int64 {
	oneSeasonAddInfo := goxml.GetData().SeasonAddInfoM.GetOneSeasonAddInfo(c.userData.Base.SeasonId)
	if oneSeasonAddInfo == nil {
		return 0
	}
	power := int64(0)
	for _, infos := range oneSeasonAddInfo.OneTypeData {
		for handbookType, infosByHandbookType := range infos.Data {
			for sysId, addInfos := range infosByHandbookType {
				maxStar, emblemExclusiveLv := c.seasonAddGetMaxParams(handbookType, sysId)
				if maxStar == 0 && emblemExclusiveLv == 0 {
					continue
				}
				for _, info := range addInfos {
					needAddPower := false
					switch handbookType {
					case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar:
						if info.Star <= maxStar {
							needAddPower = true
						}
					case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
						if info.EmblemLevel <= emblemExclusiveLv {
							needAddPower = true
						}
					default:
						break
					}
					if needAddPower {
						power += int64(info.Power)
					}
				}
			}
		}
	}
	return power
}

func (c *seasonPowerCalc) seasonAddGetMaxParams(handbookType, sysId uint32) (uint32, uint32) {
	switch handbookType {
	case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
		if c.userData.ModuleGlobalAttr == nil || c.userData.ModuleGlobalAttr.HandbooksData == nil {
			return 0, 0
		}
		var heroHandbook *cl.HeroHandbook
		for _, handbook := range c.userData.ModuleGlobalAttr.HandbooksData.Heroes {
			if handbook.SysId == sysId {
				heroHandbook = handbook
				break
			}
		}
		if heroHandbook == nil {
			return 0, 0
		}
		linkBookTaskInfo := goxml.GetData().LinkBookTaskInfoM.GetRecordById(heroHandbook.Link_1StarAttr)
		if linkBookTaskInfo == nil {
			return 0, 0
		}
		return linkBookTaskInfo.Value, heroHandbook.EmblemExclusiveLv
	case goxml.SeasonAddHandbookArtifactStar:
		artifact := c.artifacts[sysId]
		if artifact == nil {
			return 0, 0
		}
		return artifact.GetStar(), 0
	default:
		l4g.Errorf("user:%d SeasonAddGetStar: handbookType error. handbookType:%d", c.userData.Id, handbookType)
	}
	return 0, 0
}
