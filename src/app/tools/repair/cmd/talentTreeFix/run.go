package talentTreeFix

import (
	"app/goxml"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath             = flag.String("talentTreeConfigData", "../data/", "service data path")
	fixTalentTreeFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixTalentTree(uid, redisClient, idFactory)
	})

	fixLogicTalentTreeHot(redisClient, idFactory)

	if fixTalentTreeFailedCount == 0 {
		l4g.Infof("[SUCCESS] fix fixTalentTree Success!")
	} else {
		l4g.Infof("[FAILED] fixTalentTree Failed, need check error log! fixTalentTreeFailedCount:%d", fixTalentTreeFailedCount)
	}
	l4g.Infof("finish repair")
}

func fixTalentTree(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixTalentTreeFailedCount++
		return
	}
	if userData == nil {
		l4g.Error("user %s fixTalentTree: data corrupt", uid)
		return
	}
	if userData.ModuleGlobalAttr == nil || userData.ModuleGlobalAttr.TalentTreeCul == nil ||
		userData.ModuleGlobalAttr.TalentTreeCul.Levels == nil {
		return
	}
	levels := userData.ModuleGlobalAttr.TalentTreeCul.Levels
	var change bool
	if levels[1105101] > 4 {
		levels[1105101] = 4
		change = true
	}
	if levels[1105103] > 4 {
		levels[1105103] = 4
		change = true
	}
	if levels[1105114] > 3 {
		levels[1105114] = 3
		change = true
	}
	if !change {
		return
	}
	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixTalentTreeFailedCount++
		l4g.Errorf("user %s fixTalentTree: save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s fixTalentTree: save success", uid)
}

func fixLogicTalentTreeHot(r *redis.Redis, idFactory *id.Factory) {
	hotData, err := r.RopDB.GetLogicTalentTreeHot()
	if err != nil {
		l4g.Error("fixLogicTalentTreeHot: get hot data err:%s")
		fixTalentTreeFailedCount++
		return
	}
	if hotData.Data == nil || len(hotData.Data.Nodes) == 0 {
		return
	}

	for _, node := range hotData.Data.Nodes {
		if node.Id == 1105101 {
			for i, level := range node.Levels {
				if level.Type >= 5 {
					node.Levels = append(node.Levels[0:i], node.Levels[i+1:]...)
					break
				}
			}
		} else if node.Id == 1105103 {
			for i, level := range node.Levels {
				if level.Type >= 5 {
					node.Levels = append(node.Levels[0:i], node.Levels[i+1:]...)
					break
				}
			}
		} else if node.Id == 1105114 {
			for i, level := range node.Levels {
				if level.Type > 3 {
					node.Levels = append(node.Levels[0:i], node.Levels[i+1:]...)
					break
				}
			}
		}
	}

	err = r.RopDB.SetLogicTalentTreeHot(hotData)
	if err != nil {
		l4g.Error("fixLogicTalentTreeHot: set hot data err:%s")
		fixTalentTreeFailedCount++
		return
	}
}
