package base

import (
	"app/logic/character"
	"app/logic/db"
	"app/logic/session"
	"app/protos/in/r2l"
	"app/tools/repair/cmd/redis"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
)

func LoadUser(uid string, r *redis.Redis) *character.User {
	retData := &r2l.R2L_Login{}
	// base
	r.RopDB.GetUserMCallSKs(uid)
	//mail
	r.RopCl.GetAllMailMCallSKs(uid)
	//hero
	r.RopCl.GetAllHeroBodyMCallSKs(uid)
	//equip
	r.RopCl.GetAllEquipmentMCallSKs(uid)
	//formation
	r.RopCl.GetAllFormationMCallSKs(uid)
	//artifact
	r.RopCl.GetAllArtifactMCallSKs(uid)
	//gem
	r.RopCl.GetAllGemInfoMCallSKs(uid)
	//emblem
	r.RopCl.GetAllEmblemInfoMCallSKs(uid)
	//mirage
	r.RopCl.GetAllMirageMCallSKs(uid)
	// mazePlayer
	r.RopCl.GetAllMazePlayerMCallSKs(uid)
	iUID, _ := strconv.ParseUint(uid, 10, 64)
	//guilduser
	r.RopDB.GetSomeGuildUserMCall([]uint64{iUID})
	//充值订单
	r.RopDB.GetAllOrderMCallSKs(uid)
	//待处理的订单
	r.RopDB.GetWaitProcessOrderMCallSK(iUID)
	//配置活动
	r.RopCl.GetAllOperateActivityMCallSKs(uid)
	//已完成问卷
	r.RopDB.GetQuestionnaireMCallSK(iUID)
	//heroStarUpCosts
	r.RopCl.GetAllHeroStarUpCostsMCallSKs(uid)
	//ban
	r.RopDB.GetSomeUserBanMCall([]uint64{iUID})
	//赛季装备
	r.RopCl.GetAllSeasonJewelryMCallSKs(uid)
	/************************REPLY*******************************/
	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.User = userData
	//邮件
	mailsData, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mails error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Mails = mailsData
	//英雄
	heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Heroes = heroesData
	//装备
	equipsData, err := r.RopCl.GetSomeEquipmentByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user equips error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Equips = equipsData
	//阵容
	formationsData, err := r.RopCl.GetSomeFormationByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user formation error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Formations = formationsData
	//神器
	artifactData, err := r.RopCl.GetSomeArtifactByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user artifact error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Artifacts = artifactData

	//宝石
	gemData, err := r.RopCl.GetSomeGemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user gem error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Gems = gemData

	//纹章
	emblemData, err := r.RopCl.GetSomeEmblemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user emblem error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Emblems = emblemData

	//个人boss
	miragesData, err := r.RopCl.GetSomeMirageByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mirage error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Mirages = miragesData

	// mazePlayer
	mazePlayerData, err := r.RopCl.GetSomeMazePlayerByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get mazePlayer error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.MazePlayer = mazePlayerData

	//guildUser
	guildUserData, err := r.RopDB.GetSomeGuildUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get guildUser error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.GuildUser = guildUserData[iUID]
	//充值订单
	orders, err := r.RopDB.GetSomeOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Orders = orders
	//待处理的订单
	waitProcessOrders, err := r.RopDB.GetWaitProcessOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get wait process orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.WaitProcessOrders = waitProcessOrders

	activities, err := r.RopCl.GetSomeOperateActivityByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get operate activiy error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Activities = activities

	//已完成问卷
	questionnaires, err := r.RopDB.GetQuestionnaireByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get questionnaire error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Questionnaires = questionnaires

	//英雄升星消耗
	heroesStarUpCosts, err := r.RopCl.GetSomeHeroStarUpCostsByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes star up costs error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.HeroesStarUpCosts = heroesStarUpCosts

	userBan, err := r.RopDB.GetSomeUserBanByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user ban error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.UserBan = userBan[iUID]

	seasonJewelry, err := r.RopCl.GetSomeSeasonJewelryByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get season jewelry error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.SeasonJewelry = seasonJewelry

	return loadUser(retData)
}

func loadUser(retData *r2l.R2L_Login) *character.User {
	client := &session.Client{}
	u := character.NewUser(client, 0, retData.User.Uuid, retData.User.ServerId, "")

	if retData.GuildUser != nil {
		u.UserGuild().Update(retData.GuildUser.GuildId, "")
	}
	u.LoadLoginUser(retData, nil)
	return u
}

func LoadUserNew(uid string, redisActor *db.RedisActor) *character.User {
	retData := &r2l.R2L_Login{}
	redisActor.LoginByUID(uid, retData)
	return loadUser(retData)
}
