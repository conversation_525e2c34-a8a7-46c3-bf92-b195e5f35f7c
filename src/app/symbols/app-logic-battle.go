// Code generated by 'yaegi extract app/logic/battle'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/bt"
	"go/constant"
	"go/token"
	"reflect"
)

func init() {
	Symbols["app/logic/battle/battle"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ActifactEnergyAction":                       reflect.ValueOf(constant.MakeFromLiteral("150", token.INT, 0)),
		"ActifactEnergyKill":                         reflect.ValueOf(constant.MakeFromLiteral("151", token.INT, 0)),
		"ActivePassiveSkill":                         reflect.ValueOf(battle.ActivePassiveSkill),
		"AddBuffRetDikang":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"AddBuffRetMianyi":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"AddBuffRetNone":                             reflect.ValueOf(constant.MakeFromLiteral("-1", token.INT, 0)),
		"AddBuffRetReflect":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"AddBuffRetSuccess":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"AddRateCondOwnerTeamLinkNum":                reflect.ValueOf(battle.AddRateCondOwnerTeamLinkNum),
		"ArtifactBattlePos":                          reflect.ValueOf(battle.ArtifactBattlePos),
		"ArtifactHurtRateFix":                        reflect.ValueOf(battle.ArtifactHurtRateFix),
		"ArtifactMaxNum":                             reflect.ValueOf(battle.ArtifactMaxNum),
		"AttackTeam":                                 reflect.ValueOf(battle.AttackTeam),
		"AttackTypeMagic":                            reflect.ValueOf(battle.AttackTypeMagic),
		"AttackTypeNone":                             reflect.ValueOf(battle.AttackTypeNone),
		"AttackTypePhysical":                         reflect.ValueOf(battle.AttackTypePhysical),
		"BaseFloat":                                  reflect.ValueOf(battle.BaseFloat),
		"BaseFloatInt":                               reflect.ValueOf(battle.BaseFloatInt),
		"BattleAdd":                                  reflect.ValueOf(battle.BattleAdd),
		"BattleDec":                                  reflect.ValueOf(battle.BattleDec),
		"BattleStaBeHurt":                            reflect.ValueOf(battle.BattleStaBeHurt),
		"BattleStaCure":                              reflect.ValueOf(battle.BattleStaCure),
		"BattleStaHurt":                              reflect.ValueOf(battle.BattleStaHurt),
		"BeGuardHurtFormula":                         reflect.ValueOf(battle.BeGuardHurtFormula),
		"BuffAddTypeCover":                           reflect.ValueOf(battle.BuffAddTypeCover),
		"BuffAddTypeCoverInheritLayer":               reflect.ValueOf(battle.BuffAddTypeCoverInheritLayer),
		"BuffAddTypeNone":                            reflect.ValueOf(battle.BuffAddTypeNone),
		"BuffAddTypeOverlay":                         reflect.ValueOf(battle.BuffAddTypeOverlay),
		"BuffAddTypePriority":                        reflect.ValueOf(battle.BuffAddTypePriority),
		"BuffAddTypePriorityCover":                   reflect.ValueOf(battle.BuffAddTypePriorityCover),
		"BuffAddTypeTeamSingle":                      reflect.ValueOf(battle.BuffAddTypeTeamSingle),
		"BuffAddTypeTypeLayer":                       reflect.ValueOf(battle.BuffAddTypeTypeLayer),
		"BuffAddTypeUpdate":                          reflect.ValueOf(battle.BuffAddTypeUpdate),
		"BuffAddTypeUpdateRound":                     reflect.ValueOf(battle.BuffAddTypeUpdateRound),
		"BuffAttrStartType":                          reflect.ValueOf(battle.BuffAttrStartType),
		"BuffBenefit":                                reflect.ValueOf(battle.BuffBenefit),
		"BuffBenefitNormal":                          reflect.ValueOf(battle.BuffBenefitNormal),
		"BuffBenefitSp":                              reflect.ValueOf(battle.BuffBenefitSp),
		"BuffBucketMax":                              reflect.ValueOf(battle.BuffBucketMax),
		"BuffChaosTarget":                            reflect.ValueOf(battle.BuffChaosTarget),
		"BuffCharmActiveRate":                        reflect.ValueOf(battle.BuffCharmActiveRate),
		"BuffCharmTarget":                            reflect.ValueOf(battle.BuffCharmTarget),
		"BuffCharmTargetCasterRate":                  reflect.ValueOf(battle.BuffCharmTargetCasterRate),
		"BuffClearNormal":                            reflect.ValueOf(battle.BuffClearNormal),
		"BuffCondAlive":                              reflect.ValueOf(battle.BuffCondAlive),
		"BuffCondOnEvent":                            reflect.ValueOf(battle.BuffCondOnEvent),
		"BuffCopyTypeHarm":                           reflect.ValueOf(battle.BuffCopyTypeHarm),
		"BuffCopyTypeHarmfulAndNoControl":            reflect.ValueOf(battle.BuffCopyTypeHarmfulAndNoControl),
		"BuffCopyTypeHelp":                           reflect.ValueOf(battle.BuffCopyTypeHelp),
		"BuffDispel":                                 reflect.ValueOf(battle.BuffDispel),
		"BuffEffectActive":                           reflect.ValueOf(battle.BuffEffectActive),
		"BuffEffectBeRemove":                         reflect.ValueOf(battle.BuffEffectBeRemove),
		"BuffEffectDecrLayer":                        reflect.ValueOf(battle.BuffEffectDecrLayer),
		"BuffEffectNotActive":                        reflect.ValueOf(battle.BuffEffectNotActive),
		"BuffHarmful":                                reflect.ValueOf(battle.BuffHarmful),
		"BuffHarmfulSp":                              reflect.ValueOf(battle.BuffHarmfulSp),
		"BuffLogicTypeAttr":                          reflect.ValueOf(battle.BuffLogicTypeAttr),
		"BuffLogicTypeCurl":                          reflect.ValueOf(battle.BuffLogicTypeCurl),
		"BuffLogicTypeHurt":                          reflect.ValueOf(battle.BuffLogicTypeHurt),
		"BuffLogicTypeShield":                        reflect.ValueOf(battle.BuffLogicTypeShield),
		"BuffMabiRate":                               reflect.ValueOf(battle.BuffMabiRate),
		"BuffPurify":                                 reflect.ValueOf(battle.BuffPurify),
		"BuffRemoveTypeBuffEffectType":               reflect.ValueOf(battle.BuffRemoveTypeBuffEffectType),
		"BuffRemoveTypeControl":                      reflect.ValueOf(battle.BuffRemoveTypeControl),
		"BuffRemoveTypeHarm":                         reflect.ValueOf(battle.BuffRemoveTypeHarm),
		"BuffRemoveTypeHarmfulAndNoControl":          reflect.ValueOf(battle.BuffRemoveTypeHarmfulAndNoControl),
		"BuffRemoveTypeHelp":                         reflect.ValueOf(battle.BuffRemoveTypeHelp),
		"BuffRemoveTypeID":                           reflect.ValueOf(battle.BuffRemoveTypeID),
		"BuffRemoveTypeIDOneLayer":                   reflect.ValueOf(battle.BuffRemoveTypeIDOneLayer),
		"BuffRemoveTypeStatus":                       reflect.ValueOf(battle.BuffRemoveTypeStatus),
		"BuffRemoveTypeStatusOneLayer":               reflect.ValueOf(battle.BuffRemoveTypeStatusOneLayer),
		"BuffSliceLengthMax":                         reflect.ValueOf(constant.MakeFromLiteral("10000", token.INT, 0)),
		"BuffSubTypeSettleAccumulateHurt":            reflect.ValueOf(battle.BuffSubTypeSettleAccumulateHurt),
		"BuffSubTypeSettleCurl":                      reflect.ValueOf(battle.BuffSubTypeSettleCurl),
		"BuffSubTypeSettleHurt":                      reflect.ValueOf(battle.BuffSubTypeSettleHurt),
		"BuffSubTypeTrrigerHurt":                     reflect.ValueOf(battle.BuffSubTypeTrrigerHurt),
		"BuffTakeActionEnd":                          reflect.ValueOf(battle.BuffTakeActionEnd),
		"BuffTakeActionPrepare":                      reflect.ValueOf(battle.BuffTakeActionPrepare),
		"BuffTakeRoundPrepare":                       reflect.ValueOf(battle.BuffTakeRoundPrepare),
		"BuffTypeAccumulateHurt":                     reflect.ValueOf(battle.BuffTypeAccumulateHurt),
		"BuffTypeAccumulateHurtShare":                reflect.ValueOf(battle.BuffTypeAccumulateHurtShare),
		"BuffTypeArtifactEnergy":                     reflect.ValueOf(battle.BuffTypeArtifactEnergy),
		"BuffTypeBlessNoDebuff":                      reflect.ValueOf(battle.BuffTypeBlessNoDebuff),
		"BuffTypeBlessNoHurted":                      reflect.ValueOf(battle.BuffTypeBlessNoHurted),
		"BuffTypeBurn":                               reflect.ValueOf(battle.BuffTypeBurn),
		"BuffTypeCastTime":                           reflect.ValueOf(battle.BuffTypeCastTime),
		"BuffTypeChaos":                              reflect.ValueOf(battle.BuffTypeChaos),
		"BuffTypeCharm":                              reflect.ValueOf(battle.BuffTypeCharm),
		"BuffTypeCorruption":                         reflect.ValueOf(battle.BuffTypeCorruption),
		"BuffTypeCountLayer":                         reflect.ValueOf(battle.BuffTypeCountLayer),
		"BuffTypeCure2Hurt":                          reflect.ValueOf(battle.BuffTypeCure2Hurt),
		"BuffTypeCut":                                reflect.ValueOf(battle.BuffTypeCut),
		"BuffTypeDeathKeepBenefitBuff":               reflect.ValueOf(battle.BuffTypeDeathKeepBenefitBuff),
		"BuffTypeEffectDefault":                      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"BuffTypeEffectMaxLayerAdd":                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"BuffTypeEffectMaxLayerMulti":                reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"BuffTypeEffectRoundAdd":                     reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"BuffTypeEntang":                             reflect.ValueOf(battle.BuffTypeEntang),
		"BuffTypeExile":                              reflect.ValueOf(battle.BuffTypeExile),
		"BuffTypeFire":                               reflect.ValueOf(battle.BuffTypeFire),
		"BuffTypeForbidAddBuff":                      reflect.ValueOf(battle.BuffTypeForbidAddBuff),
		"BuffTypeGuard":                              reflect.ValueOf(battle.BuffTypeGuard),
		"BuffTypeHide":                               reflect.ValueOf(battle.BuffTypeHide),
		"BuffTypeHot":                                reflect.ValueOf(battle.BuffTypeHot),
		"BuffTypeHot2":                               reflect.ValueOf(battle.BuffTypeHot2),
		"BuffTypeHunting":                            reflect.ValueOf(battle.BuffTypeHunting),
		"BuffTypeIgnoreShield":                       reflect.ValueOf(battle.BuffTypeIgnoreShield),
		"BuffTypeImmune":                             reflect.ValueOf(battle.BuffTypeImmune),
		"BuffTypeImmuneBadNoControlBuff":             reflect.ValueOf(battle.BuffTypeImmuneBadNoControlBuff),
		"BuffTypeImmuneControlBuff":                  reflect.ValueOf(battle.BuffTypeImmuneControlBuff),
		"BuffTypeImmuneNoAlive":                      reflect.ValueOf(battle.BuffTypeImmuneNoAlive),
		"BuffTypeImmunePositiveBuff":                 reflect.ValueOf(battle.BuffTypeImmunePositiveBuff),
		"BuffTypeImmuneStealAndEat":                  reflect.ValueOf(battle.BuffTypeImmuneStealAndEat),
		"BuffTypeInvincibleShield":                   reflect.ValueOf(battle.BuffTypeInvincibleShield),
		"BuffTypeJess":                               reflect.ValueOf(battle.BuffTypeJess),
		"BuffTypeLimitSelfHurt":                      reflect.ValueOf(battle.BuffTypeLimitSelfHurt),
		"BuffTypeLimitSelfHurt2":                     reflect.ValueOf(battle.BuffTypeLimitSelfHurt2),
		"BuffTypeMabi":                               reflect.ValueOf(battle.BuffTypeMabi),
		"BuffTypeMaxLayerHurt3":                      reflect.ValueOf(battle.BuffTypeMaxLayerHurt3),
		"BuffTypeMaxLayerHurt4":                      reflect.ValueOf(battle.BuffTypeMaxLayerHurt4),
		"BuffTypeMaxLayerHurt5":                      reflect.ValueOf(battle.BuffTypeMaxLayerHurt5),
		"BuffTypeNoAlive":                            reflect.ValueOf(battle.BuffTypeNoAlive),
		"BuffTypeNoDead":                             reflect.ValueOf(battle.BuffTypeNoDead),
		"BuffTypeNoDead2":                            reflect.ValueOf(battle.BuffTypeNoDead2),
		"BuffTypeNoDead3":                            reflect.ValueOf(battle.BuffTypeNoDead3),
		"BuffTypeNoDebuff":                           reflect.ValueOf(battle.BuffTypeNoDebuff),
		"BuffTypeNoHurted":                           reflect.ValueOf(battle.BuffTypeNoHurted),
		"BuffTypeNoHurtedBySkillType":                reflect.ValueOf(battle.BuffTypeNoHurtedBySkillType),
		"BuffTypeOnlyArtifactGroupHurt":              reflect.ValueOf(battle.BuffTypeOnlyArtifactGroupHurt),
		"BuffTypeOnlyArtifactSingleHurt":             reflect.ValueOf(battle.BuffTypeOnlyArtifactSingleHurt),
		"BuffTypeOnlyHurtBySelfOrCallDepartedSpirit": reflect.ValueOf(battle.BuffTypeOnlyHurtBySelfOrCallDepartedSpirit),
		"BuffTypeOnlyMagicHurt":                      reflect.ValueOf(battle.BuffTypeOnlyMagicHurt),
		"BuffTypeOnlyOnePosAndOneSex":                reflect.ValueOf(battle.BuffTypeOnlyOnePosAndOneSex),
		"BuffTypeOnlyPhysicalHurt":                   reflect.ValueOf(battle.BuffTypeOnlyPhysicalHurt),
		"BuffTypePoison":                             reflect.ValueOf(battle.BuffTypePoison),
		"BuffTypeReflect":                            reflect.ValueOf(battle.BuffTypeReflect),
		"BuffTypeReflectDebuff":                      reflect.ValueOf(battle.BuffTypeReflectDebuff),
		"BuffTypeReset":                              reflect.ValueOf(battle.BuffTypeReset),
		"BuffTypeResurrect":                          reflect.ValueOf(battle.BuffTypeResurrect),
		"BuffTypeSeal":                               reflect.ValueOf(battle.BuffTypeSeal),
		"BuffTypeShareDamageByHp":                    reflect.ValueOf(battle.BuffTypeShareDamageByHp),
		"BuffTypeShield":                             reflect.ValueOf(battle.BuffTypeShield),
		"BuffTypeShudder":                            reflect.ValueOf(battle.BuffTypeShudder),
		"BuffTypeSign":                               reflect.ValueOf(battle.BuffTypeSign),
		"BuffTypeSilence":                            reflect.ValueOf(battle.BuffTypeSilence),
		"BuffTypeSkillNoCritHurt2Cure":               reflect.ValueOf(battle.BuffTypeSkillNoCritHurt2Cure),
		"BuffTypeStealCure":                          reflect.ValueOf(battle.BuffTypeStealCure),
		"BuffTypeStormGather":                        reflect.ValueOf(battle.BuffTypeStormGather),
		"BuffTypeStun":                               reflect.ValueOf(battle.BuffTypeStun),
		"BuffTypeStun2":                              reflect.ValueOf(battle.BuffTypeStun2),
		"BuffTypeTaunt":                              reflect.ValueOf(battle.BuffTypeTaunt),
		"Buff_Event_Alive_Change":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"Buff_Event_Max":                             reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"Buff_Event_None":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"Buff_Event_Round_End":                       reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"CalPower":                                   reflect.ValueOf(battle.CalPower),
		"CallPos":                                    reflect.ValueOf(battle.CallPos),
		"ChangeAttrTimeBuff":                         reflect.ValueOf(battle.ChangeAttrTimeBuff),
		"ChangeAttrTimeForeve":                       reflect.ValueOf(battle.ChangeAttrTimeForeve),
		"ChangeAttrTimeMax":                          reflect.ValueOf(battle.ChangeAttrTimeMax),
		"ChangeAttrTimeOneAttack":                    reflect.ValueOf(battle.ChangeAttrTimeOneAttack),
		"ChangeAttrTimeOneEffect":                    reflect.ValueOf(battle.ChangeAttrTimeOneEffect),
		"ChangeAttrTimeOneSkill":                     reflect.ValueOf(battle.ChangeAttrTimeOneSkill),
		"ChangePsAttrTimeForeve":                     reflect.ValueOf(battle.ChangePsAttrTimeForeve),
		"ChangePsAttrTimeHalo":                       reflect.ValueOf(battle.ChangePsAttrTimeHalo),
		"ChangePsAttrTimeMax":                        reflect.ValueOf(battle.ChangePsAttrTimeMax),
		"ChangePsAttrTimeNextSkill":                  reflect.ValueOf(battle.ChangePsAttrTimeNextSkill),
		"ChangePsAttrTimeOneAttack":                  reflect.ValueOf(battle.ChangePsAttrTimeOneAttack),
		"ChangePsAttrTimeOneEffect":                  reflect.ValueOf(battle.ChangePsAttrTimeOneEffect),
		"ChangePsAttrTimeOneSkill":                   reflect.ValueOf(battle.ChangePsAttrTimeOneSkill),
		"CheckBuffInUse":                             reflect.ValueOf(battle.CheckBuffInUse),
		"CleanDefaultMaxSeasonEnergy":                reflect.ValueOf(battle.CleanDefaultMaxSeasonEnergy),
		"CloseBattleDebug":                           reflect.ValueOf(battle.CloseBattleDebug),
		"CloseDebugAttr":                             reflect.ValueOf(battle.CloseDebugAttr),
		"CloseDebugBuff":                             reflect.ValueOf(battle.CloseDebugBuff),
		"CloseDebugEvent":                            reflect.ValueOf(battle.CloseDebugEvent),
		"CloseDebugFormula":                          reflect.ValueOf(battle.CloseDebugFormula),
		"CondAccumulateBeHurt":                       reflect.ValueOf(battle.CondAccumulateBeHurt),
		"CondAliveNum":                               reflect.ValueOf(battle.CondAliveNum),
		"CondAttackBenefitBuffCount":                 reflect.ValueOf(battle.CondAttackBenefitBuffCount),
		"CondAttackBuffState":                        reflect.ValueOf(battle.CondAttackBuffState),
		"CondAttackBuffStateOrDefenseControled":      reflect.ValueOf(battle.CondAttackBuffStateOrDefenseControled),
		"CondAttackCrit":                             reflect.ValueOf(battle.CondAttackCrit),
		"CondAttackHasCrit":                          reflect.ValueOf(battle.CondAttackHasCrit),
		"CondAttackHasKill":                          reflect.ValueOf(battle.CondAttackHasKill),
		"CondAttackHasNoCrit":                        reflect.ValueOf(battle.CondAttackHasNoCrit),
		"CondAttackHasNoCritAndOnlyOne":              reflect.ValueOf(battle.CondAttackHasNoCritAndOnlyOne),
		"CondAttackHasOverCure":                      reflect.ValueOf(battle.CondAttackHasOverCure),
		"CondAttackHpLess":                           reflect.ValueOf(battle.CondAttackHpLess),
		"CondAttackHpMore":                           reflect.ValueOf(battle.CondAttackHpMore),
		"CondAttackHurtOrCureLessDefenseHp":          reflect.ValueOf(battle.CondAttackHurtOrCureLessDefenseHp),
		"CondAttackHurtOrCureMoreDefenseHp":          reflect.ValueOf(battle.CondAttackHurtOrCureMoreDefenseHp),
		"CondAttackInOwnerTarget":                    reflect.ValueOf(battle.CondAttackInOwnerTarget),
		"CondAttackIsControled":                      reflect.ValueOf(battle.CondAttackIsControled),
		"CondAttackJob":                              reflect.ValueOf(battle.CondAttackJob),
		"CondAttackNoBenefitBuffCount":               reflect.ValueOf(battle.CondAttackNoBenefitBuffCount),
		"CondAttackNoBuffState":                      reflect.ValueOf(battle.CondAttackNoBuffState),
		"CondAttackRace":                             reflect.ValueOf(battle.CondAttackRace),
		"CondAttackTargetNumLess":                    reflect.ValueOf(battle.CondAttackTargetNumLess),
		"CondAttackTeamBenefitBuffCount":             reflect.ValueOf(battle.CondAttackTeamBenefitBuffCount),
		"CondAttackTeamLinkNum":                      reflect.ValueOf(battle.CondAttackTeamLinkNum),
		"CondAttackTeamOnlyOne":                      reflect.ValueOf(battle.CondAttackTeamOnlyOne),
		"CondAttr":                                   reflect.ValueOf(battle.CondAttr),
		"CondBuffActiveSkillHurtEffective":           reflect.ValueOf(battle.CondBuffActiveSkillHurtEffective),
		"CondBuffLayerCountLess":                     reflect.ValueOf(battle.CondBuffLayerCountLess),
		"CondBuffLayerCountMoreEqual":                reflect.ValueOf(battle.CondBuffLayerCountMoreEqual),
		"CondBuffLayerRecord":                        reflect.ValueOf(battle.CondBuffLayerRecord),
		"CondBuffTypeCountMoreEqual":                 reflect.ValueOf(battle.CondBuffTypeCountMoreEqual),
		"CondControlNumMore":                         reflect.ValueOf(battle.CondControlNumMore),
		"CondCurRoundBigSkillCount":                  reflect.ValueOf(battle.CondCurRoundBigSkillCount),
		"CondCurrentRoundIsOddOrEven":                reflect.ValueOf(battle.CondCurrentRoundIsOddOrEven),
		"CondDefenseAttrLessOwner":                   reflect.ValueOf(battle.CondDefenseAttrLessOwner),
		"CondDefenseAttrMoreOwner":                   reflect.ValueOf(battle.CondDefenseAttrMoreOwner),
		"CondDefenseBenefitBuffCount":                reflect.ValueOf(battle.CondDefenseBenefitBuffCount),
		"CondDefenseBuffState":                       reflect.ValueOf(battle.CondDefenseBuffState),
		"CondDefenseBuffStateOrControled":            reflect.ValueOf(battle.CondDefenseBuffStateOrControled),
		"CondDefenseCorruptionCountMore":             reflect.ValueOf(battle.CondDefenseCorruptionCountMore),
		"CondDefenseDefCompare":                      reflect.ValueOf(battle.CondDefenseDefCompare),
		"CondDefenseHpLess":                          reflect.ValueOf(battle.CondDefenseHpLess),
		"CondDefenseHpMore":                          reflect.ValueOf(battle.CondDefenseHpMore),
		"CondDefenseInOwnerTarget":                   reflect.ValueOf(battle.CondDefenseInOwnerTarget),
		"CondDefenseIsControled":                     reflect.ValueOf(battle.CondDefenseIsControled),
		"CondDefenseJob":                             reflect.ValueOf(battle.CondDefenseJob),
		"CondDefenseNoBenefitBuffCount":              reflect.ValueOf(battle.CondDefenseNoBenefitBuffCount),
		"CondDefenseNoBuffState":                     reflect.ValueOf(battle.CondDefenseNoBuffState),
		"CondDefenseRace":                            reflect.ValueOf(battle.CondDefenseRace),
		"CondDefenseTeamLinkNum":                     reflect.ValueOf(battle.CondDefenseTeamLinkNum),
		"CondHasPsAttr":                              reflect.ValueOf(battle.CondHasPsAttr),
		"CondHaveEmptyPosOrMonsterCall":              reflect.ValueOf(battle.CondHaveEmptyPosOrMonsterCall),
		"CondHavePsID":                               reflect.ValueOf(battle.CondHavePsID),
		"CondHeroID":                                 reflect.ValueOf(battle.CondHeroID),
		"CondHeroSex":                                reflect.ValueOf(battle.CondHeroSex),
		"CondHeroStar":                               reflect.ValueOf(battle.CondHeroStar),
		"CondHpLower":                                reflect.ValueOf(battle.CondHpLower),
		"CondIsTargetExist":                          reflect.ValueOf(battle.CondIsTargetExist),
		"CondLeftMemAndNoMonsterCallHero":            reflect.ValueOf(battle.CondLeftMemAndNoMonsterCallHero),
		"CondLink":                                   reflect.ValueOf(battle.CondLink),
		"CondLinkTypeCount":                          reflect.ValueOf(battle.CondLinkTypeCount),
		"CondNoBuffID":                               reflect.ValueOf(battle.CondNoBuffID),
		"CondOneSkillAttackHasKill":                  reflect.ValueOf(battle.CondOneSkillAttackHasKill),
		"CondOpTeamLinkNum":                          reflect.ValueOf(battle.CondOpTeamLinkNum),
		"CondOwnerBuffState":                         reflect.ValueOf(battle.CondOwnerBuffState),
		"CondOwnerCanResurr":                         reflect.ValueOf(battle.CondOwnerCanResurr),
		"CondOwnerDeathState":                        reflect.ValueOf(battle.CondOwnerDeathState),
		"CondOwnerHpHigher":                          reflect.ValueOf(battle.CondOwnerHpHigher),
		"CondOwnerHpLower":                           reflect.ValueOf(battle.CondOwnerHpLower),
		"CondOwnerIsBenefitBuffCount":                reflect.ValueOf(battle.CondOwnerIsBenefitBuffCount),
		"CondOwnerNoBuffState":                       reflect.ValueOf(battle.CondOwnerNoBuffState),
		"CondOwnerPos":                               reflect.ValueOf(battle.CondOwnerPos),
		"CondOwnerTeamLinkNum":                       reflect.ValueOf(battle.CondOwnerTeamLinkNum),
		"CondResurrection":                           reflect.ValueOf(battle.CondResurrection),
		"CondRoundGE":                                reflect.ValueOf(battle.CondRoundGE),
		"CondRoundLess":                              reflect.ValueOf(battle.CondRoundLess),
		"CondSameActionAddedBuffCount":               reflect.ValueOf(battle.CondSameActionAddedBuffCount),
		"CondSeasonEnergy":                           reflect.ValueOf(battle.CondSeasonEnergy),
		"CondSeasonEnergyExtra":                      reflect.ValueOf(battle.CondSeasonEnergyExtra),
		"CondSkillGroupEqual":                        reflect.ValueOf(battle.CondSkillGroupEqual),
		"CondTeamAttrMoreEqual":                      reflect.ValueOf(battle.CondTeamAttrMoreEqual),
		"CondTeamDeathNumMoreEqual":                  reflect.ValueOf(battle.CondTeamDeathNumMoreEqual),
		"CondTeamResurrectionMoreEqual":              reflect.ValueOf(battle.CondTeamResurrectionMoreEqual),
		"CondTriggerBuffIDCountMoreThan1":            reflect.ValueOf(battle.CondTriggerBuffIDCountMoreThan1),
		"CureTypeSteal":                              reflect.ValueOf(battle.CureTypeSteal),
		"DebugPool":                                  reflect.ValueOf(battle.DebugPool),
		"DefenseTeam":                                reflect.ValueOf(battle.DefenseTeam),
		"EffectBuff933":                              reflect.ValueOf(battle.EffectBuff933),
		"EffectBuff934":                              reflect.ValueOf(battle.EffectBuff934),
		"EffectBuff935":                              reflect.ValueOf(battle.EffectBuff935),
		"EffectBuff936":                              reflect.ValueOf(battle.EffectBuff936),
		"EffectBuff937":                              reflect.ValueOf(battle.EffectBuff937),
		"EffectBuff938":                              reflect.ValueOf(battle.EffectBuff938),
		"EffectBuff939":                              reflect.ValueOf(battle.EffectBuff939),
		"EffectBuff940":                              reflect.ValueOf(battle.EffectBuff940),
		"EffectBuff941":                              reflect.ValueOf(battle.EffectBuff941),
		"EffectBuff942":                              reflect.ValueOf(battle.EffectBuff942),
		"EffectBuff943":                              reflect.ValueOf(battle.EffectBuff943),
		"EffectBuff944":                              reflect.ValueOf(battle.EffectBuff944),
		"EffectBuff945":                              reflect.ValueOf(battle.EffectBuff945),
		"EffectBuff946":                              reflect.ValueOf(battle.EffectBuff946),
		"EffectBuff947":                              reflect.ValueOf(battle.EffectBuff947),
		"EffectBuff948":                              reflect.ValueOf(battle.EffectBuff948),
		"EffectBuff949":                              reflect.ValueOf(battle.EffectBuff949),
		"EffectBuff950":                              reflect.ValueOf(battle.EffectBuff950),
		"EffectBuff951":                              reflect.ValueOf(battle.EffectBuff951),
		"EffectBuff961":                              reflect.ValueOf(battle.EffectBuff961),
		"EffectEatAddAttrs":                          reflect.ValueOf(&battle.EffectEatAddAttrs).Elem(),
		"FinnalHurtRateFix":                          reflect.ValueOf(battle.FinnalHurtRateFix),
		"FirstCallID":                                reflect.ValueOf(battle.FirstCallID),
		"Formula100":                                 reflect.ValueOf(battle.Formula100),
		"Formula101":                                 reflect.ValueOf(battle.Formula101),
		"Formula102":                                 reflect.ValueOf(battle.Formula102),
		"Formula103":                                 reflect.ValueOf(battle.Formula103),
		"Formula104":                                 reflect.ValueOf(battle.Formula104),
		"Formula106":                                 reflect.ValueOf(battle.Formula106),
		"Formula107":                                 reflect.ValueOf(battle.Formula107),
		"Formula108":                                 reflect.ValueOf(battle.Formula108),
		"Formula109":                                 reflect.ValueOf(battle.Formula109),
		"Formula110":                                 reflect.ValueOf(battle.Formula110),
		"Formula111":                                 reflect.ValueOf(battle.Formula111),
		"Formula112":                                 reflect.ValueOf(battle.Formula112),
		"Formula113":                                 reflect.ValueOf(battle.Formula113),
		"Formula114":                                 reflect.ValueOf(battle.Formula114),
		"Formula115":                                 reflect.ValueOf(battle.Formula115),
		"Formula116":                                 reflect.ValueOf(battle.Formula116),
		"Formula117":                                 reflect.ValueOf(battle.Formula117),
		"Formula118":                                 reflect.ValueOf(battle.Formula118),
		"Formula119":                                 reflect.ValueOf(battle.Formula119),
		"Formula120":                                 reflect.ValueOf(battle.Formula120),
		"Formula150":                                 reflect.ValueOf(battle.Formula150),
		"Formula151":                                 reflect.ValueOf(battle.Formula151),
		"Formula154":                                 reflect.ValueOf(battle.Formula154),
		"Formula155":                                 reflect.ValueOf(battle.Formula155),
		"Formula201":                                 reflect.ValueOf(battle.Formula201),
		"Formula202":                                 reflect.ValueOf(battle.Formula202),
		"Formula203":                                 reflect.ValueOf(battle.Formula203),
		"Formula204":                                 reflect.ValueOf(battle.Formula204),
		"Formula205":                                 reflect.ValueOf(battle.Formula205),
		"Formula206":                                 reflect.ValueOf(battle.Formula206),
		"Formula207":                                 reflect.ValueOf(battle.Formula207),
		"Formula208":                                 reflect.ValueOf(battle.Formula208),
		"Formula209":                                 reflect.ValueOf(battle.Formula209),
		"Formula210":                                 reflect.ValueOf(battle.Formula210),
		"Formula211":                                 reflect.ValueOf(battle.Formula211),
		"Formula212":                                 reflect.ValueOf(battle.Formula212),
		"Formula213":                                 reflect.ValueOf(battle.Formula213),
		"Formula214":                                 reflect.ValueOf(battle.Formula214),
		"Formula215":                                 reflect.ValueOf(battle.Formula215),
		"Formula217":                                 reflect.ValueOf(battle.Formula217),
		"Formula218":                                 reflect.ValueOf(battle.Formula218),
		"Formula222":                                 reflect.ValueOf(battle.Formula222),
		"Formula223":                                 reflect.ValueOf(battle.Formula223),
		"Formula224":                                 reflect.ValueOf(battle.Formula224),
		"Formula225":                                 reflect.ValueOf(battle.Formula225),
		"Formula226":                                 reflect.ValueOf(battle.Formula226),
		"Formula227":                                 reflect.ValueOf(battle.Formula227),
		"Formula228":                                 reflect.ValueOf(battle.Formula228),
		"Formula229":                                 reflect.ValueOf(battle.Formula229),
		"Formula230":                                 reflect.ValueOf(battle.Formula230),
		"Formula231":                                 reflect.ValueOf(battle.Formula231),
		"Formula232":                                 reflect.ValueOf(battle.Formula232),
		"Formula233":                                 reflect.ValueOf(battle.Formula233),
		"Formula234":                                 reflect.ValueOf(battle.Formula234),
		"Formula235":                                 reflect.ValueOf(battle.Formula235),
		"Formula236":                                 reflect.ValueOf(battle.Formula236),
		"Formula237":                                 reflect.ValueOf(battle.Formula237),
		"Formula238":                                 reflect.ValueOf(battle.Formula238),
		"Formula239":                                 reflect.ValueOf(battle.Formula239),
		"Formula240":                                 reflect.ValueOf(battle.Formula240),
		"Formula241":                                 reflect.ValueOf(battle.Formula241),
		"Formula242":                                 reflect.ValueOf(battle.Formula242),
		"Formula243":                                 reflect.ValueOf(battle.Formula243),
		"Formula244":                                 reflect.ValueOf(battle.Formula244),
		"Formula245":                                 reflect.ValueOf(battle.Formula245),
		"Formula246":                                 reflect.ValueOf(battle.Formula246),
		"Formula247":                                 reflect.ValueOf(battle.Formula247),
		"Formula248":                                 reflect.ValueOf(battle.Formula248),
		"Formula249":                                 reflect.ValueOf(battle.Formula249),
		"Formula250":                                 reflect.ValueOf(battle.Formula250),
		"Formula251":                                 reflect.ValueOf(battle.Formula251),
		"Formula252":                                 reflect.ValueOf(battle.Formula252),
		"Formula253":                                 reflect.ValueOf(battle.Formula253),
		"Formula254":                                 reflect.ValueOf(battle.Formula254),
		"Formula255":                                 reflect.ValueOf(battle.Formula255),
		"Formula256":                                 reflect.ValueOf(battle.Formula256),
		"Formula257":                                 reflect.ValueOf(battle.Formula257),
		"Formula258":                                 reflect.ValueOf(battle.Formula258),
		"Formula259":                                 reflect.ValueOf(battle.Formula259),
		"Formula260":                                 reflect.ValueOf(battle.Formula260),
		"Formula262":                                 reflect.ValueOf(battle.Formula262),
		"Formula263":                                 reflect.ValueOf(battle.Formula263),
		"Formula264":                                 reflect.ValueOf(battle.Formula264),
		"Formula265":                                 reflect.ValueOf(battle.Formula265),
		"Formula266":                                 reflect.ValueOf(battle.Formula266),
		"Formula267":                                 reflect.ValueOf(battle.Formula267),
		"Formula268":                                 reflect.ValueOf(battle.Formula268),
		"Formula269":                                 reflect.ValueOf(battle.Formula269),
		"Formula270":                                 reflect.ValueOf(battle.Formula270),
		"Formula271":                                 reflect.ValueOf(battle.Formula271),
		"Formula272":                                 reflect.ValueOf(battle.Formula272),
		"Formula273":                                 reflect.ValueOf(battle.Formula273),
		"Formula274":                                 reflect.ValueOf(battle.Formula274),
		"Formula275":                                 reflect.ValueOf(battle.Formula275),
		"Formula501":                                 reflect.ValueOf(battle.Formula501),
		"Formula502":                                 reflect.ValueOf(battle.Formula502),
		"Formula503":                                 reflect.ValueOf(battle.Formula503),
		"Formula504":                                 reflect.ValueOf(battle.Formula504),
		"Formula505":                                 reflect.ValueOf(battle.Formula505),
		"Formula506":                                 reflect.ValueOf(battle.Formula506),
		"Formula508":                                 reflect.ValueOf(battle.Formula508),
		"Formula509":                                 reflect.ValueOf(battle.Formula509),
		"Formula510":                                 reflect.ValueOf(battle.Formula510),
		"Formula511":                                 reflect.ValueOf(battle.Formula511),
		"Formula512":                                 reflect.ValueOf(battle.Formula512),
		"Formula513":                                 reflect.ValueOf(battle.Formula513),
		"Formula514":                                 reflect.ValueOf(battle.Formula514),
		"Formula515":                                 reflect.ValueOf(battle.Formula515),
		"Formula516":                                 reflect.ValueOf(battle.Formula516),
		"Formula517":                                 reflect.ValueOf(battle.Formula517),
		"Formula518":                                 reflect.ValueOf(battle.Formula518),
		"Formula519":                                 reflect.ValueOf(battle.Formula519),
		"Formula520":                                 reflect.ValueOf(battle.Formula520),
		"Formula521":                                 reflect.ValueOf(battle.Formula521),
		"Formula930":                                 reflect.ValueOf(battle.Formula930),
		"Formula931":                                 reflect.ValueOf(battle.Formula931),
		"Formula932":                                 reflect.ValueOf(battle.Formula932),
		"FormulaChangeSeasonLinkEnergy":              reflect.ValueOf(battle.FormulaChangeSeasonLinkEnergy),
		"FormulaChangeValue":                         reflect.ValueOf(battle.FormulaChangeValue),
		"FormulaChangeValueMaxAttackAtk":             reflect.ValueOf(battle.FormulaChangeValueMaxAttackAtk),
		"FormulaFixCure":                             reflect.ValueOf(battle.FormulaFixCure),
		"FormulaFixHurt":                             reflect.ValueOf(battle.FormulaFixHurt),
		"FormulaFixHurtType":                         reflect.ValueOf(battle.FormulaFixHurtType),
		"FormulaHurt517":                             reflect.ValueOf(battle.FormulaHurt517),
		"GVGHurtRateFix":                             reflect.ValueOf(battle.GVGHurtRateFix),
		"GenDebugFormula":                            reflect.ValueOf(battle.GenDebugFormula),
		"GetAddBuffRateDebug":                        reflect.ValueOf(battle.GetAddBuffRateDebug),
		"GetAllSameRowPos":                           reflect.ValueOf(battle.GetAllSameRowPos),
		"GetAllSameRowPosNoSelfNoCall":               reflect.ValueOf(battle.GetAllSameRowPosNoSelfNoCall),
		"GetCondCheckDebug":                          reflect.ValueOf(battle.GetCondCheckDebug),
		"GetDebugResultShow":                         reflect.ValueOf(battle.GetDebugResultShow),
		"GetFormationAltAttr":                        reflect.ValueOf(battle.GetFormationAltAttr),
		"GetMemHpPct":                                reflect.ValueOf(battle.GetMemHpPct),
		"GetMemPosHpPct":                             reflect.ValueOf(battle.GetMemPosHpPct),
		"GetOpponentRiteRestrictState":               reflect.ValueOf(battle.GetOpponentRiteRestrictState),
		"GetRiteAttrsMap":                            reflect.ValueOf(battle.GetRiteAttrsMap),
		"GetRitePowerRaisePSsMap":                    reflect.ValueOf(battle.GetRitePowerRaisePSsMap),
		"GetRiteRaisePSsMap":                         reflect.ValueOf(battle.GetRiteRaisePSsMap),
		"GetRiteRestrictState":                       reflect.ValueOf(battle.GetRiteRestrictState),
		"GuardHurtFormula":                           reflect.ValueOf(battle.GuardHurtFormula),
		"HpAttrCondHpHigher":                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"HpAttrCondHpLower":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"HurtByCallRateFix":                          reflect.ValueOf(battle.HurtByCallRateFix),
		"HurtReflectFormula":                         reflect.ValueOf(battle.HurtReflectFormula),
		"HurtTypeActiveSkillBig":                     reflect.ValueOf(battle.HurtTypeActiveSkillBig),
		"HurtTypeActiveSkillSmall":                   reflect.ValueOf(battle.HurtTypeActiveSkillSmall),
		"HurtTypeBuff":                               reflect.ValueOf(battle.HurtTypeBuff),
		"HurtTypeDoKill":                             reflect.ValueOf(battle.HurtTypeDoKill),
		"HurtTypeGuard":                              reflect.ValueOf(battle.HurtTypeGuard),
		"HurtTypeHurtSelf":                           reflect.ValueOf(battle.HurtTypeHurtSelf),
		"HurtTypeNone":                               reflect.ValueOf(battle.HurtTypeNone),
		"HurtTypeNormalSkill":                        reflect.ValueOf(battle.HurtTypeNormalSkill),
		"HurtTypePassiveSkill":                       reflect.ValueOf(battle.HurtTypePassiveSkill),
		"HurtTypeReflect":                            reflect.ValueOf(battle.HurtTypeReflect),
		"HurtTypeResurrect":                          reflect.ValueOf(battle.HurtTypeResurrect),
		"HurtTypeSkillAttackDead":                    reflect.ValueOf(battle.HurtTypeSkillAttackDead),
		"HurtTypeSuck":                               reflect.ValueOf(battle.HurtTypeSuck),
		"InitPool":                                   reflect.ValueOf(battle.InitPool),
		"IsBattleDebug":                              reflect.ValueOf(battle.IsBattleDebug),
		"IsBenefitBuff":                              reflect.ValueOf(battle.IsBenefitBuff),
		"IsBuffDispel":                               reflect.ValueOf(battle.IsBuffDispel),
		"IsHarmfulBuff":                              reflect.ValueOf(battle.IsHarmfulBuff),
		"MaxRoundNum":                                reflect.ValueOf(battle.MaxRoundNum),
		"MemberJobAssist":                            reflect.ValueOf(battle.MemberJobAssist),
		"MemberJobMagic":                             reflect.ValueOf(battle.MemberJobMagic),
		"MemberJobTank":                              reflect.ValueOf(battle.MemberJobTank),
		"MemberJobWarrior":                           reflect.ValueOf(battle.MemberJobWarrior),
		"MonsterTypeBoss":                            reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"MonsterTypeCall":                            reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"MonsterTypeCallDepartedSpirit":              reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"MonsterTypeCallHero":                        reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"MonsterTypeCallResurrection":                reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"MonsterTypeElite":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"MonsterTypeNone":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"MonsterTypeNormal":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NewAliveNumAttrManager":                     reflect.ValueOf(battle.NewAliveNumAttrManager),
		"NewAltAttr":                                 reflect.ValueOf(battle.NewAltAttr),
		"NewAltRaisePS":                              reflect.ValueOf(battle.NewAltRaisePS),
		"NewArgsBeforeHurtValueFix":                  reflect.ValueOf(battle.NewArgsBeforeHurtValueFix),
		"NewArgsBuffBeAdd":                           reflect.ValueOf(battle.NewArgsBuffBeAdd),
		"NewArgsBuffBeMianYi":                        reflect.ValueOf(battle.NewArgsBuffBeMianYi),
		"NewArgsBuffBeRemove":                        reflect.ValueOf(battle.NewArgsBuffBeRemove),
		"NewArgsDecHp":                               reflect.ValueOf(battle.NewArgsDecHp),
		"NewArtifactSkillM":                          reflect.ValueOf(battle.NewArtifactSkillM),
		"NewAttrManager":                             reflect.ValueOf(battle.NewAttrManager),
		"NewBuff":                                    reflect.ValueOf(battle.NewBuff),
		"NewBuffEventManager":                        reflect.ValueOf(battle.NewBuffEventManager),
		"NewBuffManager":                             reflect.ValueOf(battle.NewBuffManager),
		"NewCopyBuff":                                reflect.ValueOf(battle.NewCopyBuff),
		"NewHadBuffAttrManager":                      reflect.ValueOf(battle.NewHadBuffAttrManager),
		"NewHpAttrManager":                           reflect.ValueOf(battle.NewHpAttrManager),
		"NewManager":                                 reflect.ValueOf(battle.NewManager),
		"NewManagerParams":                           reflect.ValueOf(battle.NewManagerParams),
		"NewMember":                                  reflect.ValueOf(battle.NewMember),
		"NewMemberAndNewSkills":                      reflect.ValueOf(battle.NewMemberAndNewSkills),
		"NewMonster":                                 reflect.ValueOf(battle.NewMonster),
		"NewMonsterGroupTeam":                        reflect.ValueOf(battle.NewMonsterGroupTeam),
		"NewNextSkill":                               reflect.ValueOf(battle.NewNextSkill),
		"NewOneAction":                               reflect.ValueOf(battle.NewOneAction),
		"NewOneRound":                                reflect.ValueOf(battle.NewOneRound),
		"NewOneSkill":                                reflect.ValueOf(battle.NewOneSkill),
		"NewOneSkillAttack":                          reflect.ValueOf(battle.NewOneSkillAttack),
		"NewOneSkillByPassiveSkill":                  reflect.ValueOf(battle.NewOneSkillByPassiveSkill),
		"NewOneSkillEffect":                          reflect.ValueOf(battle.NewOneSkillEffect),
		"NewPosUniter":                               reflect.ValueOf(battle.NewPosUniter),
		"NewPsAttrChange":                            reflect.ValueOf(battle.NewPsAttrChange),
		"NewPsAttrManager":                           reflect.ValueOf(battle.NewPsAttrManager),
		"NewPsSkill":                                 reflect.ValueOf(battle.NewPsSkill),
		"NewPsSkillExe":                              reflect.ValueOf(battle.NewPsSkillExe),
		"NewPsSkillMgr":                              reflect.ValueOf(battle.NewPsSkillMgr),
		"NewPublicHpGroup":                           reflect.ValueOf(battle.NewPublicHpGroup),
		"NewReport":                                  reflect.ValueOf(battle.NewReport),
		"NewSkill":                                   reflect.ValueOf(battle.NewSkill),
		"NewSkillManager":                            reflect.ValueOf(battle.NewSkillManager),
		"NewSoulMember":                              reflect.ValueOf(battle.NewSoulMember),
		"NewTeam":                                    reflect.ValueOf(battle.NewTeam),
		"NewVoid":                                    reflect.ValueOf(battle.NewVoid),
		"NewVoidAttrManager":                         reflect.ValueOf(battle.NewVoidAttrManager),
		"NewVoidMember":                              reflect.ValueOf(battle.NewVoidMember),
		"NextSkillNoModify":                          reflect.ValueOf(battle.NextSkillNoModify),
		"NextSkillTypeCombo":                         reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NextSkillTypeCommon":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NextSkillTypeFightBack":                     reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"NextSkillTypeNone":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"NextSkillTypeUnion":                         reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"OpenBattleDebug":                            reflect.ValueOf(battle.OpenBattleDebug),
		"OpenDebugAttr":                              reflect.ValueOf(battle.OpenDebugAttr),
		"OpenDebugBuff":                              reflect.ValueOf(battle.OpenDebugBuff),
		"OpenDebugEvent":                             reflect.ValueOf(battle.OpenDebugEvent),
		"OpenDebugFormula":                           reflect.ValueOf(battle.OpenDebugFormula),
		"PSEffectOneAction":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PSEffectOneAttack":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PSEffectOneBattle":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"PSEffectOneSkill":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PVPHurtRateFix":                             reflect.ValueOf(battle.PVPHurtRateFix),
		"PassiveType1":                               reflect.ValueOf(battle.PassiveType1),
		"PassiveType2":                               reflect.ValueOf(battle.PassiveType2),
		"PassiveType3":                               reflect.ValueOf(battle.PassiveType3),
		"PassiveType4":                               reflect.ValueOf(battle.PassiveType4),
		"PassiveType5":                               reflect.ValueOf(battle.PassiveType5),
		"PassiveTypeNone":                            reflect.ValueOf(battle.PassiveTypeNone),
		"PsAttrActiveSkillCritDefPunctureRate":       reflect.ValueOf(battle.PsAttrActiveSkillCritDefPunctureRate),
		"PsAttrAddBuffRate":                          reflect.ValueOf(battle.PsAttrAddBuffRate),
		"PsAttrAddTypeAdd":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PsAttrAddTypeMax":                           reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PsAttrAddTypeMin":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PsAttrAddTypeNone":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"PsAttrAttrLimitFix":                         reflect.ValueOf(battle.PsAttrAttrLimitFix),
		"PsAttrBeShield":                             reflect.ValueOf(battle.PsAttrBeShield),
		"PsAttrBigHurtRate":                          reflect.ValueOf(battle.PsAttrBigHurtRate),
		"PsAttrBuffAttrFix":                          reflect.ValueOf(battle.PsAttrBuffAttrFix),
		"PsAttrCallDamAddRate":                       reflect.ValueOf(battle.PsAttrCallDamAddRate),
		"PsAttrCallDeadEventAddTriggerCount":         reflect.ValueOf(battle.PsAttrCallDeadEventAddTriggerCount),
		"PsAttrCallEventAddTriggerCount":             reflect.ValueOf(battle.PsAttrCallEventAddTriggerCount),
		"PsAttrCorruption":                           reflect.ValueOf(battle.PsAttrCorruption),
		"PsAttrCureParam":                            reflect.ValueOf(battle.PsAttrCureParam),
		"PsAttrDeadEventAddTriggerCount":             reflect.ValueOf(battle.PsAttrDeadEventAddTriggerCount),
		"PsAttrDecCritDamageFix":                     reflect.ValueOf(battle.PsAttrDecCritDamageFix),
		"PsAttrDefHurtedHpMax":                       reflect.ValueOf(battle.PsAttrDefHurtedHpMax),
		"PsAttrDefHurtedMax":                         reflect.ValueOf(battle.PsAttrDefHurtedMax),
		"PsAttrDefHurtedMaxByAttackAtk":              reflect.ValueOf(battle.PsAttrDefHurtedMaxByAttackAtk),
		"PsAttrDoKill":                               reflect.ValueOf(battle.PsAttrDoKill),
		"PsAttrHurtParam":                            reflect.ValueOf(battle.PsAttrHurtParam),
		"PsAttrHurtParamFix":                         reflect.ValueOf(battle.PsAttrHurtParamFix),
		"PsAttrHurtReduceByDivided":                  reflect.ValueOf(battle.PsAttrHurtReduceByDivided),
		"PsAttrHurtedHpPctMax":                       reflect.ValueOf(battle.PsAttrHurtedHpPctMax),
		"PsAttrHurtedHpPctMax2":                      reflect.ValueOf(battle.PsAttrHurtedHpPctMax2),
		"PsAttrInit":                                 reflect.ValueOf(battle.PsAttrInit),
		"PsAttrNormalHurtRate":                       reflect.ValueOf(battle.PsAttrNormalHurtRate),
		"PsAttrNotConsume":                           reflect.ValueOf(battle.PsAttrNotConsume),
		"PsAttrOverCureRate":                         reflect.ValueOf(battle.PsAttrOverCureRate),
		"PsAttrPsType1AddRate":                       reflect.ValueOf(battle.PsAttrPsType1AddRate),
		"PsAttrPsType2AddRate":                       reflect.ValueOf(battle.PsAttrPsType2AddRate),
		"PsAttrPsType3FixValue":                      reflect.ValueOf(battle.PsAttrPsType3FixValue),
		"PsAttrPsType4AddRate":                       reflect.ValueOf(battle.PsAttrPsType4AddRate),
		"PsAttrPsType5FixValue":                      reflect.ValueOf(battle.PsAttrPsType5FixValue),
		"PsAttrReduceBigHurtRate":                    reflect.ValueOf(battle.PsAttrReduceBigHurtRate),
		"PsAttrReduceCritDamRate":                    reflect.ValueOf(battle.PsAttrReduceCritDamRate),
		"PsAttrReduceNormalHurtRate":                 reflect.ValueOf(battle.PsAttrReduceNormalHurtRate),
		"PsAttrReduceSmallHurtRate":                  reflect.ValueOf(battle.PsAttrReduceSmallHurtRate),
		"PsAttrReflect":                              reflect.ValueOf(battle.PsAttrReflect),
		"PsAttrSeasonLinkEnergyFix":                  reflect.ValueOf(battle.PsAttrSeasonLinkEnergyFix),
		"PsAttrSeasonLinkNegEnergyFix":               reflect.ValueOf(battle.PsAttrSeasonLinkNegEnergyFix),
		"PsAttrSeasonMagDamAddRate":                  reflect.ValueOf(battle.PsAttrSeasonMagDamAddRate),
		"PsAttrSeasonMagDamAddRatePct":               reflect.ValueOf(battle.PsAttrSeasonMagDamAddRatePct),
		"PsAttrSeasonMagDamReduceRate":               reflect.ValueOf(battle.PsAttrSeasonMagDamReduceRate),
		"PsAttrSeasonMagDamReduceRatePct":            reflect.ValueOf(battle.PsAttrSeasonMagDamReduceRatePct),
		"PsAttrSeasonPhyDamAddRate":                  reflect.ValueOf(battle.PsAttrSeasonPhyDamAddRate),
		"PsAttrSeasonPhyDamAddRatePct":               reflect.ValueOf(battle.PsAttrSeasonPhyDamAddRatePct),
		"PsAttrSeasonPhyDamReduceRate":               reflect.ValueOf(battle.PsAttrSeasonPhyDamReduceRate),
		"PsAttrSeasonPhyDamReduceRatePct":            reflect.ValueOf(battle.PsAttrSeasonPhyDamReduceRatePct),
		"PsAttrShield":                               reflect.ValueOf(battle.PsAttrShield),
		"PsAttrSkillParam":                           reflect.ValueOf(battle.PsAttrSkillParam),
		"PsAttrSmallHurtRate":                        reflect.ValueOf(battle.PsAttrSmallHurtRate),
		"PsAttrStuneRate":                            reflect.ValueOf(battle.PsAttrStuneRate),
		"PsAttrTotal":                                reflect.ValueOf(battle.PsAttrTotal),
		"PsMaxTriggerCount":                          reflect.ValueOf(battle.PsMaxTriggerCount),
		"PsSkillMaxStage":                            reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"RecordBuffLayer":                            reflect.ValueOf(&battle.RecordBuffLayer).Elem(),
		"ResetDefaultCheckSeason":                    reflect.ValueOf(battle.ResetDefaultCheckSeason),
		"ResetPool":                                  reflect.ValueOf(battle.ResetPool),
		"RoundActionTimesMax":                        reflect.ValueOf(constant.MakeFromLiteral("200", token.INT, 0)),
		"SealPassiveSkill":                           reflect.ValueOf(battle.SealPassiveSkill),
		"SeasonHeroHurtRateFix":                      reflect.ValueOf(battle.SeasonHeroHurtRateFix),
		"SeasonHurtRateFix":                          reflect.ValueOf(battle.SeasonHurtRateFix),
		"SeasonLinkHurtRateFix":                      reflect.ValueOf(battle.SeasonLinkHurtRateFix),
		"SetDefaultMaxSeasonEnergy":                  reflect.ValueOf(battle.SetDefaultMaxSeasonEnergy),
		"SetNotCheckSeason":                          reflect.ValueOf(battle.SetNotCheckSeason),
		"SetStrictPool":                              reflect.ValueOf(battle.SetStrictPool),
		"SettleBuff":                                 reflect.ValueOf(battle.SettleBuff),
		"SkillAttackTypeCure":                        reflect.ValueOf(battle.SkillAttackTypeCure),
		"SkillAttackTypeHurt":                        reflect.ValueOf(battle.SkillAttackTypeHurt),
		"SkillAttackTypeSpec":                        reflect.ValueOf(battle.SkillAttackTypeSpec),
		"SkillEffectTypeAddBuff":                     reflect.ValueOf(battle.SkillEffectTypeAddBuff),
		"SkillEffectTypeCall":                        reflect.ValueOf(battle.SkillEffectTypeCall),
		"SkillEffectTypeCure":                        reflect.ValueOf(battle.SkillEffectTypeCure),
		"SkillEffectTypeEffectBuffAttr":              reflect.ValueOf(battle.SkillEffectTypeEffectBuffAttr),
		"SkillEffectTypeHurt":                        reflect.ValueOf(battle.SkillEffectTypeHurt),
		"SkillEffectTypeHurtSelf":                    reflect.ValueOf(battle.SkillEffectTypeHurtSelf),
		"SkillEffectTypeRemoveBuff":                  reflect.ValueOf(battle.SkillEffectTypeRemoveBuff),
		"SkillEffectTypeResurrectionAndCured":        reflect.ValueOf(battle.SkillEffectTypeResurrectionAndCured),
		"SkillMaxStage":                              reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"SkillTauntSkill":                            reflect.ValueOf(battle.SkillTauntSkill),
		"SkillTypeActiveBig":                         reflect.ValueOf(battle.SkillTypeActiveBig),
		"SkillTypeActiveSmall":                       reflect.ValueOf(battle.SkillTypeActiveSmall),
		"SkillTypeAttr":                              reflect.ValueOf(battle.SkillTypeAttr),
		"SkillTypeNormal":                            reflect.ValueOf(battle.SkillTypeNormal),
		"SkillTypePassive":                           reflect.ValueOf(battle.SkillTypePassive),
		"SortGetMax":                                 reflect.ValueOf(battle.SortGetMax),
		"SortGetMin":                                 reflect.ValueOf(battle.SortGetMin),
		"StaAccumulateHurt":                          reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"StaBlessNoDebuff":                           reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"StaBlessNoHurted":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"StaExile":                                   reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"StaForbidAddBuff":                           reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"StaHide":                                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"StaIgnoreShield":                            reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"StaMax":                                     reflect.ValueOf(constant.MakeFromLiteral("64", token.INT, 0)),
		"StaNoDebuff":                                reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"StaNoHurted":                                reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"StaNoSkillAttack":                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"StaResurrect":                               reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"StaResurrectioned":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"StaSealPsSkill":                             reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"StaSkillAttackEnd":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"StaSkillAttacking":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"StageOneActionEnd":                          reflect.ValueOf(battle.StageOneActionEnd),
		"StageOneActionPrepare":                      reflect.ValueOf(battle.StageOneActionPrepare),
		"StageOneRoundEnd":                           reflect.ValueOf(battle.StageOneRoundEnd),
		"StageOneRoundPrepare":                       reflect.ValueOf(battle.StageOneRoundPrepare),
		"StageOneSkillEnd":                           reflect.ValueOf(battle.StageOneSkillEnd),
		"StageOneSkillPrepare":                       reflect.ValueOf(battle.StageOneSkillPrepare),
		"StrictPool":                                 reflect.ValueOf(battle.StrictPool),
		"SuckBloodFormula":                           reflect.ValueOf(battle.SuckBloodFormula),
		"TargetCanEmpty":                             reflect.ValueOf(battle.TargetCanEmpty),
		"TargetNeedAlive":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"TargetNeedDead":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TargetNoEmpty":                              reflect.ValueOf(battle.TargetNoEmpty),
		"TargetNoNeedAlive":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TargetPos1":                                 reflect.ValueOf(battle.TargetPos1),
		"TargetPos2":                                 reflect.ValueOf(battle.TargetPos2),
		"TargetPos3":                                 reflect.ValueOf(battle.TargetPos3),
		"TargetPos4":                                 reflect.ValueOf(battle.TargetPos4),
		"TargetPos5":                                 reflect.ValueOf(battle.TargetPos5),
		"TeamMaxPos":                                 reflect.ValueOf(battle.TeamMaxPos),
		"TeamMaxPosWithCallPos":                      reflect.ValueOf(battle.TeamMaxPosWithCallPos),
		"TeamUniterBattlePos":                        reflect.ValueOf(battle.TeamUniterBattlePos),
		"UniterAlive":                                reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"UniterDead":                                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"UniterMaxNum":                               reflect.ValueOf(battle.UniterMaxNum),
		"UniterTypeArtifact":                         reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"UniterTypeArtifactM":                        reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"UniterTypeMember":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"UniterTypeNone":                             reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"UniterTypePos":                              reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"UniterTypeTeamUniter":                       reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"UniterTypeVoidMember":                       reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"YishangHurtRateFix":                         reflect.ValueOf(battle.YishangHurtRateFix),

		// type definitions
		"AddBuffResult":           reflect.ValueOf((*battle.AddBuffResult)(nil)),
		"AddRateConditionFunc":    reflect.ValueOf((*battle.AddRateConditionFunc)(nil)),
		"AliveNumAttr":            reflect.ValueOf((*battle.AliveNumAttr)(nil)),
		"AliveNumAttrManager":     reflect.ValueOf((*battle.AliveNumAttrManager)(nil)),
		"AltAttr":                 reflect.ValueOf((*battle.AltAttr)(nil)),
		"AltRaisePS":              reflect.ValueOf((*battle.AltRaisePS)(nil)),
		"ArgsBeforeHurtValueFix":  reflect.ValueOf((*battle.ArgsBeforeHurtValueFix)(nil)),
		"ArgsBuffBeAdd":           reflect.ValueOf((*battle.ArgsBuffBeAdd)(nil)),
		"ArgsBuffBeMianYi":        reflect.ValueOf((*battle.ArgsBuffBeMianYi)(nil)),
		"ArgsBuffBeRemove":        reflect.ValueOf((*battle.ArgsBuffBeRemove)(nil)),
		"ArgsDecHp":               reflect.ValueOf((*battle.ArgsDecHp)(nil)),
		"Artifact":                reflect.ValueOf((*battle.Artifact)(nil)),
		"ArtifactManager":         reflect.ValueOf((*battle.ArtifactManager)(nil)),
		"ArtifactSkillManager":    reflect.ValueOf((*battle.ArtifactSkillManager)(nil)),
		"AttrManager":             reflect.ValueOf((*battle.AttrManager)(nil)),
		"BaseChooseConditionFunc": reflect.ValueOf((*battle.BaseChooseConditionFunc)(nil)),
		"BaseChooseSortFunc":      reflect.ValueOf((*battle.BaseChooseSortFunc)(nil)),
		"BaseFilterFunc":          reflect.ValueOf((*battle.BaseFilterFunc)(nil)),
		"BaseFilterInheritFunc":   reflect.ValueOf((*battle.BaseFilterInheritFunc)(nil)),
		"BattleLimitValue":        reflect.ValueOf((*battle.BattleLimitValue)(nil)),
		"BitSet64":                reflect.ValueOf((*battle.BitSet64)(nil)),
		"Buff":                    reflect.ValueOf((*battle.Buff)(nil)),
		"BuffConditionFunc":       reflect.ValueOf((*battle.BuffConditionFunc)(nil)),
		"BuffEffect":              reflect.ValueOf((*battle.BuffEffect)(nil)),
		"BuffEffectFunc":          reflect.ValueOf((*battle.BuffEffectFunc)(nil)),
		"BuffEventManager":        reflect.ValueOf((*battle.BuffEventManager)(nil)),
		"BuffManager":             reflect.ValueOf((*battle.BuffManager)(nil)),
		"ChangeFlagManager":       reflect.ValueOf((*battle.ChangeFlagManager)(nil)),
		"CheckPsEventArgsFunc":    reflect.ValueOf((*battle.CheckPsEventArgsFunc)(nil)),
		"CmpUniter":               reflect.ValueOf((*battle.CmpUniter)(nil)),
		"ConditionFunc":           reflect.ValueOf((*battle.ConditionFunc)(nil)),
		"CureResult":              reflect.ValueOf((*battle.CureResult)(nil)),
		"EffectBuffFunc":          reflect.ValueOf((*battle.EffectBuffFunc)(nil)),
		"ExecutionPool":           reflect.ValueOf((*battle.ExecutionPool)(nil)),
		"FormulaFunc":             reflect.ValueOf((*battle.FormulaFunc)(nil)),
		"HadBuffAttr":             reflect.ValueOf((*battle.HadBuffAttr)(nil)),
		"HadBuffAttrManager":      reflect.ValueOf((*battle.HadBuffAttrManager)(nil)),
		"HpAttr":                  reflect.ValueOf((*battle.HpAttr)(nil)),
		"HpAttrManager":           reflect.ValueOf((*battle.HpAttrManager)(nil)),
		"HurtResult":              reflect.ValueOf((*battle.HurtResult)(nil)),
		"IAttrM":                  reflect.ValueOf((*battle.IAttrM)(nil)),
		"IBuff":                   reflect.ValueOf((*battle.IBuff)(nil)),
		"IBuffDataInfo":           reflect.ValueOf((*battle.IBuffDataInfo)(nil)),
		"ICanAddRoundAttackHurt":  reflect.ValueOf((*battle.ICanAddRoundAttackHurt)(nil)),
		"IDecHp":                  reflect.ValueOf((*battle.IDecHp)(nil)),
		"IExecution":              reflect.ValueOf((*battle.IExecution)(nil)),
		"IHasAttack":              reflect.ValueOf((*battle.IHasAttack)(nil)),
		"IHasBuffInfo":            reflect.ValueOf((*battle.IHasBuffInfo)(nil)),
		"IHasChangeAttrTime":      reflect.ValueOf((*battle.IHasChangeAttrTime)(nil)),
		"IHasCrit":                reflect.ValueOf((*battle.IHasCrit)(nil)),
		"IHasDefense":             reflect.ValueOf((*battle.IHasDefense)(nil)),
		"IHasKill":                reflect.ValueOf((*battle.IHasKill)(nil)),
		"IHasOverCure":            reflect.ValueOf((*battle.IHasOverCure)(nil)),
		"IHasRound":               reflect.ValueOf((*battle.IHasRound)(nil)),
		"IHasSkillStageTarget":    reflect.ValueOf((*battle.IHasSkillStageTarget)(nil)),
		"IHasSkillType":           reflect.ValueOf((*battle.IHasSkillType)(nil)),
		"IHasTarget":              reflect.ValueOf((*battle.IHasTarget)(nil)),
		"IHurtInfo":               reflect.ValueOf((*battle.IHurtInfo)(nil)),
		"IInBuffBeRemove":         reflect.ValueOf((*battle.IInBuffBeRemove)(nil)),
		"IInOneSkill":             reflect.ValueOf((*battle.IInOneSkill)(nil)),
		"IInOneSkillAttackEnd":    reflect.ValueOf((*battle.IInOneSkillAttackEnd)(nil)),
		"IInOneSkillEffect":       reflect.ValueOf((*battle.IInOneSkillEffect)(nil)),
		"IOneBattle":              reflect.ValueOf((*battle.IOneBattle)(nil)),
		"IOneSkill":               reflect.ValueOf((*battle.IOneSkill)(nil)),
		"IOneSkillEffect":         reflect.ValueOf((*battle.IOneSkillEffect)(nil)),
		"IPoolObj":                reflect.ValueOf((*battle.IPoolObj)(nil)),
		"ISkillManager":           reflect.ValueOf((*battle.ISkillManager)(nil)),
		"Manager":                 reflect.ValueOf((*battle.Manager)(nil)),
		"ManagerGlobalValue":      reflect.ValueOf((*battle.ManagerGlobalValue)(nil)),
		"ManagerParams":           reflect.ValueOf((*battle.ManagerParams)(nil)),
		"ManagerStatus":           reflect.ValueOf((*battle.ManagerStatus)(nil)),
		"Member":                  reflect.ValueOf((*battle.Member)(nil)),
		"MemberHeap":              reflect.ValueOf((*battle.MemberHeap)(nil)),
		"NextSkill":               reflect.ValueOf((*battle.NextSkill)(nil)),
		"OneAction":               reflect.ValueOf((*battle.OneAction)(nil)),
		"OneRound":                reflect.ValueOf((*battle.OneRound)(nil)),
		"OneSkill":                reflect.ValueOf((*battle.OneSkill)(nil)),
		"OneSkillAttack":          reflect.ValueOf((*battle.OneSkillAttack)(nil)),
		"OneSkillEffect":          reflect.ValueOf((*battle.OneSkillEffect)(nil)),
		"PoolType":                reflect.ValueOf((*battle.PoolType)(nil)),
		"PosUniter":               reflect.ValueOf((*battle.PosUniter)(nil)),
		"PsAttrChange":            reflect.ValueOf((*battle.PsAttrChange)(nil)),
		"PsAttrManager":           reflect.ValueOf((*battle.PsAttrManager)(nil)),
		"PsSkill":                 reflect.ValueOf((*battle.PsSkill)(nil)),
		"PsSkillExe":              reflect.ValueOf((*battle.PsSkillExe)(nil)),
		"PsSkillMgr":              reflect.ValueOf((*battle.PsSkillMgr)(nil)),
		"PublicHpGroup":           reflect.ValueOf((*battle.PublicHpGroup)(nil)),
		"RemoveBuffResult":        reflect.ValueOf((*battle.RemoveBuffResult)(nil)),
		"Report":                  reflect.ValueOf((*battle.Report)(nil)),
		"ReviseFunc":              reflect.ValueOf((*battle.ReviseFunc)(nil)),
		"Skill":                   reflect.ValueOf((*battle.Skill)(nil)),
		"SkillManager":            reflect.ValueOf((*battle.SkillManager)(nil)),
		"TargetFunc":              reflect.ValueOf((*battle.TargetFunc)(nil)),
		"Team":                    reflect.ValueOf((*battle.Team)(nil)),
		"TeamBase":                reflect.ValueOf((*battle.TeamBase)(nil)),
		"TeamEffect":              reflect.ValueOf((*battle.TeamEffect)(nil)),
		"TeamUniter":              reflect.ValueOf((*battle.TeamUniter)(nil)),
		"Uniter":                  reflect.ValueOf((*battle.Uniter)(nil)),
		"UniterArray":             reflect.ValueOf((*battle.UniterArray)(nil)),
		"Uniters":                 reflect.ValueOf((*battle.Uniters)(nil)),
		"VoidAttrManager":         reflect.ValueOf((*battle.VoidAttrManager)(nil)),
		"VoidMember":              reflect.ValueOf((*battle.VoidMember)(nil)),

		// interface wrapper definitions
		"_IAttrM":                 reflect.ValueOf((*_app_logic_battle_IAttrM)(nil)),
		"_IBuff":                  reflect.ValueOf((*_app_logic_battle_IBuff)(nil)),
		"_IBuffDataInfo":          reflect.ValueOf((*_app_logic_battle_IBuffDataInfo)(nil)),
		"_ICanAddRoundAttackHurt": reflect.ValueOf((*_app_logic_battle_ICanAddRoundAttackHurt)(nil)),
		"_IDecHp":                 reflect.ValueOf((*_app_logic_battle_IDecHp)(nil)),
		"_IExecution":             reflect.ValueOf((*_app_logic_battle_IExecution)(nil)),
		"_IHasAttack":             reflect.ValueOf((*_app_logic_battle_IHasAttack)(nil)),
		"_IHasBuffInfo":           reflect.ValueOf((*_app_logic_battle_IHasBuffInfo)(nil)),
		"_IHasChangeAttrTime":     reflect.ValueOf((*_app_logic_battle_IHasChangeAttrTime)(nil)),
		"_IHasCrit":               reflect.ValueOf((*_app_logic_battle_IHasCrit)(nil)),
		"_IHasDefense":            reflect.ValueOf((*_app_logic_battle_IHasDefense)(nil)),
		"_IHasKill":               reflect.ValueOf((*_app_logic_battle_IHasKill)(nil)),
		"_IHasOverCure":           reflect.ValueOf((*_app_logic_battle_IHasOverCure)(nil)),
		"_IHasRound":              reflect.ValueOf((*_app_logic_battle_IHasRound)(nil)),
		"_IHasSkillStageTarget":   reflect.ValueOf((*_app_logic_battle_IHasSkillStageTarget)(nil)),
		"_IHasSkillType":          reflect.ValueOf((*_app_logic_battle_IHasSkillType)(nil)),
		"_IHasTarget":             reflect.ValueOf((*_app_logic_battle_IHasTarget)(nil)),
		"_IHurtInfo":              reflect.ValueOf((*_app_logic_battle_IHurtInfo)(nil)),
		"_IInBuffBeRemove":        reflect.ValueOf((*_app_logic_battle_IInBuffBeRemove)(nil)),
		"_IInOneSkill":            reflect.ValueOf((*_app_logic_battle_IInOneSkill)(nil)),
		"_IInOneSkillAttackEnd":   reflect.ValueOf((*_app_logic_battle_IInOneSkillAttackEnd)(nil)),
		"_IInOneSkillEffect":      reflect.ValueOf((*_app_logic_battle_IInOneSkillEffect)(nil)),
		"_IOneBattle":             reflect.ValueOf((*_app_logic_battle_IOneBattle)(nil)),
		"_IOneSkill":              reflect.ValueOf((*_app_logic_battle_IOneSkill)(nil)),
		"_IOneSkillEffect":        reflect.ValueOf((*_app_logic_battle_IOneSkillEffect)(nil)),
		"_IPoolObj":               reflect.ValueOf((*_app_logic_battle_IPoolObj)(nil)),
		"_ISkillManager":          reflect.ValueOf((*_app_logic_battle_ISkillManager)(nil)),
		"_Uniter":                 reflect.ValueOf((*_app_logic_battle_Uniter)(nil)),
	}
}

// _app_logic_battle_IAttrM is an interface wrapper for IAttrM type
type _app_logic_battle_IAttrM struct {
	IValue        interface{}
	WAttrChanged  func(a0 int)
	WChangeAttr   func(a0 uint32, a1 uint32, a2 int, a3 int64)
	WChangeBuff   func(a0 bool, a1 uint32)
	WChangeHpAttr func()
	WClearAttr    func(a0 uint32)
	WGetAttr      func(a0 int) int64
	WGetBaseAttr  func(a0 int) int64
	WGetInitAttr  func(a0 int) int64
}

func (W _app_logic_battle_IAttrM) AttrChanged(a0 int) {
	W.WAttrChanged(a0)
}
func (W _app_logic_battle_IAttrM) ChangeAttr(a0 uint32, a1 uint32, a2 int, a3 int64) {
	W.WChangeAttr(a0, a1, a2, a3)
}
func (W _app_logic_battle_IAttrM) ChangeBuff(a0 bool, a1 uint32) {
	W.WChangeBuff(a0, a1)
}
func (W _app_logic_battle_IAttrM) ChangeHpAttr() {
	W.WChangeHpAttr()
}
func (W _app_logic_battle_IAttrM) ClearAttr(a0 uint32) {
	W.WClearAttr(a0)
}
func (W _app_logic_battle_IAttrM) GetAttr(a0 int) int64 {
	return W.WGetAttr(a0)
}
func (W _app_logic_battle_IAttrM) GetBaseAttr(a0 int) int64 {
	return W.WGetBaseAttr(a0)
}
func (W _app_logic_battle_IAttrM) GetInitAttr(a0 int) int64 {
	return W.WGetInitAttr(a0)
}

// _app_logic_battle_IBuff is an interface wrapper for IBuff type
type _app_logic_battle_IBuff struct {
	IValue      interface{}
	WBeActive   func(a0 battle.IExecution)
	WBeCancel   func()
	WDecrLayer  func(count uint32, iexe battle.IExecution, attack battle.Uniter)
	WID         func() uint32
	WOwner      func() battle.Uniter
	WRemoveSelf func(a0 battle.IExecution)
	WSysID      func() uint32
}

func (W _app_logic_battle_IBuff) BeActive(a0 battle.IExecution) {
	W.WBeActive(a0)
}
func (W _app_logic_battle_IBuff) BeCancel() {
	W.WBeCancel()
}
func (W _app_logic_battle_IBuff) DecrLayer(count uint32, iexe battle.IExecution, attack battle.Uniter) {
	W.WDecrLayer(count, iexe, attack)
}
func (W _app_logic_battle_IBuff) ID() uint32 {
	return W.WID()
}
func (W _app_logic_battle_IBuff) Owner() battle.Uniter {
	return W.WOwner()
}
func (W _app_logic_battle_IBuff) RemoveSelf(a0 battle.IExecution) {
	W.WRemoveSelf(a0)
}
func (W _app_logic_battle_IBuff) SysID() uint32 {
	return W.WSysID()
}

// _app_logic_battle_IBuffDataInfo is an interface wrapper for IBuffDataInfo type
type _app_logic_battle_IBuffDataInfo struct {
	IValue        interface{}
	WGetBuffLayer func() uint32
}

func (W _app_logic_battle_IBuffDataInfo) GetBuffLayer() uint32 {
	return W.WGetBuffLayer()
}

// _app_logic_battle_ICanAddRoundAttackHurt is an interface wrapper for ICanAddRoundAttackHurt type
type _app_logic_battle_ICanAddRoundAttackHurt struct {
	IValue              interface{}
	WAddRoundAttackHurt func(a0 int64)
}

func (W _app_logic_battle_ICanAddRoundAttackHurt) AddRoundAttackHurt(a0 int64) {
	W.WAddRoundAttackHurt(a0)
}

// _app_logic_battle_IDecHp is an interface wrapper for IDecHp type
type _app_logic_battle_IDecHp struct {
	IValue     interface{}
	WGetLoseHp func() int64
}

func (W _app_logic_battle_IDecHp) GetLoseHp() int64 {
	return W.WGetLoseHp()
}

// _app_logic_battle_IExecution is an interface wrapper for IExecution type
type _app_logic_battle_IExecution struct {
	IValue              interface{}
	WGetArgs            func() interface{}
	WGetArgsForTarget   func() interface{}
	WGetExecutionParams func(a0 *bt.BaseEffectInfo)
	WGetExecutionType   func() bt.SourceType
	WUpdateCureResult   func(a0 *battle.CureResult)
	WUpdateHurtResult   func(a0 *battle.HurtResult)
}

func (W _app_logic_battle_IExecution) GetArgs() interface{} {
	return W.WGetArgs()
}
func (W _app_logic_battle_IExecution) GetArgsForTarget() interface{} {
	return W.WGetArgsForTarget()
}
func (W _app_logic_battle_IExecution) GetExecutionParams(a0 *bt.BaseEffectInfo) {
	W.WGetExecutionParams(a0)
}
func (W _app_logic_battle_IExecution) GetExecutionType() bt.SourceType {
	return W.WGetExecutionType()
}
func (W _app_logic_battle_IExecution) UpdateCureResult(a0 *battle.CureResult) {
	W.WUpdateCureResult(a0)
}
func (W _app_logic_battle_IExecution) UpdateHurtResult(a0 *battle.HurtResult) {
	W.WUpdateHurtResult(a0)
}

// _app_logic_battle_IHasAttack is an interface wrapper for IHasAttack type
type _app_logic_battle_IHasAttack struct {
	IValue     interface{}
	WGetAttack func() battle.Uniter
}

func (W _app_logic_battle_IHasAttack) GetAttack() battle.Uniter {
	return W.WGetAttack()
}

// _app_logic_battle_IHasBuffInfo is an interface wrapper for IHasBuffInfo type
type _app_logic_battle_IHasBuffInfo struct {
	IValue           interface{}
	WGetBuffInfo     func() *goxml.BuffInfo
	WGetBuffTypeInfo func() *goxml.BuffTypeInfo
}

func (W _app_logic_battle_IHasBuffInfo) GetBuffInfo() *goxml.BuffInfo {
	return W.WGetBuffInfo()
}
func (W _app_logic_battle_IHasBuffInfo) GetBuffTypeInfo() *goxml.BuffTypeInfo {
	return W.WGetBuffTypeInfo()
}

// _app_logic_battle_IHasChangeAttrTime is an interface wrapper for IHasChangeAttrTime type
type _app_logic_battle_IHasChangeAttrTime struct {
	IValue             interface{}
	WAddAttrChange     func(a0 battle.Uniter)
	WAddPsAttrChange   func(a0 battle.Uniter)
	WGetChangeAttrTime func() uint32
}

func (W _app_logic_battle_IHasChangeAttrTime) AddAttrChange(a0 battle.Uniter) {
	W.WAddAttrChange(a0)
}
func (W _app_logic_battle_IHasChangeAttrTime) AddPsAttrChange(a0 battle.Uniter) {
	W.WAddPsAttrChange(a0)
}
func (W _app_logic_battle_IHasChangeAttrTime) GetChangeAttrTime() uint32 {
	return W.WGetChangeAttrTime()
}

// _app_logic_battle_IHasCrit is an interface wrapper for IHasCrit type
type _app_logic_battle_IHasCrit struct {
	IValue      interface{}
	WGetHasCrit func() bool
}

func (W _app_logic_battle_IHasCrit) GetHasCrit() bool {
	return W.WGetHasCrit()
}

// _app_logic_battle_IHasDefense is an interface wrapper for IHasDefense type
type _app_logic_battle_IHasDefense struct {
	IValue      interface{}
	WGetDefense func() battle.Uniter
}

func (W _app_logic_battle_IHasDefense) GetDefense() battle.Uniter {
	return W.WGetDefense()
}

// _app_logic_battle_IHasKill is an interface wrapper for IHasKill type
type _app_logic_battle_IHasKill struct {
	IValue      interface{}
	WGetHasKill func() bool
}

func (W _app_logic_battle_IHasKill) GetHasKill() bool {
	return W.WGetHasKill()
}

// _app_logic_battle_IHasOverCure is an interface wrapper for IHasOverCure type
type _app_logic_battle_IHasOverCure struct {
	IValue       interface{}
	WGetOverCure func() int64
}

func (W _app_logic_battle_IHasOverCure) GetOverCure() int64 {
	return W.WGetOverCure()
}

// _app_logic_battle_IHasRound is an interface wrapper for IHasRound type
type _app_logic_battle_IHasRound struct {
	IValue    interface{}
	WGetRound func() uint32
}

func (W _app_logic_battle_IHasRound) GetRound() uint32 {
	return W.WGetRound()
}

// _app_logic_battle_IHasSkillStageTarget is an interface wrapper for IHasSkillStageTarget type
type _app_logic_battle_IHasSkillStageTarget struct {
	IValue           interface{}
	WGetSkillTargets func(a0 int) *battle.Uniters
}

func (W _app_logic_battle_IHasSkillStageTarget) GetSkillTargets(a0 int) *battle.Uniters {
	return W.WGetSkillTargets(a0)
}

// _app_logic_battle_IHasSkillType is an interface wrapper for IHasSkillType type
type _app_logic_battle_IHasSkillType struct {
	IValue        interface{}
	WGetSkillType func() uint32
}

func (W _app_logic_battle_IHasSkillType) GetSkillType() uint32 {
	return W.WGetSkillType()
}

// _app_logic_battle_IHasTarget is an interface wrapper for IHasTarget type
type _app_logic_battle_IHasTarget struct {
	IValue      interface{}
	WGetTargets func() *battle.Uniters
}

func (W _app_logic_battle_IHasTarget) GetTargets() *battle.Uniters {
	return W.WGetTargets()
}

// _app_logic_battle_IHurtInfo is an interface wrapper for IHurtInfo type
type _app_logic_battle_IHurtInfo struct {
	IValue       interface{}
	WGetHurtType func() bt.SpecialType
}

func (W _app_logic_battle_IHurtInfo) GetHurtType() bt.SpecialType {
	return W.WGetHurtType()
}

// _app_logic_battle_IInBuffBeRemove is an interface wrapper for IInBuffBeRemove type
type _app_logic_battle_IInBuffBeRemove struct {
	IValue         interface{}
	WGetAbsortHurt func() int64
}

func (W _app_logic_battle_IInBuffBeRemove) GetAbsortHurt() int64 {
	return W.WGetAbsortHurt()
}

// _app_logic_battle_IInOneSkill is an interface wrapper for IInOneSkill type
type _app_logic_battle_IInOneSkill struct {
	IValue             interface{}
	WGetIsInFollow     func() bool
	WGetNextParam      func() int32
	WGetNowSkill       func() *battle.Skill
	WGetOneSkillAutoID func() uint32
}

func (W _app_logic_battle_IInOneSkill) GetIsInFollow() bool {
	return W.WGetIsInFollow()
}
func (W _app_logic_battle_IInOneSkill) GetNextParam() int32 {
	return W.WGetNextParam()
}
func (W _app_logic_battle_IInOneSkill) GetNowSkill() *battle.Skill {
	return W.WGetNowSkill()
}
func (W _app_logic_battle_IInOneSkill) GetOneSkillAutoID() uint32 {
	return W.WGetOneSkillAutoID()
}

// _app_logic_battle_IInOneSkillAttackEnd is an interface wrapper for IInOneSkillAttackEnd type
type _app_logic_battle_IInOneSkillAttackEnd struct {
	IValue      interface{}
	WGetIsDodge func() bool
}

func (W _app_logic_battle_IInOneSkillAttackEnd) GetIsDodge() bool {
	return W.WGetIsDodge()
}

// _app_logic_battle_IInOneSkillEffect is an interface wrapper for IInOneSkillEffect type
type _app_logic_battle_IInOneSkillEffect struct {
	IValue          interface{}
	WGetSkillEffect func() *goxml.SkillEffectInfo
	WGetTargets     func() *battle.Uniters
}

func (W _app_logic_battle_IInOneSkillEffect) GetSkillEffect() *goxml.SkillEffectInfo {
	return W.WGetSkillEffect()
}
func (W _app_logic_battle_IInOneSkillEffect) GetTargets() *battle.Uniters {
	return W.WGetTargets()
}

// _app_logic_battle_IOneBattle is an interface wrapper for IOneBattle type
type _app_logic_battle_IOneBattle struct {
	IValue  interface{}
	WGetM   func() *battle.Manager
	WGetRep func() *battle.Report
}

func (W _app_logic_battle_IOneBattle) GetM() *battle.Manager {
	return W.WGetM()
}
func (W _app_logic_battle_IOneBattle) GetRep() *battle.Report {
	return W.WGetRep()
}

// _app_logic_battle_IOneSkill is an interface wrapper for IOneSkill type
type _app_logic_battle_IOneSkill struct {
	IValue              interface{}
	WGetArgs            func() interface{}
	WGetArgsForTarget   func() interface{}
	WGetAttack          func() battle.Uniter
	WGetExecutionParams func(a0 *bt.BaseEffectInfo)
	WGetExecutionType   func() bt.SourceType
	WGetIsInFollow      func() bool
	WGetM               func() *battle.Manager
	WGetNextParam       func() int32
	WGetNowSkill        func() *battle.Skill
	WGetOneSkillAutoID  func() uint32
	WGetRep             func() *battle.Report
	WGetSkillTargets    func(a0 int) *battle.Uniters
	WSetSkillTargets    func(a0 int, a1 *battle.Uniters)
	WUpdateCureResult   func(a0 *battle.CureResult)
	WUpdateHurtResult   func(a0 *battle.HurtResult)
}

func (W _app_logic_battle_IOneSkill) GetArgs() interface{} {
	return W.WGetArgs()
}
func (W _app_logic_battle_IOneSkill) GetArgsForTarget() interface{} {
	return W.WGetArgsForTarget()
}
func (W _app_logic_battle_IOneSkill) GetAttack() battle.Uniter {
	return W.WGetAttack()
}
func (W _app_logic_battle_IOneSkill) GetExecutionParams(a0 *bt.BaseEffectInfo) {
	W.WGetExecutionParams(a0)
}
func (W _app_logic_battle_IOneSkill) GetExecutionType() bt.SourceType {
	return W.WGetExecutionType()
}
func (W _app_logic_battle_IOneSkill) GetIsInFollow() bool {
	return W.WGetIsInFollow()
}
func (W _app_logic_battle_IOneSkill) GetM() *battle.Manager {
	return W.WGetM()
}
func (W _app_logic_battle_IOneSkill) GetNextParam() int32 {
	return W.WGetNextParam()
}
func (W _app_logic_battle_IOneSkill) GetNowSkill() *battle.Skill {
	return W.WGetNowSkill()
}
func (W _app_logic_battle_IOneSkill) GetOneSkillAutoID() uint32 {
	return W.WGetOneSkillAutoID()
}
func (W _app_logic_battle_IOneSkill) GetRep() *battle.Report {
	return W.WGetRep()
}
func (W _app_logic_battle_IOneSkill) GetSkillTargets(a0 int) *battle.Uniters {
	return W.WGetSkillTargets(a0)
}
func (W _app_logic_battle_IOneSkill) SetSkillTargets(a0 int, a1 *battle.Uniters) {
	W.WSetSkillTargets(a0, a1)
}
func (W _app_logic_battle_IOneSkill) UpdateCureResult(a0 *battle.CureResult) {
	W.WUpdateCureResult(a0)
}
func (W _app_logic_battle_IOneSkill) UpdateHurtResult(a0 *battle.HurtResult) {
	W.WUpdateHurtResult(a0)
}

// _app_logic_battle_IOneSkillEffect is an interface wrapper for IOneSkillEffect type
type _app_logic_battle_IOneSkillEffect struct {
	IValue              interface{}
	WGetArgs            func() interface{}
	WGetArgsForTarget   func() interface{}
	WGetAttack          func() battle.Uniter
	WGetExecutionParams func(a0 *bt.BaseEffectInfo)
	WGetExecutionType   func() bt.SourceType
	WGetIsInFollow      func() bool
	WGetM               func() *battle.Manager
	WGetNextParam       func() int32
	WGetNowSkill        func() *battle.Skill
	WGetOneSkillAutoID  func() uint32
	WGetRep             func() *battle.Report
	WGetSkillEffect     func() *goxml.SkillEffectInfo
	WGetSkillTargets    func(a0 int) *battle.Uniters
	WGetTargets         func() *battle.Uniters
	WSetSkillTargets    func(a0 int, a1 *battle.Uniters)
	WUpdateCureResult   func(a0 *battle.CureResult)
	WUpdateHurtResult   func(a0 *battle.HurtResult)
}

func (W _app_logic_battle_IOneSkillEffect) GetArgs() interface{} {
	return W.WGetArgs()
}
func (W _app_logic_battle_IOneSkillEffect) GetArgsForTarget() interface{} {
	return W.WGetArgsForTarget()
}
func (W _app_logic_battle_IOneSkillEffect) GetAttack() battle.Uniter {
	return W.WGetAttack()
}
func (W _app_logic_battle_IOneSkillEffect) GetExecutionParams(a0 *bt.BaseEffectInfo) {
	W.WGetExecutionParams(a0)
}
func (W _app_logic_battle_IOneSkillEffect) GetExecutionType() bt.SourceType {
	return W.WGetExecutionType()
}
func (W _app_logic_battle_IOneSkillEffect) GetIsInFollow() bool {
	return W.WGetIsInFollow()
}
func (W _app_logic_battle_IOneSkillEffect) GetM() *battle.Manager {
	return W.WGetM()
}
func (W _app_logic_battle_IOneSkillEffect) GetNextParam() int32 {
	return W.WGetNextParam()
}
func (W _app_logic_battle_IOneSkillEffect) GetNowSkill() *battle.Skill {
	return W.WGetNowSkill()
}
func (W _app_logic_battle_IOneSkillEffect) GetOneSkillAutoID() uint32 {
	return W.WGetOneSkillAutoID()
}
func (W _app_logic_battle_IOneSkillEffect) GetRep() *battle.Report {
	return W.WGetRep()
}
func (W _app_logic_battle_IOneSkillEffect) GetSkillEffect() *goxml.SkillEffectInfo {
	return W.WGetSkillEffect()
}
func (W _app_logic_battle_IOneSkillEffect) GetSkillTargets(a0 int) *battle.Uniters {
	return W.WGetSkillTargets(a0)
}
func (W _app_logic_battle_IOneSkillEffect) GetTargets() *battle.Uniters {
	return W.WGetTargets()
}
func (W _app_logic_battle_IOneSkillEffect) SetSkillTargets(a0 int, a1 *battle.Uniters) {
	W.WSetSkillTargets(a0, a1)
}
func (W _app_logic_battle_IOneSkillEffect) UpdateCureResult(a0 *battle.CureResult) {
	W.WUpdateCureResult(a0)
}
func (W _app_logic_battle_IOneSkillEffect) UpdateHurtResult(a0 *battle.HurtResult) {
	W.WUpdateHurtResult(a0)
}

// _app_logic_battle_IPoolObj is an interface wrapper for IPoolObj type
type _app_logic_battle_IPoolObj struct {
	IValue interface{}
	WReset func()
}

func (W _app_logic_battle_IPoolObj) Reset() {
	W.WReset()
}

// _app_logic_battle_ISkillManager is an interface wrapper for ISkillManager type
type _app_logic_battle_ISkillManager struct {
	IValue                interface{}
	WAddNewSkill          func(a0 uint32, a1 int, a2 interface{}, a3 bool, a4 bool) *battle.Skill
	WAddPsSkill           func(a0 uint32) *battle.PsSkill
	WGetNormalSkill       func() *battle.Skill
	WGetOtherPsSkills     func() []uint32
	WGetOwnerSkills       func() []uint32
	WGetPassiveSkills     func() []*battle.Skill
	WGetRoundSkill        func(a0 uint32) *battle.Skill
	WGetSkillByPos        func(a0 int) *battle.Skill
	WGetSkillByType       func(a0 uint32) *battle.Skill
	WResetNextActionSkill func(a0 int)
	WSetBindSkill         func(a0 int, a1 uint32, a2 *battle.PsSkill)
}

func (W _app_logic_battle_ISkillManager) AddNewSkill(a0 uint32, a1 int, a2 interface{}, a3 bool, a4 bool) *battle.Skill {
	return W.WAddNewSkill(a0, a1, a2, a3, a4)
}
func (W _app_logic_battle_ISkillManager) AddPsSkill(a0 uint32) *battle.PsSkill {
	return W.WAddPsSkill(a0)
}
func (W _app_logic_battle_ISkillManager) GetNormalSkill() *battle.Skill {
	return W.WGetNormalSkill()
}
func (W _app_logic_battle_ISkillManager) GetOtherPsSkills() []uint32 {
	return W.WGetOtherPsSkills()
}
func (W _app_logic_battle_ISkillManager) GetOwnerSkills() []uint32 {
	return W.WGetOwnerSkills()
}
func (W _app_logic_battle_ISkillManager) GetPassiveSkills() []*battle.Skill {
	return W.WGetPassiveSkills()
}
func (W _app_logic_battle_ISkillManager) GetRoundSkill(a0 uint32) *battle.Skill {
	return W.WGetRoundSkill(a0)
}
func (W _app_logic_battle_ISkillManager) GetSkillByPos(a0 int) *battle.Skill {
	return W.WGetSkillByPos(a0)
}
func (W _app_logic_battle_ISkillManager) GetSkillByType(a0 uint32) *battle.Skill {
	return W.WGetSkillByType(a0)
}
func (W _app_logic_battle_ISkillManager) ResetNextActionSkill(a0 int) {
	W.WResetNextActionSkill(a0)
}
func (W _app_logic_battle_ISkillManager) SetBindSkill(a0 int, a1 uint32, a2 *battle.PsSkill) {
	W.WSetBindSkill(a0, a1, a2)
}

// _app_logic_battle_Uniter is an interface wrapper for Uniter type
type _app_logic_battle_Uniter struct {
	IValue               interface{}
	WActionPrepare       func(a0 *battle.OneAction) bool
	WAddCastBuff         func(a0 *battle.Buff)
	WAddNewSkill         func(a0 uint32, a1 int, a2 interface{}) *battle.Skill
	WAddPsSkill          func(a0 uint32) *battle.PsSkill
	WAfterDecHp          func(a0 int64)
	WAttackType          func() uint32
	WCanBeChoose         func(needAlive int) bool
	WChangeAttr          func(a0 uint32, a1 uint32, a2 int, a3 int64)
	WChangeBuffAttr      func(a0 uint32, a1 *battle.Buff)
	WChangePsAttr        func(a0 int, a1 uint32, a2 int, a3 int64)
	WClearAtSkillAttack  func()
	WClearAttackResult   func()
	WClearRaisePSs       func()
	WCured               func(a0 battle.Uniter, a1 int64, a2 bt.SpecialType, a3 bt.FactorType, a4 battle.IExecution)
	WDead                func(a0 *bt.HurtEffect)
	WDelCastBuff         func(a0 *battle.Buff)
	WDoAddBuff           func(a0 battle.Uniter, a1 uint32, a2 uint32, a3 uint32, a4 battle.IExecution)
	WDoRemoveBuff        func(a0 battle.Uniter, a1 uint32, a2 uint32, a3 uint32, a4 uint32, a5 uint32, a6 battle.IExecution) battle.RemoveBuffResult
	WDoResurrection      func(a0 battle.Uniter, a1 int64, a2 battle.IExecution)
	WEffectBuffAttr      func(a0 battle.Uniter, a1 uint32, a2 uint32, a3 uint32, a4 uint32, a5 battle.IExecution)
	WEliteType           func() uint32
	WFlushDebugInfo      func(a0 bool) *bt.DebugInfo
	WGetAttr             func(a0 int) int64
	WGetAttrM            func() battle.IAttrM
	WGetBuffLayerRecord  func(a0 uint32) uint32
	WGetBuffM            func() *battle.BuffManager
	WGetCallerID         func() uint32
	WGetHp               func() int64
	WGetJob              func() uint32
	WGetManager          func() *battle.Manager
	WGetMaxHp            func() int64
	WGetMonsterType      func() uint32
	WGetPSEffectValue    func(a0 int, a1 int64) int64
	WGetPsAttrM          func() *battle.PsAttrManager
	WGetRace             func() uint32
	WGetRaisePSs         func() []uint64
	WGetSkillM           func() battle.ISkillManager
	WGetStatus           func() int
	WGetSwapCount        func() uint32
	WGetSysID            func() uint32
	WGetUniterType       func() int
	WHasBuff             func(a0 uint32) bool
	WHasBuffWithSubType  func(a0 uint32, a1 uint32) bool
	WHasSta              func(a0 int) bool
	WHurted              func(a0 battle.Uniter, a1 int64, a2 bt.SpecialType, a3 bt.FactorType, a4 battle.IExecution) bool
	WID                  func() uint32
	WIndex               func() uint32
	WIsAtSkillKilled     func() bool
	WIsCall              func() bool
	WIsNoAlive           func() bool
	WIsNoDead            func() bool
	WIsNoDead3           func() bool
	WIsPhysical          func() bool
	WIsPublicHP          func() bool
	WIsStuned            func() (bool, battle.IExecution)
	WLastPos             func() uint32
	WOneActionEnd        func(a0 *battle.OneAction)
	WOneSkillPrepare     func(a0 *battle.OneSkill)
	WPos                 func() uint32
	WRecordBuffLayer     func(a0 uint32, a1 uint32)
	WSetAtSkillAttackEnd func()
	WSetAtSkillAttacking func()
	WSetLastPos          func(a0 uint32)
	WSetPos              func(a0 uint32)
	WSetSta              func(a0 int, a1 bool)
	WSetStatus           func(a0 int)
	WUpdateAttackResult  func(a0 *battle.HurtResult)
}

func (W _app_logic_battle_Uniter) ActionPrepare(a0 *battle.OneAction) bool {
	return W.WActionPrepare(a0)
}
func (W _app_logic_battle_Uniter) AddCastBuff(a0 *battle.Buff) {
	W.WAddCastBuff(a0)
}
func (W _app_logic_battle_Uniter) AddNewSkill(a0 uint32, a1 int, a2 interface{}) *battle.Skill {
	return W.WAddNewSkill(a0, a1, a2)
}
func (W _app_logic_battle_Uniter) AddPsSkill(a0 uint32) *battle.PsSkill {
	return W.WAddPsSkill(a0)
}
func (W _app_logic_battle_Uniter) AfterDecHp(a0 int64) {
	W.WAfterDecHp(a0)
}
func (W _app_logic_battle_Uniter) AttackType() uint32 {
	return W.WAttackType()
}
func (W _app_logic_battle_Uniter) CanBeChoose(needAlive int) bool {
	return W.WCanBeChoose(needAlive)
}
func (W _app_logic_battle_Uniter) ChangeAttr(a0 uint32, a1 uint32, a2 int, a3 int64) {
	W.WChangeAttr(a0, a1, a2, a3)
}
func (W _app_logic_battle_Uniter) ChangeBuffAttr(a0 uint32, a1 *battle.Buff) {
	W.WChangeBuffAttr(a0, a1)
}
func (W _app_logic_battle_Uniter) ChangePsAttr(a0 int, a1 uint32, a2 int, a3 int64) {
	W.WChangePsAttr(a0, a1, a2, a3)
}
func (W _app_logic_battle_Uniter) ClearAtSkillAttack() {
	W.WClearAtSkillAttack()
}
func (W _app_logic_battle_Uniter) ClearAttackResult() {
	W.WClearAttackResult()
}
func (W _app_logic_battle_Uniter) ClearRaisePSs() {
	W.WClearRaisePSs()
}
func (W _app_logic_battle_Uniter) Cured(a0 battle.Uniter, a1 int64, a2 bt.SpecialType, a3 bt.FactorType, a4 battle.IExecution) {
	W.WCured(a0, a1, a2, a3, a4)
}
func (W _app_logic_battle_Uniter) Dead(a0 *bt.HurtEffect) {
	W.WDead(a0)
}
func (W _app_logic_battle_Uniter) DelCastBuff(a0 *battle.Buff) {
	W.WDelCastBuff(a0)
}
func (W _app_logic_battle_Uniter) DoAddBuff(a0 battle.Uniter, a1 uint32, a2 uint32, a3 uint32, a4 battle.IExecution) {
	W.WDoAddBuff(a0, a1, a2, a3, a4)
}
func (W _app_logic_battle_Uniter) DoRemoveBuff(a0 battle.Uniter, a1 uint32, a2 uint32, a3 uint32, a4 uint32, a5 uint32, a6 battle.IExecution) battle.RemoveBuffResult {
	return W.WDoRemoveBuff(a0, a1, a2, a3, a4, a5, a6)
}
func (W _app_logic_battle_Uniter) DoResurrection(a0 battle.Uniter, a1 int64, a2 battle.IExecution) {
	W.WDoResurrection(a0, a1, a2)
}
func (W _app_logic_battle_Uniter) EffectBuffAttr(a0 battle.Uniter, a1 uint32, a2 uint32, a3 uint32, a4 uint32, a5 battle.IExecution) {
	W.WEffectBuffAttr(a0, a1, a2, a3, a4, a5)
}
func (W _app_logic_battle_Uniter) EliteType() uint32 {
	return W.WEliteType()
}
func (W _app_logic_battle_Uniter) FlushDebugInfo(a0 bool) *bt.DebugInfo {
	return W.WFlushDebugInfo(a0)
}
func (W _app_logic_battle_Uniter) GetAttr(a0 int) int64 {
	return W.WGetAttr(a0)
}
func (W _app_logic_battle_Uniter) GetAttrM() battle.IAttrM {
	return W.WGetAttrM()
}
func (W _app_logic_battle_Uniter) GetBuffLayerRecord(a0 uint32) uint32 {
	return W.WGetBuffLayerRecord(a0)
}
func (W _app_logic_battle_Uniter) GetBuffM() *battle.BuffManager {
	return W.WGetBuffM()
}
func (W _app_logic_battle_Uniter) GetCallerID() uint32 {
	return W.WGetCallerID()
}
func (W _app_logic_battle_Uniter) GetHp() int64 {
	return W.WGetHp()
}
func (W _app_logic_battle_Uniter) GetJob() uint32 {
	return W.WGetJob()
}
func (W _app_logic_battle_Uniter) GetManager() *battle.Manager {
	return W.WGetManager()
}
func (W _app_logic_battle_Uniter) GetMaxHp() int64 {
	return W.WGetMaxHp()
}
func (W _app_logic_battle_Uniter) GetMonsterType() uint32 {
	return W.WGetMonsterType()
}
func (W _app_logic_battle_Uniter) GetPSEffectValue(a0 int, a1 int64) int64 {
	return W.WGetPSEffectValue(a0, a1)
}
func (W _app_logic_battle_Uniter) GetPsAttrM() *battle.PsAttrManager {
	return W.WGetPsAttrM()
}
func (W _app_logic_battle_Uniter) GetRace() uint32 {
	return W.WGetRace()
}
func (W _app_logic_battle_Uniter) GetRaisePSs() []uint64 {
	return W.WGetRaisePSs()
}
func (W _app_logic_battle_Uniter) GetSkillM() battle.ISkillManager {
	return W.WGetSkillM()
}
func (W _app_logic_battle_Uniter) GetStatus() int {
	return W.WGetStatus()
}
func (W _app_logic_battle_Uniter) GetSwapCount() uint32 {
	return W.WGetSwapCount()
}
func (W _app_logic_battle_Uniter) GetSysID() uint32 {
	return W.WGetSysID()
}
func (W _app_logic_battle_Uniter) GetUniterType() int {
	return W.WGetUniterType()
}
func (W _app_logic_battle_Uniter) HasBuff(a0 uint32) bool {
	return W.WHasBuff(a0)
}
func (W _app_logic_battle_Uniter) HasBuffWithSubType(a0 uint32, a1 uint32) bool {
	return W.WHasBuffWithSubType(a0, a1)
}
func (W _app_logic_battle_Uniter) HasSta(a0 int) bool {
	return W.WHasSta(a0)
}
func (W _app_logic_battle_Uniter) Hurted(a0 battle.Uniter, a1 int64, a2 bt.SpecialType, a3 bt.FactorType, a4 battle.IExecution) bool {
	return W.WHurted(a0, a1, a2, a3, a4)
}
func (W _app_logic_battle_Uniter) ID() uint32 {
	return W.WID()
}
func (W _app_logic_battle_Uniter) Index() uint32 {
	return W.WIndex()
}
func (W _app_logic_battle_Uniter) IsAtSkillKilled() bool {
	return W.WIsAtSkillKilled()
}
func (W _app_logic_battle_Uniter) IsCall() bool {
	return W.WIsCall()
}
func (W _app_logic_battle_Uniter) IsNoAlive() bool {
	return W.WIsNoAlive()
}
func (W _app_logic_battle_Uniter) IsNoDead() bool {
	return W.WIsNoDead()
}
func (W _app_logic_battle_Uniter) IsNoDead3() bool {
	return W.WIsNoDead3()
}
func (W _app_logic_battle_Uniter) IsPhysical() bool {
	return W.WIsPhysical()
}
func (W _app_logic_battle_Uniter) IsPublicHP() bool {
	return W.WIsPublicHP()
}
func (W _app_logic_battle_Uniter) IsStuned() (bool, battle.IExecution) {
	return W.WIsStuned()
}
func (W _app_logic_battle_Uniter) LastPos() uint32 {
	return W.WLastPos()
}
func (W _app_logic_battle_Uniter) OneActionEnd(a0 *battle.OneAction) {
	W.WOneActionEnd(a0)
}
func (W _app_logic_battle_Uniter) OneSkillPrepare(a0 *battle.OneSkill) {
	W.WOneSkillPrepare(a0)
}
func (W _app_logic_battle_Uniter) Pos() uint32 {
	return W.WPos()
}
func (W _app_logic_battle_Uniter) RecordBuffLayer(a0 uint32, a1 uint32) {
	W.WRecordBuffLayer(a0, a1)
}
func (W _app_logic_battle_Uniter) SetAtSkillAttackEnd() {
	W.WSetAtSkillAttackEnd()
}
func (W _app_logic_battle_Uniter) SetAtSkillAttacking() {
	W.WSetAtSkillAttacking()
}
func (W _app_logic_battle_Uniter) SetLastPos(a0 uint32) {
	W.WSetLastPos(a0)
}
func (W _app_logic_battle_Uniter) SetPos(a0 uint32) {
	W.WSetPos(a0)
}
func (W _app_logic_battle_Uniter) SetSta(a0 int, a1 bool) {
	W.WSetSta(a0, a1)
}
func (W _app_logic_battle_Uniter) SetStatus(a0 int) {
	W.WSetStatus(a0)
}
func (W _app_logic_battle_Uniter) UpdateAttackResult(a0 *battle.HurtResult) {
	W.WUpdateAttackResult(a0)
}
