// Code generated by 'yaegi extract app/protos/out/ret'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/protos/out/ret"
	"reflect"
)

func init() {
	Symbols["app/protos/out/ret/ret"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"RET_ACCOUNT_SIZE_OUT_OF_LIMIT":                           reflect.ValueOf(ret.RET_ACCOUNT_SIZE_OUT_OF_LIMIT),
		"RET_ACHIEVE_PROGRESS_NOT_FINISH":                         reflect.ValueOf(ret.RET_ACHIEVE_PROGRESS_NOT_FINISH),
		"RET_ACTIVITY_PUZZLE_CELL_XY_ERROR":                       reflect.ValueOf(ret.RET_ACTIVITY_PUZZLE_CELL_XY_ERROR),
		"RET_ACTIVITY_PUZZLE_REPEATED_CELL":                       reflect.ValueOf(ret.RET_ACTIVITY_PUZZLE_REPEATED_CELL),
		"RET_ACTIVITY_PYRAMID_CHOOSE_AWARD_DRAW_MAX":              reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_CHOOSE_AWARD_DRAW_MAX),
		"RET_ACTIVITY_PYRAMID_CHOOSE_REQUIREMENT_NOT_ACHIEVE":     reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_CHOOSE_REQUIREMENT_NOT_ACHIEVE),
		"RET_ACTIVITY_PYRAMID_DRAW_AWARDS_ERROR":                  reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_DRAW_AWARDS_ERROR),
		"RET_ACTIVITY_PYRAMID_DRAW_FAILED":                        reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_DRAW_FAILED),
		"RET_ACTIVITY_PYRAMID_DRAW_OVER_MAX_ROUND":                reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_DRAW_OVER_MAX_ROUND),
		"RET_ACTIVITY_PYRAMID_DRAW_POOL_EMPTY":                    reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_DRAW_POOL_EMPTY),
		"RET_ACTIVITY_PYRAMID_DRAW_POOL_ERROR":                    reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_DRAW_POOL_ERROR),
		"RET_ACTIVITY_PYRAMID_LATTICE_CHANGE_CHOOSE_AWARD_FAILED": reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_LATTICE_CHANGE_CHOOSE_AWARD_FAILED),
		"RET_ACTIVITY_PYRAMID_NOT_CHOOSE_AWARD_BEFORE_DRAW":       reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_NOT_CHOOSE_AWARD_BEFORE_DRAW),
		"RET_ACTIVITY_PYRAMID_NOT_OPEN":                           reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_NOT_OPEN),
		"RET_ACTIVITY_PYRAMID_TASK_NOT_FINISH":                    reflect.ValueOf(ret.RET_ACTIVITY_PYRAMID_TASK_NOT_FINISH),
		"RET_ACTIVITY_RECHARGE_BUY_LIMIT":                         reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_BUY_LIMIT),
		"RET_ACTIVITY_RECHARGE_CHECK_PRIVILEGED_FAILED":           reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_CHECK_PRIVILEGED_FAILED),
		"RET_ACTIVITY_RECHARGE_CONDITION_ERROR":                   reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_CONDITION_ERROR),
		"RET_ACTIVITY_RECHARGE_GIFT_NOT_OPEN":                     reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_GIFT_NOT_OPEN),
		"RET_ACTIVITY_RECHARGE_PRIVILEGED_NOT_EXPIRED":            reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_PRIVILEGED_NOT_EXPIRED),
		"RET_ACTIVITY_RECHARGE_TIME_OUT":                          reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_TIME_OUT),
		"RET_ACTIVITY_RECHARGE_TODAY_IS_RECHARGE":                 reflect.ValueOf(ret.RET_ACTIVITY_RECHARGE_TODAY_IS_RECHARGE),
		"RET_ACTIVITY_STORY_BUY_CONDITION_CHECK_FAILED":           reflect.ValueOf(ret.RET_ACTIVITY_STORY_BUY_CONDITION_CHECK_FAILED),
		"RET_ACTIVITY_STORY_FIGHT_CHECK_FAILED":                   reflect.ValueOf(ret.RET_ACTIVITY_STORY_FIGHT_CHECK_FAILED),
		"RET_ACTIVITY_STORY_LOGIN_AWARD_CONDITION_FAILED":         reflect.ValueOf(ret.RET_ACTIVITY_STORY_LOGIN_AWARD_CONDITION_FAILED),
		"RET_ACTIVITY_STORY_NEED_GET_DATA_BEFORE":                 reflect.ValueOf(ret.RET_ACTIVITY_STORY_NEED_GET_DATA_BEFORE),
		"RET_ACTIVITY_STORY_NOT_HAS_OPEN":                         reflect.ValueOf(ret.RET_ACTIVITY_STORY_NOT_HAS_OPEN),
		"RET_ACTIVITY_STORY_NOT_OPEN":                             reflect.ValueOf(ret.RET_ACTIVITY_STORY_NOT_OPEN),
		"RET_ACTIVITY_SUB_SHOT_OPEN_DAY_CHECK_FAILED":             reflect.ValueOf(ret.RET_ACTIVITY_SUB_SHOT_OPEN_DAY_CHECK_FAILED),
		"RET_ACTIVITY_SUB_SHOT_STAGE_CHECK_FAILED":                reflect.ValueOf(ret.RET_ACTIVITY_SUB_SHOT_STAGE_CHECK_FAILED),
		"RET_ACTIVITY_SUM_ID_CHECK_ERROR":                         reflect.ValueOf(ret.RET_ACTIVITY_SUM_ID_CHECK_ERROR),
		"RET_ACTIVITY_SUM_NOT_INIT":                               reflect.ValueOf(ret.RET_ACTIVITY_SUM_NOT_INIT),
		"RET_ACTIVITY_SUM_NOT_OPEN":                               reflect.ValueOf(ret.RET_ACTIVITY_SUM_NOT_OPEN),
		"RET_ACTIVITY_SUM_TICKET_BUY_OPEN_ERROR":                  reflect.ValueOf(ret.RET_ACTIVITY_SUM_TICKET_BUY_OPEN_ERROR),
		"RET_ACTIVITY_TASK_MODULE_ERROR":                          reflect.ValueOf(ret.RET_ACTIVITY_TASK_MODULE_ERROR),
		"RET_ACTIVITY_TASK_PROGRESS_ERROR":                        reflect.ValueOf(ret.RET_ACTIVITY_TASK_PROGRESS_ERROR),
		"RET_ACTIVITY_TURN_TABLE_BUFF_NOT_ENOUGH":                 reflect.ValueOf(ret.RET_ACTIVITY_TURN_TABLE_BUFF_NOT_ENOUGH),
		"RET_ACTIVITY_TURN_TABLE_LOGIN_AWARD_CONDITION_FAILED":    reflect.ValueOf(ret.RET_ACTIVITY_TURN_TABLE_LOGIN_AWARD_CONDITION_FAILED),
		"RET_ACTIVITY_TURN_TABLE_NOT_OPEN":                        reflect.ValueOf(ret.RET_ACTIVITY_TURN_TABLE_NOT_OPEN),
		"RET_ADD_ORDER_REPEAT":                                    reflect.ValueOf(ret.RET_ADD_ORDER_REPEAT),
		"RET_ARENA_ADD_FAILED":                                    reflect.ValueOf(ret.RET_ARENA_ADD_FAILED),
		"RET_ARENA_ATTACK_FORMATION_NOT_EXIST":                    reflect.ValueOf(ret.RET_ARENA_ATTACK_FORMATION_NOT_EXIST),
		"RET_ARENA_BATTLE_ERROR":                                  reflect.ValueOf(ret.RET_ARENA_BATTLE_ERROR),
		"RET_ARENA_MATCH_CONFIG_ERROR":                            reflect.ValueOf(ret.RET_ARENA_MATCH_CONFIG_ERROR),
		"RET_ARENA_MUST_REFRESH":                                  reflect.ValueOf(ret.RET_ARENA_MUST_REFRESH),
		"RET_ARENA_NOT_IN_RANK_LIST":                              reflect.ValueOf(ret.RET_ARENA_NOT_IN_RANK_LIST),
		"RET_ARENA_NOT_OPEN":                                      reflect.ValueOf(ret.RET_ARENA_NOT_OPEN),
		"RET_ARENA_NO_USER_DATA":                                  reflect.ValueOf(ret.RET_ARENA_NO_USER_DATA),
		"RET_ARENA_OPPONENT_NOT_EXIST":                            reflect.ValueOf(ret.RET_ARENA_OPPONENT_NOT_EXIST),
		"RET_ARENA_OPPONENT_NUM_ERROR":                            reflect.ValueOf(ret.RET_ARENA_OPPONENT_NUM_ERROR),
		"RET_ARENA_OPPONENT_SCORE_CHANGE":                         reflect.ValueOf(ret.RET_ARENA_OPPONENT_SCORE_CHANGE),
		"RET_ARENA_OP_BEING_ATTACKED":                             reflect.ValueOf(ret.RET_ARENA_OP_BEING_ATTACKED),
		"RET_ARENA_REFRESH_CD_ERROR":                              reflect.ValueOf(ret.RET_ARENA_REFRESH_CD_ERROR),
		"RET_ARENA_REFRESH_TYPE_ERROR":                            reflect.ValueOf(ret.RET_ARENA_REFRESH_TYPE_ERROR),
		"RET_ARENA_REPEAT_LIKE_ERROR":                             reflect.ValueOf(ret.RET_ARENA_REPEAT_LIKE_ERROR),
		"RET_ARENA_SELF_BEING_ATTACKED":                           reflect.ValueOf(ret.RET_ARENA_SELF_BEING_ATTACKED),
		"RET_ARENA_TASK_NO_AWARD_CAN_RECV":                        reflect.ValueOf(ret.RET_ARENA_TASK_NO_AWARD_CAN_RECV),
		"RET_ARTIFACT_DEBUT_CANT_RECV_ACT_AWARD":                  reflect.ValueOf(ret.RET_ARTIFACT_DEBUT_CANT_RECV_ACT_AWARD),
		"RET_ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME":                   reflect.ValueOf(ret.RET_ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME),
		"RET_ARTIFACT_DEBUT_NOT_IN_AWARD_TIME":                    reflect.ValueOf(ret.RET_ARTIFACT_DEBUT_NOT_IN_AWARD_TIME),
		"RET_ARTIFACT_DEBUT_NO_WISH_ARTIFACT":                     reflect.ValueOf(ret.RET_ARTIFACT_DEBUT_NO_WISH_ARTIFACT),
		"RET_ARTIFACT_NOT_EXIST":                                  reflect.ValueOf(ret.RET_ARTIFACT_NOT_EXIST),
		"RET_ARTIFACT_STRENGTH_STAR_LIMIT":                        reflect.ValueOf(ret.RET_ARTIFACT_STRENGTH_STAR_LIMIT),
		"RET_ASSISTANCE_ACTIVITY_CALC_OPEN_TIME_FAILED":           reflect.ValueOf(ret.RET_ASSISTANCE_ACTIVITY_CALC_OPEN_TIME_FAILED),
		"RET_ASSISTANCE_ACTIVITY_NOT_OPEN":                        reflect.ValueOf(ret.RET_ASSISTANCE_ACTIVITY_NOT_OPEN),
		"RET_ASSISTANT_MONTHLY_CARD_EXPIRED":                      reflect.ValueOf(ret.RET_ASSISTANT_MONTHLY_CARD_EXPIRED),
		"RET_ASSISTANT_REPEATED_GOODS_CHOICE":                     reflect.ValueOf(ret.RET_ASSISTANT_REPEATED_GOODS_CHOICE),
		"RET_AVATAR_EXPIRED":                                      reflect.ValueOf(ret.RET_AVATAR_EXPIRED),
		"RET_AVATAR_NO_ACTIVE":                                    reflect.ValueOf(ret.RET_AVATAR_NO_ACTIVE),
		"RET_BAN_ACCOUNT":                                         reflect.ValueOf(ret.RET_BAN_ACCOUNT),
		"RET_BAN_ACCOUNT_TEMPORARY":                               reflect.ValueOf(ret.RET_BAN_ACCOUNT_TEMPORARY),
		"RET_BAN_CHAT":                                            reflect.ValueOf(ret.RET_BAN_CHAT),
		"RET_BAN_USER_BEFORE_OP":                                  reflect.ValueOf(ret.RET_BAN_USER_BEFORE_OP),
		"RET_BATTLE_ERROR":                                        reflect.ValueOf(ret.RET_BATTLE_ERROR),
		"RET_BATTLE_REPORT_NOT_EXIST":                             reflect.ValueOf(ret.RET_BATTLE_REPORT_NOT_EXIST),
		"RET_BATTLE_TEAM_NO_HP":                                   reflect.ValueOf(ret.RET_BATTLE_TEAM_NO_HP),
		"RET_BLACK_FRIEND_LIST_FULL":                              reflect.ValueOf(ret.RET_BLACK_FRIEND_LIST_FULL),
		"RET_BOSS_RUSH_CUR_BOSS_CANNOT_ATTACK":                    reflect.ValueOf(ret.RET_BOSS_RUSH_CUR_BOSS_CANNOT_ATTACK),
		"RET_BOSS_RUSH_CUR_BOSS_LOCKED":                           reflect.ValueOf(ret.RET_BOSS_RUSH_CUR_BOSS_LOCKED),
		"RET_BOX_POINT_NOT_ENOUGH":                                reflect.ValueOf(ret.RET_BOX_POINT_NOT_ENOUGH),
		"RET_BUY_COUNT_LIMIT":                                     reflect.ValueOf(ret.RET_BUY_COUNT_LIMIT),
		"RET_CARNIVAL_TASK_PROGRESS_NOT_FINISH":                   reflect.ValueOf(ret.RET_CARNIVAL_TASK_PROGRESS_NOT_FINISH),
		"RET_CHAT_FRIEND_IN_BLACKLIST":                            reflect.ValueOf(ret.RET_CHAT_FRIEND_IN_BLACKLIST),
		"RET_CHAT_GUILD_MSG_NOT_EXIST":                            reflect.ValueOf(ret.RET_CHAT_GUILD_MSG_NOT_EXIST),
		"RET_CHAT_LIKE_BE_LIMIT":                                  reflect.ValueOf(ret.RET_CHAT_LIKE_BE_LIMIT),
		"RET_CHAT_LIKE_MSG_ID_NOT_EXIST":                          reflect.ValueOf(ret.RET_CHAT_LIKE_MSG_ID_NOT_EXIST),
		"RET_CHAT_LIKE_NOT_CAN":                                   reflect.ValueOf(ret.RET_CHAT_LIKE_NOT_CAN),
		"RET_CHAT_MSG_ID_INVALID":                                 reflect.ValueOf(ret.RET_CHAT_MSG_ID_INVALID),
		"RET_CHAT_MSG_TYPE_INVALID":                               reflect.ValueOf(ret.RET_CHAT_MSG_TYPE_INVALID),
		"RET_CHECK_GOODS_FAILED":                                  reflect.ValueOf(ret.RET_CHECK_GOODS_FAILED),
		"RET_CLIENT_REQUEST_ERROR":                                reflect.ValueOf(ret.RET_CLIENT_REQUEST_ERROR),
		"RET_CLIENT_VERSION_TOO_LOWER":                            reflect.ValueOf(ret.RET_CLIENT_VERSION_TOO_LOWER),
		"RET_COGNITION_LOGS_SERVER_ERROR":                         reflect.ValueOf(ret.RET_COGNITION_LOGS_SERVER_ERROR),
		"RET_COMMON_LEVEL_NOT_ENOUGH":                             reflect.ValueOf(ret.RET_COMMON_LEVEL_NOT_ENOUGH),
		"RET_COMMON_STAR_NOT_ENOUGH":                              reflect.ValueOf(ret.RET_COMMON_STAR_NOT_ENOUGH),
		"RET_COMMON_USER_OFFLINE":                                 reflect.ValueOf(ret.RET_COMMON_USER_OFFLINE),
		"RET_CONTENT_INVALID":                                     reflect.ValueOf(ret.RET_CONTENT_INVALID),
		"RET_CONTENT_LENGTH_LIMIT":                                reflect.ValueOf(ret.RET_CONTENT_LENGTH_LIMIT),
		"RET_COUNT_NOT_ENOUGH":                                    reflect.ValueOf(ret.RET_COUNT_NOT_ENOUGH),
		"RET_COUNT_PRICE_ERROR":                                   reflect.ValueOf(ret.RET_COUNT_PRICE_ERROR),
		"RET_CREATE_USER_FALIED":                                  reflect.ValueOf(ret.RET_CREATE_USER_FALIED),
		"RET_CROSS_ARENA_DEFENDER_RANK_CHANGED":                   reflect.ValueOf(ret.RET_CROSS_ARENA_DEFENDER_RANK_CHANGED),
		"RET_CROSS_ARENA_FIGHTERS_NOT_IN_SAME_ROOM":               reflect.ValueOf(ret.RET_CROSS_ARENA_FIGHTERS_NOT_IN_SAME_ROOM),
		"RET_CROSS_ARENA_FIGHTER_LOCKED":                          reflect.ValueOf(ret.RET_CROSS_ARENA_FIGHTER_LOCKED),
		"RET_CROSS_ARENA_FIGHTER_NOT_EXIST":                       reflect.ValueOf(ret.RET_CROSS_ARENA_FIGHTER_NOT_EXIST),
		"RET_CROSS_ARENA_RESETTING":                               reflect.ValueOf(ret.RET_CROSS_ARENA_RESETTING),
		"RET_CROSS_LAST_RANK_NOT_LOAD":                            reflect.ValueOf(ret.RET_CROSS_LAST_RANK_NOT_LOAD),
		"RET_CROSS_MAINTAIN":                                      reflect.ValueOf(ret.RET_CROSS_MAINTAIN),
		"RET_CROSS_NO_FOUND_ACTIVITY":                             reflect.ValueOf(ret.RET_CROSS_NO_FOUND_ACTIVITY),
		"RET_CROSS_PARTITION_RESET_NOT_FINISH":                    reflect.ValueOf(ret.RET_CROSS_PARTITION_RESET_NOT_FINISH),
		"RET_CROSS_PEAK_INIT_DATA_NOT_ENOUGH":                     reflect.ValueOf(ret.RET_CROSS_PEAK_INIT_DATA_NOT_ENOUGH),
		"RET_CROSS_PEAK_INIT_DATA_PREPARING":                      reflect.ValueOf(ret.RET_CROSS_PEAK_INIT_DATA_PREPARING),
		"RET_CROSS_REQ_TIMEOUT":                                   reflect.ValueOf(ret.RET_CROSS_REQ_TIMEOUT),
		"RET_CRYSTAL_ACHIEVEMENT_ACTIVED":                         reflect.ValueOf(ret.RET_CRYSTAL_ACHIEVEMENT_ACTIVED),
		"RET_CRYSTAL_ACHIEVEMENT_NOT_FINISH":                      reflect.ValueOf(ret.RET_CRYSTAL_ACHIEVEMENT_NOT_FINISH),
		"RET_CRYSTAL_BLESS_EXP_NOT_ENOUGH":                        reflect.ValueOf(ret.RET_CRYSTAL_BLESS_EXP_NOT_ENOUGH),
		"RET_CRYSTAL_BLESS_MAX_LEVEL_LIMIT":                       reflect.ValueOf(ret.RET_CRYSTAL_BLESS_MAX_LEVEL_LIMIT),
		"RET_CRYSTAL_CONTRACT_HERO_CANNOT_CHANGE":                 reflect.ValueOf(ret.RET_CRYSTAL_CONTRACT_HERO_CANNOT_CHANGE),
		"RET_CRYSTAL_CONTRACT_HERO_CANNOT_DELETE":                 reflect.ValueOf(ret.RET_CRYSTAL_CONTRACT_HERO_CANNOT_DELETE),
		"RET_CRYSTAL_CONTRACT_NOT_SUCCESS":                        reflect.ValueOf(ret.RET_CRYSTAL_CONTRACT_NOT_SUCCESS),
		"RET_CRYSTAL_EXIST_IN_CONTRACT_HEROES":                    reflect.ValueOf(ret.RET_CRYSTAL_EXIST_IN_CONTRACT_HEROES),
		"RET_CRYSTAL_EXIST_IN_RESONANCE_HEROES":                   reflect.ValueOf(ret.RET_CRYSTAL_EXIST_IN_RESONANCE_HEROES),
		"RET_CRYSTAL_IS_FREEDOM_HERO":                             reflect.ValueOf(ret.RET_CRYSTAL_IS_FREEDOM_HERO),
		"RET_CRYSTAL_NOT_FREEDOM_HERO":                            reflect.ValueOf(ret.RET_CRYSTAL_NOT_FREEDOM_HERO),
		"RET_CRYSTAL_NO_MORE_SLOT_CAN_UNLOCK":                     reflect.ValueOf(ret.RET_CRYSTAL_NO_MORE_SLOT_CAN_UNLOCK),
		"RET_CRYSTAL_RESONANCE_RARE_LIMIT":                        reflect.ValueOf(ret.RET_CRYSTAL_RESONANCE_RARE_LIMIT),
		"RET_CRYSTAL_SLOT_CD_OVER":                                reflect.ValueOf(ret.RET_CRYSTAL_SLOT_CD_OVER),
		"RET_CRYSTAL_SLOT_EMPTY":                                  reflect.ValueOf(ret.RET_CRYSTAL_SLOT_EMPTY),
		"RET_CRYSTAL_SLOT_ID_ILLEGAL":                             reflect.ValueOf(ret.RET_CRYSTAL_SLOT_ID_ILLEGAL),
		"RET_CRYSTAL_SLOT_NOT_EMPTY":                              reflect.ValueOf(ret.RET_CRYSTAL_SLOT_NOT_EMPTY),
		"RET_CRYSTAL_SLOT_NOT_EXIST":                              reflect.ValueOf(ret.RET_CRYSTAL_SLOT_NOT_EXIST),
		"RET_CRYSTAL_SLOT_UNDER_CD":                               reflect.ValueOf(ret.RET_CRYSTAL_SLOT_UNDER_CD),
		"RET_DAILY_ATTENDANCE_AWARD_ALREADY_RECEIVED":             reflect.ValueOf(ret.RET_DAILY_ATTENDANCE_AWARD_ALREADY_RECEIVED),
		"RET_DAILY_ATTENDANCE_AWARD_OP_INVALID":                   reflect.ValueOf(ret.RET_DAILY_ATTENDANCE_AWARD_OP_INVALID),
		"RET_DAILY_ATTENDANCE_DATA_NOT_EXIST":                     reflect.ValueOf(ret.RET_DAILY_ATTENDANCE_DATA_NOT_EXIST),
		"RET_DAILY_ATTENDANCE_MAIL_REWARD_FAILED":                 reflect.ValueOf(ret.RET_DAILY_ATTENDANCE_MAIL_REWARD_FAILED),
		"RET_DAILY_ATTENDANCE_NOT_RECHARGE":                       reflect.ValueOf(ret.RET_DAILY_ATTENDANCE_NOT_RECHARGE),
		"RET_DAILY_SPECIAL_AWARD_ALREADY_RECEIVE":                 reflect.ValueOf(ret.RET_DAILY_SPECIAL_AWARD_ALREADY_RECEIVE),
		"RET_DAILY_TASK_PROGRESS_NOT_FINISH":                      reflect.ValueOf(ret.RET_DAILY_TASK_PROGRESS_NOT_FINISH),
		"RET_DAILY_WISH_ACTIVITY_INFO_TIME_ERROR":                 reflect.ValueOf(ret.RET_DAILY_WISH_ACTIVITY_INFO_TIME_ERROR),
		"RET_DAILY_WISH_ACTIVITY_NOT_OPEN":                        reflect.ValueOf(ret.RET_DAILY_WISH_ACTIVITY_NOT_OPEN),
		"RET_DAILY_WISH_ACTIVITY_RAND_AWARD_ID":                   reflect.ValueOf(ret.RET_DAILY_WISH_ACTIVITY_RAND_AWARD_ID),
		"RET_DAILY_WISH_ACTIVITY_SAVE_XML_ERROR":                  reflect.ValueOf(ret.RET_DAILY_WISH_ACTIVITY_SAVE_XML_ERROR),
		"RET_DAILY_WISH_ACTIVITY_TIME_EXPIRE":                     reflect.ValueOf(ret.RET_DAILY_WISH_ACTIVITY_TIME_EXPIRE),
		"RET_DAILY_WISH_ACTIVITY_TIME_REPEATED":                   reflect.ValueOf(ret.RET_DAILY_WISH_ACTIVITY_TIME_REPEATED),
		"RET_DAILY_WISH_AWARD_ACT_ID_ERROR":                       reflect.ValueOf(ret.RET_DAILY_WISH_AWARD_ACT_ID_ERROR),
		"RET_DAILY_WISH_AWARD_ID_NOT_EXIST":                       reflect.ValueOf(ret.RET_DAILY_WISH_AWARD_ID_NOT_EXIST),
		"RET_DAILY_WISH_AWARD_LEN_ERROR":                          reflect.ValueOf(ret.RET_DAILY_WISH_AWARD_LEN_ERROR),
		"RET_DAILY_WISH_AWARD_SAVE_XML_ERROR":                     reflect.ValueOf(ret.RET_DAILY_WISH_AWARD_SAVE_XML_ERROR),
		"RET_DAILY_WISH_SUMMON_COUNT_NOT_ENOUGH":                  reflect.ValueOf(ret.RET_DAILY_WISH_SUMMON_COUNT_NOT_ENOUGH),
		"RET_DEBUT_ACTIVITY_RELEASE_NUM_OVER_ONE":                 reflect.ValueOf(ret.RET_DEBUT_ACTIVITY_RELEASE_NUM_OVER_ONE),
		"RET_DEBUT_SUMMON_AWARD_NUM_NOT_MATCH":                    reflect.ValueOf(ret.RET_DEBUT_SUMMON_AWARD_NUM_NOT_MATCH),
		"RET_DEVICE_ID_SIZE_OUT_OF_LIMIT":                         reflect.ValueOf(ret.RET_DEVICE_ID_SIZE_OUT_OF_LIMIT),
		"RET_DISORDER_LAND_BATTLE_RES_NOT_ENOUGH":                 reflect.ValueOf(ret.RET_DISORDER_LAND_BATTLE_RES_NOT_ENOUGH),
		"RET_DISORDER_LAND_GET_RUNE_FAILED":                       reflect.ValueOf(ret.RET_DISORDER_LAND_GET_RUNE_FAILED),
		"RET_DISORDER_LAND_NEIGHBOR_NODE_NOT_COMPLETE":            reflect.ValueOf(ret.RET_DISORDER_LAND_NEIGHBOR_NODE_NOT_COMPLETE),
		"RET_DISORDER_LAND_NODE_ALREADY_COMPLETE":                 reflect.ValueOf(ret.RET_DISORDER_LAND_NODE_ALREADY_COMPLETE),
		"RET_DISORDER_LAND_NODE_NOT_COMPLETE":                     reflect.ValueOf(ret.RET_DISORDER_LAND_NODE_NOT_COMPLETE),
		"RET_DISORDER_LAND_NOT_OPEN":                              reflect.ValueOf(ret.RET_DISORDER_LAND_NOT_OPEN),
		"RET_DISORDER_LAND_NOT_SELECT_DROP_GROUP":                 reflect.ValueOf(ret.RET_DISORDER_LAND_NOT_SELECT_DROP_GROUP),
		"RET_DISORDER_LAND_PRE_DIFFICULTY_NOT_PASS":               reflect.ValueOf(ret.RET_DISORDER_LAND_PRE_DIFFICULTY_NOT_PASS),
		"RET_DISORDER_LAND_PRE_MAP_NOT_EXIST":                     reflect.ValueOf(ret.RET_DISORDER_LAND_PRE_MAP_NOT_EXIST),
		"RET_DISORDER_LAND_RAND_AWARD_FAILED":                     reflect.ValueOf(ret.RET_DISORDER_LAND_RAND_AWARD_FAILED),
		"RET_DISORDER_LAND_RAND_DROP_GROUP_FAILED":                reflect.ValueOf(ret.RET_DISORDER_LAND_RAND_DROP_GROUP_FAILED),
		"RET_DISORDER_LAND_SEASON_DAY_NOT_ENOUGH":                 reflect.ValueOf(ret.RET_DISORDER_LAND_SEASON_DAY_NOT_ENOUGH),
		"RET_DISORDER_LAND_SEASON_LEVEL_NOT_ENOUGH":               reflect.ValueOf(ret.RET_DISORDER_LAND_SEASON_LEVEL_NOT_ENOUGH),
		"RET_DISORDER_LAND_SEASON_LINK_LEVEL_NOT_ENOUGH":          reflect.ValueOf(ret.RET_DISORDER_LAND_SEASON_LINK_LEVEL_NOT_ENOUGH),
		"RET_DISORDER_LAND_SEASON_LINK_NUM_NOT_ENOUGH":            reflect.ValueOf(ret.RET_DISORDER_LAND_SEASON_LINK_NUM_NOT_ENOUGH),
		"RET_DISPATCH_HERO_LINK_NOT_MATCH":                        reflect.ValueOf(ret.RET_DISPATCH_HERO_LINK_NOT_MATCH),
		"RET_DISPATCH_HERO_NOT_MATCH":                             reflect.ValueOf(ret.RET_DISPATCH_HERO_NOT_MATCH),
		"RET_DISPATCH_HERO_RACE_NOT_MATCH":                        reflect.ValueOf(ret.RET_DISPATCH_HERO_RACE_NOT_MATCH),
		"RET_DISPATCH_HERO_STAR_NOT_MATCH":                        reflect.ValueOf(ret.RET_DISPATCH_HERO_STAR_NOT_MATCH),
		"RET_DISPATCH_TASK_NOT_EXIST":                             reflect.ValueOf(ret.RET_DISPATCH_TASK_NOT_EXIST),
		"RET_DISPATCH_TASK_TIME_NOT_UP":                           reflect.ValueOf(ret.RET_DISPATCH_TASK_TIME_NOT_UP),
		"RET_DISPATCH_TOO_MANY_COMPLETED_TASKS":                   reflect.ValueOf(ret.RET_DISPATCH_TOO_MANY_COMPLETED_TASKS),
		"RET_DISPATCH_XML_INFO_NOT_EXIST":                         reflect.ValueOf(ret.RET_DISPATCH_XML_INFO_NOT_EXIST),
		"RET_DIVINE_DEMON_ACTIVITY_ALREADY_EXPIRE":                reflect.ValueOf(ret.RET_DIVINE_DEMON_ACTIVITY_ALREADY_EXPIRE),
		"RET_DIVINE_DEMON_ACTIVITY_ALREADY_OFFLINE":               reflect.ValueOf(ret.RET_DIVINE_DEMON_ACTIVITY_ALREADY_OFFLINE),
		"RET_DIVINE_DEMON_ACTIVITY_ALREADY_RELEASE":               reflect.ValueOf(ret.RET_DIVINE_DEMON_ACTIVITY_ALREADY_RELEASE),
		"RET_DIVINE_DEMON_ACTIVITY_ID_INVALID":                    reflect.ValueOf(ret.RET_DIVINE_DEMON_ACTIVITY_ID_INVALID),
		"RET_DIVINE_DEMON_ACTIVITY_ID_NOT_EXIST":                  reflect.ValueOf(ret.RET_DIVINE_DEMON_ACTIVITY_ID_NOT_EXIST),
		"RET_DIVINE_DEMON_ACTIVITY_NOT_OPEN":                      reflect.ValueOf(ret.RET_DIVINE_DEMON_ACTIVITY_NOT_OPEN),
		"RET_DIVINE_DEMON_BE_SERVER_OPEN_DAY_LIMIT":               reflect.ValueOf(ret.RET_DIVINE_DEMON_BE_SERVER_OPEN_DAY_LIMIT),
		"RET_DIVINE_DEMON_DIAMOND_SUMMON_COUNT_NOT_ENOUGH":        reflect.ValueOf(ret.RET_DIVINE_DEMON_DIAMOND_SUMMON_COUNT_NOT_ENOUGH),
		"RET_DIVINE_DEMON_OP_STATUS_INVALID":                      reflect.ValueOf(ret.RET_DIVINE_DEMON_OP_STATUS_INVALID),
		"RET_DIVINE_DEMON_RAND_CLASS_FAILED":                      reflect.ValueOf(ret.RET_DIVINE_DEMON_RAND_CLASS_FAILED),
		"RET_DIVINE_DEMON_RAND_GROUP_FAILED":                      reflect.ValueOf(ret.RET_DIVINE_DEMON_RAND_GROUP_FAILED),
		"RET_DIVINE_DEMON_SERVER_DAY_LIMIT_INVALID":               reflect.ValueOf(ret.RET_DIVINE_DEMON_SERVER_DAY_LIMIT_INVALID),
		"RET_DIVINE_DEMON_SHOP_ID_INVALID":                        reflect.ValueOf(ret.RET_DIVINE_DEMON_SHOP_ID_INVALID),
		"RET_DIVINE_DEMON_SUMMON_DROP_AWARD_NOT_EXIST":            reflect.ValueOf(ret.RET_DIVINE_DEMON_SUMMON_DROP_AWARD_NOT_EXIST),
		"RET_DIVINE_DEMON_SUMMON_GRADE_INVALID":                   reflect.ValueOf(ret.RET_DIVINE_DEMON_SUMMON_GRADE_INVALID),
		"RET_DIVINE_DEMON_SUMMON_RAND_SC_FAILED":                  reflect.ValueOf(ret.RET_DIVINE_DEMON_SUMMON_RAND_SC_FAILED),
		"RET_DIVINE_DEMON_TASK_AWARD_BE_RECEIVE":                  reflect.ValueOf(ret.RET_DIVINE_DEMON_TASK_AWARD_BE_RECEIVE),
		"RET_DIVINE_DEMON_TASK_ID_NOT_EXIST":                      reflect.ValueOf(ret.RET_DIVINE_DEMON_TASK_ID_NOT_EXIST),
		"RET_DIVINE_DEMON_TASK_ID_REPEAT":                         reflect.ValueOf(ret.RET_DIVINE_DEMON_TASK_ID_REPEAT),
		"RET_DIVINE_DEMON_TASK_INSTANCE_NOT_EXIST":                reflect.ValueOf(ret.RET_DIVINE_DEMON_TASK_INSTANCE_NOT_EXIST),
		"RET_DIVINE_DEMON_TASK_NOT_FINISH":                        reflect.ValueOf(ret.RET_DIVINE_DEMON_TASK_NOT_FINISH),
		"RET_DIVINE_DEMON_TIME_INVALID":                           reflect.ValueOf(ret.RET_DIVINE_DEMON_TIME_INVALID),
		"RET_DROP_ACTIVITY_ID_NOT_MATCH":                          reflect.ValueOf(ret.RET_DROP_ACTIVITY_ID_NOT_MATCH),
		"RET_DROP_ACTIVITY_MAX_EXCHANGE_LIMIT":                    reflect.ValueOf(ret.RET_DROP_ACTIVITY_MAX_EXCHANGE_LIMIT),
		"RET_DROP_ACTIVITY_NO_ACTIVITY":                           reflect.ValueOf(ret.RET_DROP_ACTIVITY_NO_ACTIVITY),
		"RET_DUEL_IN_CD":                                          reflect.ValueOf(ret.RET_DUEL_IN_CD),
		"RET_DUEL_RIVAL_FORMATION_NOT_EXIST":                      reflect.ValueOf(ret.RET_DUEL_RIVAL_FORMATION_NOT_EXIST),
		"RET_DUNGEON_NOT_START_ONHOOK":                            reflect.ValueOf(ret.RET_DUNGEON_NOT_START_ONHOOK),
		"RET_DUNGEON_REPEAT_FIGHT":                                reflect.ValueOf(ret.RET_DUNGEON_REPEAT_FIGHT),
		"RET_EMBLEM_CUSTOMIZE_EMBLEM_ID_OVER_SIZE":                reflect.ValueOf(ret.RET_EMBLEM_CUSTOMIZE_EMBLEM_ID_OVER_SIZE),
		"RET_EMBLEM_CUSTOMIZE_ITEM_VALUE_ERROR":                   reflect.ValueOf(ret.RET_EMBLEM_CUSTOMIZE_ITEM_VALUE_ERROR),
		"RET_EMBLEM_CUSTOMIZE_RANDOM_TYPE_ERROR":                  reflect.ValueOf(ret.RET_EMBLEM_CUSTOMIZE_RANDOM_TYPE_ERROR),
		"RET_EMBLEM_CUSTOMIZE_TYPE_ERROR":                         reflect.ValueOf(ret.RET_EMBLEM_CUSTOMIZE_TYPE_ERROR),
		"RET_EMBLEM_DECOMPOSE_RARE_LIMIT":                         reflect.ValueOf(ret.RET_EMBLEM_DECOMPOSE_RARE_LIMIT),
		"RET_EMBLEM_FEATURE_HERO_JOB_NOT_MATCH":                   reflect.ValueOf(ret.RET_EMBLEM_FEATURE_HERO_JOB_NOT_MATCH),
		"RET_EMBLEM_NOT_EXIST":                                    reflect.ValueOf(ret.RET_EMBLEM_NOT_EXIST),
		"RET_EMBLEM_NOT_INITIAL":                                  reflect.ValueOf(ret.RET_EMBLEM_NOT_INITIAL),
		"RET_EMBLEM_SLOT_NOT_UNLOCK":                              reflect.ValueOf(ret.RET_EMBLEM_SLOT_NOT_UNLOCK),
		"RET_EMBLEM_UPGRADE_NOT_ORANGE_RARE":                      reflect.ValueOf(ret.RET_EMBLEM_UPGRADE_NOT_ORANGE_RARE),
		"RET_EMBLEM_WEAR_ERROR":                                   reflect.ValueOf(ret.RET_EMBLEM_WEAR_ERROR),
		"RET_EQUIP_NOT_EXIST":                                     reflect.ValueOf(ret.RET_EQUIP_NOT_EXIST),
		"RET_EQUIP_ONE_KEY_REMOVE_ERROR":                          reflect.ValueOf(ret.RET_EQUIP_ONE_KEY_REMOVE_ERROR),
		"RET_EQUIP_ONE_KEY_WEAR_ERROR":                            reflect.ValueOf(ret.RET_EQUIP_ONE_KEY_WEAR_ERROR),
		"RET_EQUIP_TARGET_LEVEL_ERROR":                            reflect.ValueOf(ret.RET_EQUIP_TARGET_LEVEL_ERROR),
		"RET_EQUIP_WAS_USED":                                      reflect.ValueOf(ret.RET_EQUIP_WAS_USED),
		"RET_EQUIP_WEAR_TYPE_ERROR":                               reflect.ValueOf(ret.RET_EQUIP_WEAR_TYPE_ERROR),
		"RET_ERROR":                                               reflect.ValueOf(ret.RET_ERROR),
		"RET_FIGHT_TIME_ILLEGAL":                                  reflect.ValueOf(ret.RET_FIGHT_TIME_ILLEGAL),
		"RET_FLOWER_ATTACK_OWN_FLOWERBED":                         reflect.ValueOf(ret.RET_FLOWER_ATTACK_OWN_FLOWERBED),
		"RET_FLOWER_BATTLE_ERROR":                                 reflect.ValueOf(ret.RET_FLOWER_BATTLE_ERROR),
		"RET_FLOWER_END_GROW":                                     reflect.ValueOf(ret.RET_FLOWER_END_GROW),
		"RET_FLOWER_ENEMY_LOCKED":                                 reflect.ValueOf(ret.RET_FLOWER_ENEMY_LOCKED),
		"RET_FLOWER_ENEMY_NOT_IN_SEARCH_LIST":                     reflect.ValueOf(ret.RET_FLOWER_ENEMY_NOT_IN_SEARCH_LIST),
		"RET_FLOWER_ENEMY_NO_SNATCH_CHANGE":                       reflect.ValueOf(ret.RET_FLOWER_ENEMY_NO_SNATCH_CHANGE),
		"RET_FLOWER_FLOWERBED_IN_PROTECTING":                      reflect.ValueOf(ret.RET_FLOWER_FLOWERBED_IN_PROTECTING),
		"RET_FLOWER_FLOWERBED_OWNER_NOT_SAME":                     reflect.ValueOf(ret.RET_FLOWER_FLOWERBED_OWNER_NOT_SAME),
		"RET_FLOWER_GOBLIN_CANNOT_CHANGE_AGAIN":                   reflect.ValueOf(ret.RET_FLOWER_GOBLIN_CANNOT_CHANGE_AGAIN),
		"RET_FLOWER_GUIDED_NOT_EXIST":                             reflect.ValueOf(ret.RET_FLOWER_GUIDED_NOT_EXIST),
		"RET_FLOWER_LEVEL_IS_TOP":                                 reflect.ValueOf(ret.RET_FLOWER_LEVEL_IS_TOP),
		"RET_FLOWER_LEVEL_UP_METHOD_ERROR":                        reflect.ValueOf(ret.RET_FLOWER_LEVEL_UP_METHOD_ERROR),
		"RET_FLOWER_LOG_NOT_EXIST":                                reflect.ValueOf(ret.RET_FLOWER_LOG_NOT_EXIST),
		"RET_FLOWER_NEED_HARVEST":                                 reflect.ValueOf(ret.RET_FLOWER_NEED_HARVEST),
		"RET_FLOWER_NOT_EXIST":                                    reflect.ValueOf(ret.RET_FLOWER_NOT_EXIST),
		"RET_FLOWER_NOT_HARVEST_TIME":                             reflect.ValueOf(ret.RET_FLOWER_NOT_HARVEST_TIME),
		"RET_FLOWER_NO_ATTACK_FORMATION":                          reflect.ValueOf(ret.RET_FLOWER_NO_ATTACK_FORMATION),
		"RET_FLOWER_NO_FLOWERBED":                                 reflect.ValueOf(ret.RET_FLOWER_NO_FLOWERBED),
		"RET_FLOWER_NO_OCCUPY_ATTACK_NUM":                         reflect.ValueOf(ret.RET_FLOWER_NO_OCCUPY_ATTACK_NUM),
		"RET_FLOWER_NO_OCCUPY_AWARD":                              reflect.ValueOf(ret.RET_FLOWER_NO_OCCUPY_AWARD),
		"RET_FLOWER_OCCUPY_ATTACK_NUM_LIMIT":                      reflect.ValueOf(ret.RET_FLOWER_OCCUPY_ATTACK_NUM_LIMIT),
		"RET_FLOWER_OCCUPY_AWARD_NEED_RECV":                       reflect.ValueOf(ret.RET_FLOWER_OCCUPY_AWARD_NEED_RECV),
		"RET_FLOWER_OCCUPY_EXTEND_NUM_LIMIT":                      reflect.ValueOf(ret.RET_FLOWER_OCCUPY_EXTEND_NUM_LIMIT),
		"RET_FLOWER_OCCUPY_MODE_NOT_OPEN":                         reflect.ValueOf(ret.RET_FLOWER_OCCUPY_MODE_NOT_OPEN),
		"RET_FLOWER_OTHER_SNATCH":                                 reflect.ValueOf(ret.RET_FLOWER_OTHER_SNATCH),
		"RET_FLOWER_PLANT_MODE_NOT_EXIST":                         reflect.ValueOf(ret.RET_FLOWER_PLANT_MODE_NOT_EXIST),
		"RET_FLOWER_RANDOM_GOBLIN_SCORE_FAILED":                   reflect.ValueOf(ret.RET_FLOWER_RANDOM_GOBLIN_SCORE_FAILED),
		"RET_FLOWER_REPEAT_SNATCH":                                reflect.ValueOf(ret.RET_FLOWER_REPEAT_SNATCH),
		"RET_FLOWER_SCORE_ERROR":                                  reflect.ValueOf(ret.RET_FLOWER_SCORE_ERROR),
		"RET_FLOWER_SEARCH_CD":                                    reflect.ValueOf(ret.RET_FLOWER_SEARCH_CD),
		"RET_FLOWER_SEED_NOT_EXIST":                               reflect.ValueOf(ret.RET_FLOWER_SEED_NOT_EXIST),
		"RET_FLOWER_SELF_LOCKED":                                  reflect.ValueOf(ret.RET_FLOWER_SELF_LOCKED),
		"RET_FLOWER_SHARE_CD_ERROR":                               reflect.ValueOf(ret.RET_FLOWER_SHARE_CD_ERROR),
		"RET_FLOWER_STAGE_ERROR":                                  reflect.ValueOf(ret.RET_FLOWER_STAGE_ERROR),
		"RET_FLOWER_START_FEED_FAILED":                            reflect.ValueOf(ret.RET_FLOWER_START_FEED_FAILED),
		"RET_FLOWER_TIMBER_NOT_OPEN":                              reflect.ValueOf(ret.RET_FLOWER_TIMBER_NOT_OPEN),
		"RET_FORECAST_AWARD_RECEIVED":                             reflect.ValueOf(ret.RET_FORECAST_AWARD_RECEIVED),
		"RET_FORECAST_NOT_EXIST":                                  reflect.ValueOf(ret.RET_FORECAST_NOT_EXIST),
		"RET_FORECAST_TASK_PROGRESS_NOT_FINISH":                   reflect.ValueOf(ret.RET_FORECAST_TASK_PROGRESS_NOT_FINISH),
		"RET_FOREST_BATTLE_ERROR":                                 reflect.ValueOf(ret.RET_FOREST_BATTLE_ERROR),
		"RET_FOREST_COLLECT_HERO_RARE_ERR":                        reflect.ValueOf(ret.RET_FOREST_COLLECT_HERO_RARE_ERR),
		"RET_FOREST_END_GROW":                                     reflect.ValueOf(ret.RET_FOREST_END_GROW),
		"RET_FOREST_ENEMY_LOCKED":                                 reflect.ValueOf(ret.RET_FOREST_ENEMY_LOCKED),
		"RET_FOREST_ENEMY_NOT_IN_SEARCH_LIST":                     reflect.ValueOf(ret.RET_FOREST_ENEMY_NOT_IN_SEARCH_LIST),
		"RET_FOREST_GOBLIN_CANNOT_CHANGE_AGAIN":                   reflect.ValueOf(ret.RET_FOREST_GOBLIN_CANNOT_CHANGE_AGAIN),
		"RET_FOREST_LEVEL_AWARD_RECEIVED":                         reflect.ValueOf(ret.RET_FOREST_LEVEL_AWARD_RECEIVED),
		"RET_FOREST_LEVEL_NO_AWARD":                               reflect.ValueOf(ret.RET_FOREST_LEVEL_NO_AWARD),
		"RET_FOREST_LEVEL_UNREACHED":                              reflect.ValueOf(ret.RET_FOREST_LEVEL_UNREACHED),
		"RET_FOREST_LOG_NOT_EXIST":                                reflect.ValueOf(ret.RET_FOREST_LOG_NOT_EXIST),
		"RET_FOREST_NEED_HARVEST":                                 reflect.ValueOf(ret.RET_FOREST_NEED_HARVEST),
		"RET_FOREST_NOT_EXIST":                                    reflect.ValueOf(ret.RET_FOREST_NOT_EXIST),
		"RET_FOREST_NOT_HARVEST_TIME":                             reflect.ValueOf(ret.RET_FOREST_NOT_HARVEST_TIME),
		"RET_FOREST_NO_ATTACK_FORMATION":                          reflect.ValueOf(ret.RET_FOREST_NO_ATTACK_FORMATION),
		"RET_FOREST_NO_LOOTED_CHANCE":                             reflect.ValueOf(ret.RET_FOREST_NO_LOOTED_CHANCE),
		"RET_FOREST_PVP_TREE_NOT_OPEN":                            reflect.ValueOf(ret.RET_FOREST_PVP_TREE_NOT_OPEN),
		"RET_FOREST_REPEAT_LOOT":                                  reflect.ValueOf(ret.RET_FOREST_REPEAT_LOOT),
		"RET_FOREST_SCORE_ERROR":                                  reflect.ValueOf(ret.RET_FOREST_SCORE_ERROR),
		"RET_FOREST_SEARCH_CD":                                    reflect.ValueOf(ret.RET_FOREST_SEARCH_CD),
		"RET_FOREST_STAGE_ERROR":                                  reflect.ValueOf(ret.RET_FOREST_STAGE_ERROR),
		"RET_FOREST_TREE_NOT_EXIST":                               reflect.ValueOf(ret.RET_FOREST_TREE_NOT_EXIST),
		"RET_FOREST_TREE_TYPE_ERROR":                              reflect.ValueOf(ret.RET_FOREST_TREE_TYPE_ERROR),
		"RET_FORMATION_DUPLICATE_POSITION":                        reflect.ValueOf(ret.RET_FORMATION_DUPLICATE_POSITION),
		"RET_FORMATION_DUPLICATE_RITE":                            reflect.ValueOf(ret.RET_FORMATION_DUPLICATE_RITE),
		"RET_FORMATION_INVALID_POSITION":                          reflect.ValueOf(ret.RET_FORMATION_INVALID_POSITION),
		"RET_FORMATION_NOT_EXIST":                                 reflect.ValueOf(ret.RET_FORMATION_NOT_EXIST),
		"RET_FORMATION_NOT_OMNIHERO_HAVE_LINKS":                   reflect.ValueOf(ret.RET_FORMATION_NOT_OMNIHERO_HAVE_LINKS),
		"RET_FORMATION_NOT_SEASON_FUNCTION":                       reflect.ValueOf(ret.RET_FORMATION_NOT_SEASON_FUNCTION),
		"RET_FORMATION_OMNIHERO_COUNT_EXCEED":                     reflect.ValueOf(ret.RET_FORMATION_OMNIHERO_COUNT_EXCEED),
		"RET_FORMATION_OMNIHERO_LINK_NOT_ACTIVE":                  reflect.ValueOf(ret.RET_FORMATION_OMNIHERO_LINK_NOT_ACTIVE),
		"RET_FORMATION_OMNIHERO_LINK_TYPE_ERR":                    reflect.ValueOf(ret.RET_FORMATION_OMNIHERO_LINK_TYPE_ERR),
		"RET_FORMATION_OMNIHERO_SEASON_LINK_NOT_MATCH":            reflect.ValueOf(ret.RET_FORMATION_OMNIHERO_SEASON_LINK_NOT_MATCH),
		"RET_FRAGMENT_USE_NUM_LIMIT":                              reflect.ValueOf(ret.RET_FRAGMENT_USE_NUM_LIMIT),
		"RET_FRIEND_IN_TARGET_BLACKLIST":                          reflect.ValueOf(ret.RET_FRIEND_IN_TARGET_BLACKLIST),
		"RET_FRIEND_LIST_FULL":                                    reflect.ValueOf(ret.RET_FRIEND_LIST_FULL),
		"RET_FRIEND_NOT_IN_REQUEST":                               reflect.ValueOf(ret.RET_FRIEND_NOT_IN_REQUEST),
		"RET_FRIEND_RECV_LIKE_COUNT_LIMIT":                        reflect.ValueOf(ret.RET_FRIEND_RECV_LIKE_COUNT_LIMIT),
		"RET_FRIEND_REPEATED_REQUEST":                             reflect.ValueOf(ret.RET_FRIEND_REPEATED_REQUEST),
		"RET_FRIEND_REQUEST_FULL":                                 reflect.ValueOf(ret.RET_FRIEND_REQUEST_FULL),
		"RET_FRIEND_SEND_LIKE_COUNT_LIMIT":                        reflect.ValueOf(ret.RET_FRIEND_SEND_LIKE_COUNT_LIMIT),
		"RET_FRIEND_TARGET_LIST_FULL":                             reflect.ValueOf(ret.RET_FRIEND_TARGET_LIST_FULL),
		"RET_FUNCTION_NOT_OPEN":                                   reflect.ValueOf(ret.RET_FUNCTION_NOT_OPEN),
		"RET_GATEWAY_CLIENTS_OUT_OF_LIMIT":                        reflect.ValueOf(ret.RET_GATEWAY_CLIENTS_OUT_OF_LIMIT),
		"RET_GEM_ADVANCED_ATTR_NOT_EXIST":                         reflect.ValueOf(ret.RET_GEM_ADVANCED_ATTR_NOT_EXIST),
		"RET_GEM_BE_HERO_WEAR":                                    reflect.ValueOf(ret.RET_GEM_BE_HERO_WEAR),
		"RET_GEM_CONVERT_OP_TYPE_INVALID":                         reflect.ValueOf(ret.RET_GEM_CONVERT_OP_TYPE_INVALID),
		"RET_GEM_GEN_ATTR_IS_ZERO":                                reflect.ValueOf(ret.RET_GEM_GEN_ATTR_IS_ZERO),
		"RET_GEM_INHERIT_ATTR_NOT_MATCH":                          reflect.ValueOf(ret.RET_GEM_INHERIT_ATTR_NOT_MATCH),
		"RET_GEM_LOCK_ATTR_NOT_MATCH":                             reflect.ValueOf(ret.RET_GEM_LOCK_ATTR_NOT_MATCH),
		"RET_GEM_NOT_ENOUGH_LEVEL":                                reflect.ValueOf(ret.RET_GEM_NOT_ENOUGH_LEVEL),
		"RET_GEM_NOT_ENOUGH_SPACE_IN_BAG":                         reflect.ValueOf(ret.RET_GEM_NOT_ENOUGH_SPACE_IN_BAG),
		"RET_GEM_NOT_ENOUGH_STAR":                                 reflect.ValueOf(ret.RET_GEM_NOT_ENOUGH_STAR),
		"RET_GEM_NOT_EXIST":                                       reflect.ValueOf(ret.RET_GEM_NOT_EXIST),
		"RET_GEM_NOT_MATCH_COMPOSE_SUM":                           reflect.ValueOf(ret.RET_GEM_NOT_MATCH_COMPOSE_SUM),
		"RET_GEM_NOT_MATCH_TARGET_RARE":                           reflect.ValueOf(ret.RET_GEM_NOT_MATCH_TARGET_RARE),
		"RET_GEM_NOT_SAME_RARE":                                   reflect.ValueOf(ret.RET_GEM_NOT_SAME_RARE),
		"RET_GEM_NO_REBUILD":                                      reflect.ValueOf(ret.RET_GEM_NO_REBUILD),
		"RET_GEM_SLOT_NOT_MATCH":                                  reflect.ValueOf(ret.RET_GEM_SLOT_NOT_MATCH),
		"RET_GEM_TMP_ATTR_NOT_EXIST":                              reflect.ValueOf(ret.RET_GEM_TMP_ATTR_NOT_EXIST),
		"RET_GEM_UNLOCK_NOT_MATCH":                                reflect.ValueOf(ret.RET_GEM_UNLOCK_NOT_MATCH),
		"RET_GET_USER_DATA_ERROR":                                 reflect.ValueOf(ret.RET_GET_USER_DATA_ERROR),
		"RET_GET_USER_DATA_ID_NOT_EXIST":                          reflect.ValueOf(ret.RET_GET_USER_DATA_ID_NOT_EXIST),
		"RET_GIFT_CODE_CHANNEL_ERROR":                             reflect.ValueOf(ret.RET_GIFT_CODE_CHANNEL_ERROR),
		"RET_GIFT_CODE_CLOSED":                                    reflect.ValueOf(ret.RET_GIFT_CODE_CLOSED),
		"RET_GIFT_CODE_EXPIRE":                                    reflect.ValueOf(ret.RET_GIFT_CODE_EXPIRE),
		"RET_GIFT_CODE_EXPIRE_OR_NOT_EXIST":                       reflect.ValueOf(ret.RET_GIFT_CODE_EXPIRE_OR_NOT_EXIST),
		"RET_GIFT_CODE_FREQUENT":                                  reflect.ValueOf(ret.RET_GIFT_CODE_FREQUENT),
		"RET_GIFT_CODE_ILLEGAL":                                   reflect.ValueOf(ret.RET_GIFT_CODE_ILLEGAL),
		"RET_GIFT_CODE_OP_ID_ERROR":                               reflect.ValueOf(ret.RET_GIFT_CODE_OP_ID_ERROR),
		"RET_GIFT_CODE_OVER_USE_COUNT":                            reflect.ValueOf(ret.RET_GIFT_CODE_OVER_USE_COUNT),
		"RET_GIFT_CODE_SERVICE_ERROR":                             reflect.ValueOf(ret.RET_GIFT_CODE_SERVICE_ERROR),
		"RET_GIFT_CODE_USED_OTHERS":                               reflect.ValueOf(ret.RET_GIFT_CODE_USED_OTHERS),
		"RET_GIFT_CODE_USED_SELF":                                 reflect.ValueOf(ret.RET_GIFT_CODE_USED_SELF),
		"RET_GIFT_CODE_USE_ERROR":                                 reflect.ValueOf(ret.RET_GIFT_CODE_USE_ERROR),
		"RET_GM_ACTIVITY_DATA_UPDATE_FAILED":                      reflect.ValueOf(ret.RET_GM_ACTIVITY_DATA_UPDATE_FAILED),
		"RET_GM_COMMON_ACTIVITY_FIX_TIME_ERR":                     reflect.ValueOf(ret.RET_GM_COMMON_ACTIVITY_FIX_TIME_ERR),
		"RET_GM_CROSS_SET_PART_AREA_NO_NEED_NEXT_PART":            reflect.ValueOf(ret.RET_GM_CROSS_SET_PART_AREA_NO_NEED_NEXT_PART),
		"RET_GM_DAILY_WISH_ACTIVITY_LIMIT_ERR":                    reflect.ValueOf(ret.RET_GM_DAILY_WISH_ACTIVITY_LIMIT_ERR),
		"RET_GM_DAILY_WISH_ACTIVITY_RECHARGE_TRIGGER_ERR":         reflect.ValueOf(ret.RET_GM_DAILY_WISH_ACTIVITY_RECHARGE_TRIGGER_ERR),
		"RET_GM_GUILD_NOT_EXIST":                                  reflect.ValueOf(ret.RET_GM_GUILD_NOT_EXIST),
		"RET_GM_REQ_PARAM_ILLEGAL":                                reflect.ValueOf(ret.RET_GM_REQ_PARAM_ILLEGAL),
		"RET_GM_SERVER_ERROR":                                     reflect.ValueOf(ret.RET_GM_SERVER_ERROR),
		"RET_GM_TIME_IS_LESS_THAN_CROSS_NOW":                      reflect.ValueOf(ret.RET_GM_TIME_IS_LESS_THAN_CROSS_NOW),
		"RET_GM_TIME_IS_LESS_THAN_LOGIC_NOW":                      reflect.ValueOf(ret.RET_GM_TIME_IS_LESS_THAN_LOGIC_NOW),
		"RET_GODDESS_CONTRACT_MAX_LEVEL":                          reflect.ValueOf(ret.RET_GODDESS_CONTRACT_MAX_LEVEL),
		"RET_GODDESS_DUNGEON_LOCK":                                reflect.ValueOf(ret.RET_GODDESS_DUNGEON_LOCK),
		"RET_GODDESS_FEED_ITEM_NOT_ENOUGH":                        reflect.ValueOf(ret.RET_GODDESS_FEED_ITEM_NOT_ENOUGH),
		"RET_GODDESS_HAS_INIT":                                    reflect.ValueOf(ret.RET_GODDESS_HAS_INIT),
		"RET_GODDESS_INIT_FAILED":                                 reflect.ValueOf(ret.RET_GODDESS_INIT_FAILED),
		"RET_GODDESS_IS_MAX_LEVEL":                                reflect.ValueOf(ret.RET_GODDESS_IS_MAX_LEVEL),
		"RET_GODDESS_LEVEL_NOT_ENOUGH":                            reflect.ValueOf(ret.RET_GODDESS_LEVEL_NOT_ENOUGH),
		"RET_GODDESS_NOT_INIT":                                    reflect.ValueOf(ret.RET_GODDESS_NOT_INIT),
		"RET_GODDESS_PRE_DUNGEON_NOT_FINISH":                      reflect.ValueOf(ret.RET_GODDESS_PRE_DUNGEON_NOT_FINISH),
		"RET_GODDESS_PRE_GODDESS_NOT_FINISH":                      reflect.ValueOf(ret.RET_GODDESS_PRE_GODDESS_NOT_FINISH),
		"RET_GODDESS_PRE_GODDESS_NOT_INIT":                        reflect.ValueOf(ret.RET_GODDESS_PRE_GODDESS_NOT_INIT),
		"RET_GODDESS_RECOVER_FINISH":                              reflect.ValueOf(ret.RET_GODDESS_RECOVER_FINISH),
		"RET_GODDESS_RECOVER_NOT_FINISH":                          reflect.ValueOf(ret.RET_GODDESS_RECOVER_NOT_FINISH),
		"RET_GODDESS_RECOVER_TIME_NOT_ENOUGH":                     reflect.ValueOf(ret.RET_GODDESS_RECOVER_TIME_NOT_ENOUGH),
		"RET_GODDESS_STORY_AWARD_IS_TOKEN":                        reflect.ValueOf(ret.RET_GODDESS_STORY_AWARD_IS_TOKEN),
		"RET_GODDESS_SUIT_NOT_EXIST":                              reflect.ValueOf(ret.RET_GODDESS_SUIT_NOT_EXIST),
		"RET_GODDESS_TOUCH_COUNT_NOT_ENOUGH":                      reflect.ValueOf(ret.RET_GODDESS_TOUCH_COUNT_NOT_ENOUGH),
		"RET_GOD_PRESENT_ALREADY_INIT":                            reflect.ValueOf(ret.RET_GOD_PRESENT_ALREADY_INIT),
		"RET_GOD_PRESENT_ANOTHER_POOL_HAS_STASH_AWARD":            reflect.ValueOf(ret.RET_GOD_PRESENT_ANOTHER_POOL_HAS_STASH_AWARD),
		"RET_GOD_PRESENT_AWARD_ALREADY_RECV":                      reflect.ValueOf(ret.RET_GOD_PRESENT_AWARD_ALREADY_RECV),
		"RET_GOD_PRESENT_LAST_NOT_FINISH":                         reflect.ValueOf(ret.RET_GOD_PRESENT_LAST_NOT_FINISH),
		"RET_GOD_PRESENT_NOT_EXIST":                               reflect.ValueOf(ret.RET_GOD_PRESENT_NOT_EXIST),
		"RET_GOD_PRESENT_SUMMON_ALREADY_FINISH":                   reflect.ValueOf(ret.RET_GOD_PRESENT_SUMMON_ALREADY_FINISH),
		"RET_GOD_PRESENT_SUMMON_NOT_FINISH":                       reflect.ValueOf(ret.RET_GOD_PRESENT_SUMMON_NOT_FINISH),
		"RET_GOD_PRESENT_TIME_ERROR":                              reflect.ValueOf(ret.RET_GOD_PRESENT_TIME_ERROR),
		"RET_GOLDBUY_BUY_GROUP_NOT_EXIST":                         reflect.ValueOf(ret.RET_GOLDBUY_BUY_GROUP_NOT_EXIST),
		"RET_GOLDBUY_CHEST_NOT_EXIST":                             reflect.ValueOf(ret.RET_GOLDBUY_CHEST_NOT_EXIST),
		"RET_GOLDBUY_NOT_EXIST":                                   reflect.ValueOf(ret.RET_GOLDBUY_NOT_EXIST),
		"RET_GOLDBUY_USE_COUNT_LIMIT":                             reflect.ValueOf(ret.RET_GOLDBUY_USE_COUNT_LIMIT),
		"RET_GOODS_BUY_NUM_LIMIT":                                 reflect.ValueOf(ret.RET_GOODS_BUY_NUM_LIMIT),
		"RET_GRPC_SERVER_NOT_OPEN":                                reflect.ValueOf(ret.RET_GRPC_SERVER_NOT_OPEN),
		"RET_GST_ARENA_NOT_OPEN":                                  reflect.ValueOf(ret.RET_GST_ARENA_NOT_OPEN),
		"RET_GST_ARENA_TEAM_LIMIT":                                reflect.ValueOf(ret.RET_GST_ARENA_TEAM_LIMIT),
		"RET_GST_ARENA_VOTE_TIMES_NOT_ENOUGH":                     reflect.ValueOf(ret.RET_GST_ARENA_VOTE_TIMES_NOT_ENOUGH),
		"RET_GST_BOSS_ALREADY_REFRESH":                            reflect.ValueOf(ret.RET_GST_BOSS_ALREADY_REFRESH),
		"RET_GST_BOSS_AWARD_ALREADY_RESET":                        reflect.ValueOf(ret.RET_GST_BOSS_AWARD_ALREADY_RESET),
		"RET_GST_BOSS_FIGHT_AWARD":                                reflect.ValueOf(ret.RET_GST_BOSS_FIGHT_AWARD),
		"RET_GST_BOSS_IS_DIED":                                    reflect.ValueOf(ret.RET_GST_BOSS_IS_DIED),
		"RET_GST_BOSS_USER_NOT_SIGN":                              reflect.ValueOf(ret.RET_GST_BOSS_USER_NOT_SIGN),
		"RET_GST_CANT_FIND_TEAM":                                  reflect.ValueOf(ret.RET_GST_CANT_FIND_TEAM),
		"RET_GST_CHALLENGE_CANT_FIGHT":                            reflect.ValueOf(ret.RET_GST_CHALLENGE_CANT_FIGHT),
		"RET_GST_CHALLENGE_NEED_CHOOSE_BUFF":                      reflect.ValueOf(ret.RET_GST_CHALLENGE_NEED_CHOOSE_BUFF),
		"RET_GST_CHALLENGE_NOT_OPEN":                              reflect.ValueOf(ret.RET_GST_CHALLENGE_NOT_OPEN),
		"RET_GST_CHALLENGE_OPPONENT_NIL":                          reflect.ValueOf(ret.RET_GST_CHALLENGE_OPPONENT_NIL),
		"RET_GST_CHALLENGE_QUIT_LIMIT":                            reflect.ValueOf(ret.RET_GST_CHALLENGE_QUIT_LIMIT),
		"RET_GST_CHALLENGE_TEAM_EXPIRE":                           reflect.ValueOf(ret.RET_GST_CHALLENGE_TEAM_EXPIRE),
		"RET_GST_DISPATCH_HERO_FORMATION_REPEATER":                reflect.ValueOf(ret.RET_GST_DISPATCH_HERO_FORMATION_REPEATER),
		"RET_GST_DISPATCH_HERO_OTHER_BUILD_REPEATER":              reflect.ValueOf(ret.RET_GST_DISPATCH_HERO_OTHER_BUILD_REPEATER),
		"RET_GST_DRAGON_CANNOT_ATTACK":                            reflect.ValueOf(ret.RET_GST_DRAGON_CANNOT_ATTACK),
		"RET_GST_DRAGON_CANNOT_EVOLVE":                            reflect.ValueOf(ret.RET_GST_DRAGON_CANNOT_EVOLVE),
		"RET_GST_DRAGON_LEVEL_NOT_ENOUGH":                         reflect.ValueOf(ret.RET_GST_DRAGON_LEVEL_NOT_ENOUGH),
		"RET_GST_DRAGON_NOT_IN_FIGHT":                             reflect.ValueOf(ret.RET_GST_DRAGON_NOT_IN_FIGHT),
		"RET_GST_DRAGON_OFF":                                      reflect.ValueOf(ret.RET_GST_DRAGON_OFF),
		"RET_GST_DRAGON_POS_LOCKED":                               reflect.ValueOf(ret.RET_GST_DRAGON_POS_LOCKED),
		"RET_GST_DRAGON_SETTLEMENT":                               reflect.ValueOf(ret.RET_GST_DRAGON_SETTLEMENT),
		"RET_GST_GROUND_NIL":                                      reflect.ValueOf(ret.RET_GST_GROUND_NIL),
		"RET_GST_GROUP_NIL":                                       reflect.ValueOf(ret.RET_GST_GROUP_NIL),
		"RET_GST_GROUP_NOT_MATCH":                                 reflect.ValueOf(ret.RET_GST_GROUP_NOT_MATCH),
		"RET_GST_GUILD_CHANGED":                                   reflect.ValueOf(ret.RET_GST_GUILD_CHANGED),
		"RET_GST_GUILD_LEVEL_IS_NOT_OPEN":                         reflect.ValueOf(ret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN),
		"RET_GST_GUILD_NIL":                                       reflect.ValueOf(ret.RET_GST_GUILD_NIL),
		"RET_GST_MOVE_LINE_ILLEGAL":                               reflect.ValueOf(ret.RET_GST_MOVE_LINE_ILLEGAL),
		"RET_GST_ORE_CANT_FIGHT":                                  reflect.ValueOf(ret.RET_GST_ORE_CANT_FIGHT),
		"RET_GST_ORE_CANT_FIND":                                   reflect.ValueOf(ret.RET_GST_ORE_CANT_FIND),
		"RET_GST_ORE_CANT_SWEEP":                                  reflect.ValueOf(ret.RET_GST_ORE_CANT_SWEEP),
		"RET_GST_ORE_FIGHT_TIMES_NOT_ENOUGH":                      reflect.ValueOf(ret.RET_GST_ORE_FIGHT_TIMES_NOT_ENOUGH),
		"RET_GST_ORE_NOT_IN_FIGHT_TIME":                           reflect.ValueOf(ret.RET_GST_ORE_NOT_IN_FIGHT_TIME),
		"RET_GST_ORE_NUM_MAX":                                     reflect.ValueOf(ret.RET_GST_ORE_NUM_MAX),
		"RET_GST_RECV_TASK_SEASON_OR_ROUND_NOT_SAME":              reflect.ValueOf(ret.RET_GST_RECV_TASK_SEASON_OR_ROUND_NOT_SAME),
		"RET_GST_SKILL_ASSEMBLE_GROUND_MAX":                       reflect.ValueOf(ret.RET_GST_SKILL_ASSEMBLE_GROUND_MAX),
		"RET_GST_SKILL_ASSEMBLE_TIMES_NOT_ENOUGH":                 reflect.ValueOf(ret.RET_GST_SKILL_ASSEMBLE_TIMES_NOT_ENOUGH),
		"RET_GST_SYNC_GUILD_NIL":                                  reflect.ValueOf(ret.RET_GST_SYNC_GUILD_NIL),
		"RET_GST_TEAMS_NUM_ERR":                                   reflect.ValueOf(ret.RET_GST_TEAMS_NUM_ERR),
		"RET_GST_TEAM_NOT_HANGUP":                                 reflect.ValueOf(ret.RET_GST_TEAM_NOT_HANGUP),
		"RET_GST_TECH_CANT_DONATE":                                reflect.ValueOf(ret.RET_GST_TECH_CANT_DONATE),
		"RET_GST_TECH_CANT_FIND":                                  reflect.ValueOf(ret.RET_GST_TECH_CANT_FIND),
		"RET_GST_TECH_CANT_LEVEL_UP":                              reflect.ValueOf(ret.RET_GST_TECH_CANT_LEVEL_UP),
		"RET_GST_USER_CANT_OPERATE":                               reflect.ValueOf(ret.RET_GST_USER_CANT_OPERATE),
		"RET_GST_USER_CANT_SIGN":                                  reflect.ValueOf(ret.RET_GST_USER_CANT_SIGN),
		"RET_GST_USER_DONATE_ITEM_ID_ERROR":                       reflect.ValueOf(ret.RET_GST_USER_DONATE_ITEM_ID_ERROR),
		"RET_GST_USER_HAD_SIGN":                                   reflect.ValueOf(ret.RET_GST_USER_HAD_SIGN),
		"RET_GST_USER_NOT_SIGN":                                   reflect.ValueOf(ret.RET_GST_USER_NOT_SIGN),
		"RET_GST_USER_ONCE_DONATE_COUNT_IS_ERROR":                 reflect.ValueOf(ret.RET_GST_USER_ONCE_DONATE_COUNT_IS_ERROR),
		"RET_GUIDANCE_NOT_CAN_SELECT_SKIP":                        reflect.ValueOf(ret.RET_GUIDANCE_NOT_CAN_SELECT_SKIP),
		"RET_GUIDANCE_REPEAT_SELECT_SKIP":                         reflect.ValueOf(ret.RET_GUIDANCE_REPEAT_SELECT_SKIP),
		"RET_GUIDANCE_REPEAT_SKIP":                                reflect.ValueOf(ret.RET_GUIDANCE_REPEAT_SKIP),
		"RET_GUID_DUNGEON_CHAPTER_NOT_FINISH":                     reflect.ValueOf(ret.RET_GUID_DUNGEON_CHAPTER_NOT_FINISH),
		"RET_GUILD_APPLY_HAS_BEEN_PROCESSED":                      reflect.ValueOf(ret.RET_GUILD_APPLY_HAS_BEEN_PROCESSED),
		"RET_GUILD_APPLY_LIST_FULL":                               reflect.ValueOf(ret.RET_GUILD_APPLY_LIST_FULL),
		"RET_GUILD_APPLY_REPEAT":                                  reflect.ValueOf(ret.RET_GUILD_APPLY_REPEAT),
		"RET_GUILD_CHEST_ACTIVATE_IS_MAX":                         reflect.ValueOf(ret.RET_GUILD_CHEST_ACTIVATE_IS_MAX),
		"RET_GUILD_CHEST_ACTIVATE_REPEATED":                       reflect.ValueOf(ret.RET_GUILD_CHEST_ACTIVATE_REPEATED),
		"RET_GUILD_CHEST_ATTACH_LIMIT":                            reflect.ValueOf(ret.RET_GUILD_CHEST_ATTACH_LIMIT),
		"RET_GUILD_CHEST_CAN_NOT_RECV":                            reflect.ValueOf(ret.RET_GUILD_CHEST_CAN_NOT_RECV),
		"RET_GUILD_CHEST_CHEST_IS_EXIST":                          reflect.ValueOf(ret.RET_GUILD_CHEST_CHEST_IS_EXIST),
		"RET_GUILD_CHEST_CHEST_RECV_FIN":                          reflect.ValueOf(ret.RET_GUILD_CHEST_CHEST_RECV_FIN),
		"RET_GUILD_CHEST_ID_IS_NIL":                               reflect.ValueOf(ret.RET_GUILD_CHEST_ID_IS_NIL),
		"RET_GUILD_CHEST_ITEM_IS_EXPIRE":                          reflect.ValueOf(ret.RET_GUILD_CHEST_ITEM_IS_EXPIRE),
		"RET_GUILD_CHEST_LIKE_REPEATED":                           reflect.ValueOf(ret.RET_GUILD_CHEST_LIKE_REPEATED),
		"RET_GUILD_CHEST_RECV_BEFORE_LIKE":                        reflect.ValueOf(ret.RET_GUILD_CHEST_RECV_BEFORE_LIKE),
		"RET_GUILD_CHEST_RECV_REPEATED":                           reflect.ValueOf(ret.RET_GUILD_CHEST_RECV_REPEATED),
		"RET_GUILD_COMBINE_APPLY_EXPIRED":                         reflect.ValueOf(ret.RET_GUILD_COMBINE_APPLY_EXPIRED),
		"RET_GUILD_COMBINE_APPLY_NOT_EXIST":                       reflect.ValueOf(ret.RET_GUILD_COMBINE_APPLY_NOT_EXIST),
		"RET_GUILD_COMBINE_APPLY_RECV_MAX":                        reflect.ValueOf(ret.RET_GUILD_COMBINE_APPLY_RECV_MAX),
		"RET_GUILD_COMBINE_APPLY_REFUSE_FAILED":                   reflect.ValueOf(ret.RET_GUILD_COMBINE_APPLY_REFUSE_FAILED),
		"RET_GUILD_COMBINE_APPLY_SEND_MAX":                        reflect.ValueOf(ret.RET_GUILD_COMBINE_APPLY_SEND_MAX),
		"RET_GUILD_COMBINE_INVITE_LEADER_NOT_JOIN":                reflect.ValueOf(ret.RET_GUILD_COMBINE_INVITE_LEADER_NOT_JOIN),
		"RET_GUILD_COMBINE_INVITE_MEMBER_LIMIT":                   reflect.ValueOf(ret.RET_GUILD_COMBINE_INVITE_MEMBER_LIMIT),
		"RET_GUILD_COMBINE_NOT_OPEN":                              reflect.ValueOf(ret.RET_GUILD_COMBINE_NOT_OPEN),
		"RET_GUILD_COMBINE_REQUEST_LEADER_NOT_JOIN":               reflect.ValueOf(ret.RET_GUILD_COMBINE_REQUEST_LEADER_NOT_JOIN),
		"RET_GUILD_COMBINE_REQUEST_MEMBER_LIMIT":                  reflect.ValueOf(ret.RET_GUILD_COMBINE_REQUEST_MEMBER_LIMIT),
		"RET_GUILD_COMBINE_TARGET_NOT_OPEN":                       reflect.ValueOf(ret.RET_GUILD_COMBINE_TARGET_NOT_OPEN),
		"RET_GUILD_CREATE_CD":                                     reflect.ValueOf(ret.RET_GUILD_CREATE_CD),
		"RET_GUILD_DEPUTY_COUNT_LIMIT":                            reflect.ValueOf(ret.RET_GUILD_DEPUTY_COUNT_LIMIT),
		"RET_GUILD_DONATE_COUNT_MAX":                              reflect.ValueOf(ret.RET_GUILD_DONATE_COUNT_MAX),
		"RET_GUILD_DONATE_GET_AWARD_REPEATED":                     reflect.ValueOf(ret.RET_GUILD_DONATE_GET_AWARD_REPEATED),
		"RET_GUILD_DONATE_POINT_NOT_ENOUGH":                       reflect.ValueOf(ret.RET_GUILD_DONATE_POINT_NOT_ENOUGH),
		"RET_GUILD_DUNGEON_BOSS_BE_DEFEATED":                      reflect.ValueOf(ret.RET_GUILD_DUNGEON_BOSS_BE_DEFEATED),
		"RET_GUILD_DUNGEON_CHAPTER_CHANGE":                        reflect.ValueOf(ret.RET_GUILD_DUNGEON_CHAPTER_CHANGE),
		"RET_GUILD_DUNGEON_NO_BOSS_CAN_RESTORE":                   reflect.ValueOf(ret.RET_GUILD_DUNGEON_NO_BOSS_CAN_RESTORE),
		"RET_GUILD_DUNGEON_NO_CACHE":                              reflect.ValueOf(ret.RET_GUILD_DUNGEON_NO_CACHE),
		"RET_GUILD_DUNGEON_RESETTING":                             reflect.ValueOf(ret.RET_GUILD_DUNGEON_RESETTING),
		"RET_GUILD_DUNGEON_RESETTING_LIMIT_KICK_MEMBER":           reflect.ValueOf(ret.RET_GUILD_DUNGEON_RESETTING_LIMIT_KICK_MEMBER),
		"RET_GUILD_DUNGEON_STRATEGY_RESURRECTED_CHAPTER_BOSS":     reflect.ValueOf(ret.RET_GUILD_DUNGEON_STRATEGY_RESURRECTED_CHAPTER_BOSS),
		"RET_GUILD_DUNGEON_SWEEP_LIMIT":                           reflect.ValueOf(ret.RET_GUILD_DUNGEON_SWEEP_LIMIT),
		"RET_GUILD_DUNGEON_TASK_NOT_FINISH":                       reflect.ValueOf(ret.RET_GUILD_DUNGEON_TASK_NOT_FINISH),
		"RET_GUILD_DUNGEON_TOP_DIVISION_NOT_FINISH":               reflect.ValueOf(ret.RET_GUILD_DUNGEON_TOP_DIVISION_NOT_FINISH),
		"RET_GUILD_HAVE_MEMBER_DISBAND_BE_LIMIT":                  reflect.ValueOf(ret.RET_GUILD_HAVE_MEMBER_DISBAND_BE_LIMIT),
		"RET_GUILD_JOIN_CD":                                       reflect.ValueOf(ret.RET_GUILD_JOIN_CD),
		"RET_GUILD_JOIN_LEVEL_LIMIT":                              reflect.ValueOf(ret.RET_GUILD_JOIN_LEVEL_LIMIT),
		"RET_GUILD_JOIN_POWER_LIMIT":                              reflect.ValueOf(ret.RET_GUILD_JOIN_POWER_LIMIT),
		"RET_GUILD_KICK_COUNT_LIMIT":                              reflect.ValueOf(ret.RET_GUILD_KICK_COUNT_LIMIT),
		"RET_GUILD_KICK_GRADE_LIMIT":                              reflect.ValueOf(ret.RET_GUILD_KICK_GRADE_LIMIT),
		"RET_GUILD_LOADING":                                       reflect.ValueOf(ret.RET_GUILD_LOADING),
		"RET_GUILD_MEDAL_LIKE_REPEAT":                             reflect.ValueOf(ret.RET_GUILD_MEDAL_LIKE_REPEAT),
		"RET_GUILD_MEDAL_NOT_OPEN":                                reflect.ValueOf(ret.RET_GUILD_MEDAL_NOT_OPEN),
		"RET_GUILD_MEMBER_COUNT_LIMIT":                            reflect.ValueOf(ret.RET_GUILD_MEMBER_COUNT_LIMIT),
		"RET_GUILD_MOB_ACCEPT_TASK_REPEATED":                      reflect.ValueOf(ret.RET_GUILD_MOB_ACCEPT_TASK_REPEATED),
		"RET_GUILD_MOB_ACCEPT_TIMES_NOT_ENOUGH":                   reflect.ValueOf(ret.RET_GUILD_MOB_ACCEPT_TIMES_NOT_ENOUGH),
		"RET_GUILD_MOB_BUY_FAILED":                                reflect.ValueOf(ret.RET_GUILD_MOB_BUY_FAILED),
		"RET_GUILD_MOB_BUY_LIMIT":                                 reflect.ValueOf(ret.RET_GUILD_MOB_BUY_LIMIT),
		"RET_GUILD_MOB_FRESH_TIMES_NOT_ENOUGH":                    reflect.ValueOf(ret.RET_GUILD_MOB_FRESH_TIMES_NOT_ENOUGH),
		"RET_GUILD_MOB_GUILD_ACCEPT_TASK_LIMIT":                   reflect.ValueOf(ret.RET_GUILD_MOB_GUILD_ACCEPT_TASK_LIMIT),
		"RET_GUILD_MOB_GUILD_LEVEL_ERR":                           reflect.ValueOf(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR),
		"RET_GUILD_MOB_NO_TASK":                                   reflect.ValueOf(ret.RET_GUILD_MOB_NO_TASK),
		"RET_GUILD_MOB_SELF_ACCEPT_TASK_LIMIT":                    reflect.ValueOf(ret.RET_GUILD_MOB_SELF_ACCEPT_TASK_LIMIT),
		"RET_GUILD_MOB_TASKING":                                   reflect.ValueOf(ret.RET_GUILD_MOB_TASKING),
		"RET_GUILD_MOB_TASK_OPEN_DAY_LIMIT":                       reflect.ValueOf(ret.RET_GUILD_MOB_TASK_OPEN_DAY_LIMIT),
		"RET_GUILD_NAME_USED":                                     reflect.ValueOf(ret.RET_GUILD_NAME_USED),
		"RET_GUILD_NEED_APPLY":                                    reflect.ValueOf(ret.RET_GUILD_NEED_APPLY),
		"RET_GUILD_NOTICE_FORBIDDEN":                              reflect.ValueOf(ret.RET_GUILD_NOTICE_FORBIDDEN),
		"RET_GUILD_NOT_EXIST":                                     reflect.ValueOf(ret.RET_GUILD_NOT_EXIST),
		"RET_GUILD_RECRUIT_CD_NOT_EXPIRE":                         reflect.ValueOf(ret.RET_GUILD_RECRUIT_CD_NOT_EXPIRE),
		"RET_GUILD_RECRUIT_GRADE_INVALID":                         reflect.ValueOf(ret.RET_GUILD_RECRUIT_GRADE_INVALID),
		"RET_GUILD_REFUSE_JOIN":                                   reflect.ValueOf(ret.RET_GUILD_REFUSE_JOIN),
		"RET_GUILD_SEND_MAIL_CD":                                  reflect.ValueOf(ret.RET_GUILD_SEND_MAIL_CD),
		"RET_GUILD_SET_NAME_CD":                                   reflect.ValueOf(ret.RET_GUILD_SET_NAME_CD),
		"RET_GUILD_SIGN_IN_LIMIT":                                 reflect.ValueOf(ret.RET_GUILD_SIGN_IN_LIMIT),
		"RET_GUILD_STRING_ILLEGAL_CHARACTER":                      reflect.ValueOf(ret.RET_GUILD_STRING_ILLEGAL_CHARACTER),
		"RET_GUILD_STRING_LENGTH_LIMIT":                           reflect.ValueOf(ret.RET_GUILD_STRING_LENGTH_LIMIT),
		"RET_GUILD_TALENT_ALL_JOB_LEVEL_LIMIT":                    reflect.ValueOf(ret.RET_GUILD_TALENT_ALL_JOB_LEVEL_LIMIT),
		"RET_GUILD_TALENT_CONNECT_TYPE_NOT_INVALID":               reflect.ValueOf(ret.RET_GUILD_TALENT_CONNECT_TYPE_NOT_INVALID),
		"RET_GUILD_TALENT_FLOOR_NOT_RESET":                        reflect.ValueOf(ret.RET_GUILD_TALENT_FLOOR_NOT_RESET),
		"RET_GUILD_TALENT_JOB_LEVEL_FULL":                         reflect.ValueOf(ret.RET_GUILD_TALENT_JOB_LEVEL_FULL),
		"RET_GUILD_TALENT_LEVEL_INVALID_NOT_RESET":                reflect.ValueOf(ret.RET_GUILD_TALENT_LEVEL_INVALID_NOT_RESET),
		"RET_GUILD_TALENT_LEVEL_NOT_INVALID":                      reflect.ValueOf(ret.RET_GUILD_TALENT_LEVEL_NOT_INVALID),
		"RET_GUILD_TALENT_NODE_ALREADY_LEVEL_UP":                  reflect.ValueOf(ret.RET_GUILD_TALENT_NODE_ALREADY_LEVEL_UP),
		"RET_GUILD_TALENT_NOT_INIT":                               reflect.ValueOf(ret.RET_GUILD_TALENT_NOT_INIT),
		"RET_GUILD_TALENT_OTHER_JOB_LEVEL_LIMIT":                  reflect.ValueOf(ret.RET_GUILD_TALENT_OTHER_JOB_LEVEL_LIMIT),
		"RET_GUILD_TALENT_POINT_NOT_ENOUGH":                       reflect.ValueOf(ret.RET_GUILD_TALENT_POINT_NOT_ENOUGH),
		"RET_GUILD_TALENT_PRE_NODE_LEVEL_NOT_ENOUGH":              reflect.ValueOf(ret.RET_GUILD_TALENT_PRE_NODE_LEVEL_NOT_ENOUGH),
		"RET_GUILD_TALENT_PRE_NODE_NOT_ACTIVATE":                  reflect.ValueOf(ret.RET_GUILD_TALENT_PRE_NODE_NOT_ACTIVATE),
		"RET_GUILD_TALENT_PURCHASE_ID_INVALID":                    reflect.ValueOf(ret.RET_GUILD_TALENT_PURCHASE_ID_INVALID),
		"RET_GUILD_TALENT_XML_DATA_NOT_EXIST":                     reflect.ValueOf(ret.RET_GUILD_TALENT_XML_DATA_NOT_EXIST),
		"RET_HANDBOOK_ACTIVATE_CHECK_ERROR":                       reflect.ValueOf(ret.RET_HANDBOOK_ACTIVATE_CHECK_ERROR),
		"RET_HANDBOOK_NOT_EXIST":                                  reflect.ValueOf(ret.RET_HANDBOOK_NOT_EXIST),
		"RET_HANDBOOK_REPEAT_ACTIVATE":                            reflect.ValueOf(ret.RET_HANDBOOK_REPEAT_ACTIVATE),
		"RET_HAS_GUILD":                                           reflect.ValueOf(ret.RET_HAS_GUILD),
		"RET_HAVE_SAME_ARTIFACT":                                  reflect.ValueOf(ret.RET_HAVE_SAME_ARTIFACT),
		"RET_HERO_AWAKEN_LEVEL_MAX":                               reflect.ValueOf(ret.RET_HERO_AWAKEN_LEVEL_MAX),
		"RET_HERO_AWAKEN_LEVEL_OUT_OF_LIMIT":                      reflect.ValueOf(ret.RET_HERO_AWAKEN_LEVEL_OUT_OF_LIMIT),
		"RET_HERO_AWAKEN_NOT_UNLOCKED":                            reflect.ValueOf(ret.RET_HERO_AWAKEN_NOT_UNLOCKED),
		"RET_HERO_CANNOT_CONSUMED":                                reflect.ValueOf(ret.RET_HERO_CANNOT_CONSUMED),
		"RET_HERO_CONVERT_AWAKEN_NOT_MORE_THAN_MAX":               reflect.ValueOf(ret.RET_HERO_CONVERT_AWAKEN_NOT_MORE_THAN_MAX),
		"RET_HERO_CONVERT_NO_RES_TO_CONVERT":                      reflect.ValueOf(ret.RET_HERO_CONVERT_NO_RES_TO_CONVERT),
		"RET_HERO_CULTIVATED":                                     reflect.ValueOf(ret.RET_HERO_CULTIVATED),
		"RET_HERO_DISBAND_CHECK_FAILED":                           reflect.ValueOf(ret.RET_HERO_DISBAND_CHECK_FAILED),
		"RET_HERO_EQUIPPED":                                       reflect.ValueOf(ret.RET_HERO_EQUIPPED),
		"RET_HERO_EXCHANGE_RACE_NOT_MATCH":                        reflect.ValueOf(ret.RET_HERO_EXCHANGE_RACE_NOT_MATCH),
		"RET_HERO_GEM_NOT_OPEN":                                   reflect.ValueOf(ret.RET_HERO_GEM_NOT_OPEN),
		"RET_HERO_IN_CRYSTAL":                                     reflect.ValueOf(ret.RET_HERO_IN_CRYSTAL),
		"RET_HERO_LOCKED":                                         reflect.ValueOf(ret.RET_HERO_LOCKED),
		"RET_HERO_MAX_LEVEL_LIMIT":                                reflect.ValueOf(ret.RET_HERO_MAX_LEVEL_LIMIT),
		"RET_HERO_NOT_AWAKENABLE":                                 reflect.ValueOf(ret.RET_HERO_NOT_AWAKENABLE),
		"RET_HERO_NOT_ENOUGH":                                     reflect.ValueOf(ret.RET_HERO_NOT_ENOUGH),
		"RET_HERO_NOT_EXIST":                                      reflect.ValueOf(ret.RET_HERO_NOT_EXIST),
		"RET_HERO_NOT_IN_CRYSTAL":                                 reflect.ValueOf(ret.RET_HERO_NOT_IN_CRYSTAL),
		"RET_HERO_SLOT_MAX_LIMIT":                                 reflect.ValueOf(ret.RET_HERO_SLOT_MAX_LIMIT),
		"RET_HERO_STAGE_MAX_LEVEL_LIMIT":                          reflect.ValueOf(ret.RET_HERO_STAGE_MAX_LEVEL_LIMIT),
		"RET_HERO_STARUP_CARDS_NOT_MATCH":                         reflect.ValueOf(ret.RET_HERO_STARUP_CARDS_NOT_MATCH),
		"RET_HERO_STAR_NOT_ENOUSH":                                reflect.ValueOf(ret.RET_HERO_STAR_NOT_ENOUSH),
		"RET_INVALID_TIME_FORMAT":                                 reflect.ValueOf(ret.RET_INVALID_TIME_FORMAT),
		"RET_IP_SIZE_OUT_OF_LIMIT":                                reflect.ValueOf(ret.RET_IP_SIZE_OUT_OF_LIMIT),
		"RET_ITEM_CANNOT_USE":                                     reflect.ValueOf(ret.RET_ITEM_CANNOT_USE),
		"RET_ITEM_CANNOT_USE_LEVEL":                               reflect.ValueOf(ret.RET_ITEM_CANNOT_USE_LEVEL),
		"RET_ITEM_USE_DUJIE_PROP_FULL":                            reflect.ValueOf(ret.RET_ITEM_USE_DUJIE_PROP_FULL),
		"RET_ITEM_USE_NUM_LIMIT":                                  reflect.ValueOf(ret.RET_ITEM_USE_NUM_LIMIT),
		"RET_KNIGHT_NOT_EXIST":                                    reflect.ValueOf(ret.RET_KNIGHT_NOT_EXIST),
		"RET_LEVEL_UP_MAX":                                        reflect.ValueOf(ret.RET_LEVEL_UP_MAX),
		"RET_LINK_SUMMON_POOL_ID_IS_ERROR":                        reflect.ValueOf(ret.RET_LINK_SUMMON_POOL_ID_IS_ERROR),
		"RET_LINK_SUMMON_RAND_GROUP_FAILED":                       reflect.ValueOf(ret.RET_LINK_SUMMON_RAND_GROUP_FAILED),
		"RET_LINK_SUMMON_TIMES_UPDATE":                            reflect.ValueOf(ret.RET_LINK_SUMMON_TIMES_UPDATE),
		"RET_LOGIN_ABNORMAL":                                      reflect.ValueOf(ret.RET_LOGIN_ABNORMAL),
		"RET_LOGIN_BEFORE_START_TIME":                             reflect.ValueOf(ret.RET_LOGIN_BEFORE_START_TIME),
		"RET_LOGIN_LIMIT":                                         reflect.ValueOf(ret.RET_LOGIN_LIMIT),
		"RET_LOGIN_REPEAT":                                        reflect.ValueOf(ret.RET_LOGIN_REPEAT),
		"RET_MAIL_HAS_DELETE_OR_EXPIRED":                          reflect.ValueOf(ret.RET_MAIL_HAS_DELETE_OR_EXPIRED),
		"RET_MAIN_ROLE_TYPE_ILLEGAL":                              reflect.ValueOf(ret.RET_MAIN_ROLE_TYPE_ILLEGAL),
		"RET_MAX_HERO_STAR_LIMIT":                                 reflect.ValueOf(ret.RET_MAX_HERO_STAR_LIMIT),
		"RET_MAZE_BATTLE_AWARDS_NOT_EXIST":                        reflect.ValueOf(ret.RET_MAZE_BATTLE_AWARDS_NOT_EXIST),
		"RET_MAZE_BOSS_GRID_NOT_ALLOWED_GO_UP":                    reflect.ValueOf(ret.RET_MAZE_BOSS_GRID_NOT_ALLOWED_GO_UP),
		"RET_MAZE_BOX_AWARDS_NOT_EXIST":                           reflect.ValueOf(ret.RET_MAZE_BOX_AWARDS_NOT_EXIST),
		"RET_MAZE_BOX_ID_NOT_EXIST":                               reflect.ValueOf(ret.RET_MAZE_BOX_ID_NOT_EXIST),
		"RET_MAZE_BOX_KEY_NOT_ENOUGH":                             reflect.ValueOf(ret.RET_MAZE_BOX_KEY_NOT_ENOUGH),
		"RET_MAZE_BUFF_ID_NOT_EXIST":                              reflect.ValueOf(ret.RET_MAZE_BUFF_ID_NOT_EXIST),
		"RET_MAZE_BUY_RECEIVE_COUNT_NOT_EXIST":                    reflect.ValueOf(ret.RET_MAZE_BUY_RECEIVE_COUNT_NOT_EXIST),
		"RET_MAZE_CURSE_LAND_NOT_EXIST":                           reflect.ValueOf(ret.RET_MAZE_CURSE_LAND_NOT_EXIST),
		"RET_MAZE_DE_BUFF_NOT_EXIST":                              reflect.ValueOf(ret.RET_MAZE_DE_BUFF_NOT_EXIST),
		"RET_MAZE_DUNGEON_RANK_NOT_EXIST":                         reflect.ValueOf(ret.RET_MAZE_DUNGEON_RANK_NOT_EXIST),
		"RET_MAZE_EVENT_COMPLETED":                                reflect.ValueOf(ret.RET_MAZE_EVENT_COMPLETED),
		"RET_MAZE_EVENT_ID_NOT_EXIST":                             reflect.ValueOf(ret.RET_MAZE_EVENT_ID_NOT_EXIST),
		"RET_MAZE_EVENT_TYPE_NOT_EXIST":                           reflect.ValueOf(ret.RET_MAZE_EVENT_TYPE_NOT_EXIST),
		"RET_MAZE_FORMATION_NOT_EXIST":                            reflect.ValueOf(ret.RET_MAZE_FORMATION_NOT_EXIST),
		"RET_MAZE_GRID_NOT_EXIST":                                 reflect.ValueOf(ret.RET_MAZE_GRID_NOT_EXIST),
		"RET_MAZE_GUARD_BATTLE_COUNT_NOT_ENOUGH":                  reflect.ValueOf(ret.RET_MAZE_GUARD_BATTLE_COUNT_NOT_ENOUGH),
		"RET_MAZE_HERO_HP_IS_ZERO":                                reflect.ValueOf(ret.RET_MAZE_HERO_HP_IS_ZERO),
		"RET_MAZE_HERO_RECOVERY_CONFIG_NOT_EXIST":                 reflect.ValueOf(ret.RET_MAZE_HERO_RECOVERY_CONFIG_NOT_EXIST),
		"RET_MAZE_MAP_EXPIRED":                                    reflect.ValueOf(ret.RET_MAZE_MAP_EXPIRED),
		"RET_MAZE_MAP_NOT_EXIST":                                  reflect.ValueOf(ret.RET_MAZE_MAP_NOT_EXIST),
		"RET_MAZE_MAP_PLAYER_NOT_EXIST":                           reflect.ValueOf(ret.RET_MAZE_MAP_PLAYER_NOT_EXIST),
		"RET_MAZE_MONTHLY_CARD_NOT_EXIST":                         reflect.ValueOf(ret.RET_MAZE_MONTHLY_CARD_NOT_EXIST),
		"RET_MAZE_OBSTACLE_NOT_ALLOWED_GO_UP":                     reflect.ValueOf(ret.RET_MAZE_OBSTACLE_NOT_ALLOWED_GO_UP),
		"RET_MAZE_PASS_EVENT_NOT_COMPLETE":                        reflect.ValueOf(ret.RET_MAZE_PASS_EVENT_NOT_COMPLETE),
		"RET_MAZE_PASS_NUM_NOT_ENOUGH":                            reflect.ValueOf(ret.RET_MAZE_PASS_NUM_NOT_ENOUGH),
		"RET_MAZE_POWER_NOT_ENOUGH":                               reflect.ValueOf(ret.RET_MAZE_POWER_NOT_ENOUGH),
		"RET_MAZE_RANDOM_ALTER_BUFF_FAILED":                       reflect.ValueOf(ret.RET_MAZE_RANDOM_ALTER_BUFF_FAILED),
		"RET_MAZE_REPEAT_SWEEP":                                   reflect.ValueOf(ret.RET_MAZE_REPEAT_SWEEP),
		"RET_MAZE_REVIVE_NOT_USE":                                 reflect.ValueOf(ret.RET_MAZE_REVIVE_NOT_USE),
		"RET_MAZE_SNAPSHOT_USER_NOT_EXIST":                        reflect.ValueOf(ret.RET_MAZE_SNAPSHOT_USER_NOT_EXIST),
		"RET_MAZE_SOUL_ALTER_COMPLETED_NUM_NOT_ENOUGH":            reflect.ValueOf(ret.RET_MAZE_SOUL_ALTER_COMPLETED_NUM_NOT_ENOUGH),
		"RET_MAZE_TASK_AWARD_RECEIVED":                            reflect.ValueOf(ret.RET_MAZE_TASK_AWARD_RECEIVED),
		"RET_MAZE_TASK_PROGRESS_NOT_FINISH":                       reflect.ValueOf(ret.RET_MAZE_TASK_PROGRESS_NOT_FINISH),
		"RET_MAZE_USER_LEVEL_NOT_EXIST":                           reflect.ValueOf(ret.RET_MAZE_USER_LEVEL_NOT_EXIST),
		"RET_MAZE_XML_DATA_CONFIG_ERROR":                          reflect.ValueOf(ret.RET_MAZE_XML_DATA_CONFIG_ERROR),
		"RET_MEDAL_DAILY_AWARD_NOT_OPEN":                          reflect.ValueOf(ret.RET_MEDAL_DAILY_AWARD_NOT_OPEN),
		"RET_MEDAL_NOT_EXIST":                                     reflect.ValueOf(ret.RET_MEDAL_NOT_EXIST),
		"RET_MEDAL_REPEAT_RECEIVE_AWARD":                          reflect.ValueOf(ret.RET_MEDAL_REPEAT_RECEIVE_AWARD),
		"RET_MEDAL_TASK_AWARD_NOT_RECEIVE":                        reflect.ValueOf(ret.RET_MEDAL_TASK_AWARD_NOT_RECEIVE),
		"RET_MEMORY_CHIP_GROUP_NOT_FIRST":                         reflect.ValueOf(ret.RET_MEMORY_CHIP_GROUP_NOT_FIRST),
		"RET_MEMORY_CHIP_GROUP_NOT_SAME":                          reflect.ValueOf(ret.RET_MEMORY_CHIP_GROUP_NOT_SAME),
		"RET_MEMORY_CHIP_NOT_EXIST":                               reflect.ValueOf(ret.RET_MEMORY_CHIP_NOT_EXIST),
		"RET_MEMORY_CHIP_PRE_GROUP_NOT_VALID":                     reflect.ValueOf(ret.RET_MEMORY_CHIP_PRE_GROUP_NOT_VALID),
		"RET_MEMORY_CHIP_REPEAT_UNLOCK":                           reflect.ValueOf(ret.RET_MEMORY_CHIP_REPEAT_UNLOCK),
		"RET_MIRAGE_AFFIX_NOT_EXIST":                              reflect.ValueOf(ret.RET_MIRAGE_AFFIX_NOT_EXIST),
		"RET_MIRAGE_ATTACK_FORMATION_NOT_EXIST":                   reflect.ValueOf(ret.RET_MIRAGE_ATTACK_FORMATION_NOT_EXIST),
		"RET_MIRAGE_AWARD_ALREADY_RECEIVED":                       reflect.ValueOf(ret.RET_MIRAGE_AWARD_ALREADY_RECEIVED),
		"RET_MIRAGE_CANNOT_SET_AFFIX":                             reflect.ValueOf(ret.RET_MIRAGE_CANNOT_SET_AFFIX),
		"RET_MIRAGE_DROP_AWARD_NOT_EXIST":                         reflect.ValueOf(ret.RET_MIRAGE_DROP_AWARD_NOT_EXIST),
		"RET_MIRAGE_FIGHT_COUNT_NOT_ENOUGH":                       reflect.ValueOf(ret.RET_MIRAGE_FIGHT_COUNT_NOT_ENOUGH),
		"RET_MIRAGE_HURDLE_ALREADY_PASS":                          reflect.ValueOf(ret.RET_MIRAGE_HURDLE_ALREADY_PASS),
		"RET_MIRAGE_HURDLE_NOT_EXIST_AWARD":                       reflect.ValueOf(ret.RET_MIRAGE_HURDLE_NOT_EXIST_AWARD),
		"RET_MIRAGE_NOT_EXIST":                                    reflect.ValueOf(ret.RET_MIRAGE_NOT_EXIST),
		"RET_MIRAGE_NOT_OPEN":                                     reflect.ValueOf(ret.RET_MIRAGE_NOT_OPEN),
		"RET_MIRAGE_POWER_NOT_ENOUGH":                             reflect.ValueOf(ret.RET_MIRAGE_POWER_NOT_ENOUGH),
		"RET_MIRAGE_STAR_NOT_ENOUGH":                              reflect.ValueOf(ret.RET_MIRAGE_STAR_NOT_ENOUGH),
		"RET_MIRAGE_VICTORY_AWARD_NOT_EXIST":                      reflect.ValueOf(ret.RET_MIRAGE_VICTORY_AWARD_NOT_EXIST),
		"RET_MODULE_NOT_REGISTER":                                 reflect.ValueOf(ret.RET_MODULE_NOT_REGISTER),
		"RET_MONTHLY_CARD_NOT_EXIST":                              reflect.ValueOf(ret.RET_MONTHLY_CARD_NOT_EXIST),
		"RET_MONTH_TASKS_DAILY_REWARD_END":                        reflect.ValueOf(ret.RET_MONTH_TASKS_DAILY_REWARD_END),
		"RET_MONTH_TASKS_GROUP_NOT_OPEN":                          reflect.ValueOf(ret.RET_MONTH_TASKS_GROUP_NOT_OPEN),
		"RET_MONTH_TASKS_TASK_NOT_FINISH":                         reflect.ValueOf(ret.RET_MONTH_TASKS_TASK_NOT_FINISH),
		"RET_MUTE_JASON_MARSHAL_FAIL":                             reflect.ValueOf(ret.RET_MUTE_JASON_MARSHAL_FAIL),
		"RET_MUTE_SERVER_ERROR":                                   reflect.ValueOf(ret.RET_MUTE_SERVER_ERROR),
		"RET_NEW_YEAR_ACTIVITY_FUNCTION_IS_NOT_OPEN":              reflect.ValueOf(ret.RET_NEW_YEAR_ACTIVITY_FUNCTION_IS_NOT_OPEN),
		"RET_NEW_YEAR_ACTIVITY_LOGIN_AWARD_CONDITION_FAILED":      reflect.ValueOf(ret.RET_NEW_YEAR_ACTIVITY_LOGIN_AWARD_CONDITION_FAILED),
		"RET_NEW_YEAR_ACTIVITY_NEED_GET_DATA_BEFORE":              reflect.ValueOf(ret.RET_NEW_YEAR_ACTIVITY_NEED_GET_DATA_BEFORE),
		"RET_NOT_ENOUGH_RESOURCES":                                reflect.ValueOf(ret.RET_NOT_ENOUGH_RESOURCES),
		"RET_NOT_ENOUGH_SPACE_IN_BAGS":                            reflect.ValueOf(ret.RET_NOT_ENOUGH_SPACE_IN_BAGS),
		"RET_NOT_GUILD_LEADER":                                    reflect.ValueOf(ret.RET_NOT_GUILD_LEADER),
		"RET_NOT_GUILD_LEADER_OR_DEPUTY":                          reflect.ValueOf(ret.RET_NOT_GUILD_LEADER_OR_DEPUTY),
		"RET_NOT_GUILD_MEMBER":                                    reflect.ValueOf(ret.RET_NOT_GUILD_MEMBER),
		"RET_NOT_IN_GUILD":                                        reflect.ValueOf(ret.RET_NOT_IN_GUILD),
		"RET_NOT_ITEM_RESOURCES":                                  reflect.ValueOf(ret.RET_NOT_ITEM_RESOURCES),
		"RET_NOT_RANDOM_SHOP":                                     reflect.ValueOf(ret.RET_NOT_RANDOM_SHOP),
		"RET_NO_DUNGEON_FORMATION":                                reflect.ValueOf(ret.RET_NO_DUNGEON_FORMATION),
		"RET_NO_FIND_USER":                                        reflect.ValueOf(ret.RET_NO_FIND_USER),
		"RET_NO_GUILD_CAN_JOIN":                                   reflect.ValueOf(ret.RET_NO_GUILD_CAN_JOIN),
		"RET_NO_HERO_AWAKEN_LEVEL_UNLOCKED":                       reflect.ValueOf(ret.RET_NO_HERO_AWAKEN_LEVEL_UNLOCKED),
		"RET_NO_HERO_REACH_TOP_STAR":                              reflect.ValueOf(ret.RET_NO_HERO_REACH_TOP_STAR),
		"RET_NO_ITEM":                                             reflect.ValueOf(ret.RET_NO_ITEM),
		"RET_NO_REFRESH_TIMES":                                    reflect.ValueOf(ret.RET_NO_REFRESH_TIMES),
		"RET_OK":                                                  reflect.ValueOf(ret.RET_OK),
		"RET_ONHOOK_AWARD_ERROR":                                  reflect.ValueOf(ret.RET_ONHOOK_AWARD_ERROR),
		"RET_ONHOOK_TIME_ERROR":                                   reflect.ValueOf(ret.RET_ONHOOK_TIME_ERROR),
		"RET_OPERATE_ACTIVITY_INIT_TYPE_ERROR":                    reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_INIT_TYPE_ERROR),
		"RET_OPERATE_ACTIVITY_IS_NOT_OPENING":                     reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_IS_NOT_OPENING),
		"RET_OPERATE_ACTIVITY_LAST_ROUND_NOT_FINISH":              reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_LAST_ROUND_NOT_FINISH),
		"RET_OPERATE_ACTIVITY_NOT_INIT":                           reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_NOT_INIT),
		"RET_OPERATE_ACTIVITY_PROGRESS_NOT_FINISH":                reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_PROGRESS_NOT_FINISH),
		"RET_OPERATE_ACTIVITY_ROUND_TYPE_IS_ERROR":                reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_ROUND_TYPE_IS_ERROR),
		"RET_OPERATE_ACTIVITY_SUB_TASK_ROUND_ERROR":               reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_SUB_TASK_ROUND_ERROR),
		"RET_OPERATE_ACTIVITY_TASK_AWARD_REPEAT_RECEIVE":          reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_TASK_AWARD_REPEAT_RECEIVE),
		"RET_OPERATE_ACTIVITY_TASK_IN_NOVICE_PROTECTION":          reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_TASK_IN_NOVICE_PROTECTION),
		"RET_OPERATE_ACTIVITY_TASK_PROGRESS_NOT_FINISH":           reflect.ValueOf(ret.RET_OPERATE_ACTIVITY_TASK_PROGRESS_NOT_FINISH),
		"RET_OPERATE_TOO_OFTEN":                                   reflect.ValueOf(ret.RET_OPERATE_TOO_OFTEN),
		"RET_PARAM_LENGTH_LIMIT":                                  reflect.ValueOf(ret.RET_PARAM_LENGTH_LIMIT),
		"RET_PASS_ACTIVE_NOT_RECHARGE":                            reflect.ValueOf(ret.RET_PASS_ACTIVE_NOT_RECHARGE),
		"RET_PASS_ACTIVE_TASK_FINISH":                             reflect.ValueOf(ret.RET_PASS_ACTIVE_TASK_FINISH),
		"RET_PASS_PREVIOUS_NOT_FINISHED":                          reflect.ValueOf(ret.RET_PASS_PREVIOUS_NOT_FINISHED),
		"RET_PASS_SYSTEM_ID_NOT_INIT":                             reflect.ValueOf(ret.RET_PASS_SYSTEM_ID_NOT_INIT),
		"RET_PASS_TASK_AWARD_REPEAT_RECEIVE":                      reflect.ValueOf(ret.RET_PASS_TASK_AWARD_REPEAT_RECEIVE),
		"RET_PASS_TASK_NOT_MATCH":                                 reflect.ValueOf(ret.RET_PASS_TASK_NOT_MATCH),
		"RET_PASS_TASK_PROGRESS_NOT_FINISH":                       reflect.ValueOf(ret.RET_PASS_TASK_PROGRESS_NOT_FINISH),
		"RET_PEAK_FIGHT_NOT_FINISH":                               reflect.ValueOf(ret.RET_PEAK_FIGHT_NOT_FINISH),
		"RET_PEAK_FIRST_SEASON_NOT_START":                         reflect.ValueOf(ret.RET_PEAK_FIRST_SEASON_NOT_START),
		"RET_PEAK_GURSS_TIME_END":                                 reflect.ValueOf(ret.RET_PEAK_GURSS_TIME_END),
		"RET_PEAK_GURSS_UID_NOT_IN_MATCH":                         reflect.ValueOf(ret.RET_PEAK_GURSS_UID_NOT_IN_MATCH),
		"RET_PEAK_MATCH_NOT_EXIST":                                reflect.ValueOf(ret.RET_PEAK_MATCH_NOT_EXIST),
		"RET_PEAK_MATCH_ROUND_ERR":                                reflect.ValueOf(ret.RET_PEAK_MATCH_ROUND_ERR),
		"RET_PEAK_NOT_GUESS_TIME":                                 reflect.ValueOf(ret.RET_PEAK_NOT_GUESS_TIME),
		"RET_PEAK_NOT_IN_PHASE":                                   reflect.ValueOf(ret.RET_PEAK_NOT_IN_PHASE),
		"RET_PEAK_NOT_IN_SEASON":                                  reflect.ValueOf(ret.RET_PEAK_NOT_IN_SEASON),
		"RET_PEAK_NOT_PLAYER":                                     reflect.ValueOf(ret.RET_PEAK_NOT_PLAYER),
		"RET_PEAK_NO_FIGHTER":                                     reflect.ValueOf(ret.RET_PEAK_NO_FIGHTER),
		"RET_PEAK_NO_GUESS_COUNT":                                 reflect.ValueOf(ret.RET_PEAK_NO_GUESS_COUNT),
		"RET_PEAK_NO_TOP3":                                        reflect.ValueOf(ret.RET_PEAK_NO_TOP3),
		"RET_PEAK_NO_USER":                                        reflect.ValueOf(ret.RET_PEAK_NO_USER),
		"RET_PEAK_PHASE_FINISHED":                                 reflect.ValueOf(ret.RET_PEAK_PHASE_FINISHED),
		"RET_PEAK_PHASE_NOT_OPEN":                                 reflect.ValueOf(ret.RET_PEAK_PHASE_NOT_OPEN),
		"RET_PEAK_RANDOM_WORSHIP_DROP_FAIL":                       reflect.ValueOf(ret.RET_PEAK_RANDOM_WORSHIP_DROP_FAIL),
		"RET_PEAK_RECEIVED_INVITE_REWARD":                         reflect.ValueOf(ret.RET_PEAK_RECEIVED_INVITE_REWARD),
		"RET_PEAK_SAME_MATCH_GUESS_DIFFERENT":                     reflect.ValueOf(ret.RET_PEAK_SAME_MATCH_GUESS_DIFFERENT),
		"RET_PREVIOUS_DUNGEON_NOT_FINISH":                         reflect.ValueOf(ret.RET_PREVIOUS_DUNGEON_NOT_FINISH),
		"RET_PRE_SEASON_RECV_CHECK_FAILED":                        reflect.ValueOf(ret.RET_PRE_SEASON_RECV_CHECK_FAILED),
		"RET_PROMOTION_BUY_NUMBER_LIMIT":                          reflect.ValueOf(ret.RET_PROMOTION_BUY_NUMBER_LIMIT),
		"RET_PROMOTION_CHAIN_GIFT_PRE_GIFT_NOT_BUY":               reflect.ValueOf(ret.RET_PROMOTION_CHAIN_GIFT_PRE_GIFT_NOT_BUY),
		"RET_PROMOTION_GIFT_LIMIT":                                reflect.ValueOf(ret.RET_PROMOTION_GIFT_LIMIT),
		"RET_PROMOTION_OPTIONAL_GIFT_AWARD_NOT_CHOOSE":            reflect.ValueOf(ret.RET_PROMOTION_OPTIONAL_GIFT_AWARD_NOT_CHOOSE),
		"RET_QUEUING":                                             reflect.ValueOf(ret.RET_QUEUING),
		"RET_RATE_ALREADY_SCORED":                                 reflect.ValueOf(ret.RET_RATE_ALREADY_SCORED),
		"RET_REBASE_JASON_MARSHAL_FAIL":                           reflect.ValueOf(ret.RET_REBASE_JASON_MARSHAL_FAIL),
		"RET_REBASE_SERVER_ERROR":                                 reflect.ValueOf(ret.RET_REBASE_SERVER_ERROR),
		"RET_RECHARGE_ORDER_PROCESS_FAIL":                         reflect.ValueOf(ret.RET_RECHARGE_ORDER_PROCESS_FAIL),
		"RET_REFUND_COUPON_DISABLED":                              reflect.ValueOf(ret.RET_REFUND_COUPON_DISABLED),
		"RET_REFUND_ORDER_NOT_EXIST":                              reflect.ValueOf(ret.RET_REFUND_ORDER_NOT_EXIST),
		"RET_REFUND_ORDER_PROCESS_FAIL":                           reflect.ValueOf(ret.RET_REFUND_ORDER_PROCESS_FAIL),
		"RET_REPEATED_PARAM":                                      reflect.ValueOf(ret.RET_REPEATED_PARAM),
		"RET_REPEATED_RECEIVE_AWARD":                              reflect.ValueOf(ret.RET_REPEATED_RECEIVE_AWARD),
		"RET_REPEAT_RECV_SAVE_H5_REWARD":                          reflect.ValueOf(ret.RET_REPEAT_RECV_SAVE_H5_REWARD),
		"RET_RESONANCE_HERO_FORBIDDEN_OPERATE":                    reflect.ValueOf(ret.RET_RESONANCE_HERO_FORBIDDEN_OPERATE),
		"RET_RESOURCE_CAN_NOT_COST":                               reflect.ValueOf(ret.RET_RESOURCE_CAN_NOT_COST),
		"RET_RESOURCE_CHECK_ATTR_FAILED":                          reflect.ValueOf(ret.RET_RESOURCE_CHECK_ATTR_FAILED),
		"RET_RESOURCE_COST_ID_IS_REPEATER":                        reflect.ValueOf(ret.RET_RESOURCE_COST_ID_IS_REPEATER),
		"RET_RESOURCE_IS_NIL_OR_COUNT_ZERO":                       reflect.ValueOf(ret.RET_RESOURCE_IS_NIL_OR_COUNT_ZERO),
		"RET_RESOURCE_LEN_IS_ZERO":                                reflect.ValueOf(ret.RET_RESOURCE_LEN_IS_ZERO),
		"RET_RESOURCE_MERGE_GET_XML_DATA_FAILED":                  reflect.ValueOf(ret.RET_RESOURCE_MERGE_GET_XML_DATA_FAILED),
		"RET_RESOURCE_RESOLVE_FAILED":                             reflect.ValueOf(ret.RET_RESOURCE_RESOLVE_FAILED),
		"RET_RETURN_INVALID_LOGIN_DAY_INDEX":                      reflect.ValueOf(ret.RET_RETURN_INVALID_LOGIN_DAY_INDEX),
		"RET_RETURN_NOT_OPEN":                                     reflect.ValueOf(ret.RET_RETURN_NOT_OPEN),
		"RET_RITE_DUPLICATED_RARE":                                reflect.ValueOf(ret.RET_RITE_DUPLICATED_RARE),
		"RET_RITE_GRIDS_NOT_ALL_EQUIPPED":                         reflect.ValueOf(ret.RET_RITE_GRIDS_NOT_ALL_EQUIPPED),
		"RET_RITE_GRIDS_RARE_NOT_ENOUGH":                          reflect.ValueOf(ret.RET_RITE_GRIDS_RARE_NOT_ENOUGH),
		"RET_RITE_INVALID_RARE":                                   reflect.ValueOf(ret.RET_RITE_INVALID_RARE),
		"RET_RITE_MAX_RARE":                                       reflect.ValueOf(ret.RET_RITE_MAX_RARE),
		"RET_RITE_NOT_ACTIVE":                                     reflect.ValueOf(ret.RET_RITE_NOT_ACTIVE),
		"RET_RITE_NOT_FOR_USER":                                   reflect.ValueOf(ret.RET_RITE_NOT_FOR_USER),
		"RET_RITE_NOT_FOUND":                                      reflect.ValueOf(ret.RET_RITE_NOT_FOUND),
		"RET_RITE_POWER_NOT_FOUND":                                reflect.ValueOf(ret.RET_RITE_POWER_NOT_FOUND),
		"RET_RITE_RARE_NOT_ENOUGH":                                reflect.ValueOf(ret.RET_RITE_RARE_NOT_ENOUGH),
		"RET_SEASON_ARENA_CROSS_USER_LOCKED":                      reflect.ValueOf(ret.RET_SEASON_ARENA_CROSS_USER_LOCKED),
		"RET_SEASON_ARENA_FIGHT_CHECK_FAILED":                     reflect.ValueOf(ret.RET_SEASON_ARENA_FIGHT_CHECK_FAILED),
		"RET_SEASON_ARENA_FIGHT_CHECK_FREE_REFRESH":               reflect.ValueOf(ret.RET_SEASON_ARENA_FIGHT_CHECK_FREE_REFRESH),
		"RET_SEASON_ARENA_FIGHT_COUNT_CHECK_FAILED":               reflect.ValueOf(ret.RET_SEASON_ARENA_FIGHT_COUNT_CHECK_FAILED),
		"RET_SEASON_ARENA_FREE_REFRESH_CHECK_FAILED":              reflect.ValueOf(ret.RET_SEASON_ARENA_FREE_REFRESH_CHECK_FAILED),
		"RET_SEASON_ARENA_ROUND_AWARD_CHECK_FAILED":               reflect.ValueOf(ret.RET_SEASON_ARENA_ROUND_AWARD_CHECK_FAILED),
		"RET_SEASON_ARENA_SEARCH_OPPONENTS_FAILED":                reflect.ValueOf(ret.RET_SEASON_ARENA_SEARCH_OPPONENTS_FAILED),
		"RET_SEASON_ARENA_SELF_IS_ATTACKING":                      reflect.ValueOf(ret.RET_SEASON_ARENA_SELF_IS_ATTACKING),
		"RET_SEASON_ARENA_SERVER_ARENA_CHECK_FAILED":              reflect.ValueOf(ret.RET_SEASON_ARENA_SERVER_ARENA_CHECK_FAILED),
		"RET_SEASON_ARENA_STATE_NOT_OPEN":                         reflect.ValueOf(ret.RET_SEASON_ARENA_STATE_NOT_OPEN),
		"RET_SEASON_ARENA_STA_IS_NOT_OPEN":                        reflect.ValueOf(ret.RET_SEASON_ARENA_STA_IS_NOT_OPEN),
		"RET_SEASON_ARENA_TASK_PROGRESS_NOT_ENOUGH":               reflect.ValueOf(ret.RET_SEASON_ARENA_TASK_PROGRESS_NOT_ENOUGH),
		"RET_SEASON_ARENA_USER_NOT_FIND":                          reflect.ValueOf(ret.RET_SEASON_ARENA_USER_NOT_FIND),
		"RET_SEASON_DOOR_LINE_CANT_FIGHT":                         reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_CANT_FIGHT),
		"RET_SEASON_DOOR_LINE_FIGHT_HAD_FINISH":                   reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_FIGHT_HAD_FINISH),
		"RET_SEASON_DOOR_LINE_FIGHT_NOT_FINISH":                   reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_FIGHT_NOT_FINISH),
		"RET_SEASON_DOOR_LINE_FIGHT_NO_REWARD":                    reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_FIGHT_NO_REWARD),
		"RET_SEASON_DOOR_LINE_FIGHT_TIMES_LIMIT":                  reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_FIGHT_TIMES_LIMIT),
		"RET_SEASON_DOOR_LINE_OIL_NOT_ENOUGH":                     reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_OIL_NOT_ENOUGH),
		"RET_SEASON_DOOR_LINE_OIL_USED":                           reflect.ValueOf(ret.RET_SEASON_DOOR_LINE_OIL_USED),
		"RET_SEASON_DUNGEON_TASK_PROGRESS_NOT_ENOUGH":             reflect.ValueOf(ret.RET_SEASON_DUNGEON_TASK_PROGRESS_NOT_ENOUGH),
		"RET_SEASON_JEWELRY_HERO_WEAR_RARE_ERROR":                 reflect.ValueOf(ret.RET_SEASON_JEWELRY_HERO_WEAR_RARE_ERROR),
		"RET_SEASON_JEWELRY_HERO_WEAR_TAG_ERROR":                  reflect.ValueOf(ret.RET_SEASON_JEWELRY_HERO_WEAR_TAG_ERROR),
		"RET_SEASON_JEWELRY_NOT_EXIST":                            reflect.ValueOf(ret.RET_SEASON_JEWELRY_NOT_EXIST),
		"RET_SEASON_JEWELRY_SKILL_NOT_EXIST":                      reflect.ValueOf(ret.RET_SEASON_JEWELRY_SKILL_NOT_EXIST),
		"RET_SEASON_LINK_INVALID_HEROID":                          reflect.ValueOf(ret.RET_SEASON_LINK_INVALID_HEROID),
		"RET_SEASON_LINK_INVALID_MONUMENT_ID":                     reflect.ValueOf(ret.RET_SEASON_LINK_INVALID_MONUMENT_ID),
		"RET_SEASON_LINK_LINK_ACTIVATED":                          reflect.ValueOf(ret.RET_SEASON_LINK_LINK_ACTIVATED),
		"RET_SEASON_LINK_MONUMENT_RARE_NOT_ENOUGH":                reflect.ValueOf(ret.RET_SEASON_LINK_MONUMENT_RARE_NOT_ENOUGH),
		"RET_SEASON_MAP_BUY_GOODS_NUM_LIMIT":                      reflect.ValueOf(ret.RET_SEASON_MAP_BUY_GOODS_NUM_LIMIT),
		"RET_SEASON_MAP_EVENT_NOT_EXIST":                          reflect.ValueOf(ret.RET_SEASON_MAP_EVENT_NOT_EXIST),
		"RET_SEASON_MAP_MONSTER_DEAD":                             reflect.ValueOf(ret.RET_SEASON_MAP_MONSTER_DEAD),
		"RET_SEASON_MAP_NO_BUFF":                                  reflect.ValueOf(ret.RET_SEASON_MAP_NO_BUFF),
		"RET_SEASON_MAP_POSITION_NOT_EXIST":                       reflect.ValueOf(ret.RET_SEASON_MAP_POSITION_NOT_EXIST),
		"RET_SEASON_MAP_PRICE_CHANGED":                            reflect.ValueOf(ret.RET_SEASON_MAP_PRICE_CHANGED),
		"RET_SEASON_MAP_TASK_PROGRESS_NOT_ENOUGH":                 reflect.ValueOf(ret.RET_SEASON_MAP_TASK_PROGRESS_NOT_ENOUGH),
		"RET_SEASON_RETURN_AWARD_ID_NOT_FOUND":                    reflect.ValueOf(ret.RET_SEASON_RETURN_AWARD_ID_NOT_FOUND),
		"RET_SEASON_RETURN_DUNGEON_NOT_ENOUGH":                    reflect.ValueOf(ret.RET_SEASON_RETURN_DUNGEON_NOT_ENOUGH),
		"RET_SEASON_RETURN_DUPLICATE_AWARD_ID":                    reflect.ValueOf(ret.RET_SEASON_RETURN_DUPLICATE_AWARD_ID),
		"RET_SEASON_SHOP_ACTIVITY_NOT_OPEN":                       reflect.ValueOf(ret.RET_SEASON_SHOP_ACTIVITY_NOT_OPEN),
		"RET_SELECT_ITEM_COUNT_MORE_THAN_ITEM_COUNT":              reflect.ValueOf(ret.RET_SELECT_ITEM_COUNT_MORE_THAN_ITEM_COUNT),
		"RET_SELECT_SUMMON_ID_INVALID":                            reflect.ValueOf(ret.RET_SELECT_SUMMON_ID_INVALID),
		"RET_SELECT_SUMMON_NOT_OPEN":                              reflect.ValueOf(ret.RET_SELECT_SUMMON_NOT_OPEN),
		"RET_SELECT_SUMMON_RAND_CLASS_FAILED":                     reflect.ValueOf(ret.RET_SELECT_SUMMON_RAND_CLASS_FAILED),
		"RET_SELECT_SUMMON_RAND_GROUP_FAILED":                     reflect.ValueOf(ret.RET_SELECT_SUMMON_RAND_GROUP_FAILED),
		"RET_SELECT_SUMMON_SHOP_ID_INVALID":                       reflect.ValueOf(ret.RET_SELECT_SUMMON_SHOP_ID_INVALID),
		"RET_SERVER_BUSY":                                         reflect.ValueOf(ret.RET_SERVER_BUSY),
		"RET_SERVER_CREATE_USER_CLOSED":                           reflect.ValueOf(ret.RET_SERVER_CREATE_USER_CLOSED),
		"RET_SERVER_ERROR":                                        reflect.ValueOf(ret.RET_SERVER_ERROR),
		"RET_SERVER_MAINTAIN":                                     reflect.ValueOf(ret.RET_SERVER_MAINTAIN),
		"RET_SET_TO_EARLIER_NOT_PERMITED":                         reflect.ValueOf(ret.RET_SET_TO_EARLIER_NOT_PERMITED),
		"RET_SHOP_EXT_TYPE_CARNIVAL_FAILED":                       reflect.ValueOf(ret.RET_SHOP_EXT_TYPE_CARNIVAL_FAILED),
		"RET_SHOP_NOT_EXIST":                                      reflect.ValueOf(ret.RET_SHOP_NOT_EXIST),
		"RET_SKIN_NOT_BELONG_HERO":                                reflect.ValueOf(ret.RET_SKIN_NOT_BELONG_HERO),
		"RET_SKIN_NOT_EXIST":                                      reflect.ValueOf(ret.RET_SKIN_NOT_EXIST),
		"RET_SKIN_NOT_FOREVER":                                    reflect.ValueOf(ret.RET_SKIN_NOT_FOREVER),
		"RET_SKIN_REPEAT_USE":                                     reflect.ValueOf(ret.RET_SKIN_REPEAT_USE),
		"RET_SPEED_ONHOOK_NUM_LIMIT":                              reflect.ValueOf(ret.RET_SPEED_ONHOOK_NUM_LIMIT),
		"RET_STORY_REVIEW_STORY_UNLOCKED":                         reflect.ValueOf(ret.RET_STORY_REVIEW_STORY_UNLOCKED),
		"RET_SUB_ITEM_NOT_BELONG_TO_ITEM":                         reflect.ValueOf(ret.RET_SUB_ITEM_NOT_BELONG_TO_ITEM),
		"RET_SUMMON_HERO_ERROR":                                   reflect.ValueOf(ret.RET_SUMMON_HERO_ERROR),
		"RET_SYSTEM_DATA_ERROR":                                   reflect.ValueOf(ret.RET_SYSTEM_DATA_ERROR),
		"RET_TALENT_TREE_PLAN_NUM_LIMIT":                          reflect.ValueOf(ret.RET_TALENT_TREE_PLAN_NUM_LIMIT),
		"RET_TALENT_TREE_RESET_GST_LIMIT":                         reflect.ValueOf(ret.RET_TALENT_TREE_RESET_GST_LIMIT),
		"RET_TARGET_UID_IS_OWN":                                   reflect.ValueOf(ret.RET_TARGET_UID_IS_OWN),
		"RET_TIME_TOO_SHORT_NO_AWARD":                             reflect.ValueOf(ret.RET_TIME_TOO_SHORT_NO_AWARD),
		"RET_TOWERSTAR_CHAPTER_NOT_UNLOCK":                        reflect.ValueOf(ret.RET_TOWERSTAR_CHAPTER_NOT_UNLOCK),
		"RET_TOWERSTAR_DAILY_NO_REWARD":                           reflect.ValueOf(ret.RET_TOWERSTAR_DAILY_NO_REWARD),
		"RET_TOWERSTAR_DUNGEON_ILLEGAL":                           reflect.ValueOf(ret.RET_TOWERSTAR_DUNGEON_ILLEGAL),
		"RET_TOWERSTAR_DUNGEON_NOT_UNLOCK":                        reflect.ValueOf(ret.RET_TOWERSTAR_DUNGEON_NOT_UNLOCK),
		"RET_TOWERSTAR_DUNGEON_STAR_FULL":                         reflect.ValueOf(ret.RET_TOWERSTAR_DUNGEON_STAR_FULL),
		"RET_TOWERSTAR_NO_TEAM":                                   reflect.ValueOf(ret.RET_TOWERSTAR_NO_TEAM),
		"RET_TOWERSTAR_REWARD_ILLEGAL":                            reflect.ValueOf(ret.RET_TOWERSTAR_REWARD_ILLEGAL),
		"RET_TOWER_JUMP_COUNT_MAX_LIMIT":                          reflect.ValueOf(ret.RET_TOWER_JUMP_COUNT_MAX_LIMIT),
		"RET_TOWER_JUMP_COUNT_MIN_LIMIT":                          reflect.ValueOf(ret.RET_TOWER_JUMP_COUNT_MIN_LIMIT),
		"RET_TOWER_JUMP_FLOOR_ILLEGAL":                            reflect.ValueOf(ret.RET_TOWER_JUMP_FLOOR_ILLEGAL),
		"RET_TOWER_JUMP_MULTI_LIMIT":                              reflect.ValueOf(ret.RET_TOWER_JUMP_MULTI_LIMIT),
		"RET_TOWER_JUMP_POWER_NOT_ENOUGH":                         reflect.ValueOf(ret.RET_TOWER_JUMP_POWER_NOT_ENOUGH),
		"RET_TOWER_JUMP_START_FLOOR_LIMIT":                        reflect.ValueOf(ret.RET_TOWER_JUMP_START_FLOOR_LIMIT),
		"RET_TOWER_NOT_EXIST":                                     reflect.ValueOf(ret.RET_TOWER_NOT_EXIST),
		"RET_TOWER_SEASON_TASK_PROGRESS_NOT_ENOUGH":               reflect.ValueOf(ret.RET_TOWER_SEASON_TASK_PROGRESS_NOT_ENOUGH),
		"RET_TOWER_SWEEP_COUNT_NOT_ENOUGH":                        reflect.ValueOf(ret.RET_TOWER_SWEEP_COUNT_NOT_ENOUGH),
		"RET_TOWER_TYPE_NOT_EXIST":                                reflect.ValueOf(ret.RET_TOWER_TYPE_NOT_EXIST),
		"RET_TRAIL_TYPE_NOT_INIT":                                 reflect.ValueOf(ret.RET_TRAIL_TYPE_NOT_INIT),
		"RET_TRAIL_TYPE_NOT_OPEN":                                 reflect.ValueOf(ret.RET_TRAIL_TYPE_NOT_OPEN),
		"RET_USER_ACCOUNT_EXISTS":                                 reflect.ValueOf(ret.RET_USER_ACCOUNT_EXISTS),
		"RET_USER_ID_NOT_VALID":                                   reflect.ValueOf(ret.RET_USER_ID_NOT_VALID),
		"RET_USER_NAME_ILLEGAL":                                   reflect.ValueOf(ret.RET_USER_NAME_ILLEGAL),
		"RET_USER_NAME_ILLEGAL_CHARACTER":                         reflect.ValueOf(ret.RET_USER_NAME_ILLEGAL_CHARACTER),
		"RET_USER_NAME_IS_EMPTY":                                  reflect.ValueOf(ret.RET_USER_NAME_IS_EMPTY),
		"RET_USER_NAME_LENGTH_LIMIT":                              reflect.ValueOf(ret.RET_USER_NAME_LENGTH_LIMIT),
		"RET_USER_NAME_PURELY_NUMERICAL":                          reflect.ValueOf(ret.RET_USER_NAME_PURELY_NUMERICAL),
		"RET_USER_NAME_REPEAT":                                    reflect.ValueOf(ret.RET_USER_NAME_REPEAT),
		"RET_USER_NOT_EXIST":                                      reflect.ValueOf(ret.RET_USER_NOT_EXIST),
		"RET_USER_NOT_FOUND":                                      reflect.ValueOf(ret.RET_USER_NOT_FOUND),
		"RET_USER_SAME_OPERATE":                                   reflect.ValueOf(ret.RET_USER_SAME_OPERATE),
		"RET_USER_SET_NAME_REPEATED":                              reflect.ValueOf(ret.RET_USER_SET_NAME_REPEATED),
		"RET_USER_WRONG":                                          reflect.ValueOf(ret.RET_USER_WRONG),
		"RET_USE_ITEM_COUNT_MORE_THAN_MAX_LIMIT":                  reflect.ValueOf(ret.RET_USE_ITEM_COUNT_MORE_THAN_MAX_LIMIT),
		"RET_VIP_LEVEL_NOT_ENOUGH":                                reflect.ValueOf(ret.RET_VIP_LEVEL_NOT_ENOUGH),
		"RET_WORD_CHECK_SENSITIVE":                                reflect.ValueOf(ret.RET_WORD_CHECK_SENSITIVE),
		"RET_WORLD_BOSS_ALREADY_MATCH_ROOM":                       reflect.ValueOf(ret.RET_WORLD_BOSS_ALREADY_MATCH_ROOM),
		"RET_WORLD_BOSS_CROSS_AGAIN_MATCH_ROOM":                   reflect.ValueOf(ret.RET_WORLD_BOSS_CROSS_AGAIN_MATCH_ROOM),
		"RET_WORLD_BOSS_CROSS_GET_RANK_FAILED":                    reflect.ValueOf(ret.RET_WORLD_BOSS_CROSS_GET_RANK_FAILED),
		"RET_WORLD_BOSS_CROSS_GET_ROOM_LOG_FAILED":                reflect.ValueOf(ret.RET_WORLD_BOSS_CROSS_GET_ROOM_LOG_FAILED),
		"RET_WORLD_BOSS_CROSS_INIT_DATA_FAILED":                   reflect.ValueOf(ret.RET_WORLD_BOSS_CROSS_INIT_DATA_FAILED),
		"RET_WORLD_BOSS_CROSS_NOT_EXIST_SETTLE_DATA":              reflect.ValueOf(ret.RET_WORLD_BOSS_CROSS_NOT_EXIST_SETTLE_DATA),
		"RET_WORLD_BOSS_HANDBOOK_SCORE_INVALID":                   reflect.ValueOf(ret.RET_WORLD_BOSS_HANDBOOK_SCORE_INVALID),
		"RET_WORLD_BOSS_LEVEL_INVALID":                            reflect.ValueOf(ret.RET_WORLD_BOSS_LEVEL_INVALID),
		"RET_WORLD_BOSS_MAX_HURT_IS_ZERO":                         reflect.ValueOf(ret.RET_WORLD_BOSS_MAX_HURT_IS_ZERO),
		"RET_WORLD_BOSS_NOT_DISPLAY_PERIOD":                       reflect.ValueOf(ret.RET_WORLD_BOSS_NOT_DISPLAY_PERIOD),
		"RET_WORLD_BOSS_NOT_MATCH_ROOM":                           reflect.ValueOf(ret.RET_WORLD_BOSS_NOT_MATCH_ROOM),
		"RET_WORLD_BOSS_NOT_OPEN":                                 reflect.ValueOf(ret.RET_WORLD_BOSS_NOT_OPEN),
		"RET_WORLD_BOSS_TASK_AWARD_ALREADY_RECEIVED":              reflect.ValueOf(ret.RET_WORLD_BOSS_TASK_AWARD_ALREADY_RECEIVED),
		"RET_WORLD_BOSS_TASK_NOT_FINISH":                          reflect.ValueOf(ret.RET_WORLD_BOSS_TASK_NOT_FINISH),
		"RET_WORLD_BOSS_WORSHIP_DROP_AWARD_NOT_EXIST":             reflect.ValueOf(ret.RET_WORLD_BOSS_WORSHIP_DROP_AWARD_NOT_EXIST),
		"RET_WRESTLE_CHANGE_ROOM_CD":                              reflect.ValueOf(ret.RET_WRESTLE_CHANGE_ROOM_CD),
		"RET_WRESTLE_FAIL_CD_NOT_EXPIRE":                          reflect.ValueOf(ret.RET_WRESTLE_FAIL_CD_NOT_EXPIRE),
		"RET_WRESTLE_NOT_OPEN":                                    reflect.ValueOf(ret.RET_WRESTLE_NOT_OPEN),
		"RET_WRESTLE_RESETTING":                                   reflect.ValueOf(ret.RET_WRESTLE_RESETTING),
		"RET_WRESTLE_SELF_IS_ATTACKING":                           reflect.ValueOf(ret.RET_WRESTLE_SELF_IS_ATTACKING),
		"RET_name":                                                reflect.ValueOf(&ret.RET_name).Elem(),
		"RET_value":                                               reflect.ValueOf(&ret.RET_value).Elem(),

		// type definitions
		"RET": reflect.ValueOf((*ret.RET)(nil)),
	}
}
