// Code generated by 'yaegi extract app/gmxml'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/gmxml"
	"go/constant"
	"go/token"
	"reflect"
)

func init() {
	Symbols["app/gmxml/gmxml"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ActivityCouponInfoM":                    reflect.ValueOf(&gmxml.ActivityCouponInfoM).Elem(),
		"AnnouncementInfoM":                      reflect.ValueOf(&gmxml.AnnouncementInfoM).Elem(),
		"AnnouncementOpTypeDelete":               reflect.ValueOf(gmxml.AnnouncementOpTypeDelete),
		"AnnouncementOpTypeUpdate":               reflect.ValueOf(gmxml.AnnouncementOpTypeUpdate),
		"ArtifactDebutDeleteDelay":               reflect.ValueOf(constant.MakeFromLiteral("2592000", token.INT, 0)),
		"ArtifactDebutInfoM":                     reflect.ValueOf(&gmxml.ArtifactDebutInfoM).Elem(),
		"CouponActivityDeleteDelay":              reflect.ValueOf(constant.MakeFromLiteral("2592000", token.INT, 0)),
		"CouponActivityFlushLimit":               reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"DailyWishActivityInfoM":                 reflect.ValueOf(&gmxml.DailyWishActivityInfoM).Elem(),
		"DailyWishActivityNone":                  reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"DailyWishActivityOffline":               reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"DailyWishActivityOnline":                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DailyWishAwardInfoM":                    reflect.ValueOf(&gmxml.DailyWishAwardInfoM).Elem(),
		"DeleteQuestionnaireDelay":               reflect.ValueOf(constant.MakeFromLiteral("1209600", token.INT, 0)),
		"DeleteUint32Slice":                      reflect.ValueOf(gmxml.DeleteUint32Slice),
		"DivineDemonInfoM":                       reflect.ValueOf(&gmxml.DivineDemonInfoM).Elem(),
		"DivineDemonStatusOffline":               reflect.ValueOf(gmxml.DivineDemonStatusOffline),
		"DivineDemonStatusOnline":                reflect.ValueOf(gmxml.DivineDemonStatusOnline),
		"DropActivityDeleteDelay":                reflect.ValueOf(constant.MakeFromLiteral("2592000", token.INT, 0)),
		"DropActivityFlushLimit":                 reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"DropActivityInfoM":                      reflect.ValueOf(&gmxml.DropActivityInfoM).Elem(),
		"FileModTime":                            reflect.ValueOf(gmxml.FileModTime),
		"GiftCodeInfoM":                          reflect.ValueOf(&gmxml.GiftCodeInfoM).Elem(),
		"GmConfigClose":                          reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GmConfigInfoM":                          reflect.ValueOf(&gmxml.GmConfigInfoM).Elem(),
		"GmConfigOpen":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GmConfigRate":                           reflect.ValueOf(gmxml.GmConfigRate),
		"IsIntersected":                          reflect.ValueOf(gmxml.IsIntersected),
		"Load":                                   reflect.ValueOf(gmxml.Load),
		"OpStatusOff":                            reflect.ValueOf(gmxml.OpStatusOff),
		"OpStatusRelease":                        reflect.ValueOf(gmxml.OpStatusRelease),
		"OperateActivityDeleteCountLimit":        reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"OperateActivityDeleteDay":               reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"OperateActivityDeleteDelay":             reflect.ValueOf(constant.MakeFromLiteral("1209600", token.INT, 0)),
		"OperateActivityGift":                    reflect.ValueOf(gmxml.OperateActivityGift),
		"OperateActivityInfoM":                   reflect.ValueOf(&gmxml.OperateActivityInfoM).Elem(),
		"OperateActivityTask":                    reflect.ValueOf(gmxml.OperateActivityTask),
		"OperateActivityTaskNormal":              reflect.ValueOf(gmxml.OperateActivityTaskNormal),
		"OperateActivityTaskRound":               reflect.ValueOf(gmxml.OperateActivityTaskRound),
		"OperateGiftInfoM":                       reflect.ValueOf(&gmxml.OperateGiftInfoM).Elem(),
		"OperateTaskEventRecordFromActivityOpen": reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"OperateTaskEventRecordFromServiceOpen":  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"OperateTaskEventRecordNone":             reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"OperateTaskInfoM":                       reflect.ValueOf(&gmxml.OperateTaskInfoM).Elem(),
		"PromotionGiftInfoM":                     reflect.ValueOf(&gmxml.PromotionGiftInfoM).Elem(),
		"PromotionGiftPick":                      reflect.ValueOf(gmxml.PromotionGiftPick),
		"PromotionGiftProgression":               reflect.ValueOf(gmxml.PromotionGiftProgression),
		"PromotionGiftRebate":                    reflect.ValueOf(gmxml.PromotionGiftRebate),
		"PyramidActivityDeleteDelay":             reflect.ValueOf(constant.MakeFromLiteral("2592000", token.INT, 0)),
		"PyramidActivityFlushLimit":              reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"PyramidActivityInfoM":                   reflect.ValueOf(&gmxml.PyramidActivityInfoM).Elem(),
		"QuestionnaireInfoM":                     reflect.ValueOf(&gmxml.QuestionnaireInfoM).Elem(),
		"SelectSummonInfoM":                      reflect.ValueOf(&gmxml.SelectSummonInfoM).Elem(),
		"SelectSummonStatusOffline":              reflect.ValueOf(gmxml.SelectSummonStatusOffline),
		"SelectSummonStatusOnline":               reflect.ValueOf(gmxml.SelectSummonStatusOnline),

		// type definitions
		"ActivityCouponInfo":           reflect.ValueOf((*gmxml.ActivityCouponInfo)(nil)),
		"ActivityCouponInfoExt":        reflect.ValueOf((*gmxml.ActivityCouponInfoExt)(nil)),
		"ActivityCouponInfoManager":    reflect.ValueOf((*gmxml.ActivityCouponInfoManager)(nil)),
		"ActivityCouponInfos":          reflect.ValueOf((*gmxml.ActivityCouponInfos)(nil)),
		"ActivityTime":                 reflect.ValueOf((*gmxml.ActivityTime)(nil)),
		"AnnouncementCache":            reflect.ValueOf((*gmxml.AnnouncementCache)(nil)),
		"AnnouncementContext":          reflect.ValueOf((*gmxml.AnnouncementContext)(nil)),
		"AnnouncementInfo":             reflect.ValueOf((*gmxml.AnnouncementInfo)(nil)),
		"AnnouncementInfoManager":      reflect.ValueOf((*gmxml.AnnouncementInfoManager)(nil)),
		"AnnouncementInfos":            reflect.ValueOf((*gmxml.AnnouncementInfos)(nil)),
		"ArtifactDebutInfo":            reflect.ValueOf((*gmxml.ArtifactDebutInfo)(nil)),
		"ArtifactDebutInfoManager":     reflect.ValueOf((*gmxml.ArtifactDebutInfoManager)(nil)),
		"ArtifactDebutInfos":           reflect.ValueOf((*gmxml.ArtifactDebutInfos)(nil)),
		"CheckQuestionnaireMsg":        reflect.ValueOf((*gmxml.CheckQuestionnaireMsg)(nil)),
		"ContentCache":                 reflect.ValueOf((*gmxml.ContentCache)(nil)),
		"DailyWishActivityInfo":        reflect.ValueOf((*gmxml.DailyWishActivityInfo)(nil)),
		"DailyWishActivityInfoManager": reflect.ValueOf((*gmxml.DailyWishActivityInfoManager)(nil)),
		"DailyWishActivityInfos":       reflect.ValueOf((*gmxml.DailyWishActivityInfos)(nil)),
		"DailyWishAwardInfo":           reflect.ValueOf((*gmxml.DailyWishAwardInfo)(nil)),
		"DailyWishAwardInfoManager":    reflect.ValueOf((*gmxml.DailyWishAwardInfoManager)(nil)),
		"DailyWishAwardInfos":          reflect.ValueOf((*gmxml.DailyWishAwardInfos)(nil)),
		"DivineDemonInfo":              reflect.ValueOf((*gmxml.DivineDemonInfo)(nil)),
		"DivineDemonInfoManager":       reflect.ValueOf((*gmxml.DivineDemonInfoManager)(nil)),
		"DivineDemonInfos":             reflect.ValueOf((*gmxml.DivineDemonInfos)(nil)),
		"DropActivityInfo":             reflect.ValueOf((*gmxml.DropActivityInfo)(nil)),
		"DropActivityInfoExt":          reflect.ValueOf((*gmxml.DropActivityInfoExt)(nil)),
		"DropActivityInfoManager":      reflect.ValueOf((*gmxml.DropActivityInfoManager)(nil)),
		"DropActivityInfos":            reflect.ValueOf((*gmxml.DropActivityInfos)(nil)),
		"FlushCondition":               reflect.ValueOf((*gmxml.FlushCondition)(nil)),
		"GiftCodeInfo":                 reflect.ValueOf((*gmxml.GiftCodeInfo)(nil)),
		"GiftCodeInfoManager":          reflect.ValueOf((*gmxml.GiftCodeInfoManager)(nil)),
		"GiftCodeInfos":                reflect.ValueOf((*gmxml.GiftCodeInfos)(nil)),
		"GmConfigID":                   reflect.ValueOf((*gmxml.GmConfigID)(nil)),
		"GmConfigInfo":                 reflect.ValueOf((*gmxml.GmConfigInfo)(nil)),
		"GmConfigInfoManager":          reflect.ValueOf((*gmxml.GmConfigInfoManager)(nil)),
		"GmConfigInfos":                reflect.ValueOf((*gmxml.GmConfigInfos)(nil)),
		"OperateActivityInfo":          reflect.ValueOf((*gmxml.OperateActivityInfo)(nil)),
		"OperateActivityInfoManager":   reflect.ValueOf((*gmxml.OperateActivityInfoManager)(nil)),
		"OperateActivityInfos":         reflect.ValueOf((*gmxml.OperateActivityInfos)(nil)),
		"OperateGiftInfo":              reflect.ValueOf((*gmxml.OperateGiftInfo)(nil)),
		"OperateGiftInfoManager":       reflect.ValueOf((*gmxml.OperateGiftInfoManager)(nil)),
		"OperateGiftInfos":             reflect.ValueOf((*gmxml.OperateGiftInfos)(nil)),
		"OperateTaskInfo":              reflect.ValueOf((*gmxml.OperateTaskInfo)(nil)),
		"OperateTaskInfoManager":       reflect.ValueOf((*gmxml.OperateTaskInfoManager)(nil)),
		"OperateTaskInfos":             reflect.ValueOf((*gmxml.OperateTaskInfos)(nil)),
		"PromotionGiftInfo":            reflect.ValueOf((*gmxml.PromotionGiftInfo)(nil)),
		"PromotionGiftInfoManager":     reflect.ValueOf((*gmxml.PromotionGiftInfoManager)(nil)),
		"PromotionGiftInfos":           reflect.ValueOf((*gmxml.PromotionGiftInfos)(nil)),
		"PyramidActivityInfo":          reflect.ValueOf((*gmxml.PyramidActivityInfo)(nil)),
		"PyramidActivityInfoExt":       reflect.ValueOf((*gmxml.PyramidActivityInfoExt)(nil)),
		"PyramidActivityInfoManager":   reflect.ValueOf((*gmxml.PyramidActivityInfoManager)(nil)),
		"PyramidActivityInfos":         reflect.ValueOf((*gmxml.PyramidActivityInfos)(nil)),
		"QuestionnaireContent":         reflect.ValueOf((*gmxml.QuestionnaireContent)(nil)),
		"QuestionnaireInfo":            reflect.ValueOf((*gmxml.QuestionnaireInfo)(nil)),
		"QuestionnaireInfoManager":     reflect.ValueOf((*gmxml.QuestionnaireInfoManager)(nil)),
		"QuestionnaireInfos":           reflect.ValueOf((*gmxml.QuestionnaireInfos)(nil)),
		"ReleaseStatus":                reflect.ValueOf((*gmxml.ReleaseStatus)(nil)),
		"SelectSummonInfo":             reflect.ValueOf((*gmxml.SelectSummonInfo)(nil)),
		"SelectSummonInfoManager":      reflect.ValueOf((*gmxml.SelectSummonInfoManager)(nil)),
		"SelectSummonInfos":            reflect.ValueOf((*gmxml.SelectSummonInfos)(nil)),
		"SelectSummonReleaseStatus":    reflect.ValueOf((*gmxml.SelectSummonReleaseStatus)(nil)),
	}
}
