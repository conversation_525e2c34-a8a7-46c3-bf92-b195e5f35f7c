// Code generated by 'yaegi extract app/protos/in/gm'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/protos/in/db"
	"app/protos/in/gm"
	"app/protos/out/cl"
	"context"
	"google.golang.org/grpc"
	"reflect"
)

func init() {
	Symbols["app/protos/in/gm/gm"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ARTIFACT_DEBUT_AD_MIN_ID":            reflect.ValueOf(gm.ARTIFACT_DEBUT_AD_MIN_ID),
		"ARTIFACT_DEBUT_AD_NONE":              reflect.ValueOf(gm.ARTIFACT_DEBUT_AD_NONE),
		"ARTIFACT_DEBUT_name":                 reflect.ValueOf(&gm.ARTIFACT_DEBUT_name).Elem(),
		"ARTIFACT_DEBUT_value":                reflect.ValueOf(&gm.ARTIFACT_DEBUT_value).Elem(),
		"ErrIntOverflowGm":                    reflect.ValueOf(&gm.ErrIntOverflowGm).Elem(),
		"ErrInvalidLengthGm":                  reflect.ValueOf(&gm.ErrInvalidLengthGm).Elem(),
		"ErrUnexpectedEndOfGroupGm":           reflect.ValueOf(&gm.ErrUnexpectedEndOfGroupGm).Elem(),
		"ID_Fn_ActivityInfo":                  reflect.ValueOf(gm.ID_Fn_ActivityInfo),
		"ID_Fn_AddGuildMobScore":              reflect.ValueOf(gm.ID_Fn_AddGuildMobScore),
		"ID_Fn_BanAccount":                    reflect.ValueOf(gm.ID_Fn_BanAccount),
		"ID_Fn_BanProtocol":                   reflect.ValueOf(gm.ID_Fn_BanProtocol),
		"ID_Fn_ChangeBags":                    reflect.ValueOf(gm.ID_Fn_ChangeBags),
		"ID_Fn_ChangeUserName":                reflect.ValueOf(gm.ID_Fn_ChangeUserName),
		"ID_Fn_CheckDropActivity":             reflect.ValueOf(gm.ID_Fn_CheckDropActivity),
		"ID_Fn_ClearUserResource":             reflect.ValueOf(gm.ID_Fn_ClearUserResource),
		"ID_Fn_CloseGuidance":                 reflect.ValueOf(gm.ID_Fn_CloseGuidance),
		"ID_Fn_DelChatGroupTag":               reflect.ValueOf(gm.ID_Fn_DelChatGroupTag),
		"ID_Fn_DeleteCurrenciesReq":           reflect.ValueOf(gm.ID_Fn_DeleteCurrenciesReq),
		"ID_Fn_DeleteHero":                    reflect.ValueOf(gm.ID_Fn_DeleteHero),
		"ID_Fn_DeleteServerMail":              reflect.ValueOf(gm.ID_Fn_DeleteServerMail),
		"ID_Fn_DeleteUserMail":                reflect.ValueOf(gm.ID_Fn_DeleteUserMail),
		"ID_Fn_GetActPartition":               reflect.ValueOf(gm.ID_Fn_GetActPartition),
		"ID_Fn_GetActPartitionRunInfo":        reflect.ValueOf(gm.ID_Fn_GetActPartitionRunInfo),
		"ID_Fn_GetRankInfo":                   reflect.ValueOf(gm.ID_Fn_GetRankInfo),
		"ID_Fn_GetRechargeList":               reflect.ValueOf(gm.ID_Fn_GetRechargeList),
		"ID_Fn_GetUser":                       reflect.ValueOf(gm.ID_Fn_GetUser),
		"ID_Fn_GetUserMailList":               reflect.ValueOf(gm.ID_Fn_GetUserMailList),
		"ID_Fn_GetVersion":                    reflect.ValueOf(gm.ID_Fn_GetVersion),
		"ID_Fn_GmDailyAttendanceHero":         reflect.ValueOf(gm.ID_Fn_GmDailyAttendanceHero),
		"ID_Fn_GmDivineDemonBench":            reflect.ValueOf(gm.ID_Fn_GmDivineDemonBench),
		"ID_Fn_GmGSTTeamMove":                 reflect.ValueOf(gm.ID_Fn_GmGSTTeamMove),
		"ID_Fn_GmGetGuildInfo":                reflect.ValueOf(gm.ID_Fn_GmGetGuildInfo),
		"ID_Fn_GmGetGuildList":                reflect.ValueOf(gm.ID_Fn_GmGetGuildList),
		"ID_Fn_GmGuildChangeLeader":           reflect.ValueOf(gm.ID_Fn_GmGuildChangeLeader),
		"ID_Fn_GmGuildChangeName":             reflect.ValueOf(gm.ID_Fn_GmGuildChangeName),
		"ID_Fn_GmGuildChangeNotice":           reflect.ValueOf(gm.ID_Fn_GmGuildChangeNotice),
		"ID_Fn_GmGuildDeleteMail":             reflect.ValueOf(gm.ID_Fn_GmGuildDeleteMail),
		"ID_Fn_GmGuildDisband":                reflect.ValueOf(gm.ID_Fn_GmGuildDisband),
		"ID_Fn_GmGuildKickMember":             reflect.ValueOf(gm.ID_Fn_GmGuildKickMember),
		"ID_Fn_GmSetSeasonArenaScore":         reflect.ValueOf(gm.ID_Fn_GmSetSeasonArenaScore),
		"ID_Fn_GuildNoticeManage":             reflect.ValueOf(gm.ID_Fn_GuildNoticeManage),
		"ID_Fn_Hotfix":                        reflect.ValueOf(gm.ID_Fn_Hotfix),
		"ID_Fn_ImportUser":                    reflect.ValueOf(gm.ID_Fn_ImportUser),
		"ID_Fn_KickAccount":                   reflect.ValueOf(gm.ID_Fn_KickAccount),
		"ID_Fn_MAX":                           reflect.ValueOf(gm.ID_Fn_MAX),
		"ID_Fn_Min":                           reflect.ValueOf(gm.ID_Fn_Min),
		"ID_Fn_NewQuestionnaireFinish":        reflect.ValueOf(gm.ID_Fn_NewQuestionnaireFinish),
		"ID_Fn_NotifyRecharge":                reflect.ValueOf(gm.ID_Fn_NotifyRecharge),
		"ID_Fn_NotifyRefund":                  reflect.ValueOf(gm.ID_Fn_NotifyRefund),
		"ID_Fn_OnlineNum":                     reflect.ValueOf(gm.ID_Fn_OnlineNum),
		"ID_Fn_QueryServerTime":               reflect.ValueOf(gm.ID_Fn_QueryServerTime),
		"ID_Fn_QuestionnaireFinish":           reflect.ValueOf(gm.ID_Fn_QuestionnaireFinish),
		"ID_Fn_RecoveryGuildDungeonResetTime": reflect.ValueOf(gm.ID_Fn_RecoveryGuildDungeonResetTime),
		"ID_Fn_ReduceResources":               reflect.ValueOf(gm.ID_Fn_ReduceResources),
		"ID_Fn_ReleaseOperateActivity":        reflect.ValueOf(gm.ID_Fn_ReleaseOperateActivity),
		"ID_Fn_ReleaseOperatePageInfo":        reflect.ValueOf(gm.ID_Fn_ReleaseOperatePageInfo),
		"ID_Fn_Remain":                        reflect.ValueOf(gm.ID_Fn_Remain),
		"ID_Fn_ResetArea":                     reflect.ValueOf(gm.ID_Fn_ResetArea),
		"ID_Fn_ResetArenaScore":               reflect.ValueOf(gm.ID_Fn_ResetArenaScore),
		"ID_Fn_ResetDailyNum":                 reflect.ValueOf(gm.ID_Fn_ResetDailyNum),
		"ID_Fn_ResetGuildQuitTm":              reflect.ValueOf(gm.ID_Fn_ResetGuildQuitTm),
		"ID_Fn_ResetMaze":                     reflect.ValueOf(gm.ID_Fn_ResetMaze),
		"ID_Fn_ResetMirageFloor":              reflect.ValueOf(gm.ID_Fn_ResetMirageFloor),
		"ID_Fn_ResetScore":                    reflect.ValueOf(gm.ID_Fn_ResetScore),
		"ID_Fn_ResetTaleChapterFinish":        reflect.ValueOf(gm.ID_Fn_ResetTaleChapterFinish),
		"ID_Fn_ResetTaleElite":                reflect.ValueOf(gm.ID_Fn_ResetTaleElite),
		"ID_Fn_ResetTowerFloor":               reflect.ValueOf(gm.ID_Fn_ResetTowerFloor),
		"ID_Fn_ResetTowerstar":                reflect.ValueOf(gm.ID_Fn_ResetTowerstar),
		"ID_Fn_ResetTrialLevel":               reflect.ValueOf(gm.ID_Fn_ResetTrialLevel),
		"ID_Fn_SendGiftCodeInfo":              reflect.ValueOf(gm.ID_Fn_SendGiftCodeInfo),
		"ID_Fn_SendPGPMail":                   reflect.ValueOf(gm.ID_Fn_SendPGPMail),
		"ID_Fn_SendQuestionnaire":             reflect.ValueOf(gm.ID_Fn_SendQuestionnaire),
		"ID_Fn_SendServerMail":                reflect.ValueOf(gm.ID_Fn_SendServerMail),
		"ID_Fn_SendTopResource":               reflect.ValueOf(gm.ID_Fn_SendTopResource),
		"ID_Fn_SendUserMail":                  reflect.ValueOf(gm.ID_Fn_SendUserMail),
		"ID_Fn_SetAccountTag":                 reflect.ValueOf(gm.ID_Fn_SetAccountTag),
		"ID_Fn_SetActPartition":               reflect.ValueOf(gm.ID_Fn_SetActPartition),
		"ID_Fn_SetAllPushGift":                reflect.ValueOf(gm.ID_Fn_SetAllPushGift),
		"ID_Fn_SetBattleHeroStar":             reflect.ValueOf(gm.ID_Fn_SetBattleHeroStar),
		"ID_Fn_SetBossRushLevel":              reflect.ValueOf(gm.ID_Fn_SetBossRushLevel),
		"ID_Fn_SetDailyAttendance":            reflect.ValueOf(gm.ID_Fn_SetDailyAttendance),
		"ID_Fn_SetDisorderLand":               reflect.ValueOf(gm.ID_Fn_SetDisorderLand),
		"ID_Fn_SetDispatchLevel":              reflect.ValueOf(gm.ID_Fn_SetDispatchLevel),
		"ID_Fn_SetDungeonID":                  reflect.ValueOf(gm.ID_Fn_SetDungeonID),
		"ID_Fn_SetFlowerLevel":                reflect.ValueOf(gm.ID_Fn_SetFlowerLevel),
		"ID_Fn_SetForestLevel":                reflect.ValueOf(gm.ID_Fn_SetForestLevel),
		"ID_Fn_SetGmConfig":                   reflect.ValueOf(gm.ID_Fn_SetGmConfig),
		"ID_Fn_SetGuidanceClose":              reflect.ValueOf(gm.ID_Fn_SetGuidanceClose),
		"ID_Fn_SetGuildDungeonCurrentChapter": reflect.ValueOf(gm.ID_Fn_SetGuildDungeonCurrentChapter),
		"ID_Fn_SetGuildLevel":                 reflect.ValueOf(gm.ID_Fn_SetGuildLevel),
		"ID_Fn_SetHeroAwakenLevel":            reflect.ValueOf(gm.ID_Fn_SetHeroAwakenLevel),
		"ID_Fn_SetMazeTaskLevel":              reflect.ValueOf(gm.ID_Fn_SetMazeTaskLevel),
		"ID_Fn_SetMedalLevel":                 reflect.ValueOf(gm.ID_Fn_SetMedalLevel),
		"ID_Fn_SetRiteRare":                   reflect.ValueOf(gm.ID_Fn_SetRiteRare),
		"ID_Fn_SetSeasonLevel":                reflect.ValueOf(gm.ID_Fn_SetSeasonLevel),
		"ID_Fn_SetSeasonLink":                 reflect.ValueOf(gm.ID_Fn_SetSeasonLink),
		"ID_Fn_SetSeasonLinkMonuments":        reflect.ValueOf(gm.ID_Fn_SetSeasonLinkMonuments),
		"ID_Fn_SetServerTime":                 reflect.ValueOf(gm.ID_Fn_SetServerTime),
		"ID_Fn_SetSinglePushGift":             reflect.ValueOf(gm.ID_Fn_SetSinglePushGift),
		"ID_Fn_SetTalentTree":                 reflect.ValueOf(gm.ID_Fn_SetTalentTree),
		"ID_Fn_SetTaskFinish":                 reflect.ValueOf(gm.ID_Fn_SetTaskFinish),
		"ID_Fn_SetTowerSeasonFloor":           reflect.ValueOf(gm.ID_Fn_SetTowerSeasonFloor),
		"ID_Fn_SetTowerstarDungeonID":         reflect.ValueOf(gm.ID_Fn_SetTowerstarDungeonID),
		"ID_Fn_SetUserLevel":                  reflect.ValueOf(gm.ID_Fn_SetUserLevel),
		"ID_Fn_SetUserResource":               reflect.ValueOf(gm.ID_Fn_SetUserResource),
		"ID_Fn_SetUserVip":                    reflect.ValueOf(gm.ID_Fn_SetUserVip),
		"ID_Fn_SetWrestleLevel":               reflect.ValueOf(gm.ID_Fn_SetWrestleLevel),
		"ID_Fn_StartArea":                     reflect.ValueOf(gm.ID_Fn_StartArea),
		"ID_Fn_StopArea":                      reflect.ValueOf(gm.ID_Fn_StopArea),
		"ID_Fn_ToggleUserLogTrace":            reflect.ValueOf(gm.ID_Fn_ToggleUserLogTrace),
		"ID_Fn_UpdateAnnouncement":            reflect.ValueOf(gm.ID_Fn_UpdateAnnouncement),
		"ID_Fn_UpdateArtifactDebut":           reflect.ValueOf(gm.ID_Fn_UpdateArtifactDebut),
		"ID_Fn_UpdateCouponActivity":          reflect.ValueOf(gm.ID_Fn_UpdateCouponActivity),
		"ID_Fn_UpdateDailyWishActivity":       reflect.ValueOf(gm.ID_Fn_UpdateDailyWishActivity),
		"ID_Fn_UpdateDivineDemon":             reflect.ValueOf(gm.ID_Fn_UpdateDivineDemon),
		"ID_Fn_UpdateDropActivity":            reflect.ValueOf(gm.ID_Fn_UpdateDropActivity),
		"ID_Fn_UpdateMultiLangs":              reflect.ValueOf(gm.ID_Fn_UpdateMultiLangs),
		"ID_Fn_UpdatePeopleGroupPackage":      reflect.ValueOf(gm.ID_Fn_UpdatePeopleGroupPackage),
		"ID_Fn_UpdatePyramidActivity":         reflect.ValueOf(gm.ID_Fn_UpdatePyramidActivity),
		"ID_Fn_UpdateSelectSummon":            reflect.ValueOf(gm.ID_Fn_UpdateSelectSummon),
		"ID_name":                             reflect.ValueOf(&gm.ID_name).Elem(),
		"ID_value":                            reflect.ValueOf(&gm.ID_value).Elem(),
		"NewCrossClient":                      reflect.ValueOf(gm.NewCrossClient),
		"NewCrossMasterClient":                reflect.ValueOf(gm.NewCrossMasterClient),
		"NewGmTestClient":                     reflect.ValueOf(gm.NewGmTestClient),
		"NewLogicClient":                      reflect.ValueOf(gm.NewLogicClient),
		"RegisterCrossMasterServer":           reflect.ValueOf(gm.RegisterCrossMasterServer),
		"RegisterCrossServer":                 reflect.ValueOf(gm.RegisterCrossServer),
		"RegisterGmTestServer":                reflect.ValueOf(gm.RegisterGmTestServer),
		"RegisterLogicServer":                 reflect.ValueOf(gm.RegisterLogicServer),
		"SEARCH_BY_ID":                        reflect.ValueOf(gm.SEARCH_BY_ID),
		"SEARCH_BY_NAME":                      reflect.ValueOf(gm.SEARCH_BY_NAME),
		"SEARCH_BY_NONE":                      reflect.ValueOf(gm.SEARCH_BY_NONE),
		"SEARCH_BY_UUID":                      reflect.ValueOf(gm.SEARCH_BY_UUID),
		"SEARCH_name":                         reflect.ValueOf(&gm.SEARCH_name).Elem(),
		"SEARCH_value":                        reflect.ValueOf(&gm.SEARCH_value).Elem(),
		"SET_DUNGEON_TYPE_SDT_ACTIVITY_STORY": reflect.ValueOf(gm.SET_DUNGEON_TYPE_SDT_ACTIVITY_STORY),
		"SET_DUNGEON_TYPE_SDT_NONE":           reflect.ValueOf(gm.SET_DUNGEON_TYPE_SDT_NONE),
		"SET_DUNGEON_TYPE_SDT_SEASON_DUNGEON": reflect.ValueOf(gm.SET_DUNGEON_TYPE_SDT_SEASON_DUNGEON),
		"SET_DUNGEON_TYPE_SDT_TOWER_SEASON":   reflect.ValueOf(gm.SET_DUNGEON_TYPE_SDT_TOWER_SEASON),
		"SET_DUNGEON_TYPE_name":               reflect.ValueOf(&gm.SET_DUNGEON_TYPE_name).Elem(),
		"SET_DUNGEON_TYPE_value":              reflect.ValueOf(&gm.SET_DUNGEON_TYPE_value).Elem(),

		// type definitions
		"ARTIFACT_DEBUT":                 reflect.ValueOf((*gm.ARTIFACT_DEBUT)(nil)),
		"AccountTagReq":                  reflect.ValueOf((*gm.AccountTagReq)(nil)),
		"AccountTagRsp":                  reflect.ValueOf((*gm.AccountTagRsp)(nil)),
		"ActInfo":                        reflect.ValueOf((*gm.ActInfo)(nil)),
		"ActPartitionRunInfo":            reflect.ValueOf((*gm.ActPartitionRunInfo)(nil)),
		"ActivityInfoRsp":                reflect.ValueOf((*gm.ActivityInfoRsp)(nil)),
		"AnnouncementReq":                reflect.ValueOf((*gm.AnnouncementReq)(nil)),
		"ArenaReq":                       reflect.ValueOf((*gm.ArenaReq)(nil)),
		"ArtifactDebutReq":               reflect.ValueOf((*gm.ArtifactDebutReq)(nil)),
		"BagsOp":                         reflect.ValueOf((*gm.BagsOp)(nil)),
		"BanAccountReq":                  reflect.ValueOf((*gm.BanAccountReq)(nil)),
		"BanCmd":                         reflect.ValueOf((*gm.BanCmd)(nil)),
		"BattleHeroStar":                 reflect.ValueOf((*gm.BattleHeroStar)(nil)),
		"BossRush":                       reflect.ValueOf((*gm.BossRush)(nil)),
		"Cmd":                            reflect.ValueOf((*gm.Cmd)(nil)),
		"CrossClient":                    reflect.ValueOf((*gm.CrossClient)(nil)),
		"CrossMasterClient":              reflect.ValueOf((*gm.CrossMasterClient)(nil)),
		"CrossMasterServer":              reflect.ValueOf((*gm.CrossMasterServer)(nil)),
		"CrossServer":                    reflect.ValueOf((*gm.CrossServer)(nil)),
		"DailyWishInfo":                  reflect.ValueOf((*gm.DailyWishInfo)(nil)),
		"DelServerMail":                  reflect.ValueOf((*gm.DelServerMail)(nil)),
		"DeleteCurrenciesReq":            reflect.ValueOf((*gm.DeleteCurrenciesReq)(nil)),
		"DeleteHeroReq":                  reflect.ValueOf((*gm.DeleteHeroReq)(nil)),
		"DeleteUserMailReq":              reflect.ValueOf((*gm.DeleteUserMailReq)(nil)),
		"DivineDemonBenchReq":            reflect.ValueOf((*gm.DivineDemonBenchReq)(nil)),
		"DivineDemonBenchRsp":            reflect.ValueOf((*gm.DivineDemonBenchRsp)(nil)),
		"DivineDemonReq":                 reflect.ValueOf((*gm.DivineDemonReq)(nil)),
		"GMGSTTeamMoveReq":               reflect.ValueOf((*gm.GMGSTTeamMoveReq)(nil)),
		"GMGSTTeamMoveRsp":               reflect.ValueOf((*gm.GMGSTTeamMoveRsp)(nil)),
		"GetActPartitionResp":            reflect.ValueOf((*gm.GetActPartitionResp)(nil)),
		"GetActPartitionRunInfoResp":     reflect.ValueOf((*gm.GetActPartitionRunInfoResp)(nil)),
		"GetRankInfoResp":                reflect.ValueOf((*gm.GetRankInfoResp)(nil)),
		"GiftCodeInfo":                   reflect.ValueOf((*gm.GiftCodeInfo)(nil)),
		"GmConfigReq":                    reflect.ValueOf((*gm.GmConfigReq)(nil)),
		"GmGetGuildInfoReq":              reflect.ValueOf((*gm.GmGetGuildInfoReq)(nil)),
		"GmGetGuildInfoResp":             reflect.ValueOf((*gm.GmGetGuildInfoResp)(nil)),
		"GmGetGuildListReq":              reflect.ValueOf((*gm.GmGetGuildListReq)(nil)),
		"GmGetGuildListResp":             reflect.ValueOf((*gm.GmGetGuildListResp)(nil)),
		"GmGuildBaseInfo":                reflect.ValueOf((*gm.GmGuildBaseInfo)(nil)),
		"GmGuildChangeLeaderReq":         reflect.ValueOf((*gm.GmGuildChangeLeaderReq)(nil)),
		"GmGuildChangeLeaderResp":        reflect.ValueOf((*gm.GmGuildChangeLeaderResp)(nil)),
		"GmGuildChangeNameReq":           reflect.ValueOf((*gm.GmGuildChangeNameReq)(nil)),
		"GmGuildChangeNameResp":          reflect.ValueOf((*gm.GmGuildChangeNameResp)(nil)),
		"GmGuildChangeNoticeReq":         reflect.ValueOf((*gm.GmGuildChangeNoticeReq)(nil)),
		"GmGuildChangeNoticeResp":        reflect.ValueOf((*gm.GmGuildChangeNoticeResp)(nil)),
		"GmGuildDeleteMailReq":           reflect.ValueOf((*gm.GmGuildDeleteMailReq)(nil)),
		"GmGuildDeleteMailResp":          reflect.ValueOf((*gm.GmGuildDeleteMailResp)(nil)),
		"GmGuildDisbandReq":              reflect.ValueOf((*gm.GmGuildDisbandReq)(nil)),
		"GmGuildDisbandResp":             reflect.ValueOf((*gm.GmGuildDisbandResp)(nil)),
		"GmGuildInfo":                    reflect.ValueOf((*gm.GmGuildInfo)(nil)),
		"GmGuildKickMemberReq":           reflect.ValueOf((*gm.GmGuildKickMemberReq)(nil)),
		"GmGuildKickMemberResp":          reflect.ValueOf((*gm.GmGuildKickMemberResp)(nil)),
		"GmGuildMemberInfo":              reflect.ValueOf((*gm.GmGuildMemberInfo)(nil)),
		"GmTestClient":                   reflect.ValueOf((*gm.GmTestClient)(nil)),
		"GmTestServer":                   reflect.ValueOf((*gm.GmTestServer)(nil)),
		"HotfixReq":                      reflect.ValueOf((*gm.HotfixReq)(nil)),
		"ID":                             reflect.ValueOf((*gm.ID)(nil)),
		"ImportUserReq":                  reflect.ValueOf((*gm.ImportUserReq)(nil)),
		"ImportUserRsp":                  reflect.ValueOf((*gm.ImportUserRsp)(nil)),
		"LogTraceOp":                     reflect.ValueOf((*gm.LogTraceOp)(nil)),
		"LogicClient":                    reflect.ValueOf((*gm.LogicClient)(nil)),
		"LogicServer":                    reflect.ValueOf((*gm.LogicServer)(nil)),
		"MailMultiLangs":                 reflect.ValueOf((*gm.MailMultiLangs)(nil)),
		"MirageReq":                      reflect.ValueOf((*gm.MirageReq)(nil)),
		"MultiLangs":                     reflect.ValueOf((*gm.MultiLangs)(nil)),
		"Object":                         reflect.ValueOf((*gm.Object)(nil)),
		"OperateActivity":                reflect.ValueOf((*gm.OperateActivity)(nil)),
		"OperateActivityInfos":           reflect.ValueOf((*gm.OperateActivityInfos)(nil)),
		"OperateGiftInfos":               reflect.ValueOf((*gm.OperateGiftInfos)(nil)),
		"OperatePageInfos":               reflect.ValueOf((*gm.OperatePageInfos)(nil)),
		"OperateTaskInfos":               reflect.ValueOf((*gm.OperateTaskInfos)(nil)),
		"PGPMail":                        reflect.ValueOf((*gm.PGPMail)(nil)),
		"PromotionGiftInfos":             reflect.ValueOf((*gm.PromotionGiftInfos)(nil)),
		"QuestionnaireFinishReq":         reflect.ValueOf((*gm.QuestionnaireFinishReq)(nil)),
		"RankInfo":                       reflect.ValueOf((*gm.RankInfo)(nil)),
		"RechargeListReq":                reflect.ValueOf((*gm.RechargeListReq)(nil)),
		"RechargeListRsp":                reflect.ValueOf((*gm.RechargeListRsp)(nil)),
		"ResetAreaReq":                   reflect.ValueOf((*gm.ResetAreaReq)(nil)),
		"Result":                         reflect.ValueOf((*gm.Result)(nil)),
		"RetOnlineNum":                   reflect.ValueOf((*gm.RetOnlineNum)(nil)),
		"RetUser":                        reflect.ValueOf((*gm.RetUser)(nil)),
		"SEARCH":                         reflect.ValueOf((*gm.SEARCH)(nil)),
		"SET_DUNGEON_TYPE":               reflect.ValueOf((*gm.SET_DUNGEON_TYPE)(nil)),
		"SeasonLinkRune":                 reflect.ValueOf((*gm.SeasonLinkRune)(nil)),
		"SelectSummonReq":                reflect.ValueOf((*gm.SelectSummonReq)(nil)),
		"ServerMail":                     reflect.ValueOf((*gm.ServerMail)(nil)),
		"ServerTimeReq":                  reflect.ValueOf((*gm.ServerTimeReq)(nil)),
		"ServerTimeRsp":                  reflect.ValueOf((*gm.ServerTimeRsp)(nil)),
		"SetActPartitionReq":             reflect.ValueOf((*gm.SetActPartitionReq)(nil)),
		"SetBossRushLevelReq":            reflect.ValueOf((*gm.SetBossRushLevelReq)(nil)),
		"SetSeasonLinkMonumentsReq":      reflect.ValueOf((*gm.SetSeasonLinkMonumentsReq)(nil)),
		"SinglePushGiftReq":              reflect.ValueOf((*gm.SinglePushGiftReq)(nil)),
		"StartAreaReq":                   reflect.ValueOf((*gm.StartAreaReq)(nil)),
		"StopAreaReq":                    reflect.ValueOf((*gm.StopAreaReq)(nil)),
		"TowerReq":                       reflect.ValueOf((*gm.TowerReq)(nil)),
		"TrialReq":                       reflect.ValueOf((*gm.TrialReq)(nil)),
		"UnimplementedCrossMasterServer": reflect.ValueOf((*gm.UnimplementedCrossMasterServer)(nil)),
		"UnimplementedCrossServer":       reflect.ValueOf((*gm.UnimplementedCrossServer)(nil)),
		"UnimplementedGmTestServer":      reflect.ValueOf((*gm.UnimplementedGmTestServer)(nil)),
		"UnimplementedLogicServer":       reflect.ValueOf((*gm.UnimplementedLogicServer)(nil)),
		"UpdatePeopleGroupPackageReq":    reflect.ValueOf((*gm.UpdatePeopleGroupPackageReq)(nil)),
		"UserIndex":                      reflect.ValueOf((*gm.UserIndex)(nil)),
		"UserMailListReq":                reflect.ValueOf((*gm.UserMailListReq)(nil)),
		"UserMailListRsp":                reflect.ValueOf((*gm.UserMailListRsp)(nil)),
		"UserReq":                        reflect.ValueOf((*gm.UserReq)(nil)),
		"UserResReq":                     reflect.ValueOf((*gm.UserResReq)(nil)),
		"UserResource":                   reflect.ValueOf((*gm.UserResource)(nil)),
		"UserSetHeroAwakenLevel":         reflect.ValueOf((*gm.UserSetHeroAwakenLevel)(nil)),
		"UserSetParam":                   reflect.ValueOf((*gm.UserSetParam)(nil)),
		"UserSetRiteRare":                reflect.ValueOf((*gm.UserSetRiteRare)(nil)),
		"UsersMail":                      reflect.ValueOf((*gm.UsersMail)(nil)),
		"VersionReq":                     reflect.ValueOf((*gm.VersionReq)(nil)),
		"VersionRsp":                     reflect.ValueOf((*gm.VersionRsp)(nil)),
		"WebLargeRechargeMailParams":     reflect.ValueOf((*gm.WebLargeRechargeMailParams)(nil)),

		// interface wrapper definitions
		"_CrossClient":       reflect.ValueOf((*_app_protos_in_gm_CrossClient)(nil)),
		"_CrossMasterClient": reflect.ValueOf((*_app_protos_in_gm_CrossMasterClient)(nil)),
		"_CrossMasterServer": reflect.ValueOf((*_app_protos_in_gm_CrossMasterServer)(nil)),
		"_CrossServer":       reflect.ValueOf((*_app_protos_in_gm_CrossServer)(nil)),
		"_GmTestClient":      reflect.ValueOf((*_app_protos_in_gm_GmTestClient)(nil)),
		"_GmTestServer":      reflect.ValueOf((*_app_protos_in_gm_GmTestServer)(nil)),
		"_LogicClient":       reflect.ValueOf((*_app_protos_in_gm_LogicClient)(nil)),
		"_LogicServer":       reflect.ValueOf((*_app_protos_in_gm_LogicServer)(nil)),
	}
}

// _app_protos_in_gm_CrossClient is an interface wrapper for CrossClient type
type _app_protos_in_gm_CrossClient struct {
	IValue               interface{}
	WGetRankInfo         func(ctx context.Context, in *gm.RankInfo, opts ...grpc.CallOption) (*gm.GetRankInfoResp, error)
	WGmGSTTeamMove       func(ctx context.Context, in *gm.GMGSTTeamMoveReq, opts ...grpc.CallOption) (*gm.GMGSTTeamMoveRsp, error)
	WGmGetGuildInfo      func(ctx context.Context, in *gm.GmGetGuildInfoReq, opts ...grpc.CallOption) (*gm.GmGetGuildInfoResp, error)
	WGmGetGuildList      func(ctx context.Context, in *gm.GmGetGuildListReq, opts ...grpc.CallOption) (*gm.GmGetGuildListResp, error)
	WGmGuildChangeLeader func(ctx context.Context, in *gm.GmGuildChangeLeaderReq, opts ...grpc.CallOption) (*gm.GmGuildChangeLeaderResp, error)
	WGmGuildChangeName   func(ctx context.Context, in *gm.GmGuildChangeNameReq, opts ...grpc.CallOption) (*gm.GmGuildChangeNameResp, error)
	WGmGuildChangeNotice func(ctx context.Context, in *gm.GmGuildChangeNoticeReq, opts ...grpc.CallOption) (*gm.GmGuildChangeNoticeResp, error)
	WGmGuildDeleteMail   func(ctx context.Context, in *gm.GmGuildDeleteMailReq, opts ...grpc.CallOption) (*gm.GmGuildDeleteMailResp, error)
	WGmGuildDisband      func(ctx context.Context, in *gm.GmGuildDisbandReq, opts ...grpc.CallOption) (*gm.GmGuildDisbandResp, error)
	WGmGuildKickMember   func(ctx context.Context, in *gm.GmGuildKickMemberReq, opts ...grpc.CallOption) (*gm.GmGuildKickMemberResp, error)
}

func (W _app_protos_in_gm_CrossClient) GetRankInfo(ctx context.Context, in *gm.RankInfo, opts ...grpc.CallOption) (*gm.GetRankInfoResp, error) {
	return W.WGetRankInfo(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGSTTeamMove(ctx context.Context, in *gm.GMGSTTeamMoveReq, opts ...grpc.CallOption) (*gm.GMGSTTeamMoveRsp, error) {
	return W.WGmGSTTeamMove(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGetGuildInfo(ctx context.Context, in *gm.GmGetGuildInfoReq, opts ...grpc.CallOption) (*gm.GmGetGuildInfoResp, error) {
	return W.WGmGetGuildInfo(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGetGuildList(ctx context.Context, in *gm.GmGetGuildListReq, opts ...grpc.CallOption) (*gm.GmGetGuildListResp, error) {
	return W.WGmGetGuildList(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGuildChangeLeader(ctx context.Context, in *gm.GmGuildChangeLeaderReq, opts ...grpc.CallOption) (*gm.GmGuildChangeLeaderResp, error) {
	return W.WGmGuildChangeLeader(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGuildChangeName(ctx context.Context, in *gm.GmGuildChangeNameReq, opts ...grpc.CallOption) (*gm.GmGuildChangeNameResp, error) {
	return W.WGmGuildChangeName(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGuildChangeNotice(ctx context.Context, in *gm.GmGuildChangeNoticeReq, opts ...grpc.CallOption) (*gm.GmGuildChangeNoticeResp, error) {
	return W.WGmGuildChangeNotice(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGuildDeleteMail(ctx context.Context, in *gm.GmGuildDeleteMailReq, opts ...grpc.CallOption) (*gm.GmGuildDeleteMailResp, error) {
	return W.WGmGuildDeleteMail(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGuildDisband(ctx context.Context, in *gm.GmGuildDisbandReq, opts ...grpc.CallOption) (*gm.GmGuildDisbandResp, error) {
	return W.WGmGuildDisband(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossClient) GmGuildKickMember(ctx context.Context, in *gm.GmGuildKickMemberReq, opts ...grpc.CallOption) (*gm.GmGuildKickMemberResp, error) {
	return W.WGmGuildKickMember(ctx, in, opts...)
}

// _app_protos_in_gm_CrossMasterClient is an interface wrapper for CrossMasterClient type
type _app_protos_in_gm_CrossMasterClient struct {
	IValue                  interface{}
	WGetActPartition        func(ctx context.Context, in *gm.ActInfo, opts ...grpc.CallOption) (*gm.GetActPartitionResp, error)
	WGetActPartitionRunInfo func(ctx context.Context, in *gm.ActInfo, opts ...grpc.CallOption) (*gm.GetActPartitionRunInfoResp, error)
	WResetArea              func(ctx context.Context, in *gm.ResetAreaReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetActPartition        func(ctx context.Context, in *gm.SetActPartitionReq, opts ...grpc.CallOption) (*gm.Result, error)
	WStartArea              func(ctx context.Context, in *gm.StartAreaReq, opts ...grpc.CallOption) (*gm.Result, error)
	WStopArea               func(ctx context.Context, in *gm.StopAreaReq, opts ...grpc.CallOption) (*gm.Result, error)
}

func (W _app_protos_in_gm_CrossMasterClient) GetActPartition(ctx context.Context, in *gm.ActInfo, opts ...grpc.CallOption) (*gm.GetActPartitionResp, error) {
	return W.WGetActPartition(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossMasterClient) GetActPartitionRunInfo(ctx context.Context, in *gm.ActInfo, opts ...grpc.CallOption) (*gm.GetActPartitionRunInfoResp, error) {
	return W.WGetActPartitionRunInfo(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossMasterClient) ResetArea(ctx context.Context, in *gm.ResetAreaReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetArea(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossMasterClient) SetActPartition(ctx context.Context, in *gm.SetActPartitionReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetActPartition(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossMasterClient) StartArea(ctx context.Context, in *gm.StartAreaReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WStartArea(ctx, in, opts...)
}
func (W _app_protos_in_gm_CrossMasterClient) StopArea(ctx context.Context, in *gm.StopAreaReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WStopArea(ctx, in, opts...)
}

// _app_protos_in_gm_CrossMasterServer is an interface wrapper for CrossMasterServer type
type _app_protos_in_gm_CrossMasterServer struct {
	IValue                  interface{}
	WGetActPartition        func(a0 context.Context, a1 *gm.ActInfo) (*gm.GetActPartitionResp, error)
	WGetActPartitionRunInfo func(a0 context.Context, a1 *gm.ActInfo) (*gm.GetActPartitionRunInfoResp, error)
	WResetArea              func(a0 context.Context, a1 *gm.ResetAreaReq) (*gm.Result, error)
	WSetActPartition        func(a0 context.Context, a1 *gm.SetActPartitionReq) (*gm.Result, error)
	WStartArea              func(a0 context.Context, a1 *gm.StartAreaReq) (*gm.Result, error)
	WStopArea               func(a0 context.Context, a1 *gm.StopAreaReq) (*gm.Result, error)
}

func (W _app_protos_in_gm_CrossMasterServer) GetActPartition(a0 context.Context, a1 *gm.ActInfo) (*gm.GetActPartitionResp, error) {
	return W.WGetActPartition(a0, a1)
}
func (W _app_protos_in_gm_CrossMasterServer) GetActPartitionRunInfo(a0 context.Context, a1 *gm.ActInfo) (*gm.GetActPartitionRunInfoResp, error) {
	return W.WGetActPartitionRunInfo(a0, a1)
}
func (W _app_protos_in_gm_CrossMasterServer) ResetArea(a0 context.Context, a1 *gm.ResetAreaReq) (*gm.Result, error) {
	return W.WResetArea(a0, a1)
}
func (W _app_protos_in_gm_CrossMasterServer) SetActPartition(a0 context.Context, a1 *gm.SetActPartitionReq) (*gm.Result, error) {
	return W.WSetActPartition(a0, a1)
}
func (W _app_protos_in_gm_CrossMasterServer) StartArea(a0 context.Context, a1 *gm.StartAreaReq) (*gm.Result, error) {
	return W.WStartArea(a0, a1)
}
func (W _app_protos_in_gm_CrossMasterServer) StopArea(a0 context.Context, a1 *gm.StopAreaReq) (*gm.Result, error) {
	return W.WStopArea(a0, a1)
}

// _app_protos_in_gm_CrossServer is an interface wrapper for CrossServer type
type _app_protos_in_gm_CrossServer struct {
	IValue               interface{}
	WGetRankInfo         func(a0 context.Context, a1 *gm.RankInfo) (*gm.GetRankInfoResp, error)
	WGmGSTTeamMove       func(a0 context.Context, a1 *gm.GMGSTTeamMoveReq) (*gm.GMGSTTeamMoveRsp, error)
	WGmGetGuildInfo      func(a0 context.Context, a1 *gm.GmGetGuildInfoReq) (*gm.GmGetGuildInfoResp, error)
	WGmGetGuildList      func(a0 context.Context, a1 *gm.GmGetGuildListReq) (*gm.GmGetGuildListResp, error)
	WGmGuildChangeLeader func(a0 context.Context, a1 *gm.GmGuildChangeLeaderReq) (*gm.GmGuildChangeLeaderResp, error)
	WGmGuildChangeName   func(a0 context.Context, a1 *gm.GmGuildChangeNameReq) (*gm.GmGuildChangeNameResp, error)
	WGmGuildChangeNotice func(a0 context.Context, a1 *gm.GmGuildChangeNoticeReq) (*gm.GmGuildChangeNoticeResp, error)
	WGmGuildDeleteMail   func(a0 context.Context, a1 *gm.GmGuildDeleteMailReq) (*gm.GmGuildDeleteMailResp, error)
	WGmGuildDisband      func(a0 context.Context, a1 *gm.GmGuildDisbandReq) (*gm.GmGuildDisbandResp, error)
	WGmGuildKickMember   func(a0 context.Context, a1 *gm.GmGuildKickMemberReq) (*gm.GmGuildKickMemberResp, error)
}

func (W _app_protos_in_gm_CrossServer) GetRankInfo(a0 context.Context, a1 *gm.RankInfo) (*gm.GetRankInfoResp, error) {
	return W.WGetRankInfo(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGSTTeamMove(a0 context.Context, a1 *gm.GMGSTTeamMoveReq) (*gm.GMGSTTeamMoveRsp, error) {
	return W.WGmGSTTeamMove(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGetGuildInfo(a0 context.Context, a1 *gm.GmGetGuildInfoReq) (*gm.GmGetGuildInfoResp, error) {
	return W.WGmGetGuildInfo(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGetGuildList(a0 context.Context, a1 *gm.GmGetGuildListReq) (*gm.GmGetGuildListResp, error) {
	return W.WGmGetGuildList(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGuildChangeLeader(a0 context.Context, a1 *gm.GmGuildChangeLeaderReq) (*gm.GmGuildChangeLeaderResp, error) {
	return W.WGmGuildChangeLeader(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGuildChangeName(a0 context.Context, a1 *gm.GmGuildChangeNameReq) (*gm.GmGuildChangeNameResp, error) {
	return W.WGmGuildChangeName(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGuildChangeNotice(a0 context.Context, a1 *gm.GmGuildChangeNoticeReq) (*gm.GmGuildChangeNoticeResp, error) {
	return W.WGmGuildChangeNotice(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGuildDeleteMail(a0 context.Context, a1 *gm.GmGuildDeleteMailReq) (*gm.GmGuildDeleteMailResp, error) {
	return W.WGmGuildDeleteMail(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGuildDisband(a0 context.Context, a1 *gm.GmGuildDisbandReq) (*gm.GmGuildDisbandResp, error) {
	return W.WGmGuildDisband(a0, a1)
}
func (W _app_protos_in_gm_CrossServer) GmGuildKickMember(a0 context.Context, a1 *gm.GmGuildKickMemberReq) (*gm.GmGuildKickMemberResp, error) {
	return W.WGmGuildKickMember(a0, a1)
}

// _app_protos_in_gm_GmTestClient is an interface wrapper for GmTestClient type
type _app_protos_in_gm_GmTestClient struct {
	IValue                         interface{}
	WAddGuildMobScore              func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WClearUserResource             func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WDivineDemonBench              func(ctx context.Context, in *gm.DivineDemonBenchReq, opts ...grpc.CallOption) (*gm.DivineDemonBenchRsp, error)
	WImportUser                    func(ctx context.Context, in *gm.ImportUserReq, opts ...grpc.CallOption) (*gm.ImportUserRsp, error)
	WQueryServerTime               func(ctx context.Context, in *gm.Cmd, opts ...grpc.CallOption) (*gm.ServerTimeRsp, error)
	WRecoveryGuildDungeonResetTime func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetArenaScore               func(ctx context.Context, in *gm.ArenaReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetDailyNum                 func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetGuildQuitTm              func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetMaze                     func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetMirageFloor              func(ctx context.Context, in *gm.MirageReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetScore                    func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetTaleChapterFinish        func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetTaleElite                func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetTowerFloor               func(ctx context.Context, in *gm.TowerReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetTowerstar                func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WResetTrialLevel               func(ctx context.Context, in *gm.TrialReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSendTopResource               func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetAllPushGift                func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetBattleHeroStar             func(ctx context.Context, in *gm.BattleHeroStar, opts ...grpc.CallOption) (*gm.Result, error)
	WSetBossRushLevel              func(ctx context.Context, in *gm.SetBossRushLevelReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetDailyAttendance            func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetDailyAttendanceHero        func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetDisorderLand               func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetDispatchLevel              func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetDungeonID                  func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetFlowerLevel                func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetForestLevel                func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetGuidanceClose              func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetGuildDungeonCurrentChapter func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetGuildLevel                 func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetHeroAwakenLevel            func(ctx context.Context, in *gm.UserSetHeroAwakenLevel, opts ...grpc.CallOption) (*gm.Result, error)
	WSetMazeTaskLevel              func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetMedalLevel                 func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetRemain                     func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetRiteRare                   func(ctx context.Context, in *gm.UserSetRiteRare, opts ...grpc.CallOption) (*gm.Result, error)
	WSetSeasonArenaScore           func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetSeasonLevel                func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetSeasonLink                 func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetSeasonLinkMonuments        func(ctx context.Context, in *gm.SetSeasonLinkMonumentsReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetServerTime                 func(ctx context.Context, in *gm.ServerTimeReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetSinglePushGift             func(ctx context.Context, in *gm.SinglePushGiftReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetTalentTree                 func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetTaskFinish                 func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetTowerSeasonFloor           func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetTowerstarDungeonID         func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetUserLevel                  func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetUserResource               func(ctx context.Context, in *gm.UserResource, opts ...grpc.CallOption) (*gm.Result, error)
	WSetUserVip                    func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WSetWrestleLevel               func(ctx context.Context, in *gm.UserSetParam, opts ...grpc.CallOption) (*gm.Result, error)
}

func (W _app_protos_in_gm_GmTestClient) AddGuildMobScore(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WAddGuildMobScore(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ClearUserResource(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WClearUserResource(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) DivineDemonBench(ctx context.Context, in *gm.DivineDemonBenchReq, opts ...grpc.CallOption) (*gm.DivineDemonBenchRsp, error) {
	return W.WDivineDemonBench(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ImportUser(ctx context.Context, in *gm.ImportUserReq, opts ...grpc.CallOption) (*gm.ImportUserRsp, error) {
	return W.WImportUser(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) QueryServerTime(ctx context.Context, in *gm.Cmd, opts ...grpc.CallOption) (*gm.ServerTimeRsp, error) {
	return W.WQueryServerTime(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) RecoveryGuildDungeonResetTime(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WRecoveryGuildDungeonResetTime(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetArenaScore(ctx context.Context, in *gm.ArenaReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetArenaScore(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetDailyNum(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetDailyNum(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetGuildQuitTm(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetGuildQuitTm(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetMaze(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetMaze(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetMirageFloor(ctx context.Context, in *gm.MirageReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetMirageFloor(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetScore(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetScore(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetTaleChapterFinish(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetTaleChapterFinish(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetTaleElite(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetTaleElite(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetTowerFloor(ctx context.Context, in *gm.TowerReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetTowerFloor(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetTowerstar(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetTowerstar(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) ResetTrialLevel(ctx context.Context, in *gm.TrialReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WResetTrialLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SendTopResource(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSendTopResource(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetAllPushGift(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetAllPushGift(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetBattleHeroStar(ctx context.Context, in *gm.BattleHeroStar, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetBattleHeroStar(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetBossRushLevel(ctx context.Context, in *gm.SetBossRushLevelReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetBossRushLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetDailyAttendance(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetDailyAttendance(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetDailyAttendanceHero(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetDailyAttendanceHero(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetDisorderLand(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetDisorderLand(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetDispatchLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetDispatchLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetDungeonID(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetDungeonID(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetFlowerLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetFlowerLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetForestLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetForestLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetGuidanceClose(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetGuidanceClose(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetGuildDungeonCurrentChapter(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetGuildDungeonCurrentChapter(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetGuildLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetGuildLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetHeroAwakenLevel(ctx context.Context, in *gm.UserSetHeroAwakenLevel, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetHeroAwakenLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetMazeTaskLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetMazeTaskLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetMedalLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetMedalLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetRemain(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetRemain(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetRiteRare(ctx context.Context, in *gm.UserSetRiteRare, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetRiteRare(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetSeasonArenaScore(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetSeasonArenaScore(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetSeasonLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetSeasonLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetSeasonLink(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetSeasonLink(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetSeasonLinkMonuments(ctx context.Context, in *gm.SetSeasonLinkMonumentsReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetSeasonLinkMonuments(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetServerTime(ctx context.Context, in *gm.ServerTimeReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetServerTime(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetSinglePushGift(ctx context.Context, in *gm.SinglePushGiftReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetSinglePushGift(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetTalentTree(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetTalentTree(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetTaskFinish(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetTaskFinish(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetTowerSeasonFloor(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetTowerSeasonFloor(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetTowerstarDungeonID(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetTowerstarDungeonID(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetUserLevel(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetUserLevel(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetUserResource(ctx context.Context, in *gm.UserResource, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetUserResource(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetUserVip(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetUserVip(ctx, in, opts...)
}
func (W _app_protos_in_gm_GmTestClient) SetWrestleLevel(ctx context.Context, in *gm.UserSetParam, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetWrestleLevel(ctx, in, opts...)
}

// _app_protos_in_gm_GmTestServer is an interface wrapper for GmTestServer type
type _app_protos_in_gm_GmTestServer struct {
	IValue                         interface{}
	WAddGuildMobScore              func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WClearUserResource             func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WDivineDemonBench              func(a0 context.Context, a1 *gm.DivineDemonBenchReq) (*gm.DivineDemonBenchRsp, error)
	WImportUser                    func(a0 context.Context, a1 *gm.ImportUserReq) (*gm.ImportUserRsp, error)
	WQueryServerTime               func(a0 context.Context, a1 *gm.Cmd) (*gm.ServerTimeRsp, error)
	WRecoveryGuildDungeonResetTime func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetArenaScore               func(a0 context.Context, a1 *gm.ArenaReq) (*gm.Result, error)
	WResetDailyNum                 func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetGuildQuitTm              func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetMaze                     func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetMirageFloor              func(a0 context.Context, a1 *gm.MirageReq) (*gm.Result, error)
	WResetScore                    func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetTaleChapterFinish        func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetTaleElite                func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetTowerFloor               func(a0 context.Context, a1 *gm.TowerReq) (*gm.Result, error)
	WResetTowerstar                func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WResetTrialLevel               func(a0 context.Context, a1 *gm.TrialReq) (*gm.Result, error)
	WSendTopResource               func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetAllPushGift                func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetBattleHeroStar             func(a0 context.Context, a1 *gm.BattleHeroStar) (*gm.Result, error)
	WSetBossRushLevel              func(a0 context.Context, a1 *gm.SetBossRushLevelReq) (*gm.Result, error)
	WSetDailyAttendance            func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetDailyAttendanceHero        func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetDisorderLand               func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetDispatchLevel              func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetDungeonID                  func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetFlowerLevel                func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetForestLevel                func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetGuidanceClose              func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetGuildDungeonCurrentChapter func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetGuildLevel                 func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetHeroAwakenLevel            func(a0 context.Context, a1 *gm.UserSetHeroAwakenLevel) (*gm.Result, error)
	WSetMazeTaskLevel              func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetMedalLevel                 func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetRemain                     func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetRiteRare                   func(a0 context.Context, a1 *gm.UserSetRiteRare) (*gm.Result, error)
	WSetSeasonArenaScore           func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetSeasonLevel                func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetSeasonLink                 func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetSeasonLinkMonuments        func(a0 context.Context, a1 *gm.SetSeasonLinkMonumentsReq) (*gm.Result, error)
	WSetServerTime                 func(a0 context.Context, a1 *gm.ServerTimeReq) (*gm.Result, error)
	WSetSinglePushGift             func(a0 context.Context, a1 *gm.SinglePushGiftReq) (*gm.Result, error)
	WSetTalentTree                 func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetTaskFinish                 func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetTowerSeasonFloor           func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetTowerstarDungeonID         func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetUserLevel                  func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetUserResource               func(a0 context.Context, a1 *gm.UserResource) (*gm.Result, error)
	WSetUserVip                    func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WSetWrestleLevel               func(a0 context.Context, a1 *gm.UserSetParam) (*gm.Result, error)
}

func (W _app_protos_in_gm_GmTestServer) AddGuildMobScore(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WAddGuildMobScore(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ClearUserResource(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WClearUserResource(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) DivineDemonBench(a0 context.Context, a1 *gm.DivineDemonBenchReq) (*gm.DivineDemonBenchRsp, error) {
	return W.WDivineDemonBench(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ImportUser(a0 context.Context, a1 *gm.ImportUserReq) (*gm.ImportUserRsp, error) {
	return W.WImportUser(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) QueryServerTime(a0 context.Context, a1 *gm.Cmd) (*gm.ServerTimeRsp, error) {
	return W.WQueryServerTime(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) RecoveryGuildDungeonResetTime(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WRecoveryGuildDungeonResetTime(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetArenaScore(a0 context.Context, a1 *gm.ArenaReq) (*gm.Result, error) {
	return W.WResetArenaScore(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetDailyNum(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetDailyNum(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetGuildQuitTm(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetGuildQuitTm(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetMaze(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetMaze(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetMirageFloor(a0 context.Context, a1 *gm.MirageReq) (*gm.Result, error) {
	return W.WResetMirageFloor(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetScore(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetScore(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetTaleChapterFinish(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetTaleChapterFinish(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetTaleElite(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetTaleElite(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetTowerFloor(a0 context.Context, a1 *gm.TowerReq) (*gm.Result, error) {
	return W.WResetTowerFloor(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetTowerstar(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WResetTowerstar(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) ResetTrialLevel(a0 context.Context, a1 *gm.TrialReq) (*gm.Result, error) {
	return W.WResetTrialLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SendTopResource(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSendTopResource(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetAllPushGift(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetAllPushGift(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetBattleHeroStar(a0 context.Context, a1 *gm.BattleHeroStar) (*gm.Result, error) {
	return W.WSetBattleHeroStar(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetBossRushLevel(a0 context.Context, a1 *gm.SetBossRushLevelReq) (*gm.Result, error) {
	return W.WSetBossRushLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetDailyAttendance(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetDailyAttendance(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetDailyAttendanceHero(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetDailyAttendanceHero(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetDisorderLand(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetDisorderLand(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetDispatchLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetDispatchLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetDungeonID(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetDungeonID(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetFlowerLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetFlowerLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetForestLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetForestLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetGuidanceClose(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetGuidanceClose(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetGuildDungeonCurrentChapter(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetGuildDungeonCurrentChapter(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetGuildLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetGuildLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetHeroAwakenLevel(a0 context.Context, a1 *gm.UserSetHeroAwakenLevel) (*gm.Result, error) {
	return W.WSetHeroAwakenLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetMazeTaskLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetMazeTaskLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetMedalLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetMedalLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetRemain(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetRemain(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetRiteRare(a0 context.Context, a1 *gm.UserSetRiteRare) (*gm.Result, error) {
	return W.WSetRiteRare(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetSeasonArenaScore(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetSeasonArenaScore(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetSeasonLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetSeasonLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetSeasonLink(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetSeasonLink(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetSeasonLinkMonuments(a0 context.Context, a1 *gm.SetSeasonLinkMonumentsReq) (*gm.Result, error) {
	return W.WSetSeasonLinkMonuments(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetServerTime(a0 context.Context, a1 *gm.ServerTimeReq) (*gm.Result, error) {
	return W.WSetServerTime(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetSinglePushGift(a0 context.Context, a1 *gm.SinglePushGiftReq) (*gm.Result, error) {
	return W.WSetSinglePushGift(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetTalentTree(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetTalentTree(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetTaskFinish(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetTaskFinish(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetTowerSeasonFloor(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetTowerSeasonFloor(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetTowerstarDungeonID(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetTowerstarDungeonID(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetUserLevel(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetUserLevel(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetUserResource(a0 context.Context, a1 *gm.UserResource) (*gm.Result, error) {
	return W.WSetUserResource(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetUserVip(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WSetUserVip(a0, a1)
}
func (W _app_protos_in_gm_GmTestServer) SetWrestleLevel(a0 context.Context, a1 *gm.UserSetParam) (*gm.Result, error) {
	return W.WSetWrestleLevel(a0, a1)
}

// _app_protos_in_gm_LogicClient is an interface wrapper for LogicClient type
type _app_protos_in_gm_LogicClient struct {
	IValue                    interface{}
	WBanAccount               func(ctx context.Context, in *gm.BanAccountReq, opts ...grpc.CallOption) (*gm.Result, error)
	WBanProtocol              func(ctx context.Context, in *gm.BanCmd, opts ...grpc.CallOption) (*gm.Result, error)
	WChangeBags               func(ctx context.Context, in *gm.BagsOp, opts ...grpc.CallOption) (*gm.Result, error)
	WChangeUserName           func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WCheckDropActivity        func(ctx context.Context, in *cl.DropActivityBase, opts ...grpc.CallOption) (*gm.Result, error)
	WCloseGuidance            func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WDelChatGroupTag          func(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error)
	WDeleteCurrencies         func(ctx context.Context, in *gm.DeleteCurrenciesReq, opts ...grpc.CallOption) (*gm.Result, error)
	WDeleteHero               func(ctx context.Context, in *gm.DeleteHeroReq, opts ...grpc.CallOption) (*gm.Result, error)
	WDeleteServerMail         func(ctx context.Context, in *gm.DelServerMail, opts ...grpc.CallOption) (*gm.Result, error)
	WDeleteUserMail           func(ctx context.Context, in *gm.DeleteUserMailReq, opts ...grpc.CallOption) (*gm.Result, error)
	WGetActivityInfo          func(ctx context.Context, in *gm.UserIndex, opts ...grpc.CallOption) (*gm.ActivityInfoRsp, error)
	WGetRechargeList          func(ctx context.Context, in *gm.RechargeListReq, opts ...grpc.CallOption) (*gm.RechargeListRsp, error)
	WGetUser                  func(ctx context.Context, in *gm.UserIndex, opts ...grpc.CallOption) (*gm.RetUser, error)
	WGetUserMailList          func(ctx context.Context, in *gm.UserMailListReq, opts ...grpc.CallOption) (*gm.UserMailListRsp, error)
	WGetVersion               func(ctx context.Context, in *gm.VersionReq, opts ...grpc.CallOption) (*gm.VersionRsp, error)
	WHotfix                   func(ctx context.Context, in *gm.HotfixReq, opts ...grpc.CallOption) (*gm.Result, error)
	WKickAccount              func(ctx context.Context, in *gm.UserIndex, opts ...grpc.CallOption) (*gm.Result, error)
	WNewQuestionnaireFinish   func(ctx context.Context, in *gm.QuestionnaireFinishReq, opts ...grpc.CallOption) (*gm.Result, error)
	WNotifyRecharge           func(ctx context.Context, in *db.Order, opts ...grpc.CallOption) (*gm.Result, error)
	WNotifyRefund             func(ctx context.Context, in *db.Order, opts ...grpc.CallOption) (*gm.Result, error)
	WOnlineNum                func(ctx context.Context, in *gm.Cmd, opts ...grpc.CallOption) (*gm.RetOnlineNum, error)
	WQuestionnaireFinish      func(ctx context.Context, in *gm.QuestionnaireFinishReq, opts ...grpc.CallOption) (*gm.Result, error)
	WReduceResources          func(ctx context.Context, in *gm.Object, opts ...grpc.CallOption) (*gm.Result, error)
	WReleaseOperateActivity   func(ctx context.Context, in *gm.OperateActivity, opts ...grpc.CallOption) (*gm.Result, error)
	WReleaseOperatePageInfo   func(ctx context.Context, in *gm.OperatePageInfos, opts ...grpc.CallOption) (*gm.Result, error)
	WSendGiftCodeInfo         func(ctx context.Context, in *gm.GiftCodeInfo, opts ...grpc.CallOption) (*gm.Result, error)
	WSendPGPMail              func(ctx context.Context, in *gm.PGPMail, opts ...grpc.CallOption) (*gm.Result, error)
	WSendQuestionnaire        func(ctx context.Context, in *cl.Questionnaire, opts ...grpc.CallOption) (*gm.Result, error)
	WSendServerMail           func(ctx context.Context, in *gm.ServerMail, opts ...grpc.CallOption) (*gm.Result, error)
	WSendUserMail             func(ctx context.Context, in *gm.UsersMail, opts ...grpc.CallOption) (*gm.Result, error)
	WSetAccountTag            func(ctx context.Context, in *gm.AccountTagReq, opts ...grpc.CallOption) (*gm.AccountTagRsp, error)
	WSetGmConfig              func(ctx context.Context, in *gm.GmConfigReq, opts ...grpc.CallOption) (*gm.Result, error)
	WToggleUserLogTrace       func(ctx context.Context, in *gm.LogTraceOp, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateAnnouncement       func(ctx context.Context, in *gm.AnnouncementReq, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateArtifactDebut      func(ctx context.Context, in *gm.ArtifactDebutReq, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateCouponActivity     func(ctx context.Context, in *cl.ActivityCouponXml, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateDailyWishActivity  func(ctx context.Context, in *gm.DailyWishInfo, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateDivineDemon        func(ctx context.Context, in *gm.DivineDemonReq, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateDropActivity       func(ctx context.Context, in *cl.DropActivityBase, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateMultiLangs         func(ctx context.Context, in *gm.MultiLangs, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdatePeopleGroupPackage func(ctx context.Context, in *gm.UpdatePeopleGroupPackageReq, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdatePyramidActivity    func(ctx context.Context, in *cl.PyramidActivityBase, opts ...grpc.CallOption) (*gm.Result, error)
	WUpdateSelectSummon       func(ctx context.Context, in *gm.SelectSummonReq, opts ...grpc.CallOption) (*gm.Result, error)
}

func (W _app_protos_in_gm_LogicClient) BanAccount(ctx context.Context, in *gm.BanAccountReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WBanAccount(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) BanProtocol(ctx context.Context, in *gm.BanCmd, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WBanProtocol(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) ChangeBags(ctx context.Context, in *gm.BagsOp, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WChangeBags(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) ChangeUserName(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WChangeUserName(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) CheckDropActivity(ctx context.Context, in *cl.DropActivityBase, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WCheckDropActivity(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) CloseGuidance(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WCloseGuidance(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) DelChatGroupTag(ctx context.Context, in *gm.UserReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WDelChatGroupTag(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) DeleteCurrencies(ctx context.Context, in *gm.DeleteCurrenciesReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WDeleteCurrencies(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) DeleteHero(ctx context.Context, in *gm.DeleteHeroReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WDeleteHero(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) DeleteServerMail(ctx context.Context, in *gm.DelServerMail, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WDeleteServerMail(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) DeleteUserMail(ctx context.Context, in *gm.DeleteUserMailReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WDeleteUserMail(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) GetActivityInfo(ctx context.Context, in *gm.UserIndex, opts ...grpc.CallOption) (*gm.ActivityInfoRsp, error) {
	return W.WGetActivityInfo(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) GetRechargeList(ctx context.Context, in *gm.RechargeListReq, opts ...grpc.CallOption) (*gm.RechargeListRsp, error) {
	return W.WGetRechargeList(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) GetUser(ctx context.Context, in *gm.UserIndex, opts ...grpc.CallOption) (*gm.RetUser, error) {
	return W.WGetUser(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) GetUserMailList(ctx context.Context, in *gm.UserMailListReq, opts ...grpc.CallOption) (*gm.UserMailListRsp, error) {
	return W.WGetUserMailList(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) GetVersion(ctx context.Context, in *gm.VersionReq, opts ...grpc.CallOption) (*gm.VersionRsp, error) {
	return W.WGetVersion(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) Hotfix(ctx context.Context, in *gm.HotfixReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WHotfix(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) KickAccount(ctx context.Context, in *gm.UserIndex, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WKickAccount(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) NewQuestionnaireFinish(ctx context.Context, in *gm.QuestionnaireFinishReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WNewQuestionnaireFinish(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) NotifyRecharge(ctx context.Context, in *db.Order, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WNotifyRecharge(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) NotifyRefund(ctx context.Context, in *db.Order, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WNotifyRefund(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) OnlineNum(ctx context.Context, in *gm.Cmd, opts ...grpc.CallOption) (*gm.RetOnlineNum, error) {
	return W.WOnlineNum(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) QuestionnaireFinish(ctx context.Context, in *gm.QuestionnaireFinishReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WQuestionnaireFinish(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) ReduceResources(ctx context.Context, in *gm.Object, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WReduceResources(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) ReleaseOperateActivity(ctx context.Context, in *gm.OperateActivity, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WReleaseOperateActivity(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) ReleaseOperatePageInfo(ctx context.Context, in *gm.OperatePageInfos, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WReleaseOperatePageInfo(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SendGiftCodeInfo(ctx context.Context, in *gm.GiftCodeInfo, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSendGiftCodeInfo(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SendPGPMail(ctx context.Context, in *gm.PGPMail, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSendPGPMail(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SendQuestionnaire(ctx context.Context, in *cl.Questionnaire, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSendQuestionnaire(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SendServerMail(ctx context.Context, in *gm.ServerMail, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSendServerMail(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SendUserMail(ctx context.Context, in *gm.UsersMail, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSendUserMail(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SetAccountTag(ctx context.Context, in *gm.AccountTagReq, opts ...grpc.CallOption) (*gm.AccountTagRsp, error) {
	return W.WSetAccountTag(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) SetGmConfig(ctx context.Context, in *gm.GmConfigReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WSetGmConfig(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) ToggleUserLogTrace(ctx context.Context, in *gm.LogTraceOp, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WToggleUserLogTrace(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateAnnouncement(ctx context.Context, in *gm.AnnouncementReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateAnnouncement(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateArtifactDebut(ctx context.Context, in *gm.ArtifactDebutReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateArtifactDebut(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateCouponActivity(ctx context.Context, in *cl.ActivityCouponXml, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateCouponActivity(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateDailyWishActivity(ctx context.Context, in *gm.DailyWishInfo, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateDailyWishActivity(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateDivineDemon(ctx context.Context, in *gm.DivineDemonReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateDivineDemon(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateDropActivity(ctx context.Context, in *cl.DropActivityBase, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateDropActivity(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateMultiLangs(ctx context.Context, in *gm.MultiLangs, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateMultiLangs(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdatePeopleGroupPackage(ctx context.Context, in *gm.UpdatePeopleGroupPackageReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdatePeopleGroupPackage(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdatePyramidActivity(ctx context.Context, in *cl.PyramidActivityBase, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdatePyramidActivity(ctx, in, opts...)
}
func (W _app_protos_in_gm_LogicClient) UpdateSelectSummon(ctx context.Context, in *gm.SelectSummonReq, opts ...grpc.CallOption) (*gm.Result, error) {
	return W.WUpdateSelectSummon(ctx, in, opts...)
}

// _app_protos_in_gm_LogicServer is an interface wrapper for LogicServer type
type _app_protos_in_gm_LogicServer struct {
	IValue                    interface{}
	WBanAccount               func(a0 context.Context, a1 *gm.BanAccountReq) (*gm.Result, error)
	WBanProtocol              func(a0 context.Context, a1 *gm.BanCmd) (*gm.Result, error)
	WChangeBags               func(a0 context.Context, a1 *gm.BagsOp) (*gm.Result, error)
	WChangeUserName           func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WCheckDropActivity        func(a0 context.Context, a1 *cl.DropActivityBase) (*gm.Result, error)
	WCloseGuidance            func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WDelChatGroupTag          func(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error)
	WDeleteCurrencies         func(a0 context.Context, a1 *gm.DeleteCurrenciesReq) (*gm.Result, error)
	WDeleteHero               func(a0 context.Context, a1 *gm.DeleteHeroReq) (*gm.Result, error)
	WDeleteServerMail         func(a0 context.Context, a1 *gm.DelServerMail) (*gm.Result, error)
	WDeleteUserMail           func(a0 context.Context, a1 *gm.DeleteUserMailReq) (*gm.Result, error)
	WGetActivityInfo          func(a0 context.Context, a1 *gm.UserIndex) (*gm.ActivityInfoRsp, error)
	WGetRechargeList          func(a0 context.Context, a1 *gm.RechargeListReq) (*gm.RechargeListRsp, error)
	WGetUser                  func(a0 context.Context, a1 *gm.UserIndex) (*gm.RetUser, error)
	WGetUserMailList          func(a0 context.Context, a1 *gm.UserMailListReq) (*gm.UserMailListRsp, error)
	WGetVersion               func(a0 context.Context, a1 *gm.VersionReq) (*gm.VersionRsp, error)
	WHotfix                   func(a0 context.Context, a1 *gm.HotfixReq) (*gm.Result, error)
	WKickAccount              func(a0 context.Context, a1 *gm.UserIndex) (*gm.Result, error)
	WNewQuestionnaireFinish   func(a0 context.Context, a1 *gm.QuestionnaireFinishReq) (*gm.Result, error)
	WNotifyRecharge           func(a0 context.Context, a1 *db.Order) (*gm.Result, error)
	WNotifyRefund             func(a0 context.Context, a1 *db.Order) (*gm.Result, error)
	WOnlineNum                func(a0 context.Context, a1 *gm.Cmd) (*gm.RetOnlineNum, error)
	WQuestionnaireFinish      func(a0 context.Context, a1 *gm.QuestionnaireFinishReq) (*gm.Result, error)
	WReduceResources          func(a0 context.Context, a1 *gm.Object) (*gm.Result, error)
	WReleaseOperateActivity   func(a0 context.Context, a1 *gm.OperateActivity) (*gm.Result, error)
	WReleaseOperatePageInfo   func(a0 context.Context, a1 *gm.OperatePageInfos) (*gm.Result, error)
	WSendGiftCodeInfo         func(a0 context.Context, a1 *gm.GiftCodeInfo) (*gm.Result, error)
	WSendPGPMail              func(a0 context.Context, a1 *gm.PGPMail) (*gm.Result, error)
	WSendQuestionnaire        func(a0 context.Context, a1 *cl.Questionnaire) (*gm.Result, error)
	WSendServerMail           func(a0 context.Context, a1 *gm.ServerMail) (*gm.Result, error)
	WSendUserMail             func(a0 context.Context, a1 *gm.UsersMail) (*gm.Result, error)
	WSetAccountTag            func(a0 context.Context, a1 *gm.AccountTagReq) (*gm.AccountTagRsp, error)
	WSetGmConfig              func(a0 context.Context, a1 *gm.GmConfigReq) (*gm.Result, error)
	WToggleUserLogTrace       func(a0 context.Context, a1 *gm.LogTraceOp) (*gm.Result, error)
	WUpdateAnnouncement       func(a0 context.Context, a1 *gm.AnnouncementReq) (*gm.Result, error)
	WUpdateArtifactDebut      func(a0 context.Context, a1 *gm.ArtifactDebutReq) (*gm.Result, error)
	WUpdateCouponActivity     func(a0 context.Context, a1 *cl.ActivityCouponXml) (*gm.Result, error)
	WUpdateDailyWishActivity  func(a0 context.Context, a1 *gm.DailyWishInfo) (*gm.Result, error)
	WUpdateDivineDemon        func(a0 context.Context, a1 *gm.DivineDemonReq) (*gm.Result, error)
	WUpdateDropActivity       func(a0 context.Context, a1 *cl.DropActivityBase) (*gm.Result, error)
	WUpdateMultiLangs         func(a0 context.Context, a1 *gm.MultiLangs) (*gm.Result, error)
	WUpdatePeopleGroupPackage func(a0 context.Context, a1 *gm.UpdatePeopleGroupPackageReq) (*gm.Result, error)
	WUpdatePyramidActivity    func(a0 context.Context, a1 *cl.PyramidActivityBase) (*gm.Result, error)
	WUpdateSelectSummon       func(a0 context.Context, a1 *gm.SelectSummonReq) (*gm.Result, error)
}

func (W _app_protos_in_gm_LogicServer) BanAccount(a0 context.Context, a1 *gm.BanAccountReq) (*gm.Result, error) {
	return W.WBanAccount(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) BanProtocol(a0 context.Context, a1 *gm.BanCmd) (*gm.Result, error) {
	return W.WBanProtocol(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) ChangeBags(a0 context.Context, a1 *gm.BagsOp) (*gm.Result, error) {
	return W.WChangeBags(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) ChangeUserName(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WChangeUserName(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) CheckDropActivity(a0 context.Context, a1 *cl.DropActivityBase) (*gm.Result, error) {
	return W.WCheckDropActivity(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) CloseGuidance(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WCloseGuidance(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) DelChatGroupTag(a0 context.Context, a1 *gm.UserReq) (*gm.Result, error) {
	return W.WDelChatGroupTag(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) DeleteCurrencies(a0 context.Context, a1 *gm.DeleteCurrenciesReq) (*gm.Result, error) {
	return W.WDeleteCurrencies(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) DeleteHero(a0 context.Context, a1 *gm.DeleteHeroReq) (*gm.Result, error) {
	return W.WDeleteHero(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) DeleteServerMail(a0 context.Context, a1 *gm.DelServerMail) (*gm.Result, error) {
	return W.WDeleteServerMail(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) DeleteUserMail(a0 context.Context, a1 *gm.DeleteUserMailReq) (*gm.Result, error) {
	return W.WDeleteUserMail(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) GetActivityInfo(a0 context.Context, a1 *gm.UserIndex) (*gm.ActivityInfoRsp, error) {
	return W.WGetActivityInfo(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) GetRechargeList(a0 context.Context, a1 *gm.RechargeListReq) (*gm.RechargeListRsp, error) {
	return W.WGetRechargeList(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) GetUser(a0 context.Context, a1 *gm.UserIndex) (*gm.RetUser, error) {
	return W.WGetUser(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) GetUserMailList(a0 context.Context, a1 *gm.UserMailListReq) (*gm.UserMailListRsp, error) {
	return W.WGetUserMailList(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) GetVersion(a0 context.Context, a1 *gm.VersionReq) (*gm.VersionRsp, error) {
	return W.WGetVersion(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) Hotfix(a0 context.Context, a1 *gm.HotfixReq) (*gm.Result, error) {
	return W.WHotfix(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) KickAccount(a0 context.Context, a1 *gm.UserIndex) (*gm.Result, error) {
	return W.WKickAccount(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) NewQuestionnaireFinish(a0 context.Context, a1 *gm.QuestionnaireFinishReq) (*gm.Result, error) {
	return W.WNewQuestionnaireFinish(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) NotifyRecharge(a0 context.Context, a1 *db.Order) (*gm.Result, error) {
	return W.WNotifyRecharge(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) NotifyRefund(a0 context.Context, a1 *db.Order) (*gm.Result, error) {
	return W.WNotifyRefund(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) OnlineNum(a0 context.Context, a1 *gm.Cmd) (*gm.RetOnlineNum, error) {
	return W.WOnlineNum(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) QuestionnaireFinish(a0 context.Context, a1 *gm.QuestionnaireFinishReq) (*gm.Result, error) {
	return W.WQuestionnaireFinish(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) ReduceResources(a0 context.Context, a1 *gm.Object) (*gm.Result, error) {
	return W.WReduceResources(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) ReleaseOperateActivity(a0 context.Context, a1 *gm.OperateActivity) (*gm.Result, error) {
	return W.WReleaseOperateActivity(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) ReleaseOperatePageInfo(a0 context.Context, a1 *gm.OperatePageInfos) (*gm.Result, error) {
	return W.WReleaseOperatePageInfo(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SendGiftCodeInfo(a0 context.Context, a1 *gm.GiftCodeInfo) (*gm.Result, error) {
	return W.WSendGiftCodeInfo(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SendPGPMail(a0 context.Context, a1 *gm.PGPMail) (*gm.Result, error) {
	return W.WSendPGPMail(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SendQuestionnaire(a0 context.Context, a1 *cl.Questionnaire) (*gm.Result, error) {
	return W.WSendQuestionnaire(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SendServerMail(a0 context.Context, a1 *gm.ServerMail) (*gm.Result, error) {
	return W.WSendServerMail(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SendUserMail(a0 context.Context, a1 *gm.UsersMail) (*gm.Result, error) {
	return W.WSendUserMail(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SetAccountTag(a0 context.Context, a1 *gm.AccountTagReq) (*gm.AccountTagRsp, error) {
	return W.WSetAccountTag(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) SetGmConfig(a0 context.Context, a1 *gm.GmConfigReq) (*gm.Result, error) {
	return W.WSetGmConfig(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) ToggleUserLogTrace(a0 context.Context, a1 *gm.LogTraceOp) (*gm.Result, error) {
	return W.WToggleUserLogTrace(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateAnnouncement(a0 context.Context, a1 *gm.AnnouncementReq) (*gm.Result, error) {
	return W.WUpdateAnnouncement(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateArtifactDebut(a0 context.Context, a1 *gm.ArtifactDebutReq) (*gm.Result, error) {
	return W.WUpdateArtifactDebut(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateCouponActivity(a0 context.Context, a1 *cl.ActivityCouponXml) (*gm.Result, error) {
	return W.WUpdateCouponActivity(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateDailyWishActivity(a0 context.Context, a1 *gm.DailyWishInfo) (*gm.Result, error) {
	return W.WUpdateDailyWishActivity(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateDivineDemon(a0 context.Context, a1 *gm.DivineDemonReq) (*gm.Result, error) {
	return W.WUpdateDivineDemon(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateDropActivity(a0 context.Context, a1 *cl.DropActivityBase) (*gm.Result, error) {
	return W.WUpdateDropActivity(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateMultiLangs(a0 context.Context, a1 *gm.MultiLangs) (*gm.Result, error) {
	return W.WUpdateMultiLangs(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdatePeopleGroupPackage(a0 context.Context, a1 *gm.UpdatePeopleGroupPackageReq) (*gm.Result, error) {
	return W.WUpdatePeopleGroupPackage(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdatePyramidActivity(a0 context.Context, a1 *cl.PyramidActivityBase) (*gm.Result, error) {
	return W.WUpdatePyramidActivity(a0, a1)
}
func (W _app_protos_in_gm_LogicServer) UpdateSelectSummon(a0 context.Context, a1 *gm.SelectSummonReq) (*gm.Result, error) {
	return W.WUpdateSelectSummon(a0, a1)
}
