// Code generated by 'yaegi extract app/protos/out/common'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/protos/out/common"
	"reflect"
)

func init() {
	Symbols["app/protos/out/common/common"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ACHIEVE_TYPE_OF_COMMON_ACHIEVE":                       reflect.ValueOf(common.ACHIEVE_TYPE_OF_COMMON_ACHIEVE),
		"ACHIEVE_TYPE_OF_DAILY_ACHIEVE":                        reflect.ValueOf(common.ACHIEVE_TYPE_OF_DAILY_ACHIEVE),
		"ACHIEVE_TYPE_name":                                    reflect.ValueOf(&common.ACHIEVE_TYPE_name).Elem(),
		"ACHIEVE_TYPE_value":                                   reflect.ValueOf(&common.ACHIEVE_TYPE_value).Elem(),
		"ACTIVITY_RECHARGE_GIFT_RESET_TYPE_ARGET_DAILY":        reflect.ValueOf(common.ACTIVITY_RECHARGE_GIFT_RESET_TYPE_ARGET_DAILY),
		"ACTIVITY_RECHARGE_GIFT_RESET_TYPE_ARGET_NONE":         reflect.ValueOf(common.ACTIVITY_RECHARGE_GIFT_RESET_TYPE_ARGET_NONE),
		"ACTIVITY_RECHARGE_GIFT_RESET_TYPE_name":               reflect.ValueOf(&common.ACTIVITY_RECHARGE_GIFT_RESET_TYPE_name).Elem(),
		"ACTIVITY_RECHARGE_GIFT_RESET_TYPE_value":              reflect.ValueOf(&common.ACTIVITY_RECHARGE_GIFT_RESET_TYPE_value).Elem(),
		"ACTIVITY_RECHARGE_SHOP_ARS_DAILY_ONE_CLICK":           reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_DAILY_ONE_CLICK),
		"ACTIVITY_RECHARGE_SHOP_ARS_DAILY_SPECIAL":             reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_DAILY_SPECIAL),
		"ACTIVITY_RECHARGE_SHOP_ARS_DAY_RESET":                 reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_DAY_RESET),
		"ACTIVITY_RECHARGE_SHOP_ARS_NONE":                      reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_NONE),
		"ACTIVITY_RECHARGE_SHOP_ARS_OPEN_LIMIT":                reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_OPEN_LIMIT),
		"ACTIVITY_RECHARGE_SHOP_ARS_PUZZLE_SHOP":               reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_PUZZLE_SHOP),
		"ACTIVITY_RECHARGE_SHOP_ARS_WEEK_RESET":                reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_ARS_WEEK_RESET),
		"ACTIVITY_RECHARGE_SHOP_BUY_TYPE_ARSBT_CHECK":          reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE_ARSBT_CHECK),
		"ACTIVITY_RECHARGE_SHOP_BUY_TYPE_ARSBT_SUCCESS":        reflect.ValueOf(common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE_ARSBT_SUCCESS),
		"ACTIVITY_RECHARGE_SHOP_BUY_TYPE_name":                 reflect.ValueOf(&common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE_name).Elem(),
		"ACTIVITY_RECHARGE_SHOP_BUY_TYPE_value":                reflect.ValueOf(&common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE_value).Elem(),
		"ACTIVITY_RECHARGE_SHOP_name":                          reflect.ValueOf(&common.ACTIVITY_RECHARGE_SHOP_name).Elem(),
		"ACTIVITY_RECHARGE_SHOP_value":                         reflect.ValueOf(&common.ACTIVITY_RECHARGE_SHOP_value).Elem(),
		"ACTIVITY_STORY_FIGHT_TYPE_ASFT_FIGHT":                 reflect.ValueOf(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_FIGHT),
		"ACTIVITY_STORY_FIGHT_TYPE_ASFT_NONE":                  reflect.ValueOf(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_NONE),
		"ACTIVITY_STORY_FIGHT_TYPE_ASFT_SWEEP":                 reflect.ValueOf(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_SWEEP),
		"ACTIVITY_STORY_FIGHT_TYPE_name":                       reflect.ValueOf(&common.ACTIVITY_STORY_FIGHT_TYPE_name).Elem(),
		"ACTIVITY_STORY_FIGHT_TYPE_value":                      reflect.ValueOf(&common.ACTIVITY_STORY_FIGHT_TYPE_value).Elem(),
		"ARENA_TIME_AT_NONE":                                   reflect.ValueOf(common.ARENA_TIME_AT_NONE),
		"ARENA_TIME_DAILY_AWARD":                               reflect.ValueOf(common.ARENA_TIME_DAILY_AWARD),
		"ARENA_TIME_SEASON_RESET":                              reflect.ValueOf(common.ARENA_TIME_SEASON_RESET),
		"ARENA_TIME_name":                                      reflect.ValueOf(&common.ARENA_TIME_name).Elem(),
		"ARENA_TIME_value":                                     reflect.ValueOf(&common.ARENA_TIME_value).Elem(),
		"ARTIFACT_DEBUT_ACT_RECV_ADAR_LOGIN":                   reflect.ValueOf(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_LOGIN),
		"ARTIFACT_DEBUT_ACT_RECV_ADAR_NONE":                    reflect.ValueOf(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_NONE),
		"ARTIFACT_DEBUT_ACT_RECV_ADAR_PUZZLE":                  reflect.ValueOf(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_PUZZLE),
		"ARTIFACT_DEBUT_ACT_RECV_ADAR_SUMMON":                  reflect.ValueOf(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_SUMMON),
		"ARTIFACT_DEBUT_ACT_RECV_name":                         reflect.ValueOf(&common.ARTIFACT_DEBUT_ACT_RECV_name).Elem(),
		"ARTIFACT_DEBUT_ACT_RECV_value":                        reflect.ValueOf(&common.ARTIFACT_DEBUT_ACT_RECV_value).Elem(),
		"ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_FRAGMENT":           reflect.ValueOf(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_FRAGMENT),
		"ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_NONE":               reflect.ValueOf(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_NONE),
		"ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_WHOLE":              reflect.ValueOf(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_WHOLE),
		"ARTIFACT_DEBUT_DRAW_ACT_TYPE_name":                    reflect.ValueOf(&common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_name).Elem(),
		"ARTIFACT_DEBUT_DRAW_ACT_TYPE_value":                   reflect.ValueOf(&common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_value).Elem(),
		"ARTIFACT_DEBUT_SUMMON_CATEGORY_JUNIOR":                reflect.ValueOf(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_JUNIOR),
		"ARTIFACT_DEBUT_SUMMON_CATEGORY_NONE":                  reflect.ValueOf(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_NONE),
		"ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR":                reflect.ValueOf(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR),
		"ARTIFACT_DEBUT_SUMMON_name":                           reflect.ValueOf(&common.ARTIFACT_DEBUT_SUMMON_name).Elem(),
		"ARTIFACT_DEBUT_SUMMON_value":                          reflect.ValueOf(&common.ARTIFACT_DEBUT_SUMMON_value).Elem(),
		"ARTIFACT_DEBUT_TYPE_ADT_COMMON":                       reflect.ValueOf(common.ARTIFACT_DEBUT_TYPE_ADT_COMMON),
		"ARTIFACT_DEBUT_TYPE_ADT_NEW":                          reflect.ValueOf(common.ARTIFACT_DEBUT_TYPE_ADT_NEW),
		"ARTIFACT_DEBUT_TYPE_ADT_NONE":                         reflect.ValueOf(common.ARTIFACT_DEBUT_TYPE_ADT_NONE),
		"ARTIFACT_DEBUT_TYPE_name":                             reflect.ValueOf(&common.ARTIFACT_DEBUT_TYPE_name).Elem(),
		"ARTIFACT_DEBUT_TYPE_value":                            reflect.ValueOf(&common.ARTIFACT_DEBUT_TYPE_value).Elem(),
		"ATTRINDEX_ATTACK":                                     reflect.ValueOf(common.ATTRINDEX_ATTACK),
		"ATTRINDEX_ATTACK_ADD_PER":                             reflect.ValueOf(common.ATTRINDEX_ATTACK_ADD_PER),
		"ATTRINDEX_BASE_MAX":                                   reflect.ValueOf(common.ATTRINDEX_BASE_MAX),
		"ATTRINDEX_DEFEND":                                     reflect.ValueOf(common.ATTRINDEX_DEFEND),
		"ATTRINDEX_DEFEND_ADD_PER":                             reflect.ValueOf(common.ATTRINDEX_DEFEND_ADD_PER),
		"ATTRINDEX_HP":                                         reflect.ValueOf(common.ATTRINDEX_HP),
		"ATTRINDEX_HP_ADD_PER":                                 reflect.ValueOf(common.ATTRINDEX_HP_ADD_PER),
		"ATTRINDEX_MAX":                                        reflect.ValueOf(common.ATTRINDEX_MAX),
		"ATTRINDEX_MIN":                                        reflect.ValueOf(common.ATTRINDEX_MIN),
		"ATTRINDEX_name":                                       reflect.ValueOf(&common.ATTRINDEX_name).Elem(),
		"ATTRINDEX_value":                                      reflect.ValueOf(&common.ATTRINDEX_value).Elem(),
		"AVATAR_DEFAULT_AVATAR_NONE":                           reflect.ValueOf(common.AVATAR_DEFAULT_AVATAR_NONE),
		"AVATAR_DEFAULT_CHAT_BUBBLES":                          reflect.ValueOf(common.AVATAR_DEFAULT_CHAT_BUBBLES),
		"AVATAR_DEFAULT_FRAME":                                 reflect.ValueOf(common.AVATAR_DEFAULT_FRAME),
		"AVATAR_DEFAULT_ICON":                                  reflect.ValueOf(common.AVATAR_DEFAULT_ICON),
		"AVATAR_DEFAULT_IMAGE":                                 reflect.ValueOf(common.AVATAR_DEFAULT_IMAGE),
		"AVATAR_DEFAULT_name":                                  reflect.ValueOf(&common.AVATAR_DEFAULT_name).Elem(),
		"AVATAR_DEFAULT_value":                                 reflect.ValueOf(&common.AVATAR_DEFAULT_value).Elem(),
		"AVATAR_SET_INDEX_ASI_CHAT_BUBBLES":                    reflect.ValueOf(common.AVATAR_SET_INDEX_ASI_CHAT_BUBBLES),
		"AVATAR_SET_INDEX_ASI_FRAME":                           reflect.ValueOf(common.AVATAR_SET_INDEX_ASI_FRAME),
		"AVATAR_SET_INDEX_ASI_ICON":                            reflect.ValueOf(common.AVATAR_SET_INDEX_ASI_ICON),
		"AVATAR_SET_INDEX_ASI_IMAGE":                           reflect.ValueOf(common.AVATAR_SET_INDEX_ASI_IMAGE),
		"AVATAR_SET_INDEX_ASI_MAX":                             reflect.ValueOf(common.AVATAR_SET_INDEX_ASI_MAX),
		"AVATAR_SET_INDEX_name":                                reflect.ValueOf(&common.AVATAR_SET_INDEX_name).Elem(),
		"AVATAR_SET_INDEX_value":                               reflect.ValueOf(&common.AVATAR_SET_INDEX_value).Elem(),
		"AVATAR_TYPE_ATP_ARENA_DIVISION":                       reflect.ValueOf(common.AVATAR_TYPE_ATP_ARENA_DIVISION),
		"AVATAR_TYPE_ATP_ARENA_FOREVER":                        reflect.ValueOf(common.AVATAR_TYPE_ATP_ARENA_FOREVER),
		"AVATAR_TYPE_ATP_NONE":                                 reflect.ValueOf(common.AVATAR_TYPE_ATP_NONE),
		"AVATAR_TYPE_name":                                     reflect.ValueOf(&common.AVATAR_TYPE_name).Elem(),
		"AVATAR_TYPE_value":                                    reflect.ValueOf(&common.AVATAR_TYPE_value).Elem(),
		"BAN_TYPE_CHAT":                                        reflect.ValueOf(common.BAN_TYPE_CHAT),
		"BAN_TYPE_LOGIN":                                       reflect.ValueOf(common.BAN_TYPE_LOGIN),
		"BAN_TYPE_LOGIN_TEMPORARY":                             reflect.ValueOf(common.BAN_TYPE_LOGIN_TEMPORARY),
		"BAN_TYPE_MAX":                                         reflect.ValueOf(common.BAN_TYPE_MAX),
		"BAN_TYPE_NONE":                                        reflect.ValueOf(common.BAN_TYPE_NONE),
		"BAN_name":                                             reflect.ValueOf(&common.BAN_name).Elem(),
		"BAN_value":                                            reflect.ValueOf(&common.BAN_value).Elem(),
		"BOSS_RUSH_BUY_TYPE_BRBT_DIAMOND":                      reflect.ValueOf(common.BOSS_RUSH_BUY_TYPE_BRBT_DIAMOND),
		"BOSS_RUSH_BUY_TYPE_BRBT_ITEM":                         reflect.ValueOf(common.BOSS_RUSH_BUY_TYPE_BRBT_ITEM),
		"BOSS_RUSH_BUY_TYPE_BRBT_NONE":                         reflect.ValueOf(common.BOSS_RUSH_BUY_TYPE_BRBT_NONE),
		"BOSS_RUSH_BUY_TYPE_name":                              reflect.ValueOf(&common.BOSS_RUSH_BUY_TYPE_name).Elem(),
		"BOSS_RUSH_BUY_TYPE_value":                             reflect.ValueOf(&common.BOSS_RUSH_BUY_TYPE_value).Elem(),
		"CAREER_ZY_HUN":                                        reflect.ValueOf(common.CAREER_ZY_HUN),
		"CAREER_ZY_NONE":                                       reflect.ValueOf(common.CAREER_ZY_NONE),
		"CAREER_ZY_SHU":                                        reflect.ValueOf(common.CAREER_ZY_SHU),
		"CAREER_ZY_TI":                                         reflect.ValueOf(common.CAREER_ZY_TI),
		"CAREER_name":                                          reflect.ValueOf(&common.CAREER_name).Elem(),
		"CAREER_value":                                         reflect.ValueOf(&common.CAREER_value).Elem(),
		"CHAT_LIKE_CL_GET_LIST_MAX_LENGTH":                     reflect.ValueOf(common.CHAT_LIKE_CL_GET_LIST_MAX_LENGTH),
		"CHAT_LIKE_CL_MAX_LIKE_NUM":                            reflect.ValueOf(common.CHAT_LIKE_CL_MAX_LIKE_NUM),
		"CHAT_LIKE_CL_NONE":                                    reflect.ValueOf(common.CHAT_LIKE_CL_NONE),
		"CHAT_LIKE_name":                                       reflect.ValueOf(&common.CHAT_LIKE_name).Elem(),
		"CHAT_LIKE_value":                                      reflect.ValueOf(&common.CHAT_LIKE_value).Elem(),
		"CONFIG_ACTIVITY_DROP_SERVER_DATA":                     reflect.ValueOf(common.CONFIG_ACTIVITY_DROP_SERVER_DATA),
		"CONFIG_ACTIVITY_MIRAGE_DURATION":                      reflect.ValueOf(common.CONFIG_ACTIVITY_MIRAGE_DURATION),
		"CONFIG_ACTIVITY_MIRAGE_LIKE_DIAMOND":                  reflect.ValueOf(common.CONFIG_ACTIVITY_MIRAGE_LIKE_DIAMOND),
		"CONFIG_ACTIVITY_MIRAGE_RANK_REMAIN":                   reflect.ValueOf(common.CONFIG_ACTIVITY_MIRAGE_RANK_REMAIN),
		"CONFIG_ACTIVITY_RECHARGE_DAILY_WISH":                  reflect.ValueOf(common.CONFIG_ACTIVITY_RECHARGE_DAILY_WISH),
		"CONFIG_ACTIVITY_STORY_SHEILD_DAYS":                    reflect.ValueOf(common.CONFIG_ACTIVITY_STORY_SHEILD_DAYS),
		"CONFIG_ACTIVITY_TICKET_INCREASE_DAILY":                reflect.ValueOf(common.CONFIG_ACTIVITY_TICKET_INCREASE_DAILY),
		"CONFIG_ACTIVITY_TICKET_INCREASE_LIMIT":                reflect.ValueOf(common.CONFIG_ACTIVITY_TICKET_INCREASE_LIMIT),
		"CONFIG_ACTIVITY_TOWER_DURATION":                       reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_DURATION),
		"CONFIG_ACTIVITY_TOWER_LIKE_DIAMOND":                   reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_LIKE_DIAMOND),
		"CONFIG_ACTIVITY_TOWER_RANK_REMAIN":                    reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_RANK_REMAIN),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_AWARD_END":          reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_AWARD_END),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_AWARD_END_ROUND_2":  reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_AWARD_END_ROUND_2),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_DAILY_LIKE_AWARD":   reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_DAILY_LIKE_AWARD),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_END":                reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_END),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_END_ROUND_2":        reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_END_ROUND_2),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_OPEN":               reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_OPEN),
		"CONFIG_ACTIVITY_TOWER_SEASON_RANK_OPEN_ROUND_2":       reflect.ValueOf(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_OPEN_ROUND_2),
		"CONFIG_ARENA_BOT_LV_ADD_NUM":                          reflect.ValueOf(common.CONFIG_ARENA_BOT_LV_ADD_NUM),
		"CONFIG_ARENA_BOT_LV_SUB_NUM":                          reflect.ValueOf(common.CONFIG_ARENA_BOT_LV_SUB_NUM),
		"CONFIG_ARENA_BOT_MAX_SCORE":                           reflect.ValueOf(common.CONFIG_ARENA_BOT_MAX_SCORE),
		"CONFIG_ARENA_BOT_MIN_SCORE":                           reflect.ValueOf(common.CONFIG_ARENA_BOT_MIN_SCORE),
		"CONFIG_ARENA_DAILY_AWARD_TM":                          reflect.ValueOf(common.CONFIG_ARENA_DAILY_AWARD_TM),
		"CONFIG_ARENA_DAILY_FREE_FIGHT_NUM":                    reflect.ValueOf(common.CONFIG_ARENA_DAILY_FREE_FIGHT_NUM),
		"CONFIG_ARENA_MAX_DAILY_LIKE_NUM":                      reflect.ValueOf(common.CONFIG_ARENA_MAX_DAILY_LIKE_NUM),
		"CONFIG_ARENA_REFRESH_CD":                              reflect.ValueOf(common.CONFIG_ARENA_REFRESH_CD),
		"CONFIG_ARENA_SCORE_DIFF_LIMIT":                        reflect.ValueOf(common.CONFIG_ARENA_SCORE_DIFF_LIMIT),
		"CONFIG_ARENA_SEASON_RESET_TM":                         reflect.ValueOf(common.CONFIG_ARENA_SEASON_RESET_TM),
		"CONFIG_ARENA_SHOW_MIN_RANK":                           reflect.ValueOf(common.CONFIG_ARENA_SHOW_MIN_RANK),
		"CONFIG_ARTIFACT_EXCHANGE_COST_NUM":                    reflect.ValueOf(common.CONFIG_ARTIFACT_EXCHANGE_COST_NUM),
		"CONFIG_ARTIFACT_GUARANTEE_ID":                         reflect.ValueOf(common.CONFIG_ARTIFACT_GUARANTEE_ID),
		"CONFIG_ASIAN_GVG_SHIELD_TIME":                         reflect.ValueOf(common.CONFIG_ASIAN_GVG_SHIELD_TIME),
		"CONFIG_ASSISTANCE_ACTIVITY_NAME_NUM":                  reflect.ValueOf(common.CONFIG_ASSISTANCE_ACTIVITY_NAME_NUM),
		"CONFIG_ASSISTANT_SHOP_CHOOSE_GOODS_LIMIT":             reflect.ValueOf(common.CONFIG_ASSISTANT_SHOP_CHOOSE_GOODS_LIMIT),
		"CONFIG_AVATAR_USE_ITEM_ONCE":                          reflect.ValueOf(common.CONFIG_AVATAR_USE_ITEM_ONCE),
		"CONFIG_AWAKEN_UNIVERSAL_ITEM":                         reflect.ValueOf(common.CONFIG_AWAKEN_UNIVERSAL_ITEM),
		"CONFIG_BLESSED_HERO_POWER_RATE":                       reflect.ValueOf(common.CONFIG_BLESSED_HERO_POWER_RATE),
		"CONFIG_BOT_NAME_A_MAX":                                reflect.ValueOf(common.CONFIG_BOT_NAME_A_MAX),
		"CONFIG_BOT_NAME_B_MAX":                                reflect.ValueOf(common.CONFIG_BOT_NAME_B_MAX),
		"CONFIG_CHAT_FREQUENCY_TIME":                           reflect.ValueOf(common.CONFIG_CHAT_FREQUENCY_TIME),
		"CONFIG_COMPLIANCE_DURATION":                           reflect.ValueOf(common.CONFIG_COMPLIANCE_DURATION),
		"CONFIG_COMPLIANCE_LIKE_DIAMOND":                       reflect.ValueOf(common.CONFIG_COMPLIANCE_LIKE_DIAMOND),
		"CONFIG_COMPLIANCE_OPEN_DATE":                          reflect.ValueOf(common.CONFIG_COMPLIANCE_OPEN_DATE),
		"CONFIG_COMPLIANCE_OPEN_SERVER":                        reflect.ValueOf(common.CONFIG_COMPLIANCE_OPEN_SERVER),
		"CONFIG_CONFIG_NONE":                                   reflect.ValueOf(common.CONFIG_CONFIG_NONE),
		"CONFIG_DEBUT_SCORE_EXCHANGE":                          reflect.ValueOf(common.CONFIG_DEBUT_SCORE_EXCHANGE),
		"CONFIG_DISPATCH_FORCE_RED_COUNTS":                     reflect.ValueOf(common.CONFIG_DISPATCH_FORCE_RED_COUNTS),
		"CONFIG_DISPATCH_INITIAL_TASK1":                        reflect.ValueOf(common.CONFIG_DISPATCH_INITIAL_TASK1),
		"CONFIG_DISPATCH_INITIAL_TASK2":                        reflect.ValueOf(common.CONFIG_DISPATCH_INITIAL_TASK2),
		"CONFIG_DISPATCH_INITIAL_TASK3":                        reflect.ValueOf(common.CONFIG_DISPATCH_INITIAL_TASK3),
		"CONFIG_DISPATCH_MAX_FINISHED":                         reflect.ValueOf(common.CONFIG_DISPATCH_MAX_FINISHED),
		"CONFIG_DISPATCH_NEW_TASK_NUM":                         reflect.ValueOf(common.CONFIG_DISPATCH_NEW_TASK_NUM),
		"CONFIG_DISPATCH_SPEED_REMAIN_TIME":                    reflect.ValueOf(common.CONFIG_DISPATCH_SPEED_REMAIN_TIME),
		"CONFIG_DROP_ACTIVITY_DUNGEON_RATE":                    reflect.ValueOf(common.CONFIG_DROP_ACTIVITY_DUNGEON_RATE),
		"CONFIG_DUEL_COOLDOWN_TIME":                            reflect.ValueOf(common.CONFIG_DUEL_COOLDOWN_TIME),
		"CONFIG_DUNGEON_MAX_TURN":                              reflect.ValueOf(common.CONFIG_DUNGEON_MAX_TURN),
		"CONFIG_EMBLEM_SLOT_ADD_NUM":                           reflect.ValueOf(common.CONFIG_EMBLEM_SLOT_ADD_NUM),
		"CONFIG_EMBLEM_SLOT_BUY_GROUP":                         reflect.ValueOf(common.CONFIG_EMBLEM_SLOT_BUY_GROUP),
		"CONFIG_EMBLEM_UMDECOMPOSE_RARE":                       reflect.ValueOf(common.CONFIG_EMBLEM_UMDECOMPOSE_RARE),
		"CONFIG_ENCHANT_LIMIT_RARE":                            reflect.ValueOf(common.CONFIG_ENCHANT_LIMIT_RARE),
		"CONFIG_EQUIP_CONFIG_BAG_MAX_LIMIT_ID":                 reflect.ValueOf(common.CONFIG_EQUIP_CONFIG_BAG_MAX_LIMIT_ID),
		"CONFIG_FRAGMENT_COMPOSE_MAX_UNIT_NUM":                 reflect.ValueOf(common.CONFIG_FRAGMENT_COMPOSE_MAX_UNIT_NUM),
		"CONFIG_FRIEND_BLACK_MAX_NUM":                          reflect.ValueOf(common.CONFIG_FRIEND_BLACK_MAX_NUM),
		"CONFIG_FRIEND_MAX_NUM":                                reflect.ValueOf(common.CONFIG_FRIEND_MAX_NUM),
		"CONFIG_FRIEND_RECEIVE_PER_GOLD":                       reflect.ValueOf(common.CONFIG_FRIEND_RECEIVE_PER_GOLD),
		"CONFIG_FRIEND_RECEIVE_PER_POINT":                      reflect.ValueOf(common.CONFIG_FRIEND_RECEIVE_PER_POINT),
		"CONFIG_FRIEND_RECOMMEND_ACTIVE_DAY":                   reflect.ValueOf(common.CONFIG_FRIEND_RECOMMEND_ACTIVE_DAY),
		"CONFIG_FRIEND_RECOMMEND_MAX_LEVEL":                    reflect.ValueOf(common.CONFIG_FRIEND_RECOMMEND_MAX_LEVEL),
		"CONFIG_FRIEND_RECOMMEND_MIN_LEVEL":                    reflect.ValueOf(common.CONFIG_FRIEND_RECOMMEND_MIN_LEVEL),
		"CONFIG_FRIEND_RECOMMEND_NUM":                          reflect.ValueOf(common.CONFIG_FRIEND_RECOMMEND_NUM),
		"CONFIG_FRIEND_RECOMMEND_RANGE_LEVEL":                  reflect.ValueOf(common.CONFIG_FRIEND_RECOMMEND_RANGE_LEVEL),
		"CONFIG_FRIEND_RECV_MAX_COUNT":                         reflect.ValueOf(common.CONFIG_FRIEND_RECV_MAX_COUNT),
		"CONFIG_FRIEND_REQUEST_MAX_NUM":                        reflect.ValueOf(common.CONFIG_FRIEND_REQUEST_MAX_NUM),
		"CONFIG_FRIEND_SEND_MAX_COUNT":                         reflect.ValueOf(common.CONFIG_FRIEND_SEND_MAX_COUNT),
		"CONFIG_GODDESS_CONTRACT_LEVEL_UP_ITEM":                reflect.ValueOf(common.CONFIG_GODDESS_CONTRACT_LEVEL_UP_ITEM),
		"CONFIG_GODDESS_TOUCH_LIMIT":                           reflect.ValueOf(common.CONFIG_GODDESS_TOUCH_LIMIT),
		"CONFIG_GOD_PRESENT_HERO_REDUCE_RARE":                  reflect.ValueOf(common.CONFIG_GOD_PRESENT_HERO_REDUCE_RARE),
		"CONFIG_GOODS_BUY_MAX_NUM":                             reflect.ValueOf(common.CONFIG_GOODS_BUY_MAX_NUM),
		"CONFIG_HANDBOOK_HERO_AWARD":                           reflect.ValueOf(common.CONFIG_HANDBOOK_HERO_AWARD),
		"CONFIG_HERO_AWAKEN_CONVERT_RES_LIMIT":                 reflect.ValueOf(common.CONFIG_HERO_AWAKEN_CONVERT_RES_LIMIT),
		"CONFIG_HERO_BAG_BUY_GROUP":                            reflect.ValueOf(common.CONFIG_HERO_BAG_BUY_GROUP),
		"CONFIG_HERO_CONVERT_NUM":                              reflect.ValueOf(common.CONFIG_HERO_CONVERT_NUM),
		"CONFIG_HERO_CONVERT_OPEN":                             reflect.ValueOf(common.CONFIG_HERO_CONVERT_OPEN),
		"CONFIG_HERO_DISBAND_MAX_NUM":                          reflect.ValueOf(common.CONFIG_HERO_DISBAND_MAX_NUM),
		"CONFIG_HERO_LEVEL_ONEKEY_NUM":                         reflect.ValueOf(common.CONFIG_HERO_LEVEL_ONEKEY_NUM),
		"CONFIG_HERO_LEVEL_ONEKEY_OFF":                         reflect.ValueOf(common.CONFIG_HERO_LEVEL_ONEKEY_OFF),
		"CONFIG_HERO_SLOT_ADD_COST":                            reflect.ValueOf(common.CONFIG_HERO_SLOT_ADD_COST),
		"CONFIG_HERO_SLOT_ADD_NUM":                             reflect.ValueOf(common.CONFIG_HERO_SLOT_ADD_NUM),
		"CONFIG_HERO_SLOT_MAX_NUM":                             reflect.ValueOf(common.CONFIG_HERO_SLOT_MAX_NUM),
		"CONFIG_HERO_SLOT_MIN_NUM":                             reflect.ValueOf(common.CONFIG_HERO_SLOT_MIN_NUM),
		"CONFIG_HERO_TRANSLATION":                              reflect.ValueOf(common.CONFIG_HERO_TRANSLATION),
		"CONFIG_HOT_RANK_REFRESH_TIME":                         reflect.ValueOf(common.CONFIG_HOT_RANK_REFRESH_TIME),
		"CONFIG_ITEM_SELL_MAX_NUM":                             reflect.ValueOf(common.CONFIG_ITEM_SELL_MAX_NUM),
		"CONFIG_ITEM_USE_MAX_NUM":                              reflect.ValueOf(common.CONFIG_ITEM_USE_MAX_NUM),
		"CONFIG_LINK1_UNLOCK_HEROSTAR":                         reflect.ValueOf(common.CONFIG_LINK1_UNLOCK_HEROSTAR),
		"CONFIG_LINK2_UNLOCK_HEROSTAR":                         reflect.ValueOf(common.CONFIG_LINK2_UNLOCK_HEROSTAR),
		"CONFIG_LINK3_UNLOCK_HEROSTAR":                         reflect.ValueOf(common.CONFIG_LINK3_UNLOCK_HEROSTAR),
		"CONFIG_LINK4_UNLOCK_HERO_STAR":                        reflect.ValueOf(common.CONFIG_LINK4_UNLOCK_HERO_STAR),
		"CONFIG_LINK5_ID":                                      reflect.ValueOf(common.CONFIG_LINK5_ID),
		"CONFIG_LINK5_UNLOCK_HEROSTAR":                         reflect.ValueOf(common.CONFIG_LINK5_UNLOCK_HEROSTAR),
		"CONFIG_LINK5_UNLOCK_HEROSTAR_PRE":                     reflect.ValueOf(common.CONFIG_LINK5_UNLOCK_HEROSTAR_PRE),
		"CONFIG_MAIL_NUM_LIMIT":                                reflect.ValueOf(common.CONFIG_MAIL_NUM_LIMIT),
		"CONFIG_MAIL_TIME_LIMIT":                               reflect.ValueOf(common.CONFIG_MAIL_TIME_LIMIT),
		"CONFIG_MAX_GIFTS_SEND_NUM":                            reflect.ValueOf(common.CONFIG_MAX_GIFTS_SEND_NUM),
		"CONFIG_MIRAGE_BUY_COUNT_COST_ITEM_ID":                 reflect.ValueOf(common.CONFIG_MIRAGE_BUY_COUNT_COST_ITEM_ID),
		"CONFIG_NEW_YEAR_ACTIVITY_SHEILD_DAY":                  reflect.ValueOf(common.CONFIG_NEW_YEAR_ACTIVITY_SHEILD_DAY),
		"CONFIG_ONHOOK_MAX_TIME":                               reflect.ValueOf(common.CONFIG_ONHOOK_MAX_TIME),
		"CONFIG_OSS_OPEN_LV":                                   reflect.ValueOf(common.CONFIG_OSS_OPEN_LV),
		"CONFIG_OVERALL_RATING_MULTIPLE":                       reflect.ValueOf(common.CONFIG_OVERALL_RATING_MULTIPLE),
		"CONFIG_RECHARGE_BY_COUPON_COST":                       reflect.ValueOf(common.CONFIG_RECHARGE_BY_COUPON_COST),
		"CONFIG_RECHARGE_REFUND_DUNGEON_CN":                    reflect.ValueOf(common.CONFIG_RECHARGE_REFUND_DUNGEON_CN),
		"CONFIG_RECHARGE_REFUND_TIME_CN":                       reflect.ValueOf(common.CONFIG_RECHARGE_REFUND_TIME_CN),
		"CONFIG_RITE_10_RECYCLE_POINT":                         reflect.ValueOf(common.CONFIG_RITE_10_RECYCLE_POINT),
		"CONFIG_RITE_20_RECYCLE_POINT":                         reflect.ValueOf(common.CONFIG_RITE_20_RECYCLE_POINT),
		"CONFIG_RITE_30_RECYCLE_POINT":                         reflect.ValueOf(common.CONFIG_RITE_30_RECYCLE_POINT),
		"CONFIG_RITE_40_RECYCLE_POINT":                         reflect.ValueOf(common.CONFIG_RITE_40_RECYCLE_POINT),
		"CONFIG_RITE_50_RECYCLE_POINT":                         reflect.ValueOf(common.CONFIG_RITE_50_RECYCLE_POINT),
		"CONFIG_RITE_60_RECYCLE_POINT":                         reflect.ValueOf(common.CONFIG_RITE_60_RECYCLE_POINT),
		"CONFIG_RITE_POWER_RECYCLE_POINT":                      reflect.ValueOf(common.CONFIG_RITE_POWER_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_10_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_10_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_20_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_20_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_30_LV_RECYCLE_POINT":               reflect.ValueOf(common.CONFIG_SEASON_LINK_30_LV_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_30_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_30_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_40_LV_RECYCLE_POINT":               reflect.ValueOf(common.CONFIG_SEASON_LINK_40_LV_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_40_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_40_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_50_LV_RECYCLE_POINT":               reflect.ValueOf(common.CONFIG_SEASON_LINK_50_LV_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_50_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_50_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_60_LV_RECYCLE_POINT":               reflect.ValueOf(common.CONFIG_SEASON_LINK_60_LV_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_60_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_60_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_70_RARE_RECYCLE_POINT":             reflect.ValueOf(common.CONFIG_SEASON_LINK_70_RARE_RECYCLE_POINT),
		"CONFIG_SEASON_LINK_ACTIVE_ITEM":                       reflect.ValueOf(common.CONFIG_SEASON_LINK_ACTIVE_ITEM),
		"CONFIG_SEASON_LINK_ACTIVE_ITEM_COUNT":                 reflect.ValueOf(common.CONFIG_SEASON_LINK_ACTIVE_ITEM_COUNT),
		"CONFIG_SEASON_OPEN_SERVER_DAY_LIMIT":                  reflect.ValueOf(common.CONFIG_SEASON_OPEN_SERVER_DAY_LIMIT),
		"CONFIG_SEASON_RETURN_RECEIVE":                         reflect.ValueOf(common.CONFIG_SEASON_RETURN_RECEIVE),
		"CONFIG_SKIN_DECOMPOSE_DIAMOND_LIMIT":                  reflect.ValueOf(common.CONFIG_SKIN_DECOMPOSE_DIAMOND_LIMIT),
		"CONFIG_SKIN_DECOMPOSE_GOLD_LIMIT":                     reflect.ValueOf(common.CONFIG_SKIN_DECOMPOSE_GOLD_LIMIT),
		"CONFIG_SOCIETY_MAIL_DIAMOND_AWARD":                    reflect.ValueOf(common.CONFIG_SOCIETY_MAIL_DIAMOND_AWARD),
		"CONFIG_SOCIETY_MAIL_PUSH_LEVEL":                       reflect.ValueOf(common.CONFIG_SOCIETY_MAIL_PUSH_LEVEL),
		"CONFIG_TOWERSTAR_DYNAMIC_FORMATION_OPEN_CHAPTER":      reflect.ValueOf(common.CONFIG_TOWERSTAR_DYNAMIC_FORMATION_OPEN_CHAPTER),
		"CONFIG_TURNTABLE_RANDOM_BUFF_NUM":                     reflect.ValueOf(common.CONFIG_TURNTABLE_RANDOM_BUFF_NUM),
		"CONFIG_TURNTABLE_TICKETS_BUY_DAY":                     reflect.ValueOf(common.CONFIG_TURNTABLE_TICKETS_BUY_DAY),
		"CONFIG_USER_NAME_MAX_LENGTH":                          reflect.ValueOf(common.CONFIG_USER_NAME_MAX_LENGTH),
		"CONFIG_USER_NAME_SET_COST":                            reflect.ValueOf(common.CONFIG_USER_NAME_SET_COST),
		"CONFIG_WISHLIST_CD":                                   reflect.ValueOf(common.CONFIG_WISHLIST_CD),
		"CONFIG_WISHLIST_CD_NEW":                               reflect.ValueOf(common.CONFIG_WISHLIST_CD_NEW),
		"CONFIG_WISHLIST_RESET_TIMES":                          reflect.ValueOf(common.CONFIG_WISHLIST_RESET_TIMES),
		"CONFIG_WISHLIST_RESET_VALUE":                          reflect.ValueOf(common.CONFIG_WISHLIST_RESET_VALUE),
		"CONFIG_name":                                          reflect.ValueOf(&common.CONFIG_name).Elem(),
		"CONFIG_value":                                         reflect.ValueOf(&common.CONFIG_value).Elem(),
		"CROSS_ARENA_TIME_CAT_NONE":                            reflect.ValueOf(common.CROSS_ARENA_TIME_CAT_NONE),
		"CROSS_ARENA_TIME_CAT_RESET_BEGIN":                     reflect.ValueOf(common.CROSS_ARENA_TIME_CAT_RESET_BEGIN),
		"CROSS_ARENA_TIME_CAT_RESET_END":                       reflect.ValueOf(common.CROSS_ARENA_TIME_CAT_RESET_END),
		"CROSS_ARENA_TIME_name":                                reflect.ValueOf(&common.CROSS_ARENA_TIME_name).Elem(),
		"CROSS_ARENA_TIME_value":                               reflect.ValueOf(&common.CROSS_ARENA_TIME_value).Elem(),
		"CRYSTAL_SHARE_ATTR_TYPE_CSAT_ALL":                     reflect.ValueOf(common.CRYSTAL_SHARE_ATTR_TYPE_CSAT_ALL),
		"CRYSTAL_SHARE_ATTR_TYPE_CSAT_EMBLEM":                  reflect.ValueOf(common.CRYSTAL_SHARE_ATTR_TYPE_CSAT_EMBLEM),
		"CRYSTAL_SHARE_ATTR_TYPE_CSAT_EQUIPMENT":               reflect.ValueOf(common.CRYSTAL_SHARE_ATTR_TYPE_CSAT_EQUIPMENT),
		"CRYSTAL_SHARE_ATTR_TYPE_CSAT_GEM":                     reflect.ValueOf(common.CRYSTAL_SHARE_ATTR_TYPE_CSAT_GEM),
		"CRYSTAL_SHARE_ATTR_TYPE_CSAT_HERO":                    reflect.ValueOf(common.CRYSTAL_SHARE_ATTR_TYPE_CSAT_HERO),
		"CRYSTAL_SHARE_ATTR_TYPE_CSAT_NONE":                    reflect.ValueOf(common.CRYSTAL_SHARE_ATTR_TYPE_CSAT_NONE),
		"CRYSTAL_SHARE_ATTR_TYPE_name":                         reflect.ValueOf(&common.CRYSTAL_SHARE_ATTR_TYPE_name).Elem(),
		"CRYSTAL_SHARE_ATTR_TYPE_value":                        reflect.ValueOf(&common.CRYSTAL_SHARE_ATTR_TYPE_value).Elem(),
		"DAILY_ATTENDANCE_AWARD_DAA_BOTH":                      reflect.ValueOf(common.DAILY_ATTENDANCE_AWARD_DAA_BOTH),
		"DAILY_ATTENDANCE_AWARD_DAA_FIRST":                     reflect.ValueOf(common.DAILY_ATTENDANCE_AWARD_DAA_FIRST),
		"DAILY_ATTENDANCE_AWARD_DAA_NONE":                      reflect.ValueOf(common.DAILY_ATTENDANCE_AWARD_DAA_NONE),
		"DAILY_ATTENDANCE_AWARD_DAA_SECOND":                    reflect.ValueOf(common.DAILY_ATTENDANCE_AWARD_DAA_SECOND),
		"DAILY_ATTENDANCE_AWARD_name":                          reflect.ValueOf(&common.DAILY_ATTENDANCE_AWARD_name).Elem(),
		"DAILY_ATTENDANCE_AWARD_value":                         reflect.ValueOf(&common.DAILY_ATTENDANCE_AWARD_value).Elem(),
		"DAILY_SPECIAL_RECV_AWARD_DSRA_DAILY":                  reflect.ValueOf(common.DAILY_SPECIAL_RECV_AWARD_DSRA_DAILY),
		"DAILY_SPECIAL_RECV_AWARD_DSRA_NONE":                   reflect.ValueOf(common.DAILY_SPECIAL_RECV_AWARD_DSRA_NONE),
		"DAILY_SPECIAL_RECV_AWARD_DSRA_SCORE":                  reflect.ValueOf(common.DAILY_SPECIAL_RECV_AWARD_DSRA_SCORE),
		"DAILY_SPECIAL_RECV_AWARD_name":                        reflect.ValueOf(&common.DAILY_SPECIAL_RECV_AWARD_name).Elem(),
		"DAILY_SPECIAL_RECV_AWARD_value":                       reflect.ValueOf(&common.DAILY_SPECIAL_RECV_AWARD_value).Elem(),
		"DAILY_WISH_WEIGHT_END_ID":                             reflect.ValueOf(common.DAILY_WISH_WEIGHT_END_ID),
		"DAILY_WISH_WEIGHT_MAX":                                reflect.ValueOf(common.DAILY_WISH_WEIGHT_MAX),
		"DAILY_WISH_WEIGHT_NONE":                               reflect.ValueOf(common.DAILY_WISH_WEIGHT_NONE),
		"DAILY_WISH_WEIGHT_START_ID":                           reflect.ValueOf(common.DAILY_WISH_WEIGHT_START_ID),
		"DAILY_WISH_name":                                      reflect.ValueOf(&common.DAILY_WISH_name).Elem(),
		"DAILY_WISH_value":                                     reflect.ValueOf(&common.DAILY_WISH_value).Elem(),
		"DB_BASE_USER_CHAT_BUBBLES_EXPIRED":                    reflect.ValueOf(common.DB_BASE_USER_CHAT_BUBBLES_EXPIRED),
		"DB_BASE_USER_FARM_EXPIRED":                            reflect.ValueOf(common.DB_BASE_USER_FARM_EXPIRED),
		"DB_BASE_USER_ICON_EXPIRED":                            reflect.ValueOf(common.DB_BASE_USER_ICON_EXPIRED),
		"DB_BASE_USER_IMAGE_EXPIRED":                           reflect.ValueOf(common.DB_BASE_USER_IMAGE_EXPIRED),
		"DB_BASE_USER_MAX_EXPIRED":                             reflect.ValueOf(common.DB_BASE_USER_MAX_EXPIRED),
		"DB_BASE_USER_TITLE_EXPIRED":                           reflect.ValueOf(common.DB_BASE_USER_TITLE_EXPIRED),
		"DB_BASE_USER_name":                                    reflect.ValueOf(&common.DB_BASE_USER_name).Elem(),
		"DB_BASE_USER_value":                                   reflect.ValueOf(&common.DB_BASE_USER_value).Elem(),
		"DEL_MAIL_TYPE_MAIL_CHANGE":                            reflect.ValueOf(common.DEL_MAIL_TYPE_MAIL_CHANGE),
		"DEL_MAIL_TYPE_MAIL_DELETE":                            reflect.ValueOf(common.DEL_MAIL_TYPE_MAIL_DELETE),
		"DEL_MAIL_TYPE_MAIL_NO_CHANGE":                         reflect.ValueOf(common.DEL_MAIL_TYPE_MAIL_NO_CHANGE),
		"DEL_MAIL_TYPE_name":                                   reflect.ValueOf(&common.DEL_MAIL_TYPE_name).Elem(),
		"DEL_MAIL_TYPE_value":                                  reflect.ValueOf(&common.DEL_MAIL_TYPE_value).Elem(),
		"DIAMOND_TYPE_PRESENT":                                 reflect.ValueOf(common.DIAMOND_TYPE_PRESENT),
		"DIAMOND_TYPE_RECHARGE":                                reflect.ValueOf(common.DIAMOND_TYPE_RECHARGE),
		"DIAMOND_TYPE_name":                                    reflect.ValueOf(&common.DIAMOND_TYPE_name).Elem(),
		"DIAMOND_TYPE_value":                                   reflect.ValueOf(&common.DIAMOND_TYPE_value).Elem(),
		"DISORDER_LAND_ADD_DLA_BATTLE":                         reflect.ValueOf(common.DISORDER_LAND_ADD_DLA_BATTLE),
		"DISORDER_LAND_ADD_DLA_MAX":                            reflect.ValueOf(common.DISORDER_LAND_ADD_DLA_MAX),
		"DISORDER_LAND_ADD_DLA_NONE":                           reflect.ValueOf(common.DISORDER_LAND_ADD_DLA_NONE),
		"DISORDER_LAND_ADD_DLA_STONE":                          reflect.ValueOf(common.DISORDER_LAND_ADD_DLA_STONE),
		"DISORDER_LAND_ADD_name":                               reflect.ValueOf(&common.DISORDER_LAND_ADD_name).Elem(),
		"DISORDER_LAND_ADD_value":                              reflect.ValueOf(&common.DISORDER_LAND_ADD_value).Elem(),
		"DISORDER_LAND_BATTLE_DLB_BATTLE":                      reflect.ValueOf(common.DISORDER_LAND_BATTLE_DLB_BATTLE),
		"DISORDER_LAND_BATTLE_DLB_MAX":                         reflect.ValueOf(common.DISORDER_LAND_BATTLE_DLB_MAX),
		"DISORDER_LAND_BATTLE_DLB_NONE":                        reflect.ValueOf(common.DISORDER_LAND_BATTLE_DLB_NONE),
		"DISORDER_LAND_BATTLE_DLB_SWEEP":                       reflect.ValueOf(common.DISORDER_LAND_BATTLE_DLB_SWEEP),
		"DISORDER_LAND_BATTLE_name":                            reflect.ValueOf(&common.DISORDER_LAND_BATTLE_name).Elem(),
		"DISORDER_LAND_BATTLE_value":                           reflect.ValueOf(&common.DISORDER_LAND_BATTLE_value).Elem(),
		"DISORDER_LAND_BUY_TYPE_DLBT_DIAMOND":                  reflect.ValueOf(common.DISORDER_LAND_BUY_TYPE_DLBT_DIAMOND),
		"DISORDER_LAND_BUY_TYPE_DLBT_ITEM":                     reflect.ValueOf(common.DISORDER_LAND_BUY_TYPE_DLBT_ITEM),
		"DISORDER_LAND_BUY_TYPE_DLBT_NONE":                     reflect.ValueOf(common.DISORDER_LAND_BUY_TYPE_DLBT_NONE),
		"DISORDER_LAND_BUY_TYPE_name":                          reflect.ValueOf(&common.DISORDER_LAND_BUY_TYPE_name).Elem(),
		"DISORDER_LAND_BUY_TYPE_value":                         reflect.ValueOf(&common.DISORDER_LAND_BUY_TYPE_value).Elem(),
		"DISORDER_LAND_EVENT_TYPE_DLET_BATTLE":                 reflect.ValueOf(common.DISORDER_LAND_EVENT_TYPE_DLET_BATTLE),
		"DISORDER_LAND_EVENT_TYPE_DLET_BOX":                    reflect.ValueOf(common.DISORDER_LAND_EVENT_TYPE_DLET_BOX),
		"DISORDER_LAND_EVENT_TYPE_DLET_MAX":                    reflect.ValueOf(common.DISORDER_LAND_EVENT_TYPE_DLET_MAX),
		"DISORDER_LAND_EVENT_TYPE_DLET_NONE":                   reflect.ValueOf(common.DISORDER_LAND_EVENT_TYPE_DLET_NONE),
		"DISORDER_LAND_EVENT_TYPE_DLET_RELIC":                  reflect.ValueOf(common.DISORDER_LAND_EVENT_TYPE_DLET_RELIC),
		"DISORDER_LAND_EVENT_TYPE_DLET_STONE":                  reflect.ValueOf(common.DISORDER_LAND_EVENT_TYPE_DLET_STONE),
		"DISORDER_LAND_EVENT_TYPE_name":                        reflect.ValueOf(&common.DISORDER_LAND_EVENT_TYPE_name).Elem(),
		"DISORDER_LAND_EVENT_TYPE_value":                       reflect.ValueOf(&common.DISORDER_LAND_EVENT_TYPE_value).Elem(),
		"DISORDER_LAND_GROUP_TAG_DLGT_MAX":                     reflect.ValueOf(common.DISORDER_LAND_GROUP_TAG_DLGT_MAX),
		"DISORDER_LAND_GROUP_TAG_DLGT_NONE":                    reflect.ValueOf(common.DISORDER_LAND_GROUP_TAG_DLGT_NONE),
		"DISORDER_LAND_GROUP_TAG_DLGT_TAG1":                    reflect.ValueOf(common.DISORDER_LAND_GROUP_TAG_DLGT_TAG1),
		"DISORDER_LAND_GROUP_TAG_DLGT_TAG2":                    reflect.ValueOf(common.DISORDER_LAND_GROUP_TAG_DLGT_TAG2),
		"DISORDER_LAND_GROUP_TAG_DLGT_TAG3":                    reflect.ValueOf(common.DISORDER_LAND_GROUP_TAG_DLGT_TAG3),
		"DISORDER_LAND_GROUP_TAG_name":                         reflect.ValueOf(&common.DISORDER_LAND_GROUP_TAG_name).Elem(),
		"DISORDER_LAND_GROUP_TAG_value":                        reflect.ValueOf(&common.DISORDER_LAND_GROUP_TAG_value).Elem(),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_A":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_A),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_B":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_B),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_C":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_C),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_D":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_D),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_E":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_E),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_F":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_F),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_G":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_G),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_H":                     reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_H),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_MAX":                   reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_MAX),
		"DISORDER_LAND_HURDLE_TYPE_DLHT_NONE":                  reflect.ValueOf(common.DISORDER_LAND_HURDLE_TYPE_DLHT_NONE),
		"DISORDER_LAND_HURDLE_TYPE_name":                       reflect.ValueOf(&common.DISORDER_LAND_HURDLE_TYPE_name).Elem(),
		"DISORDER_LAND_HURDLE_TYPE_value":                      reflect.ValueOf(&common.DISORDER_LAND_HURDLE_TYPE_value).Elem(),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_EIGHT":               reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_EIGHT),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_ELEVEN":              reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_ELEVEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_FIFTEEN":             reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_FIFTEEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_FIVE":                reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_FIVE),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_FOUR":                reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_FOUR),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_FOURTEEN":            reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_FOURTEEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_MAX":                 reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_MAX),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_NINE":                reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_NINE),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_SEVEN":               reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_SEVEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_SIX":                 reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_SIX),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_SIXTEEN":             reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_SIXTEEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_TEN":                 reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_TEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_THIRTEEN":            reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_THIRTEEN),
		"DISORDER_LAND_LEVEL_DLL_DISORDER_TWELVE":              reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_DISORDER_TWELVE),
		"DISORDER_LAND_LEVEL_DLL_HARD":                         reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_HARD),
		"DISORDER_LAND_LEVEL_DLL_NIGHT_MIRE":                   reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_NIGHT_MIRE),
		"DISORDER_LAND_LEVEL_DLL_NONE":                         reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_NONE),
		"DISORDER_LAND_LEVEL_DLL_NORMAL":                       reflect.ValueOf(common.DISORDER_LAND_LEVEL_DLL_NORMAL),
		"DISORDER_LAND_LEVEL_name":                             reflect.ValueOf(&common.DISORDER_LAND_LEVEL_name).Elem(),
		"DISORDER_LAND_LEVEL_value":                            reflect.ValueOf(&common.DISORDER_LAND_LEVEL_value).Elem(),
		"DISORDER_LAND_NODE_UNLOCK_DLNU_ALL":                   reflect.ValueOf(common.DISORDER_LAND_NODE_UNLOCK_DLNU_ALL),
		"DISORDER_LAND_NODE_UNLOCK_DLNU_ANY":                   reflect.ValueOf(common.DISORDER_LAND_NODE_UNLOCK_DLNU_ANY),
		"DISORDER_LAND_NODE_UNLOCK_DLNU_MAX":                   reflect.ValueOf(common.DISORDER_LAND_NODE_UNLOCK_DLNU_MAX),
		"DISORDER_LAND_NODE_UNLOCK_DLNU_NONE":                  reflect.ValueOf(common.DISORDER_LAND_NODE_UNLOCK_DLNU_NONE),
		"DISORDER_LAND_NODE_UNLOCK_DLNU_UNLOCK":                reflect.ValueOf(common.DISORDER_LAND_NODE_UNLOCK_DLNU_UNLOCK),
		"DISORDER_LAND_NODE_UNLOCK_name":                       reflect.ValueOf(&common.DISORDER_LAND_NODE_UNLOCK_name).Elem(),
		"DISORDER_LAND_NODE_UNLOCK_value":                      reflect.ValueOf(&common.DISORDER_LAND_NODE_UNLOCK_value).Elem(),
		"DISORDER_LAND_RARE_DLR_BLUE":                          reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_BLUE),
		"DISORDER_LAND_RARE_DLR_GREEN":                         reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_GREEN),
		"DISORDER_LAND_RARE_DLR_MAX":                           reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_MAX),
		"DISORDER_LAND_RARE_DLR_MIGHT":                         reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_MIGHT),
		"DISORDER_LAND_RARE_DLR_NONE":                          reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_NONE),
		"DISORDER_LAND_RARE_DLR_ORANGE":                        reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_ORANGE),
		"DISORDER_LAND_RARE_DLR_PURPLE":                        reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_PURPLE),
		"DISORDER_LAND_RARE_DLR_RED":                           reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_RED),
		"DISORDER_LAND_RARE_DLR_WHITE":                         reflect.ValueOf(common.DISORDER_LAND_RARE_DLR_WHITE),
		"DISORDER_LAND_RARE_name":                              reflect.ValueOf(&common.DISORDER_LAND_RARE_name).Elem(),
		"DISORDER_LAND_RARE_value":                             reflect.ValueOf(&common.DISORDER_LAND_RARE_value).Elem(),
		"DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_DIAMOND":          reflect.ValueOf(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_DIAMOND),
		"DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_MAX":              reflect.ValueOf(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_MAX),
		"DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_NONE":             reflect.ValueOf(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_NONE),
		"DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_SUMMON_FREE":      reflect.ValueOf(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_SUMMON_FREE),
		"DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_SUMMON_ITEM":      reflect.ValueOf(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_SUMMON_ITEM),
		"DIVINE_DEMON_SUMMON_COST_TYPE_name":                   reflect.ValueOf(&common.DIVINE_DEMON_SUMMON_COST_TYPE_name).Elem(),
		"DIVINE_DEMON_SUMMON_COST_TYPE_value":                  reflect.ValueOf(&common.DIVINE_DEMON_SUMMON_COST_TYPE_value).Elem(),
		"DIVINE_DEMON_SUMMON_TYPE_DSN_MANY":                    reflect.ValueOf(common.DIVINE_DEMON_SUMMON_TYPE_DSN_MANY),
		"DIVINE_DEMON_SUMMON_TYPE_DSN_SINGLE":                  reflect.ValueOf(common.DIVINE_DEMON_SUMMON_TYPE_DSN_SINGLE),
		"DIVINE_DEMON_SUMMON_TYPE_DST_NONE":                    reflect.ValueOf(common.DIVINE_DEMON_SUMMON_TYPE_DST_NONE),
		"DIVINE_DEMON_SUMMON_TYPE_name":                        reflect.ValueOf(&common.DIVINE_DEMON_SUMMON_TYPE_name).Elem(),
		"DIVINE_DEMON_SUMMON_TYPE_value":                       reflect.ValueOf(&common.DIVINE_DEMON_SUMMON_TYPE_value).Elem(),
		"DIVINE_DEMON_TASK_TYPE_DTT_ACTIVE":                    reflect.ValueOf(common.DIVINE_DEMON_TASK_TYPE_DTT_ACTIVE),
		"DIVINE_DEMON_TASK_TYPE_DTT_NONE":                      reflect.ValueOf(common.DIVINE_DEMON_TASK_TYPE_DTT_NONE),
		"DIVINE_DEMON_TASK_TYPE_DTT_SUMMON":                    reflect.ValueOf(common.DIVINE_DEMON_TASK_TYPE_DTT_SUMMON),
		"DIVINE_DEMON_TASK_TYPE_DTT_UP_STAR":                   reflect.ValueOf(common.DIVINE_DEMON_TASK_TYPE_DTT_UP_STAR),
		"DIVINE_DEMON_TASK_TYPE_name":                          reflect.ValueOf(&common.DIVINE_DEMON_TASK_TYPE_name).Elem(),
		"DIVINE_DEMON_TASK_TYPE_value":                         reflect.ValueOf(&common.DIVINE_DEMON_TASK_TYPE_value).Elem(),
		"DROP_TYPE_DROP_TYPE_NONE":                             reflect.ValueOf(common.DROP_TYPE_DROP_TYPE_NONE),
		"DROP_TYPE_DT_RECURSION":                               reflect.ValueOf(common.DROP_TYPE_DT_RECURSION),
		"DROP_TYPE_name":                                       reflect.ValueOf(&common.DROP_TYPE_name).Elem(),
		"DROP_TYPE_value":                                      reflect.ValueOf(&common.DROP_TYPE_value).Elem(),
		"DUEL_STATUS_TYPE_DSST_CLOSE":                          reflect.ValueOf(common.DUEL_STATUS_TYPE_DSST_CLOSE),
		"DUEL_STATUS_TYPE_DSST_NONE":                           reflect.ValueOf(common.DUEL_STATUS_TYPE_DSST_NONE),
		"DUEL_STATUS_TYPE_DSST_OPEN":                           reflect.ValueOf(common.DUEL_STATUS_TYPE_DSST_OPEN),
		"DUEL_STATUS_TYPE_name":                                reflect.ValueOf(&common.DUEL_STATUS_TYPE_name).Elem(),
		"DUEL_STATUS_TYPE_value":                               reflect.ValueOf(&common.DUEL_STATUS_TYPE_value).Elem(),
		"EMBLEM_AFFIX_TYPE_EAT_ANCIENT":                        reflect.ValueOf(common.EMBLEM_AFFIX_TYPE_EAT_ANCIENT),
		"EMBLEM_AFFIX_TYPE_EAT_ARCHAIC":                        reflect.ValueOf(common.EMBLEM_AFFIX_TYPE_EAT_ARCHAIC),
		"EMBLEM_AFFIX_TYPE_EAT_NONE":                           reflect.ValueOf(common.EMBLEM_AFFIX_TYPE_EAT_NONE),
		"EMBLEM_AFFIX_TYPE_name":                               reflect.ValueOf(&common.EMBLEM_AFFIX_TYPE_name).Elem(),
		"EMBLEM_AFFIX_TYPE_value":                              reflect.ValueOf(&common.EMBLEM_AFFIX_TYPE_value).Elem(),
		"EMBLEM_CONFIG_BAG_MAX_LIMIT_ID":                       reflect.ValueOf(common.EMBLEM_CONFIG_BAG_MAX_LIMIT_ID),
		"EMBLEM_CONFIG_EMBLEM_NONE":                            reflect.ValueOf(common.EMBLEM_CONFIG_EMBLEM_NONE),
		"EMBLEM_CONFIG_MAX_LEVELUP_COUNT":                      reflect.ValueOf(common.EMBLEM_CONFIG_MAX_LEVELUP_COUNT),
		"EMBLEM_CONFIG_SLOT_FOUR_UNLOCK":                       reflect.ValueOf(common.EMBLEM_CONFIG_SLOT_FOUR_UNLOCK),
		"EMBLEM_CONFIG_SLOT_ONE_UNLOCK":                        reflect.ValueOf(common.EMBLEM_CONFIG_SLOT_ONE_UNLOCK),
		"EMBLEM_CONFIG_SLOT_THREE_UNLOCK":                      reflect.ValueOf(common.EMBLEM_CONFIG_SLOT_THREE_UNLOCK),
		"EMBLEM_CONFIG_SLOT_TWO_UNLOCK":                        reflect.ValueOf(common.EMBLEM_CONFIG_SLOT_TWO_UNLOCK),
		"EMBLEM_CONFIG_name":                                   reflect.ValueOf(&common.EMBLEM_CONFIG_name).Elem(),
		"EMBLEM_CONFIG_value":                                  reflect.ValueOf(&common.EMBLEM_CONFIG_value).Elem(),
		"EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_LOCK":    reflect.ValueOf(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_LOCK),
		"EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_NONE":    reflect.ValueOf(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_NONE),
		"EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_SAVE":    reflect.ValueOf(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_SAVE),
		"EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_name":            reflect.ValueOf(&common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_name).Elem(),
		"EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_value":           reflect.ValueOf(&common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_value).Elem(),
		"EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_NONE":                  reflect.ValueOf(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_NONE),
		"EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SKILL":                 reflect.ValueOf(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SKILL),
		"EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SUIT":                  reflect.ValueOf(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SUIT),
		"EMBLEM_SUCCINCT_LOCK_TYPE_name":                       reflect.ValueOf(&common.EMBLEM_SUCCINCT_LOCK_TYPE_name).Elem(),
		"EMBLEM_SUCCINCT_LOCK_TYPE_value":                      reflect.ValueOf(&common.EMBLEM_SUCCINCT_LOCK_TYPE_value).Elem(),
		"EQUIPMENT_ATRR_ADD":                                   reflect.ValueOf(common.EQUIPMENT_ATRR_ADD),
		"EQUIPMENT_ATRR_EQUIPMENT_ATRR_NONE":                   reflect.ValueOf(common.EQUIPMENT_ATRR_EQUIPMENT_ATRR_NONE),
		"EQUIPMENT_ATRR_PRO":                                   reflect.ValueOf(common.EQUIPMENT_ATRR_PRO),
		"EQUIPMENT_ATRR_name":                                  reflect.ValueOf(&common.EQUIPMENT_ATRR_name).Elem(),
		"EQUIPMENT_ATRR_value":                                 reflect.ValueOf(&common.EQUIPMENT_ATRR_value).Elem(),
		"FIRST_GIFT_REWARD_DAY_FGRD_DAY_ONE":                   reflect.ValueOf(common.FIRST_GIFT_REWARD_DAY_FGRD_DAY_ONE),
		"FIRST_GIFT_REWARD_DAY_FGRD_DAY_THREE":                 reflect.ValueOf(common.FIRST_GIFT_REWARD_DAY_FGRD_DAY_THREE),
		"FIRST_GIFT_REWARD_DAY_FGRD_DAY_TWO":                   reflect.ValueOf(common.FIRST_GIFT_REWARD_DAY_FGRD_DAY_TWO),
		"FIRST_GIFT_REWARD_DAY_FGRD_NONE":                      reflect.ValueOf(common.FIRST_GIFT_REWARD_DAY_FGRD_NONE),
		"FIRST_GIFT_REWARD_DAY_name":                           reflect.ValueOf(&common.FIRST_GIFT_REWARD_DAY_name).Elem(),
		"FIRST_GIFT_REWARD_DAY_value":                          reflect.ValueOf(&common.FIRST_GIFT_REWARD_DAY_value).Elem(),
		"FLOWER_LOG_FL_ASSIST":                                 reflect.ValueOf(common.FLOWER_LOG_FL_ASSIST),
		"FLOWER_LOG_FL_ASSISTED":                               reflect.ValueOf(common.FLOWER_LOG_FL_ASSISTED),
		"FLOWER_LOG_FL_NONE":                                   reflect.ValueOf(common.FLOWER_LOG_FL_NONE),
		"FLOWER_LOG_FL_OCCUPIED":                               reflect.ValueOf(common.FLOWER_LOG_FL_OCCUPIED),
		"FLOWER_LOG_FL_OCCUPIED_WIN":                           reflect.ValueOf(common.FLOWER_LOG_FL_OCCUPIED_WIN),
		"FLOWER_LOG_FL_SNATCHED":                               reflect.ValueOf(common.FLOWER_LOG_FL_SNATCHED),
		"FLOWER_LOG_TYPE_FLT_NONE":                             reflect.ValueOf(common.FLOWER_LOG_TYPE_FLT_NONE),
		"FLOWER_LOG_TYPE_FLT_OCCUPY":                           reflect.ValueOf(common.FLOWER_LOG_TYPE_FLT_OCCUPY),
		"FLOWER_LOG_TYPE_FLT_SNATCH":                           reflect.ValueOf(common.FLOWER_LOG_TYPE_FLT_SNATCH),
		"FLOWER_LOG_TYPE_name":                                 reflect.ValueOf(&common.FLOWER_LOG_TYPE_name).Elem(),
		"FLOWER_LOG_TYPE_value":                                reflect.ValueOf(&common.FLOWER_LOG_TYPE_value).Elem(),
		"FLOWER_LOG_name":                                      reflect.ValueOf(&common.FLOWER_LOG_name).Elem(),
		"FLOWER_LOG_value":                                     reflect.ValueOf(&common.FLOWER_LOG_value).Elem(),
		"FLOWER_OCCUPY_END_FOE_ABANDON":                        reflect.ValueOf(common.FLOWER_OCCUPY_END_FOE_ABANDON),
		"FLOWER_OCCUPY_END_FOE_NONE":                           reflect.ValueOf(common.FLOWER_OCCUPY_END_FOE_NONE),
		"FLOWER_OCCUPY_END_FOE_ROBBED_END":                     reflect.ValueOf(common.FLOWER_OCCUPY_END_FOE_ROBBED_END),
		"FLOWER_OCCUPY_END_FOE_ROBBED_REASSIGN":                reflect.ValueOf(common.FLOWER_OCCUPY_END_FOE_ROBBED_REASSIGN),
		"FLOWER_OCCUPY_END_FOE_TIME_UP":                        reflect.ValueOf(common.FLOWER_OCCUPY_END_FOE_TIME_UP),
		"FLOWER_OCCUPY_END_name":                               reflect.ValueOf(&common.FLOWER_OCCUPY_END_name).Elem(),
		"FLOWER_OCCUPY_END_value":                              reflect.ValueOf(&common.FLOWER_OCCUPY_END_value).Elem(),
		"FLOWER_OCCUPY_FO_NONE":                                reflect.ValueOf(common.FLOWER_OCCUPY_FO_NONE),
		"FLOWER_OCCUPY_FO_OPEN_LV":                             reflect.ValueOf(common.FLOWER_OCCUPY_FO_OPEN_LV),
		"FLOWER_OCCUPY_name":                                   reflect.ValueOf(&common.FLOWER_OCCUPY_name).Elem(),
		"FLOWER_OCCUPY_value":                                  reflect.ValueOf(&common.FLOWER_OCCUPY_value).Elem(),
		"FLOWER_STAGE_FS_FEEDING":                              reflect.ValueOf(common.FLOWER_STAGE_FS_FEEDING),
		"FLOWER_STAGE_FS_FS_NONE":                              reflect.ValueOf(common.FLOWER_STAGE_FS_FS_NONE),
		"FLOWER_STAGE_FS_GROWING":                              reflect.ValueOf(common.FLOWER_STAGE_FS_GROWING),
		"FLOWER_STAGE_FS_WAITING":                              reflect.ValueOf(common.FLOWER_STAGE_FS_WAITING),
		"FLOWER_STAGE_name":                                    reflect.ValueOf(&common.FLOWER_STAGE_name).Elem(),
		"FLOWER_STAGE_value":                                   reflect.ValueOf(&common.FLOWER_STAGE_value).Elem(),
		"FOREST_PVP_TREE_FPT_NONE":                             reflect.ValueOf(common.FOREST_PVP_TREE_FPT_NONE),
		"FOREST_PVP_TREE_OLD_OPEN_LV":                          reflect.ValueOf(common.FOREST_PVP_TREE_OLD_OPEN_LV),
		"FOREST_PVP_TREE_OPEN_LV":                              reflect.ValueOf(common.FOREST_PVP_TREE_OPEN_LV),
		"FOREST_PVP_TREE_name":                                 reflect.ValueOf(&common.FOREST_PVP_TREE_name).Elem(),
		"FOREST_PVP_TREE_value":                                reflect.ValueOf(&common.FOREST_PVP_TREE_value).Elem(),
		"FOREST_STAGE_FEEDING":                                 reflect.ValueOf(common.FOREST_STAGE_FEEDING),
		"FOREST_STAGE_FS_NONE":                                 reflect.ValueOf(common.FOREST_STAGE_FS_NONE),
		"FOREST_STAGE_GROWING":                                 reflect.ValueOf(common.FOREST_STAGE_GROWING),
		"FOREST_STAGE_WAITING":                                 reflect.ValueOf(common.FOREST_STAGE_WAITING),
		"FOREST_STAGE_name":                                    reflect.ValueOf(&common.FOREST_STAGE_name).Elem(),
		"FOREST_STAGE_value":                                   reflect.ValueOf(&common.FOREST_STAGE_value).Elem(),
		"FOREST_TREE_TYPE_FTT_NONE":                            reflect.ValueOf(common.FOREST_TREE_TYPE_FTT_NONE),
		"FOREST_TREE_TYPE_FTT_PEACEFUL":                        reflect.ValueOf(common.FOREST_TREE_TYPE_FTT_PEACEFUL),
		"FOREST_TREE_TYPE_FTT_PVP":                             reflect.ValueOf(common.FOREST_TREE_TYPE_FTT_PVP),
		"FOREST_TREE_TYPE_name":                                reflect.ValueOf(&common.FOREST_TREE_TYPE_name).Elem(),
		"FOREST_TREE_TYPE_value":                               reflect.ValueOf(&common.FOREST_TREE_TYPE_value).Elem(),
		"FORMATION_ID_FI_ACTIVITY_STORY":                       reflect.ValueOf(common.FORMATION_ID_FI_ACTIVITY_STORY),
		"FORMATION_ID_FI_ACTIVITY_STORY_EQUAL":                 reflect.ValueOf(common.FORMATION_ID_FI_ACTIVITY_STORY_EQUAL),
		"FORMATION_ID_FI_ARENA_ATTACK":                         reflect.ValueOf(common.FORMATION_ID_FI_ARENA_ATTACK),
		"FORMATION_ID_FI_ARENA_DEFENSE":                        reflect.ValueOf(common.FORMATION_ID_FI_ARENA_DEFENSE),
		"FORMATION_ID_FI_BOSS_RUSH_1":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_1),
		"FORMATION_ID_FI_BOSS_RUSH_2":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_2),
		"FORMATION_ID_FI_BOSS_RUSH_3":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_3),
		"FORMATION_ID_FI_BOSS_RUSH_4":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_4),
		"FORMATION_ID_FI_BOSS_RUSH_5":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_5),
		"FORMATION_ID_FI_BOSS_RUSH_6":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_6),
		"FORMATION_ID_FI_BOSS_RUSH_7":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_7),
		"FORMATION_ID_FI_BOSS_RUSH_8":                          reflect.ValueOf(common.FORMATION_ID_FI_BOSS_RUSH_8),
		"FORMATION_ID_FI_CROSS_ARENA_ATTACK":                   reflect.ValueOf(common.FORMATION_ID_FI_CROSS_ARENA_ATTACK),
		"FORMATION_ID_FI_CROSS_ARENA_DEFENSE":                  reflect.ValueOf(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_A":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_A),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_B":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_B),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_C":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_C),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_D":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_D),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_E":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_E),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_F":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_F),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_G":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_G),
		"FORMATION_ID_FI_DISORDER_LAND_HURDLE_H":               reflect.ValueOf(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_H),
		"FORMATION_ID_FI_DUEL_1":                               reflect.ValueOf(common.FORMATION_ID_FI_DUEL_1),
		"FORMATION_ID_FI_DUEL_2":                               reflect.ValueOf(common.FORMATION_ID_FI_DUEL_2),
		"FORMATION_ID_FI_DUEL_3":                               reflect.ValueOf(common.FORMATION_ID_FI_DUEL_3),
		"FORMATION_ID_FI_DUNGEON":                              reflect.ValueOf(common.FORMATION_ID_FI_DUNGEON),
		"FORMATION_ID_FI_FLOWER_ATTACK":                        reflect.ValueOf(common.FORMATION_ID_FI_FLOWER_ATTACK),
		"FORMATION_ID_FI_FLOWER_DEFENSE":                       reflect.ValueOf(common.FORMATION_ID_FI_FLOWER_DEFENSE),
		"FORMATION_ID_FI_FOREST_ATTACK":                        reflect.ValueOf(common.FORMATION_ID_FI_FOREST_ATTACK),
		"FORMATION_ID_FI_FOREST_DEFENSE":                       reflect.ValueOf(common.FORMATION_ID_FI_FOREST_DEFENSE),
		"FORMATION_ID_FI_GODDESS_CONTRACT":                     reflect.ValueOf(common.FORMATION_ID_FI_GODDESS_CONTRACT),
		"FORMATION_ID_FI_GST":                                  reflect.ValueOf(common.FORMATION_ID_FI_GST),
		"FORMATION_ID_FI_GST_BOSS_1":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_1),
		"FORMATION_ID_FI_GST_BOSS_2":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_2),
		"FORMATION_ID_FI_GST_BOSS_3":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_3),
		"FORMATION_ID_FI_GST_BOSS_4":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_4),
		"FORMATION_ID_FI_GST_BOSS_5":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_5),
		"FORMATION_ID_FI_GST_BOSS_6":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_6),
		"FORMATION_ID_FI_GST_BOSS_7":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_7),
		"FORMATION_ID_FI_GST_BOSS_8":                           reflect.ValueOf(common.FORMATION_ID_FI_GST_BOSS_8),
		"FORMATION_ID_FI_GST_CHALLENGE":                        reflect.ValueOf(common.FORMATION_ID_FI_GST_CHALLENGE),
		"FORMATION_ID_FI_GST_DRAGON_1":                         reflect.ValueOf(common.FORMATION_ID_FI_GST_DRAGON_1),
		"FORMATION_ID_FI_GST_DRAGON_2":                         reflect.ValueOf(common.FORMATION_ID_FI_GST_DRAGON_2),
		"FORMATION_ID_FI_GST_DRAGON_3":                         reflect.ValueOf(common.FORMATION_ID_FI_GST_DRAGON_3),
		"FORMATION_ID_FI_GST_DRAGON_4":                         reflect.ValueOf(common.FORMATION_ID_FI_GST_DRAGON_4),
		"FORMATION_ID_FI_GST_DRAGON_5":                         reflect.ValueOf(common.FORMATION_ID_FI_GST_DRAGON_5),
		"FORMATION_ID_FI_GST_ORE_1":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_1),
		"FORMATION_ID_FI_GST_ORE_2":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_2),
		"FORMATION_ID_FI_GST_ORE_3":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_3),
		"FORMATION_ID_FI_GST_ORE_4":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_4),
		"FORMATION_ID_FI_GST_ORE_5":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_5),
		"FORMATION_ID_FI_GST_ORE_6":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_6),
		"FORMATION_ID_FI_GST_ORE_7":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_7),
		"FORMATION_ID_FI_GST_ORE_8":                            reflect.ValueOf(common.FORMATION_ID_FI_GST_ORE_8),
		"FORMATION_ID_FI_GUILD_DUNGEON":                        reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON),
		"FORMATION_ID_FI_GUILD_DUNGEON_1":                      reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON_1),
		"FORMATION_ID_FI_GUILD_DUNGEON_2":                      reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON_2),
		"FORMATION_ID_FI_GUILD_DUNGEON_3":                      reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON_3),
		"FORMATION_ID_FI_GUILD_DUNGEON_4":                      reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON_4),
		"FORMATION_ID_FI_GUILD_DUNGEON_5":                      reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON_5),
		"FORMATION_ID_FI_GUILD_DUNGEON_6":                      reflect.ValueOf(common.FORMATION_ID_FI_GUILD_DUNGEON_6),
		"FORMATION_ID_FI_MAZE":                                 reflect.ValueOf(common.FORMATION_ID_FI_MAZE),
		"FORMATION_ID_FI_MIRAGE_RACE_DEMON":                    reflect.ValueOf(common.FORMATION_ID_FI_MIRAGE_RACE_DEMON),
		"FORMATION_ID_FI_MIRAGE_RACE_EMPIRE":                   reflect.ValueOf(common.FORMATION_ID_FI_MIRAGE_RACE_EMPIRE),
		"FORMATION_ID_FI_MIRAGE_RACE_FOREST":                   reflect.ValueOf(common.FORMATION_ID_FI_MIRAGE_RACE_FOREST),
		"FORMATION_ID_FI_MIRAGE_RACE_MOON":                     reflect.ValueOf(common.FORMATION_ID_FI_MIRAGE_RACE_MOON),
		"FORMATION_ID_FI_MIRAGE_RACE_PROTOSS":                  reflect.ValueOf(common.FORMATION_ID_FI_MIRAGE_RACE_PROTOSS),
		"FORMATION_ID_FI_MIRAGE_RACE_SIX":                      reflect.ValueOf(common.FORMATION_ID_FI_MIRAGE_RACE_SIX),
		"FORMATION_ID_FI_NONE":                                 reflect.ValueOf(common.FORMATION_ID_FI_NONE),
		"FORMATION_ID_FI_PEAK_1":                               reflect.ValueOf(common.FORMATION_ID_FI_PEAK_1),
		"FORMATION_ID_FI_PEAK_2":                               reflect.ValueOf(common.FORMATION_ID_FI_PEAK_2),
		"FORMATION_ID_FI_PRE_SEASON_LINK_1":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_1),
		"FORMATION_ID_FI_PRE_SEASON_LINK_10":                   reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_10),
		"FORMATION_ID_FI_PRE_SEASON_LINK_2":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_2),
		"FORMATION_ID_FI_PRE_SEASON_LINK_3":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_3),
		"FORMATION_ID_FI_PRE_SEASON_LINK_4":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_4),
		"FORMATION_ID_FI_PRE_SEASON_LINK_5":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_5),
		"FORMATION_ID_FI_PRE_SEASON_LINK_6":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_6),
		"FORMATION_ID_FI_PRE_SEASON_LINK_7":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_7),
		"FORMATION_ID_FI_PRE_SEASON_LINK_8":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_8),
		"FORMATION_ID_FI_PRE_SEASON_LINK_9":                    reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_9),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_1":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_1),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_10":               reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_10),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_2":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_2),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_3":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_3),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_4":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_4),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_5":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_5),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_6":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_6),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_7":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_7),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_8":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_8),
		"FORMATION_ID_FI_PRE_SEASON_LINK_PVP_9":                reflect.ValueOf(common.FORMATION_ID_FI_PRE_SEASON_LINK_PVP_9),
		"FORMATION_ID_FI_SEASON_ARENA_FIVE_ATTACK":             reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_FIVE_ATTACK),
		"FORMATION_ID_FI_SEASON_ARENA_FIVE_DEFENSE":            reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_FIVE_DEFENSE),
		"FORMATION_ID_FI_SEASON_ARENA_NINE_ATTACK":             reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_NINE_ATTACK),
		"FORMATION_ID_FI_SEASON_ARENA_NINE_DEFENSE":            reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_NINE_DEFENSE),
		"FORMATION_ID_FI_SEASON_ARENA_SEVEN_ATTACK":            reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_SEVEN_ATTACK),
		"FORMATION_ID_FI_SEASON_ARENA_SEVEN_DEFENSE":           reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_SEVEN_DEFENSE),
		"FORMATION_ID_FI_SEASON_ARENA_THREE_ATTACK":            reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_THREE_ATTACK),
		"FORMATION_ID_FI_SEASON_ARENA_THREE_DEFENSE":           reflect.ValueOf(common.FORMATION_ID_FI_SEASON_ARENA_THREE_DEFENSE),
		"FORMATION_ID_FI_SEASON_DOOR_1":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_1),
		"FORMATION_ID_FI_SEASON_DOOR_2":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_2),
		"FORMATION_ID_FI_SEASON_DOOR_3":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_3),
		"FORMATION_ID_FI_SEASON_DOOR_4":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_4),
		"FORMATION_ID_FI_SEASON_DOOR_5":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_5),
		"FORMATION_ID_FI_SEASON_DOOR_6":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_6),
		"FORMATION_ID_FI_SEASON_DOOR_7":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_7),
		"FORMATION_ID_FI_SEASON_DOOR_8":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DOOR_8),
		"FORMATION_ID_FI_SEASON_DUNGEON":                       reflect.ValueOf(common.FORMATION_ID_FI_SEASON_DUNGEON),
		"FORMATION_ID_FI_SEASON_MAP_1":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_1),
		"FORMATION_ID_FI_SEASON_MAP_10":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_10),
		"FORMATION_ID_FI_SEASON_MAP_11":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_11),
		"FORMATION_ID_FI_SEASON_MAP_12":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_12),
		"FORMATION_ID_FI_SEASON_MAP_13":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_13),
		"FORMATION_ID_FI_SEASON_MAP_14":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_14),
		"FORMATION_ID_FI_SEASON_MAP_15":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_15),
		"FORMATION_ID_FI_SEASON_MAP_16":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_16),
		"FORMATION_ID_FI_SEASON_MAP_17":                        reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_17),
		"FORMATION_ID_FI_SEASON_MAP_2":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_2),
		"FORMATION_ID_FI_SEASON_MAP_3":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_3),
		"FORMATION_ID_FI_SEASON_MAP_4":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_4),
		"FORMATION_ID_FI_SEASON_MAP_5":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_5),
		"FORMATION_ID_FI_SEASON_MAP_6":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_6),
		"FORMATION_ID_FI_SEASON_MAP_7":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_7),
		"FORMATION_ID_FI_SEASON_MAP_8":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_8),
		"FORMATION_ID_FI_SEASON_MAP_9":                         reflect.ValueOf(common.FORMATION_ID_FI_SEASON_MAP_9),
		"FORMATION_ID_FI_TALES":                                reflect.ValueOf(common.FORMATION_ID_FI_TALES),
		"FORMATION_ID_FI_TALES_ELITE":                          reflect.ValueOf(common.FORMATION_ID_FI_TALES_ELITE),
		"FORMATION_ID_FI_TOWER":                                reflect.ValueOf(common.FORMATION_ID_FI_TOWER),
		"FORMATION_ID_FI_TOWERSTAR":                            reflect.ValueOf(common.FORMATION_ID_FI_TOWERSTAR),
		"FORMATION_ID_FI_TOWER_SEASON":                         reflect.ValueOf(common.FORMATION_ID_FI_TOWER_SEASON),
		"FORMATION_ID_FI_TRIAL_EQUIP":                          reflect.ValueOf(common.FORMATION_ID_FI_TRIAL_EQUIP),
		"FORMATION_ID_FI_TRIAL_EXP":                            reflect.ValueOf(common.FORMATION_ID_FI_TRIAL_EXP),
		"FORMATION_ID_FI_TRIAL_GOLD":                           reflect.ValueOf(common.FORMATION_ID_FI_TRIAL_GOLD),
		"FORMATION_ID_FI_TRIAL_HERO":                           reflect.ValueOf(common.FORMATION_ID_FI_TRIAL_HERO),
		"FORMATION_ID_FI_WORLD_BOSS":                           reflect.ValueOf(common.FORMATION_ID_FI_WORLD_BOSS),
		"FORMATION_ID_name":                                    reflect.ValueOf(&common.FORMATION_ID_name).Elem(),
		"FORMATION_ID_value":                                   reflect.ValueOf(&common.FORMATION_ID_value).Elem(),
		"FORMATION_TYPE_FTYPE_NONE":                            reflect.ValueOf(common.FORMATION_TYPE_FTYPE_NONE),
		"FORMATION_TYPE_FTYPE_PVE":                             reflect.ValueOf(common.FORMATION_TYPE_FTYPE_PVE),
		"FORMATION_TYPE_FTYPE_PVP":                             reflect.ValueOf(common.FORMATION_TYPE_FTYPE_PVP),
		"FORMATION_TYPE_name":                                  reflect.ValueOf(&common.FORMATION_TYPE_name).Elem(),
		"FORMATION_TYPE_value":                                 reflect.ValueOf(&common.FORMATION_TYPE_value).Elem(),
		"FUNCID_MODULE_ACHIEVE":                                reflect.ValueOf(common.FUNCID_MODULE_ACHIEVE),
		"FUNCID_MODULE_ACHIEVEMENTS_SHOWCASE_S":                reflect.ValueOf(common.FUNCID_MODULE_ACHIEVEMENTS_SHOWCASE_S),
		"FUNCID_MODULE_ACTIVE_SHOP":                            reflect.ValueOf(common.FUNCID_MODULE_ACTIVE_SHOP),
		"FUNCID_MODULE_ACTIVITY_COMPLIANCE":                    reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_COMPLIANCE),
		"FUNCID_MODULE_ACTIVITY_COUPON":                        reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_COUPON),
		"FUNCID_MODULE_ACTIVITY_GODDESS_CONTRACT":              reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_GODDESS_CONTRACT),
		"FUNCID_MODULE_ACTIVITY_LIFELONG_GIFTS_S":              reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_LIFELONG_GIFTS_S),
		"FUNCID_MODULE_ACTIVITY_MIRAGE":                        reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_MIRAGE),
		"FUNCID_MODULE_ACTIVITY_RANK_TOWER_SEASON":             reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_RANK_TOWER_SEASON),
		"FUNCID_MODULE_ACTIVITY_RECHARGE_S":                    reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_RECHARGE_S),
		"FUNCID_MODULE_ACTIVITY_RETURN":                        reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_RETURN),
		"FUNCID_MODULE_ACTIVITY_SCHEDULE":                      reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_SCHEDULE),
		"FUNCID_MODULE_ACTIVITY_STORY":                         reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_STORY),
		"FUNCID_MODULE_ACTIVITY_TOWER":                         reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_TOWER),
		"FUNCID_MODULE_ACTIVITY_TURN_TABLE":                    reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_TURN_TABLE),
		"FUNCID_MODULE_ACTIVITY_WEB":                           reflect.ValueOf(common.FUNCID_MODULE_ACTIVITY_WEB),
		"FUNCID_MODULE_ARENA":                                  reflect.ValueOf(common.FUNCID_MODULE_ARENA),
		"FUNCID_MODULE_ARENA_OPPONENT_S":                       reflect.ValueOf(common.FUNCID_MODULE_ARENA_OPPONENT_S),
		"FUNCID_MODULE_ARENA_RANK":                             reflect.ValueOf(common.FUNCID_MODULE_ARENA_RANK),
		"FUNCID_MODULE_ARENA_SHOP":                             reflect.ValueOf(common.FUNCID_MODULE_ARENA_SHOP),
		"FUNCID_MODULE_ARTIFACT":                               reflect.ValueOf(common.FUNCID_MODULE_ARTIFACT),
		"FUNCID_MODULE_ARTIFACT_DEBUT":                         reflect.ValueOf(common.FUNCID_MODULE_ARTIFACT_DEBUT),
		"FUNCID_MODULE_ARTIFACT_FORGE":                         reflect.ValueOf(common.FUNCID_MODULE_ARTIFACT_FORGE),
		"FUNCID_MODULE_ARTIFACT_POINTS_EXCHANGE":               reflect.ValueOf(common.FUNCID_MODULE_ARTIFACT_POINTS_EXCHANGE),
		"FUNCID_MODULE_ARTIFACT_STRENGTH":                      reflect.ValueOf(common.FUNCID_MODULE_ARTIFACT_STRENGTH),
		"FUNCID_MODULE_ARTIFACT_SUMMON":                        reflect.ValueOf(common.FUNCID_MODULE_ARTIFACT_SUMMON),
		"FUNCID_MODULE_ASSISTANCE_ACTIVITY":                    reflect.ValueOf(common.FUNCID_MODULE_ASSISTANCE_ACTIVITY),
		"FUNCID_MODULE_ASSISTANT":                              reflect.ValueOf(common.FUNCID_MODULE_ASSISTANT),
		"FUNCID_MODULE_ASSOC":                                  reflect.ValueOf(common.FUNCID_MODULE_ASSOC),
		"FUNCID_MODULE_AVATAR":                                 reflect.ValueOf(common.FUNCID_MODULE_AVATAR),
		"FUNCID_MODULE_BAG":                                    reflect.ValueOf(common.FUNCID_MODULE_BAG),
		"FUNCID_MODULE_BELLTOWER":                              reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER),
		"FUNCID_MODULE_BELLTOWER_ARTIFACT_REVIVE":              reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_ARTIFACT_REVIVE),
		"FUNCID_MODULE_BELLTOWER_EQUIP_DECOMPOSE":              reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_EQUIP_DECOMPOSE),
		"FUNCID_MODULE_BELLTOWER_EQUIP_REVIVE":                 reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_EQUIP_REVIVE),
		"FUNCID_MODULE_BELLTOWER_GEM_DECOMPOSE":                reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_GEM_DECOMPOSE),
		"FUNCID_MODULE_BELLTOWER_HERO_BACK":                    reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_HERO_BACK),
		"FUNCID_MODULE_BELLTOWER_HERO_CHANGE":                  reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_HERO_CHANGE),
		"FUNCID_MODULE_BELLTOWER_HERO_DISBAND":                 reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_HERO_DISBAND),
		"FUNCID_MODULE_BELLTOWER_HERO_REVIVE":                  reflect.ValueOf(common.FUNCID_MODULE_BELLTOWER_HERO_REVIVE),
		"FUNCID_MODULE_BOSS_RUSH":                              reflect.ValueOf(common.FUNCID_MODULE_BOSS_RUSH),
		"FUNCID_MODULE_CARNIVAL_FOURTEEN":                      reflect.ValueOf(common.FUNCID_MODULE_CARNIVAL_FOURTEEN),
		"FUNCID_MODULE_CARNIVAL_SEASON":                        reflect.ValueOf(common.FUNCID_MODULE_CARNIVAL_SEASON),
		"FUNCID_MODULE_CARNIVAL_SEVEN":                         reflect.ValueOf(common.FUNCID_MODULE_CARNIVAL_SEVEN),
		"FUNCID_MODULE_CHAT":                                   reflect.ValueOf(common.FUNCID_MODULE_CHAT),
		"FUNCID_MODULE_CHAT_GUILD":                             reflect.ValueOf(common.FUNCID_MODULE_CHAT_GUILD),
		"FUNCID_MODULE_CHAT_LIKE":                              reflect.ValueOf(common.FUNCID_MODULE_CHAT_LIKE),
		"FUNCID_MODULE_CHAT_PARTITION":                         reflect.ValueOf(common.FUNCID_MODULE_CHAT_PARTITION),
		"FUNCID_MODULE_CHAT_PRIVATE":                           reflect.ValueOf(common.FUNCID_MODULE_CHAT_PRIVATE),
		"FUNCID_MODULE_CHAT_SYSTEM":                            reflect.ValueOf(common.FUNCID_MODULE_CHAT_SYSTEM),
		"FUNCID_MODULE_CHAT_WORLD":                             reflect.ValueOf(common.FUNCID_MODULE_CHAT_WORLD),
		"FUNCID_MODULE_CHAT_WORLD_SPEAK":                       reflect.ValueOf(common.FUNCID_MODULE_CHAT_WORLD_SPEAK),
		"FUNCID_MODULE_CITY":                                   reflect.ValueOf(common.FUNCID_MODULE_CITY),
		"FUNCID_MODULE_CLIENT_INFO_S":                          reflect.ValueOf(common.FUNCID_MODULE_CLIENT_INFO_S),
		"FUNCID_MODULE_COGNITION":                              reflect.ValueOf(common.FUNCID_MODULE_COGNITION),
		"FUNCID_MODULE_COMPLIANCE_TASKS_S":                     reflect.ValueOf(common.FUNCID_MODULE_COMPLIANCE_TASKS_S),
		"FUNCID_MODULE_COUPON_USE":                             reflect.ValueOf(common.FUNCID_MODULE_COUPON_USE),
		"FUNCID_MODULE_CRYSTAL":                                reflect.ValueOf(common.FUNCID_MODULE_CRYSTAL),
		"FUNCID_MODULE_CRYSTAL_BLESSING":                       reflect.ValueOf(common.FUNCID_MODULE_CRYSTAL_BLESSING),
		"FUNCID_MODULE_CRYSTAL_EMBLEM":                         reflect.ValueOf(common.FUNCID_MODULE_CRYSTAL_EMBLEM),
		"FUNCID_MODULE_CRYSTAL_EQUIPMENT":                      reflect.ValueOf(common.FUNCID_MODULE_CRYSTAL_EQUIPMENT),
		"FUNCID_MODULE_CRYSTAL_GEM":                            reflect.ValueOf(common.FUNCID_MODULE_CRYSTAL_GEM),
		"FUNCID_MODULE_CRYSTAL_HIDDEN_WEAPON":                  reflect.ValueOf(common.FUNCID_MODULE_CRYSTAL_HIDDEN_WEAPON),
		"FUNCID_MODULE_ChAT_GROUP_TAG_S":                       reflect.ValueOf(common.FUNCID_MODULE_ChAT_GROUP_TAG_S),
		"FUNCID_MODULE_DAILY_ATTENDANCE_HERO":                  reflect.ValueOf(common.FUNCID_MODULE_DAILY_ATTENDANCE_HERO),
		"FUNCID_MODULE_DAILY_ATTENDANCE_S":                     reflect.ValueOf(common.FUNCID_MODULE_DAILY_ATTENDANCE_S),
		"FUNCID_MODULE_DAILY_SPECIAL_S":                        reflect.ValueOf(common.FUNCID_MODULE_DAILY_SPECIAL_S),
		"FUNCID_MODULE_DAILY_TASK":                             reflect.ValueOf(common.FUNCID_MODULE_DAILY_TASK),
		"FUNCID_MODULE_DAILY_WISH":                             reflect.ValueOf(common.FUNCID_MODULE_DAILY_WISH),
		"FUNCID_MODULE_DIAMOND_PASS":                           reflect.ValueOf(common.FUNCID_MODULE_DIAMOND_PASS),
		"FUNCID_MODULE_DIAMOND_SHOP":                           reflect.ValueOf(common.FUNCID_MODULE_DIAMOND_SHOP),
		"FUNCID_MODULE_DISORDER_LAND":                          reflect.ValueOf(common.FUNCID_MODULE_DISORDER_LAND),
		"FUNCID_MODULE_DISPATCH":                               reflect.ValueOf(common.FUNCID_MODULE_DISPATCH),
		"FUNCID_MODULE_DIVINE_DEMON":                           reflect.ValueOf(common.FUNCID_MODULE_DIVINE_DEMON),
		"FUNCID_MODULE_DIVINE_DEMON_WISH_RECORD_S":             reflect.ValueOf(common.FUNCID_MODULE_DIVINE_DEMON_WISH_RECORD_S),
		"FUNCID_MODULE_DROP_ACTIVITY":                          reflect.ValueOf(common.FUNCID_MODULE_DROP_ACTIVITY),
		"FUNCID_MODULE_DROP_S":                                 reflect.ValueOf(common.FUNCID_MODULE_DROP_S),
		"FUNCID_MODULE_DUEL":                                   reflect.ValueOf(common.FUNCID_MODULE_DUEL),
		"FUNCID_MODULE_DUEL_1":                                 reflect.ValueOf(common.FUNCID_MODULE_DUEL_1),
		"FUNCID_MODULE_DUEL_2":                                 reflect.ValueOf(common.FUNCID_MODULE_DUEL_2),
		"FUNCID_MODULE_DUEL_3":                                 reflect.ValueOf(common.FUNCID_MODULE_DUEL_3),
		"FUNCID_MODULE_DUNGEON":                                reflect.ValueOf(common.FUNCID_MODULE_DUNGEON),
		"FUNCID_MODULE_EMBLEM":                                 reflect.ValueOf(common.FUNCID_MODULE_EMBLEM),
		"FUNCID_MODULE_EMBLEM_SUCCINCT":                        reflect.ValueOf(common.FUNCID_MODULE_EMBLEM_SUCCINCT),
		"FUNCID_MODULE_EMBLEM_UPGRADE":                         reflect.ValueOf(common.FUNCID_MODULE_EMBLEM_UPGRADE),
		"FUNCID_MODULE_EQUIP":                                  reflect.ValueOf(common.FUNCID_MODULE_EQUIP),
		"FUNCID_MODULE_EQUIP_ENCHANT":                          reflect.ValueOf(common.FUNCID_MODULE_EQUIP_ENCHANT),
		"FUNCID_MODULE_EQUIP_EVOLUTION":                        reflect.ValueOf(common.FUNCID_MODULE_EQUIP_EVOLUTION),
		"FUNCID_MODULE_EQUIP_REFINE":                           reflect.ValueOf(common.FUNCID_MODULE_EQUIP_REFINE),
		"FUNCID_MODULE_EQUIP_STRENGTH":                         reflect.ValueOf(common.FUNCID_MODULE_EQUIP_STRENGTH),
		"FUNCID_MODULE_FIELD":                                  reflect.ValueOf(common.FUNCID_MODULE_FIELD),
		"FUNCID_MODULE_FLOWER":                                 reflect.ValueOf(common.FUNCID_MODULE_FLOWER),
		"FUNCID_MODULE_FLOWER_S":                               reflect.ValueOf(common.FUNCID_MODULE_FLOWER_S),
		"FUNCID_MODULE_FORECAST":                               reflect.ValueOf(common.FUNCID_MODULE_FORECAST),
		"FUNCID_MODULE_FOREST":                                 reflect.ValueOf(common.FUNCID_MODULE_FOREST),
		"FUNCID_MODULE_FRIEND":                                 reflect.ValueOf(common.FUNCID_MODULE_FRIEND),
		"FUNCID_MODULE_GEM":                                    reflect.ValueOf(common.FUNCID_MODULE_GEM),
		"FUNCID_MODULE_GENERAL_SHOP":                           reflect.ValueOf(common.FUNCID_MODULE_GENERAL_SHOP),
		"FUNCID_MODULE_GIFT_CODE":                              reflect.ValueOf(common.FUNCID_MODULE_GIFT_CODE),
		"FUNCID_MODULE_GLOBAL_ATTR_S":                          reflect.ValueOf(common.FUNCID_MODULE_GLOBAL_ATTR_S),
		"FUNCID_MODULE_GODDESS_CONTRACT":                       reflect.ValueOf(common.FUNCID_MODULE_GODDESS_CONTRACT),
		"FUNCID_MODULE_GOD_PRESENT":                            reflect.ValueOf(common.FUNCID_MODULE_GOD_PRESENT),
		"FUNCID_MODULE_GOLD_BUY":                               reflect.ValueOf(common.FUNCID_MODULE_GOLD_BUY),
		"FUNCID_MODULE_GST":                                    reflect.ValueOf(common.FUNCID_MODULE_GST),
		"FUNCID_MODULE_GST_BOSS":                               reflect.ValueOf(common.FUNCID_MODULE_GST_BOSS),
		"FUNCID_MODULE_GST_CHALLENGE":                          reflect.ValueOf(common.FUNCID_MODULE_GST_CHALLENGE),
		"FUNCID_MODULE_GST_DRAGON":                             reflect.ValueOf(common.FUNCID_MODULE_GST_DRAGON),
		"FUNCID_MODULE_GST_ORE":                                reflect.ValueOf(common.FUNCID_MODULE_GST_ORE),
		"FUNCID_MODULE_GST_TECH":                               reflect.ValueOf(common.FUNCID_MODULE_GST_TECH),
		"FUNCID_MODULE_GUIDANCE_S":                             reflect.ValueOf(common.FUNCID_MODULE_GUIDANCE_S),
		"FUNCID_MODULE_GUILD":                                  reflect.ValueOf(common.FUNCID_MODULE_GUILD),
		"FUNCID_MODULE_GUILD_CHEST":                            reflect.ValueOf(common.FUNCID_MODULE_GUILD_CHEST),
		"FUNCID_MODULE_GUILD_DONATE":                           reflect.ValueOf(common.FUNCID_MODULE_GUILD_DONATE),
		"FUNCID_MODULE_GUILD_DUNGEON":                          reflect.ValueOf(common.FUNCID_MODULE_GUILD_DUNGEON),
		"FUNCID_MODULE_GUILD_DUNGEON_BOX_ALL_RECV":             reflect.ValueOf(common.FUNCID_MODULE_GUILD_DUNGEON_BOX_ALL_RECV),
		"FUNCID_MODULE_GUILD_MOBILIZATION":                     reflect.ValueOf(common.FUNCID_MODULE_GUILD_MOBILIZATION),
		"FUNCID_MODULE_GUILD_RECRUIT":                          reflect.ValueOf(common.FUNCID_MODULE_GUILD_RECRUIT),
		"FUNCID_MODULE_GUILD_TALENT":                           reflect.ValueOf(common.FUNCID_MODULE_GUILD_TALENT),
		"FUNCID_MODULE_HANDBOOK":                               reflect.ValueOf(common.FUNCID_MODULE_HANDBOOK),
		"FUNCID_MODULE_HERO":                                   reflect.ValueOf(common.FUNCID_MODULE_HERO),
		"FUNCID_MODULE_HERO_AWAKEN":                            reflect.ValueOf(common.FUNCID_MODULE_HERO_AWAKEN),
		"FUNCID_MODULE_HERO_CONVERSION":                        reflect.ValueOf(common.FUNCID_MODULE_HERO_CONVERSION),
		"FUNCID_MODULE_HERO_CONVERT":                           reflect.ValueOf(common.FUNCID_MODULE_HERO_CONVERT),
		"FUNCID_MODULE_HERO_EXCHANGE":                          reflect.ValueOf(common.FUNCID_MODULE_HERO_EXCHANGE),
		"FUNCID_MODULE_HERO_SHOP":                              reflect.ValueOf(common.FUNCID_MODULE_HERO_SHOP),
		"FUNCID_MODULE_HERO_STAGE_UP":                          reflect.ValueOf(common.FUNCID_MODULE_HERO_STAGE_UP),
		"FUNCID_MODULE_HONOR_SHOP":                             reflect.ValueOf(common.FUNCID_MODULE_HONOR_SHOP),
		"FUNCID_MODULE_HOT_RANK":                               reflect.ValueOf(common.FUNCID_MODULE_HOT_RANK),
		"FUNCID_MODULE_LEADERBOARD":                            reflect.ValueOf(common.FUNCID_MODULE_LEADERBOARD),
		"FUNCID_MODULE_LINE_TASK":                              reflect.ValueOf(common.FUNCID_MODULE_LINE_TASK),
		"FUNCID_MODULE_LINK_S":                                 reflect.ValueOf(common.FUNCID_MODULE_LINK_S),
		"FUNCID_MODULE_LINK_SUMMON":                            reflect.ValueOf(common.FUNCID_MODULE_LINK_SUMMON),
		"FUNCID_MODULE_MAIL":                                   reflect.ValueOf(common.FUNCID_MODULE_MAIL),
		"FUNCID_MODULE_MAIN_PASS":                              reflect.ValueOf(common.FUNCID_MODULE_MAIN_PASS),
		"FUNCID_MODULE_MASTER":                                 reflect.ValueOf(common.FUNCID_MODULE_MASTER),
		"FUNCID_MODULE_MAZE":                                   reflect.ValueOf(common.FUNCID_MODULE_MAZE),
		"FUNCID_MODULE_MEDAL":                                  reflect.ValueOf(common.FUNCID_MODULE_MEDAL),
		"FUNCID_MODULE_MEMORY":                                 reflect.ValueOf(common.FUNCID_MODULE_MEMORY),
		"FUNCID_MODULE_MIRAGE":                                 reflect.ValueOf(common.FUNCID_MODULE_MIRAGE),
		"FUNCID_MODULE_MIRAGE_HELL":                            reflect.ValueOf(common.FUNCID_MODULE_MIRAGE_HELL),
		"FUNCID_MODULE_MONTHLY_CARD":                           reflect.ValueOf(common.FUNCID_MODULE_MONTHLY_CARD),
		"FUNCID_MODULE_MONTHLY_TASK":                           reflect.ValueOf(common.FUNCID_MODULE_MONTHLY_TASK),
		"FUNCID_MODULE_MONTH_TASKS_S":                          reflect.ValueOf(common.FUNCID_MODULE_MONTH_TASKS_S),
		"FUNCID_MODULE_NEW_YEAR_ACTIVITY":                      reflect.ValueOf(common.FUNCID_MODULE_NEW_YEAR_ACTIVITY),
		"FUNCID_MODULE_NONE":                                   reflect.ValueOf(common.FUNCID_MODULE_NONE),
		"FUNCID_MODULE_OPERATE_ACTIVITY":                       reflect.ValueOf(common.FUNCID_MODULE_OPERATE_ACTIVITY),
		"FUNCID_MODULE_ORDERS_S":                               reflect.ValueOf(common.FUNCID_MODULE_ORDERS_S),
		"FUNCID_MODULE_PASS_DAILY_ACTIVE":                      reflect.ValueOf(common.FUNCID_MODULE_PASS_DAILY_ACTIVE),
		"FUNCID_MODULE_PASS_MAZE_ACTIVE":                       reflect.ValueOf(common.FUNCID_MODULE_PASS_MAZE_ACTIVE),
		"FUNCID_MODULE_PASS_S":                                 reflect.ValueOf(common.FUNCID_MODULE_PASS_S),
		"FUNCID_MODULE_PEAK":                                   reflect.ValueOf(common.FUNCID_MODULE_PEAK),
		"FUNCID_MODULE_PRE_SEASON":                             reflect.ValueOf(common.FUNCID_MODULE_PRE_SEASON),
		"FUNCID_MODULE_PROMOTION":                              reflect.ValueOf(common.FUNCID_MODULE_PROMOTION),
		"FUNCID_MODULE_PROPHET_PASS":                           reflect.ValueOf(common.FUNCID_MODULE_PROPHET_PASS),
		"FUNCID_MODULE_PUSH_GIFT":                              reflect.ValueOf(common.FUNCID_MODULE_PUSH_GIFT),
		"FUNCID_MODULE_PYRAMID":                                reflect.ValueOf(common.FUNCID_MODULE_PYRAMID),
		"FUNCID_MODULE_QUESTIONNAIRE_S":                        reflect.ValueOf(common.FUNCID_MODULE_QUESTIONNAIRE_S),
		"FUNCID_MODULE_RANK_ACHIEVE_ARENA":                     reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_ARENA),
		"FUNCID_MODULE_RANK_ACHIEVE_AWARD_S":                   reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_AWARD_S),
		"FUNCID_MODULE_RANK_ACHIEVE_DUNGEON":                   reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_DUNGEON),
		"FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_DEMON":              reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_DEMON),
		"FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_EMPIRE":             reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_EMPIRE),
		"FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_FOREST":             reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_FOREST),
		"FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_MOON":               reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_MOON),
		"FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_PROTOSS":            reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_PROTOSS),
		"FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_SIX":                reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_SIX),
		"FUNCID_MODULE_RANK_ACHIEVE_POWER":                     reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_POWER),
		"FUNCID_MODULE_RANK_ACHIEVE_TOWER":                     reflect.ValueOf(common.FUNCID_MODULE_RANK_ACHIEVE_TOWER),
		"FUNCID_MODULE_RATE_S":                                 reflect.ValueOf(common.FUNCID_MODULE_RATE_S),
		"FUNCID_MODULE_RECHARGE_S":                             reflect.ValueOf(common.FUNCID_MODULE_RECHARGE_S),
		"FUNCID_MODULE_REMAIN":                                 reflect.ValueOf(common.FUNCID_MODULE_REMAIN),
		"FUNCID_MODULE_RITE":                                   reflect.ValueOf(common.FUNCID_MODULE_RITE),
		"FUNCID_MODULE_ROUND_ACTIVITY":                         reflect.ValueOf(common.FUNCID_MODULE_ROUND_ACTIVITY),
		"FUNCID_MODULE_SAVE_H5_DESKTOP":                        reflect.ValueOf(common.FUNCID_MODULE_SAVE_H5_DESKTOP),
		"FUNCID_MODULE_SEASON":                                 reflect.ValueOf(common.FUNCID_MODULE_SEASON),
		"FUNCID_MODULE_SEASON_ARENA":                           reflect.ValueOf(common.FUNCID_MODULE_SEASON_ARENA),
		"FUNCID_MODULE_SEASON_COMPLIANCE":                      reflect.ValueOf(common.FUNCID_MODULE_SEASON_COMPLIANCE),
		"FUNCID_MODULE_SEASON_DOOR":                            reflect.ValueOf(common.FUNCID_MODULE_SEASON_DOOR),
		"FUNCID_MODULE_SEASON_DUNGEON":                         reflect.ValueOf(common.FUNCID_MODULE_SEASON_DUNGEON),
		"FUNCID_MODULE_SEASON_FLASH_BACK":                      reflect.ValueOf(common.FUNCID_MODULE_SEASON_FLASH_BACK),
		"FUNCID_MODULE_SEASON_JEWELRY":                         reflect.ValueOf(common.FUNCID_MODULE_SEASON_JEWELRY),
		"FUNCID_MODULE_SEASON_LEVEL":                           reflect.ValueOf(common.FUNCID_MODULE_SEASON_LEVEL),
		"FUNCID_MODULE_SEASON_LINK":                            reflect.ValueOf(common.FUNCID_MODULE_SEASON_LINK),
		"FUNCID_MODULE_SEASON_MAP":                             reflect.ValueOf(common.FUNCID_MODULE_SEASON_MAP),
		"FUNCID_MODULE_SEASON_RETURN":                          reflect.ValueOf(common.FUNCID_MODULE_SEASON_RETURN),
		"FUNCID_MODULE_SEASON_SHOP":                            reflect.ValueOf(common.FUNCID_MODULE_SEASON_SHOP),
		"FUNCID_MODULE_SELECT_SUMMON":                          reflect.ValueOf(common.FUNCID_MODULE_SELECT_SUMMON),
		"FUNCID_MODULE_SEVENDAY_LOGIN":                         reflect.ValueOf(common.FUNCID_MODULE_SEVENDAY_LOGIN),
		"FUNCID_MODULE_SHARE":                                  reflect.ValueOf(common.FUNCID_MODULE_SHARE),
		"FUNCID_MODULE_SHARE_GROWTH_S":                         reflect.ValueOf(common.FUNCID_MODULE_SHARE_GROWTH_S),
		"FUNCID_MODULE_SHOP":                                   reflect.ValueOf(common.FUNCID_MODULE_SHOP),
		"FUNCID_MODULE_SHOP_COUPON":                            reflect.ValueOf(common.FUNCID_MODULE_SHOP_COUPON),
		"FUNCID_MODULE_SHOP_S":                                 reflect.ValueOf(common.FUNCID_MODULE_SHOP_S),
		"FUNCID_MODULE_SKILL_SHOP":                             reflect.ValueOf(common.FUNCID_MODULE_SKILL_SHOP),
		"FUNCID_MODULE_SKIN":                                   reflect.ValueOf(common.FUNCID_MODULE_SKIN),
		"FUNCID_MODULE_SOCIAL":                                 reflect.ValueOf(common.FUNCID_MODULE_SOCIAL),
		"FUNCID_MODULE_SPEED_ONHOOK":                           reflect.ValueOf(common.FUNCID_MODULE_SPEED_ONHOOK),
		"FUNCID_MODULE_SPINNER":                                reflect.ValueOf(common.FUNCID_MODULE_SPINNER),
		"FUNCID_MODULE_STORY_REVIEW":                           reflect.ValueOf(common.FUNCID_MODULE_STORY_REVIEW),
		"FUNCID_MODULE_SUMMON":                                 reflect.ValueOf(common.FUNCID_MODULE_SUMMON),
		"FUNCID_MODULE_TALENT_TREE":                            reflect.ValueOf(common.FUNCID_MODULE_TALENT_TREE),
		"FUNCID_MODULE_TALES":                                  reflect.ValueOf(common.FUNCID_MODULE_TALES),
		"FUNCID_MODULE_TALES_ELITE":                            reflect.ValueOf(common.FUNCID_MODULE_TALES_ELITE),
		"FUNCID_MODULE_TITLE":                                  reflect.ValueOf(common.FUNCID_MODULE_TITLE),
		"FUNCID_MODULE_TOWER":                                  reflect.ValueOf(common.FUNCID_MODULE_TOWER),
		"FUNCID_MODULE_TOWERSTAR":                              reflect.ValueOf(common.FUNCID_MODULE_TOWERSTAR),
		"FUNCID_MODULE_TOWER_JUMP":                             reflect.ValueOf(common.FUNCID_MODULE_TOWER_JUMP),
		"FUNCID_MODULE_TOWER_PASS":                             reflect.ValueOf(common.FUNCID_MODULE_TOWER_PASS),
		"FUNCID_MODULE_TOWER_SEASON":                           reflect.ValueOf(common.FUNCID_MODULE_TOWER_SEASON),
		"FUNCID_MODULE_TRIAL":                                  reflect.ValueOf(common.FUNCID_MODULE_TRIAL),
		"FUNCID_MODULE_TRIAL_EQUIP":                            reflect.ValueOf(common.FUNCID_MODULE_TRIAL_EQUIP),
		"FUNCID_MODULE_TRIAL_EXP":                              reflect.ValueOf(common.FUNCID_MODULE_TRIAL_EXP),
		"FUNCID_MODULE_TRIAL_GOLD":                             reflect.ValueOf(common.FUNCID_MODULE_TRIAL_GOLD),
		"FUNCID_MODULE_TRIAL_HERO":                             reflect.ValueOf(common.FUNCID_MODULE_TRIAL_HERO),
		"FUNCID_MODULE_USER_GUILD_CHEST_ITEM_S":                reflect.ValueOf(common.FUNCID_MODULE_USER_GUILD_CHEST_ITEM_S),
		"FUNCID_MODULE_VIP":                                    reflect.ValueOf(common.FUNCID_MODULE_VIP),
		"FUNCID_MODULE_VIP_S":                                  reflect.ValueOf(common.FUNCID_MODULE_VIP_S),
		"FUNCID_MODULE_WEEKLY_TASK":                            reflect.ValueOf(common.FUNCID_MODULE_WEEKLY_TASK),
		"FUNCID_MODULE_WORLD_BOSS":                             reflect.ValueOf(common.FUNCID_MODULE_WORLD_BOSS),
		"FUNCID_MODULE_WRESTLE":                                reflect.ValueOf(common.FUNCID_MODULE_WRESTLE),
		"FUNCID_name":                                          reflect.ValueOf(&common.FUNCID_name).Elem(),
		"FUNCID_value":                                         reflect.ValueOf(&common.FUNCID_value).Elem(),
		"GEM_CONFIG_GEM_BAG_MAX_LIMIT_ID":                      reflect.ValueOf(common.GEM_CONFIG_GEM_BAG_MAX_LIMIT_ID),
		"GEM_CONFIG_GEM_LEFT_SLOT":                             reflect.ValueOf(common.GEM_CONFIG_GEM_LEFT_SLOT),
		"GEM_CONFIG_GEM_LEFT_SLOT_UNLOCK_LEVEL_ID":             reflect.ValueOf(common.GEM_CONFIG_GEM_LEFT_SLOT_UNLOCK_LEVEL_ID),
		"GEM_CONFIG_GEM_NONE":                                  reflect.ValueOf(common.GEM_CONFIG_GEM_NONE),
		"GEM_CONFIG_GEM_RIGHT_SLOT":                            reflect.ValueOf(common.GEM_CONFIG_GEM_RIGHT_SLOT),
		"GEM_CONFIG_GEM_RIGHT_SLOT_UNLOCK_STAR_ID":             reflect.ValueOf(common.GEM_CONFIG_GEM_RIGHT_SLOT_UNLOCK_STAR_ID),
		"GEM_CONFIG_name":                                      reflect.ValueOf(&common.GEM_CONFIG_name).Elem(),
		"GEM_CONFIG_value":                                     reflect.ValueOf(&common.GEM_CONFIG_value).Elem(),
		"GLOBAL_ATTR_TYPE_GA_ARTIFACT":                         reflect.ValueOf(common.GLOBAL_ATTR_TYPE_GA_ARTIFACT),
		"GLOBAL_ATTR_TYPE_GA_MEMORY":                           reflect.ValueOf(common.GLOBAL_ATTR_TYPE_GA_MEMORY),
		"GLOBAL_ATTR_TYPE_GA_NONE":                             reflect.ValueOf(common.GLOBAL_ATTR_TYPE_GA_NONE),
		"GLOBAL_ATTR_TYPE_name":                                reflect.ValueOf(&common.GLOBAL_ATTR_TYPE_name).Elem(),
		"GLOBAL_ATTR_TYPE_value":                               reflect.ValueOf(&common.GLOBAL_ATTR_TYPE_value).Elem(),
		"GM_XML_OPERATE_ACTIVITY_INFO":                         reflect.ValueOf(common.GM_XML_OPERATE_ACTIVITY_INFO),
		"GM_XML_OPERATE_GIFT_INFO":                             reflect.ValueOf(common.GM_XML_OPERATE_GIFT_INFO),
		"GM_XML_OPERATE_MAX":                                   reflect.ValueOf(common.GM_XML_OPERATE_MAX),
		"GM_XML_OPERATE_NONE":                                  reflect.ValueOf(common.GM_XML_OPERATE_NONE),
		"GM_XML_name":                                          reflect.ValueOf(&common.GM_XML_name).Elem(),
		"GM_XML_value":                                         reflect.ValueOf(&common.GM_XML_value).Elem(),
		"GODDESS_BODY_TOUCH_TYPE_INTIMATE":                     reflect.ValueOf(common.GODDESS_BODY_TOUCH_TYPE_INTIMATE),
		"GODDESS_BODY_TOUCH_TYPE_NONE":                         reflect.ValueOf(common.GODDESS_BODY_TOUCH_TYPE_NONE),
		"GODDESS_BODY_TOUCH_TYPE_NORMAL":                       reflect.ValueOf(common.GODDESS_BODY_TOUCH_TYPE_NORMAL),
		"GODDESS_BODY_name":                                    reflect.ValueOf(&common.GODDESS_BODY_name).Elem(),
		"GODDESS_BODY_value":                                   reflect.ValueOf(&common.GODDESS_BODY_value).Elem(),
		"GOD_PRESENT_BEHAVIOR_TYPE_GPBT_COLLECTED":             reflect.ValueOf(common.GOD_PRESENT_BEHAVIOR_TYPE_GPBT_COLLECTED),
		"GOD_PRESENT_BEHAVIOR_TYPE_GPBT_NONE":                  reflect.ValueOf(common.GOD_PRESENT_BEHAVIOR_TYPE_GPBT_NONE),
		"GOD_PRESENT_BEHAVIOR_TYPE_GPBT_REPLACE":               reflect.ValueOf(common.GOD_PRESENT_BEHAVIOR_TYPE_GPBT_REPLACE),
		"GOD_PRESENT_BEHAVIOR_TYPE_GPBT_SUMMON":                reflect.ValueOf(common.GOD_PRESENT_BEHAVIOR_TYPE_GPBT_SUMMON),
		"GOD_PRESENT_BEHAVIOR_TYPE_name":                       reflect.ValueOf(&common.GOD_PRESENT_BEHAVIOR_TYPE_name).Elem(),
		"GOD_PRESENT_BEHAVIOR_TYPE_value":                      reflect.ValueOf(&common.GOD_PRESENT_BEHAVIOR_TYPE_value).Elem(),
		"GST_BOSS_BUY_TYPE_GBBT_DIAMOND":                       reflect.ValueOf(common.GST_BOSS_BUY_TYPE_GBBT_DIAMOND),
		"GST_BOSS_BUY_TYPE_GBBT_ITEM":                          reflect.ValueOf(common.GST_BOSS_BUY_TYPE_GBBT_ITEM),
		"GST_BOSS_BUY_TYPE_GBBT_NONE":                          reflect.ValueOf(common.GST_BOSS_BUY_TYPE_GBBT_NONE),
		"GST_BOSS_BUY_TYPE_name":                               reflect.ValueOf(&common.GST_BOSS_BUY_TYPE_name).Elem(),
		"GST_BOSS_BUY_TYPE_value":                              reflect.ValueOf(&common.GST_BOSS_BUY_TYPE_value).Elem(),
		"GST_DRAGON_HP_SLOT_GSTDHS_INIT":                       reflect.ValueOf(common.GST_DRAGON_HP_SLOT_GSTDHS_INIT),
		"GST_DRAGON_HP_SLOT_GSTDHS_NONE":                       reflect.ValueOf(common.GST_DRAGON_HP_SLOT_GSTDHS_NONE),
		"GST_DRAGON_HP_SLOT_name":                              reflect.ValueOf(&common.GST_DRAGON_HP_SLOT_name).Elem(),
		"GST_DRAGON_HP_SLOT_value":                             reflect.ValueOf(&common.GST_DRAGON_HP_SLOT_value).Elem(),
		"GST_DRAGON_POS_GSTDP_FOUR":                            reflect.ValueOf(common.GST_DRAGON_POS_GSTDP_FOUR),
		"GST_DRAGON_POS_GSTDP_NONE":                            reflect.ValueOf(common.GST_DRAGON_POS_GSTDP_NONE),
		"GST_DRAGON_POS_GSTDP_ONE":                             reflect.ValueOf(common.GST_DRAGON_POS_GSTDP_ONE),
		"GST_DRAGON_POS_GSTDP_THREE":                           reflect.ValueOf(common.GST_DRAGON_POS_GSTDP_THREE),
		"GST_DRAGON_POS_GSTDP_TWO":                             reflect.ValueOf(common.GST_DRAGON_POS_GSTDP_TWO),
		"GST_DRAGON_POS_name":                                  reflect.ValueOf(&common.GST_DRAGON_POS_name).Elem(),
		"GST_DRAGON_POS_value":                                 reflect.ValueOf(&common.GST_DRAGON_POS_value).Elem(),
		"GUILD_APPLY_TYPE_GAT_GUILD_COMBINE":                   reflect.ValueOf(common.GUILD_APPLY_TYPE_GAT_GUILD_COMBINE),
		"GUILD_APPLY_TYPE_GAT_NONE":                            reflect.ValueOf(common.GUILD_APPLY_TYPE_GAT_NONE),
		"GUILD_APPLY_TYPE_GAT_USER":                            reflect.ValueOf(common.GUILD_APPLY_TYPE_GAT_USER),
		"GUILD_APPLY_TYPE_name":                                reflect.ValueOf(&common.GUILD_APPLY_TYPE_name).Elem(),
		"GUILD_APPLY_TYPE_value":                               reflect.ValueOf(&common.GUILD_APPLY_TYPE_value).Elem(),
		"GUILD_CHEST_LIKE_TYPE_GCLT_NONE":                      reflect.ValueOf(common.GUILD_CHEST_LIKE_TYPE_GCLT_NONE),
		"GUILD_CHEST_LIKE_TYPE_GCLT_NORMAL":                    reflect.ValueOf(common.GUILD_CHEST_LIKE_TYPE_GCLT_NORMAL),
		"GUILD_CHEST_LIKE_TYPE_GCLT_SPECIFICAL":                reflect.ValueOf(common.GUILD_CHEST_LIKE_TYPE_GCLT_SPECIFICAL),
		"GUILD_CHEST_LIKE_TYPE_name":                           reflect.ValueOf(&common.GUILD_CHEST_LIKE_TYPE_name).Elem(),
		"GUILD_CHEST_LIKE_TYPE_value":                          reflect.ValueOf(&common.GUILD_CHEST_LIKE_TYPE_value).Elem(),
		"GUILD_COMBINE_APPLY_TYPE_GCAT_CANCEL":                 reflect.ValueOf(common.GUILD_COMBINE_APPLY_TYPE_GCAT_CANCEL),
		"GUILD_COMBINE_APPLY_TYPE_GCAT_INVITE":                 reflect.ValueOf(common.GUILD_COMBINE_APPLY_TYPE_GCAT_INVITE),
		"GUILD_COMBINE_APPLY_TYPE_GCAT_NONE":                   reflect.ValueOf(common.GUILD_COMBINE_APPLY_TYPE_GCAT_NONE),
		"GUILD_COMBINE_APPLY_TYPE_GCAT_REQUEST":                reflect.ValueOf(common.GUILD_COMBINE_APPLY_TYPE_GCAT_REQUEST),
		"GUILD_COMBINE_APPLY_TYPE_name":                        reflect.ValueOf(&common.GUILD_COMBINE_APPLY_TYPE_name).Elem(),
		"GUILD_COMBINE_APPLY_TYPE_value":                       reflect.ValueOf(&common.GUILD_COMBINE_APPLY_TYPE_value).Elem(),
		"GUILD_COMBINE_STATUS_GCS_CAN_OPERATE":                 reflect.ValueOf(common.GUILD_COMBINE_STATUS_GCS_CAN_OPERATE),
		"GUILD_COMBINE_STATUS_GCS_FUNC_LOCKED":                 reflect.ValueOf(common.GUILD_COMBINE_STATUS_GCS_FUNC_LOCKED),
		"GUILD_COMBINE_STATUS_GCS_HAS_OPERATED":                reflect.ValueOf(common.GUILD_COMBINE_STATUS_GCS_HAS_OPERATED),
		"GUILD_COMBINE_STATUS_GCS_LEADER_NOT_JOIN":             reflect.ValueOf(common.GUILD_COMBINE_STATUS_GCS_LEADER_NOT_JOIN),
		"GUILD_COMBINE_STATUS_GCS_MEMBER_NUM_LIMIT":            reflect.ValueOf(common.GUILD_COMBINE_STATUS_GCS_MEMBER_NUM_LIMIT),
		"GUILD_COMBINE_STATUS_GCS_NONE":                        reflect.ValueOf(common.GUILD_COMBINE_STATUS_GCS_NONE),
		"GUILD_COMBINE_STATUS_name":                            reflect.ValueOf(&common.GUILD_COMBINE_STATUS_name).Elem(),
		"GUILD_COMBINE_STATUS_value":                           reflect.ValueOf(&common.GUILD_COMBINE_STATUS_value).Elem(),
		"GUILD_CREATE_DAY_GCD_GREATE":                          reflect.ValueOf(common.GUILD_CREATE_DAY_GCD_GREATE),
		"GUILD_CREATE_DAY_GCD_LESS_EQUAL":                      reflect.ValueOf(common.GUILD_CREATE_DAY_GCD_LESS_EQUAL),
		"GUILD_CREATE_DAY_name":                                reflect.ValueOf(&common.GUILD_CREATE_DAY_name).Elem(),
		"GUILD_CREATE_DAY_value":                               reflect.ValueOf(&common.GUILD_CREATE_DAY_value).Elem(),
		"GUILD_DUNGEON_MONTHLY_TIME_GDMT_NONE":                 reflect.ValueOf(common.GUILD_DUNGEON_MONTHLY_TIME_GDMT_NONE),
		"GUILD_DUNGEON_MONTHLY_TIME_MONTHLY_RESET_BEGIN":       reflect.ValueOf(common.GUILD_DUNGEON_MONTHLY_TIME_MONTHLY_RESET_BEGIN),
		"GUILD_DUNGEON_MONTHLY_TIME_MONTHLY_RESET_CLOSE":       reflect.ValueOf(common.GUILD_DUNGEON_MONTHLY_TIME_MONTHLY_RESET_CLOSE),
		"GUILD_DUNGEON_MONTHLY_TIME_MONTHLY_RESET_END":         reflect.ValueOf(common.GUILD_DUNGEON_MONTHLY_TIME_MONTHLY_RESET_END),
		"GUILD_DUNGEON_MONTHLY_TIME_name":                      reflect.ValueOf(&common.GUILD_DUNGEON_MONTHLY_TIME_name).Elem(),
		"GUILD_DUNGEON_MONTHLY_TIME_value":                     reflect.ValueOf(&common.GUILD_DUNGEON_MONTHLY_TIME_value).Elem(),
		"GUILD_DUNGEON_WEEKLY_TIME_GDWT_NONE":                  reflect.ValueOf(common.GUILD_DUNGEON_WEEKLY_TIME_GDWT_NONE),
		"GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_BEGIN":         reflect.ValueOf(common.GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_BEGIN),
		"GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_CLOSE":         reflect.ValueOf(common.GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_CLOSE),
		"GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_END":           reflect.ValueOf(common.GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_END),
		"GUILD_DUNGEON_WEEKLY_TIME_name":                       reflect.ValueOf(&common.GUILD_DUNGEON_WEEKLY_TIME_name).Elem(),
		"GUILD_DUNGEON_WEEKLY_TIME_value":                      reflect.ValueOf(&common.GUILD_DUNGEON_WEEKLY_TIME_value).Elem(),
		"GUILD_MOBILIZATION_RANK_TYPE_GMRT_NONE":               reflect.ValueOf(common.GUILD_MOBILIZATION_RANK_TYPE_GMRT_NONE),
		"GUILD_MOBILIZATION_RANK_TYPE_GMRT_NORMAL":             reflect.ValueOf(common.GUILD_MOBILIZATION_RANK_TYPE_GMRT_NORMAL),
		"GUILD_MOBILIZATION_RANK_TYPE_GMRT_SIMPLE":             reflect.ValueOf(common.GUILD_MOBILIZATION_RANK_TYPE_GMRT_SIMPLE),
		"GUILD_MOBILIZATION_RANK_TYPE_name":                    reflect.ValueOf(&common.GUILD_MOBILIZATION_RANK_TYPE_name).Elem(),
		"GUILD_MOBILIZATION_RANK_TYPE_value":                   reflect.ValueOf(&common.GUILD_MOBILIZATION_RANK_TYPE_value).Elem(),
		"GVG_DONATE_TYPE_GVGD_ITEM":                            reflect.ValueOf(common.GVG_DONATE_TYPE_GVGD_ITEM),
		"GVG_DONATE_TYPE_GVGD_MANAGER":                         reflect.ValueOf(common.GVG_DONATE_TYPE_GVGD_MANAGER),
		"GVG_DONATE_TYPE_GVGD_MAX":                             reflect.ValueOf(common.GVG_DONATE_TYPE_GVGD_MAX),
		"GVG_DONATE_TYPE_GVGD_NONE":                            reflect.ValueOf(common.GVG_DONATE_TYPE_GVGD_NONE),
		"GVG_DONATE_TYPE_name":                                 reflect.ValueOf(&common.GVG_DONATE_TYPE_name).Elem(),
		"GVG_DONATE_TYPE_value":                                reflect.ValueOf(&common.GVG_DONATE_TYPE_value).Elem(),
		"HANDBOOK_HT_FETTER":                                   reflect.ValueOf(common.HANDBOOK_HT_FETTER),
		"HANDBOOK_HT_HERO":                                     reflect.ValueOf(common.HANDBOOK_HT_HERO),
		"HANDBOOK_HT_MAX":                                      reflect.ValueOf(common.HANDBOOK_HT_MAX),
		"HANDBOOK_HT_NONE":                                     reflect.ValueOf(common.HANDBOOK_HT_NONE),
		"HANDBOOK_name":                                        reflect.ValueOf(&common.HANDBOOK_name).Elem(),
		"HANDBOOK_value":                                       reflect.ValueOf(&common.HANDBOOK_value).Elem(),
		"HERO_CRYSTAL_TYPE_HCT_CONTRACT":                       reflect.ValueOf(common.HERO_CRYSTAL_TYPE_HCT_CONTRACT),
		"HERO_CRYSTAL_TYPE_HCT_FREEDOM":                        reflect.ValueOf(common.HERO_CRYSTAL_TYPE_HCT_FREEDOM),
		"HERO_CRYSTAL_TYPE_HCT_RESONANCE":                      reflect.ValueOf(common.HERO_CRYSTAL_TYPE_HCT_RESONANCE),
		"HERO_CRYSTAL_TYPE_name":                               reflect.ValueOf(&common.HERO_CRYSTAL_TYPE_name).Elem(),
		"HERO_CRYSTAL_TYPE_value":                              reflect.ValueOf(&common.HERO_CRYSTAL_TYPE_value).Elem(),
		"HERO_TAG_HT_BLESSED":                                  reflect.ValueOf(common.HERO_TAG_HT_BLESSED),
		"HERO_TAG_HT_CONTRACT":                                 reflect.ValueOf(common.HERO_TAG_HT_CONTRACT),
		"HERO_TAG_HT_MARTERIAL":                                reflect.ValueOf(common.HERO_TAG_HT_MARTERIAL),
		"HERO_TAG_name":                                        reflect.ValueOf(&common.HERO_TAG_name).Elem(),
		"HERO_TAG_value":                                       reflect.ValueOf(&common.HERO_TAG_value).Elem(),
		"HOT_RANK_CROSS_MERGE_TIME_HRCMT_BEGIN":                reflect.ValueOf(common.HOT_RANK_CROSS_MERGE_TIME_HRCMT_BEGIN),
		"HOT_RANK_CROSS_MERGE_TIME_HRCMT_END":                  reflect.ValueOf(common.HOT_RANK_CROSS_MERGE_TIME_HRCMT_END),
		"HOT_RANK_CROSS_MERGE_TIME_HRCMT_NONE":                 reflect.ValueOf(common.HOT_RANK_CROSS_MERGE_TIME_HRCMT_NONE),
		"HOT_RANK_CROSS_MERGE_TIME_name":                       reflect.ValueOf(&common.HOT_RANK_CROSS_MERGE_TIME_name).Elem(),
		"HOT_RANK_CROSS_MERGE_TIME_value":                      reflect.ValueOf(&common.HOT_RANK_CROSS_MERGE_TIME_value).Elem(),
		"ITEM_TYPE_AVATAR_ITEM_TYPE":                           reflect.ValueOf(common.ITEM_TYPE_AVATAR_ITEM_TYPE),
		"ITEM_TYPE_AWAKEN_MATERIAL_ITEM_TYPE":                  reflect.ValueOf(common.ITEM_TYPE_AWAKEN_MATERIAL_ITEM_TYPE),
		"ITEM_TYPE_BOX_ITEM_TYPE":                              reflect.ValueOf(common.ITEM_TYPE_BOX_ITEM_TYPE),
		"ITEM_TYPE_GOLD_ITEM_TYPE":                             reflect.ValueOf(common.ITEM_TYPE_GOLD_ITEM_TYPE),
		"ITEM_TYPE_HERO_EXP_ITEM_TYPE":                         reflect.ValueOf(common.ITEM_TYPE_HERO_EXP_ITEM_TYPE),
		"ITEM_TYPE_MAZE_ITEM_TYPE":                             reflect.ValueOf(common.ITEM_TYPE_MAZE_ITEM_TYPE),
		"ITEM_TYPE_NONE_ITEM_TYPE":                             reflect.ValueOf(common.ITEM_TYPE_NONE_ITEM_TYPE),
		"ITEM_TYPE_SEASON_JEWELRY_SELECTIVE_TYPE":              reflect.ValueOf(common.ITEM_TYPE_SEASON_JEWELRY_SELECTIVE_TYPE),
		"ITEM_TYPE_SELECTIVE_TYPE":                             reflect.ValueOf(common.ITEM_TYPE_SELECTIVE_TYPE),
		"ITEM_TYPE_SKIN_ITEM_TYPE":                             reflect.ValueOf(common.ITEM_TYPE_SKIN_ITEM_TYPE),
		"ITEM_TYPE_name":                                       reflect.ValueOf(&common.ITEM_TYPE_name).Elem(),
		"ITEM_TYPE_value":                                      reflect.ValueOf(&common.ITEM_TYPE_value).Elem(),
		"KNIGHT_ID_KNIGHT_ID_NONE":                             reflect.ValueOf(common.KNIGHT_ID_KNIGHT_ID_NONE),
		"KNIGHT_ID_MAIN_ROLE_FEMALE":                           reflect.ValueOf(common.KNIGHT_ID_MAIN_ROLE_FEMALE),
		"KNIGHT_ID_MAIN_ROLE_MALE":                             reflect.ValueOf(common.KNIGHT_ID_MAIN_ROLE_MALE),
		"KNIGHT_ID_name":                                       reflect.ValueOf(&common.KNIGHT_ID_name).Elem(),
		"KNIGHT_ID_value":                                      reflect.ValueOf(&common.KNIGHT_ID_value).Elem(),
		"MAIL_COND_TYPE_CREATE_TIME":                           reflect.ValueOf(common.MAIL_COND_TYPE_CREATE_TIME),
		"MAIL_COND_TYPE_LEVEL":                                 reflect.ValueOf(common.MAIL_COND_TYPE_LEVEL),
		"MAIL_COND_TYPE_MAIL_COND_NONE":                        reflect.ValueOf(common.MAIL_COND_TYPE_MAIL_COND_NONE),
		"MAIL_COND_TYPE_VIP":                                   reflect.ValueOf(common.MAIL_COND_TYPE_VIP),
		"MAIL_COND_name":                                       reflect.ValueOf(&common.MAIL_COND_name).Elem(),
		"MAIL_COND_value":                                      reflect.ValueOf(&common.MAIL_COND_value).Elem(),
		"MAIL_FLAG_DELETE":                                     reflect.ValueOf(common.MAIL_FLAG_DELETE),
		"MAIL_FLAG_DRAW":                                       reflect.ValueOf(common.MAIL_FLAG_DRAW),
		"MAIL_FLAG_NEW":                                        reflect.ValueOf(common.MAIL_FLAG_NEW),
		"MAIL_FLAG_READ":                                       reflect.ValueOf(common.MAIL_FLAG_READ),
		"MAIL_FLAG_name":                                       reflect.ValueOf(&common.MAIL_FLAG_name).Elem(),
		"MAIL_FLAG_value":                                      reflect.ValueOf(&common.MAIL_FLAG_value).Elem(),
		"MAU_MAU_NONE":                                         reflect.ValueOf(common.MAU_MAU_NONE),
		"MAU_MAU_RESERVE":                                      reflect.ValueOf(common.MAU_MAU_RESERVE),
		"MAU_name":                                             reflect.ValueOf(&common.MAU_name).Elem(),
		"MAU_value":                                            reflect.ValueOf(&common.MAU_value).Elem(),
		"MIRAGE_SWEEP_TYPE_MST_ASSISTANT":                      reflect.ValueOf(common.MIRAGE_SWEEP_TYPE_MST_ASSISTANT),
		"MIRAGE_SWEEP_TYPE_MST_NONE":                           reflect.ValueOf(common.MIRAGE_SWEEP_TYPE_MST_NONE),
		"MIRAGE_SWEEP_TYPE_MST_NORMAL":                         reflect.ValueOf(common.MIRAGE_SWEEP_TYPE_MST_NORMAL),
		"MIRAGE_SWEEP_TYPE_name":                               reflect.ValueOf(&common.MIRAGE_SWEEP_TYPE_name).Elem(),
		"MIRAGE_SWEEP_TYPE_value":                              reflect.ValueOf(&common.MIRAGE_SWEEP_TYPE_value).Elem(),
		"OFFLINE_REASON_GM_KICK":                               reflect.ValueOf(common.OFFLINE_REASON_GM_KICK),
		"OFFLINE_REASON_NORMAL":                                reflect.ValueOf(common.OFFLINE_REASON_NORMAL),
		"OFFLINE_REASON_OTHERS":                                reflect.ValueOf(common.OFFLINE_REASON_OTHERS),
		"OFFLINE_REASON_REPEATED_KICK":                         reflect.ValueOf(common.OFFLINE_REASON_REPEATED_KICK),
		"OFFLINE_REASON_RESTART":                               reflect.ValueOf(common.OFFLINE_REASON_RESTART),
		"OFFLINE_REASON_WRONG":                                 reflect.ValueOf(common.OFFLINE_REASON_WRONG),
		"OFFLINE_name":                                         reflect.ValueOf(&common.OFFLINE_name).Elem(),
		"OFFLINE_value":                                        reflect.ValueOf(&common.OFFLINE_value).Elem(),
		"OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_GIFT":     reflect.ValueOf(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_GIFT),
		"OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_NONE":     reflect.ValueOf(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_NONE),
		"OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_TASK":     reflect.ValueOf(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_TASK),
		"OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT":           reflect.ValueOf(common.OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT),
		"OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT":        reflect.ValueOf(common.OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT),
		"OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT":        reflect.ValueOf(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT),
		"OPERATE_ACTIVITY_TYPE_name":                           reflect.ValueOf(&common.OPERATE_ACTIVITY_TYPE_name).Elem(),
		"OPERATE_ACTIVITY_TYPE_value":                          reflect.ValueOf(&common.OPERATE_ACTIVITY_TYPE_value).Elem(),
		"ORDER_STATUS_OS_FAIL":                                 reflect.ValueOf(common.ORDER_STATUS_OS_FAIL),
		"ORDER_STATUS_OS_NONE":                                 reflect.ValueOf(common.ORDER_STATUS_OS_NONE),
		"ORDER_STATUS_OS_SAVE":                                 reflect.ValueOf(common.ORDER_STATUS_OS_SAVE),
		"ORDER_STATUS_OS_SUCCESS":                              reflect.ValueOf(common.ORDER_STATUS_OS_SUCCESS),
		"ORDER_STATUS_name":                                    reflect.ValueOf(&common.ORDER_STATUS_name).Elem(),
		"ORDER_STATUS_value":                                   reflect.ValueOf(&common.ORDER_STATUS_value).Elem(),
		"ORDER_TYPE_OT_COUPON":                                 reflect.ValueOf(common.ORDER_TYPE_OT_COUPON),
		"ORDER_TYPE_OT_RECHARGE":                               reflect.ValueOf(common.ORDER_TYPE_OT_RECHARGE),
		"ORDER_TYPE_OT_REPLACEMENT":                            reflect.ValueOf(common.ORDER_TYPE_OT_REPLACEMENT),
		"ORDER_TYPE_OT_WELFARE":                                reflect.ValueOf(common.ORDER_TYPE_OT_WELFARE),
		"ORDER_TYPE_name":                                      reflect.ValueOf(&common.ORDER_TYPE_name).Elem(),
		"ORDER_TYPE_value":                                     reflect.ValueOf(&common.ORDER_TYPE_value).Elem(),
		"PARAM_LIMIT_PL_HANDBOOK_TYPE_MAX_NUM":                 reflect.ValueOf(common.PARAM_LIMIT_PL_HANDBOOK_TYPE_MAX_NUM),
		"PARAM_LIMIT_PL_NONE":                                  reflect.ValueOf(common.PARAM_LIMIT_PL_NONE),
		"PARAM_LIMIT_name":                                     reflect.ValueOf(&common.PARAM_LIMIT_name).Elem(),
		"PARAM_LIMIT_value":                                    reflect.ValueOf(&common.PARAM_LIMIT_value).Elem(),
		"PEAK_PHASE_STATE_PPS_END":                             reflect.ValueOf(common.PEAK_PHASE_STATE_PPS_END),
		"PEAK_PHASE_STATE_PPS_INIT":                            reflect.ValueOf(common.PEAK_PHASE_STATE_PPS_INIT),
		"PEAK_PHASE_STATE_PPS_INIT_DATA":                       reflect.ValueOf(common.PEAK_PHASE_STATE_PPS_INIT_DATA),
		"PEAK_PHASE_STATE_PPS_RUNNING":                         reflect.ValueOf(common.PEAK_PHASE_STATE_PPS_RUNNING),
		"PEAK_PHASE_STATE_name":                                reflect.ValueOf(&common.PEAK_PHASE_STATE_name).Elem(),
		"PEAK_PHASE_STATE_value":                               reflect.ValueOf(&common.PEAK_PHASE_STATE_value).Elem(),
		"PEAK_RANK_TYPE_PRT_NONE":                              reflect.ValueOf(common.PEAK_RANK_TYPE_PRT_NONE),
		"PEAK_RANK_TYPE_PRT_PHASE_TOP8":                        reflect.ValueOf(common.PEAK_RANK_TYPE_PRT_PHASE_TOP8),
		"PEAK_RANK_TYPE_PRT_SEASON_ALL":                        reflect.ValueOf(common.PEAK_RANK_TYPE_PRT_SEASON_ALL),
		"PEAK_RANK_TYPE_PRT_SEASON_TOP3":                       reflect.ValueOf(common.PEAK_RANK_TYPE_PRT_SEASON_TOP3),
		"PEAK_RANK_TYPE_name":                                  reflect.ValueOf(&common.PEAK_RANK_TYPE_name).Elem(),
		"PEAK_RANK_TYPE_value":                                 reflect.ValueOf(&common.PEAK_RANK_TYPE_value).Elem(),
		"PEAK_ROUND_DURATION_PRD_NONE":                         reflect.ValueOf(common.PEAK_ROUND_DURATION_PRD_NONE),
		"PEAK_ROUND_DURATION_PRD_TIME":                         reflect.ValueOf(common.PEAK_ROUND_DURATION_PRD_TIME),
		"PEAK_ROUND_DURATION_name":                             reflect.ValueOf(&common.PEAK_ROUND_DURATION_name).Elem(),
		"PEAK_ROUND_DURATION_value":                            reflect.ValueOf(&common.PEAK_ROUND_DURATION_value).Elem(),
		"PEAK_ROUND_STATE_PRS_END":                             reflect.ValueOf(common.PEAK_ROUND_STATE_PRS_END),
		"PEAK_ROUND_STATE_PRS_INIT":                            reflect.ValueOf(common.PEAK_ROUND_STATE_PRS_INIT),
		"PEAK_ROUND_STATE_PRS_RUNNING":                         reflect.ValueOf(common.PEAK_ROUND_STATE_PRS_RUNNING),
		"PEAK_ROUND_STATE_name":                                reflect.ValueOf(&common.PEAK_ROUND_STATE_name).Elem(),
		"PEAK_ROUND_STATE_value":                               reflect.ValueOf(&common.PEAK_ROUND_STATE_value).Elem(),
		"PEAK_SEASON_RANK_PSR_NONE":                            reflect.ValueOf(common.PEAK_SEASON_RANK_PSR_NONE),
		"PEAK_SEASON_RANK_PSR_SHOW_COUNT":                      reflect.ValueOf(common.PEAK_SEASON_RANK_PSR_SHOW_COUNT),
		"PEAK_SEASON_RANK_name":                                reflect.ValueOf(&common.PEAK_SEASON_RANK_name).Elem(),
		"PEAK_SEASON_RANK_value":                               reflect.ValueOf(&common.PEAK_SEASON_RANK_value).Elem(),
		"PEAK_SEASON_STATE_PSS_END":                            reflect.ValueOf(common.PEAK_SEASON_STATE_PSS_END),
		"PEAK_SEASON_STATE_PSS_INIT":                           reflect.ValueOf(common.PEAK_SEASON_STATE_PSS_INIT),
		"PEAK_SEASON_STATE_PSS_RUNNING":                        reflect.ValueOf(common.PEAK_SEASON_STATE_PSS_RUNNING),
		"PEAK_SEASON_STATE_name":                               reflect.ValueOf(&common.PEAK_SEASON_STATE_name).Elem(),
		"PEAK_SEASON_STATE_value":                              reflect.ValueOf(&common.PEAK_SEASON_STATE_value).Elem(),
		"PEAK_STATUS_PS_INIT":                                  reflect.ValueOf(common.PEAK_STATUS_PS_INIT),
		"PEAK_STATUS_PS_NONE":                                  reflect.ValueOf(common.PEAK_STATUS_PS_NONE),
		"PEAK_STATUS_PS_NORMAL":                                reflect.ValueOf(common.PEAK_STATUS_PS_NORMAL),
		"PEAK_STATUS_PS_RESET":                                 reflect.ValueOf(common.PEAK_STATUS_PS_RESET),
		"PEAK_STATUS_name":                                     reflect.ValueOf(&common.PEAK_STATUS_name).Elem(),
		"PEAK_STATUS_value":                                    reflect.ValueOf(&common.PEAK_STATUS_value).Elem(),
		"PROMOTION_GIFT_LIMIT_TYPE_PGLT_MAX":                   reflect.ValueOf(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_MAX),
		"PROMOTION_GIFT_LIMIT_TYPE_PGLT_NONE":                  reflect.ValueOf(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_NONE),
		"PROMOTION_GIFT_LIMIT_TYPE_PGLT_PGP":                   reflect.ValueOf(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_PGP),
		"PROMOTION_GIFT_LIMIT_TYPE_PGLT_RECHARGE_AMOUNT":       reflect.ValueOf(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_RECHARGE_AMOUNT),
		"PROMOTION_GIFT_LIMIT_TYPE_PGLT_USER_LEVEL":            reflect.ValueOf(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_USER_LEVEL),
		"PROMOTION_GIFT_LIMIT_TYPE_name":                       reflect.ValueOf(&common.PROMOTION_GIFT_LIMIT_TYPE_name).Elem(),
		"PROMOTION_GIFT_LIMIT_TYPE_value":                      reflect.ValueOf(&common.PROMOTION_GIFT_LIMIT_TYPE_value).Elem(),
		"PURCHASEID_ACTIVITY_COMPLIANCE_RANK_LIKE_COUNT":       reflect.ValueOf(common.PURCHASEID_ACTIVITY_COMPLIANCE_RANK_LIKE_COUNT),
		"PURCHASEID_ACTIVITY_MIRAGE_RANK_LIKE_COUNT":           reflect.ValueOf(common.PURCHASEID_ACTIVITY_MIRAGE_RANK_LIKE_COUNT),
		"PURCHASEID_ACTIVITY_TOWER_RANK_LIKE_COUNT":            reflect.ValueOf(common.PURCHASEID_ACTIVITY_TOWER_RANK_LIKE_COUNT),
		"PURCHASEID_ACTIVITY_TOWER_SEASON_RANK_LIKE_COUNT":     reflect.ValueOf(common.PURCHASEID_ACTIVITY_TOWER_SEASON_RANK_LIKE_COUNT),
		"PURCHASEID_ACTIVITY_TURAN_TABLE_BUY_TICKET":           reflect.ValueOf(common.PURCHASEID_ACTIVITY_TURAN_TABLE_BUY_TICKET),
		"PURCHASEID_ARENA_FIGHT_COUNT":                         reflect.ValueOf(common.PURCHASEID_ARENA_FIGHT_COUNT),
		"PURCHASEID_ARENA_LIKE_COUNT":                          reflect.ValueOf(common.PURCHASEID_ARENA_LIKE_COUNT),
		"PURCHASEID_ARENA_REFRESH_COUNT":                       reflect.ValueOf(common.PURCHASEID_ARENA_REFRESH_COUNT),
		"PURCHASEID_BOSS_RUSH_BUY_COUNT":                       reflect.ValueOf(common.PURCHASEID_BOSS_RUSH_BUY_COUNT),
		"PURCHASEID_DAILY_SPECIAL_DAILY_AWARD":                 reflect.ValueOf(common.PURCHASEID_DAILY_SPECIAL_DAILY_AWARD),
		"PURCHASEID_DISORDER_LAND_BUY_COUNT":                   reflect.ValueOf(common.PURCHASEID_DISORDER_LAND_BUY_COUNT),
		"PURCHASEID_DISPATCH_REFRESH_FREE_COUNT":               reflect.ValueOf(common.PURCHASEID_DISPATCH_REFRESH_FREE_COUNT),
		"PURCHASEID_DROP_ACTIVITY_DAILY_REWARD":                reflect.ValueOf(common.PURCHASEID_DROP_ACTIVITY_DAILY_REWARD),
		"PURCHASEID_FLOWER_PLANT_DAILY_COUNT":                  reflect.ValueOf(common.PURCHASEID_FLOWER_PLANT_DAILY_COUNT),
		"PURCHASEID_FLOWER_SNATCH_DAILY_COUNT":                 reflect.ValueOf(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT),
		"PURCHASEID_FOREST_LOOT_COUNT":                         reflect.ValueOf(common.PURCHASEID_FOREST_LOOT_COUNT),
		"PURCHASEID_FOREST_PEACHFUL_TREE_START_FEED_COUNT":     reflect.ValueOf(common.PURCHASEID_FOREST_PEACHFUL_TREE_START_FEED_COUNT),
		"PURCHASEID_FOREST_PVP_TREE_START_FEED_COUNT":          reflect.ValueOf(common.PURCHASEID_FOREST_PVP_TREE_START_FEED_COUNT),
		"PURCHASEID_GST_BOSS_BUY_CHALLENGE_COUNT":              reflect.ValueOf(common.PURCHASEID_GST_BOSS_BUY_CHALLENGE_COUNT),
		"PURCHASEID_GST_CONTRIBUTION_RANK_LIKE_COUNT":          reflect.ValueOf(common.PURCHASEID_GST_CONTRIBUTION_RANK_LIKE_COUNT),
		"PURCHASEID_GST_DRAGON_BUY_CHALLENGE_COUNT":            reflect.ValueOf(common.PURCHASEID_GST_DRAGON_BUY_CHALLENGE_COUNT),
		"PURCHASEID_GST_KILL_RANK_LIKE_COUNT":                  reflect.ValueOf(common.PURCHASEID_GST_KILL_RANK_LIKE_COUNT),
		"PURCHASEID_GST_ORE_ASSIST":                            reflect.ValueOf(common.PURCHASEID_GST_ORE_ASSIST),
		"PURCHASEID_GST_ORE_FIGHT":                             reflect.ValueOf(common.PURCHASEID_GST_ORE_FIGHT),
		"PURCHASEID_GST_TRIPLE_KILL_RANK_LIKE_COUNT":           reflect.ValueOf(common.PURCHASEID_GST_TRIPLE_KILL_RANK_LIKE_COUNT),
		"PURCHASEID_GUILD_DUNGEON_FIGHT_COUNT":                 reflect.ValueOf(common.PURCHASEID_GUILD_DUNGEON_FIGHT_COUNT),
		"PURCHASEID_GUILD_TALENT_FIFTH_RESET_COUNT":            reflect.ValueOf(common.PURCHASEID_GUILD_TALENT_FIFTH_RESET_COUNT),
		"PURCHASEID_GUILD_TALENT_FIRST_RESET_COUNT":            reflect.ValueOf(common.PURCHASEID_GUILD_TALENT_FIRST_RESET_COUNT),
		"PURCHASEID_GUILD_TALENT_FOURTH_RESET_COUNT":           reflect.ValueOf(common.PURCHASEID_GUILD_TALENT_FOURTH_RESET_COUNT),
		"PURCHASEID_GUILD_TALENT_SECOND_RESET_COUNT":           reflect.ValueOf(common.PURCHASEID_GUILD_TALENT_SECOND_RESET_COUNT),
		"PURCHASEID_GUILD_TALENT_SIXTH_RESET_COUNT":            reflect.ValueOf(common.PURCHASEID_GUILD_TALENT_SIXTH_RESET_COUNT),
		"PURCHASEID_GUILD_TALENT_THIRD_RESET_COUNT":            reflect.ValueOf(common.PURCHASEID_GUILD_TALENT_THIRD_RESET_COUNT),
		"PURCHASEID_MEDAL_DAILY_AWARD_COUNT":                   reflect.ValueOf(common.PURCHASEID_MEDAL_DAILY_AWARD_COUNT),
		"PURCHASEID_MIRAGE_COUPON_BUY_COUNT":                   reflect.ValueOf(common.PURCHASEID_MIRAGE_COUPON_BUY_COUNT),
		"PURCHASEID_MIRAGE_HELL_FIGHT_COUNT":                   reflect.ValueOf(common.PURCHASEID_MIRAGE_HELL_FIGHT_COUNT),
		"PURCHASEID_MIRAGE_SKY_FIGHT_COUNT":                    reflect.ValueOf(common.PURCHASEID_MIRAGE_SKY_FIGHT_COUNT),
		"PURCHASEID_MONTHLY_CARD_GROW_UP_DAILY_AWARD_COUNT":    reflect.ValueOf(common.PURCHASEID_MONTHLY_CARD_GROW_UP_DAILY_AWARD_COUNT),
		"PURCHASEID_MONTHLY_CARD_SUPPLY_DAILY_AWARD_COUNT":     reflect.ValueOf(common.PURCHASEID_MONTHLY_CARD_SUPPLY_DAILY_AWARD_COUNT),
		"PURCHASEID_MONTH_TASKS_DAILY_AWARD_RECV_COUNT":        reflect.ValueOf(common.PURCHASEID_MONTH_TASKS_DAILY_AWARD_RECV_COUNT),
		"PURCHASEID_NONE":                                      reflect.ValueOf(common.PURCHASEID_NONE),
		"PURCHASEID_PEAK_WORSHIP_COUNT":                        reflect.ValueOf(common.PURCHASEID_PEAK_WORSHIP_COUNT),
		"PURCHASEID_SEASON_ARENA_BUY_CHALLENGE_COUNT":          reflect.ValueOf(common.PURCHASEID_SEASON_ARENA_BUY_CHALLENGE_COUNT),
		"PURCHASEID_SEASON_ARENA_REFRESH_OPPONENT_COUNT":       reflect.ValueOf(common.PURCHASEID_SEASON_ARENA_REFRESH_OPPONENT_COUNT),
		"PURCHASEID_SHARE_RECV_AWARD_COUNT":                    reflect.ValueOf(common.PURCHASEID_SHARE_RECV_AWARD_COUNT),
		"PURCHASEID_SPEED_ONHOOK":                              reflect.ValueOf(common.PURCHASEID_SPEED_ONHOOK),
		"PURCHASEID_TALES_ELITE_COUNT":                         reflect.ValueOf(common.PURCHASEID_TALES_ELITE_COUNT),
		"PURCHASEID_TOWERSTAR_DAILY_AWARD_COUNT":               reflect.ValueOf(common.PURCHASEID_TOWERSTAR_DAILY_AWARD_COUNT),
		"PURCHASEID_TOWER_SEASON_WEEKLY_LIKE_COUNT":            reflect.ValueOf(common.PURCHASEID_TOWER_SEASON_WEEKLY_LIKE_COUNT),
		"PURCHASEID_TOWER_SWEEP_COUNT":                         reflect.ValueOf(common.PURCHASEID_TOWER_SWEEP_COUNT),
		"PURCHASEID_TRIAL_EMBLEM_COUNT":                        reflect.ValueOf(common.PURCHASEID_TRIAL_EMBLEM_COUNT),
		"PURCHASEID_TRIAL_EQUIP_COUNT":                         reflect.ValueOf(common.PURCHASEID_TRIAL_EQUIP_COUNT),
		"PURCHASEID_TRIAL_EXP_COUNT":                           reflect.ValueOf(common.PURCHASEID_TRIAL_EXP_COUNT),
		"PURCHASEID_TRIAL_GOLD_COUNT":                          reflect.ValueOf(common.PURCHASEID_TRIAL_GOLD_COUNT),
		"PURCHASEID_TRIAL_HERO_COUNT":                          reflect.ValueOf(common.PURCHASEID_TRIAL_HERO_COUNT),
		"PURCHASEID_WORLD_BOSS_DAILY_WORSHIP_COUNT":            reflect.ValueOf(common.PURCHASEID_WORLD_BOSS_DAILY_WORSHIP_COUNT),
		"PURCHASEID_WRESTLE_LIKE_COUNT":                        reflect.ValueOf(common.PURCHASEID_WRESTLE_LIKE_COUNT),
		"PURCHASEID_name":                                      reflect.ValueOf(&common.PURCHASEID_name).Elem(),
		"PURCHASEID_value":                                     reflect.ValueOf(&common.PURCHASEID_value).Elem(),
		"PURCHASE_FACTION_NONE_NUM":                            reflect.ValueOf(common.PURCHASE_FACTION_NONE_NUM),
		"PURCHASE_FACTION_PILL_NUM":                            reflect.ValueOf(common.PURCHASE_FACTION_PILL_NUM),
		"PURCHASE_TYPE_PT_COUPON":                              reflect.ValueOf(common.PURCHASE_TYPE_PT_COUPON),
		"PURCHASE_TYPE_PT_NONE":                                reflect.ValueOf(common.PURCHASE_TYPE_PT_NONE),
		"PURCHASE_TYPE_PT_NORMAL":                              reflect.ValueOf(common.PURCHASE_TYPE_PT_NORMAL),
		"PURCHASE_TYPE_name":                                   reflect.ValueOf(&common.PURCHASE_TYPE_name).Elem(),
		"PURCHASE_TYPE_value":                                  reflect.ValueOf(&common.PURCHASE_TYPE_value).Elem(),
		"PURCHASE_name":                                        reflect.ValueOf(&common.PURCHASE_name).Elem(),
		"PURCHASE_value":                                       reflect.ValueOf(&common.PURCHASE_value).Elem(),
		"PUSH_SET_TYPE_PST_FLOWER":                             reflect.ValueOf(common.PUSH_SET_TYPE_PST_FLOWER),
		"PUSH_SET_TYPE_PST_MAX":                                reflect.ValueOf(common.PUSH_SET_TYPE_PST_MAX),
		"PUSH_SET_TYPE_PST_NONE":                               reflect.ValueOf(common.PUSH_SET_TYPE_PST_NONE),
		"PUSH_SET_TYPE_name":                                   reflect.ValueOf(&common.PUSH_SET_TYPE_name).Elem(),
		"PUSH_SET_TYPE_value":                                  reflect.ValueOf(&common.PUSH_SET_TYPE_value).Elem(),
		"QUALITY_BLUE":                                         reflect.ValueOf(common.QUALITY_BLUE),
		"QUALITY_GOLDEN":                                       reflect.ValueOf(common.QUALITY_GOLDEN),
		"QUALITY_GREEN":                                        reflect.ValueOf(common.QUALITY_GREEN),
		"QUALITY_GREY":                                         reflect.ValueOf(common.QUALITY_GREY),
		"QUALITY_NONE_QUALITY":                                 reflect.ValueOf(common.QUALITY_NONE_QUALITY),
		"QUALITY_ORANGE":                                       reflect.ValueOf(common.QUALITY_ORANGE),
		"QUALITY_PURPLE":                                       reflect.ValueOf(common.QUALITY_PURPLE),
		"QUALITY_RED":                                          reflect.ValueOf(common.QUALITY_RED),
		"QUALITY_name":                                         reflect.ValueOf(&common.QUALITY_name).Elem(),
		"QUALITY_value":                                        reflect.ValueOf(&common.QUALITY_value).Elem(),
		"RANK_ID_NONE":                                         reflect.ValueOf(common.RANK_ID_NONE),
		"RANK_ID_TOWER":                                        reflect.ValueOf(common.RANK_ID_TOWER),
		"RANK_TYPE_MAXIMUM":                                    reflect.ValueOf(common.RANK_TYPE_MAXIMUM),
		"RANK_TYPE_MINIMUM":                                    reflect.ValueOf(common.RANK_TYPE_MINIMUM),
		"RANK_TYPE_name":                                       reflect.ValueOf(&common.RANK_TYPE_name).Elem(),
		"RANK_TYPE_value":                                      reflect.ValueOf(&common.RANK_TYPE_value).Elem(),
		"RANK_name":                                            reflect.ValueOf(&common.RANK_name).Elem(),
		"RANK_value":                                           reflect.ValueOf(&common.RANK_value).Elem(),
		"RECHARGE_TYPE_RT_ACTIVITY_COUPON":                     reflect.ValueOf(common.RECHARGE_TYPE_RT_ACTIVITY_COUPON),
		"RECHARGE_TYPE_RT_ACTIVITY_RECHARGE":                   reflect.ValueOf(common.RECHARGE_TYPE_RT_ACTIVITY_RECHARGE),
		"RECHARGE_TYPE_RT_COUPON":                              reflect.ValueOf(common.RECHARGE_TYPE_RT_COUPON),
		"RECHARGE_TYPE_RT_FIRST_GIFT":                          reflect.ValueOf(common.RECHARGE_TYPE_RT_FIRST_GIFT),
		"RECHARGE_TYPE_RT_GIFT":                                reflect.ValueOf(common.RECHARGE_TYPE_RT_GIFT),
		"RECHARGE_TYPE_RT_LIFELONG_GIFT":                       reflect.ValueOf(common.RECHARGE_TYPE_RT_LIFELONG_GIFT),
		"RECHARGE_TYPE_RT_MONTHLY_CARD":                        reflect.ValueOf(common.RECHARGE_TYPE_RT_MONTHLY_CARD),
		"RECHARGE_TYPE_RT_NORMAL":                              reflect.ValueOf(common.RECHARGE_TYPE_RT_NORMAL),
		"RECHARGE_TYPE_RT_PASS":                                reflect.ValueOf(common.RECHARGE_TYPE_RT_PASS),
		"RECHARGE_TYPE_RT_PROMOTION":                           reflect.ValueOf(common.RECHARGE_TYPE_RT_PROMOTION),
		"RECHARGE_TYPE_RT_PUSH_GIFT":                           reflect.ValueOf(common.RECHARGE_TYPE_RT_PUSH_GIFT),
		"RECHARGE_TYPE_RT_VIP_GIFT":                            reflect.ValueOf(common.RECHARGE_TYPE_RT_VIP_GIFT),
		"RECHARGE_TYPE_RT_WEB_COUPON":                          reflect.ValueOf(common.RECHARGE_TYPE_RT_WEB_COUPON),
		"RECHARGE_TYPE_RT_WEB_DIAMOND":                         reflect.ValueOf(common.RECHARGE_TYPE_RT_WEB_DIAMOND),
		"RECHARGE_TYPE_RT_WEB_GIFT":                            reflect.ValueOf(common.RECHARGE_TYPE_RT_WEB_GIFT),
		"RECHARGE_TYPE_name":                                   reflect.ValueOf(&common.RECHARGE_TYPE_name).Elem(),
		"RECHARGE_TYPE_value":                                  reflect.ValueOf(&common.RECHARGE_TYPE_value).Elem(),
		"RED_POINT_ARENA_DIVISION_TASK":                        reflect.ValueOf(common.RED_POINT_ARENA_DIVISION_TASK),
		"RED_POINT_DISORDER_LAND_MAP_STAMINA_IS_FULL":          reflect.ValueOf(common.RED_POINT_DISORDER_LAND_MAP_STAMINA_IS_FULL),
		"RED_POINT_DISPATCH_HAVE_AWARD_RECEIVE":                reflect.ValueOf(common.RED_POINT_DISPATCH_HAVE_AWARD_RECEIVE),
		"RED_POINT_FRIEND_HAVE_LIKE_RECEIVE":                   reflect.ValueOf(common.RED_POINT_FRIEND_HAVE_LIKE_RECEIVE),
		"RED_POINT_FRIEND_HAVE_REQUEST":                        reflect.ValueOf(common.RED_POINT_FRIEND_HAVE_REQUEST),
		"RED_POINT_GOLD_BUY_HAVE_FREE_COUNT":                   reflect.ValueOf(common.RED_POINT_GOLD_BUY_HAVE_FREE_COUNT),
		"RED_POINT_GUILD_DUNGEON_CHAPTER_TASK":                 reflect.ValueOf(common.RED_POINT_GUILD_DUNGEON_CHAPTER_TASK),
		"RED_POINT_GUILD_DUNGEON_FIGHT":                        reflect.ValueOf(common.RED_POINT_GUILD_DUNGEON_FIGHT),
		"RED_POINT_GUILD_DUNGEON_TOP_DIVISION":                 reflect.ValueOf(common.RED_POINT_GUILD_DUNGEON_TOP_DIVISION),
		"RED_POINT_GUILD_HAVE_APPLY":                           reflect.ValueOf(common.RED_POINT_GUILD_HAVE_APPLY),
		"RED_POINT_GUILD_HAVE_COMBINE_APPLY":                   reflect.ValueOf(common.RED_POINT_GUILD_HAVE_COMBINE_APPLY),
		"RED_POINT_GUILD_TALENT_LEVEL_CAN_UP":                  reflect.ValueOf(common.RED_POINT_GUILD_TALENT_LEVEL_CAN_UP),
		"RED_POINT_GUILD_TODAY_SIGN_IN":                        reflect.ValueOf(common.RED_POINT_GUILD_TODAY_SIGN_IN),
		"RED_POINT_GUILd_DUNGEON_BOSS_BOX_AWARD":               reflect.ValueOf(common.RED_POINT_GUILd_DUNGEON_BOSS_BOX_AWARD),
		"RED_POINT_MAZE_HAVE_EVENT_NO_COMPLETE":                reflect.ValueOf(common.RED_POINT_MAZE_HAVE_EVENT_NO_COMPLETE),
		"RED_POINT_MIRAGE_CAN_FIGHT_DEMON":                     reflect.ValueOf(common.RED_POINT_MIRAGE_CAN_FIGHT_DEMON),
		"RED_POINT_MIRAGE_CAN_FIGHT_EMPIRE":                    reflect.ValueOf(common.RED_POINT_MIRAGE_CAN_FIGHT_EMPIRE),
		"RED_POINT_MIRAGE_CAN_FIGHT_FOREST":                    reflect.ValueOf(common.RED_POINT_MIRAGE_CAN_FIGHT_FOREST),
		"RED_POINT_MIRAGE_CAN_FIGHT_MOON":                      reflect.ValueOf(common.RED_POINT_MIRAGE_CAN_FIGHT_MOON),
		"RED_POINT_MIRAGE_CAN_FIGHT_PROTOSS":                   reflect.ValueOf(common.RED_POINT_MIRAGE_CAN_FIGHT_PROTOSS),
		"RED_POINT_MIRAGE_CAN_FIGHT_SIX":                       reflect.ValueOf(common.RED_POINT_MIRAGE_CAN_FIGHT_SIX),
		"RED_POINT_MIRAGE_UNCLAIMED_AWARD_DEMON":               reflect.ValueOf(common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_DEMON),
		"RED_POINT_MIRAGE_UNCLAIMED_AWARD_EMPIRE":              reflect.ValueOf(common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_EMPIRE),
		"RED_POINT_MIRAGE_UNCLAIMED_AWARD_FOREST":              reflect.ValueOf(common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_FOREST),
		"RED_POINT_MIRAGE_UNCLAIMED_AWARD_MOON":                reflect.ValueOf(common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_MOON),
		"RED_POINT_MIRAGE_UNCLAIMED_AWARD_PROTOSS":             reflect.ValueOf(common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_PROTOSS),
		"RED_POINT_MIRAGE_UNCLAIMED_AWARD_SIX":                 reflect.ValueOf(common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_SIX),
		"RED_POINT_NOT_DRAW_MAIL":                              reflect.ValueOf(common.RED_POINT_NOT_DRAW_MAIL),
		"RED_POINT_RANK_ACHIEVE_ARENA":                         reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_ARENA),
		"RED_POINT_RANK_ACHIEVE_DUNGEON":                       reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_DUNGEON),
		"RED_POINT_RANK_ACHIEVE_MIRAGE_DEMON":                  reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_MIRAGE_DEMON),
		"RED_POINT_RANK_ACHIEVE_MIRAGE_EMPIRE":                 reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_MIRAGE_EMPIRE),
		"RED_POINT_RANK_ACHIEVE_MIRAGE_FOREST":                 reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_MIRAGE_FOREST),
		"RED_POINT_RANK_ACHIEVE_MIRAGE_MOON":                   reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_MIRAGE_MOON),
		"RED_POINT_RANK_ACHIEVE_MIRAGE_PROTOSS":                reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_MIRAGE_PROTOSS),
		"RED_POINT_RANK_ACHIEVE_MIRAGE_SIX":                    reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_MIRAGE_SIX),
		"RED_POINT_RANK_ACHIEVE_POWER":                         reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_POWER),
		"RED_POINT_RANK_ACHIEVE_TOWER":                         reflect.ValueOf(common.RED_POINT_RANK_ACHIEVE_TOWER),
		"RED_POINT_RP_NONE":                                    reflect.ValueOf(common.RED_POINT_RP_NONE),
		"RED_POINT_SEASON_COMPLIANCE_AWARD":                    reflect.ValueOf(common.RED_POINT_SEASON_COMPLIANCE_AWARD),
		"RED_POINT_SEASON_MAP_TASK1_RECV_AWARD":                reflect.ValueOf(common.RED_POINT_SEASON_MAP_TASK1_RECV_AWARD),
		"RED_POINT_SEASON_MAP_TASK2_RECV_AWARD":                reflect.ValueOf(common.RED_POINT_SEASON_MAP_TASK2_RECV_AWARD),
		"RED_POINT_TALES_ELITE_OPEN_AND_HAVE_NUM":              reflect.ValueOf(common.RED_POINT_TALES_ELITE_OPEN_AND_HAVE_NUM),
		"RED_POINT_TALES_HAVE_AWARD_TAKE":                      reflect.ValueOf(common.RED_POINT_TALES_HAVE_AWARD_TAKE),
		"RED_POINT_TALES_HAVE_CHAPTER_FIGHT":                   reflect.ValueOf(common.RED_POINT_TALES_HAVE_CHAPTER_FIGHT),
		"RED_POINT_TOWERSTAR_DAILY":                            reflect.ValueOf(common.RED_POINT_TOWERSTAR_DAILY),
		"RED_POINT_TOWERSTAR_FIGHT":                            reflect.ValueOf(common.RED_POINT_TOWERSTAR_FIGHT),
		"RED_POINT_TOWERSTAR_STAR":                             reflect.ValueOf(common.RED_POINT_TOWERSTAR_STAR),
		"RED_POINT_TOWER_CAN_FIGHT":                            reflect.ValueOf(common.RED_POINT_TOWER_CAN_FIGHT),
		"RED_POINT_TOWER_CAN_JUMP":                             reflect.ValueOf(common.RED_POINT_TOWER_CAN_JUMP),
		"RED_POINT_TOWER_SEASON_TASK_RECV_AWARD":               reflect.ValueOf(common.RED_POINT_TOWER_SEASON_TASK_RECV_AWARD),
		"RED_POINT_UNREAD_MAIL":                                reflect.ValueOf(common.RED_POINT_UNREAD_MAIL),
		"RED_POINT_WRESTLE_BE_DEFEATED":                        reflect.ValueOf(common.RED_POINT_WRESTLE_BE_DEFEATED),
		"RED_POINT_WRESTLE_LIKE":                               reflect.ValueOf(common.RED_POINT_WRESTLE_LIKE),
		"RED_POINT_WRESTLE_PROMOTED_TASK":                      reflect.ValueOf(common.RED_POINT_WRESTLE_PROMOTED_TASK),
		"RED_POINT_name":                                       reflect.ValueOf(&common.RED_POINT_name).Elem(),
		"RED_POINT_value":                                      reflect.ValueOf(&common.RED_POINT_value).Elem(),
		"REFUND_STATUS_RS_FAIL":                                reflect.ValueOf(common.REFUND_STATUS_RS_FAIL),
		"REFUND_STATUS_RS_NONE":                                reflect.ValueOf(common.REFUND_STATUS_RS_NONE),
		"REFUND_STATUS_RS_SUCCESS":                             reflect.ValueOf(common.REFUND_STATUS_RS_SUCCESS),
		"REFUND_STATUS_name":                                   reflect.ValueOf(&common.REFUND_STATUS_name).Elem(),
		"REFUND_STATUS_value":                                  reflect.ValueOf(&common.REFUND_STATUS_value).Elem(),
		"RESET_TYPE_DAILY":                                     reflect.ValueOf(common.RESET_TYPE_DAILY),
		"RESET_TYPE_MONTHLY":                                   reflect.ValueOf(common.RESET_TYPE_MONTHLY),
		"RESET_TYPE_RESET_NULL":                                reflect.ValueOf(common.RESET_TYPE_RESET_NULL),
		"RESET_TYPE_WEEKLY":                                    reflect.ValueOf(common.RESET_TYPE_WEEKLY),
		"RESET_TYPE_WEEKLY_FRIDAY":                             reflect.ValueOf(common.RESET_TYPE_WEEKLY_FRIDAY),
		"RESET_TYPE_name":                                      reflect.ValueOf(&common.RESET_TYPE_name).Elem(),
		"RESET_TYPE_value":                                     reflect.ValueOf(&common.RESET_TYPE_value).Elem(),
		"RESOURCE_ARTIFACT":                                    reflect.ValueOf(common.RESOURCE_ARTIFACT),
		"RESOURCE_ARTIFACT_FRAGMENT":                           reflect.ValueOf(common.RESOURCE_ARTIFACT_FRAGMENT),
		"RESOURCE_ATTR_TYPE_RESAT_ADDITIVE_HERO":               reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_ADDITIVE_HERO),
		"RESOURCE_ATTR_TYPE_RESAT_AWARD_FLAG":                  reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_AWARD_FLAG),
		"RESOURCE_ATTR_TYPE_RESAT_FROM_ID":                     reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_FROM_ID),
		"RESOURCE_ATTR_TYPE_RESAT_IGNORE":                      reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_IGNORE),
		"RESOURCE_ATTR_TYPE_RESAT_ITEM_EXPIRED_TIME":           reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_ITEM_EXPIRED_TIME),
		"RESOURCE_ATTR_TYPE_RESAT_NONE":                        reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_NONE),
		"RESOURCE_ATTR_TYPE_RESAT_SKILL":                       reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_SKILL),
		"RESOURCE_ATTR_TYPE_RESAT_TASK_ID":                     reflect.ValueOf(common.RESOURCE_ATTR_TYPE_RESAT_TASK_ID),
		"RESOURCE_ATTR_TYPE_name":                              reflect.ValueOf(&common.RESOURCE_ATTR_TYPE_name).Elem(),
		"RESOURCE_ATTR_TYPE_value":                             reflect.ValueOf(&common.RESOURCE_ATTR_TYPE_value).Elem(),
		"RESOURCE_AVATAR":                                      reflect.ValueOf(common.RESOURCE_AVATAR),
		"RESOURCE_COUPON":                                      reflect.ValueOf(common.RESOURCE_COUPON),
		"RESOURCE_DIAMOND":                                     reflect.ValueOf(common.RESOURCE_DIAMOND),
		"RESOURCE_EMBLEM":                                      reflect.ValueOf(common.RESOURCE_EMBLEM),
		"RESOURCE_EMBLEM_FRAGMENT":                             reflect.ValueOf(common.RESOURCE_EMBLEM_FRAGMENT),
		"RESOURCE_EQUIP":                                       reflect.ValueOf(common.RESOURCE_EQUIP),
		"RESOURCE_FRAGMENT":                                    reflect.ValueOf(common.RESOURCE_FRAGMENT),
		"RESOURCE_GEM":                                         reflect.ValueOf(common.RESOURCE_GEM),
		"RESOURCE_GOLD":                                        reflect.ValueOf(common.RESOURCE_GOLD),
		"RESOURCE_GUILD_CHEST_ITEM":                            reflect.ValueOf(common.RESOURCE_GUILD_CHEST_ITEM),
		"RESOURCE_HERO":                                        reflect.ValueOf(common.RESOURCE_HERO),
		"RESOURCE_ITEM":                                        reflect.ValueOf(common.RESOURCE_ITEM),
		"RESOURCE_MONUMENT_RUNE":                               reflect.ValueOf(common.RESOURCE_MONUMENT_RUNE),
		"RESOURCE_NONE_RESOURCE":                               reflect.ValueOf(common.RESOURCE_NONE_RESOURCE),
		"RESOURCE_PEXP":                                        reflect.ValueOf(common.RESOURCE_PEXP),
		"RESOURCE_REMAIN":                                      reflect.ValueOf(common.RESOURCE_REMAIN),
		"RESOURCE_REMAIN_COST_ITEM":                            reflect.ValueOf(common.RESOURCE_REMAIN_COST_ITEM),
		"RESOURCE_REMAIN_FRAGMENT":                             reflect.ValueOf(common.RESOURCE_REMAIN_FRAGMENT),
		"RESOURCE_REWARD_FLAG_RRF_DECOMPOSE_RESULT":            reflect.ValueOf(common.RESOURCE_REWARD_FLAG_RRF_DECOMPOSE_RESULT),
		"RESOURCE_REWARD_FLAG_RRF_NEW_DECOMPOSED":              reflect.ValueOf(common.RESOURCE_REWARD_FLAG_RRF_NEW_DECOMPOSED),
		"RESOURCE_REWARD_FLAG_RRF_NORMAL":                      reflect.ValueOf(common.RESOURCE_REWARD_FLAG_RRF_NORMAL),
		"RESOURCE_REWARD_FLAG_RRF_OWNED_DECOMPOSED":            reflect.ValueOf(common.RESOURCE_REWARD_FLAG_RRF_OWNED_DECOMPOSED),
		"RESOURCE_REWARD_FLAG_name":                            reflect.ValueOf(&common.RESOURCE_REWARD_FLAG_name).Elem(),
		"RESOURCE_REWARD_FLAG_value":                           reflect.ValueOf(&common.RESOURCE_REWARD_FLAG_value).Elem(),
		"RESOURCE_RITE_MARK":                                   reflect.ValueOf(common.RESOURCE_RITE_MARK),
		"RESOURCE_SEASON_JEWELRY":                              reflect.ValueOf(common.RESOURCE_SEASON_JEWELRY),
		"RESOURCE_SEASON_MAP_GOODS":                            reflect.ValueOf(common.RESOURCE_SEASON_MAP_GOODS),
		"RESOURCE_SKIN":                                        reflect.ValueOf(common.RESOURCE_SKIN),
		"RESOURCE_TITLE":                                       reflect.ValueOf(common.RESOURCE_TITLE),
		"RESOURCE_TOKEN":                                       reflect.ValueOf(common.RESOURCE_TOKEN),
		"RESOURCE_VIP_EXP":                                     reflect.ValueOf(common.RESOURCE_VIP_EXP),
		"RESOURCE_name":                                        reflect.ValueOf(&common.RESOURCE_name).Elem(),
		"RESOURCE_value":                                       reflect.ValueOf(&common.RESOURCE_value).Elem(),
		"RITE_RESTRICT_STATE_RRS_BE_RESTRICTED":                reflect.ValueOf(common.RITE_RESTRICT_STATE_RRS_BE_RESTRICTED),
		"RITE_RESTRICT_STATE_RRS_NONE":                         reflect.ValueOf(common.RITE_RESTRICT_STATE_RRS_NONE),
		"RITE_RESTRICT_STATE_RRS_RESTRICT":                     reflect.ValueOf(common.RITE_RESTRICT_STATE_RRS_RESTRICT),
		"RITE_RESTRICT_STATE_name":                             reflect.ValueOf(&common.RITE_RESTRICT_STATE_name).Elem(),
		"RITE_RESTRICT_STATE_value":                            reflect.ValueOf(&common.RITE_RESTRICT_STATE_value).Elem(),
		"ROUND_ACTIVITY_TYPE_RAT_ARTIFACT":                     reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_ARTIFACT),
		"ROUND_ACTIVITY_TYPE_RAT_ARTIFACT_DEBUT":               reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_ARTIFACT_DEBUT),
		"ROUND_ACTIVITY_TYPE_RAT_DIVINE_DEMON":                 reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_DIVINE_DEMON),
		"ROUND_ACTIVITY_TYPE_RAT_HERO_SENIOR":                  reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_HERO_SENIOR),
		"ROUND_ACTIVITY_TYPE_RAT_LINK":                         reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_LINK),
		"ROUND_ACTIVITY_TYPE_RAT_MIRAGE":                       reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_MIRAGE),
		"ROUND_ACTIVITY_TYPE_RAT_NONE":                         reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_NONE),
		"ROUND_ACTIVITY_TYPE_RAT_PROPHET":                      reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_PROPHET),
		"ROUND_ACTIVITY_TYPE_RAT_WORLD_BOSS":                   reflect.ValueOf(common.ROUND_ACTIVITY_TYPE_RAT_WORLD_BOSS),
		"ROUND_ACTIVITY_TYPE_name":                             reflect.ValueOf(&common.ROUND_ACTIVITY_TYPE_name).Elem(),
		"ROUND_ACTIVITY_TYPE_value":                            reflect.ValueOf(&common.ROUND_ACTIVITY_TYPE_value).Elem(),
		"SEASON_ARENA_RESET_SAR_NONE":                          reflect.ValueOf(common.SEASON_ARENA_RESET_SAR_NONE),
		"SEASON_ARENA_RESET_SAR_RESET_BEGIN_TIME":              reflect.ValueOf(common.SEASON_ARENA_RESET_SAR_RESET_BEGIN_TIME),
		"SEASON_ARENA_RESET_SAR_RESET_END_TIME":                reflect.ValueOf(common.SEASON_ARENA_RESET_SAR_RESET_END_TIME),
		"SEASON_ARENA_RESET_name":                              reflect.ValueOf(&common.SEASON_ARENA_RESET_name).Elem(),
		"SEASON_ARENA_RESET_value":                             reflect.ValueOf(&common.SEASON_ARENA_RESET_value).Elem(),
		"SEASON_ARENA_SA_CLOSE":                                reflect.ValueOf(common.SEASON_ARENA_SA_CLOSE),
		"SEASON_ARENA_SA_OPEN":                                 reflect.ValueOf(common.SEASON_ARENA_SA_OPEN),
		"SEASON_ARENA_SA_ROUND_BREAK":                          reflect.ValueOf(common.SEASON_ARENA_SA_ROUND_BREAK),
		"SEASON_ARENA_SA_ROUND_END":                            reflect.ValueOf(common.SEASON_ARENA_SA_ROUND_END),
		"SEASON_ARENA_SA_SETTLEMENT_COMPLETE":                  reflect.ValueOf(common.SEASON_ARENA_SA_SETTLEMENT_COMPLETE),
		"SEASON_ARENA_SA_SETTLEMENT_START":                     reflect.ValueOf(common.SEASON_ARENA_SA_SETTLEMENT_START),
		"SEASON_ARENA_name":                                    reflect.ValueOf(&common.SEASON_ARENA_name).Elem(),
		"SEASON_ARENA_value":                                   reflect.ValueOf(&common.SEASON_ARENA_value).Elem(),
		"SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_BLUE_RITE":    reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_BLUE_RITE),
		"SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_GREEN_RITE":   reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_GREEN_RITE),
		"SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_GREY_RITE":    reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_GREY_RITE),
		"SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_ORANGE_RITE":  reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_ORANGE_RITE),
		"SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_PURPLE_RITE":  reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_PURPLE_RITE),
		"SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_RED_RITE":     reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_ACTIVATION_RED_RITE),
		"SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_FIRST_WIN": reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_FIRST_WIN),
		"SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_GET_BOX":   reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_GET_BOX),
		"SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_GET_RELIC": reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_GET_RELIC),
		"SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_GET_STONE": reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_DISORDER_LAND_GET_STONE),
		"SEASON_DATA_RECORD_TYPE_SDRT_DUNGEON":                 reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_DUNGEON),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_BLUE_MARK":           reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_BLUE_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_GREEN_MARK":          reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_GREEN_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_GREY_MARK":           reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_GREY_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_ORANGE_MARK":         reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_ORANGE_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_POWER_MARK":          reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_POWER_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_PURPLE_MARK":         reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_PURPLE_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_GET_RED_MARK":            reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_GET_RED_MARK),
		"SEASON_DATA_RECORD_TYPE_SDRT_LEVEL":                   reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_LEVEL),
		"SEASON_DATA_RECORD_TYPE_SDRT_NONE":                    reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_NONE),
		"SEASON_DATA_RECORD_TYPE_SDRT_PEAK_BEAST_RANK":         reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_PEAK_BEAST_RANK),
		"SEASON_DATA_RECORD_TYPE_SDRT_PVE_WIN_WITH_RITE":       reflect.ValueOf(common.SEASON_DATA_RECORD_TYPE_SDRT_PVE_WIN_WITH_RITE),
		"SEASON_DATA_RECORD_TYPE_name":                         reflect.ValueOf(&common.SEASON_DATA_RECORD_TYPE_name).Elem(),
		"SEASON_DATA_RECORD_TYPE_value":                        reflect.ValueOf(&common.SEASON_DATA_RECORD_TYPE_value).Elem(),
		"SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_LIMITED":         reflect.ValueOf(common.SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_LIMITED),
		"SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_NONE":            reflect.ValueOf(common.SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_NONE),
		"SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_UNLIMITED":       reflect.ValueOf(common.SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_UNLIMITED),
		"SEASON_DOOR_OIL_OPERATION_TYPE_name":                  reflect.ValueOf(&common.SEASON_DOOR_OIL_OPERATION_TYPE_name).Elem(),
		"SEASON_DOOR_OIL_OPERATION_TYPE_value":                 reflect.ValueOf(&common.SEASON_DOOR_OIL_OPERATION_TYPE_value).Elem(),
		"SEASON_DOOR_REWARD_TYPE_DRP_NONE":                     reflect.ValueOf(common.SEASON_DOOR_REWARD_TYPE_DRP_NONE),
		"SEASON_DOOR_REWARD_TYPE_DRP_OIL":                      reflect.ValueOf(common.SEASON_DOOR_REWARD_TYPE_DRP_OIL),
		"SEASON_DOOR_REWARD_TYPE_DRP_REST":                     reflect.ValueOf(common.SEASON_DOOR_REWARD_TYPE_DRP_REST),
		"SEASON_DOOR_REWARD_TYPE_name":                         reflect.ValueOf(&common.SEASON_DOOR_REWARD_TYPE_name).Elem(),
		"SEASON_DOOR_REWARD_TYPE_value":                        reflect.ValueOf(&common.SEASON_DOOR_REWARD_TYPE_value).Elem(),
		"SEASON_DUNGEON_TYPE_SDT_NORMAL":                       reflect.ValueOf(common.SEASON_DUNGEON_TYPE_SDT_NORMAL),
		"SEASON_DUNGEON_TYPE_SDT_THROUGH":                      reflect.ValueOf(common.SEASON_DUNGEON_TYPE_SDT_THROUGH),
		"SEASON_DUNGEON_TYPE_name":                             reflect.ValueOf(&common.SEASON_DUNGEON_TYPE_name).Elem(),
		"SEASON_DUNGEON_TYPE_value":                            reflect.ValueOf(&common.SEASON_DUNGEON_TYPE_value).Elem(),
		"SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_TAKE_OFF":        reflect.ValueOf(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_TAKE_OFF),
		"SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR":            reflect.ValueOf(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR),
		"SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR_ALL":        reflect.ValueOf(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR_ALL),
		"SEASON_JEWELRY_OPERATE_TYPE_JOT_NONE":                 reflect.ValueOf(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_NONE),
		"SEASON_JEWELRY_OPERATE_TYPE_JOT_TAKE_OFF":             reflect.ValueOf(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_TAKE_OFF),
		"SEASON_JEWELRY_OPERATE_TYPE_JOT_WEAR":                 reflect.ValueOf(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_WEAR),
		"SEASON_JEWELRY_OPERATE_TYPE_name":                     reflect.ValueOf(&common.SEASON_JEWELRY_OPERATE_TYPE_name).Elem(),
		"SEASON_JEWELRY_OPERATE_TYPE_value":                    reflect.ValueOf(&common.SEASON_JEWELRY_OPERATE_TYPE_value).Elem(),
		"SEASON_JEWELRY_SKILL_POS_JSP_NONE":                    reflect.ValueOf(common.SEASON_JEWELRY_SKILL_POS_JSP_NONE),
		"SEASON_JEWELRY_SKILL_POS_JSP_ONE":                     reflect.ValueOf(common.SEASON_JEWELRY_SKILL_POS_JSP_ONE),
		"SEASON_JEWELRY_SKILL_POS_JSP_THREE":                   reflect.ValueOf(common.SEASON_JEWELRY_SKILL_POS_JSP_THREE),
		"SEASON_JEWELRY_SKILL_POS_JSP_TWO":                     reflect.ValueOf(common.SEASON_JEWELRY_SKILL_POS_JSP_TWO),
		"SEASON_JEWELRY_SKILL_POS_name":                        reflect.ValueOf(&common.SEASON_JEWELRY_SKILL_POS_name).Elem(),
		"SEASON_JEWELRY_SKILL_POS_value":                       reflect.ValueOf(&common.SEASON_JEWELRY_SKILL_POS_value).Elem(),
		"SEASON_RETURN_TYPE_SRT_NONE":                          reflect.ValueOf(common.SEASON_RETURN_TYPE_SRT_NONE),
		"SEASON_RETURN_TYPE_SRT_OPEN":                          reflect.ValueOf(common.SEASON_RETURN_TYPE_SRT_OPEN),
		"SEASON_RETURN_TYPE_SRT_RETURN":                        reflect.ValueOf(common.SEASON_RETURN_TYPE_SRT_RETURN),
		"SEASON_RETURN_TYPE_name":                              reflect.ValueOf(&common.SEASON_RETURN_TYPE_name).Elem(),
		"SEASON_RETURN_TYPE_value":                             reflect.ValueOf(&common.SEASON_RETURN_TYPE_value).Elem(),
		"SELECT_SUMMON_COST_TYPE_SSCT_FREE":                    reflect.ValueOf(common.SELECT_SUMMON_COST_TYPE_SSCT_FREE),
		"SELECT_SUMMON_COST_TYPE_SSCT_ITEM":                    reflect.ValueOf(common.SELECT_SUMMON_COST_TYPE_SSCT_ITEM),
		"SELECT_SUMMON_COST_TYPE_SSCT_NONE":                    reflect.ValueOf(common.SELECT_SUMMON_COST_TYPE_SSCT_NONE),
		"SELECT_SUMMON_COST_TYPE_name":                         reflect.ValueOf(&common.SELECT_SUMMON_COST_TYPE_name).Elem(),
		"SELECT_SUMMON_COST_TYPE_value":                        reflect.ValueOf(&common.SELECT_SUMMON_COST_TYPE_value).Elem(),
		"SELECT_SUMMON_TYPE_SST_MANY":                          reflect.ValueOf(common.SELECT_SUMMON_TYPE_SST_MANY),
		"SELECT_SUMMON_TYPE_SST_NONE":                          reflect.ValueOf(common.SELECT_SUMMON_TYPE_SST_NONE),
		"SELECT_SUMMON_TYPE_SST_SINGLE":                        reflect.ValueOf(common.SELECT_SUMMON_TYPE_SST_SINGLE),
		"SELECT_SUMMON_TYPE_name":                              reflect.ValueOf(&common.SELECT_SUMMON_TYPE_name).Elem(),
		"SELECT_SUMMON_TYPE_value":                             reflect.ValueOf(&common.SELECT_SUMMON_TYPE_value).Elem(),
		"SHARE_GROWTH_TYPE_SGT_ALL":                            reflect.ValueOf(common.SHARE_GROWTH_TYPE_SGT_ALL),
		"SHARE_GROWTH_TYPE_SGT_EQUIPMENT":                      reflect.ValueOf(common.SHARE_GROWTH_TYPE_SGT_EQUIPMENT),
		"SHARE_GROWTH_TYPE_SGT_HERO":                           reflect.ValueOf(common.SHARE_GROWTH_TYPE_SGT_HERO),
		"SHARE_GROWTH_TYPE_name":                               reflect.ValueOf(&common.SHARE_GROWTH_TYPE_name).Elem(),
		"SHARE_GROWTH_TYPE_value":                              reflect.ValueOf(&common.SHARE_GROWTH_TYPE_value).Elem(),
		"SHOPID_ARENA_SHOP":                                    reflect.ValueOf(common.SHOPID_ARENA_SHOP),
		"SHOPID_ARTIFACT_SHOP":                                 reflect.ValueOf(common.SHOPID_ARTIFACT_SHOP),
		"SHOPID_EMBLEM_SHOP":                                   reflect.ValueOf(common.SHOPID_EMBLEM_SHOP),
		"SHOPID_GEM_SHOP":                                      reflect.ValueOf(common.SHOPID_GEM_SHOP),
		"SHOPID_GENERAL_SHOP":                                  reflect.ValueOf(common.SHOPID_GENERAL_SHOP),
		"SHOPID_GUILD_SHOP":                                    reflect.ValueOf(common.SHOPID_GUILD_SHOP),
		"SHOPID_HERO_SHOP":                                     reflect.ValueOf(common.SHOPID_HERO_SHOP),
		"SHOPID_NONE_SHOP":                                     reflect.ValueOf(common.SHOPID_NONE_SHOP),
		"SHOPID_PROPHET_SHOP":                                  reflect.ValueOf(common.SHOPID_PROPHET_SHOP),
		"SHOPID_SEASON_MAP_SHOP":                               reflect.ValueOf(common.SHOPID_SEASON_MAP_SHOP),
		"SHOPID_SKILL_SHOP":                                    reflect.ValueOf(common.SHOPID_SKILL_SHOP),
		"SHOPID_WORLD_BOSS_SHOP":                               reflect.ValueOf(common.SHOPID_WORLD_BOSS_SHOP),
		"SHOPID_name":                                          reflect.ValueOf(&common.SHOPID_name).Elem(),
		"SHOPID_value":                                         reflect.ValueOf(&common.SHOPID_value).Elem(),
		"SHOP_BUY_NUM_MAX":                                     reflect.ValueOf(common.SHOP_BUY_NUM_MAX),
		"SHOP_SHOP_NONE":                                       reflect.ValueOf(common.SHOP_SHOP_NONE),
		"SHOP_TYPE_ST_ARTIFACT_DEBUT":                          reflect.ValueOf(common.SHOP_TYPE_ST_ARTIFACT_DEBUT),
		"SHOP_TYPE_ST_FIXED":                                   reflect.ValueOf(common.SHOP_TYPE_ST_FIXED),
		"SHOP_TYPE_ST_NONE":                                    reflect.ValueOf(common.SHOP_TYPE_ST_NONE),
		"SHOP_TYPE_ST_RANDOM":                                  reflect.ValueOf(common.SHOP_TYPE_ST_RANDOM),
		"SHOP_TYPE_ST_RANDOM_BOX":                              reflect.ValueOf(common.SHOP_TYPE_ST_RANDOM_BOX),
		"SHOP_TYPE_ST_ROUND":                                   reflect.ValueOf(common.SHOP_TYPE_ST_ROUND),
		"SHOP_TYPE_name":                                       reflect.ValueOf(&common.SHOP_TYPE_name).Elem(),
		"SHOP_TYPE_value":                                      reflect.ValueOf(&common.SHOP_TYPE_value).Elem(),
		"SHOP_name":                                            reflect.ValueOf(&common.SHOP_name).Elem(),
		"SHOP_value":                                           reflect.ValueOf(&common.SHOP_value).Elem(),
		"TASK_RESET_TYPE_RET_ACHIEVE":                          reflect.ValueOf(common.TASK_RESET_TYPE_RET_ACHIEVE),
		"TASK_RESET_TYPE_RET_DAILY":                            reflect.ValueOf(common.TASK_RESET_TYPE_RET_DAILY),
		"TASK_RESET_TYPE_RET_LINE":                             reflect.ValueOf(common.TASK_RESET_TYPE_RET_LINE),
		"TASK_RESET_TYPE_RET_MONTHLY":                          reflect.ValueOf(common.TASK_RESET_TYPE_RET_MONTHLY),
		"TASK_RESET_TYPE_RET_NONE":                             reflect.ValueOf(common.TASK_RESET_TYPE_RET_NONE),
		"TASK_RESET_TYPE_RET_WEEKLY":                           reflect.ValueOf(common.TASK_RESET_TYPE_RET_WEEKLY),
		"TASK_RESET_TYPE_name":                                 reflect.ValueOf(&common.TASK_RESET_TYPE_name).Elem(),
		"TASK_RESET_TYPE_value":                                reflect.ValueOf(&common.TASK_RESET_TYPE_value).Elem(),
		"TASK_TYPE_FINISH_TYPE_FT_GREATER":                     reflect.ValueOf(common.TASK_TYPE_FINISH_TYPE_FT_GREATER),
		"TASK_TYPE_FINISH_TYPE_FT_LESS":                        reflect.ValueOf(common.TASK_TYPE_FINISH_TYPE_FT_LESS),
		"TASK_TYPE_FINISH_TYPE_FT_NONE":                        reflect.ValueOf(common.TASK_TYPE_FINISH_TYPE_FT_NONE),
		"TASK_TYPE_FINISH_TYPE_name":                           reflect.ValueOf(&common.TASK_TYPE_FINISH_TYPE_name).Elem(),
		"TASK_TYPE_FINISH_TYPE_value":                          reflect.ValueOf(&common.TASK_TYPE_FINISH_TYPE_value).Elem(),
		"TASK_TYPE_ID_TTYPE_ID_COMPLETE_ANY_DISPATCH_TASK":     reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_ANY_DISPATCH_TASK),
		"TASK_TYPE_ID_TTYPE_ID_COMPLETE_BLUE_DISPATCH_TASK":    reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_BLUE_DISPATCH_TASK),
		"TASK_TYPE_ID_TTYPE_ID_COMPLETE_GREEN_DISPATCH_TASK":   reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_GREEN_DISPATCH_TASK),
		"TASK_TYPE_ID_TTYPE_ID_COMPLETE_ORANGE_DISPATCH_TASK":  reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_ORANGE_DISPATCH_TASK),
		"TASK_TYPE_ID_TTYPE_ID_COMPLETE_PURPLE_DISPATCH_TASK":  reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_PURPLE_DISPATCH_TASK),
		"TASK_TYPE_ID_TTYPE_ID_COMPLETE_RED_DISPATCH_TASK":     reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_RED_DISPATCH_TASK),
		"TASK_TYPE_ID_TTYPE_ID_MAIN_DUNGEON_FLOOR":             reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MAIN_DUNGEON_FLOOR),
		"TASK_TYPE_ID_TTYPE_ID_MAZE_BOSS_FIGHT_WIN":            reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MAZE_BOSS_FIGHT_WIN),
		"TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_DEMON_FLOOR":        reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_DEMON_FLOOR),
		"TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_EMPIRE_FLOOR":       reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_EMPIRE_FLOOR),
		"TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_FOREST_FLOOR":       reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_FOREST_FLOOR),
		"TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_MOON_FLOOR":         reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_MOON_FLOOR),
		"TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_PROTOSS_FLOOR":      reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_PROTOSS_FLOOR),
		"TASK_TYPE_ID_TTYPE_ID_NONE":                           reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_NONE),
		"TASK_TYPE_ID_TTYPE_ID_SUMMON_DAILY_ADVANCED_COUNT":    reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_SUMMON_DAILY_ADVANCED_COUNT),
		"TASK_TYPE_ID_TTYPE_ID_SUMMON_HERO_RARE_TRUE_FIVE":     reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_SUMMON_HERO_RARE_TRUE_FIVE),
		"TASK_TYPE_ID_TTYPE_ID_TOWER_SEASON_ATTACH_FLOOR":      reflect.ValueOf(common.TASK_TYPE_ID_TTYPE_ID_TOWER_SEASON_ATTACH_FLOOR),
		"TASK_TYPE_ID_name":                                    reflect.ValueOf(&common.TASK_TYPE_ID_name).Elem(),
		"TASK_TYPE_ID_value":                                   reflect.ValueOf(&common.TASK_TYPE_ID_value).Elem(),
		"TASK_TYPE_PROGRESS_TYPE_PROGRESS_TYPE_NONE":           reflect.ValueOf(common.TASK_TYPE_PROGRESS_TYPE_PROGRESS_TYPE_NONE),
		"TASK_TYPE_PROGRESS_TYPE_PT_ADD":                       reflect.ValueOf(common.TASK_TYPE_PROGRESS_TYPE_PT_ADD),
		"TASK_TYPE_PROGRESS_TYPE_PT_MAX":                       reflect.ValueOf(common.TASK_TYPE_PROGRESS_TYPE_PT_MAX),
		"TASK_TYPE_PROGRESS_TYPE_PT_MIN":                       reflect.ValueOf(common.TASK_TYPE_PROGRESS_TYPE_PT_MIN),
		"TASK_TYPE_PROGRESS_TYPE_PT_MORE_THAN":                 reflect.ValueOf(common.TASK_TYPE_PROGRESS_TYPE_PT_MORE_THAN),
		"TASK_TYPE_PROGRESS_TYPE_PT_UNIQUE":                    reflect.ValueOf(common.TASK_TYPE_PROGRESS_TYPE_PT_UNIQUE),
		"TASK_TYPE_PROGRESS_TYPE_name":                         reflect.ValueOf(&common.TASK_TYPE_PROGRESS_TYPE_name).Elem(),
		"TASK_TYPE_PROGRESS_TYPE_value":                        reflect.ValueOf(&common.TASK_TYPE_PROGRESS_TYPE_value).Elem(),
		"TASK_TYPE_TTYPE_CONTINUE_LOGIN_DAY":                   reflect.ValueOf(common.TASK_TYPE_TTYPE_CONTINUE_LOGIN_DAY),
		"TASK_TYPE_TTYPE_HAVE_DEMON_HERO":                      reflect.ValueOf(common.TASK_TYPE_TTYPE_HAVE_DEMON_HERO),
		"TASK_TYPE_TTYPE_HAVE_EMPIRE_HERO":                     reflect.ValueOf(common.TASK_TYPE_TTYPE_HAVE_EMPIRE_HERO),
		"TASK_TYPE_TTYPE_HAVE_FOREST_HERO":                     reflect.ValueOf(common.TASK_TYPE_TTYPE_HAVE_FOREST_HERO),
		"TASK_TYPE_TTYPE_HAVE_MOON_HERO":                       reflect.ValueOf(common.TASK_TYPE_TTYPE_HAVE_MOON_HERO),
		"TASK_TYPE_TTYPE_HAVE_PROTOSS_HERO":                    reflect.ValueOf(common.TASK_TYPE_TTYPE_HAVE_PROTOSS_HERO),
		"TASK_TYPE_TTYPE_NONE":                                 reflect.ValueOf(common.TASK_TYPE_TTYPE_NONE),
		"TASK_TYPE_name":                                       reflect.ValueOf(&common.TASK_TYPE_name).Elem(),
		"TASK_TYPE_value":                                      reflect.ValueOf(&common.TASK_TYPE_value).Elem(),
		"TIME_TYPE_MAX_SIZE":                                   reflect.ValueOf(common.TIME_TYPE_MAX_SIZE),
		"TIME_TYPE_NORMAL_DATE":                                reflect.ValueOf(common.TIME_TYPE_NORMAL_DATE),
		"TIME_TYPE_NOT_USE":                                    reflect.ValueOf(common.TIME_TYPE_NOT_USE),
		"TIME_TYPE_SERVER_OPEN":                                reflect.ValueOf(common.TIME_TYPE_SERVER_OPEN),
		"TIME_TYPE_USER_CREATE":                                reflect.ValueOf(common.TIME_TYPE_USER_CREATE),
		"TIME_TYPE_name":                                       reflect.ValueOf(&common.TIME_TYPE_name).Elem(),
		"TIME_TYPE_value":                                      reflect.ValueOf(&common.TIME_TYPE_value).Elem(),
		"TOKEN_TYPE_ARENA_REPUTATION":                          reflect.ValueOf(common.TOKEN_TYPE_ARENA_REPUTATION),
		"TOKEN_TYPE_ARTIFACT_DEBUT_DRAW_POINTS":                reflect.ValueOf(common.TOKEN_TYPE_ARTIFACT_DEBUT_DRAW_POINTS),
		"TOKEN_TYPE_ARTIFACT_DEBUT_SHOP_TOKEN":                 reflect.ValueOf(common.TOKEN_TYPE_ARTIFACT_DEBUT_SHOP_TOKEN),
		"TOKEN_TYPE_ARTIFACT_POINT":                            reflect.ValueOf(common.TOKEN_TYPE_ARTIFACT_POINT),
		"TOKEN_TYPE_BOUNTY_INTEL":                              reflect.ValueOf(common.TOKEN_TYPE_BOUNTY_INTEL),
		"TOKEN_TYPE_DAILY_SPECIAL_SCORE":                       reflect.ValueOf(common.TOKEN_TYPE_DAILY_SPECIAL_SCORE),
		"TOKEN_TYPE_DEBUT_POINT":                               reflect.ValueOf(common.TOKEN_TYPE_DEBUT_POINT),
		"TOKEN_TYPE_DESTINY_CARD":                              reflect.ValueOf(common.TOKEN_TYPE_DESTINY_CARD),
		"TOKEN_TYPE_DIVINITY":                                  reflect.ValueOf(common.TOKEN_TYPE_DIVINITY),
		"TOKEN_TYPE_EMBLEM_CURRENCY":                           reflect.ValueOf(common.TOKEN_TYPE_EMBLEM_CURRENCY),
		"TOKEN_TYPE_FOREST_PETAL":                              reflect.ValueOf(common.TOKEN_TYPE_FOREST_PETAL),
		"TOKEN_TYPE_FRIEND_LIKE":                               reflect.ValueOf(common.TOKEN_TYPE_FRIEND_LIKE),
		"TOKEN_TYPE_GEM_CRAFT_ODDS":                            reflect.ValueOf(common.TOKEN_TYPE_GEM_CRAFT_ODDS),
		"TOKEN_TYPE_GOLD_POINT":                                reflect.ValueOf(common.TOKEN_TYPE_GOLD_POINT),
		"TOKEN_TYPE_GUILD_CONTRIBUTION":                        reflect.ValueOf(common.TOKEN_TYPE_GUILD_CONTRIBUTION),
		"TOKEN_TYPE_GUILD_SKILL_POINT":                         reflect.ValueOf(common.TOKEN_TYPE_GUILD_SKILL_POINT),
		"TOKEN_TYPE_GUILD_TALENT_POINT_1":                      reflect.ValueOf(common.TOKEN_TYPE_GUILD_TALENT_POINT_1),
		"TOKEN_TYPE_GUILD_TALENT_POINT_2":                      reflect.ValueOf(common.TOKEN_TYPE_GUILD_TALENT_POINT_2),
		"TOKEN_TYPE_GUILD_TALENT_POINT_3":                      reflect.ValueOf(common.TOKEN_TYPE_GUILD_TALENT_POINT_3),
		"TOKEN_TYPE_GUILD_TALENT_POINT_4":                      reflect.ValueOf(common.TOKEN_TYPE_GUILD_TALENT_POINT_4),
		"TOKEN_TYPE_GUILD_TALENT_POINT_5":                      reflect.ValueOf(common.TOKEN_TYPE_GUILD_TALENT_POINT_5),
		"TOKEN_TYPE_GUILD_TALENT_POINT_6":                      reflect.ValueOf(common.TOKEN_TYPE_GUILD_TALENT_POINT_6),
		"TOKEN_TYPE_LOSE_POINT":                                reflect.ValueOf(common.TOKEN_TYPE_LOSE_POINT),
		"TOKEN_TYPE_MAZE_ADVANCED_TOKEN":                       reflect.ValueOf(common.TOKEN_TYPE_MAZE_ADVANCED_TOKEN),
		"TOKEN_TYPE_MAZE_VAULT_KEY":                            reflect.ValueOf(common.TOKEN_TYPE_MAZE_VAULT_KEY),
		"TOKEN_TYPE_NONE_TOKEN_TYPE":                           reflect.ValueOf(common.TOKEN_TYPE_NONE_TOKEN_TYPE),
		"TOKEN_TYPE_ORACLE_ESSENCE":                            reflect.ValueOf(common.TOKEN_TYPE_ORACLE_ESSENCE),
		"TOKEN_TYPE_ORACLE_QUARTZ":                             reflect.ValueOf(common.TOKEN_TYPE_ORACLE_QUARTZ),
		"TOKEN_TYPE_PRESTIGE":                                  reflect.ValueOf(common.TOKEN_TYPE_PRESTIGE),
		"TOKEN_TYPE_SCROLL_SHARD":                              reflect.ValueOf(common.TOKEN_TYPE_SCROLL_SHARD),
		"TOKEN_TYPE_SPIRITUAL_ECHO":                            reflect.ValueOf(common.TOKEN_TYPE_SPIRITUAL_ECHO),
		"TOKEN_TYPE_WRESTLE_POINT":                             reflect.ValueOf(common.TOKEN_TYPE_WRESTLE_POINT),
		"TOKEN_TYPE_name":                                      reflect.ValueOf(&common.TOKEN_TYPE_name).Elem(),
		"TOKEN_TYPE_value":                                     reflect.ValueOf(&common.TOKEN_TYPE_value).Elem(),
		"TOWER_JUMP_TJ_COUNT_LIMIT":                            reflect.ValueOf(common.TOWER_JUMP_TJ_COUNT_LIMIT),
		"TOWER_JUMP_TJ_NONE":                                   reflect.ValueOf(common.TOWER_JUMP_TJ_NONE),
		"TOWER_JUMP_name":                                      reflect.ValueOf(&common.TOWER_JUMP_name).Elem(),
		"TOWER_JUMP_value":                                     reflect.ValueOf(&common.TOWER_JUMP_value).Elem(),
		"TOWER_SEASON_RANK_TSR_CURRENT":                        reflect.ValueOf(common.TOWER_SEASON_RANK_TSR_CURRENT),
		"TOWER_SEASON_RANK_TSR_CURRENT_ACTIVITY":               reflect.ValueOf(common.TOWER_SEASON_RANK_TSR_CURRENT_ACTIVITY),
		"TOWER_SEASON_RANK_TSR_HALL_OF_NAME":                   reflect.ValueOf(common.TOWER_SEASON_RANK_TSR_HALL_OF_NAME),
		"TOWER_SEASON_RANK_TSR_HALL_OF_NAME_TOP_3":             reflect.ValueOf(common.TOWER_SEASON_RANK_TSR_HALL_OF_NAME_TOP_3),
		"TOWER_SEASON_RANK_TSR_NONE":                           reflect.ValueOf(common.TOWER_SEASON_RANK_TSR_NONE),
		"TOWER_SEASON_RANK_name":                               reflect.ValueOf(&common.TOWER_SEASON_RANK_name).Elem(),
		"TOWER_SEASON_RANK_value":                              reflect.ValueOf(&common.TOWER_SEASON_RANK_value).Elem(),
		"TOWER_SEASON_TIME_TST_NONE":                           reflect.ValueOf(common.TOWER_SEASON_TIME_TST_NONE),
		"TOWER_SEASON_TIME_TST_RESET_BEGIN":                    reflect.ValueOf(common.TOWER_SEASON_TIME_TST_RESET_BEGIN),
		"TOWER_SEASON_TIME_TST_RESET_END":                      reflect.ValueOf(common.TOWER_SEASON_TIME_TST_RESET_END),
		"TOWER_SEASON_TIME_name":                               reflect.ValueOf(&common.TOWER_SEASON_TIME_name).Elem(),
		"TOWER_SEASON_TIME_value":                              reflect.ValueOf(&common.TOWER_SEASON_TIME_value).Elem(),
		"TOWER_TYPE_TT_COMMON":                                 reflect.ValueOf(common.TOWER_TYPE_TT_COMMON),
		"TOWER_TYPE_TT_NONE":                                   reflect.ValueOf(common.TOWER_TYPE_TT_NONE),
		"TOWER_TYPE_name":                                      reflect.ValueOf(&common.TOWER_TYPE_name).Elem(),
		"TOWER_TYPE_value":                                     reflect.ValueOf(&common.TOWER_TYPE_value).Elem(),
		"TRIAL_TYPE_EMBLEM_TRIAL":                              reflect.ValueOf(common.TRIAL_TYPE_EMBLEM_TRIAL),
		"TRIAL_TYPE_EQUIP_TRIAL":                               reflect.ValueOf(common.TRIAL_TYPE_EQUIP_TRIAL),
		"TRIAL_TYPE_EXP_TRIAL":                                 reflect.ValueOf(common.TRIAL_TYPE_EXP_TRIAL),
		"TRIAL_TYPE_GOLD_TRIAL":                                reflect.ValueOf(common.TRIAL_TYPE_GOLD_TRIAL),
		"TRIAL_TYPE_HERO_TRIAL":                                reflect.ValueOf(common.TRIAL_TYPE_HERO_TRIAL),
		"TRIAL_TYPE_NONE_TRIAL":                                reflect.ValueOf(common.TRIAL_TYPE_NONE_TRIAL),
		"TRIAL_TYPE_name":                                      reflect.ValueOf(&common.TRIAL_TYPE_name).Elem(),
		"TRIAL_TYPE_value":                                     reflect.ValueOf(&common.TRIAL_TYPE_value).Elem(),
		"WEAR_OP_TAKEOFF":                                      reflect.ValueOf(common.WEAR_OP_TAKEOFF),
		"WEAR_OP_WEAR":                                         reflect.ValueOf(common.WEAR_OP_WEAR),
		"WEAR_OP_WEAR_NONE":                                    reflect.ValueOf(common.WEAR_OP_WEAR_NONE),
		"WEAR_OP_name":                                         reflect.ValueOf(&common.WEAR_OP_name).Elem(),
		"WEAR_OP_value":                                        reflect.ValueOf(&common.WEAR_OP_value).Elem(),
		"WORLD_BOSS_FIGHT_TYPE_WBFT_FIGHT":                     reflect.ValueOf(common.WORLD_BOSS_FIGHT_TYPE_WBFT_FIGHT),
		"WORLD_BOSS_FIGHT_TYPE_WBFT_NONE":                      reflect.ValueOf(common.WORLD_BOSS_FIGHT_TYPE_WBFT_NONE),
		"WORLD_BOSS_FIGHT_TYPE_WBFT_SWEEP":                     reflect.ValueOf(common.WORLD_BOSS_FIGHT_TYPE_WBFT_SWEEP),
		"WORLD_BOSS_FIGHT_TYPE_name":                           reflect.ValueOf(&common.WORLD_BOSS_FIGHT_TYPE_name).Elem(),
		"WORLD_BOSS_FIGHT_TYPE_value":                          reflect.ValueOf(&common.WORLD_BOSS_FIGHT_TYPE_value).Elem(),
		"WORLD_BOSS_LEVEL_WBL_HARD":                            reflect.ValueOf(common.WORLD_BOSS_LEVEL_WBL_HARD),
		"WORLD_BOSS_LEVEL_WBL_MAX":                             reflect.ValueOf(common.WORLD_BOSS_LEVEL_WBL_MAX),
		"WORLD_BOSS_LEVEL_WBL_NIGHT_MIRE":                      reflect.ValueOf(common.WORLD_BOSS_LEVEL_WBL_NIGHT_MIRE),
		"WORLD_BOSS_LEVEL_WBL_NONE":                            reflect.ValueOf(common.WORLD_BOSS_LEVEL_WBL_NONE),
		"WORLD_BOSS_LEVEL_WBL_NORMAL":                          reflect.ValueOf(common.WORLD_BOSS_LEVEL_WBL_NORMAL),
		"WORLD_BOSS_LEVEL_name":                                reflect.ValueOf(&common.WORLD_BOSS_LEVEL_name).Elem(),
		"WORLD_BOSS_LEVEL_value":                               reflect.ValueOf(&common.WORLD_BOSS_LEVEL_value).Elem(),
		"WORLD_BOSS_LOG_TYPE_WBLT_HURT":                        reflect.ValueOf(common.WORLD_BOSS_LOG_TYPE_WBLT_HURT),
		"WORLD_BOSS_LOG_TYPE_WBLT_JOIN_ROOM":                   reflect.ValueOf(common.WORLD_BOSS_LOG_TYPE_WBLT_JOIN_ROOM),
		"WORLD_BOSS_LOG_TYPE_WBLT_NONE":                        reflect.ValueOf(common.WORLD_BOSS_LOG_TYPE_WBLT_NONE),
		"WORLD_BOSS_LOG_TYPE_WBLT_RANK_CHANGE":                 reflect.ValueOf(common.WORLD_BOSS_LOG_TYPE_WBLT_RANK_CHANGE),
		"WORLD_BOSS_LOG_TYPE_WBLT_RANK_FIRST":                  reflect.ValueOf(common.WORLD_BOSS_LOG_TYPE_WBLT_RANK_FIRST),
		"WORLD_BOSS_LOG_TYPE_name":                             reflect.ValueOf(&common.WORLD_BOSS_LOG_TYPE_name).Elem(),
		"WORLD_BOSS_LOG_TYPE_value":                            reflect.ValueOf(&common.WORLD_BOSS_LOG_TYPE_value).Elem(),
		"WORLD_BOSS_RANK_TYPE_WBRT_FIGHT_STAR":                 reflect.ValueOf(common.WORLD_BOSS_RANK_TYPE_WBRT_FIGHT_STAR),
		"WORLD_BOSS_RANK_TYPE_WBRT_MAX":                        reflect.ValueOf(common.WORLD_BOSS_RANK_TYPE_WBRT_MAX),
		"WORLD_BOSS_RANK_TYPE_WBRT_NONE":                       reflect.ValueOf(common.WORLD_BOSS_RANK_TYPE_WBRT_NONE),
		"WORLD_BOSS_RANK_TYPE_WBRT_PARTITION":                  reflect.ValueOf(common.WORLD_BOSS_RANK_TYPE_WBRT_PARTITION),
		"WORLD_BOSS_RANK_TYPE_WBRT_ROOM":                       reflect.ValueOf(common.WORLD_BOSS_RANK_TYPE_WBRT_ROOM),
		"WORLD_BOSS_RANK_TYPE_WBRT_SELF":                       reflect.ValueOf(common.WORLD_BOSS_RANK_TYPE_WBRT_SELF),
		"WORLD_BOSS_RANK_TYPE_name":                            reflect.ValueOf(&common.WORLD_BOSS_RANK_TYPE_name).Elem(),
		"WORLD_BOSS_RANK_TYPE_value":                           reflect.ValueOf(&common.WORLD_BOSS_RANK_TYPE_value).Elem(),
		"WRESTLE_LEVEL_WL_ALL":                                 reflect.ValueOf(common.WRESTLE_LEVEL_WL_ALL),
		"WRESTLE_LEVEL_WL_EIGHTH":                              reflect.ValueOf(common.WRESTLE_LEVEL_WL_EIGHTH),
		"WRESTLE_LEVEL_WL_FIFTH":                               reflect.ValueOf(common.WRESTLE_LEVEL_WL_FIFTH),
		"WRESTLE_LEVEL_WL_FIRST":                               reflect.ValueOf(common.WRESTLE_LEVEL_WL_FIRST),
		"WRESTLE_LEVEL_WL_FOURTH":                              reflect.ValueOf(common.WRESTLE_LEVEL_WL_FOURTH),
		"WRESTLE_LEVEL_WL_MAX":                                 reflect.ValueOf(common.WRESTLE_LEVEL_WL_MAX),
		"WRESTLE_LEVEL_WL_NINTH":                               reflect.ValueOf(common.WRESTLE_LEVEL_WL_NINTH),
		"WRESTLE_LEVEL_WL_SECOND":                              reflect.ValueOf(common.WRESTLE_LEVEL_WL_SECOND),
		"WRESTLE_LEVEL_WL_SEVENTH":                             reflect.ValueOf(common.WRESTLE_LEVEL_WL_SEVENTH),
		"WRESTLE_LEVEL_WL_SIXTH":                               reflect.ValueOf(common.WRESTLE_LEVEL_WL_SIXTH),
		"WRESTLE_LEVEL_WL_THIRD":                               reflect.ValueOf(common.WRESTLE_LEVEL_WL_THIRD),
		"WRESTLE_LEVEL_name":                                   reflect.ValueOf(&common.WRESTLE_LEVEL_name).Elem(),
		"WRESTLE_LEVEL_value":                                  reflect.ValueOf(&common.WRESTLE_LEVEL_value).Elem(),
		"WRESTLE_OP_STATUS_WOS_DEFEATED":                       reflect.ValueOf(common.WRESTLE_OP_STATUS_WOS_DEFEATED),
		"WRESTLE_OP_STATUS_WOS_NONE":                           reflect.ValueOf(common.WRESTLE_OP_STATUS_WOS_NONE),
		"WRESTLE_OP_STATUS_WOS_REVENGE":                        reflect.ValueOf(common.WRESTLE_OP_STATUS_WOS_REVENGE),
		"WRESTLE_OP_STATUS_name":                               reflect.ValueOf(&common.WRESTLE_OP_STATUS_name).Elem(),
		"WRESTLE_OP_STATUS_value":                              reflect.ValueOf(&common.WRESTLE_OP_STATUS_value).Elem(),
		"WRESTLE_RANK_WR_CURRENT":                              reflect.ValueOf(common.WRESTLE_RANK_WR_CURRENT),
		"WRESTLE_RANK_WR_HALL_OF_NAME":                         reflect.ValueOf(common.WRESTLE_RANK_WR_HALL_OF_NAME),
		"WRESTLE_RANK_WR_NONE":                                 reflect.ValueOf(common.WRESTLE_RANK_WR_NONE),
		"WRESTLE_RANK_name":                                    reflect.ValueOf(&common.WRESTLE_RANK_name).Elem(),
		"WRESTLE_RANK_value":                                   reflect.ValueOf(&common.WRESTLE_RANK_value).Elem(),
		"WRESTLE_STATUS_WS_NONE":                               reflect.ValueOf(common.WRESTLE_STATUS_WS_NONE),
		"WRESTLE_STATUS_WS_PREPARING":                          reflect.ValueOf(common.WRESTLE_STATUS_WS_PREPARING),
		"WRESTLE_STATUS_WS_RESETTING":                          reflect.ValueOf(common.WRESTLE_STATUS_WS_RESETTING),
		"WRESTLE_STATUS_name":                                  reflect.ValueOf(&common.WRESTLE_STATUS_name).Elem(),
		"WRESTLE_STATUS_value":                                 reflect.ValueOf(&common.WRESTLE_STATUS_value).Elem(),

		// type definitions
		"ACHIEVE_TYPE":                         reflect.ValueOf((*common.ACHIEVE_TYPE)(nil)),
		"ACTIVITY_RECHARGE_GIFT_RESET_TYPE":    reflect.ValueOf((*common.ACTIVITY_RECHARGE_GIFT_RESET_TYPE)(nil)),
		"ACTIVITY_RECHARGE_SHOP":               reflect.ValueOf((*common.ACTIVITY_RECHARGE_SHOP)(nil)),
		"ACTIVITY_RECHARGE_SHOP_BUY_TYPE":      reflect.ValueOf((*common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE)(nil)),
		"ACTIVITY_STORY_FIGHT_TYPE":            reflect.ValueOf((*common.ACTIVITY_STORY_FIGHT_TYPE)(nil)),
		"ARENA_TIME":                           reflect.ValueOf((*common.ARENA_TIME)(nil)),
		"ARTIFACT_DEBUT_ACT_RECV":              reflect.ValueOf((*common.ARTIFACT_DEBUT_ACT_RECV)(nil)),
		"ARTIFACT_DEBUT_DRAW_ACT_TYPE":         reflect.ValueOf((*common.ARTIFACT_DEBUT_DRAW_ACT_TYPE)(nil)),
		"ARTIFACT_DEBUT_SUMMON":                reflect.ValueOf((*common.ARTIFACT_DEBUT_SUMMON)(nil)),
		"ARTIFACT_DEBUT_TYPE":                  reflect.ValueOf((*common.ARTIFACT_DEBUT_TYPE)(nil)),
		"ATTRINDEX":                            reflect.ValueOf((*common.ATTRINDEX)(nil)),
		"AVATAR_DEFAULT":                       reflect.ValueOf((*common.AVATAR_DEFAULT)(nil)),
		"AVATAR_SET_INDEX":                     reflect.ValueOf((*common.AVATAR_SET_INDEX)(nil)),
		"AVATAR_TYPE":                          reflect.ValueOf((*common.AVATAR_TYPE)(nil)),
		"BAN":                                  reflect.ValueOf((*common.BAN)(nil)),
		"BOSS_RUSH_BUY_TYPE":                   reflect.ValueOf((*common.BOSS_RUSH_BUY_TYPE)(nil)),
		"CAREER":                               reflect.ValueOf((*common.CAREER)(nil)),
		"CHAT_LIKE":                            reflect.ValueOf((*common.CHAT_LIKE)(nil)),
		"CONFIG":                               reflect.ValueOf((*common.CONFIG)(nil)),
		"CROSS_ARENA_TIME":                     reflect.ValueOf((*common.CROSS_ARENA_TIME)(nil)),
		"CRYSTAL_SHARE_ATTR_TYPE":              reflect.ValueOf((*common.CRYSTAL_SHARE_ATTR_TYPE)(nil)),
		"DAILY_ATTENDANCE_AWARD":               reflect.ValueOf((*common.DAILY_ATTENDANCE_AWARD)(nil)),
		"DAILY_SPECIAL_RECV_AWARD":             reflect.ValueOf((*common.DAILY_SPECIAL_RECV_AWARD)(nil)),
		"DAILY_WISH":                           reflect.ValueOf((*common.DAILY_WISH)(nil)),
		"DB_BASE_USER":                         reflect.ValueOf((*common.DB_BASE_USER)(nil)),
		"DEL_MAIL_TYPE":                        reflect.ValueOf((*common.DEL_MAIL_TYPE)(nil)),
		"DIAMOND_TYPE":                         reflect.ValueOf((*common.DIAMOND_TYPE)(nil)),
		"DISORDER_LAND_ADD":                    reflect.ValueOf((*common.DISORDER_LAND_ADD)(nil)),
		"DISORDER_LAND_BATTLE":                 reflect.ValueOf((*common.DISORDER_LAND_BATTLE)(nil)),
		"DISORDER_LAND_BUY_TYPE":               reflect.ValueOf((*common.DISORDER_LAND_BUY_TYPE)(nil)),
		"DISORDER_LAND_EVENT_TYPE":             reflect.ValueOf((*common.DISORDER_LAND_EVENT_TYPE)(nil)),
		"DISORDER_LAND_GROUP_TAG":              reflect.ValueOf((*common.DISORDER_LAND_GROUP_TAG)(nil)),
		"DISORDER_LAND_HURDLE_TYPE":            reflect.ValueOf((*common.DISORDER_LAND_HURDLE_TYPE)(nil)),
		"DISORDER_LAND_LEVEL":                  reflect.ValueOf((*common.DISORDER_LAND_LEVEL)(nil)),
		"DISORDER_LAND_NODE_UNLOCK":            reflect.ValueOf((*common.DISORDER_LAND_NODE_UNLOCK)(nil)),
		"DISORDER_LAND_RARE":                   reflect.ValueOf((*common.DISORDER_LAND_RARE)(nil)),
		"DIVINE_DEMON_SUMMON_COST_TYPE":        reflect.ValueOf((*common.DIVINE_DEMON_SUMMON_COST_TYPE)(nil)),
		"DIVINE_DEMON_SUMMON_TYPE":             reflect.ValueOf((*common.DIVINE_DEMON_SUMMON_TYPE)(nil)),
		"DIVINE_DEMON_TASK_TYPE":               reflect.ValueOf((*common.DIVINE_DEMON_TASK_TYPE)(nil)),
		"DROP_TYPE":                            reflect.ValueOf((*common.DROP_TYPE)(nil)),
		"DUEL_STATUS_TYPE":                     reflect.ValueOf((*common.DUEL_STATUS_TYPE)(nil)),
		"EMBLEM_AFFIX_TYPE":                    reflect.ValueOf((*common.EMBLEM_AFFIX_TYPE)(nil)),
		"EMBLEM_CONFIG":                        reflect.ValueOf((*common.EMBLEM_CONFIG)(nil)),
		"EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE": reflect.ValueOf((*common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE)(nil)),
		"EMBLEM_SUCCINCT_LOCK_TYPE":            reflect.ValueOf((*common.EMBLEM_SUCCINCT_LOCK_TYPE)(nil)),
		"EQUIPMENT_ATRR":                       reflect.ValueOf((*common.EQUIPMENT_ATRR)(nil)),
		"FIRST_GIFT_REWARD_DAY":                reflect.ValueOf((*common.FIRST_GIFT_REWARD_DAY)(nil)),
		"FLOWER_LOG":                           reflect.ValueOf((*common.FLOWER_LOG)(nil)),
		"FLOWER_LOG_TYPE":                      reflect.ValueOf((*common.FLOWER_LOG_TYPE)(nil)),
		"FLOWER_OCCUPY":                        reflect.ValueOf((*common.FLOWER_OCCUPY)(nil)),
		"FLOWER_OCCUPY_END":                    reflect.ValueOf((*common.FLOWER_OCCUPY_END)(nil)),
		"FLOWER_STAGE":                         reflect.ValueOf((*common.FLOWER_STAGE)(nil)),
		"FOREST_PVP_TREE":                      reflect.ValueOf((*common.FOREST_PVP_TREE)(nil)),
		"FOREST_STAGE":                         reflect.ValueOf((*common.FOREST_STAGE)(nil)),
		"FOREST_TREE_TYPE":                     reflect.ValueOf((*common.FOREST_TREE_TYPE)(nil)),
		"FORMATION_ID":                         reflect.ValueOf((*common.FORMATION_ID)(nil)),
		"FORMATION_TYPE":                       reflect.ValueOf((*common.FORMATION_TYPE)(nil)),
		"FUNCID":                               reflect.ValueOf((*common.FUNCID)(nil)),
		"GEM_CONFIG":                           reflect.ValueOf((*common.GEM_CONFIG)(nil)),
		"GLOBAL_ATTR_TYPE":                     reflect.ValueOf((*common.GLOBAL_ATTR_TYPE)(nil)),
		"GM_XML":                               reflect.ValueOf((*common.GM_XML)(nil)),
		"GODDESS_BODY":                         reflect.ValueOf((*common.GODDESS_BODY)(nil)),
		"GOD_PRESENT_BEHAVIOR_TYPE":            reflect.ValueOf((*common.GOD_PRESENT_BEHAVIOR_TYPE)(nil)),
		"GST_BOSS_BUY_TYPE":                    reflect.ValueOf((*common.GST_BOSS_BUY_TYPE)(nil)),
		"GST_DRAGON_HP_SLOT":                   reflect.ValueOf((*common.GST_DRAGON_HP_SLOT)(nil)),
		"GST_DRAGON_POS":                       reflect.ValueOf((*common.GST_DRAGON_POS)(nil)),
		"GUILD_APPLY_TYPE":                     reflect.ValueOf((*common.GUILD_APPLY_TYPE)(nil)),
		"GUILD_CHEST_LIKE_TYPE":                reflect.ValueOf((*common.GUILD_CHEST_LIKE_TYPE)(nil)),
		"GUILD_COMBINE_APPLY_TYPE":             reflect.ValueOf((*common.GUILD_COMBINE_APPLY_TYPE)(nil)),
		"GUILD_COMBINE_STATUS":                 reflect.ValueOf((*common.GUILD_COMBINE_STATUS)(nil)),
		"GUILD_CREATE_DAY":                     reflect.ValueOf((*common.GUILD_CREATE_DAY)(nil)),
		"GUILD_DUNGEON_MONTHLY_TIME":           reflect.ValueOf((*common.GUILD_DUNGEON_MONTHLY_TIME)(nil)),
		"GUILD_DUNGEON_WEEKLY_TIME":            reflect.ValueOf((*common.GUILD_DUNGEON_WEEKLY_TIME)(nil)),
		"GUILD_MOBILIZATION_RANK_TYPE":         reflect.ValueOf((*common.GUILD_MOBILIZATION_RANK_TYPE)(nil)),
		"GVG_DONATE_TYPE":                      reflect.ValueOf((*common.GVG_DONATE_TYPE)(nil)),
		"HANDBOOK":                             reflect.ValueOf((*common.HANDBOOK)(nil)),
		"HERO_CRYSTAL_TYPE":                    reflect.ValueOf((*common.HERO_CRYSTAL_TYPE)(nil)),
		"HERO_TAG":                             reflect.ValueOf((*common.HERO_TAG)(nil)),
		"HOT_RANK_CROSS_MERGE_TIME":            reflect.ValueOf((*common.HOT_RANK_CROSS_MERGE_TIME)(nil)),
		"ITEM_TYPE":                            reflect.ValueOf((*common.ITEM_TYPE)(nil)),
		"KNIGHT_ID":                            reflect.ValueOf((*common.KNIGHT_ID)(nil)),
		"MAIL_COND":                            reflect.ValueOf((*common.MAIL_COND)(nil)),
		"MAIL_FLAG":                            reflect.ValueOf((*common.MAIL_FLAG)(nil)),
		"MAU":                                  reflect.ValueOf((*common.MAU)(nil)),
		"MIRAGE_SWEEP_TYPE":                    reflect.ValueOf((*common.MIRAGE_SWEEP_TYPE)(nil)),
		"OFFLINE":                              reflect.ValueOf((*common.OFFLINE)(nil)),
		"OPERATE_ACTIVITY_TYPE":                reflect.ValueOf((*common.OPERATE_ACTIVITY_TYPE)(nil)),
		"ORDER_STATUS":                         reflect.ValueOf((*common.ORDER_STATUS)(nil)),
		"ORDER_TYPE":                           reflect.ValueOf((*common.ORDER_TYPE)(nil)),
		"PARAM_LIMIT":                          reflect.ValueOf((*common.PARAM_LIMIT)(nil)),
		"PEAK_PHASE_STATE":                     reflect.ValueOf((*common.PEAK_PHASE_STATE)(nil)),
		"PEAK_RANK_TYPE":                       reflect.ValueOf((*common.PEAK_RANK_TYPE)(nil)),
		"PEAK_ROUND_DURATION":                  reflect.ValueOf((*common.PEAK_ROUND_DURATION)(nil)),
		"PEAK_ROUND_STATE":                     reflect.ValueOf((*common.PEAK_ROUND_STATE)(nil)),
		"PEAK_SEASON_RANK":                     reflect.ValueOf((*common.PEAK_SEASON_RANK)(nil)),
		"PEAK_SEASON_STATE":                    reflect.ValueOf((*common.PEAK_SEASON_STATE)(nil)),
		"PEAK_STATUS":                          reflect.ValueOf((*common.PEAK_STATUS)(nil)),
		"PROMOTION_GIFT_LIMIT_TYPE":            reflect.ValueOf((*common.PROMOTION_GIFT_LIMIT_TYPE)(nil)),
		"PURCHASE":                             reflect.ValueOf((*common.PURCHASE)(nil)),
		"PURCHASEID":                           reflect.ValueOf((*common.PURCHASEID)(nil)),
		"PURCHASE_TYPE":                        reflect.ValueOf((*common.PURCHASE_TYPE)(nil)),
		"PUSH_SET_TYPE":                        reflect.ValueOf((*common.PUSH_SET_TYPE)(nil)),
		"QUALITY":                              reflect.ValueOf((*common.QUALITY)(nil)),
		"RANK":                                 reflect.ValueOf((*common.RANK)(nil)),
		"RANK_TYPE":                            reflect.ValueOf((*common.RANK_TYPE)(nil)),
		"RECHARGE_TYPE":                        reflect.ValueOf((*common.RECHARGE_TYPE)(nil)),
		"RED_POINT":                            reflect.ValueOf((*common.RED_POINT)(nil)),
		"REFUND_STATUS":                        reflect.ValueOf((*common.REFUND_STATUS)(nil)),
		"RESET_TYPE":                           reflect.ValueOf((*common.RESET_TYPE)(nil)),
		"RESOURCE":                             reflect.ValueOf((*common.RESOURCE)(nil)),
		"RESOURCE_ATTR_TYPE":                   reflect.ValueOf((*common.RESOURCE_ATTR_TYPE)(nil)),
		"RESOURCE_REWARD_FLAG":                 reflect.ValueOf((*common.RESOURCE_REWARD_FLAG)(nil)),
		"RITE_RESTRICT_STATE":                  reflect.ValueOf((*common.RITE_RESTRICT_STATE)(nil)),
		"ROUND_ACTIVITY_TYPE":                  reflect.ValueOf((*common.ROUND_ACTIVITY_TYPE)(nil)),
		"SEASON_ARENA":                         reflect.ValueOf((*common.SEASON_ARENA)(nil)),
		"SEASON_ARENA_RESET":                   reflect.ValueOf((*common.SEASON_ARENA_RESET)(nil)),
		"SEASON_DATA_RECORD_TYPE":              reflect.ValueOf((*common.SEASON_DATA_RECORD_TYPE)(nil)),
		"SEASON_DOOR_OIL_OPERATION_TYPE":       reflect.ValueOf((*common.SEASON_DOOR_OIL_OPERATION_TYPE)(nil)),
		"SEASON_DOOR_REWARD_TYPE":              reflect.ValueOf((*common.SEASON_DOOR_REWARD_TYPE)(nil)),
		"SEASON_DUNGEON_TYPE":                  reflect.ValueOf((*common.SEASON_DUNGEON_TYPE)(nil)),
		"SEASON_JEWELRY_OPERATE_TYPE":          reflect.ValueOf((*common.SEASON_JEWELRY_OPERATE_TYPE)(nil)),
		"SEASON_JEWELRY_SKILL_POS":             reflect.ValueOf((*common.SEASON_JEWELRY_SKILL_POS)(nil)),
		"SEASON_RETURN_TYPE":                   reflect.ValueOf((*common.SEASON_RETURN_TYPE)(nil)),
		"SELECT_SUMMON_COST_TYPE":              reflect.ValueOf((*common.SELECT_SUMMON_COST_TYPE)(nil)),
		"SELECT_SUMMON_TYPE":                   reflect.ValueOf((*common.SELECT_SUMMON_TYPE)(nil)),
		"SHARE_GROWTH_TYPE":                    reflect.ValueOf((*common.SHARE_GROWTH_TYPE)(nil)),
		"SHOP":                                 reflect.ValueOf((*common.SHOP)(nil)),
		"SHOPID":                               reflect.ValueOf((*common.SHOPID)(nil)),
		"SHOP_TYPE":                            reflect.ValueOf((*common.SHOP_TYPE)(nil)),
		"TASK_RESET_TYPE":                      reflect.ValueOf((*common.TASK_RESET_TYPE)(nil)),
		"TASK_TYPE":                            reflect.ValueOf((*common.TASK_TYPE)(nil)),
		"TASK_TYPE_FINISH_TYPE":                reflect.ValueOf((*common.TASK_TYPE_FINISH_TYPE)(nil)),
		"TASK_TYPE_ID":                         reflect.ValueOf((*common.TASK_TYPE_ID)(nil)),
		"TASK_TYPE_PROGRESS_TYPE":              reflect.ValueOf((*common.TASK_TYPE_PROGRESS_TYPE)(nil)),
		"TIME_TYPE":                            reflect.ValueOf((*common.TIME_TYPE)(nil)),
		"TOKEN_TYPE":                           reflect.ValueOf((*common.TOKEN_TYPE)(nil)),
		"TOWER_JUMP":                           reflect.ValueOf((*common.TOWER_JUMP)(nil)),
		"TOWER_SEASON_RANK":                    reflect.ValueOf((*common.TOWER_SEASON_RANK)(nil)),
		"TOWER_SEASON_TIME":                    reflect.ValueOf((*common.TOWER_SEASON_TIME)(nil)),
		"TOWER_TYPE":                           reflect.ValueOf((*common.TOWER_TYPE)(nil)),
		"TRIAL_TYPE":                           reflect.ValueOf((*common.TRIAL_TYPE)(nil)),
		"WEAR_OP":                              reflect.ValueOf((*common.WEAR_OP)(nil)),
		"WORLD_BOSS_FIGHT_TYPE":                reflect.ValueOf((*common.WORLD_BOSS_FIGHT_TYPE)(nil)),
		"WORLD_BOSS_LEVEL":                     reflect.ValueOf((*common.WORLD_BOSS_LEVEL)(nil)),
		"WORLD_BOSS_LOG_TYPE":                  reflect.ValueOf((*common.WORLD_BOSS_LOG_TYPE)(nil)),
		"WORLD_BOSS_RANK_TYPE":                 reflect.ValueOf((*common.WORLD_BOSS_RANK_TYPE)(nil)),
		"WRESTLE_LEVEL":                        reflect.ValueOf((*common.WRESTLE_LEVEL)(nil)),
		"WRESTLE_OP_STATUS":                    reflect.ValueOf((*common.WRESTLE_OP_STATUS)(nil)),
		"WRESTLE_RANK":                         reflect.ValueOf((*common.WRESTLE_RANK)(nil)),
		"WRESTLE_STATUS":                       reflect.ValueOf((*common.WRESTLE_STATUS)(nil)),
	}
}
