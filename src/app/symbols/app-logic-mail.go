// Code generated by 'yaegi extract app/logic/mail'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/logic/mail"
	"reflect"
)

func init() {
	Symbols["app/logic/mail/mail"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CheckGlobalMailConds":        reflect.ValueOf(mail.CheckGlobalMailConds),
		"CheckGlobalMailOpAndChannel": reflect.ValueOf(mail.CheckGlobalMailOpAndChannel),
		"HasAwards":                   reflect.ValueOf(mail.HasAwards),
		"HasRead":                     reflect.ValueOf(mail.HasRead),
		"MailOpDraw":                  reflect.ValueOf(mail.MailOpDraw),
		"MailOpRead":                  reflect.ValueOf(mail.MailOpRead),
		"MaxExpireInterval":           reflect.ValueOf(mail.MaxExpireInterval),
		"NewBox":                      reflect.ValueOf(mail.NewBox),
		"NewMail":                     reflect.ValueOf(mail.NewMail),
		"TypeGlobal":                  reflect.ValueOf(mail.TypeGlobal),
		"TypeGuard":                   reflect.ValueOf(mail.TypeGuard),
		"TypeNormal":                  reflect.ValueOf(mail.TypeNormal),

		// type definitions
		"Box":        reflect.ValueOf((*mail.Box)(nil)),
		"MailOpType": reflect.ValueOf((*mail.MailOpType)(nil)),
		"User":       reflect.ValueOf((*mail.User)(nil)),

		// interface wrapper definitions
		"_User": reflect.ValueOf((*_app_logic_mail_User)(nil)),
	}
}

// _app_logic_mail_User is an interface wrapper for User type
type _app_logic_mail_User struct {
	IValue      interface{}
	WChannel    func() uint32
	WCreateTime func() int64
	WLevel      func() uint32
	WOpID       func() uint32
	WVip        func() uint32
}

func (W _app_logic_mail_User) Channel() uint32 {
	return W.WChannel()
}
func (W _app_logic_mail_User) CreateTime() int64 {
	return W.WCreateTime()
}
func (W _app_logic_mail_User) Level() uint32 {
	return W.WLevel()
}
func (W _app_logic_mail_User) OpID() uint32 {
	return W.WOpID()
}
func (W _app_logic_mail_User) Vip() uint32 {
	return W.WVip()
}
