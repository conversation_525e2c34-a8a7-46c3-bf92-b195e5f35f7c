// Code generated by 'yaegi extract app/protos/in/log'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/protos/in/log"
	"reflect"
)

func init() {
	Symbols["app/protos/in/log/log"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_DROP":                       reflect.ValueOf(log.BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_DROP),
		"BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_NONE":                       reflect.ValueOf(log.BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_NONE),
		"BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_PROGRESS":                   reflect.ValueOf(log.BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_PROGRESS),
		"BOSS_RUSH_AWARD_TYPE_name":                                            reflect.ValueOf(&log.BOSS_RUSH_AWARD_TYPE_name).Elem(),
		"BOSS_RUSH_AWARD_TYPE_value":                                           reflect.ValueOf(&log.BOSS_RUSH_AWARD_TYPE_value).Elem(),
		"ES_LOG_INDEX_ES_LOG_INDEX_GUILD_INFO":                                 reflect.ValueOf(log.ES_LOG_INDEX_ES_LOG_INDEX_GUILD_INFO),
		"ES_LOG_INDEX_ES_LOG_INDEX_NONE":                                       reflect.ValueOf(log.ES_LOG_INDEX_ES_LOG_INDEX_NONE),
		"ES_LOG_INDEX_ES_LOG_INDEX_USER_INFO":                                  reflect.ValueOf(log.ES_LOG_INDEX_ES_LOG_INDEX_USER_INFO),
		"ES_LOG_INDEX_name":                                                    reflect.ValueOf(&log.ES_LOG_INDEX_name).Elem(),
		"ES_LOG_INDEX_value":                                                   reflect.ValueOf(&log.ES_LOG_INDEX_value).Elem(),
		"ErrIntOverflowLog":                                                    reflect.ValueOf(&log.ErrIntOverflowLog).Elem(),
		"ErrInvalidLengthLog":                                                  reflect.ValueOf(&log.ErrInvalidLengthLog).Elem(),
		"ErrUnexpectedEndOfGroupLog":                                           reflect.ValueOf(&log.ErrUnexpectedEndOfGroupLog).Elem(),
		"GUILD_LOG_MANAGE_GLM_ACCEPT":                                          reflect.ValueOf(log.GUILD_LOG_MANAGE_GLM_ACCEPT),
		"GUILD_LOG_MANAGE_GLM_CREATE":                                          reflect.ValueOf(log.GUILD_LOG_MANAGE_GLM_CREATE),
		"GUILD_LOG_MANAGE_GLM_DISBAND":                                         reflect.ValueOf(log.GUILD_LOG_MANAGE_GLM_DISBAND),
		"GUILD_LOG_MANAGE_GLM_KICK":                                            reflect.ValueOf(log.GUILD_LOG_MANAGE_GLM_KICK),
		"GUILD_LOG_MANAGE_GLM_RECRUIT":                                         reflect.ValueOf(log.GUILD_LOG_MANAGE_GLM_RECRUIT),
		"GUILD_LOG_MANAGE_GLM_REFUSE":                                          reflect.ValueOf(log.GUILD_LOG_MANAGE_GLM_REFUSE),
		"GUILD_LOG_MANAGE_name":                                                reflect.ValueOf(&log.GUILD_LOG_MANAGE_name).Elem(),
		"GUILD_LOG_MANAGE_value":                                               reflect.ValueOf(&log.GUILD_LOG_MANAGE_value).Elem(),
		"GUILD_LOG_TEXT_INFO_GLTI_MODIFY_INFO":                                 reflect.ValueOf(log.GUILD_LOG_TEXT_INFO_GLTI_MODIFY_INFO),
		"GUILD_LOG_TEXT_INFO_GLTI_MODIFY_NOTICE":                               reflect.ValueOf(log.GUILD_LOG_TEXT_INFO_GLTI_MODIFY_NOTICE),
		"GUILD_LOG_TEXT_INFO_GLTI_SET_NAME":                                    reflect.ValueOf(log.GUILD_LOG_TEXT_INFO_GLTI_SET_NAME),
		"GUILD_LOG_TEXT_INFO_name":                                             reflect.ValueOf(&log.GUILD_LOG_TEXT_INFO_name).Elem(),
		"GUILD_LOG_TEXT_INFO_value":                                            reflect.ValueOf(&log.GUILD_LOG_TEXT_INFO_value).Elem(),
		"GUILD_LOG_USER_GLU_APPLY":                                             reflect.ValueOf(log.GUILD_LOG_USER_GLU_APPLY),
		"GUILD_LOG_USER_GLU_BE_APPROVED":                                       reflect.ValueOf(log.GUILD_LOG_USER_GLU_BE_APPROVED),
		"GUILD_LOG_USER_GLU_BE_KICK":                                           reflect.ValueOf(log.GUILD_LOG_USER_GLU_BE_KICK),
		"GUILD_LOG_USER_GLU_CANCEL_APPLY":                                      reflect.ValueOf(log.GUILD_LOG_USER_GLU_CANCEL_APPLY),
		"GUILD_LOG_USER_GLU_COMBINE_JOIN":                                      reflect.ValueOf(log.GUILD_LOG_USER_GLU_COMBINE_JOIN),
		"GUILD_LOG_USER_GLU_COMBINE_KICK":                                      reflect.ValueOf(log.GUILD_LOG_USER_GLU_COMBINE_KICK),
		"GUILD_LOG_USER_GLU_JOIN":                                              reflect.ValueOf(log.GUILD_LOG_USER_GLU_JOIN),
		"GUILD_LOG_USER_GLU_QUICK_JOIN":                                        reflect.ValueOf(log.GUILD_LOG_USER_GLU_QUICK_JOIN),
		"GUILD_LOG_USER_GLU_QUIT":                                              reflect.ValueOf(log.GUILD_LOG_USER_GLU_QUIT),
		"GUILD_LOG_USER_name":                                                  reflect.ValueOf(&log.GUILD_LOG_USER_name).Elem(),
		"GUILD_LOG_USER_value":                                                 reflect.ValueOf(&log.GUILD_LOG_USER_value).Elem(),
		"GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_DRAGON_ATTACK":                  reflect.ValueOf(log.GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_DRAGON_ATTACK),
		"GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_DRAGON_CHALLENGE_COUNT":         reflect.ValueOf(log.GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_DRAGON_CHALLENGE_COUNT),
		"GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_DUNGEON":                        reflect.ValueOf(log.GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_DUNGEON),
		"GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_NONE":                           reflect.ValueOf(log.GUILD_STRATEGY_TYPE_GST_STRATEGY_TYPE_NONE),
		"GUILD_STRATEGY_TYPE_name":                                             reflect.ValueOf(&log.GUILD_STRATEGY_TYPE_name).Elem(),
		"GUILD_STRATEGY_TYPE_value":                                            reflect.ValueOf(&log.GUILD_STRATEGY_TYPE_value).Elem(),
		"KAFKA_HANDLER_TYPE_KAFKA_HANDLER_TYPE_ES":                             reflect.ValueOf(log.KAFKA_HANDLER_TYPE_KAFKA_HANDLER_TYPE_ES),
		"KAFKA_HANDLER_TYPE_KAFKA_HANDLER_TYPE_NONE":                           reflect.ValueOf(log.KAFKA_HANDLER_TYPE_KAFKA_HANDLER_TYPE_NONE),
		"KAFKA_HANDLER_TYPE_name":                                              reflect.ValueOf(&log.KAFKA_HANDLER_TYPE_name).Elem(),
		"KAFKA_HANDLER_TYPE_value":                                             reflect.ValueOf(&log.KAFKA_HANDLER_TYPE_value).Elem(),
		"KAFKA_TOPIC_KAFKA_TOPIC_ES":                                           reflect.ValueOf(log.KAFKA_TOPIC_KAFKA_TOPIC_ES),
		"KAFKA_TOPIC_KAFKA_TOPIC_NONE":                                         reflect.ValueOf(log.KAFKA_TOPIC_KAFKA_TOPIC_NONE),
		"KAFKA_TOPIC_name":                                                     reflect.ValueOf(&log.KAFKA_TOPIC_name).Elem(),
		"KAFKA_TOPIC_value":                                                    reflect.ValueOf(&log.KAFKA_TOPIC_value).Elem(),
		"RESOURCE_CHANGE_REASON_ACHIEVE":                                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACHIEVE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_COMPLIANCE_LIKE":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_COMPLIANCE_LIKE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_COMPLIANCE_RECV_AWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_COMPLIANCE_RECV_AWARD),
		"RESOURCE_CHANGE_REASON_ACTIVITY_COUPON_RECV":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_COUPON_RECV),
		"RESOURCE_CHANGE_REASON_ACTIVITY_LIFELONG_GIFT":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_LIFELONG_GIFT),
		"RESOURCE_CHANGE_REASON_ACTIVITY_MIRAGE_LIKE":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_MIRAGE_LIKE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_AWARD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_AWARD),
		"RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_CELL":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_CELL),
		"RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_INIT":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_INIT),
		"RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_RECYCLE_TICKET":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_RECYCLE_TICKET),
		"RESOURCE_CHANGE_REASON_ACTIVITY_RECHARGE_BUY":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_RECHARGE_BUY),
		"RESOURCE_CHANGE_REASON_ACTIVITY_RECHARGE_PRIVILEGED":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_RECHARGE_PRIVILEGED),
		"RESOURCE_CHANGE_REASON_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS":             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS),
		"RESOURCE_CHANGE_REASON_ACTIVITY_STORY_CHANGE_ACTIVITY":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_CHANGE_ACTIVITY),
		"RESOURCE_CHANGE_REASON_ACTIVITY_STORY_EXCHANGE":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_EXCHANGE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_STORY_FIGHT":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_FIGHT),
		"RESOURCE_CHANGE_REASON_ACTIVITY_STORY_LOGIN_AWARD":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_LOGIN_AWARD),
		"RESOURCE_CHANGE_REASON_ACTIVITY_STORY_SWEEP":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_SWEEP),
		"RESOURCE_CHANGE_REASON_ACTIVITY_STORY_TICKET_INCREASE":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_TICKET_INCREASE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_BUY_TICKET":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_BUY_TICKET),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_DROP_EXPIRED":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_DROP_EXPIRED),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_EXCHANGE":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_EXCHANGE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_FEED":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_FEED),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_LOGIN_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_LOGIN_AWARD),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_MAKE_GIFT":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_MAKE_GIFT),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_SHOOT":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_SHOOT),
		"RESOURCE_CHANGE_REASON_ACTIVITY_SUM_TASK_AWARD":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_ACTIVITY_TOWER_LIKE":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_TOWER_LIKE),
		"RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_BUY_TICKET":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_BUY_TICKET),
		"RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_LOGIN_AWARD":               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_LOGIN_AWARD),
		"RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_RECYCLE_TICKET":            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_RECYCLE_TICKET),
		"RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_SUMMON":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_SUMMON),
		"RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_TASK_AWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_ADD_PURCHASE_NUM":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ADD_PURCHASE_NUM),
		"RESOURCE_CHANGE_REASON_ARENA_BUY_TICKET":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARENA_BUY_TICKET),
		"RESOURCE_CHANGE_REASON_ARENA_FIGHT":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARENA_FIGHT),
		"RESOURCE_CHANGE_REASON_ARENA_LIKE":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARENA_LIKE),
		"RESOURCE_CHANGE_REASON_ARENA_RECV_TASK_AWARD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARENA_RECV_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_ARENA_REFRESH":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARENA_REFRESH),
		"RESOURCE_CHANGE_REASON_ARENA_VOTE":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARENA_VOTE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_ACTIVATE":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_ACTIVATE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_ADD_POINTS":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_ADD_POINTS),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_OPEN_PUZZLE":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_OPEN_PUZZLE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_POINTS_EXCHANGE":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_POINTS_EXCHANGE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RECV_ACT_AWARD":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RECV_ACT_AWARD),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RECV_TASK_AWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RECV_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RESET":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RESET),
		"RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_SUMMON":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_SUMMON),
		"RESOURCE_CHANGE_REASON_ARTIFACT_FORGE":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_FORGE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_FRAGMENT_RECYCLE_AWARD":               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_FRAGMENT_RECYCLE_AWARD),
		"RESOURCE_CHANGE_REASON_ARTIFACT_FRAGMENT_RECYCLE_COST":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_FRAGMENT_RECYCLE_COST),
		"RESOURCE_CHANGE_REASON_ARTIFACT_POINTS_EXCHANGE":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_POINTS_EXCHANGE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_REVIVE":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_REVIVE),
		"RESOURCE_CHANGE_REASON_ARTIFACT_STAR_UP":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_STAR_UP),
		"RESOURCE_CHANGE_REASON_ARTIFACT_STRENGTH":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ARTIFACT_STRENGTH),
		"RESOURCE_CHANGE_REASON_ASSISTANCE_ACTIVITY":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ASSISTANCE_ACTIVITY),
		"RESOURCE_CHANGE_REASON_AUTO_DECOMPOSE":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_AUTO_DECOMPOSE),
		"RESOURCE_CHANGE_REASON_AVATAR_USE_ITEM":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_AVATAR_USE_ITEM),
		"RESOURCE_CHANGE_REASON_BOSS_RUSH_ATTACK_BOSS":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_ATTACK_BOSS),
		"RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_BUY":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_BUY),
		"RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_INIT":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_INIT),
		"RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_RECOVER":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_RECOVER),
		"RESOURCE_CHANGE_REASON_BOSS_RUSH_TASK_AWARD":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_BOX_EXCHANGE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOX_EXCHANGE),
		"RESOURCE_CHANGE_REASON_BOX_OPEN":                                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BOX_OPEN),
		"RESOURCE_CHANGE_REASON_BUY_EMBLEM_SLOT":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BUY_EMBLEM_SLOT),
		"RESOURCE_CHANGE_REASON_BUY_HERO_SLOT":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_BUY_HERO_SLOT),
		"RESOURCE_CHANGE_REASON_CARNIVAL_TASK_RECV_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_CARNIVAL_TASK_RECV_AWARD),
		"RESOURCE_CHANGE_REASON_COMPLIANCE_TASKS":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_COMPLIANCE_TASKS),
		"RESOURCE_CHANGE_REASON_CONVERT_HERO_AWAKEN_ITEM":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_CONVERT_HERO_AWAKEN_ITEM),
		"RESOURCE_CHANGE_REASON_CRYSTAL_ADD_RESONANCE":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_CRYSTAL_ADD_RESONANCE),
		"RESOURCE_CHANGE_REASON_CRYSTAL_SPEED_SLOT_CD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_CRYSTAL_SPEED_SLOT_CD),
		"RESOURCE_CHANGE_REASON_CRYSTAL_UNLOCK_SLOT":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_CRYSTAL_UNLOCK_SLOT),
		"RESOURCE_CHANGE_REASON_DAILYTASK":                                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DAILYTASK),
		"RESOURCE_CHANGE_REASON_DAILY_ATTENDANCE_HERO":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DAILY_ATTENDANCE_HERO),
		"RESOURCE_CHANGE_REASON_DAILY_ATTENDANCE_RECEIVE_AWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DAILY_ATTENDANCE_RECEIVE_AWARD),
		"RESOURCE_CHANGE_REASON_DAILY_SPECIAL_DAILY_AWARD":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DAILY_SPECIAL_DAILY_AWARD),
		"RESOURCE_CHANGE_REASON_DAILY_SPECIAL_SCORE_AWARD":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DAILY_SPECIAL_SCORE_AWARD),
		"RESOURCE_CHANGE_REASON_DAILY_WISH_SUMMON":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DAILY_WISH_SUMMON),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_BATTLE":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_BATTLE),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_BOX":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_BOX),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_BUY_STAMINA":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_BUY_STAMINA),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_RELIC_AWARD":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_RELIC_AWARD),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_RELIC_CLEAN":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_RELIC_CLEAN),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_STAMINA_OR_KEY":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_STAMINA_OR_KEY),
		"RESOURCE_CHANGE_REASON_DISORDER_LAND_SWEEP":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_SWEEP),
		"RESOURCE_CHANGE_REASON_DISPATCH_RECEIVE_AWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISPATCH_RECEIVE_AWARD),
		"RESOURCE_CHANGE_REASON_DISPATCH_RECEIVE_TASK":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISPATCH_RECEIVE_TASK),
		"RESOURCE_CHANGE_REASON_DISPATCH_REFRESH":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DISPATCH_REFRESH),
		"RESOURCE_CHANGE_REASON_DIVINE_DEMON_HERO_SUMMON":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DIVINE_DEMON_HERO_SUMMON),
		"RESOURCE_CHANGE_REASON_DIVINE_DEMON_SUMMON_AWARD":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DIVINE_DEMON_SUMMON_AWARD),
		"RESOURCE_CHANGE_REASON_DIVINE_DEMON_TASK_AWARD":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DIVINE_DEMON_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_DRAW_MAIL":                                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DRAW_MAIL),
		"RESOURCE_CHANGE_REASON_DRAW_MAILS":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DRAW_MAILS),
		"RESOURCE_CHANGE_REASON_DROP_ACTIVITY_DAILY_AWARD":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DROP_ACTIVITY_DAILY_AWARD),
		"RESOURCE_CHANGE_REASON_DROP_ACTIVITY_EXCHANGE":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DROP_ACTIVITY_EXCHANGE),
		"RESOURCE_CHANGE_REASON_DROP_ACTIVITY_EXPIRED":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DROP_ACTIVITY_EXPIRED),
		"RESOURCE_CHANGE_REASON_DUNGEON_FIGHT":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_DUNGEON_FIGHT),
		"RESOURCE_CHANGE_REASON_EMBLEM_BLESSING":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_BLESSING),
		"RESOURCE_CHANGE_REASON_EMBLEM_COMPOSE":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_COMPOSE),
		"RESOURCE_CHANGE_REASON_EMBLEM_CUSTOMIZE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_CUSTOMIZE),
		"RESOURCE_CHANGE_REASON_EMBLEM_DECOMPOSE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_DECOMPOSE),
		"RESOURCE_CHANGE_REASON_EMBLEM_FRAGMENT_COMPOSE":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_FRAGMENT_COMPOSE),
		"RESOURCE_CHANGE_REASON_EMBLEM_GROW_TRANSFER":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_GROW_TRANSFER),
		"RESOURCE_CHANGE_REASON_EMBLEM_LEVEL_UP":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_EMBLEM_STAGE_UP":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_STAGE_UP),
		"RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT),
		"RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT_ITEM_CONFLATE":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT_ITEM_CONFLATE),
		"RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT_LOCK":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT_LOCK),
		"RESOURCE_CHANGE_REASON_EMBLEM_UPGRADE":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EMBLEM_UPGRADE),
		"RESOURCE_CHANGE_REASON_EQUIP_DECOMPOSE":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_DECOMPOSE),
		"RESOURCE_CHANGE_REASON_EQUIP_ENCHANT":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_ENCHANT),
		"RESOURCE_CHANGE_REASON_EQUIP_EVOLUTION":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_EVOLUTION),
		"RESOURCE_CHANGE_REASON_EQUIP_GROW_TRANSFER":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_GROW_TRANSFER),
		"RESOURCE_CHANGE_REASON_EQUIP_REFINE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_REFINE),
		"RESOURCE_CHANGE_REASON_EQUIP_REVIVE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_REVIVE),
		"RESOURCE_CHANGE_REASON_EQUIP_STRENGTH":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_EQUIP_STRENGTH),
		"RESOURCE_CHANGE_REASON_FIRST_RECHARGE_GIFT":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FIRST_RECHARGE_GIFT),
		"RESOURCE_CHANGE_REASON_FLOWER_ASSIST_RECV":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_ASSIST_RECV),
		"RESOURCE_CHANGE_REASON_FLOWER_BUY_OCCPUY_ATTACK_NUM":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_BUY_OCCPUY_ATTACK_NUM),
		"RESOURCE_CHANGE_REASON_FLOWER_CHANGE_GOBLIN":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_CHANGE_GOBLIN),
		"RESOURCE_CHANGE_REASON_FLOWER_CHANGE_HIGHEST_SEED":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_CHANGE_HIGHEST_SEED),
		"RESOURCE_CHANGE_REASON_FLOWER_EXTEND_OCCUPY_TIME":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_EXTEND_OCCUPY_TIME),
		"RESOURCE_CHANGE_REASON_FLOWER_HARVEST":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_HARVEST),
		"RESOURCE_CHANGE_REASON_FLOWER_LAST_OCCPUY_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_LAST_OCCPUY_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_LOOT":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_LOOT),
		"RESOURCE_CHANGE_REASON_FLOWER_OCCPUY_ATTACK_ALLY":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_OCCPUY_ATTACK_ALLY),
		"RESOURCE_CHANGE_REASON_FLOWER_OCCPUY_REVENGE_ALLY":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_OCCPUY_REVENGE_ALLY),
		"RESOURCE_CHANGE_REASON_FLOWER_RECV_LV_AWARD":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_LV_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_MONTHLY_CARD_AWARD":         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_MONTHLY_CARD_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_PREVIEW_AWARD":              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_PREVIEW_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_PREVIEW_MONTHLY_CARD_AWARD": reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_OCCUPY_PREVIEW_MONTHLY_CARD_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_REVENGE":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_REVENGE),
		"RESOURCE_CHANGE_REASON_FLOWER_REVENGE_LAST_OCCPUY_AWARD":              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_REVENGE_LAST_OCCPUY_AWARD),
		"RESOURCE_CHANGE_REASON_FLOWER_SNATCH":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_SNATCH),
		"RESOURCE_CHANGE_REASON_FLOWER_SPEED_GROW":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FLOWER_SPEED_GROW),
		"RESOURCE_CHANGE_REASON_FORECAST_AWARD":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FORECAST_AWARD),
		"RESOURCE_CHANGE_REASON_FOREST_RECV_LV_AWARD":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FOREST_RECV_LV_AWARD),
		"RESOURCE_CHANGE_REASON_FRAGMENT_COMPOSE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FRAGMENT_COMPOSE),
		"RESOURCE_CHANGE_REASON_FRAGMENT_COMPOSE_ALL":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FRAGMENT_COMPOSE_ALL),
		"RESOURCE_CHANGE_REASON_FRIEND_RECV_LIKE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FRIEND_RECV_LIKE),
		"RESOURCE_CHANGE_REASON_FRIEND_SEND_LIKE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_FRIEND_SEND_LIKE),
		"RESOURCE_CHANGE_REASON_GEM_COMPOSE":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GEM_COMPOSE),
		"RESOURCE_CHANGE_REASON_GEM_DECOMPOSE":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GEM_DECOMPOSE),
		"RESOURCE_CHANGE_REASON_GEM_REBUILD":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GEM_REBUILD),
		"RESOURCE_CHANGE_REASON_GIFT_CODE":                                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GIFT_CODE),
		"RESOURCE_CHANGE_REASON_GM":                                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GM),
		"RESOURCE_CHANGE_REASON_GODDESS_CONTRACT_LEVEL_UP":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_CONTRACT_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_GODDESS_CONTRACT_RECOVERY":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_CONTRACT_RECOVERY),
		"RESOURCE_CHANGE_REASON_GODDESS_FEED":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_FEED),
		"RESOURCE_CHANGE_REASON_GODDESS_RECOVERY":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_RECOVERY),
		"RESOURCE_CHANGE_REASON_GODDESS_STORY_AWARDS":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_STORY_AWARDS),
		"RESOURCE_CHANGE_REASON_GODDESS_TALES_TAKE_AWARDS":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_TALES_TAKE_AWARDS),
		"RESOURCE_CHANGE_REASON_GODDESS_UNLOCK_DUNGEON":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GODDESS_UNLOCK_DUNGEON),
		"RESOURCE_CHANGE_REASON_GOD_PRESENT_COLLECTED":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GOD_PRESENT_COLLECTED),
		"RESOURCE_CHANGE_REASON_GOD_PRESENT_RECV_AWARDS":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GOD_PRESENT_RECV_AWARDS),
		"RESOURCE_CHANGE_REASON_GOD_PRESENT_RECV_ITEM":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GOD_PRESENT_RECV_ITEM),
		"RESOURCE_CHANGE_REASON_GOD_PRESENT_REPLACEMENT":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GOD_PRESENT_REPLACEMENT),
		"RESOURCE_CHANGE_REASON_GOD_PRESENT_SUMMON":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GOD_PRESENT_SUMMON),
		"RESOURCE_CHANGE_REASON_GOLDBUY_RECEIVE":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GOLDBUY_RECEIVE),
		"RESOURCE_CHANGE_REASON_GST_BOSS_BUY_CHALLENGE":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_BUY_CHALLENGE),
		"RESOURCE_CHANGE_REASON_GST_BOSS_CHALLENGE_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_CHALLENGE_AWARD),
		"RESOURCE_CHANGE_REASON_GST_BOSS_CHALLENGE_RECOVER":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_CHALLENGE_RECOVER),
		"RESOURCE_CHANGE_REASON_GST_BOSS_FAIL_BACK_CHALLENGE":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_FAIL_BACK_CHALLENGE),
		"RESOURCE_CHANGE_REASON_GST_BOSS_INIT_CHALLENGE":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_INIT_CHALLENGE),
		"RESOURCE_CHANGE_REASON_GST_BOSS_MEMBER_AWARD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_MEMBER_AWARD),
		"RESOURCE_CHANGE_REASON_GST_BOSS_USE_CHALLENGE":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BOSS_USE_CHALLENGE),
		"RESOURCE_CHANGE_REASON_GST_BUILD_SEASON_RESET":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_BUILD_SEASON_RESET),
		"RESOURCE_CHANGE_REASON_GST_CHALLENGE_BOX_REWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_CHALLENGE_BOX_REWARD),
		"RESOURCE_CHANGE_REASON_GST_CHALLENGE_TASK":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_CHALLENGE_TASK),
		"RESOURCE_CHANGE_REASON_GST_DONATE_COST":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_DONATE_COST),
		"RESOURCE_CHANGE_REASON_GST_DRAGON_CHALLENGE_AWARD":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_DRAGON_CHALLENGE_AWARD),
		"RESOURCE_CHANGE_REASON_GST_DRAGON_DIAMOND_BUY_CHALLENGE_COUNT":        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_DRAGON_DIAMOND_BUY_CHALLENGE_COUNT),
		"RESOURCE_CHANGE_REASON_GST_DRAGON_DIAMOND_BUY_FAILED":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_DRAGON_DIAMOND_BUY_FAILED),
		"RESOURCE_CHANGE_REASON_GST_DRAGON_ITEM_BUY_CHALLENGE_COUNT":           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_DRAGON_ITEM_BUY_CHALLENGE_COUNT),
		"RESOURCE_CHANGE_REASON_GST_DRAGON_ITEM_BUY_FAILED":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_DRAGON_ITEM_BUY_FAILED),
		"RESOURCE_CHANGE_REASON_GST_GET_HANG_UP_RECV":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_GET_HANG_UP_RECV),
		"RESOURCE_CHANGE_REASON_GST_GROUP_RANK_LIKE":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_GROUP_RANK_LIKE),
		"RESOURCE_CHANGE_REASON_GST_ORE_BUY_ASSIST_TIMES":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_ORE_BUY_ASSIST_TIMES),
		"RESOURCE_CHANGE_REASON_GST_ORE_BUY_FIGHT_TIMES":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_ORE_BUY_FIGHT_TIMES),
		"RESOURCE_CHANGE_REASON_GST_ORE_FIGHT_AWARD":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_ORE_FIGHT_AWARD),
		"RESOURCE_CHANGE_REASON_GST_ORE_SEASON_RESET":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_ORE_SEASON_RESET),
		"RESOURCE_CHANGE_REASON_GST_TASK_REWARD":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_TASK_REWARD),
		"RESOURCE_CHANGE_REASON_GST_TECH_DONATE":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_TECH_DONATE),
		"RESOURCE_CHANGE_REASON_GST_TECH_LEVEL_UP":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_TECH_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_GST_TECH_TASK":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GST_TECH_TASK),
		"RESOURCE_CHANGE_REASON_GUIDANCE_SKIP":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUIDANCE_SKIP),
		"RESOURCE_CHANGE_REASON_GUILD_BUILD_DONATE_COST":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_BUILD_DONATE_COST),
		"RESOURCE_CHANGE_REASON_GUILD_CHEST_RECV":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_CHEST_RECV),
		"RESOURCE_CHANGE_REASON_GUILD_CHEST_RECV_FLOWER":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_CHEST_RECV_FLOWER),
		"RESOURCE_CHANGE_REASON_GUILD_CHEST_SEND_FLOWER":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_CHEST_SEND_FLOWER),
		"RESOURCE_CHANGE_REASON_GUILD_CREATE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_CREATE),
		"RESOURCE_CHANGE_REASON_GUILD_DONATE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DONATE),
		"RESOURCE_CHANGE_REASON_GUILD_DUNGEON_BUY_CHALLENGE_TIMES":             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_BUY_CHALLENGE_TIMES),
		"RESOURCE_CHANGE_REASON_GUILD_DUNGEON_CHAPTER_RANK_LIKE":               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_CHAPTER_RANK_LIKE),
		"RESOURCE_CHANGE_REASON_GUILD_DUNGEON_CHAPTER_TASK_RECEIVE":            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_CHAPTER_TASK_RECEIVE),
		"RESOURCE_CHANGE_REASON_GUILD_DUNGEON_FIGHT":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_FIGHT),
		"RESOURCE_CHANGE_REASON_GUILD_DUNGEON_RECV_BOSS_BOX":                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_RECV_BOSS_BOX),
		"RESOURCE_CHANGE_REASON_GUILD_DUNGEON_SEASON_TOP_DIVISION":             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_SEASON_TOP_DIVISION),
		"RESOURCE_CHANGE_REASON_GUILD_GET_DONATE_AWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_GET_DONATE_AWARD),
		"RESOURCE_CHANGE_REASON_GUILD_MEDAL_DISORDER_LAND_ADD":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_MEDAL_DISORDER_LAND_ADD),
		"RESOURCE_CHANGE_REASON_GUILD_MEDAL_LIKE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_MEDAL_LIKE),
		"RESOURCE_CHANGE_REASON_GUILD_MEDAL_LIKE_TO_ITEM":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_MEDAL_LIKE_TO_ITEM),
		"RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_BUY_TIMES":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_BUY_TIMES),
		"RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_SCORE_LEVELS":               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_SCORE_LEVELS),
		"RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_USER_SCORE_AWARD":           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_USER_SCORE_AWARD),
		"RESOURCE_CHANGE_REASON_GUILD_SET_NAME":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_SET_NAME),
		"RESOURCE_CHANGE_REASON_GUILD_SIGN_IN":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_SIGN_IN),
		"RESOURCE_CHANGE_REASON_GUILD_TALENT_LEVEL_UP":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_TALENT_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_GUILD_TALENT_RESET":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_GUILD_TALENT_RESET),
		"RESOURCE_CHANGE_REASON_HANDBOOK_AWARD":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HANDBOOK_AWARD),
		"RESOURCE_CHANGE_REASON_HERO_AWAKEN":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_AWAKEN),
		"RESOURCE_CHANGE_REASON_HERO_BACK":                                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_BACK),
		"RESOURCE_CHANGE_REASON_HERO_CHANGE":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_CHANGE),
		"RESOURCE_CHANGE_REASON_HERO_CHANGE_SAVE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_CHANGE_SAVE),
		"RESOURCE_CHANGE_REASON_HERO_CONVERSION":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_CONVERSION),
		"RESOURCE_CHANGE_REASON_HERO_CONVERT":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_CONVERT),
		"RESOURCE_CHANGE_REASON_HERO_DISBAND":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_DISBAND),
		"RESOURCE_CHANGE_REASON_HERO_EXCHANGE":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_EXCHANGE),
		"RESOURCE_CHANGE_REASON_HERO_GEM_LEVEL_UP":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_GEM_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_HERO_LEVEL_UP":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_HERO_REVIVE":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_REVIVE),
		"RESOURCE_CHANGE_REASON_HERO_UPGRADE_STAGE":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_UPGRADE_STAGE),
		"RESOURCE_CHANGE_REASON_HERO_UPGRADE_STAR":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_HERO_UPGRADE_STAR),
		"RESOURCE_CHANGE_REASON_ITEM_SELECT":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ITEM_SELECT),
		"RESOURCE_CHANGE_REASON_ITEM_SELL":                                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ITEM_SELL),
		"RESOURCE_CHANGE_REASON_ITEM_USE":                                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ITEM_USE),
		"RESOURCE_CHANGE_REASON_LINK_SUMMON":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_LINK_SUMMON),
		"RESOURCE_CHANGE_REASON_MAZE_BATTLE_EVENT":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_BATTLE_EVENT),
		"RESOURCE_CHANGE_REASON_MAZE_BOX_EVENT":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_BOX_EVENT),
		"RESOURCE_CHANGE_REASON_MAZE_BUY_REVIVE":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_BUY_REVIVE),
		"RESOURCE_CHANGE_REASON_MAZE_CHOICE_EVENT":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_CHOICE_EVENT),
		"RESOURCE_CHANGE_REASON_MAZE_RECOVERY_HEROES":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_RECOVERY_HEROES),
		"RESOURCE_CHANGE_REASON_MAZE_SWEEP_AWARDS":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_SWEEP_AWARDS),
		"RESOURCE_CHANGE_REASON_MAZE_TASK_AWARD":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MAZE_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_MEDAL_DAILY_AWARD_RECEIVE":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MEDAL_DAILY_AWARD_RECEIVE),
		"RESOURCE_CHANGE_REASON_MEDAL_LEVEL_AWARD_RECEIVE":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MEDAL_LEVEL_AWARD_RECEIVE),
		"RESOURCE_CHANGE_REASON_MEDAL_TASK_AWARD_RECEIVE":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MEDAL_TASK_AWARD_RECEIVE),
		"RESOURCE_CHANGE_REASON_MEMORY_UNLOCK_CHIP":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MEMORY_UNLOCK_CHIP),
		"RESOURCE_CHANGE_REASON_MIRAGE_BUY_COUNT":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MIRAGE_BUY_COUNT),
		"RESOURCE_CHANGE_REASON_MIRAGE_FIGHT":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MIRAGE_FIGHT),
		"RESOURCE_CHANGE_REASON_MIRAGE_POWER_CRUSH":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MIRAGE_POWER_CRUSH),
		"RESOURCE_CHANGE_REASON_MIRAGE_RECEIVE_AWARD":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MIRAGE_RECEIVE_AWARD),
		"RESOURCE_CHANGE_REASON_MIRAGE_RECV_STAR_AWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MIRAGE_RECV_STAR_AWARD),
		"RESOURCE_CHANGE_REASON_MIRAGE_SWEEP":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MIRAGE_SWEEP),
		"RESOURCE_CHANGE_REASON_MONTHLY_CARD_DAILY_AWARD_RECEIVE":              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MONTHLY_CARD_DAILY_AWARD_RECEIVE),
		"RESOURCE_CHANGE_REASON_MONTHLY_CARD_RECHARGE":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MONTHLY_CARD_RECHARGE),
		"RESOURCE_CHANGE_REASON_MONTH_TASKS_RECV_AWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_MONTH_TASKS_RECV_AWARD),
		"RESOURCE_CHANGE_REASON_NEW_HANDBOOK_HERO":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_NEW_HANDBOOK_HERO),
		"RESOURCE_CHANGE_REASON_NEW_SKIN_AVATAR":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_NEW_SKIN_AVATAR),
		"RESOURCE_CHANGE_REASON_NEW_YEAR_LOGIN_ACTIVITY":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_NEW_YEAR_LOGIN_ACTIVITY),
		"RESOURCE_CHANGE_REASON_NONE":                                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_NONE),
		"RESOURCE_CHANGE_REASON_ONHOOK":                                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ONHOOK),
		"RESOURCE_CHANGE_REASON_OPERATION_ACTIVITY_GIFT":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_OPERATION_ACTIVITY_GIFT),
		"RESOURCE_CHANGE_REASON_OPERATION_ACTIVITY_TASK":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_OPERATION_ACTIVITY_TASK),
		"RESOURCE_CHANGE_REASON_PASS_LEVEL_BUY":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PASS_LEVEL_BUY),
		"RESOURCE_CHANGE_REASON_PASS_RECEIVE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PASS_RECEIVE),
		"RESOURCE_CHANGE_REASON_PASS_RECHARGE":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PASS_RECHARGE),
		"RESOURCE_CHANGE_REASON_PEAK_GUESS":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PEAK_GUESS),
		"RESOURCE_CHANGE_REASON_PEAK_RECV_INVITE_AWARDS":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PEAK_RECV_INVITE_AWARDS),
		"RESOURCE_CHANGE_REASON_PEAK_WORSHIP":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PEAK_WORSHIP),
		"RESOURCE_CHANGE_REASON_PLAYER_LEVELUP":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PLAYER_LEVELUP),
		"RESOURCE_CHANGE_REASON_PRE_SEASON_RECV":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PRE_SEASON_RECV),
		"RESOURCE_CHANGE_REASON_PROMOTION_FREE_GIFT":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PROMOTION_FREE_GIFT),
		"RESOURCE_CHANGE_REASON_PROMOTION_GIFT":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PROMOTION_GIFT),
		"RESOURCE_CHANGE_REASON_PUSH_GIFT_RECHARGE":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PUSH_GIFT_RECHARGE),
		"RESOURCE_CHANGE_REASON_PYRAMID_DRAW":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PYRAMID_DRAW),
		"RESOURCE_CHANGE_REASON_PYRAMID_RESET":                                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PYRAMID_RESET),
		"RESOURCE_CHANGE_REASON_PYRAMID_TASK_RECEIVE_AWARD":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_PYRAMID_TASK_RECEIVE_AWARD),
		"RESOURCE_CHANGE_REASON_QUIT_GUILD":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_QUIT_GUILD),
		"RESOURCE_CHANGE_REASON_RANKACHIEVE_RECV_AWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RANKACHIEVE_RECV_AWARD),
		"RESOURCE_CHANGE_REASON_RANK_ACTIVITY_TOWER_SEASON_LIKE_AWARD":         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RANK_ACTIVITY_TOWER_SEASON_LIKE_AWARD),
		"RESOURCE_CHANGE_REASON_RECHARGE_BY_COUPON":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECHARGE_BY_COUPON),
		"RESOURCE_CHANGE_REASON_RECHARGE_COUPON":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECHARGE_COUPON),
		"RESOURCE_CHANGE_REASON_RECHARGE_FIRST_PRESENT":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECHARGE_FIRST_PRESENT),
		"RESOURCE_CHANGE_REASON_RECHARGE_NORMAL":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECHARGE_NORMAL),
		"RESOURCE_CHANGE_REASON_RECHARGE_REFUND":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECHARGE_REFUND),
		"RESOURCE_CHANGE_REASON_RECV_H5_DESKTOP_REWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECV_H5_DESKTOP_REWARD),
		"RESOURCE_CHANGE_REASON_RECV_SEASON_DUNGEON_AWARD":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECV_SEASON_DUNGEON_AWARD),
		"RESOURCE_CHANGE_REASON_RECV_SEASON_LEVEL_AWARDS":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECV_SEASON_LEVEL_AWARDS),
		"RESOURCE_CHANGE_REASON_RECV_SEASON_LEVEL_TASK_AWARDS":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECV_SEASON_LEVEL_TASK_AWARDS),
		"RESOURCE_CHANGE_REASON_RECV_SHARE_AWARD":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RECV_SHARE_AWARD),
		"RESOURCE_CHANGE_REASON_REFRESH_RANDOM_SHOP":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_REFRESH_RANDOM_SHOP),
		"RESOURCE_CHANGE_REASON_REG_ADD_RESOURCE":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_REG_ADD_RESOURCE),
		"RESOURCE_CHANGE_REASON_REMAIN_BOOK_LEVEL_UP":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_REMAIN_BOOK_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_RITE_MARK_DECOMPOSE":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RITE_MARK_DECOMPOSE),
		"RESOURCE_CHANGE_REASON_RITE_RECYCLE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RITE_RECYCLE),
		"RESOURCE_CHANGE_REASON_RITE_TAKE_RARE_AWARDS":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_RITE_TAKE_RARE_AWARDS),
		"RESOURCE_CHANGE_REASON_ROBOT_ADD_RESOURCE":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ROBOT_ADD_RESOURCE),
		"RESOURCE_CHANGE_REASON_ROUND_ACTIVITY_RECV_TASK_AWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_ROUND_ACTIVITY_RECV_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_SEASON_ARENA_BUY_CHALLENGE_COUNT":              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_ARENA_BUY_CHALLENGE_COUNT),
		"RESOURCE_CHANGE_REASON_SEASON_ARENA_CHALLENGE":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_ARENA_CHALLENGE),
		"RESOURCE_CHANGE_REASON_SEASON_ARENA_DIVISION_AWARD":                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_ARENA_DIVISION_AWARD),
		"RESOURCE_CHANGE_REASON_SEASON_ARENA_REFRESH_OPPONENT":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_ARENA_REFRESH_OPPONENT),
		"RESOURCE_CHANGE_REASON_SEASON_ARENA_TASK_AWARD":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_ARENA_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_SEASON_ARENA_USE_TICKET":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_ARENA_USE_TICKET),
		"RESOURCE_CHANGE_REASON_SEASON_COMPLIANCE_STAGE_REWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_COMPLIANCE_STAGE_REWARD),
		"RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_LINE":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_LINE),
		"RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_LINE_REWARD":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_LINE_REWARD),
		"RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_REWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_REWARD),
		"RESOURCE_CHANGE_REASON_SEASON_DOOR_SEASON_RESET":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_SEASON_RESET),
		"RESOURCE_CHANGE_REASON_SEASON_DOOR_TASK_REWARD":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_TASK_REWARD),
		"RESOURCE_CHANGE_REASON_SEASON_DOOR_USE_OIL":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_USE_OIL),
		"RESOURCE_CHANGE_REASON_SEASON_DUNGEON_RECV_AWARDS":                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_DUNGEON_RECV_AWARDS),
		"RESOURCE_CHANGE_REASON_SEASON_JEWELRY_DECOMPOSE":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_JEWELRY_DECOMPOSE),
		"RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SEASON_RESET":                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SEASON_RESET),
		"RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SKILL_CHANGE":                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SKILL_CHANGE),
		"RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SKILL_CLASS_UP":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SKILL_CLASS_UP),
		"RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SKILL_LEVEL_UP":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SKILL_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_SEASON_LEVEL_UP":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_SEASON_LINK_ACTIVATE":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_LINK_ACTIVATE),
		"RESOURCE_CHANGE_REASON_SEASON_LINK_TAKE_RARE_AWARDS":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_LINK_TAKE_RARE_AWARDS),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_ALTAR_RESET":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_ALTAR_RESET),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_BUY_GOODS":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_BUY_GOODS),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_BUY_STAMINA":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_BUY_STAMINA),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_ERROR_BACK":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_ERROR_BACK),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT_PROGRESS":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT_PROGRESS),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_INIT":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_INIT),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_MASTER":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_MASTER),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_RECOVER":                            reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_RECOVER),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_SEASON_RESET":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_SEASON_RESET),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_SELL_GOODS":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_SELL_GOODS),
		"RESOURCE_CHANGE_REASON_SEASON_MAP_TASK_AWARD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_MAP_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_SEASON_RESET_DEL_LEVEL_TOKEN":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_RESET_DEL_LEVEL_TOKEN),
		"RESOURCE_CHANGE_REASON_SEASON_RETURN_TAKE_AWARDS":                     reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_RETURN_TAKE_AWARDS),
		"RESOURCE_CHANGE_REASON_SEASON_SHOP_BUY":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_SHOP_BUY),
		"RESOURCE_CHANGE_REASON_SEASON_TALENT_AWARD_RESET_ITEM":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEASON_TALENT_AWARD_RESET_ITEM),
		"RESOURCE_CHANGE_REASON_SELECT_SUMMON_AWARD":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SELECT_SUMMON_AWARD),
		"RESOURCE_CHANGE_REASON_SET_NAME":                                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SET_NAME),
		"RESOURCE_CHANGE_REASON_SEVENDAY_AWARD":                                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SEVENDAY_AWARD),
		"RESOURCE_CHANGE_REASON_SHOP_BUY":                                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SHOP_BUY),
		"RESOURCE_CHANGE_REASON_SHOP_RESET":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SHOP_RESET),
		"RESOURCE_CHANGE_REASON_SPEED_ONHOOK":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SPEED_ONHOOK),
		"RESOURCE_CHANGE_REASON_STORY_REVIEW_UNLOCK":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_STORY_REVIEW_UNLOCK),
		"RESOURCE_CHANGE_REASON_SUMMON_HERO":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_SUMMON_HERO),
		"RESOURCE_CHANGE_REASON_TALENT_TREE_LEVEL_UP":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TALENT_TREE_LEVEL_UP),
		"RESOURCE_CHANGE_REASON_TALENT_TREE_RECV_TASK_AWARDS":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TALENT_TREE_RECV_TASK_AWARDS),
		"RESOURCE_CHANGE_REASON_TALENT_TREE_RESET":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TALENT_TREE_RESET),
		"RESOURCE_CHANGE_REASON_TALENT_TREE_SEASON_RESET":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TALENT_TREE_SEASON_RESET),
		"RESOURCE_CHANGE_REASON_TALES_ELITE":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TALES_ELITE),
		"RESOURCE_CHANGE_REASON_TALES_TAKE_AWARD":                              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TALES_TAKE_AWARD),
		"RESOURCE_CHANGE_REASON_TIME_LIMIT_ITEM":                               reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TIME_LIMIT_ITEM),
		"RESOURCE_CHANGE_REASON_TOWERSTAR_DAILY_AWARD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWERSTAR_DAILY_AWARD),
		"RESOURCE_CHANGE_REASON_TOWERSTAR_FIRST_AWARD":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWERSTAR_FIRST_AWARD),
		"RESOURCE_CHANGE_REASON_TOWERSTAR_STAR_AWARD":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWERSTAR_STAR_AWARD),
		"RESOURCE_CHANGE_REASON_TOWER_FIGHT":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWER_FIGHT),
		"RESOURCE_CHANGE_REASON_TOWER_JUMP":                                    reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWER_JUMP),
		"RESOURCE_CHANGE_REASON_TOWER_SEASON_FIGHT_PASS_AWARD":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWER_SEASON_FIGHT_PASS_AWARD),
		"RESOURCE_CHANGE_REASON_TOWER_SEASON_LIKE_AWARD":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWER_SEASON_LIKE_AWARD),
		"RESOURCE_CHANGE_REASON_TOWER_SEASON_RECV_TASK_AWARD":                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWER_SEASON_RECV_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_TOWER_SWEEP":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TOWER_SWEEP),
		"RESOURCE_CHANGE_REASON_TRIAL_FIGHT":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TRIAL_FIGHT),
		"RESOURCE_CHANGE_REASON_TRIAL_ON_HOOK_RECV_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TRIAL_ON_HOOK_RECV_AWARD),
		"RESOURCE_CHANGE_REASON_TRIAL_RECEIVE_STAR_AWARD":                      reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TRIAL_RECEIVE_STAR_AWARD),
		"RESOURCE_CHANGE_REASON_TRIAL_SPEED_ON_HOOK_RECV_AWARD":                reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TRIAL_SPEED_ON_HOOK_RECV_AWARD),
		"RESOURCE_CHANGE_REASON_TRIAL_SWEEP":                                   reflect.ValueOf(log.RESOURCE_CHANGE_REASON_TRIAL_SWEEP),
		"RESOURCE_CHANGE_REASON_VIP_BUY_GIFT":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_VIP_BUY_GIFT),
		"RESOURCE_CHANGE_REASON_VIP_RECHARGE_GIFT":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_VIP_RECHARGE_GIFT),
		"RESOURCE_CHANGE_REASON_WEB_RECHARGE_COUPON":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WEB_RECHARGE_COUPON),
		"RESOURCE_CHANGE_REASON_WEB_RECHARGE_DIAMOND":                          reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WEB_RECHARGE_DIAMOND),
		"RESOURCE_CHANGE_REASON_WEB_RECHARGE_GIFT":                             reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WEB_RECHARGE_GIFT),
		"RESOURCE_CHANGE_REASON_WORLD_BOSS_FIGHT_AWARD":                        reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_FIGHT_AWARD),
		"RESOURCE_CHANGE_REASON_WORLD_BOSS_FIGHT_COST":                         reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_FIGHT_COST),
		"RESOURCE_CHANGE_REASON_WORLD_BOSS_RECEIVE_TASK_AWARD":                 reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_RECEIVE_TASK_AWARD),
		"RESOURCE_CHANGE_REASON_WORLD_BOSS_RECEIVE_WORSHIP_AWARD":              reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_RECEIVE_WORSHIP_AWARD),
		"RESOURCE_CHANGE_REASON_WRESTLE_CHANGE_ROOM":                           reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WRESTLE_CHANGE_ROOM),
		"RESOURCE_CHANGE_REASON_WRESTLE_LIKE":                                  reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WRESTLE_LIKE),
		"RESOURCE_CHANGE_REASON_WRESTLE_RECV_LEVEL_TASK":                       reflect.ValueOf(log.RESOURCE_CHANGE_REASON_WRESTLE_RECV_LEVEL_TASK),
		"RESOURCE_CHANGE_name":                                                 reflect.ValueOf(&log.RESOURCE_CHANGE_name).Elem(),
		"RESOURCE_CHANGE_value":                                                reflect.ValueOf(&log.RESOURCE_CHANGE_value).Elem(),
		"SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_SJSCCT_GIVEUP":               reflect.ValueOf(log.SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_SJSCCT_GIVEUP),
		"SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_SJSCCT_NONE":                 reflect.ValueOf(log.SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_SJSCCT_NONE),
		"SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_SJSCCT_RESERVE":              reflect.ValueOf(log.SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_SJSCCT_RESERVE),
		"SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_name":                        reflect.ValueOf(&log.SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_name).Elem(),
		"SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_value":                       reflect.ValueOf(&log.SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE_value).Elem(),
		"SUB_TYPE_ID_ACCOUNT_LEVEL":                                            reflect.ValueOf(log.SUB_TYPE_ID_ACCOUNT_LEVEL),
		"SUB_TYPE_ID_ACCOUNT_OFFLINE":                                          reflect.ValueOf(log.SUB_TYPE_ID_ACCOUNT_OFFLINE),
		"SUB_TYPE_ID_ACCOUNT_ONLINE":                                           reflect.ValueOf(log.SUB_TYPE_ID_ACCOUNT_ONLINE),
		"SUB_TYPE_ID_ACCOUNT_SET_NAME":                                         reflect.ValueOf(log.SUB_TYPE_ID_ACCOUNT_SET_NAME),
		"SUB_TYPE_ID_ACCOUNT_VIP":                                              reflect.ValueOf(log.SUB_TYPE_ID_ACCOUNT_VIP),
		"SUB_TYPE_ID_ACCUSATION":                                               reflect.ValueOf(log.SUB_TYPE_ID_ACCUSATION),
		"SUB_TYPE_ID_ACTIVITY_RECHARGE_BUY":                                    reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_RECHARGE_BUY),
		"SUB_TYPE_ID_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS":                        reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS),
		"SUB_TYPE_ID_ACTIVITY_STORY_EXCHANGE":                                  reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_STORY_EXCHANGE),
		"SUB_TYPE_ID_ACTIVITY_STORY_FIGHT":                                     reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_STORY_FIGHT),
		"SUB_TYPE_ID_ACTIVITY_STORY_LOGIN_AWARD":                               reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_STORY_LOGIN_AWARD),
		"SUB_TYPE_ID_ACTIVITY_SUM_EXCHANGE":                                    reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_EXCHANGE),
		"SUB_TYPE_ID_ACTIVITY_SUM_FEED_FEED":                                   reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_FEED_FEED),
		"SUB_TYPE_ID_ACTIVITY_SUM_FEED_MAKE_GIFT":                              reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_FEED_MAKE_GIFT),
		"SUB_TYPE_ID_ACTIVITY_SUM_LOGIN_REWARD":                                reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_LOGIN_REWARD),
		"SUB_TYPE_ID_ACTIVITY_SUM_PUZZLE_CELL":                                 reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_PUZZLE_CELL),
		"SUB_TYPE_ID_ACTIVITY_SUM_SHOOT":                                       reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_SHOOT),
		"SUB_TYPE_ID_ACTIVITY_SUM_TASK_REWARD":                                 reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_TASK_REWARD),
		"SUB_TYPE_ID_ACTIVITY_SUM_TICKET_BUY":                                  reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_TICKET_BUY),
		"SUB_TYPE_ID_ACTIVITY_SUM_TURN_TABLE_SELECT_BUFF":                      reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_TURN_TABLE_SELECT_BUFF),
		"SUB_TYPE_ID_ACTIVITY_SUM_TURN_TABLE_SUMMON":                           reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_SUM_TURN_TABLE_SUMMON),
		"SUB_TYPE_ID_ACTIVITY_TURN_TABLE_LOGIN_AWARD":                          reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_LOGIN_AWARD),
		"SUB_TYPE_ID_ACTIVITY_TURN_TABLE_SUMMON":                               reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_SUMMON),
		"SUB_TYPE_ID_ACTIVITY_TURN_TABLE_TASK_AWARD":                           reflect.ValueOf(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_TASK_AWARD),
		"SUB_TYPE_ID_ADD_DIAMOND":                                              reflect.ValueOf(log.SUB_TYPE_ID_ADD_DIAMOND),
		"SUB_TYPE_ID_ADD_PURCHASE_NUM":                                         reflect.ValueOf(log.SUB_TYPE_ID_ADD_PURCHASE_NUM),
		"SUB_TYPE_ID_ARENA_BE_FIGHT":                                           reflect.ValueOf(log.SUB_TYPE_ID_ARENA_BE_FIGHT),
		"SUB_TYPE_ID_ARENA_FIGHT":                                              reflect.ValueOf(log.SUB_TYPE_ID_ARENA_FIGHT),
		"SUB_TYPE_ID_ARENA_LIKE":                                               reflect.ValueOf(log.SUB_TYPE_ID_ARENA_LIKE),
		"SUB_TYPE_ID_ARENA_RECV_AWARD":                                         reflect.ValueOf(log.SUB_TYPE_ID_ARENA_RECV_AWARD),
		"SUB_TYPE_ID_ARENA_REFRESH":                                            reflect.ValueOf(log.SUB_TYPE_ID_ARENA_REFRESH),
		"SUB_TYPE_ID_ARTIFACT_ACTIVATE":                                        reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_ACTIVATE),
		"SUB_TYPE_ID_ARTIFACT_DEBUT_OPEN_PUZZLE":                               reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_DEBUT_OPEN_PUZZLE),
		"SUB_TYPE_ID_ARTIFACT_DEBUT_RECV_ACT_AWARD":                            reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_DEBUT_RECV_ACT_AWARD),
		"SUB_TYPE_ID_ARTIFACT_DEBUT_RECV_TASK_AWARD":                           reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_DEBUT_RECV_TASK_AWARD),
		"SUB_TYPE_ID_ARTIFACT_DEBUT_SET_WISH":                                  reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_DEBUT_SET_WISH),
		"SUB_TYPE_ID_ARTIFACT_DEBUT_SUMMON":                                    reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_DEBUT_SUMMON),
		"SUB_TYPE_ID_ARTIFACT_FORGE":                                           reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_FORGE),
		"SUB_TYPE_ID_ARTIFACT_FRAGMENT_RECYCLE":                                reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_FRAGMENT_RECYCLE),
		"SUB_TYPE_ID_ARTIFACT_REVIVE":                                          reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_REVIVE),
		"SUB_TYPE_ID_ARTIFACT_STAR_UP":                                         reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_STAR_UP),
		"SUB_TYPE_ID_ARTIFACT_STRENGTH":                                        reflect.ValueOf(log.SUB_TYPE_ID_ARTIFACT_STRENGTH),
		"SUB_TYPE_ID_ASSISTANCE_ACTIVITY_RECV_AWARD":                           reflect.ValueOf(log.SUB_TYPE_ID_ASSISTANCE_ACTIVITY_RECV_AWARD),
		"SUB_TYPE_ID_AVATAR_ADD":                                               reflect.ValueOf(log.SUB_TYPE_ID_AVATAR_ADD),
		"SUB_TYPE_ID_AVATAR_SET_ICON":                                          reflect.ValueOf(log.SUB_TYPE_ID_AVATAR_SET_ICON),
		"SUB_TYPE_ID_AVATAR_TIME_EXPAND":                                       reflect.ValueOf(log.SUB_TYPE_ID_AVATAR_TIME_EXPAND),
		"SUB_TYPE_ID_BOSS_RUSH_FIGHT":                                          reflect.ValueOf(log.SUB_TYPE_ID_BOSS_RUSH_FIGHT),
		"SUB_TYPE_ID_BOSS_RUSH_TASK_AWARD":                                     reflect.ValueOf(log.SUB_TYPE_ID_BOSS_RUSH_TASK_AWARD),
		"SUB_TYPE_ID_CARNIVAL_RECEIVE_AWARD":                                   reflect.ValueOf(log.SUB_TYPE_ID_CARNIVAL_RECEIVE_AWARD),
		"SUB_TYPE_ID_CHAT":                                                     reflect.ValueOf(log.SUB_TYPE_ID_CHAT),
		"SUB_TYPE_ID_CHAT_LIKE":                                                reflect.ValueOf(log.SUB_TYPE_ID_CHAT_LIKE),
		"SUB_TYPE_ID_CONSUME_DIAMOND":                                          reflect.ValueOf(log.SUB_TYPE_ID_CONSUME_DIAMOND),
		"SUB_TYPE_ID_CRYSTAL_ACTIVE_ACHIEVEMENT":                               reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_ACTIVE_ACHIEVEMENT),
		"SUB_TYPE_ID_CRYSTAL_ADD_HERO":                                         reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_ADD_HERO),
		"SUB_TYPE_ID_CRYSTAL_BLESSING_LEVEL_UP":                                reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_BLESSING_LEVEL_UP),
		"SUB_TYPE_ID_CRYSTAL_INIT":                                             reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_INIT),
		"SUB_TYPE_ID_CRYSTAL_REMOVE_HERO":                                      reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_REMOVE_HERO),
		"SUB_TYPE_ID_CRYSTAL_SPEED_SLOT_C_D":                                   reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_SPEED_SLOT_C_D),
		"SUB_TYPE_ID_CRYSTAL_UNLOCK_SLOT":                                      reflect.ValueOf(log.SUB_TYPE_ID_CRYSTAL_UNLOCK_SLOT),
		"SUB_TYPE_ID_DAILY_ATTENDANCE_RECV_AWARD":                              reflect.ValueOf(log.SUB_TYPE_ID_DAILY_ATTENDANCE_RECV_AWARD),
		"SUB_TYPE_ID_DAILY_SPECIAL_RECV_AWARD":                                 reflect.ValueOf(log.SUB_TYPE_ID_DAILY_SPECIAL_RECV_AWARD),
		"SUB_TYPE_ID_DELETE_MAILS":                                             reflect.ValueOf(log.SUB_TYPE_ID_DELETE_MAILS),
		"SUB_TYPE_ID_DISORDER_LAND_BUY_STAMINA":                                reflect.ValueOf(log.SUB_TYPE_ID_DISORDER_LAND_BUY_STAMINA),
		"SUB_TYPE_ID_DISORDER_LAND_FIGHT":                                      reflect.ValueOf(log.SUB_TYPE_ID_DISORDER_LAND_FIGHT),
		"SUB_TYPE_ID_DISORDER_LAND_TRIGGER_EVENT":                              reflect.ValueOf(log.SUB_TYPE_ID_DISORDER_LAND_TRIGGER_EVENT),
		"SUB_TYPE_ID_DISORDER_LAND_TRIGGER_GUARANTEE":                          reflect.ValueOf(log.SUB_TYPE_ID_DISORDER_LAND_TRIGGER_GUARANTEE),
		"SUB_TYPE_ID_DISPATCH_LEVEL_UP":                                        reflect.ValueOf(log.SUB_TYPE_ID_DISPATCH_LEVEL_UP),
		"SUB_TYPE_ID_DISPATCH_RECEIVE_AWARD":                                   reflect.ValueOf(log.SUB_TYPE_ID_DISPATCH_RECEIVE_AWARD),
		"SUB_TYPE_ID_DISPATCH_RECEIVE_TASK":                                    reflect.ValueOf(log.SUB_TYPE_ID_DISPATCH_RECEIVE_TASK),
		"SUB_TYPE_ID_DISPATCH_REFRESH_TASK":                                    reflect.ValueOf(log.SUB_TYPE_ID_DISPATCH_REFRESH_TASK),
		"SUB_TYPE_ID_DIVINE_DEMON_RECEIVE_TASK_AWARD":                          reflect.ValueOf(log.SUB_TYPE_ID_DIVINE_DEMON_RECEIVE_TASK_AWARD),
		"SUB_TYPE_ID_DIVINE_DEMON_SUMMON":                                      reflect.ValueOf(log.SUB_TYPE_ID_DIVINE_DEMON_SUMMON),
		"SUB_TYPE_ID_DRAW_MAILS":                                               reflect.ValueOf(log.SUB_TYPE_ID_DRAW_MAILS),
		"SUB_TYPE_ID_DROP_ACTIVITY_EXCHANGE":                                   reflect.ValueOf(log.SUB_TYPE_ID_DROP_ACTIVITY_EXCHANGE),
		"SUB_TYPE_ID_DROP_ACTIVITY_RECV_DAILY_AWARD":                           reflect.ValueOf(log.SUB_TYPE_ID_DROP_ACTIVITY_RECV_DAILY_AWARD),
		"SUB_TYPE_ID_DUEL":                                                     reflect.ValueOf(log.SUB_TYPE_ID_DUEL),
		"SUB_TYPE_ID_DUNGEON_FIGHT":                                            reflect.ValueOf(log.SUB_TYPE_ID_DUNGEON_FIGHT),
		"SUB_TYPE_ID_DUNGEON_RECV_AWARD":                                       reflect.ValueOf(log.SUB_TYPE_ID_DUNGEON_RECV_AWARD),
		"SUB_TYPE_ID_DUNGEON_SPEED_RECV_AWARD":                                 reflect.ValueOf(log.SUB_TYPE_ID_DUNGEON_SPEED_RECV_AWARD),
		"SUB_TYPE_ID_EMBLEM_BLESSING":                                          reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_BLESSING),
		"SUB_TYPE_ID_EMBLEM_BUY_SLOT":                                          reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_BUY_SLOT),
		"SUB_TYPE_ID_EMBLEM_COMPOSE":                                           reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_COMPOSE),
		"SUB_TYPE_ID_EMBLEM_DECOMPOSE":                                         reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_DECOMPOSE),
		"SUB_TYPE_ID_EMBLEM_EXCLUSIVE":                                         reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_EXCLUSIVE),
		"SUB_TYPE_ID_EMBLEM_FRAGMENT_COMPOSE":                                  reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_FRAGMENT_COMPOSE),
		"SUB_TYPE_ID_EMBLEM_GROW_TRANSFER":                                     reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_GROW_TRANSFER),
		"SUB_TYPE_ID_EMBLEM_LEVELUP":                                           reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_LEVELUP),
		"SUB_TYPE_ID_EMBLEM_REVIVE":                                            reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_REVIVE),
		"SUB_TYPE_ID_EMBLEM_SET_AUTO_DECOMPOSE":                                reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_SET_AUTO_DECOMPOSE),
		"SUB_TYPE_ID_EMBLEM_STAGEUP":                                           reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_STAGEUP),
		"SUB_TYPE_ID_EMBLEM_SUCCINCT":                                          reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_SUCCINCT),
		"SUB_TYPE_ID_EMBLEM_SUCCINCT_ITEM_CONFLATE":                            reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_SUCCINCT_ITEM_CONFLATE),
		"SUB_TYPE_ID_EMBLEM_SUCCINCT_LOCK_OR_SAVE":                             reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_SUCCINCT_LOCK_OR_SAVE),
		"SUB_TYPE_ID_EMBLEM_UPGRADE":                                           reflect.ValueOf(log.SUB_TYPE_ID_EMBLEM_UPGRADE),
		"SUB_TYPE_ID_EQUIP_DECOMPOSE":                                          reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_DECOMPOSE),
		"SUB_TYPE_ID_EQUIP_ENCHANT":                                            reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_ENCHANT),
		"SUB_TYPE_ID_EQUIP_EVOLUTION":                                          reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_EVOLUTION),
		"SUB_TYPE_ID_EQUIP_GROW_TRANSFER":                                      reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_GROW_TRANSFER),
		"SUB_TYPE_ID_EQUIP_REFINE":                                             reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_REFINE),
		"SUB_TYPE_ID_EQUIP_REVIVE":                                             reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_REVIVE),
		"SUB_TYPE_ID_EQUIP_SET_AUTO_DECOMPOSE":                                 reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_SET_AUTO_DECOMPOSE),
		"SUB_TYPE_ID_EQUIP_STRENGTH":                                           reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_STRENGTH),
		"SUB_TYPE_ID_EQUIP_WEAR":                                               reflect.ValueOf(log.SUB_TYPE_ID_EQUIP_WEAR),
		"SUB_TYPE_ID_FIGHT_PVE":                                                reflect.ValueOf(log.SUB_TYPE_ID_FIGHT_PVE),
		"SUB_TYPE_ID_FIGHT_PVP":                                                reflect.ValueOf(log.SUB_TYPE_ID_FIGHT_PVP),
		"SUB_TYPE_ID_FIGHT_REPORT":                                             reflect.ValueOf(log.SUB_TYPE_ID_FIGHT_REPORT),
		"SUB_TYPE_ID_FIGHT_REPORT_DETAILS":                                     reflect.ValueOf(log.SUB_TYPE_ID_FIGHT_REPORT_DETAILS),
		"SUB_TYPE_ID_FLOWER_ATTACK_LEVEL_GUARD":                                reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_ATTACK_LEVEL_GUARD),
		"SUB_TYPE_ID_FLOWER_BE_SNATCH":                                         reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_BE_SNATCH),
		"SUB_TYPE_ID_FLOWER_BUY_OCCUPY_ATTACK_NUM":                             reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_BUY_OCCUPY_ATTACK_NUM),
		"SUB_TYPE_ID_FLOWER_CHANGE_GOBLIN":                                     reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_CHANGE_GOBLIN),
		"SUB_TYPE_ID_FLOWER_EXTEND_OCCUPY_TIME":                                reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_EXTEND_OCCUPY_TIME),
		"SUB_TYPE_ID_FLOWER_FEED_GOBLIN":                                       reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_FEED_GOBLIN),
		"SUB_TYPE_ID_FLOWER_FEED_SPECIAL":                                      reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_FEED_SPECIAL),
		"SUB_TYPE_ID_FLOWER_HARVEST":                                           reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_HARVEST),
		"SUB_TYPE_ID_FLOWER_LEVEL_UP":                                          reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_LEVEL_UP),
		"SUB_TYPE_ID_FLOWER_OCCUPY_ATTACK":                                     reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_OCCUPY_ATTACK),
		"SUB_TYPE_ID_FLOWER_OCCUPY_BE_ATTACK":                                  reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_OCCUPY_BE_ATTACK),
		"SUB_TYPE_ID_FLOWER_RECV_LIKE":                                         reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_RECV_LIKE),
		"SUB_TYPE_ID_FLOWER_RECV_OCCUPY_AWARD":                                 reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_RECV_OCCUPY_AWARD),
		"SUB_TYPE_ID_FLOWER_RECV_PREVIEW_OCCUPY_AWARD":                         reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_RECV_PREVIEW_OCCUPY_AWARD),
		"SUB_TYPE_ID_FLOWER_REVENGE":                                           reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_REVENGE),
		"SUB_TYPE_ID_FLOWER_SEARCH":                                            reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_SEARCH),
		"SUB_TYPE_ID_FLOWER_SEND_LIKE":                                         reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_SEND_LIKE),
		"SUB_TYPE_ID_FLOWER_SNATCH":                                            reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_SNATCH),
		"SUB_TYPE_ID_FLOWER_SPEED_GROW":                                        reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_SPEED_GROW),
		"SUB_TYPE_ID_FLOWER_START_FEED":                                        reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_START_FEED),
		"SUB_TYPE_ID_FLOWER_START_PLANT":                                       reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_START_PLANT),
		"SUB_TYPE_ID_FLOWER_UPDATE_SLOT":                                       reflect.ValueOf(log.SUB_TYPE_ID_FLOWER_UPDATE_SLOT),
		"SUB_TYPE_ID_FORECAST_RECEIVE_AWARD":                                   reflect.ValueOf(log.SUB_TYPE_ID_FORECAST_RECEIVE_AWARD),
		"SUB_TYPE_ID_FORMATION":                                                reflect.ValueOf(log.SUB_TYPE_ID_FORMATION),
		"SUB_TYPE_ID_FRAGMENT_COMPOSE":                                         reflect.ValueOf(log.SUB_TYPE_ID_FRAGMENT_COMPOSE),
		"SUB_TYPE_ID_FRIEND_ADD":                                               reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_ADD),
		"SUB_TYPE_ID_FRIEND_ADDED":                                             reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_ADDED),
		"SUB_TYPE_ID_FRIEND_BLACKLIST":                                         reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_BLACKLIST),
		"SUB_TYPE_ID_FRIEND_BLACKLISTED":                                       reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_BLACKLISTED),
		"SUB_TYPE_ID_FRIEND_CONFIRM":                                           reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_CONFIRM),
		"SUB_TYPE_ID_FRIEND_DELETE":                                            reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_DELETE),
		"SUB_TYPE_ID_FRIEND_DELETED":                                           reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_DELETED),
		"SUB_TYPE_ID_FRIEND_RECV_LIKE":                                         reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_RECV_LIKE),
		"SUB_TYPE_ID_FRIEND_REMBLACKLIST":                                      reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_REMBLACKLIST),
		"SUB_TYPE_ID_FRIEND_REMOVED_FROM_BLACKLIST":                            reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_REMOVED_FROM_BLACKLIST),
		"SUB_TYPE_ID_FRIEND_SEND_LIKE":                                         reflect.ValueOf(log.SUB_TYPE_ID_FRIEND_SEND_LIKE),
		"SUB_TYPE_ID_GEM_COMPOSE":                                              reflect.ValueOf(log.SUB_TYPE_ID_GEM_COMPOSE),
		"SUB_TYPE_ID_GEM_CONVERT":                                              reflect.ValueOf(log.SUB_TYPE_ID_GEM_CONVERT),
		"SUB_TYPE_ID_GEM_DECOMPOSE":                                            reflect.ValueOf(log.SUB_TYPE_ID_GEM_DECOMPOSE),
		"SUB_TYPE_ID_GEM_WEAR":                                                 reflect.ValueOf(log.SUB_TYPE_ID_GEM_WEAR),
		"SUB_TYPE_ID_GIFT_CODE":                                                reflect.ValueOf(log.SUB_TYPE_ID_GIFT_CODE),
		"SUB_TYPE_ID_GM_DELETE_RESOURCES":                                      reflect.ValueOf(log.SUB_TYPE_ID_GM_DELETE_RESOURCES),
		"SUB_TYPE_ID_GODDESS_CHAPTER_FIGHT":                                    reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_CHAPTER_FIGHT),
		"SUB_TYPE_ID_GODDESS_CHAPTER_FINISH":                                   reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_CHAPTER_FINISH),
		"SUB_TYPE_ID_GODDESS_CHAPTER_TAKE_REWARD":                              reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_CHAPTER_TAKE_REWARD),
		"SUB_TYPE_ID_GODDESS_COLLECTION":                                       reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_COLLECTION),
		"SUB_TYPE_ID_GODDESS_CONTRACT_LEVEL_UP":                                reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_CONTRACT_LEVEL_UP),
		"SUB_TYPE_ID_GODDESS_FEED":                                             reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_FEED),
		"SUB_TYPE_ID_GODDESS_INIT":                                             reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_INIT),
		"SUB_TYPE_ID_GODDESS_LEVEL_UP":                                         reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_LEVEL_UP),
		"SUB_TYPE_ID_GODDESS_STORY_REWARD":                                     reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_STORY_REWARD),
		"SUB_TYPE_ID_GODDESS_TOUCH":                                            reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_TOUCH),
		"SUB_TYPE_ID_GODDESS_UNLOCK_CHAPTER":                                   reflect.ValueOf(log.SUB_TYPE_ID_GODDESS_UNLOCK_CHAPTER),
		"SUB_TYPE_ID_GOD_PRESENT_RECV_AWARDS":                                  reflect.ValueOf(log.SUB_TYPE_ID_GOD_PRESENT_RECV_AWARDS),
		"SUB_TYPE_ID_GOD_PRESENT_RECV_ITEM":                                    reflect.ValueOf(log.SUB_TYPE_ID_GOD_PRESENT_RECV_ITEM),
		"SUB_TYPE_ID_GOD_PRESENT_SUMMON":                                       reflect.ValueOf(log.SUB_TYPE_ID_GOD_PRESENT_SUMMON),
		"SUB_TYPE_ID_GOLD_BUY_GET_GOLD":                                        reflect.ValueOf(log.SUB_TYPE_ID_GOLD_BUY_GET_GOLD),
		"SUB_TYPE_ID_GST_BOSS_AWARD":                                           reflect.ValueOf(log.SUB_TYPE_ID_GST_BOSS_AWARD),
		"SUB_TYPE_ID_GST_BOSS_BUY_CHALLENGE":                                   reflect.ValueOf(log.SUB_TYPE_ID_GST_BOSS_BUY_CHALLENGE),
		"SUB_TYPE_ID_GST_BOSS_FIGHT":                                           reflect.ValueOf(log.SUB_TYPE_ID_GST_BOSS_FIGHT),
		"SUB_TYPE_ID_GST_DRAGON_CULTIVATION":                                   reflect.ValueOf(log.SUB_TYPE_ID_GST_DRAGON_CULTIVATION),
		"SUB_TYPE_ID_GST_DRAGON_FIGHT":                                         reflect.ValueOf(log.SUB_TYPE_ID_GST_DRAGON_FIGHT),
		"SUB_TYPE_ID_GST_DRAGON_SETTLEMENT":                                    reflect.ValueOf(log.SUB_TYPE_ID_GST_DRAGON_SETTLEMENT),
		"SUB_TYPE_ID_GST_DRAGON_TASK_AWARD":                                    reflect.ValueOf(log.SUB_TYPE_ID_GST_DRAGON_TASK_AWARD),
		"SUB_TYPE_ID_GUIDANCE_FINISH_GROUP":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUIDANCE_FINISH_GROUP),
		"SUB_TYPE_ID_GUIDANCE_FINISH_NODE":                                     reflect.ValueOf(log.SUB_TYPE_ID_GUIDANCE_FINISH_NODE),
		"SUB_TYPE_ID_GUIDANCE_SELECT_SKIP_TAG":                                 reflect.ValueOf(log.SUB_TYPE_ID_GUIDANCE_SELECT_SKIP_TAG),
		"SUB_TYPE_ID_GUIDANCE_SKIP":                                            reflect.ValueOf(log.SUB_TYPE_ID_GUIDANCE_SKIP),
		"SUB_TYPE_ID_GUILD_APPLY_RATIFY":                                       reflect.ValueOf(log.SUB_TYPE_ID_GUILD_APPLY_RATIFY),
		"SUB_TYPE_ID_GUILD_CHEST_ACTIVATE":                                     reflect.ValueOf(log.SUB_TYPE_ID_GUILD_CHEST_ACTIVATE),
		"SUB_TYPE_ID_GUILD_CHEST_RECV_AWARD":                                   reflect.ValueOf(log.SUB_TYPE_ID_GUILD_CHEST_RECV_AWARD),
		"SUB_TYPE_ID_GUILD_CHEST_SET_LIKE":                                     reflect.ValueOf(log.SUB_TYPE_ID_GUILD_CHEST_SET_LIKE),
		"SUB_TYPE_ID_GUILD_CREATE":                                             reflect.ValueOf(log.SUB_TYPE_ID_GUILD_CREATE),
		"SUB_TYPE_ID_GUILD_DISBAND":                                            reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DISBAND),
		"SUB_TYPE_ID_GUILD_DONATE":                                             reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DONATE),
		"SUB_TYPE_ID_GUILD_DONATE_RECEIVE":                                     reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DONATE_RECEIVE),
		"SUB_TYPE_ID_GUILD_DUNGEON_AWARD_RECEIVE":                              reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_AWARD_RECEIVE),
		"SUB_TYPE_ID_GUILD_DUNGEON_BUY_CHALLENGE_TIMES":                        reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_BUY_CHALLENGE_TIMES),
		"SUB_TYPE_ID_GUILD_DUNGEON_CHAPTER_RANK_LIKE":                          reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_CHAPTER_RANK_LIKE),
		"SUB_TYPE_ID_GUILD_DUNGEON_FIGHT":                                      reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_FIGHT),
		"SUB_TYPE_ID_GUILD_DUNGEON_NEW_FIGHT":                                  reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_NEW_FIGHT),
		"SUB_TYPE_ID_GUILD_DUNGEON_RECV_BOSS_BOX_AWARD":                        reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_RECV_BOSS_BOX_AWARD),
		"SUB_TYPE_ID_GUILD_DUNGEON_RECV_CHAPTER_TASK_AWARD":                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_RECV_CHAPTER_TASK_AWARD),
		"SUB_TYPE_ID_GUILD_DUNGEON_RECV_SEASON_TOP_DIVISION_AWARD":             reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_RECV_SEASON_TOP_DIVISION_AWARD),
		"SUB_TYPE_ID_GUILD_DUNGEON_USE_STRATEGY":                               reflect.ValueOf(log.SUB_TYPE_ID_GUILD_DUNGEON_USE_STRATEGY),
		"SUB_TYPE_ID_GUILD_MANAGE":                                             reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MANAGE),
		"SUB_TYPE_ID_GUILD_MANAGER_MEMBER":                                     reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MANAGER_MEMBER),
		"SUB_TYPE_ID_GUILD_MOB_ACCEPT_TASK":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MOB_ACCEPT_TASK),
		"SUB_TYPE_ID_GUILD_MOB_BUY_TIMES":                                      reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MOB_BUY_TIMES),
		"SUB_TYPE_ID_GUILD_MOB_GET_SCORE":                                      reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MOB_GET_SCORE),
		"SUB_TYPE_ID_GUILD_MODIFY_INFO":                                        reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MODIFY_INFO),
		"SUB_TYPE_ID_GUILD_MODIFY_NOTICE":                                      reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MODIFY_NOTICE),
		"SUB_TYPE_ID_GUILD_MODIFY_TEXT_INFO":                                   reflect.ValueOf(log.SUB_TYPE_ID_GUILD_MODIFY_TEXT_INFO),
		"SUB_TYPE_ID_GUILD_POSITION_CHANGE":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_POSITION_CHANGE),
		"SUB_TYPE_ID_GUILD_QUIT":                                               reflect.ValueOf(log.SUB_TYPE_ID_GUILD_QUIT),
		"SUB_TYPE_ID_GUILD_RECRUIT":                                            reflect.ValueOf(log.SUB_TYPE_ID_GUILD_RECRUIT),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BALANCE":                                 reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BALANCE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS":                                   reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS_CHANGE":                            reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS_CHANGE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BOX_RECV":                                reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BOX_RECV),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DISPATCH":                          reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DISPATCH),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DONATE":                            reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DONATE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_TASK":                              reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_TASK),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_FIGHT":                         reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_FIGHT),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_STREAK":                        reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_STREAK),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_TASK_AWARD":                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_TASK_AWARD),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_DISPATCH":                                reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_DISPATCH),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_DONATE":                                  reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_DONATE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_FIGHT":                                   reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_FIGHT),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_GUILD_SCORE":                             reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_GUILD_SCORE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_HOST":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_HOST),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_MODIFY_NOTICE":                           reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_MODIFY_NOTICE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_OCCUPATION":                              reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_OCCUPATION),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_FIGHT":                               reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_FIGHT),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_RESOURCE_CHANGE":                     reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_RESOURCE_CHANGE),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_REORDER":                                 reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_REORDER),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_STREAK":                                  reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_STREAK),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_TASK":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_TASK),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_LEVEL_UP":                           reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_LEVEL_UP),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_TASK_AWARD":                         reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_TASK_AWARD),
		"SUB_TYPE_ID_GUILD_SAND_TABLE_VOTE":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SAND_TABLE_VOTE),
		"SUB_TYPE_ID_GUILD_SEASON_FINAL_DIVISION":                              reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SEASON_FINAL_DIVISION),
		"SUB_TYPE_ID_GUILD_SEND_MAIL":                                          reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SEND_MAIL),
		"SUB_TYPE_ID_GUILD_SET_NAME":                                           reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SET_NAME),
		"SUB_TYPE_ID_GUILD_SIGN_IN":                                            reflect.ValueOf(log.SUB_TYPE_ID_GUILD_SIGN_IN),
		"SUB_TYPE_ID_GUILD_TALENT_LEVEL_UP":                                    reflect.ValueOf(log.SUB_TYPE_ID_GUILD_TALENT_LEVEL_UP),
		"SUB_TYPE_ID_GUILD_TALENT_RESET":                                       reflect.ValueOf(log.SUB_TYPE_ID_GUILD_TALENT_RESET),
		"SUB_TYPE_ID_GUILD_USER_APPLY":                                         reflect.ValueOf(log.SUB_TYPE_ID_GUILD_USER_APPLY),
		"SUB_TYPE_ID_GUILD_USER_JOIN_OR_QUIT":                                  reflect.ValueOf(log.SUB_TYPE_ID_GUILD_USER_JOIN_OR_QUIT),
		"SUB_TYPE_ID_HANDBOOKS_ACTIVE":                                         reflect.ValueOf(log.SUB_TYPE_ID_HANDBOOKS_ACTIVE),
		"SUB_TYPE_ID_HANDBOOKS_ACTIVE_HERO_ATTR":                               reflect.ValueOf(log.SUB_TYPE_ID_HANDBOOKS_ACTIVE_HERO_ATTR),
		"SUB_TYPE_ID_HANDBOOKS_ADD":                                            reflect.ValueOf(log.SUB_TYPE_ID_HANDBOOKS_ADD),
		"SUB_TYPE_ID_HANDBOOKS_RECV_AWARD":                                     reflect.ValueOf(log.SUB_TYPE_ID_HANDBOOKS_RECV_AWARD),
		"SUB_TYPE_ID_HERO_AWAKEN":                                              reflect.ValueOf(log.SUB_TYPE_ID_HERO_AWAKEN),
		"SUB_TYPE_ID_HERO_BACK":                                                reflect.ValueOf(log.SUB_TYPE_ID_HERO_BACK),
		"SUB_TYPE_ID_HERO_BUY_SLOT":                                            reflect.ValueOf(log.SUB_TYPE_ID_HERO_BUY_SLOT),
		"SUB_TYPE_ID_HERO_CHANGE_RANDOM":                                       reflect.ValueOf(log.SUB_TYPE_ID_HERO_CHANGE_RANDOM),
		"SUB_TYPE_ID_HERO_CHANGE_SAVE":                                         reflect.ValueOf(log.SUB_TYPE_ID_HERO_CHANGE_SAVE),
		"SUB_TYPE_ID_HERO_CONVERSION":                                          reflect.ValueOf(log.SUB_TYPE_ID_HERO_CONVERSION),
		"SUB_TYPE_ID_HERO_CONVERT":                                             reflect.ValueOf(log.SUB_TYPE_ID_HERO_CONVERT),
		"SUB_TYPE_ID_HERO_DECOMPOSE":                                           reflect.ValueOf(log.SUB_TYPE_ID_HERO_DECOMPOSE),
		"SUB_TYPE_ID_HERO_EMBLEM_SKILL":                                        reflect.ValueOf(log.SUB_TYPE_ID_HERO_EMBLEM_SKILL),
		"SUB_TYPE_ID_HERO_EXCHANGE":                                            reflect.ValueOf(log.SUB_TYPE_ID_HERO_EXCHANGE),
		"SUB_TYPE_ID_HERO_GEM_LEVEL_UP":                                        reflect.ValueOf(log.SUB_TYPE_ID_HERO_GEM_LEVEL_UP),
		"SUB_TYPE_ID_HERO_LEVEL_UP":                                            reflect.ValueOf(log.SUB_TYPE_ID_HERO_LEVEL_UP),
		"SUB_TYPE_ID_HERO_REVIVE":                                              reflect.ValueOf(log.SUB_TYPE_ID_HERO_REVIVE),
		"SUB_TYPE_ID_HERO_STAGE_UP":                                            reflect.ValueOf(log.SUB_TYPE_ID_HERO_STAGE_UP),
		"SUB_TYPE_ID_HERO_STAR_UP":                                             reflect.ValueOf(log.SUB_TYPE_ID_HERO_STAR_UP),
		"SUB_TYPE_ID_HERO_TAG_UPDATE":                                          reflect.ValueOf(log.SUB_TYPE_ID_HERO_TAG_UPDATE),
		"SUB_TYPE_ID_HERO_UPDATE_LOCK_STATUS":                                  reflect.ValueOf(log.SUB_TYPE_ID_HERO_UPDATE_LOCK_STATUS),
		"SUB_TYPE_ID_ITEM_SELECT":                                              reflect.ValueOf(log.SUB_TYPE_ID_ITEM_SELECT),
		"SUB_TYPE_ID_ITEM_SELL":                                                reflect.ValueOf(log.SUB_TYPE_ID_ITEM_SELL),
		"SUB_TYPE_ID_ITEM_USE":                                                 reflect.ValueOf(log.SUB_TYPE_ID_ITEM_USE),
		"SUB_TYPE_ID_LOGIN_INIT":                                               reflect.ValueOf(log.SUB_TYPE_ID_LOGIN_INIT),
		"SUB_TYPE_ID_LOGIN_SUCCESS":                                            reflect.ValueOf(log.SUB_TYPE_ID_LOGIN_SUCCESS),
		"SUB_TYPE_ID_MAZE_BUY_REVIVE":                                          reflect.ValueOf(log.SUB_TYPE_ID_MAZE_BUY_REVIVE),
		"SUB_TYPE_ID_MAZE_FIGHT":                                               reflect.ValueOf(log.SUB_TYPE_ID_MAZE_FIGHT),
		"SUB_TYPE_ID_MAZE_GET_GRID":                                            reflect.ValueOf(log.SUB_TYPE_ID_MAZE_GET_GRID),
		"SUB_TYPE_ID_MAZE_GET_MAP":                                             reflect.ValueOf(log.SUB_TYPE_ID_MAZE_GET_MAP),
		"SUB_TYPE_ID_MAZE_RECOVERY_HERO":                                       reflect.ValueOf(log.SUB_TYPE_ID_MAZE_RECOVERY_HERO),
		"SUB_TYPE_ID_MAZE_SELECT_BUFF":                                         reflect.ValueOf(log.SUB_TYPE_ID_MAZE_SELECT_BUFF),
		"SUB_TYPE_ID_MAZE_SWEEP":                                               reflect.ValueOf(log.SUB_TYPE_ID_MAZE_SWEEP),
		"SUB_TYPE_ID_MAZE_TASK_RECEIVE_AWARD":                                  reflect.ValueOf(log.SUB_TYPE_ID_MAZE_TASK_RECEIVE_AWARD),
		"SUB_TYPE_ID_MAZE_TRIGGER_EVENT":                                       reflect.ValueOf(log.SUB_TYPE_ID_MAZE_TRIGGER_EVENT),
		"SUB_TYPE_ID_MAZE_USE_ITEM":                                            reflect.ValueOf(log.SUB_TYPE_ID_MAZE_USE_ITEM),
		"SUB_TYPE_ID_MEDAL_RECEIVE_AWARD":                                      reflect.ValueOf(log.SUB_TYPE_ID_MEDAL_RECEIVE_AWARD),
		"SUB_TYPE_ID_MEMORY_CHIP_UNLOCK":                                       reflect.ValueOf(log.SUB_TYPE_ID_MEMORY_CHIP_UNLOCK),
		"SUB_TYPE_ID_MIRAGE_FIGHT":                                             reflect.ValueOf(log.SUB_TYPE_ID_MIRAGE_FIGHT),
		"SUB_TYPE_ID_MIRAGE_POWER_CRUSH":                                       reflect.ValueOf(log.SUB_TYPE_ID_MIRAGE_POWER_CRUSH),
		"SUB_TYPE_ID_MIRAGE_RECEIVE_AWARD":                                     reflect.ValueOf(log.SUB_TYPE_ID_MIRAGE_RECEIVE_AWARD),
		"SUB_TYPE_ID_MIRAGE_SAVE_AFFIXES":                                      reflect.ValueOf(log.SUB_TYPE_ID_MIRAGE_SAVE_AFFIXES),
		"SUB_TYPE_ID_MIRAGE_SWEEP":                                             reflect.ValueOf(log.SUB_TYPE_ID_MIRAGE_SWEEP),
		"SUB_TYPE_ID_MONITOR":                                                  reflect.ValueOf(log.SUB_TYPE_ID_MONITOR),
		"SUB_TYPE_ID_MONTHLY_CARD_RECEIVE_AWARD":                               reflect.ValueOf(log.SUB_TYPE_ID_MONTHLY_CARD_RECEIVE_AWARD),
		"SUB_TYPE_ID_MONTHLY_CARD_RECHARGE":                                    reflect.ValueOf(log.SUB_TYPE_ID_MONTHLY_CARD_RECHARGE),
		"SUB_TYPE_ID_MONTH_TASKS_RECV_AWARD":                                   reflect.ValueOf(log.SUB_TYPE_ID_MONTH_TASKS_RECV_AWARD),
		"SUB_TYPE_ID_MUTE_ACCOUNT":                                             reflect.ValueOf(log.SUB_TYPE_ID_MUTE_ACCOUNT),
		"SUB_TYPE_ID_NEW_YEAR_ACTIVITY_STORY_LOGIN_AWARD":                      reflect.ValueOf(log.SUB_TYPE_ID_NEW_YEAR_ACTIVITY_STORY_LOGIN_AWARD),
		"SUB_TYPE_ID_NONE":                                                     reflect.ValueOf(log.SUB_TYPE_ID_NONE),
		"SUB_TYPE_ID_OPERATE_ACTIVITY_GIFT_INTI":                               reflect.ValueOf(log.SUB_TYPE_ID_OPERATE_ACTIVITY_GIFT_INTI),
		"SUB_TYPE_ID_OPERATE_ACTIVITY_TASK_INIT":                               reflect.ValueOf(log.SUB_TYPE_ID_OPERATE_ACTIVITY_TASK_INIT),
		"SUB_TYPE_ID_OPERATE_GIFT_RECHARGE":                                    reflect.ValueOf(log.SUB_TYPE_ID_OPERATE_GIFT_RECHARGE),
		"SUB_TYPE_ID_OPERATE_TASK_RECEIVED":                                    reflect.ValueOf(log.SUB_TYPE_ID_OPERATE_TASK_RECEIVED),
		"SUB_TYPE_ID_ORDER_PROCESS":                                            reflect.ValueOf(log.SUB_TYPE_ID_ORDER_PROCESS),
		"SUB_TYPE_ID_ORDER_REFUND":                                             reflect.ValueOf(log.SUB_TYPE_ID_ORDER_REFUND),
		"SUB_TYPE_ID_PASS_LEVEL_BUY":                                           reflect.ValueOf(log.SUB_TYPE_ID_PASS_LEVEL_BUY),
		"SUB_TYPE_ID_PASS_RECEIVE_AWARD":                                       reflect.ValueOf(log.SUB_TYPE_ID_PASS_RECEIVE_AWARD),
		"SUB_TYPE_ID_PASS_RECHARGE":                                            reflect.ValueOf(log.SUB_TYPE_ID_PASS_RECHARGE),
		"SUB_TYPE_ID_PEAK_DO_GUESS":                                            reflect.ValueOf(log.SUB_TYPE_ID_PEAK_DO_GUESS),
		"SUB_TYPE_ID_PEAK_FIGHT":                                               reflect.ValueOf(log.SUB_TYPE_ID_PEAK_FIGHT),
		"SUB_TYPE_ID_PEAK_FIGHTER_RANK_CHANGE":                                 reflect.ValueOf(log.SUB_TYPE_ID_PEAK_FIGHTER_RANK_CHANGE),
		"SUB_TYPE_ID_PEAK_RECV_INVITE_REWARD":                                  reflect.ValueOf(log.SUB_TYPE_ID_PEAK_RECV_INVITE_REWARD),
		"SUB_TYPE_ID_PEAK_WORSHIP":                                             reflect.ValueOf(log.SUB_TYPE_ID_PEAK_WORSHIP),
		"SUB_TYPE_ID_PRE_SEASON_RECV":                                          reflect.ValueOf(log.SUB_TYPE_ID_PRE_SEASON_RECV),
		"SUB_TYPE_ID_PROMOTION_GIFT_INIT":                                      reflect.ValueOf(log.SUB_TYPE_ID_PROMOTION_GIFT_INIT),
		"SUB_TYPE_ID_PROMOTION_GIFT_RECHARGE":                                  reflect.ValueOf(log.SUB_TYPE_ID_PROMOTION_GIFT_RECHARGE),
		"SUB_TYPE_ID_PROMOTION_GIFT_SELECT_AWARD":                              reflect.ValueOf(log.SUB_TYPE_ID_PROMOTION_GIFT_SELECT_AWARD),
		"SUB_TYPE_ID_PUSH_GIFT_CREATE":                                         reflect.ValueOf(log.SUB_TYPE_ID_PUSH_GIFT_CREATE),
		"SUB_TYPE_ID_PUSH_GIFT_DEL":                                            reflect.ValueOf(log.SUB_TYPE_ID_PUSH_GIFT_DEL),
		"SUB_TYPE_ID_PUSH_GIFT_RECHARGE":                                       reflect.ValueOf(log.SUB_TYPE_ID_PUSH_GIFT_RECHARGE),
		"SUB_TYPE_ID_PYRAMID_CHOOSE_AWARD":                                     reflect.ValueOf(log.SUB_TYPE_ID_PYRAMID_CHOOSE_AWARD),
		"SUB_TYPE_ID_PYRAMID_DRAW":                                             reflect.ValueOf(log.SUB_TYPE_ID_PYRAMID_DRAW),
		"SUB_TYPE_ID_PYRAMID_TASK_AWARD":                                       reflect.ValueOf(log.SUB_TYPE_ID_PYRAMID_TASK_AWARD),
		"SUB_TYPE_ID_QUESTIONNAIRE_FINISH":                                     reflect.ValueOf(log.SUB_TYPE_ID_QUESTIONNAIRE_FINISH),
		"SUB_TYPE_ID_RANK_ACHIEVE_RECV_AWARD":                                  reflect.ValueOf(log.SUB_TYPE_ID_RANK_ACHIEVE_RECV_AWARD),
		"SUB_TYPE_ID_RATE_SCORE":                                               reflect.ValueOf(log.SUB_TYPE_ID_RATE_SCORE),
		"SUB_TYPE_ID_READ_MAIL":                                                reflect.ValueOf(log.SUB_TYPE_ID_READ_MAIL),
		"SUB_TYPE_ID_RECHARGE_COUPON":                                          reflect.ValueOf(log.SUB_TYPE_ID_RECHARGE_COUPON),
		"SUB_TYPE_ID_RECHARGE_FIRST_GIFT":                                      reflect.ValueOf(log.SUB_TYPE_ID_RECHARGE_FIRST_GIFT),
		"SUB_TYPE_ID_RECHARGE_NORMAL":                                          reflect.ValueOf(log.SUB_TYPE_ID_RECHARGE_NORMAL),
		"SUB_TYPE_ID_RECHARGE_WEB_DIAMOND":                                     reflect.ValueOf(log.SUB_TYPE_ID_RECHARGE_WEB_DIAMOND),
		"SUB_TYPE_ID_RECHARGE_WEB_GIFT":                                        reflect.ValueOf(log.SUB_TYPE_ID_RECHARGE_WEB_GIFT),
		"SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL":                                  reflect.ValueOf(log.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL),
		"SUB_TYPE_ID_RECV_H5_DESKTOP_REWARD":                                   reflect.ValueOf(log.SUB_TYPE_ID_RECV_H5_DESKTOP_REWARD),
		"SUB_TYPE_ID_RECV_SHARE_AWARD":                                         reflect.ValueOf(log.SUB_TYPE_ID_RECV_SHARE_AWARD),
		"SUB_TYPE_ID_REMAIN_BOOK_LEVEL_UP":                                     reflect.ValueOf(log.SUB_TYPE_ID_REMAIN_BOOK_LEVEL_UP),
		"SUB_TYPE_ID_REMAIN_BOOK_RECV_EXP":                                     reflect.ValueOf(log.SUB_TYPE_ID_REMAIN_BOOK_RECV_EXP),
		"SUB_TYPE_ID_REMAIN_STAR_UP":                                           reflect.ValueOf(log.SUB_TYPE_ID_REMAIN_STAR_UP),
		"SUB_TYPE_ID_RITE_MARK_COLLECTION":                                     reflect.ValueOf(log.SUB_TYPE_ID_RITE_MARK_COLLECTION),
		"SUB_TYPE_ID_RITE_RECYCLE":                                             reflect.ValueOf(log.SUB_TYPE_ID_RITE_RECYCLE),
		"SUB_TYPE_ID_ROUND_ACTIVITY_RECV_TASK_AWARD":                           reflect.ValueOf(log.SUB_TYPE_ID_ROUND_ACTIVITY_RECV_TASK_AWARD),
		"SUB_TYPE_ID_SEASON_ARENA_BE_FIGHT":                                    reflect.ValueOf(log.SUB_TYPE_ID_SEASON_ARENA_BE_FIGHT),
		"SUB_TYPE_ID_SEASON_ARENA_DIVISION_CHANGE":                             reflect.ValueOf(log.SUB_TYPE_ID_SEASON_ARENA_DIVISION_CHANGE),
		"SUB_TYPE_ID_SEASON_ARENA_FIGHT":                                       reflect.ValueOf(log.SUB_TYPE_ID_SEASON_ARENA_FIGHT),
		"SUB_TYPE_ID_SEASON_ARENA_RECV_DIVISION_AWARD":                         reflect.ValueOf(log.SUB_TYPE_ID_SEASON_ARENA_RECV_DIVISION_AWARD),
		"SUB_TYPE_ID_SEASON_ARENA_RECV_TASK_AWARD":                             reflect.ValueOf(log.SUB_TYPE_ID_SEASON_ARENA_RECV_TASK_AWARD),
		"SUB_TYPE_ID_SEASON_COMPLIANCE_SCORE_CHANGE":                           reflect.ValueOf(log.SUB_TYPE_ID_SEASON_COMPLIANCE_SCORE_CHANGE),
		"SUB_TYPE_ID_SEASON_DOOR_FIGHT":                                        reflect.ValueOf(log.SUB_TYPE_ID_SEASON_DOOR_FIGHT),
		"SUB_TYPE_ID_SEASON_DOOR_TASK_REWARD":                                  reflect.ValueOf(log.SUB_TYPE_ID_SEASON_DOOR_TASK_REWARD),
		"SUB_TYPE_ID_SEASON_DUNGEON_FIGHT":                                     reflect.ValueOf(log.SUB_TYPE_ID_SEASON_DUNGEON_FIGHT),
		"SUB_TYPE_ID_SEASON_DUNGEON_RECV":                                      reflect.ValueOf(log.SUB_TYPE_ID_SEASON_DUNGEON_RECV),
		"SUB_TYPE_ID_SEASON_ENTER":                                             reflect.ValueOf(log.SUB_TYPE_ID_SEASON_ENTER),
		"SUB_TYPE_ID_SEASON_JEWELRY_DECOMPOSE":                                 reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_DECOMPOSE),
		"SUB_TYPE_ID_SEASON_JEWELRY_GET":                                       reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_GET),
		"SUB_TYPE_ID_SEASON_JEWELRY_RECYCLE":                                   reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_RECYCLE),
		"SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE":                              reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE),
		"SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE_CONFIRM":                      reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE_CONFIRM),
		"SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CLASS_UP":                            reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CLASS_UP),
		"SUB_TYPE_ID_SEASON_JEWELRY_SKILL_LEVEL_UP":                            reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_LEVEL_UP),
		"SUB_TYPE_ID_SEASON_JEWELRY_WEAR":                                      reflect.ValueOf(log.SUB_TYPE_ID_SEASON_JEWELRY_WEAR),
		"SUB_TYPE_ID_SEASON_LEVEL_RECV_LV_AWARDS":                              reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LEVEL_RECV_LV_AWARDS),
		"SUB_TYPE_ID_SEASON_LEVEL_RECV_TASK_AWARDS":                            reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LEVEL_RECV_TASK_AWARDS),
		"SUB_TYPE_ID_SEASON_LEVEL_UP":                                          reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LEVEL_UP),
		"SUB_TYPE_ID_SEASON_LINK_ACTIVATION":                                   reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LINK_ACTIVATION),
		"SUB_TYPE_ID_SEASON_LINK_MONUMENT_CULTIVATION":                         reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LINK_MONUMENT_CULTIVATION),
		"SUB_TYPE_ID_SEASON_LINK_MONUMENT_RECV_RARE_AWARDS":                    reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LINK_MONUMENT_RECV_RARE_AWARDS),
		"SUB_TYPE_ID_SEASON_LINK_RECYCLE":                                      reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LINK_RECYCLE),
		"SUB_TYPE_ID_SEASON_LINK_RUNE_COLLECTION":                              reflect.ValueOf(log.SUB_TYPE_ID_SEASON_LINK_RUNE_COLLECTION),
		"SUB_TYPE_ID_SEASON_MAP_FIGHT":                                         reflect.ValueOf(log.SUB_TYPE_ID_SEASON_MAP_FIGHT),
		"SUB_TYPE_ID_SEASON_MAP_TASK_AWARD":                                    reflect.ValueOf(log.SUB_TYPE_ID_SEASON_MAP_TASK_AWARD),
		"SUB_TYPE_ID_SEASON_RETURN_ADD_AWARDS":                                 reflect.ValueOf(log.SUB_TYPE_ID_SEASON_RETURN_ADD_AWARDS),
		"SUB_TYPE_ID_SEASON_RETURN_TAKE_AWARDS":                                reflect.ValueOf(log.SUB_TYPE_ID_SEASON_RETURN_TAKE_AWARDS),
		"SUB_TYPE_ID_SELECT_SUMMON_SUMMON":                                     reflect.ValueOf(log.SUB_TYPE_ID_SELECT_SUMMON_SUMMON),
		"SUB_TYPE_ID_SET_ACCOUNT_TAG":                                          reflect.ValueOf(log.SUB_TYPE_ID_SET_ACCOUNT_TAG),
		"SUB_TYPE_ID_SET_ICON":                                                 reflect.ValueOf(log.SUB_TYPE_ID_SET_ICON),
		"SUB_TYPE_ID_SET_NAME":                                                 reflect.ValueOf(log.SUB_TYPE_ID_SET_NAME),
		"SUB_TYPE_ID_SEVENDAY_LOGIN_TAKEAWARD":                                 reflect.ValueOf(log.SUB_TYPE_ID_SEVENDAY_LOGIN_TAKEAWARD),
		"SUB_TYPE_ID_SHOP_BUY":                                                 reflect.ValueOf(log.SUB_TYPE_ID_SHOP_BUY),
		"SUB_TYPE_ID_SHOP_REFRESH":                                             reflect.ValueOf(log.SUB_TYPE_ID_SHOP_REFRESH),
		"SUB_TYPE_ID_SKIN_USE":                                                 reflect.ValueOf(log.SUB_TYPE_ID_SKIN_USE),
		"SUB_TYPE_ID_SNAPSHOT_ARTIFACT_WM":                                     reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_ARTIFACT_WM),
		"SUB_TYPE_ID_SNAPSHOT_BAG_WM":                                          reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_BAG_WM),
		"SUB_TYPE_ID_SNAPSHOT_END_WM":                                          reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_END_WM),
		"SUB_TYPE_ID_SNAPSHOT_FOREST_WM":                                       reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_FOREST_WM),
		"SUB_TYPE_ID_SNAPSHOT_FORMATION_WM":                                    reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_FORMATION_WM),
		"SUB_TYPE_ID_SNAPSHOT_FRIEND_WM":                                       reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_FRIEND_WM),
		"SUB_TYPE_ID_SNAPSHOT_GST_GUILD_WM":                                    reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_GST_GUILD_WM),
		"SUB_TYPE_ID_SNAPSHOT_GUILD_WM":                                        reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_GUILD_WM),
		"SUB_TYPE_ID_SNAPSHOT_HERO_WM":                                         reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_HERO_WM),
		"SUB_TYPE_ID_SNAPSHOT_MIN":                                             reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_MIN),
		"SUB_TYPE_ID_SNAPSHOT_PEAK_SEASON_RANK_WM":                             reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_PEAK_SEASON_RANK_WM),
		"SUB_TYPE_ID_SNAPSHOT_USER_WM":                                         reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_USER_WM),
		"SUB_TYPE_ID_SNAPSHOT_WORLD_BOSS_RANK_WM":                              reflect.ValueOf(log.SUB_TYPE_ID_SNAPSHOT_WORLD_BOSS_RANK_WM),
		"SUB_TYPE_ID_STORY_REVIEW_UNLOCK":                                      reflect.ValueOf(log.SUB_TYPE_ID_STORY_REVIEW_UNLOCK),
		"SUB_TYPE_ID_SUMMON":                                                   reflect.ValueOf(log.SUB_TYPE_ID_SUMMON),
		"SUB_TYPE_ID_SUMMON_SET_WISH_LIST":                                     reflect.ValueOf(log.SUB_TYPE_ID_SUMMON_SET_WISH_LIST),
		"SUB_TYPE_ID_TALENT_TREE_LEVEL_UP":                                     reflect.ValueOf(log.SUB_TYPE_ID_TALENT_TREE_LEVEL_UP),
		"SUB_TYPE_ID_TALENT_TREE_RANK_UPDATE":                                  reflect.ValueOf(log.SUB_TYPE_ID_TALENT_TREE_RANK_UPDATE),
		"SUB_TYPE_ID_TALENT_TREE_RECEIVE_TASK_AWARDS":                          reflect.ValueOf(log.SUB_TYPE_ID_TALENT_TREE_RECEIVE_TASK_AWARDS),
		"SUB_TYPE_ID_TALENT_TREE_RESET":                                        reflect.ValueOf(log.SUB_TYPE_ID_TALENT_TREE_RESET),
		"SUB_TYPE_ID_TALES_CHAPTER_FIGHT":                                      reflect.ValueOf(log.SUB_TYPE_ID_TALES_CHAPTER_FIGHT),
		"SUB_TYPE_ID_TALES_CHAPTER_FINISH":                                     reflect.ValueOf(log.SUB_TYPE_ID_TALES_CHAPTER_FINISH),
		"SUB_TYPE_ID_TALES_CHAPTER_TAKE_REWARD":                                reflect.ValueOf(log.SUB_TYPE_ID_TALES_CHAPTER_TAKE_REWARD),
		"SUB_TYPE_ID_TALES_ELITE_FIGHT":                                        reflect.ValueOf(log.SUB_TYPE_ID_TALES_ELITE_FIGHT),
		"SUB_TYPE_ID_TALES_ELITE_WIPE":                                         reflect.ValueOf(log.SUB_TYPE_ID_TALES_ELITE_WIPE),
		"SUB_TYPE_ID_TASK_RECEIVE_AWARD":                                       reflect.ValueOf(log.SUB_TYPE_ID_TASK_RECEIVE_AWARD),
		"SUB_TYPE_ID_TITLE_ADD":                                                reflect.ValueOf(log.SUB_TYPE_ID_TITLE_ADD),
		"SUB_TYPE_ID_TOWERSTAR_FIGHT":                                          reflect.ValueOf(log.SUB_TYPE_ID_TOWERSTAR_FIGHT),
		"SUB_TYPE_ID_TOWER_FIGHT":                                              reflect.ValueOf(log.SUB_TYPE_ID_TOWER_FIGHT),
		"SUB_TYPE_ID_TOWER_JUMP":                                               reflect.ValueOf(log.SUB_TYPE_ID_TOWER_JUMP),
		"SUB_TYPE_ID_TOWER_SEASON_FIGHT":                                       reflect.ValueOf(log.SUB_TYPE_ID_TOWER_SEASON_FIGHT),
		"SUB_TYPE_ID_TOWER_SEASON_RECV_TASK_AWARD":                             reflect.ValueOf(log.SUB_TYPE_ID_TOWER_SEASON_RECV_TASK_AWARD),
		"SUB_TYPE_ID_TOWER_SWEEP":                                              reflect.ValueOf(log.SUB_TYPE_ID_TOWER_SWEEP),
		"SUB_TYPE_ID_TRACE":                                                    reflect.ValueOf(log.SUB_TYPE_ID_TRACE),
		"SUB_TYPE_ID_TRIAL_FIGHT":                                              reflect.ValueOf(log.SUB_TYPE_ID_TRIAL_FIGHT),
		"SUB_TYPE_ID_TRIAL_ONHOOK_REWARD":                                      reflect.ValueOf(log.SUB_TYPE_ID_TRIAL_ONHOOK_REWARD),
		"SUB_TYPE_ID_TRIAL_SWEEP":                                              reflect.ValueOf(log.SUB_TYPE_ID_TRIAL_SWEEP),
		"SUB_TYPE_ID_VIP_BUY_GIFT":                                             reflect.ValueOf(log.SUB_TYPE_ID_VIP_BUY_GIFT),
		"SUB_TYPE_ID_VIP_RECHARGE_GIFT":                                        reflect.ValueOf(log.SUB_TYPE_ID_VIP_RECHARGE_GIFT),
		"SUB_TYPE_ID_VIP_UP":                                                   reflect.ValueOf(log.SUB_TYPE_ID_VIP_UP),
		"SUB_TYPE_ID_WM_ONLINE":                                                reflect.ValueOf(log.SUB_TYPE_ID_WM_ONLINE),
		"SUB_TYPE_ID_WORLD_BOSS_FIGHT":                                         reflect.ValueOf(log.SUB_TYPE_ID_WORLD_BOSS_FIGHT),
		"SUB_TYPE_ID_WORLD_BOSS_SELECT_LEVEL":                                  reflect.ValueOf(log.SUB_TYPE_ID_WORLD_BOSS_SELECT_LEVEL),
		"SUB_TYPE_ID_WORLD_BOSS_TASK_RECV_AWARD":                               reflect.ValueOf(log.SUB_TYPE_ID_WORLD_BOSS_TASK_RECV_AWARD),
		"SUB_TYPE_ID_WRESTLE_BE_FIGHT":                                         reflect.ValueOf(log.SUB_TYPE_ID_WRESTLE_BE_FIGHT),
		"SUB_TYPE_ID_WRESTLE_CHANGE_ROOM":                                      reflect.ValueOf(log.SUB_TYPE_ID_WRESTLE_CHANGE_ROOM),
		"SUB_TYPE_ID_WRESTLE_FIGHT":                                            reflect.ValueOf(log.SUB_TYPE_ID_WRESTLE_FIGHT),
		"SUB_TYPE_ID_WRESTLE_LIKE":                                             reflect.ValueOf(log.SUB_TYPE_ID_WRESTLE_LIKE),
		"SUB_TYPE_ID_WRESTLE_RECV_LEVEL_AWARD":                                 reflect.ValueOf(log.SUB_TYPE_ID_WRESTLE_RECV_LEVEL_AWARD),
		"SUB_TYPE_name":                                                        reflect.ValueOf(&log.SUB_TYPE_name).Elem(),
		"SUB_TYPE_value":                                                       reflect.ValueOf(&log.SUB_TYPE_value).Elem(),

		// type definitions
		"ArenaDivisionTaskAward":    reflect.ValueOf((*log.ArenaDivisionTaskAward)(nil)),
		"Artifact":                  reflect.ValueOf((*log.Artifact)(nil)),
		"ArtifactDebutSummon":       reflect.ValueOf((*log.ArtifactDebutSummon)(nil)),
		"ArtifactSnapshotForWm":     reflect.ValueOf((*log.ArtifactSnapshotForWm)(nil)),
		"AttrStr":                   reflect.ValueOf((*log.AttrStr)(nil)),
		"BOSS_RUSH_AWARD_TYPE":      reflect.ValueOf((*log.BOSS_RUSH_AWARD_TYPE)(nil)),
		"BagSnapshotForWm":          reflect.ValueOf((*log.BagSnapshotForWm)(nil)),
		"CharDataBag":               reflect.ValueOf((*log.CharDataBag)(nil)),
		"CultivateScoreStr":         reflect.ValueOf((*log.CultivateScoreStr)(nil)),
		"DisorderLandSnapshot":      reflect.ValueOf((*log.DisorderLandSnapshot)(nil)),
		"ESGuildInfo":               reflect.ValueOf((*log.ESGuildInfo)(nil)),
		"ESLogHandlerData":          reflect.ValueOf((*log.ESLogHandlerData)(nil)),
		"ESUserInfo":                reflect.ValueOf((*log.ESUserInfo)(nil)),
		"ES_LOG_INDEX":              reflect.ValueOf((*log.ES_LOG_INDEX)(nil)),
		"Emblem":                    reflect.ValueOf((*log.Emblem)(nil)),
		"EmblemSnapshot":            reflect.ValueOf((*log.EmblemSnapshot)(nil)),
		"EmblemSuccinctLog":         reflect.ValueOf((*log.EmblemSuccinctLog)(nil)),
		"EquipSnapshot":             reflect.ValueOf((*log.EquipSnapshot)(nil)),
		"EquipStrength":             reflect.ValueOf((*log.EquipStrength)(nil)),
		"EquipmentStr":              reflect.ValueOf((*log.EquipmentStr)(nil)),
		"FType1":                    reflect.ValueOf((*log.FType1)(nil)),
		"FType2":                    reflect.ValueOf((*log.FType2)(nil)),
		"FType3And4":                reflect.ValueOf((*log.FType3And4)(nil)),
		"Formation":                 reflect.ValueOf((*log.Formation)(nil)),
		"FormationInfoStr":          reflect.ValueOf((*log.FormationInfoStr)(nil)),
		"FormationSnapshotForWm":    reflect.ValueOf((*log.FormationSnapshotForWm)(nil)),
		"GSTDispatchTeam":           reflect.ValueOf((*log.GSTDispatchTeam)(nil)),
		"GSTFightExt":               reflect.ValueOf((*log.GSTFightExt)(nil)),
		"GSTGoddessBlessInfo":       reflect.ValueOf((*log.GSTGoddessBlessInfo)(nil)),
		"GSTGroundInfo":             reflect.ValueOf((*log.GSTGroundInfo)(nil)),
		"GSTGuildUserScore":         reflect.ValueOf((*log.GSTGuildUserScore)(nil)),
		"GSTGuildUsersScore":        reflect.ValueOf((*log.GSTGuildUsersScore)(nil)),
		"GSTReorderTeam":            reflect.ValueOf((*log.GSTReorderTeam)(nil)),
		"GUILD_LOG_MANAGE":          reflect.ValueOf((*log.GUILD_LOG_MANAGE)(nil)),
		"GUILD_LOG_TEXT_INFO":       reflect.ValueOf((*log.GUILD_LOG_TEXT_INFO)(nil)),
		"GUILD_LOG_USER":            reflect.ValueOf((*log.GUILD_LOG_USER)(nil)),
		"GUILD_STRATEGY_TYPE":       reflect.ValueOf((*log.GUILD_STRATEGY_TYPE)(nil)),
		"GstDefenseInfo":            reflect.ValueOf((*log.GstDefenseInfo)(nil)),
		"GstGuildSnapshot":          reflect.ValueOf((*log.GstGuildSnapshot)(nil)),
		"GuildDungeonBossSnapshot":  reflect.ValueOf((*log.GuildDungeonBossSnapshot)(nil)),
		"GuildLogText":              reflect.ValueOf((*log.GuildLogText)(nil)),
		"GuildManage":               reflect.ValueOf((*log.GuildManage)(nil)),
		"GuildPosition":             reflect.ValueOf((*log.GuildPosition)(nil)),
		"GuildSeasonFinalDivision":  reflect.ValueOf((*log.GuildSeasonFinalDivision)(nil)),
		"GuildSnapshot":             reflect.ValueOf((*log.GuildSnapshot)(nil)),
		"GuildTalentSnapshot":       reflect.ValueOf((*log.GuildTalentSnapshot)(nil)),
		"GuildUserJoinOrQuit":       reflect.ValueOf((*log.GuildUserJoinOrQuit)(nil)),
		"Hero":                      reflect.ValueOf((*log.Hero)(nil)),
		"HeroGemAndEmblemAddition":  reflect.ValueOf((*log.HeroGemAndEmblemAddition)(nil)),
		"HeroSkillSnapshot":         reflect.ValueOf((*log.HeroSkillSnapshot)(nil)),
		"HeroSnapshot":              reflect.ValueOf((*log.HeroSnapshot)(nil)),
		"HeroSnapshotForWm":         reflect.ValueOf((*log.HeroSnapshotForWm)(nil)),
		"HeroSysId":                 reflect.ValueOf((*log.HeroSysId)(nil)),
		"KAFKA_HANDLER_TYPE":        reflect.ValueOf((*log.KAFKA_HANDLER_TYPE)(nil)),
		"KAFKA_TOPIC":               reflect.ValueOf((*log.KAFKA_TOPIC)(nil)),
		"Link":                      reflect.ValueOf((*log.Link)(nil)),
		"LogBossRushFight":          reflect.ValueOf((*log.LogBossRushFight)(nil)),
		"LogBuildDispatchHero":      reflect.ValueOf((*log.LogBuildDispatchHero)(nil)),
		"LogGSTArenaRank":           reflect.ValueOf((*log.LogGSTArenaRank)(nil)),
		"LogGSTArenaRanks":          reflect.ValueOf((*log.LogGSTArenaRanks)(nil)),
		"LogGSTBuild":               reflect.ValueOf((*log.LogGSTBuild)(nil)),
		"LogGSTChallengeFight":      reflect.ValueOf((*log.LogGSTChallengeFight)(nil)),
		"LogGSTChallengeStreak":     reflect.ValueOf((*log.LogGSTChallengeStreak)(nil)),
		"LogGSTChallengeTaskAward":  reflect.ValueOf((*log.LogGSTChallengeTaskAward)(nil)),
		"LogGSTChallengeTeam":       reflect.ValueOf((*log.LogGSTChallengeTeam)(nil)),
		"LogGSTDragonFight":         reflect.ValueOf((*log.LogGSTDragonFight)(nil)),
		"LogGSTDragonGuild":         reflect.ValueOf((*log.LogGSTDragonGuild)(nil)),
		"LogGSTDragonSettlement":    reflect.ValueOf((*log.LogGSTDragonSettlement)(nil)),
		"LogGSTOreFight":            reflect.ValueOf((*log.LogGSTOreFight)(nil)),
		"LogGSTSkill":               reflect.ValueOf((*log.LogGSTSkill)(nil)),
		"LogGSTStreak":              reflect.ValueOf((*log.LogGSTStreak)(nil)),
		"LogGSTTechLevelUp":         reflect.ValueOf((*log.LogGSTTechLevelUp)(nil)),
		"LogGSTTechTaskAward":       reflect.ValueOf((*log.LogGSTTechTaskAward)(nil)),
		"LogGSTVote":                reflect.ValueOf((*log.LogGSTVote)(nil)),
		"LogGSTVoteDetail":          reflect.ValueOf((*log.LogGSTVoteDetail)(nil)),
		"LogGstBattleSub":           reflect.ValueOf((*log.LogGstBattleSub)(nil)),
		"LogGuildMedal":             reflect.ValueOf((*log.LogGuildMedal)(nil)),
		"LogGuildMobScore":          reflect.ValueOf((*log.LogGuildMobScore)(nil)),
		"LogHeroEmblemSkill":        reflect.ValueOf((*log.LogHeroEmblemSkill)(nil)),
		"LogHeroSelfData":           reflect.ValueOf((*log.LogHeroSelfData)(nil)),
		"LogHeroSelfDataStr":        reflect.ValueOf((*log.LogHeroSelfDataStr)(nil)),
		"LogOreResourceChange":      reflect.ValueOf((*log.LogOreResourceChange)(nil)),
		"LogSeasonArena":            reflect.ValueOf((*log.LogSeasonArena)(nil)),
		"LogSeasonDoorFight":        reflect.ValueOf((*log.LogSeasonDoorFight)(nil)),
		"LogSeasonDoorTaskReward":   reflect.ValueOf((*log.LogSeasonDoorTaskReward)(nil)),
		"LogSeasonJewelryData":      reflect.ValueOf((*log.LogSeasonJewelryData)(nil)),
		"LogSeasonJewelryDecompose": reflect.ValueOf((*log.LogSeasonJewelryDecompose)(nil)),
		"LogSeasonMapFight":         reflect.ValueOf((*log.LogSeasonMapFight)(nil)),
		"MailMessageForWm":          reflect.ValueOf((*log.MailMessageForWm)(nil)),
		"MirageSnapshot":            reflect.ValueOf((*log.MirageSnapshot)(nil)),
		"MonumentRune":              reflect.ValueOf((*log.MonumentRune)(nil)),
		"OperateActivityTask":       reflect.ValueOf((*log.OperateActivityTask)(nil)),
		"PeakFighterRank":           reflect.ValueOf((*log.PeakFighterRank)(nil)),
		"PeakRankSnapshot":          reflect.ValueOf((*log.PeakRankSnapshot)(nil)),
		"RESOURCE_CHANGE":           reflect.ValueOf((*log.RESOURCE_CHANGE)(nil)),
		"ResourceStr":               reflect.ValueOf((*log.ResourceStr)(nil)),
		"SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE": reflect.ValueOf((*log.SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE)(nil)),
		"SUB_TYPE":                 reflect.ValueOf((*log.SUB_TYPE)(nil)),
		"SeasonLinkActivation":     reflect.ValueOf((*log.SeasonLinkActivation)(nil)),
		"SeasonLinkActive":         reflect.ValueOf((*log.SeasonLinkActive)(nil)),
		"SeasonLinkRune":           reflect.ValueOf((*log.SeasonLinkRune)(nil)),
		"Team":                     reflect.ValueOf((*log.Team)(nil)),
		"TokenResourceStr":         reflect.ValueOf((*log.TokenResourceStr)(nil)),
		"TrialOnHook":              reflect.ValueOf((*log.TrialOnHook)(nil)),
		"TrialSnapshot":            reflect.ValueOf((*log.TrialSnapshot)(nil)),
		"UserMonumentRuneSnapshot": reflect.ValueOf((*log.UserMonumentRuneSnapshot)(nil)),
		"UserSnapshotForWm":        reflect.ValueOf((*log.UserSnapshotForWm)(nil)),
		"WorldBossRankSnapshot":    reflect.ValueOf((*log.WorldBossRankSnapshot)(nil)),
	}
}
