// Code generated by 'yaegi extract app/logic/evententity'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/logic/activity"
	"app/logic/evententity"
	"reflect"
)

func init() {
	Symbols["app/logic/evententity/evententity"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ActivitySumEvent":                    reflect.ValueOf(&evententity.ActivitySumEvent).Elem(),
		"ArenaShopBuyEquip":                   reflect.ValueOf(&evententity.ArenaShopBuyEquip).Elem(),
		"ArtifactSummonEvent":                 reflect.ValueOf(&evententity.ArtifactSummonEvent).Elem(),
		"ArtifactSummonPointEvent":            reflect.ValueOf(&evententity.ArtifactSummonPointEvent).Elem(),
		"BattleFailedEvent":                   reflect.ValueOf(&evententity.BattleFailedEvent).Elem(),
		"BuyNumberTypeCount":                  reflect.ValueOf(&evententity.BuyNumberTypeCount).<PERSON><PERSON>(),
		"ChatEvent":                           reflect.ValueOf(&evententity.ChatEvent).<PERSON><PERSON>(),
		"CompleteDispatchTask":                reflect.ValueOf(&evententity.CompleteDispatchTask).Elem(),
		"ComplianceTasksEvent":                reflect.ValueOf(&evententity.ComplianceTasksEvent).Elem(),
		"CostDiamondEvent":                    reflect.ValueOf(&evententity.CostDiamondEvent).Elem(),
		"DailyAttendanceExtraAwardAfterEvent": reflect.ValueOf(&evententity.DailyAttendanceExtraAwardAfterEvent).Elem(),
		"DecomposeRareEquip":                  reflect.ValueOf(&evententity.DecomposeRareEquip).Elem(),
		"DivineDemonSummonEvent":              reflect.ValueOf(&evententity.DivineDemonSummonEvent).Elem(),
		"EmblemCultivateEvent":                reflect.ValueOf(&evententity.EmblemCultivateEvent).Elem(),
		"EquipmentCultivateEvent":             reflect.ValueOf(&evententity.EquipmentCultivateEvent).Elem(),
		"FirstStarTowerEvent":                 reflect.ValueOf(&evententity.FirstStarTowerEvent).Elem(),
		"GoddessContactLevelUpEvent":          reflect.ValueOf(&evententity.GoddessContactLevelUpEvent).Elem(),
		"GoddessFeedEvent":                    reflect.ValueOf(&evententity.GoddessFeedEvent).Elem(),
		"GoddessLevelUpEvent":                 reflect.ValueOf(&evententity.GoddessLevelUpEvent).Elem(),
		"GoddessStoryFinishEvent":             reflect.ValueOf(&evententity.GoddessStoryFinishEvent).Elem(),
		"GoddessTouchEvent":                   reflect.ValueOf(&evententity.GoddessTouchEvent).Elem(),
		"GoddessTouchOrFeedEvent":             reflect.ValueOf(&evententity.GoddessTouchOrFeedEvent).Elem(),
		"GoddessUnlockEvent":                  reflect.ValueOf(&evententity.GoddessUnlockEvent).Elem(),
		"GuildDungeonBuyChallengeTimesEvent":  reflect.ValueOf(&evententity.GuildDungeonBuyChallengeTimesEvent).Elem(),
		"GuildTalentLevelUpEvent":             reflect.ValueOf(&evententity.GuildTalentLevelUpEvent).Elem(),
		"InitEventWatcher":                    reflect.ValueOf(evententity.InitEventWatcher),
		"RechargeAfterEvent":                  reflect.ValueOf(&evententity.RechargeAfterEvent).Elem(),
		"SeasonArenaOnEvent":                  reflect.ValueOf(&evententity.SeasonArenaOnEvent).Elem(),
		"SeasonComplianceParamCommonSeasonThreeOrangeEmblemStageID":  reflect.ValueOf(evententity.SeasonComplianceParamCommonSeasonThreeOrangeEmblemStageID),
		"SeasonComplianceParamCommonThreeOrangeEmblemStageID":        reflect.ValueOf(evententity.SeasonComplianceParamCommonThreeOrangeEmblemStageID),
		"SeasonComplianceParamCommonThreeRedEmblemStageID":           reflect.ValueOf(evententity.SeasonComplianceParamCommonThreeRedEmblemStageID),
		"SeasonComplianceParamCommonThreeSeasonRedEmblemStageID":     reflect.ValueOf(evententity.SeasonComplianceParamCommonThreeSeasonRedEmblemStageID),
		"SeasonComplianceParamDivineHero":                            reflect.ValueOf(evententity.SeasonComplianceParamDivineHero),
		"SeasonComplianceParamDivineSeasonAddHero":                   reflect.ValueOf(evententity.SeasonComplianceParamDivineSeasonAddHero),
		"SeasonComplianceParamHaveMoreThanOrangeEmblemStageID":       reflect.ValueOf(evententity.SeasonComplianceParamHaveMoreThanOrangeEmblemStageID),
		"SeasonComplianceParamHaveMoreThanRedEmblemStageID":          reflect.ValueOf(evententity.SeasonComplianceParamHaveMoreThanRedEmblemStageID),
		"SeasonComplianceParamNormalSeasonHero":                      reflect.ValueOf(evententity.SeasonComplianceParamNormalSeasonHero),
		"SeasonComplianceParamProtossDemonOrangeEmblemStageID":       reflect.ValueOf(evententity.SeasonComplianceParamProtossDemonOrangeEmblemStageID),
		"SeasonComplianceParamProtossDemonRedEmblemStageID":          reflect.ValueOf(evententity.SeasonComplianceParamProtossDemonRedEmblemStageID),
		"SeasonComplianceParamProtossDemonSeasonOrangeEmblemStageID": reflect.ValueOf(evententity.SeasonComplianceParamProtossDemonSeasonOrangeEmblemStageID),
		"SeasonComplianceParamProtossDemonSeasonRedEmblemStageID":    reflect.ValueOf(evententity.SeasonComplianceParamProtossDemonSeasonRedEmblemStageID),
		"SeasonComplianceParamPyramidDrawNumStageID":                 reflect.ValueOf(evententity.SeasonComplianceParamPyramidDrawNumStageID),
		"SeasonComplianceParamRedArtifactFragmentStageID":            reflect.ValueOf(evententity.SeasonComplianceParamRedArtifactFragmentStageID),
		"SeasonComplianceParamRedArtifactStageID":                    reflect.ValueOf(evententity.SeasonComplianceParamRedArtifactStageID),
		"SeasonComplianceParamRedSeasonArtifactFragmentStageID":      reflect.ValueOf(evententity.SeasonComplianceParamRedSeasonArtifactFragmentStageID),
		"SeasonComplianceParamRedSeasonArtifactStageID":              reflect.ValueOf(evententity.SeasonComplianceParamRedSeasonArtifactStageID),
		"SeasonComplianceParamUint32Stage":                           reflect.ValueOf(evententity.SeasonComplianceParamUint32Stage),
		"SeasonDoorEvent":                                            reflect.ValueOf(&evententity.SeasonDoorEvent).Elem(),
		"SeasonDungeonEvent":                                         reflect.ValueOf(&evententity.SeasonDungeonEvent).Elem(),
		"SeasonMapEvent":                                             reflect.ValueOf(&evententity.SeasonMapEvent).Elem(),
		"ShopBuyAny":                                                 reflect.ValueOf(&evententity.ShopBuyAny).Elem(),
		"ShopBuyItem":                                                reflect.ValueOf(&evententity.ShopBuyItem).Elem(),
		"SummonTrueFiveHero":                                         reflect.ValueOf(&evententity.SummonTrueFiveHero).Elem(),
		"TowerSeasonHandlerOnEvent":                                  reflect.ValueOf(&evententity.TowerSeasonHandlerOnEvent).Elem(),
		"TowerStarStartFight":                                        reflect.ValueOf(&evententity.TowerStarStartFight).Elem(),
		"TowerStartFight":                                            reflect.ValueOf(&evententity.TowerStartFight).Elem(),
		"WrestleFight":                                               reflect.ValueOf(&evententity.WrestleFight).Elem(),

		// type definitions
		"CalcScoreFunc":      reflect.ValueOf((*evententity.CalcScoreFunc)(nil)),
		"GetActivityService": reflect.ValueOf((*evententity.GetActivityService)(nil)),
		"StageIDCheckParam":  reflect.ValueOf((*evententity.StageIDCheckParam)(nil)),

		// interface wrapper definitions
		"_GetActivityService": reflect.ValueOf((*_app_logic_evententity_GetActivityService)(nil)),
	}
}

// _app_logic_evententity_GetActivityService is an interface wrapper for GetActivityService type
type _app_logic_evententity_GetActivityService struct {
	IValue       interface{}
	WGetActivity func(a0 activity.ID) activity.Activity
}

func (W _app_logic_evententity_GetActivityService) GetActivity(a0 activity.ID) activity.Activity {
	return W.WGetActivity(a0)
}
