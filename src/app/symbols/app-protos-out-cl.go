// Code generated by 'yaegi extract app/protos/out/cl'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/protos/out/cl"
	"reflect"
)

func init() {
	Symbols["app/protos/out/cl/cl"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ErrIntOverflowCl":                                            reflect.ValueOf(&cl.ErrIntOverflowCl).<PERSON>em(),
		"ErrInvalidLengthCl":                                          reflect.ValueOf(&cl.ErrInvalidLengthCl).Elem(),
		"ErrUnexpectedEndOfGroupCl":                                   reflect.ValueOf(&cl.ErrUnexpectedEndOfGroupCl).Elem(),
		"GSTBossRecordType_RecordType_Award":                          reflect.ValueOf(cl.GSTBossRecordType_RecordType_Award),
		"GSTBossRecordType_RecordType_Come":                           reflect.ValueOf(cl.GSTBossRecordType_RecordType_Come),
		"GSTBossRecordType_RecordType_Damage":                         reflect.ValueOf(cl.GSTBossRecordType_RecordType_Damage),
		"GSTBossRecordType_RecordType_Hp":                             reflect.ValueOf(cl.GSTBossRecordType_RecordType_Hp),
		"GSTBossRecordType_RecordType_None":                           reflect.ValueOf(cl.GSTBossRecordType_RecordType_None),
		"GSTBossRecordType_RecordType_Reborn":                         reflect.ValueOf(cl.GSTBossRecordType_RecordType_Reborn),
		"GSTBossRecordType_RecordType_Timeout":                        reflect.ValueOf(cl.GSTBossRecordType_RecordType_Timeout),
		"GSTBossRecordType_name":                                      reflect.ValueOf(&cl.GSTBossRecordType_name).Elem(),
		"GSTBossRecordType_value":                                     reflect.ValueOf(&cl.GSTBossRecordType_value).Elem(),
		"GSTDragonFightRecordType_DFRT_None":                          reflect.ValueOf(cl.GSTDragonFightRecordType_DFRT_None),
		"GSTDragonFightRecordType_DFRT_User_Attack":                   reflect.ValueOf(cl.GSTDragonFightRecordType_DFRT_User_Attack),
		"GSTDragonFightRecordType_DFRT_User_Kill":                     reflect.ValueOf(cl.GSTDragonFightRecordType_DFRT_User_Kill),
		"GSTDragonFightRecordType_name":                               reflect.ValueOf(&cl.GSTDragonFightRecordType_name).Elem(),
		"GSTDragonFightRecordType_value":                              reflect.ValueOf(&cl.GSTDragonFightRecordType_value).Elem(),
		"GSTGetLogReqType_ReqType_Arena_Fight_Record":                 reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Arena_Fight_Record),
		"GSTGetLogReqType_ReqType_Build_Donate_Details":               reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Build_Donate_Details),
		"GSTGetLogReqType_ReqType_Challenge":                          reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Challenge),
		"GSTGetLogReqType_ReqType_Dragon_Fight":                       reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Dragon_Fight),
		"GSTGetLogReqType_ReqType_GSTBoss_Fight_Record":               reflect.ValueOf(cl.GSTGetLogReqType_ReqType_GSTBoss_Fight_Record),
		"GSTGetLogReqType_ReqType_Ground":                             reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Ground),
		"GSTGetLogReqType_ReqType_Guild":                              reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Guild),
		"GSTGetLogReqType_ReqType_Guild_Mobilization":                 reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Guild_Mobilization),
		"GSTGetLogReqType_ReqType_Guild_Skill":                        reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Guild_Skill),
		"GSTGetLogReqType_ReqType_None":                               reflect.ValueOf(cl.GSTGetLogReqType_ReqType_None),
		"GSTGetLogReqType_ReqType_Ore_Assist":                         reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Ore_Assist),
		"GSTGetLogReqType_ReqType_Ore_Change":                         reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Ore_Change),
		"GSTGetLogReqType_ReqType_Personal_Contribution":              reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Personal_Contribution),
		"GSTGetLogReqType_ReqType_Personal_Fight":                     reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Personal_Fight),
		"GSTGetLogReqType_ReqType_Tech":                               reflect.ValueOf(cl.GSTGetLogReqType_ReqType_Tech),
		"GSTGetLogReqType_name":                                       reflect.ValueOf(&cl.GSTGetLogReqType_name).Elem(),
		"GSTGetLogReqType_value":                                      reflect.ValueOf(&cl.GSTGetLogReqType_value).Elem(),
		"GSTGuildScoreType_GSTYPE_ARENA_RANK":                         reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_ARENA_RANK),
		"GSTGuildScoreType_GSTYPE_BUILD":                              reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_BUILD),
		"GSTGuildScoreType_GSTYPE_BUILD_GODDESS_BLESS_LEVEL":          reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_BUILD_GODDESS_BLESS_LEVEL),
		"GSTGuildScoreType_GSTYPE_CHALLENGE_RANK":                     reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_CHALLENGE_RANK),
		"GSTGuildScoreType_GSTYPE_CONTROL_GROUND":                     reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_CONTROL_GROUND),
		"GSTGuildScoreType_GSTYPE_CONTROL_GROUND_GODDESS_BLESS":       reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_CONTROL_GROUND_GODDESS_BLESS),
		"GSTGuildScoreType_GSTYPE_CONTROL_GROUND_GODDESS_BLESS_LEVEL": reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_CONTROL_GROUND_GODDESS_BLESS_LEVEL),
		"GSTGuildScoreType_GSTYPE_CONTROL_GROUND_ONCE":                reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_CONTROL_GROUND_ONCE),
		"GSTGuildScoreType_GSTYPE_TOTAL":                              reflect.ValueOf(cl.GSTGuildScoreType_GSTYPE_TOTAL),
		"GSTGuildScoreType_name":                                      reflect.ValueOf(&cl.GSTGuildScoreType_name).Elem(),
		"GSTGuildScoreType_value":                                     reflect.ValueOf(&cl.GSTGuildScoreType_value).Elem(),
		"GSTLogType_LogType_ArenaFightRecord":                         reflect.ValueOf(cl.GSTLogType_LogType_ArenaFightRecord),
		"GSTLogType_LogType_BuildDonate":                              reflect.ValueOf(cl.GSTLogType_LogType_BuildDonate),
		"GSTLogType_LogType_BuildDonateDetails":                       reflect.ValueOf(cl.GSTLogType_LogType_BuildDonateDetails),
		"GSTLogType_LogType_ChallengeRecord":                          reflect.ValueOf(cl.GSTLogType_LogType_ChallengeRecord),
		"GSTLogType_LogType_Donate":                                   reflect.ValueOf(cl.GSTLogType_LogType_Donate),
		"GSTLogType_LogType_FightResult":                              reflect.ValueOf(cl.GSTLogType_LogType_FightResult),
		"GSTLogType_LogType_FightTeam":                                reflect.ValueOf(cl.GSTLogType_LogType_FightTeam),
		"GSTLogType_LogType_GSTBossFight":                             reflect.ValueOf(cl.GSTLogType_LogType_GSTBossFight),
		"GSTLogType_LogType_GSTDragonFight":                           reflect.ValueOf(cl.GSTLogType_LogType_GSTDragonFight),
		"GSTLogType_LogType_GSTDragonSkill":                           reflect.ValueOf(cl.GSTLogType_LogType_GSTDragonSkill),
		"GSTLogType_LogType_GroupSettle":                              reflect.ValueOf(cl.GSTLogType_LogType_GroupSettle),
		"GSTLogType_LogType_GuildMobilization":                        reflect.ValueOf(cl.GSTLogType_LogType_GuildMobilization),
		"GSTLogType_LogType_GuildSkill":                               reflect.ValueOf(cl.GSTLogType_LogType_GuildSkill),
		"GSTLogType_LogType_LRoundGuildRank":                          reflect.ValueOf(cl.GSTLogType_LogType_LRoundGuildRank),
		"GSTLogType_LogType_None":                                     reflect.ValueOf(cl.GSTLogType_LogType_None),
		"GSTLogType_LogType_OreAssist":                                reflect.ValueOf(cl.GSTLogType_LogType_OreAssist),
		"GSTLogType_LogType_OreChange":                                reflect.ValueOf(cl.GSTLogType_LogType_OreChange),
		"GSTLogType_LogType_RoundSettle":                              reflect.ValueOf(cl.GSTLogType_LogType_RoundSettle),
		"GSTLogType_LogType_Tech":                                     reflect.ValueOf(cl.GSTLogType_LogType_Tech),
		"GSTLogType_LogType_UserWinsRank":                             reflect.ValueOf(cl.GSTLogType_LogType_UserWinsRank),
		"GSTLogType_name":                                             reflect.ValueOf(&cl.GSTLogType_name).Elem(),
		"GSTLogType_value":                                            reflect.ValueOf(&cl.GSTLogType_value).Elem(),
		"GSTSkillType_GSTSkillType_Assemble":                          reflect.ValueOf(cl.GSTSkillType_GSTSkillType_Assemble),
		"GSTSkillType_GSTSkillType_BackDragonSkill":                   reflect.ValueOf(cl.GSTSkillType_GSTSkillType_BackDragonSkill),
		"GSTSkillType_GSTSkillType_DragonSkill":                       reflect.ValueOf(cl.GSTSkillType_GSTSkillType_DragonSkill),
		"GSTSkillType_GSTSkillType_None":                              reflect.ValueOf(cl.GSTSkillType_GSTSkillType_None),
		"GSTSkillType_name":                                           reflect.ValueOf(&cl.GSTSkillType_name).Elem(),
		"GSTSkillType_value":                                          reflect.ValueOf(&cl.GSTSkillType_value).Elem(),
		"GSTUserScoreType_USTYPE_BUILD_TASK":                          reflect.ValueOf(cl.GSTUserScoreType_USTYPE_BUILD_TASK),
		"GSTUserScoreType_USTYPE_GODDESS_BLESS":                       reflect.ValueOf(cl.GSTUserScoreType_USTYPE_GODDESS_BLESS),
		"GSTUserScoreType_USTYPE_PVE_FIGHT":                           reflect.ValueOf(cl.GSTUserScoreType_USTYPE_PVE_FIGHT),
		"GSTUserScoreType_USTYPE_PVP_FIGHT":                           reflect.ValueOf(cl.GSTUserScoreType_USTYPE_PVP_FIGHT),
		"GSTUserScoreType_USTYPE_SEND_TEAM":                           reflect.ValueOf(cl.GSTUserScoreType_USTYPE_SEND_TEAM),
		"GSTUserScoreType_USTYPE_TOTAL":                               reflect.ValueOf(cl.GSTUserScoreType_USTYPE_TOTAL),
		"GSTUserScoreType_name":                                       reflect.ValueOf(&cl.GSTUserScoreType_name).Elem(),
		"GSTUserScoreType_value":                                      reflect.ValueOf(&cl.GSTUserScoreType_value).Elem(),
		"GST_STAGE_Round0State_Finish":                                reflect.ValueOf(cl.GST_STAGE_Round0State_Finish),
		"GST_STAGE_Round0State_Matching":                              reflect.ValueOf(cl.GST_STAGE_Round0State_Matching),
		"GST_STAGE_Round0State_None":                                  reflect.ValueOf(cl.GST_STAGE_Round0State_None),
		"GST_STAGE_Round0State_Sign":                                  reflect.ValueOf(cl.GST_STAGE_Round0State_Sign),
		"GST_STAGE_RoundFightState_Fight":                             reflect.ValueOf(cl.GST_STAGE_RoundFightState_Fight),
		"GST_STAGE_RoundFightState_Finish":                            reflect.ValueOf(cl.GST_STAGE_RoundFightState_Finish),
		"GST_STAGE_RoundFightState_Operate":                           reflect.ValueOf(cl.GST_STAGE_RoundFightState_Operate),
		"GST_STAGE_RoundFightState_Reward":                            reflect.ValueOf(cl.GST_STAGE_RoundFightState_Reward),
		"GST_STAGE_RoundFightState_RewardGuild":                       reflect.ValueOf(cl.GST_STAGE_RoundFightState_RewardGuild),
		"GST_STAGE_RoundFightState_SyncGuild":                         reflect.ValueOf(cl.GST_STAGE_RoundFightState_SyncGuild),
		"GST_STAGE_RoundFightState_SyncGuildFinish":                   reflect.ValueOf(cl.GST_STAGE_RoundFightState_SyncGuildFinish),
		"GST_STAGE_name":                                              reflect.ValueOf(&cl.GST_STAGE_name).Elem(),
		"GST_STAGE_value":                                             reflect.ValueOf(&cl.GST_STAGE_value).Elem(),
		"GST_TEAM_STAGE_Gst_Team_Manager":                             reflect.ValueOf(cl.GST_TEAM_STAGE_Gst_Team_Manager),
		"GST_TEAM_STAGE_Gst_Team_None":                                reflect.ValueOf(cl.GST_TEAM_STAGE_Gst_Team_None),
		"GST_TEAM_STAGE_Gst_Team_Self":                                reflect.ValueOf(cl.GST_TEAM_STAGE_Gst_Team_Self),
		"GST_TEAM_STAGE_name":                                         reflect.ValueOf(&cl.GST_TEAM_STAGE_name).Elem(),
		"GST_TEAM_STAGE_value":                                        reflect.ValueOf(&cl.GST_TEAM_STAGE_value).Elem(),
		"GuildMobilizationTaskStatus_Finished":                        reflect.ValueOf(cl.GuildMobilizationTaskStatus_Finished),
		"GuildMobilizationTaskStatus_GetReward":                       reflect.ValueOf(cl.GuildMobilizationTaskStatus_GetReward),
		"GuildMobilizationTaskStatus_None":                            reflect.ValueOf(cl.GuildMobilizationTaskStatus_None),
		"GuildMobilizationTaskStatus_name":                            reflect.ValueOf(&cl.GuildMobilizationTaskStatus_name).Elem(),
		"GuildMobilizationTaskStatus_value":                           reflect.ValueOf(&cl.GuildMobilizationTaskStatus_value).Elem(),
		"ID_MSG_BEGIN":                                                reflect.ValueOf(cl.ID_MSG_BEGIN),
		"ID_MSG_C2L_Accusation":                                       reflect.ValueOf(cl.ID_MSG_C2L_Accusation),
		"ID_MSG_C2L_ActivityComplianceGetData":                        reflect.ValueOf(cl.ID_MSG_C2L_ActivityComplianceGetData),
		"ID_MSG_C2L_ActivityComplianceLikeRank":                       reflect.ValueOf(cl.ID_MSG_C2L_ActivityComplianceLikeRank),
		"ID_MSG_C2L_ActivityComplianceRecvAward":                      reflect.ValueOf(cl.ID_MSG_C2L_ActivityComplianceRecvAward),
		"ID_MSG_C2L_ActivityCouponBuy":                                reflect.ValueOf(cl.ID_MSG_C2L_ActivityCouponBuy),
		"ID_MSG_C2L_ActivityCouponGetData":                            reflect.ValueOf(cl.ID_MSG_C2L_ActivityCouponGetData),
		"ID_MSG_C2L_ActivityGoddessActive":                            reflect.ValueOf(cl.ID_MSG_C2L_ActivityGoddessActive),
		"ID_MSG_C2L_ActivityGoddessRecvRecoveryAward":                 reflect.ValueOf(cl.ID_MSG_C2L_ActivityGoddessRecvRecoveryAward),
		"ID_MSG_C2L_ActivityMixGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_ActivityMixGetData),
		"ID_MSG_C2L_ActivityRechargeBuy":                              reflect.ValueOf(cl.ID_MSG_C2L_ActivityRechargeBuy),
		"ID_MSG_C2L_ActivityRechargeGet":                              reflect.ValueOf(cl.ID_MSG_C2L_ActivityRechargeGet),
		"ID_MSG_C2L_ActivityReturnGetData":                            reflect.ValueOf(cl.ID_MSG_C2L_ActivityReturnGetData),
		"ID_MSG_C2L_ActivityReturnTakeLoginAwards":                    reflect.ValueOf(cl.ID_MSG_C2L_ActivityReturnTakeLoginAwards),
		"ID_MSG_C2L_ActivityScheduleGetData":                          reflect.ValueOf(cl.ID_MSG_C2L_ActivityScheduleGetData),
		"ID_MSG_C2L_ActivityStoryExchange":                            reflect.ValueOf(cl.ID_MSG_C2L_ActivityStoryExchange),
		"ID_MSG_C2L_ActivityStoryFight":                               reflect.ValueOf(cl.ID_MSG_C2L_ActivityStoryFight),
		"ID_MSG_C2L_ActivityStoryGetData":                             reflect.ValueOf(cl.ID_MSG_C2L_ActivityStoryGetData),
		"ID_MSG_C2L_ActivityStoryLoginAward":                          reflect.ValueOf(cl.ID_MSG_C2L_ActivityStoryLoginAward),
		"ID_MSG_C2L_ActivitySumActivePuzzleCell":                      reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumActivePuzzleCell),
		"ID_MSG_C2L_ActivitySumExchange":                              reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumExchange),
		"ID_MSG_C2L_ActivitySumFeed":                                  reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumFeed),
		"ID_MSG_C2L_ActivitySumGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumGetData),
		"ID_MSG_C2L_ActivitySumLoginReward":                           reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumLoginReward),
		"ID_MSG_C2L_ActivitySumMakeGift":                              reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumMakeGift),
		"ID_MSG_C2L_ActivitySumShootGameFight":                        reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumShootGameFight),
		"ID_MSG_C2L_ActivitySumTaskReward":                            reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumTaskReward),
		"ID_MSG_C2L_ActivitySumTicketBuy":                             reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumTicketBuy),
		"ID_MSG_C2L_ActivitySumTurnTableSelectBuff":                   reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumTurnTableSelectBuff),
		"ID_MSG_C2L_ActivitySumTurnTableSummon":                       reflect.ValueOf(cl.ID_MSG_C2L_ActivitySumTurnTableSummon),
		"ID_MSG_C2L_ActivityTurnTableGetData":                         reflect.ValueOf(cl.ID_MSG_C2L_ActivityTurnTableGetData),
		"ID_MSG_C2L_ActivityTurnTableRecvLogin":                       reflect.ValueOf(cl.ID_MSG_C2L_ActivityTurnTableRecvLogin),
		"ID_MSG_C2L_ActivityTurnTableRecvTask":                        reflect.ValueOf(cl.ID_MSG_C2L_ActivityTurnTableRecvTask),
		"ID_MSG_C2L_ActivityTurnTableSelectBuff":                      reflect.ValueOf(cl.ID_MSG_C2L_ActivityTurnTableSelectBuff),
		"ID_MSG_C2L_ActivityTurnTableSummon":                          reflect.ValueOf(cl.ID_MSG_C2L_ActivityTurnTableSummon),
		"ID_MSG_C2L_ActivityTurnTableTicketBuy":                       reflect.ValueOf(cl.ID_MSG_C2L_ActivityTurnTableTicketBuy),
		"ID_MSG_C2L_AddPurchaseNum":                                   reflect.ValueOf(cl.ID_MSG_C2L_AddPurchaseNum),
		"ID_MSG_C2L_AnnouncementGetData":                              reflect.ValueOf(cl.ID_MSG_C2L_AnnouncementGetData),
		"ID_MSG_C2L_ArenaFight":                                       reflect.ValueOf(cl.ID_MSG_C2L_ArenaFight),
		"ID_MSG_C2L_ArenaInfo":                                        reflect.ValueOf(cl.ID_MSG_C2L_ArenaInfo),
		"ID_MSG_C2L_ArenaLike":                                        reflect.ValueOf(cl.ID_MSG_C2L_ArenaLike),
		"ID_MSG_C2L_ArenaLogList":                                     reflect.ValueOf(cl.ID_MSG_C2L_ArenaLogList),
		"ID_MSG_C2L_ArenaRank":                                        reflect.ValueOf(cl.ID_MSG_C2L_ArenaRank),
		"ID_MSG_C2L_ArenaRecvAward":                                   reflect.ValueOf(cl.ID_MSG_C2L_ArenaRecvAward),
		"ID_MSG_C2L_ArenaRefresh":                                     reflect.ValueOf(cl.ID_MSG_C2L_ArenaRefresh),
		"ID_MSG_C2L_ArtifactActivate":                                 reflect.ValueOf(cl.ID_MSG_C2L_ArtifactActivate),
		"ID_MSG_C2L_ArtifactDebutGetActivity":                         reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutGetActivity),
		"ID_MSG_C2L_ArtifactDebutMainInfo":                            reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutMainInfo),
		"ID_MSG_C2L_ArtifactDebutOpenPuzzle":                          reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutOpenPuzzle),
		"ID_MSG_C2L_ArtifactDebutRecvActAward":                        reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutRecvActAward),
		"ID_MSG_C2L_ArtifactDebutRecvTaskAward":                       reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutRecvTaskAward),
		"ID_MSG_C2L_ArtifactDebutSetWish":                             reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutSetWish),
		"ID_MSG_C2L_ArtifactDebutSummon":                              reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutSummon),
		"ID_MSG_C2L_ArtifactDebutTestSummon":                          reflect.ValueOf(cl.ID_MSG_C2L_ArtifactDebutTestSummon),
		"ID_MSG_C2L_ArtifactForge":                                    reflect.ValueOf(cl.ID_MSG_C2L_ArtifactForge),
		"ID_MSG_C2L_ArtifactList":                                     reflect.ValueOf(cl.ID_MSG_C2L_ArtifactList),
		"ID_MSG_C2L_ArtifactRevive":                                   reflect.ValueOf(cl.ID_MSG_C2L_ArtifactRevive),
		"ID_MSG_C2L_ArtifactStarUp":                                   reflect.ValueOf(cl.ID_MSG_C2L_ArtifactStarUp),
		"ID_MSG_C2L_ArtifactStrength":                                 reflect.ValueOf(cl.ID_MSG_C2L_ArtifactStrength),
		"ID_MSG_C2L_AssistanceActivityGetAward":                       reflect.ValueOf(cl.ID_MSG_C2L_AssistanceActivityGetAward),
		"ID_MSG_C2L_AssistanceActivityGetData":                        reflect.ValueOf(cl.ID_MSG_C2L_AssistanceActivityGetData),
		"ID_MSG_C2L_AssistantGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_AssistantGetData),
		"ID_MSG_C2L_AvatarGetInfo":                                    reflect.ValueOf(cl.ID_MSG_C2L_AvatarGetInfo),
		"ID_MSG_C2L_AvatarSetIcon":                                    reflect.ValueOf(cl.ID_MSG_C2L_AvatarSetIcon),
		"ID_MSG_C2L_BattleTest":                                       reflect.ValueOf(cl.ID_MSG_C2L_BattleTest),
		"ID_MSG_C2L_BossRushBuyStamina":                               reflect.ValueOf(cl.ID_MSG_C2L_BossRushBuyStamina),
		"ID_MSG_C2L_BossRushFight":                                    reflect.ValueOf(cl.ID_MSG_C2L_BossRushFight),
		"ID_MSG_C2L_BossRushGetData":                                  reflect.ValueOf(cl.ID_MSG_C2L_BossRushGetData),
		"ID_MSG_C2L_BossRushTaskAward":                                reflect.ValueOf(cl.ID_MSG_C2L_BossRushTaskAward),
		"ID_MSG_C2L_BoxExchange":                                      reflect.ValueOf(cl.ID_MSG_C2L_BoxExchange),
		"ID_MSG_C2L_BoxGet":                                           reflect.ValueOf(cl.ID_MSG_C2L_BoxGet),
		"ID_MSG_C2L_BoxOpen":                                          reflect.ValueOf(cl.ID_MSG_C2L_BoxOpen),
		"ID_MSG_C2L_CarnivalGetData":                                  reflect.ValueOf(cl.ID_MSG_C2L_CarnivalGetData),
		"ID_MSG_C2L_CarnivalReceiveAward":                             reflect.ValueOf(cl.ID_MSG_C2L_CarnivalReceiveAward),
		"ID_MSG_C2L_ChatCostShareCount":                               reflect.ValueOf(cl.ID_MSG_C2L_ChatCostShareCount),
		"ID_MSG_C2L_ChatGetToken":                                     reflect.ValueOf(cl.ID_MSG_C2L_ChatGetToken),
		"ID_MSG_C2L_ChatSyncChatTag":                                  reflect.ValueOf(cl.ID_MSG_C2L_ChatSyncChatTag),
		"ID_MSG_C2L_ClientGetMultiLang":                               reflect.ValueOf(cl.ID_MSG_C2L_ClientGetMultiLang),
		"ID_MSG_C2L_ClientSetMultiLang":                               reflect.ValueOf(cl.ID_MSG_C2L_ClientSetMultiLang),
		"ID_MSG_C2L_CommonRankLike":                                   reflect.ValueOf(cl.ID_MSG_C2L_CommonRankLike),
		"ID_MSG_C2L_ComplianceTasksGetData":                           reflect.ValueOf(cl.ID_MSG_C2L_ComplianceTasksGetData),
		"ID_MSG_C2L_ComplianceTasksRecvTask":                          reflect.ValueOf(cl.ID_MSG_C2L_ComplianceTasksRecvTask),
		"ID_MSG_C2L_CrystalActiveAchievement":                         reflect.ValueOf(cl.ID_MSG_C2L_CrystalActiveAchievement),
		"ID_MSG_C2L_CrystalAddHero":                                   reflect.ValueOf(cl.ID_MSG_C2L_CrystalAddHero),
		"ID_MSG_C2L_CrystalGetAllData":                                reflect.ValueOf(cl.ID_MSG_C2L_CrystalGetAllData),
		"ID_MSG_C2L_CrystalRemoveHero":                                reflect.ValueOf(cl.ID_MSG_C2L_CrystalRemoveHero),
		"ID_MSG_C2L_CrystalSpeedSlotCD":                               reflect.ValueOf(cl.ID_MSG_C2L_CrystalSpeedSlotCD),
		"ID_MSG_C2L_CrystalUnlockSlot":                                reflect.ValueOf(cl.ID_MSG_C2L_CrystalUnlockSlot),
		"ID_MSG_C2L_DailyAttendanceGetData":                           reflect.ValueOf(cl.ID_MSG_C2L_DailyAttendanceGetData),
		"ID_MSG_C2L_DailyAttendanceHeroGetData":                       reflect.ValueOf(cl.ID_MSG_C2L_DailyAttendanceHeroGetData),
		"ID_MSG_C2L_DailyAttendanceHeroRecvAward":                     reflect.ValueOf(cl.ID_MSG_C2L_DailyAttendanceHeroRecvAward),
		"ID_MSG_C2L_DailyAttendanceRecvAward":                         reflect.ValueOf(cl.ID_MSG_C2L_DailyAttendanceRecvAward),
		"ID_MSG_C2L_DailySpecialGetData":                              reflect.ValueOf(cl.ID_MSG_C2L_DailySpecialGetData),
		"ID_MSG_C2L_DailySpecialRecvAward":                            reflect.ValueOf(cl.ID_MSG_C2L_DailySpecialRecvAward),
		"ID_MSG_C2L_DailyWishGet":                                     reflect.ValueOf(cl.ID_MSG_C2L_DailyWishGet),
		"ID_MSG_C2L_DailyWishSummon":                                  reflect.ValueOf(cl.ID_MSG_C2L_DailyWishSummon),
		"ID_MSG_C2L_DailyWishXmlGet":                                  reflect.ValueOf(cl.ID_MSG_C2L_DailyWishXmlGet),
		"ID_MSG_C2L_DeleteMails":                                      reflect.ValueOf(cl.ID_MSG_C2L_DeleteMails),
		"ID_MSG_C2L_DisorderlandBuyStamina":                           reflect.ValueOf(cl.ID_MSG_C2L_DisorderlandBuyStamina),
		"ID_MSG_C2L_DisorderlandGetData":                              reflect.ValueOf(cl.ID_MSG_C2L_DisorderlandGetData),
		"ID_MSG_C2L_DisorderlandRank":                                 reflect.ValueOf(cl.ID_MSG_C2L_DisorderlandRank),
		"ID_MSG_C2L_DisorderlandTestSweep":                            reflect.ValueOf(cl.ID_MSG_C2L_DisorderlandTestSweep),
		"ID_MSG_C2L_DisorderlandTriggerEvent":                         reflect.ValueOf(cl.ID_MSG_C2L_DisorderlandTriggerEvent),
		"ID_MSG_C2L_DispatchGetAwards":                                reflect.ValueOf(cl.ID_MSG_C2L_DispatchGetAwards),
		"ID_MSG_C2L_DispatchReceiveTask":                              reflect.ValueOf(cl.ID_MSG_C2L_DispatchReceiveTask),
		"ID_MSG_C2L_DispatchRefreshTask":                              reflect.ValueOf(cl.ID_MSG_C2L_DispatchRefreshTask),
		"ID_MSG_C2L_DispatchTasks":                                    reflect.ValueOf(cl.ID_MSG_C2L_DispatchTasks),
		"ID_MSG_C2L_DivineDemonGetOpenActivity":                       reflect.ValueOf(cl.ID_MSG_C2L_DivineDemonGetOpenActivity),
		"ID_MSG_C2L_DivineDemonReceiveTaskAward":                      reflect.ValueOf(cl.ID_MSG_C2L_DivineDemonReceiveTaskAward),
		"ID_MSG_C2L_DivineDemonSummon":                                reflect.ValueOf(cl.ID_MSG_C2L_DivineDemonSummon),
		"ID_MSG_C2L_DrawMails":                                        reflect.ValueOf(cl.ID_MSG_C2L_DrawMails),
		"ID_MSG_C2L_DropActivityDailyReward":                          reflect.ValueOf(cl.ID_MSG_C2L_DropActivityDailyReward),
		"ID_MSG_C2L_DropActivityExchange":                             reflect.ValueOf(cl.ID_MSG_C2L_DropActivityExchange),
		"ID_MSG_C2L_DropActivityGetActivity":                          reflect.ValueOf(cl.ID_MSG_C2L_DropActivityGetActivity),
		"ID_MSG_C2L_DropActivityMainInfo":                             reflect.ValueOf(cl.ID_MSG_C2L_DropActivityMainInfo),
		"ID_MSG_C2L_Duel":                                             reflect.ValueOf(cl.ID_MSG_C2L_Duel),
		"ID_MSG_C2L_DuelFight":                                        reflect.ValueOf(cl.ID_MSG_C2L_DuelFight),
		"ID_MSG_C2L_DuelSetStatus":                                    reflect.ValueOf(cl.ID_MSG_C2L_DuelSetStatus),
		"ID_MSG_C2L_Dungeon":                                          reflect.ValueOf(cl.ID_MSG_C2L_Dungeon),
		"ID_MSG_C2L_DungeonFight":                                     reflect.ValueOf(cl.ID_MSG_C2L_DungeonFight),
		"ID_MSG_C2L_DungeonPreview":                                   reflect.ValueOf(cl.ID_MSG_C2L_DungeonPreview),
		"ID_MSG_C2L_DungeonRecvAward":                                 reflect.ValueOf(cl.ID_MSG_C2L_DungeonRecvAward),
		"ID_MSG_C2L_DungeonSpeedRecvAward":                            reflect.ValueOf(cl.ID_MSG_C2L_DungeonSpeedRecvAward),
		"ID_MSG_C2L_EmblemBuySlot":                                    reflect.ValueOf(cl.ID_MSG_C2L_EmblemBuySlot),
		"ID_MSG_C2L_EmblemCustomize":                                  reflect.ValueOf(cl.ID_MSG_C2L_EmblemCustomize),
		"ID_MSG_C2L_EmblemDecompose":                                  reflect.ValueOf(cl.ID_MSG_C2L_EmblemDecompose),
		"ID_MSG_C2L_EmblemGet":                                        reflect.ValueOf(cl.ID_MSG_C2L_EmblemGet),
		"ID_MSG_C2L_EmblemGrowTransfer":                               reflect.ValueOf(cl.ID_MSG_C2L_EmblemGrowTransfer),
		"ID_MSG_C2L_EmblemLevelUp":                                    reflect.ValueOf(cl.ID_MSG_C2L_EmblemLevelUp),
		"ID_MSG_C2L_EmblemSetAutoDecompose":                           reflect.ValueOf(cl.ID_MSG_C2L_EmblemSetAutoDecompose),
		"ID_MSG_C2L_EmblemSuccinct":                                   reflect.ValueOf(cl.ID_MSG_C2L_EmblemSuccinct),
		"ID_MSG_C2L_EmblemSuccinctItemConflate":                       reflect.ValueOf(cl.ID_MSG_C2L_EmblemSuccinctItemConflate),
		"ID_MSG_C2L_EmblemSuccinctLockOrSave":                         reflect.ValueOf(cl.ID_MSG_C2L_EmblemSuccinctLockOrSave),
		"ID_MSG_C2L_EmblemUpgrade":                                    reflect.ValueOf(cl.ID_MSG_C2L_EmblemUpgrade),
		"ID_MSG_C2L_EmblemWear":                                       reflect.ValueOf(cl.ID_MSG_C2L_EmblemWear),
		"ID_MSG_C2L_EquipDecompose":                                   reflect.ValueOf(cl.ID_MSG_C2L_EquipDecompose),
		"ID_MSG_C2L_EquipEnchant":                                     reflect.ValueOf(cl.ID_MSG_C2L_EquipEnchant),
		"ID_MSG_C2L_EquipEvolution":                                   reflect.ValueOf(cl.ID_MSG_C2L_EquipEvolution),
		"ID_MSG_C2L_EquipGet":                                         reflect.ValueOf(cl.ID_MSG_C2L_EquipGet),
		"ID_MSG_C2L_EquipGrowTransfer":                                reflect.ValueOf(cl.ID_MSG_C2L_EquipGrowTransfer),
		"ID_MSG_C2L_EquipMultipleStrength":                            reflect.ValueOf(cl.ID_MSG_C2L_EquipMultipleStrength),
		"ID_MSG_C2L_EquipRefine":                                      reflect.ValueOf(cl.ID_MSG_C2L_EquipRefine),
		"ID_MSG_C2L_EquipRevive":                                      reflect.ValueOf(cl.ID_MSG_C2L_EquipRevive),
		"ID_MSG_C2L_EquipSetAutoDecompose":                            reflect.ValueOf(cl.ID_MSG_C2L_EquipSetAutoDecompose),
		"ID_MSG_C2L_EquipStrength":                                    reflect.ValueOf(cl.ID_MSG_C2L_EquipStrength),
		"ID_MSG_C2L_EquipWear":                                        reflect.ValueOf(cl.ID_MSG_C2L_EquipWear),
		"ID_MSG_C2L_FlowerAssistRecvLike":                             reflect.ValueOf(cl.ID_MSG_C2L_FlowerAssistRecvLike),
		"ID_MSG_C2L_FlowerAssistSendLike":                             reflect.ValueOf(cl.ID_MSG_C2L_FlowerAssistSendLike),
		"ID_MSG_C2L_FlowerAttackLevelGuard":                           reflect.ValueOf(cl.ID_MSG_C2L_FlowerAttackLevelGuard),
		"ID_MSG_C2L_FlowerBuyOccupyAttackNum":                         reflect.ValueOf(cl.ID_MSG_C2L_FlowerBuyOccupyAttackNum),
		"ID_MSG_C2L_FlowerChangeGoblin":                               reflect.ValueOf(cl.ID_MSG_C2L_FlowerChangeGoblin),
		"ID_MSG_C2L_FlowerEnemiesInfo":                                reflect.ValueOf(cl.ID_MSG_C2L_FlowerEnemiesInfo),
		"ID_MSG_C2L_FlowerExtendOccupyTime":                           reflect.ValueOf(cl.ID_MSG_C2L_FlowerExtendOccupyTime),
		"ID_MSG_C2L_FlowerFeedGoblin":                                 reflect.ValueOf(cl.ID_MSG_C2L_FlowerFeedGoblin),
		"ID_MSG_C2L_FlowerFeedSpecial":                                reflect.ValueOf(cl.ID_MSG_C2L_FlowerFeedSpecial),
		"ID_MSG_C2L_FlowerHarvest":                                    reflect.ValueOf(cl.ID_MSG_C2L_FlowerHarvest),
		"ID_MSG_C2L_FlowerJungleInfo":                                 reflect.ValueOf(cl.ID_MSG_C2L_FlowerJungleInfo),
		"ID_MSG_C2L_FlowerLevelUp":                                    reflect.ValueOf(cl.ID_MSG_C2L_FlowerLevelUp),
		"ID_MSG_C2L_FlowerLogList":                                    reflect.ValueOf(cl.ID_MSG_C2L_FlowerLogList),
		"ID_MSG_C2L_FlowerMainInfo":                                   reflect.ValueOf(cl.ID_MSG_C2L_FlowerMainInfo),
		"ID_MSG_C2L_FlowerOccupyAssistList":                           reflect.ValueOf(cl.ID_MSG_C2L_FlowerOccupyAssistList),
		"ID_MSG_C2L_FlowerOccupyAttack":                               reflect.ValueOf(cl.ID_MSG_C2L_FlowerOccupyAttack),
		"ID_MSG_C2L_FlowerOccupyHistory":                              reflect.ValueOf(cl.ID_MSG_C2L_FlowerOccupyHistory),
		"ID_MSG_C2L_FlowerOccupyRecommend":                            reflect.ValueOf(cl.ID_MSG_C2L_FlowerOccupyRecommend),
		"ID_MSG_C2L_FlowerPreviewOccupyAward":                         reflect.ValueOf(cl.ID_MSG_C2L_FlowerPreviewOccupyAward),
		"ID_MSG_C2L_FlowerRecvOccupyAward":                            reflect.ValueOf(cl.ID_MSG_C2L_FlowerRecvOccupyAward),
		"ID_MSG_C2L_FlowerRecvPreviewOccupyAward":                     reflect.ValueOf(cl.ID_MSG_C2L_FlowerRecvPreviewOccupyAward),
		"ID_MSG_C2L_FlowerRevengeOccupy":                              reflect.ValueOf(cl.ID_MSG_C2L_FlowerRevengeOccupy),
		"ID_MSG_C2L_FlowerRevengeSnatch":                              reflect.ValueOf(cl.ID_MSG_C2L_FlowerRevengeSnatch),
		"ID_MSG_C2L_FlowerSearch":                                     reflect.ValueOf(cl.ID_MSG_C2L_FlowerSearch),
		"ID_MSG_C2L_FlowerShareFlowerbed":                             reflect.ValueOf(cl.ID_MSG_C2L_FlowerShareFlowerbed),
		"ID_MSG_C2L_FlowerSnatch":                                     reflect.ValueOf(cl.ID_MSG_C2L_FlowerSnatch),
		"ID_MSG_C2L_FlowerSpeedGrow":                                  reflect.ValueOf(cl.ID_MSG_C2L_FlowerSpeedGrow),
		"ID_MSG_C2L_FlowerStartFeed":                                  reflect.ValueOf(cl.ID_MSG_C2L_FlowerStartFeed),
		"ID_MSG_C2L_FlowerStartPlant":                                 reflect.ValueOf(cl.ID_MSG_C2L_FlowerStartPlant),
		"ID_MSG_C2L_FlowerTimberInfo":                                 reflect.ValueOf(cl.ID_MSG_C2L_FlowerTimberInfo),
		"ID_MSG_C2L_Flush":                                            reflect.ValueOf(cl.ID_MSG_C2L_Flush),
		"ID_MSG_C2L_FlushRedPoint":                                    reflect.ValueOf(cl.ID_MSG_C2L_FlushRedPoint),
		"ID_MSG_C2L_ForecastGetData":                                  reflect.ValueOf(cl.ID_MSG_C2L_ForecastGetData),
		"ID_MSG_C2L_ForecastReceiveAward":                             reflect.ValueOf(cl.ID_MSG_C2L_ForecastReceiveAward),
		"ID_MSG_C2L_ForestChangeGoblin":                               reflect.ValueOf(cl.ID_MSG_C2L_ForestChangeGoblin),
		"ID_MSG_C2L_ForestFeedGoblin":                                 reflect.ValueOf(cl.ID_MSG_C2L_ForestFeedGoblin),
		"ID_MSG_C2L_ForestFeedSpecial":                                reflect.ValueOf(cl.ID_MSG_C2L_ForestFeedSpecial),
		"ID_MSG_C2L_ForestHarvest":                                    reflect.ValueOf(cl.ID_MSG_C2L_ForestHarvest),
		"ID_MSG_C2L_ForestInfo":                                       reflect.ValueOf(cl.ID_MSG_C2L_ForestInfo),
		"ID_MSG_C2L_ForestLogList":                                    reflect.ValueOf(cl.ID_MSG_C2L_ForestLogList),
		"ID_MSG_C2L_ForestLoot":                                       reflect.ValueOf(cl.ID_MSG_C2L_ForestLoot),
		"ID_MSG_C2L_ForestRecvLvAward":                                reflect.ValueOf(cl.ID_MSG_C2L_ForestRecvLvAward),
		"ID_MSG_C2L_ForestRevenge":                                    reflect.ValueOf(cl.ID_MSG_C2L_ForestRevenge),
		"ID_MSG_C2L_ForestSearch":                                     reflect.ValueOf(cl.ID_MSG_C2L_ForestSearch),
		"ID_MSG_C2L_ForestSpeedGrow":                                  reflect.ValueOf(cl.ID_MSG_C2L_ForestSpeedGrow),
		"ID_MSG_C2L_ForestStartFeed":                                  reflect.ValueOf(cl.ID_MSG_C2L_ForestStartFeed),
		"ID_MSG_C2L_ForestStartPlant":                                 reflect.ValueOf(cl.ID_MSG_C2L_ForestStartPlant),
		"ID_MSG_C2L_ForestUpdateSlot":                                 reflect.ValueOf(cl.ID_MSG_C2L_ForestUpdateSlot),
		"ID_MSG_C2L_Formation":                                        reflect.ValueOf(cl.ID_MSG_C2L_Formation),
		"ID_MSG_C2L_FragmentCompose":                                  reflect.ValueOf(cl.ID_MSG_C2L_FragmentCompose),
		"ID_MSG_C2L_FragmentComposeAll":                               reflect.ValueOf(cl.ID_MSG_C2L_FragmentComposeAll),
		"ID_MSG_C2L_FragmentComposeTest":                              reflect.ValueOf(cl.ID_MSG_C2L_FragmentComposeTest),
		"ID_MSG_C2L_FragmentEmblemCompose":                            reflect.ValueOf(cl.ID_MSG_C2L_FragmentEmblemCompose),
		"ID_MSG_C2L_FriendAdd":                                        reflect.ValueOf(cl.ID_MSG_C2L_FriendAdd),
		"ID_MSG_C2L_FriendBlacklist":                                  reflect.ValueOf(cl.ID_MSG_C2L_FriendBlacklist),
		"ID_MSG_C2L_FriendConfirm":                                    reflect.ValueOf(cl.ID_MSG_C2L_FriendConfirm),
		"ID_MSG_C2L_FriendDelete":                                     reflect.ValueOf(cl.ID_MSG_C2L_FriendDelete),
		"ID_MSG_C2L_FriendGetBlacklist":                               reflect.ValueOf(cl.ID_MSG_C2L_FriendGetBlacklist),
		"ID_MSG_C2L_FriendInfo":                                       reflect.ValueOf(cl.ID_MSG_C2L_FriendInfo),
		"ID_MSG_C2L_FriendRecommend":                                  reflect.ValueOf(cl.ID_MSG_C2L_FriendRecommend),
		"ID_MSG_C2L_FriendRecvLike":                                   reflect.ValueOf(cl.ID_MSG_C2L_FriendRecvLike),
		"ID_MSG_C2L_FriendRemBlacklist":                               reflect.ValueOf(cl.ID_MSG_C2L_FriendRemBlacklist),
		"ID_MSG_C2L_FriendRequestInfo":                                reflect.ValueOf(cl.ID_MSG_C2L_FriendRequestInfo),
		"ID_MSG_C2L_FriendSearch":                                     reflect.ValueOf(cl.ID_MSG_C2L_FriendSearch),
		"ID_MSG_C2L_FriendSendLike":                                   reflect.ValueOf(cl.ID_MSG_C2L_FriendSendLike),
		"ID_MSG_C2L_FunctionGetStatus":                                reflect.ValueOf(cl.ID_MSG_C2L_FunctionGetStatus),
		"ID_MSG_C2L_GM":                                               reflect.ValueOf(cl.ID_MSG_C2L_GM),
		"ID_MSG_C2L_GSTArenaVote":                                     reflect.ValueOf(cl.ID_MSG_C2L_GSTArenaVote),
		"ID_MSG_C2L_GSTBossAward":                                     reflect.ValueOf(cl.ID_MSG_C2L_GSTBossAward),
		"ID_MSG_C2L_GSTBossBuyChallenge":                              reflect.ValueOf(cl.ID_MSG_C2L_GSTBossBuyChallenge),
		"ID_MSG_C2L_GSTBossFight":                                     reflect.ValueOf(cl.ID_MSG_C2L_GSTBossFight),
		"ID_MSG_C2L_GSTBossGet":                                       reflect.ValueOf(cl.ID_MSG_C2L_GSTBossGet),
		"ID_MSG_C2L_GSTBossRank":                                      reflect.ValueOf(cl.ID_MSG_C2L_GSTBossRank),
		"ID_MSG_C2L_GSTChallengeBuffChoose":                           reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeBuffChoose),
		"ID_MSG_C2L_GSTChallengeFight":                                reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeFight),
		"ID_MSG_C2L_GSTChallengeFightLogList":                         reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeFightLogList),
		"ID_MSG_C2L_GSTChallengeGetData":                              reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeGetData),
		"ID_MSG_C2L_GSTChallengeMatch":                                reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeMatch),
		"ID_MSG_C2L_GSTChallengeRank":                                 reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeRank),
		"ID_MSG_C2L_GSTChallengeTaskReward":                           reflect.ValueOf(cl.ID_MSG_C2L_GSTChallengeTaskReward),
		"ID_MSG_C2L_GSTDonate":                                        reflect.ValueOf(cl.ID_MSG_C2L_GSTDonate),
		"ID_MSG_C2L_GSTDragonEvolve":                                  reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonEvolve),
		"ID_MSG_C2L_GSTDragonFight":                                   reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonFight),
		"ID_MSG_C2L_GSTDragonFightBuyCount":                           reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonFightBuyCount),
		"ID_MSG_C2L_GSTDragonFightUseTicket":                          reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonFightUseTicket),
		"ID_MSG_C2L_GSTDragonGetCultivation":                          reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonGetCultivation),
		"ID_MSG_C2L_GSTDragonGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonGetData),
		"ID_MSG_C2L_GSTDragonRank":                                    reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonRank),
		"ID_MSG_C2L_GSTDragonShow":                                    reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonShow),
		"ID_MSG_C2L_GSTDragonSkillOperate":                            reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonSkillOperate),
		"ID_MSG_C2L_GSTDragonStrategySkill":                           reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonStrategySkill),
		"ID_MSG_C2L_GSTDragonTaskAward":                               reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonTaskAward),
		"ID_MSG_C2L_GSTDragonTaskGetData":                             reflect.ValueOf(cl.ID_MSG_C2L_GSTDragonTaskGetData),
		"ID_MSG_C2L_GSTExchangeGroundTeam":                            reflect.ValueOf(cl.ID_MSG_C2L_GSTExchangeGroundTeam),
		"ID_MSG_C2L_GSTGetArenaVoteRecord":                            reflect.ValueOf(cl.ID_MSG_C2L_GSTGetArenaVoteRecord),
		"ID_MSG_C2L_GSTGetData":                                       reflect.ValueOf(cl.ID_MSG_C2L_GSTGetData),
		"ID_MSG_C2L_GSTGetGroundData":                                 reflect.ValueOf(cl.ID_MSG_C2L_GSTGetGroundData),
		"ID_MSG_C2L_GSTGetGuildDonateData":                            reflect.ValueOf(cl.ID_MSG_C2L_GSTGetGuildDonateData),
		"ID_MSG_C2L_GSTGetGuildDonateMemData":                         reflect.ValueOf(cl.ID_MSG_C2L_GSTGetGuildDonateMemData),
		"ID_MSG_C2L_GSTGetHangUpReward":                               reflect.ValueOf(cl.ID_MSG_C2L_GSTGetHangUpReward),
		"ID_MSG_C2L_GSTGetLogData":                                    reflect.ValueOf(cl.ID_MSG_C2L_GSTGetLogData),
		"ID_MSG_C2L_GSTGetTasksData":                                  reflect.ValueOf(cl.ID_MSG_C2L_GSTGetTasksData),
		"ID_MSG_C2L_GSTGetTasksReward":                                reflect.ValueOf(cl.ID_MSG_C2L_GSTGetTasksReward),
		"ID_MSG_C2L_GSTGetTeamsData":                                  reflect.ValueOf(cl.ID_MSG_C2L_GSTGetTeamsData),
		"ID_MSG_C2L_GSTGroupUsersRank":                                reflect.ValueOf(cl.ID_MSG_C2L_GSTGroupUsersRank),
		"ID_MSG_C2L_GSTGroupUsersRankLike":                            reflect.ValueOf(cl.ID_MSG_C2L_GSTGroupUsersRankLike),
		"ID_MSG_C2L_GSTGuildBuildDispatchHero":                        reflect.ValueOf(cl.ID_MSG_C2L_GSTGuildBuildDispatchHero),
		"ID_MSG_C2L_GSTGuildBuildDonate":                              reflect.ValueOf(cl.ID_MSG_C2L_GSTGuildBuildDonate),
		"ID_MSG_C2L_GSTGuildBuildDonateRank":                          reflect.ValueOf(cl.ID_MSG_C2L_GSTGuildBuildDonateRank),
		"ID_MSG_C2L_GSTGuildBuildGetData":                             reflect.ValueOf(cl.ID_MSG_C2L_GSTGuildBuildGetData),
		"ID_MSG_C2L_GSTGuildBuildGetTaskData":                         reflect.ValueOf(cl.ID_MSG_C2L_GSTGuildBuildGetTaskData),
		"ID_MSG_C2L_GSTGuildBuildRecvTaskAward":                       reflect.ValueOf(cl.ID_MSG_C2L_GSTGuildBuildRecvTaskAward),
		"ID_MSG_C2L_GSTMessageEdit":                                   reflect.ValueOf(cl.ID_MSG_C2L_GSTMessageEdit),
		"ID_MSG_C2L_GSTOreBuyTimes":                                   reflect.ValueOf(cl.ID_MSG_C2L_GSTOreBuyTimes),
		"ID_MSG_C2L_GSTOreFight":                                      reflect.ValueOf(cl.ID_MSG_C2L_GSTOreFight),
		"ID_MSG_C2L_GSTOreGetData":                                    reflect.ValueOf(cl.ID_MSG_C2L_GSTOreGetData),
		"ID_MSG_C2L_GSTOreGetOreData":                                 reflect.ValueOf(cl.ID_MSG_C2L_GSTOreGetOreData),
		"ID_MSG_C2L_GSTOreOccupy":                                     reflect.ValueOf(cl.ID_MSG_C2L_GSTOreOccupy),
		"ID_MSG_C2L_GSTOreSearchAssist":                               reflect.ValueOf(cl.ID_MSG_C2L_GSTOreSearchAssist),
		"ID_MSG_C2L_GSTPreviewHangUpReward":                           reflect.ValueOf(cl.ID_MSG_C2L_GSTPreviewHangUpReward),
		"ID_MSG_C2L_GSTRank":                                          reflect.ValueOf(cl.ID_MSG_C2L_GSTRank),
		"ID_MSG_C2L_GSTScorePreview":                                  reflect.ValueOf(cl.ID_MSG_C2L_GSTScorePreview),
		"ID_MSG_C2L_GSTSkillAssemble":                                 reflect.ValueOf(cl.ID_MSG_C2L_GSTSkillAssemble),
		"ID_MSG_C2L_GSTTeamOperate":                                   reflect.ValueOf(cl.ID_MSG_C2L_GSTTeamOperate),
		"ID_MSG_C2L_GSTTechDonate":                                    reflect.ValueOf(cl.ID_MSG_C2L_GSTTechDonate),
		"ID_MSG_C2L_GSTTechGetData":                                   reflect.ValueOf(cl.ID_MSG_C2L_GSTTechGetData),
		"ID_MSG_C2L_GSTTechGuildUserRank":                             reflect.ValueOf(cl.ID_MSG_C2L_GSTTechGuildUserRank),
		"ID_MSG_C2L_GSTTechSign":                                      reflect.ValueOf(cl.ID_MSG_C2L_GSTTechSign),
		"ID_MSG_C2L_GSTTechTaskReward":                                reflect.ValueOf(cl.ID_MSG_C2L_GSTTechTaskReward),
		"ID_MSG_C2L_GemCompose":                                       reflect.ValueOf(cl.ID_MSG_C2L_GemCompose),
		"ID_MSG_C2L_GemConvert":                                       reflect.ValueOf(cl.ID_MSG_C2L_GemConvert),
		"ID_MSG_C2L_GemDecompose":                                     reflect.ValueOf(cl.ID_MSG_C2L_GemDecompose),
		"ID_MSG_C2L_GemWear":                                          reflect.ValueOf(cl.ID_MSG_C2L_GemWear),
		"ID_MSG_C2L_GetAchieveShowcase":                               reflect.ValueOf(cl.ID_MSG_C2L_GetAchieveShowcase),
		"ID_MSG_C2L_GetBattleReport":                                  reflect.ValueOf(cl.ID_MSG_C2L_GetBattleReport),
		"ID_MSG_C2L_GetClientInfo":                                    reflect.ValueOf(cl.ID_MSG_C2L_GetClientInfo),
		"ID_MSG_C2L_GetCognitionLog":                                  reflect.ValueOf(cl.ID_MSG_C2L_GetCognitionLog),
		"ID_MSG_C2L_GetCommonRank":                                    reflect.ValueOf(cl.ID_MSG_C2L_GetCommonRank),
		"ID_MSG_C2L_GetCommonRankFirst":                               reflect.ValueOf(cl.ID_MSG_C2L_GetCommonRankFirst),
		"ID_MSG_C2L_GetCrossRankFirst":                                reflect.ValueOf(cl.ID_MSG_C2L_GetCrossRankFirst),
		"ID_MSG_C2L_GetDefFormationPower":                             reflect.ValueOf(cl.ID_MSG_C2L_GetDefFormationPower),
		"ID_MSG_C2L_GetFormation":                                     reflect.ValueOf(cl.ID_MSG_C2L_GetFormation),
		"ID_MSG_C2L_GetGems":                                          reflect.ValueOf(cl.ID_MSG_C2L_GetGems),
		"ID_MSG_C2L_GetGiftCodeAward":                                 reflect.ValueOf(cl.ID_MSG_C2L_GetGiftCodeAward),
		"ID_MSG_C2L_GetMails":                                         reflect.ValueOf(cl.ID_MSG_C2L_GetMails),
		"ID_MSG_C2L_GetPush":                                          reflect.ValueOf(cl.ID_MSG_C2L_GetPush),
		"ID_MSG_C2L_GetSeasonFlashBackData":                           reflect.ValueOf(cl.ID_MSG_C2L_GetSeasonFlashBackData),
		"ID_MSG_C2L_GetSeasonRankFirst":                               reflect.ValueOf(cl.ID_MSG_C2L_GetSeasonRankFirst),
		"ID_MSG_C2L_GetSeasonRankList":                                reflect.ValueOf(cl.ID_MSG_C2L_GetSeasonRankList),
		"ID_MSG_C2L_GetUserBattleData":                                reflect.ValueOf(cl.ID_MSG_C2L_GetUserBattleData),
		"ID_MSG_C2L_GetUserSnapshots":                                 reflect.ValueOf(cl.ID_MSG_C2L_GetUserSnapshots),
		"ID_MSG_C2L_GlobalAttrGet":                                    reflect.ValueOf(cl.ID_MSG_C2L_GlobalAttrGet),
		"ID_MSG_C2L_GlobalAttrScoreGet":                               reflect.ValueOf(cl.ID_MSG_C2L_GlobalAttrScoreGet),
		"ID_MSG_C2L_GodPresentGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_GodPresentGetData),
		"ID_MSG_C2L_GodPresentRecvAwards":                             reflect.ValueOf(cl.ID_MSG_C2L_GodPresentRecvAwards),
		"ID_MSG_C2L_GodPresentRecvItem":                               reflect.ValueOf(cl.ID_MSG_C2L_GodPresentRecvItem),
		"ID_MSG_C2L_GodPresentSummon":                                 reflect.ValueOf(cl.ID_MSG_C2L_GodPresentSummon),
		"ID_MSG_C2L_GoddessChangeSuit":                                reflect.ValueOf(cl.ID_MSG_C2L_GoddessChangeSuit),
		"ID_MSG_C2L_GoddessChapterFight":                              reflect.ValueOf(cl.ID_MSG_C2L_GoddessChapterFight),
		"ID_MSG_C2L_GoddessCollect":                                   reflect.ValueOf(cl.ID_MSG_C2L_GoddessCollect),
		"ID_MSG_C2L_GoddessContractGetData":                           reflect.ValueOf(cl.ID_MSG_C2L_GoddessContractGetData),
		"ID_MSG_C2L_GoddessFeed":                                      reflect.ValueOf(cl.ID_MSG_C2L_GoddessFeed),
		"ID_MSG_C2L_GoddessRecovery":                                  reflect.ValueOf(cl.ID_MSG_C2L_GoddessRecovery),
		"ID_MSG_C2L_GoddessStoryAward":                                reflect.ValueOf(cl.ID_MSG_C2L_GoddessStoryAward),
		"ID_MSG_C2L_GoddessSuitUnlock":                                reflect.ValueOf(cl.ID_MSG_C2L_GoddessSuitUnlock),
		"ID_MSG_C2L_GoddessTouch":                                     reflect.ValueOf(cl.ID_MSG_C2L_GoddessTouch),
		"ID_MSG_C2L_GoddessUnlock":                                    reflect.ValueOf(cl.ID_MSG_C2L_GoddessUnlock),
		"ID_MSG_C2L_GoldBuyGet":                                       reflect.ValueOf(cl.ID_MSG_C2L_GoldBuyGet),
		"ID_MSG_C2L_GoldBuyGetGold":                                   reflect.ValueOf(cl.ID_MSG_C2L_GoldBuyGetGold),
		"ID_MSG_C2L_GuidanceFinishGroup":                              reflect.ValueOf(cl.ID_MSG_C2L_GuidanceFinishGroup),
		"ID_MSG_C2L_GuidanceFinishStep":                               reflect.ValueOf(cl.ID_MSG_C2L_GuidanceFinishStep),
		"ID_MSG_C2L_GuidanceList":                                     reflect.ValueOf(cl.ID_MSG_C2L_GuidanceList),
		"ID_MSG_C2L_GuidanceSkip":                                     reflect.ValueOf(cl.ID_MSG_C2L_GuidanceSkip),
		"ID_MSG_C2L_GuildApplyList":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildApplyList),
		"ID_MSG_C2L_GuildApplyRatify":                                 reflect.ValueOf(cl.ID_MSG_C2L_GuildApplyRatify),
		"ID_MSG_C2L_GuildChestActivate":                               reflect.ValueOf(cl.ID_MSG_C2L_GuildChestActivate),
		"ID_MSG_C2L_GuildChestGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_GuildChestGetData),
		"ID_MSG_C2L_GuildChestLike":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildChestLike),
		"ID_MSG_C2L_GuildChestRecv":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildChestRecv),
		"ID_MSG_C2L_GuildCombineApply":                                reflect.ValueOf(cl.ID_MSG_C2L_GuildCombineApply),
		"ID_MSG_C2L_GuildCombineCheck":                                reflect.ValueOf(cl.ID_MSG_C2L_GuildCombineCheck),
		"ID_MSG_C2L_GuildCombineRatify":                               reflect.ValueOf(cl.ID_MSG_C2L_GuildCombineRatify),
		"ID_MSG_C2L_GuildCreate":                                      reflect.ValueOf(cl.ID_MSG_C2L_GuildCreate),
		"ID_MSG_C2L_GuildDisband":                                     reflect.ValueOf(cl.ID_MSG_C2L_GuildDisband),
		"ID_MSG_C2L_GuildDonate":                                      reflect.ValueOf(cl.ID_MSG_C2L_GuildDonate),
		"ID_MSG_C2L_GuildDonateLogList":                               reflect.ValueOf(cl.ID_MSG_C2L_GuildDonateLogList),
		"ID_MSG_C2L_GuildDungeonBuyChallengeTimes":                    reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonBuyChallengeTimes),
		"ID_MSG_C2L_GuildDungeonChapterInfo":                          reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonChapterInfo),
		"ID_MSG_C2L_GuildDungeonFight":                                reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonFight),
		"ID_MSG_C2L_GuildDungeonGetMembersFightInfo":                  reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonGetMembersFightInfo),
		"ID_MSG_C2L_GuildDungeonGetStrategy":                          reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonGetStrategy),
		"ID_MSG_C2L_GuildDungeonHallOfFame":                           reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonHallOfFame),
		"ID_MSG_C2L_GuildDungeonInfo":                                 reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonInfo),
		"ID_MSG_C2L_GuildDungeonLogList":                              reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonLogList),
		"ID_MSG_C2L_GuildDungeonRecvAllBossBoxAward":                  reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonRecvAllBossBoxAward),
		"ID_MSG_C2L_GuildDungeonRecvBossBoxAward":                     reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonRecvBossBoxAward),
		"ID_MSG_C2L_GuildDungeonRecvChapterTaskAward":                 reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonRecvChapterTaskAward),
		"ID_MSG_C2L_GuildDungeonSeasonDivisionAward":                  reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonSeasonDivisionAward),
		"ID_MSG_C2L_GuildDungeonSetFocus":                             reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonSetFocus),
		"ID_MSG_C2L_GuildDungeonTop3Guild":                            reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonTop3Guild),
		"ID_MSG_C2L_GuildDungeonUseStrategy":                          reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonUseStrategy),
		"ID_MSG_C2L_GuildDungeonUserDamageRank":                       reflect.ValueOf(cl.ID_MSG_C2L_GuildDungeonUserDamageRank),
		"ID_MSG_C2L_GuildGetBadgeList":                                reflect.ValueOf(cl.ID_MSG_C2L_GuildGetBadgeList),
		"ID_MSG_C2L_GuildGetDeclaration":                              reflect.ValueOf(cl.ID_MSG_C2L_GuildGetDeclaration),
		"ID_MSG_C2L_GuildGetDivisionAwardInfo":                        reflect.ValueOf(cl.ID_MSG_C2L_GuildGetDivisionAwardInfo),
		"ID_MSG_C2L_GuildGetDonateAward":                              reflect.ValueOf(cl.ID_MSG_C2L_GuildGetDonateAward),
		"ID_MSG_C2L_GuildGetMedals":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildGetMedals),
		"ID_MSG_C2L_GuildGetMembers":                                  reflect.ValueOf(cl.ID_MSG_C2L_GuildGetMembers),
		"ID_MSG_C2L_GuildGetMyInfo":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildGetMyInfo),
		"ID_MSG_C2L_GuildList":                                        reflect.ValueOf(cl.ID_MSG_C2L_GuildList),
		"ID_MSG_C2L_GuildListGetDetail":                               reflect.ValueOf(cl.ID_MSG_C2L_GuildListGetDetail),
		"ID_MSG_C2L_GuildLogList":                                     reflect.ValueOf(cl.ID_MSG_C2L_GuildLogList),
		"ID_MSG_C2L_GuildMainInfo":                                    reflect.ValueOf(cl.ID_MSG_C2L_GuildMainInfo),
		"ID_MSG_C2L_GuildManagerMember":                               reflect.ValueOf(cl.ID_MSG_C2L_GuildManagerMember),
		"ID_MSG_C2L_GuildMedalLike":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildMedalLike),
		"ID_MSG_C2L_GuildMobilizationAcceptTask":                      reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationAcceptTask),
		"ID_MSG_C2L_GuildMobilizationBuyTimes":                        reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationBuyTimes),
		"ID_MSG_C2L_GuildMobilizationEditMessageBoard":                reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationEditMessageBoard),
		"ID_MSG_C2L_GuildMobilizationFinishTaskLogs":                  reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationFinishTaskLogs),
		"ID_MSG_C2L_GuildMobilizationFreshTask":                       reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationFreshTask),
		"ID_MSG_C2L_GuildMobilizationGetData":                         reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationGetData),
		"ID_MSG_C2L_GuildMobilizationGiveUpTask":                      reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationGiveUpTask),
		"ID_MSG_C2L_GuildMobilizationGuildRank":                       reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationGuildRank),
		"ID_MSG_C2L_GuildMobilizationPersonalRank":                    reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationPersonalRank),
		"ID_MSG_C2L_GuildMobilizationRecvScoreLevel":                  reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationRecvScoreLevel),
		"ID_MSG_C2L_GuildMobilizationScoreAward":                      reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationScoreAward),
		"ID_MSG_C2L_GuildMobilizationSignTask":                        reflect.ValueOf(cl.ID_MSG_C2L_GuildMobilizationSignTask),
		"ID_MSG_C2L_GuildModifyInfo":                                  reflect.ValueOf(cl.ID_MSG_C2L_GuildModifyInfo),
		"ID_MSG_C2L_GuildModifyNotice":                                reflect.ValueOf(cl.ID_MSG_C2L_GuildModifyNotice),
		"ID_MSG_C2L_GuildQuit":                                        reflect.ValueOf(cl.ID_MSG_C2L_GuildQuit),
		"ID_MSG_C2L_GuildRank":                                        reflect.ValueOf(cl.ID_MSG_C2L_GuildRank),
		"ID_MSG_C2L_GuildSearch":                                      reflect.ValueOf(cl.ID_MSG_C2L_GuildSearch),
		"ID_MSG_C2L_GuildSendMail":                                    reflect.ValueOf(cl.ID_MSG_C2L_GuildSendMail),
		"ID_MSG_C2L_GuildSendRecruitMsg":                              reflect.ValueOf(cl.ID_MSG_C2L_GuildSendRecruitMsg),
		"ID_MSG_C2L_GuildSetName":                                     reflect.ValueOf(cl.ID_MSG_C2L_GuildSetName),
		"ID_MSG_C2L_GuildTalentLevelUp":                               reflect.ValueOf(cl.ID_MSG_C2L_GuildTalentLevelUp),
		"ID_MSG_C2L_GuildTalentList":                                  reflect.ValueOf(cl.ID_MSG_C2L_GuildTalentList),
		"ID_MSG_C2L_GuildTalentReset":                                 reflect.ValueOf(cl.ID_MSG_C2L_GuildTalentReset),
		"ID_MSG_C2L_GuildUserApply":                                   reflect.ValueOf(cl.ID_MSG_C2L_GuildUserApply),
		"ID_MSG_C2L_HandbooksActive":                                  reflect.ValueOf(cl.ID_MSG_C2L_HandbooksActive),
		"ID_MSG_C2L_HandbooksActiveHeroAttr":                          reflect.ValueOf(cl.ID_MSG_C2L_HandbooksActiveHeroAttr),
		"ID_MSG_C2L_HandbooksGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_HandbooksGetData),
		"ID_MSG_C2L_HandbooksReceiveAwards":                           reflect.ValueOf(cl.ID_MSG_C2L_HandbooksReceiveAwards),
		"ID_MSG_C2L_HasRecvH5DesktopReward":                           reflect.ValueOf(cl.ID_MSG_C2L_HasRecvH5DesktopReward),
		"ID_MSG_C2L_HeroAwaken":                                       reflect.ValueOf(cl.ID_MSG_C2L_HeroAwaken),
		"ID_MSG_C2L_HeroBack":                                         reflect.ValueOf(cl.ID_MSG_C2L_HeroBack),
		"ID_MSG_C2L_HeroBuySlot":                                      reflect.ValueOf(cl.ID_MSG_C2L_HeroBuySlot),
		"ID_MSG_C2L_HeroConversion":                                   reflect.ValueOf(cl.ID_MSG_C2L_HeroConversion),
		"ID_MSG_C2L_HeroConvert":                                      reflect.ValueOf(cl.ID_MSG_C2L_HeroConvert),
		"ID_MSG_C2L_HeroConvertAwakenItem":                            reflect.ValueOf(cl.ID_MSG_C2L_HeroConvertAwakenItem),
		"ID_MSG_C2L_HeroDecompose":                                    reflect.ValueOf(cl.ID_MSG_C2L_HeroDecompose),
		"ID_MSG_C2L_HeroExchange":                                     reflect.ValueOf(cl.ID_MSG_C2L_HeroExchange),
		"ID_MSG_C2L_HeroGemLevelUp":                                   reflect.ValueOf(cl.ID_MSG_C2L_HeroGemLevelUp),
		"ID_MSG_C2L_HeroGetStarUpCosts":                               reflect.ValueOf(cl.ID_MSG_C2L_HeroGetStarUpCosts),
		"ID_MSG_C2L_HeroLevelUp":                                      reflect.ValueOf(cl.ID_MSG_C2L_HeroLevelUp),
		"ID_MSG_C2L_HeroList":                                         reflect.ValueOf(cl.ID_MSG_C2L_HeroList),
		"ID_MSG_C2L_HeroRevive":                                       reflect.ValueOf(cl.ID_MSG_C2L_HeroRevive),
		"ID_MSG_C2L_HeroStageUp":                                      reflect.ValueOf(cl.ID_MSG_C2L_HeroStageUp),
		"ID_MSG_C2L_HeroStarUp":                                       reflect.ValueOf(cl.ID_MSG_C2L_HeroStarUp),
		"ID_MSG_C2L_HeroStarUpRedList":                                reflect.ValueOf(cl.ID_MSG_C2L_HeroStarUpRedList),
		"ID_MSG_C2L_HeroTagUpdate":                                    reflect.ValueOf(cl.ID_MSG_C2L_HeroTagUpdate),
		"ID_MSG_C2L_HeroTestAttr":                                     reflect.ValueOf(cl.ID_MSG_C2L_HeroTestAttr),
		"ID_MSG_C2L_HeroTestCalPower":                                 reflect.ValueOf(cl.ID_MSG_C2L_HeroTestCalPower),
		"ID_MSG_C2L_HeroUpdateLockStatus":                             reflect.ValueOf(cl.ID_MSG_C2L_HeroUpdateLockStatus),
		"ID_MSG_C2L_HotRankGet":                                       reflect.ValueOf(cl.ID_MSG_C2L_HotRankGet),
		"ID_MSG_C2L_ItemSelect":                                       reflect.ValueOf(cl.ID_MSG_C2L_ItemSelect),
		"ID_MSG_C2L_KeepAlive":                                        reflect.ValueOf(cl.ID_MSG_C2L_KeepAlive),
		"ID_MSG_C2L_LinkHeroSummon":                                   reflect.ValueOf(cl.ID_MSG_C2L_LinkHeroSummon),
		"ID_MSG_C2L_LinkHeroSummonGet":                                reflect.ValueOf(cl.ID_MSG_C2L_LinkHeroSummonGet),
		"ID_MSG_C2L_LinkHeroSummonTest":                               reflect.ValueOf(cl.ID_MSG_C2L_LinkHeroSummonTest),
		"ID_MSG_C2L_LinkInfo":                                         reflect.ValueOf(cl.ID_MSG_C2L_LinkInfo),
		"ID_MSG_C2L_LinkSetView":                                      reflect.ValueOf(cl.ID_MSG_C2L_LinkSetView),
		"ID_MSG_C2L_MazeBuyRevive":                                    reflect.ValueOf(cl.ID_MSG_C2L_MazeBuyRevive),
		"ID_MSG_C2L_MazeGetGrid":                                      reflect.ValueOf(cl.ID_MSG_C2L_MazeGetGrid),
		"ID_MSG_C2L_MazeGetMap":                                       reflect.ValueOf(cl.ID_MSG_C2L_MazeGetMap),
		"ID_MSG_C2L_MazeGetSelectMapData":                             reflect.ValueOf(cl.ID_MSG_C2L_MazeGetSelectMapData),
		"ID_MSG_C2L_MazeRecoveryHero":                                 reflect.ValueOf(cl.ID_MSG_C2L_MazeRecoveryHero),
		"ID_MSG_C2L_MazeSelectBuff":                                   reflect.ValueOf(cl.ID_MSG_C2L_MazeSelectBuff),
		"ID_MSG_C2L_MazeSweep":                                        reflect.ValueOf(cl.ID_MSG_C2L_MazeSweep),
		"ID_MSG_C2L_MazeTaskReceiveAward":                             reflect.ValueOf(cl.ID_MSG_C2L_MazeTaskReceiveAward),
		"ID_MSG_C2L_MazeTriggerEvent":                                 reflect.ValueOf(cl.ID_MSG_C2L_MazeTriggerEvent),
		"ID_MSG_C2L_MazeUseItem":                                      reflect.ValueOf(cl.ID_MSG_C2L_MazeUseItem),
		"ID_MSG_C2L_MedalGetData":                                     reflect.ValueOf(cl.ID_MSG_C2L_MedalGetData),
		"ID_MSG_C2L_MedalReceiveAward":                                reflect.ValueOf(cl.ID_MSG_C2L_MedalReceiveAward),
		"ID_MSG_C2L_MemoryLatest":                                     reflect.ValueOf(cl.ID_MSG_C2L_MemoryLatest),
		"ID_MSG_C2L_MemoryUnlock":                                     reflect.ValueOf(cl.ID_MSG_C2L_MemoryUnlock),
		"ID_MSG_C2L_MirageBuyCount":                                   reflect.ValueOf(cl.ID_MSG_C2L_MirageBuyCount),
		"ID_MSG_C2L_MirageDetail":                                     reflect.ValueOf(cl.ID_MSG_C2L_MirageDetail),
		"ID_MSG_C2L_MirageFight":                                      reflect.ValueOf(cl.ID_MSG_C2L_MirageFight),
		"ID_MSG_C2L_MirageList":                                       reflect.ValueOf(cl.ID_MSG_C2L_MirageList),
		"ID_MSG_C2L_MiragePowerCrush":                                 reflect.ValueOf(cl.ID_MSG_C2L_MiragePowerCrush),
		"ID_MSG_C2L_MirageReceiveAward":                               reflect.ValueOf(cl.ID_MSG_C2L_MirageReceiveAward),
		"ID_MSG_C2L_MirageSaveAffixes":                                reflect.ValueOf(cl.ID_MSG_C2L_MirageSaveAffixes),
		"ID_MSG_C2L_MirageSweep":                                      reflect.ValueOf(cl.ID_MSG_C2L_MirageSweep),
		"ID_MSG_C2L_MirageTestDrop":                                   reflect.ValueOf(cl.ID_MSG_C2L_MirageTestDrop),
		"ID_MSG_C2L_MonthTasksGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_MonthTasksGetData),
		"ID_MSG_C2L_MonthTasksRecvAwards":                             reflect.ValueOf(cl.ID_MSG_C2L_MonthTasksRecvAwards),
		"ID_MSG_C2L_MonthlyCardGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_MonthlyCardGetData),
		"ID_MSG_C2L_MonthlyCardReceiveAward":                          reflect.ValueOf(cl.ID_MSG_C2L_MonthlyCardReceiveAward),
		"ID_MSG_C2L_MuteAccount":                                      reflect.ValueOf(cl.ID_MSG_C2L_MuteAccount),
		"ID_MSG_C2L_NewYearActivityGetData":                           reflect.ValueOf(cl.ID_MSG_C2L_NewYearActivityGetData),
		"ID_MSG_C2L_NewYearActivityLoginAward":                        reflect.ValueOf(cl.ID_MSG_C2L_NewYearActivityLoginAward),
		"ID_MSG_C2L_OSSUrl":                                           reflect.ValueOf(cl.ID_MSG_C2L_OSSUrl),
		"ID_MSG_C2L_OperateActivityGetData":                           reflect.ValueOf(cl.ID_MSG_C2L_OperateActivityGetData),
		"ID_MSG_C2L_OperateActivityGetTaskReward":                     reflect.ValueOf(cl.ID_MSG_C2L_OperateActivityGetTaskReward),
		"ID_MSG_C2L_OperateActivityGetXML":                            reflect.ValueOf(cl.ID_MSG_C2L_OperateActivityGetXML),
		"ID_MSG_C2L_OperateActivityInitActivity":                      reflect.ValueOf(cl.ID_MSG_C2L_OperateActivityInitActivity),
		"ID_MSG_C2L_OperateActivityPromotionRechargeCheck":            reflect.ValueOf(cl.ID_MSG_C2L_OperateActivityPromotionRechargeCheck),
		"ID_MSG_C2L_OperateActivityPromotionSelectAward":              reflect.ValueOf(cl.ID_MSG_C2L_OperateActivityPromotionSelectAward),
		"ID_MSG_C2L_OssBattleReport":                                  reflect.ValueOf(cl.ID_MSG_C2L_OssBattleReport),
		"ID_MSG_C2L_PassGetData":                                      reflect.ValueOf(cl.ID_MSG_C2L_PassGetData),
		"ID_MSG_C2L_PassLevelBuy":                                     reflect.ValueOf(cl.ID_MSG_C2L_PassLevelBuy),
		"ID_MSG_C2L_PassReceiveAward":                                 reflect.ValueOf(cl.ID_MSG_C2L_PassReceiveAward),
		"ID_MSG_C2L_PeakBaseData":                                     reflect.ValueOf(cl.ID_MSG_C2L_PeakBaseData),
		"ID_MSG_C2L_PeakDoGuess":                                      reflect.ValueOf(cl.ID_MSG_C2L_PeakDoGuess),
		"ID_MSG_C2L_PeakFighterDetail":                                reflect.ValueOf(cl.ID_MSG_C2L_PeakFighterDetail),
		"ID_MSG_C2L_PeakGetLastBattleReport":                          reflect.ValueOf(cl.ID_MSG_C2L_PeakGetLastBattleReport),
		"ID_MSG_C2L_PeakGetMatch":                                     reflect.ValueOf(cl.ID_MSG_C2L_PeakGetMatch),
		"ID_MSG_C2L_PeakGuessList":                                    reflect.ValueOf(cl.ID_MSG_C2L_PeakGuessList),
		"ID_MSG_C2L_PeakRankList":                                     reflect.ValueOf(cl.ID_MSG_C2L_PeakRankList),
		"ID_MSG_C2L_PeakRecvInviteReward":                             reflect.ValueOf(cl.ID_MSG_C2L_PeakRecvInviteReward),
		"ID_MSG_C2L_PeakWorship":                                      reflect.ValueOf(cl.ID_MSG_C2L_PeakWorship),
		"ID_MSG_C2L_PreSeasonGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_PreSeasonGetData),
		"ID_MSG_C2L_PreSeasonRecvAward":                               reflect.ValueOf(cl.ID_MSG_C2L_PreSeasonRecvAward),
		"ID_MSG_C2L_PushGiftGetData":                                  reflect.ValueOf(cl.ID_MSG_C2L_PushGiftGetData),
		"ID_MSG_C2L_PyramidChooseAward":                               reflect.ValueOf(cl.ID_MSG_C2L_PyramidChooseAward),
		"ID_MSG_C2L_PyramidDraw":                                      reflect.ValueOf(cl.ID_MSG_C2L_PyramidDraw),
		"ID_MSG_C2L_PyramidGetData":                                   reflect.ValueOf(cl.ID_MSG_C2L_PyramidGetData),
		"ID_MSG_C2L_PyramidReceiveAwards":                             reflect.ValueOf(cl.ID_MSG_C2L_PyramidReceiveAwards),
		"ID_MSG_C2L_PyramidTestDraw":                                  reflect.ValueOf(cl.ID_MSG_C2L_PyramidTestDraw),
		"ID_MSG_C2L_RankAchieveList":                                  reflect.ValueOf(cl.ID_MSG_C2L_RankAchieveList),
		"ID_MSG_C2L_RankAchieveRecvAward":                             reflect.ValueOf(cl.ID_MSG_C2L_RankAchieveRecvAward),
		"ID_MSG_C2L_RateGetStatus":                                    reflect.ValueOf(cl.ID_MSG_C2L_RateGetStatus),
		"ID_MSG_C2L_RateScore":                                        reflect.ValueOf(cl.ID_MSG_C2L_RateScore),
		"ID_MSG_C2L_ReadMail":                                         reflect.ValueOf(cl.ID_MSG_C2L_ReadMail),
		"ID_MSG_C2L_RechargeByCoupon":                                 reflect.ValueOf(cl.ID_MSG_C2L_RechargeByCoupon),
		"ID_MSG_C2L_RechargeFirstGiftReward":                          reflect.ValueOf(cl.ID_MSG_C2L_RechargeFirstGiftReward),
		"ID_MSG_C2L_RechargeGetData":                                  reflect.ValueOf(cl.ID_MSG_C2L_RechargeGetData),
		"ID_MSG_C2L_RechargeSimulation":                               reflect.ValueOf(cl.ID_MSG_C2L_RechargeSimulation),
		"ID_MSG_C2L_RecvH5DesktopReward":                              reflect.ValueOf(cl.ID_MSG_C2L_RecvH5DesktopReward),
		"ID_MSG_C2L_RecvShareAward":                                   reflect.ValueOf(cl.ID_MSG_C2L_RecvShareAward),
		"ID_MSG_C2L_RemainBookLevelUp":                                reflect.ValueOf(cl.ID_MSG_C2L_RemainBookLevelUp),
		"ID_MSG_C2L_RemainBookRecvExp":                                reflect.ValueOf(cl.ID_MSG_C2L_RemainBookRecvExp),
		"ID_MSG_C2L_RemainGetData":                                    reflect.ValueOf(cl.ID_MSG_C2L_RemainGetData),
		"ID_MSG_C2L_RiteGetData":                                      reflect.ValueOf(cl.ID_MSG_C2L_RiteGetData),
		"ID_MSG_C2L_RiteTakeRareAwards":                               reflect.ValueOf(cl.ID_MSG_C2L_RiteTakeRareAwards),
		"ID_MSG_C2L_RobotBattle":                                      reflect.ValueOf(cl.ID_MSG_C2L_RobotBattle),
		"ID_MSG_C2L_RoundActivityGetData":                             reflect.ValueOf(cl.ID_MSG_C2L_RoundActivityGetData),
		"ID_MSG_C2L_RoundActivityRecvTaskAward":                       reflect.ValueOf(cl.ID_MSG_C2L_RoundActivityRecvTaskAward),
		"ID_MSG_C2L_SeasonArenaBuyChallengeCount":                     reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaBuyChallengeCount),
		"ID_MSG_C2L_SeasonArenaDivisionAward":                         reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaDivisionAward),
		"ID_MSG_C2L_SeasonArenaFight":                                 reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaFight),
		"ID_MSG_C2L_SeasonArenaGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaGetData),
		"ID_MSG_C2L_SeasonArenaGetRankList":                           reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaGetRankList),
		"ID_MSG_C2L_SeasonArenaLogList":                               reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaLogList),
		"ID_MSG_C2L_SeasonArenaOfFame":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaOfFame),
		"ID_MSG_C2L_SeasonArenaRefresh":                               reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaRefresh),
		"ID_MSG_C2L_SeasonArenaTaskAward":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaTaskAward),
		"ID_MSG_C2L_SeasonArenaUseTicket":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonArenaUseTicket),
		"ID_MSG_C2L_SeasonComplianceGetData":                          reflect.ValueOf(cl.ID_MSG_C2L_SeasonComplianceGetData),
		"ID_MSG_C2L_SeasonComplianceList":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonComplianceList),
		"ID_MSG_C2L_SeasonComplianceRecvReward":                       reflect.ValueOf(cl.ID_MSG_C2L_SeasonComplianceRecvReward),
		"ID_MSG_C2L_SeasonDoorFight":                                  reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorFight),
		"ID_MSG_C2L_SeasonDoorFightLine":                              reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorFightLine),
		"ID_MSG_C2L_SeasonDoorFightLineReward":                        reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorFightLineReward),
		"ID_MSG_C2L_SeasonDoorFightLineViewReward":                    reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorFightLineViewReward),
		"ID_MSG_C2L_SeasonDoorGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorGetData),
		"ID_MSG_C2L_SeasonDoorLog":                                    reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorLog),
		"ID_MSG_C2L_SeasonDoorTaskReward":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonDoorTaskReward),
		"ID_MSG_C2L_SeasonDungeonFight":                               reflect.ValueOf(cl.ID_MSG_C2L_SeasonDungeonFight),
		"ID_MSG_C2L_SeasonDungeonGetData":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonDungeonGetData),
		"ID_MSG_C2L_SeasonDungeonRecvReward":                          reflect.ValueOf(cl.ID_MSG_C2L_SeasonDungeonRecvReward),
		"ID_MSG_C2L_SeasonEnter":                                      reflect.ValueOf(cl.ID_MSG_C2L_SeasonEnter),
		"ID_MSG_C2L_SeasonJewelryDecompose":                           reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelryDecompose),
		"ID_MSG_C2L_SeasonJewelryGetData":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelryGetData),
		"ID_MSG_C2L_SeasonJewelryLock":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelryLock),
		"ID_MSG_C2L_SeasonJewelrySetAutoDecompose":                    reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelrySetAutoDecompose),
		"ID_MSG_C2L_SeasonJewelrySkillChange":                         reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelrySkillChange),
		"ID_MSG_C2L_SeasonJewelrySkillChangeConfirm":                  reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelrySkillChangeConfirm),
		"ID_MSG_C2L_SeasonJewelrySkillClassUp":                        reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelrySkillClassUp),
		"ID_MSG_C2L_SeasonJewelrySkillLevelUp":                        reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelrySkillLevelUp),
		"ID_MSG_C2L_SeasonJewelryTestReRollSkill":                     reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelryTestReRollSkill),
		"ID_MSG_C2L_SeasonJewelryWear":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonJewelryWear),
		"ID_MSG_C2L_SeasonLevelGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_SeasonLevelGetData),
		"ID_MSG_C2L_SeasonLevelRecvLvAwards":                          reflect.ValueOf(cl.ID_MSG_C2L_SeasonLevelRecvLvAwards),
		"ID_MSG_C2L_SeasonLevelRecvTaskAwards":                        reflect.ValueOf(cl.ID_MSG_C2L_SeasonLevelRecvTaskAwards),
		"ID_MSG_C2L_SeasonLevelUp":                                    reflect.ValueOf(cl.ID_MSG_C2L_SeasonLevelUp),
		"ID_MSG_C2L_SeasonLinkActivate":                               reflect.ValueOf(cl.ID_MSG_C2L_SeasonLinkActivate),
		"ID_MSG_C2L_SeasonLinkGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonLinkGetData),
		"ID_MSG_C2L_SeasonLinkMonumentTakeRareAwards":                 reflect.ValueOf(cl.ID_MSG_C2L_SeasonLinkMonumentTakeRareAwards),
		"ID_MSG_C2L_SeasonMapAltar":                                   reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapAltar),
		"ID_MSG_C2L_SeasonMapBuyStamina":                              reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapBuyStamina),
		"ID_MSG_C2L_SeasonMapDialogue":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapDialogue),
		"ID_MSG_C2L_SeasonMapFight":                                   reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapFight),
		"ID_MSG_C2L_SeasonMapGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapGetData),
		"ID_MSG_C2L_SeasonMapGetRankList":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapGetRankList),
		"ID_MSG_C2L_SeasonMapGoodsPrice":                              reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapGoodsPrice),
		"ID_MSG_C2L_SeasonMapMaster":                                  reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapMaster),
		"ID_MSG_C2L_SeasonMapMovePosition":                            reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapMovePosition),
		"ID_MSG_C2L_SeasonMapPassPosition":                            reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapPassPosition),
		"ID_MSG_C2L_SeasonMapPositionLogs":                            reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapPositionLogs),
		"ID_MSG_C2L_SeasonMapPriceChangeLogs":                         reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapPriceChangeLogs),
		"ID_MSG_C2L_SeasonMapSystemEvent":                             reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapSystemEvent),
		"ID_MSG_C2L_SeasonMapTaskReward":                              reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapTaskReward),
		"ID_MSG_C2L_SeasonMapTrade":                                   reflect.ValueOf(cl.ID_MSG_C2L_SeasonMapTrade),
		"ID_MSG_C2L_SeasonReturnGetData":                              reflect.ValueOf(cl.ID_MSG_C2L_SeasonReturnGetData),
		"ID_MSG_C2L_SeasonReturnTakeAwards":                           reflect.ValueOf(cl.ID_MSG_C2L_SeasonReturnTakeAwards),
		"ID_MSG_C2L_SeasonShopBuy":                                    reflect.ValueOf(cl.ID_MSG_C2L_SeasonShopBuy),
		"ID_MSG_C2L_SeasonShopGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_SeasonShopGetData),
		"ID_MSG_C2L_SeasonStartTowerRankLike":                         reflect.ValueOf(cl.ID_MSG_C2L_SeasonStartTowerRankLike),
		"ID_MSG_C2L_SeasonStartTowerRankList":                         reflect.ValueOf(cl.ID_MSG_C2L_SeasonStartTowerRankList),
		"ID_MSG_C2L_SelectSummonGetOpenActivity":                      reflect.ValueOf(cl.ID_MSG_C2L_SelectSummonGetOpenActivity),
		"ID_MSG_C2L_SelectSummonSummon":                               reflect.ValueOf(cl.ID_MSG_C2L_SelectSummonSummon),
		"ID_MSG_C2L_SelectSummonTestSummon":                           reflect.ValueOf(cl.ID_MSG_C2L_SelectSummonTestSummon),
		"ID_MSG_C2L_SellItem":                                         reflect.ValueOf(cl.ID_MSG_C2L_SellItem),
		"ID_MSG_C2L_SetClientInfo":                                    reflect.ValueOf(cl.ID_MSG_C2L_SetClientInfo),
		"ID_MSG_C2L_SetName":                                          reflect.ValueOf(cl.ID_MSG_C2L_SetName),
		"ID_MSG_C2L_SetPush":                                          reflect.ValueOf(cl.ID_MSG_C2L_SetPush),
		"ID_MSG_C2L_SetServerTime":                                    reflect.ValueOf(cl.ID_MSG_C2L_SetServerTime),
		"ID_MSG_C2L_SevenDayLoginData":                                reflect.ValueOf(cl.ID_MSG_C2L_SevenDayLoginData),
		"ID_MSG_C2L_SevenDayLoginTakeAward":                           reflect.ValueOf(cl.ID_MSG_C2L_SevenDayLoginTakeAward),
		"ID_MSG_C2L_ShopBuy":                                          reflect.ValueOf(cl.ID_MSG_C2L_ShopBuy),
		"ID_MSG_C2L_ShopList":                                         reflect.ValueOf(cl.ID_MSG_C2L_ShopList),
		"ID_MSG_C2L_ShopRefresh":                                      reflect.ValueOf(cl.ID_MSG_C2L_ShopRefresh),
		"ID_MSG_C2L_ShopReset":                                        reflect.ValueOf(cl.ID_MSG_C2L_ShopReset),
		"ID_MSG_C2L_SkinList":                                         reflect.ValueOf(cl.ID_MSG_C2L_SkinList),
		"ID_MSG_C2L_SkinUse":                                          reflect.ValueOf(cl.ID_MSG_C2L_SkinUse),
		"ID_MSG_C2L_StoryReviewGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_StoryReviewGetData),
		"ID_MSG_C2L_StoryReviewUnlock":                                reflect.ValueOf(cl.ID_MSG_C2L_StoryReviewUnlock),
		"ID_MSG_C2L_Summon":                                           reflect.ValueOf(cl.ID_MSG_C2L_Summon),
		"ID_MSG_C2L_SummonArtifactPointsExchange":                     reflect.ValueOf(cl.ID_MSG_C2L_SummonArtifactPointsExchange),
		"ID_MSG_C2L_SummonGetData":                                    reflect.ValueOf(cl.ID_MSG_C2L_SummonGetData),
		"ID_MSG_C2L_SummonSetHeroAutoDecompose":                       reflect.ValueOf(cl.ID_MSG_C2L_SummonSetHeroAutoDecompose),
		"ID_MSG_C2L_SummonSetWishList":                                reflect.ValueOf(cl.ID_MSG_C2L_SummonSetWishList),
		"ID_MSG_C2L_SummonSimulation":                                 reflect.ValueOf(cl.ID_MSG_C2L_SummonSimulation),
		"ID_MSG_C2L_SyncQuestionnaire":                                reflect.ValueOf(cl.ID_MSG_C2L_SyncQuestionnaire),
		"ID_MSG_C2L_TalentTreeGetData":                                reflect.ValueOf(cl.ID_MSG_C2L_TalentTreeGetData),
		"ID_MSG_C2L_TalentTreeHot":                                    reflect.ValueOf(cl.ID_MSG_C2L_TalentTreeHot),
		"ID_MSG_C2L_TalentTreeLevelUp":                                reflect.ValueOf(cl.ID_MSG_C2L_TalentTreeLevelUp),
		"ID_MSG_C2L_TalentTreePlanDelete":                             reflect.ValueOf(cl.ID_MSG_C2L_TalentTreePlanDelete),
		"ID_MSG_C2L_TalentTreePlanSave":                               reflect.ValueOf(cl.ID_MSG_C2L_TalentTreePlanSave),
		"ID_MSG_C2L_TalentTreeReceiveTaskAwards":                      reflect.ValueOf(cl.ID_MSG_C2L_TalentTreeReceiveTaskAwards),
		"ID_MSG_C2L_TalentTreeReset":                                  reflect.ValueOf(cl.ID_MSG_C2L_TalentTreeReset),
		"ID_MSG_C2L_TalesChapterFight":                                reflect.ValueOf(cl.ID_MSG_C2L_TalesChapterFight),
		"ID_MSG_C2L_TalesChapterFinish":                               reflect.ValueOf(cl.ID_MSG_C2L_TalesChapterFinish),
		"ID_MSG_C2L_TalesChapterTakeReward":                           reflect.ValueOf(cl.ID_MSG_C2L_TalesChapterTakeReward),
		"ID_MSG_C2L_TalesEliteFight":                                  reflect.ValueOf(cl.ID_MSG_C2L_TalesEliteFight),
		"ID_MSG_C2L_TalesEliteWipe":                                   reflect.ValueOf(cl.ID_MSG_C2L_TalesEliteWipe),
		"ID_MSG_C2L_TalesList":                                        reflect.ValueOf(cl.ID_MSG_C2L_TalesList),
		"ID_MSG_C2L_TaskGetInfo":                                      reflect.ValueOf(cl.ID_MSG_C2L_TaskGetInfo),
		"ID_MSG_C2L_TaskReceiveAward":                                 reflect.ValueOf(cl.ID_MSG_C2L_TaskReceiveAward),
		"ID_MSG_C2L_Test":                                             reflect.ValueOf(cl.ID_MSG_C2L_Test),
		"ID_MSG_C2L_TestBattleData":                                   reflect.ValueOf(cl.ID_MSG_C2L_TestBattleData),
		"ID_MSG_C2L_TestDrop":                                         reflect.ValueOf(cl.ID_MSG_C2L_TestDrop),
		"ID_MSG_C2L_TestEtcdGiftCode":                                 reflect.ValueOf(cl.ID_MSG_C2L_TestEtcdGiftCode),
		"ID_MSG_C2L_TitleList":                                        reflect.ValueOf(cl.ID_MSG_C2L_TitleList),
		"ID_MSG_C2L_TitleUse":                                         reflect.ValueOf(cl.ID_MSG_C2L_TitleUse),
		"ID_MSG_C2L_TowerFight":                                       reflect.ValueOf(cl.ID_MSG_C2L_TowerFight),
		"ID_MSG_C2L_TowerJump":                                        reflect.ValueOf(cl.ID_MSG_C2L_TowerJump),
		"ID_MSG_C2L_TowerList":                                        reflect.ValueOf(cl.ID_MSG_C2L_TowerList),
		"ID_MSG_C2L_TowerSeasonCognitionLogs":                         reflect.ValueOf(cl.ID_MSG_C2L_TowerSeasonCognitionLogs),
		"ID_MSG_C2L_TowerSeasonFight":                                 reflect.ValueOf(cl.ID_MSG_C2L_TowerSeasonFight),
		"ID_MSG_C2L_TowerSeasonGetData":                               reflect.ValueOf(cl.ID_MSG_C2L_TowerSeasonGetData),
		"ID_MSG_C2L_TowerSeasonRankLike":                              reflect.ValueOf(cl.ID_MSG_C2L_TowerSeasonRankLike),
		"ID_MSG_C2L_TowerSeasonRankList":                              reflect.ValueOf(cl.ID_MSG_C2L_TowerSeasonRankList),
		"ID_MSG_C2L_TowerSeasonRecvAward":                             reflect.ValueOf(cl.ID_MSG_C2L_TowerSeasonRecvAward),
		"ID_MSG_C2L_TowerSweep":                                       reflect.ValueOf(cl.ID_MSG_C2L_TowerSweep),
		"ID_MSG_C2L_TowerstarDailyRecvAward":                          reflect.ValueOf(cl.ID_MSG_C2L_TowerstarDailyRecvAward),
		"ID_MSG_C2L_TowerstarFight":                                   reflect.ValueOf(cl.ID_MSG_C2L_TowerstarFight),
		"ID_MSG_C2L_TowerstarGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_TowerstarGetData),
		"ID_MSG_C2L_TowerstarStarRecvAward":                           reflect.ValueOf(cl.ID_MSG_C2L_TowerstarStarRecvAward),
		"ID_MSG_C2L_TrialAward":                                       reflect.ValueOf(cl.ID_MSG_C2L_TrialAward),
		"ID_MSG_C2L_TrialFight":                                       reflect.ValueOf(cl.ID_MSG_C2L_TrialFight),
		"ID_MSG_C2L_TrialGetInfo":                                     reflect.ValueOf(cl.ID_MSG_C2L_TrialGetInfo),
		"ID_MSG_C2L_TrialPreview":                                     reflect.ValueOf(cl.ID_MSG_C2L_TrialPreview),
		"ID_MSG_C2L_TrialSpeed":                                       reflect.ValueOf(cl.ID_MSG_C2L_TrialSpeed),
		"ID_MSG_C2L_UpdateGems":                                       reflect.ValueOf(cl.ID_MSG_C2L_UpdateGems),
		"ID_MSG_C2L_UpdateTop5":                                       reflect.ValueOf(cl.ID_MSG_C2L_UpdateTop5),
		"ID_MSG_C2L_UseItem":                                          reflect.ValueOf(cl.ID_MSG_C2L_UseItem),
		"ID_MSG_C2L_ViewFormation":                                    reflect.ValueOf(cl.ID_MSG_C2L_ViewFormation),
		"ID_MSG_C2L_ViewUser":                                         reflect.ValueOf(cl.ID_MSG_C2L_ViewUser),
		"ID_MSG_C2L_VipBuyGift":                                       reflect.ValueOf(cl.ID_MSG_C2L_VipBuyGift),
		"ID_MSG_C2L_VipInfoGet":                                       reflect.ValueOf(cl.ID_MSG_C2L_VipInfoGet),
		"ID_MSG_C2L_WorldBossFight":                                   reflect.ValueOf(cl.ID_MSG_C2L_WorldBossFight),
		"ID_MSG_C2L_WorldBossGetData":                                 reflect.ValueOf(cl.ID_MSG_C2L_WorldBossGetData),
		"ID_MSG_C2L_WorldBossGetRoomLog":                              reflect.ValueOf(cl.ID_MSG_C2L_WorldBossGetRoomLog),
		"ID_MSG_C2L_WorldBossRank":                                    reflect.ValueOf(cl.ID_MSG_C2L_WorldBossRank),
		"ID_MSG_C2L_WorldBossRecvAward":                               reflect.ValueOf(cl.ID_MSG_C2L_WorldBossRecvAward),
		"ID_MSG_C2L_WorldBossRoomInfo":                                reflect.ValueOf(cl.ID_MSG_C2L_WorldBossRoomInfo),
		"ID_MSG_C2L_WorldBossSelectLevel":                             reflect.ValueOf(cl.ID_MSG_C2L_WorldBossSelectLevel),
		"ID_MSG_C2L_WorldBossWorship":                                 reflect.ValueOf(cl.ID_MSG_C2L_WorldBossWorship),
		"ID_MSG_C2L_WrestleChangeRoom":                                reflect.ValueOf(cl.ID_MSG_C2L_WrestleChangeRoom),
		"ID_MSG_C2L_WrestleFight":                                     reflect.ValueOf(cl.ID_MSG_C2L_WrestleFight),
		"ID_MSG_C2L_WrestleFightLog":                                  reflect.ValueOf(cl.ID_MSG_C2L_WrestleFightLog),
		"ID_MSG_C2L_WrestleHallOfFame":                                reflect.ValueOf(cl.ID_MSG_C2L_WrestleHallOfFame),
		"ID_MSG_C2L_WrestleInfo":                                      reflect.ValueOf(cl.ID_MSG_C2L_WrestleInfo),
		"ID_MSG_C2L_WrestleLike":                                      reflect.ValueOf(cl.ID_MSG_C2L_WrestleLike),
		"ID_MSG_C2L_WrestleMapInfo":                                   reflect.ValueOf(cl.ID_MSG_C2L_WrestleMapInfo),
		"ID_MSG_C2L_WrestleRankList":                                  reflect.ValueOf(cl.ID_MSG_C2L_WrestleRankList),
		"ID_MSG_C2L_WrestleRecvTaskAward":                             reflect.ValueOf(cl.ID_MSG_C2L_WrestleRecvTaskAward),
		"ID_MSG_C2L_WrestleRoomInfo":                                  reflect.ValueOf(cl.ID_MSG_C2L_WrestleRoomInfo),
		"ID_MSG_C2L_WrestleTopUserList":                               reflect.ValueOf(cl.ID_MSG_C2L_WrestleTopUserList),
		"ID_MSG_END":                                                  reflect.ValueOf(cl.ID_MSG_END),
		"ID_MSG_L2C_Accusation":                                       reflect.ValueOf(cl.ID_MSG_L2C_Accusation),
		"ID_MSG_L2C_ActivityComplianceGetData":                        reflect.ValueOf(cl.ID_MSG_L2C_ActivityComplianceGetData),
		"ID_MSG_L2C_ActivityComplianceLikeRank":                       reflect.ValueOf(cl.ID_MSG_L2C_ActivityComplianceLikeRank),
		"ID_MSG_L2C_ActivityComplianceRecvAward":                      reflect.ValueOf(cl.ID_MSG_L2C_ActivityComplianceRecvAward),
		"ID_MSG_L2C_ActivityComplianceScoreUpdate":                    reflect.ValueOf(cl.ID_MSG_L2C_ActivityComplianceScoreUpdate),
		"ID_MSG_L2C_ActivityCouponBuy":                                reflect.ValueOf(cl.ID_MSG_L2C_ActivityCouponBuy),
		"ID_MSG_L2C_ActivityCouponGetData":                            reflect.ValueOf(cl.ID_MSG_L2C_ActivityCouponGetData),
		"ID_MSG_L2C_ActivityCouponXmlUpdate":                          reflect.ValueOf(cl.ID_MSG_L2C_ActivityCouponXmlUpdate),
		"ID_MSG_L2C_ActivityGoddessActive":                            reflect.ValueOf(cl.ID_MSG_L2C_ActivityGoddessActive),
		"ID_MSG_L2C_ActivityGoddessRecvRecoveryAward":                 reflect.ValueOf(cl.ID_MSG_L2C_ActivityGoddessRecvRecoveryAward),
		"ID_MSG_L2C_ActivityLifelongGiftUpdate":                       reflect.ValueOf(cl.ID_MSG_L2C_ActivityLifelongGiftUpdate),
		"ID_MSG_L2C_ActivityMixGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_ActivityMixGetData),
		"ID_MSG_L2C_ActivityRechargeBuy":                              reflect.ValueOf(cl.ID_MSG_L2C_ActivityRechargeBuy),
		"ID_MSG_L2C_ActivityRechargeGet":                              reflect.ValueOf(cl.ID_MSG_L2C_ActivityRechargeGet),
		"ID_MSG_L2C_ActivityReturnGetData":                            reflect.ValueOf(cl.ID_MSG_L2C_ActivityReturnGetData),
		"ID_MSG_L2C_ActivityReturnTakeLoginAwards":                    reflect.ValueOf(cl.ID_MSG_L2C_ActivityReturnTakeLoginAwards),
		"ID_MSG_L2C_ActivityScheduleGetData":                          reflect.ValueOf(cl.ID_MSG_L2C_ActivityScheduleGetData),
		"ID_MSG_L2C_ActivityStoryExchange":                            reflect.ValueOf(cl.ID_MSG_L2C_ActivityStoryExchange),
		"ID_MSG_L2C_ActivityStoryFight":                               reflect.ValueOf(cl.ID_MSG_L2C_ActivityStoryFight),
		"ID_MSG_L2C_ActivityStoryGetData":                             reflect.ValueOf(cl.ID_MSG_L2C_ActivityStoryGetData),
		"ID_MSG_L2C_ActivityStoryLoginAward":                          reflect.ValueOf(cl.ID_MSG_L2C_ActivityStoryLoginAward),
		"ID_MSG_L2C_ActivitySumActivePuzzleCell":                      reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumActivePuzzleCell),
		"ID_MSG_L2C_ActivitySumExchange":                              reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumExchange),
		"ID_MSG_L2C_ActivitySumFeed":                                  reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumFeed),
		"ID_MSG_L2C_ActivitySumGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumGetData),
		"ID_MSG_L2C_ActivitySumLoginReward":                           reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumLoginReward),
		"ID_MSG_L2C_ActivitySumMakeGift":                              reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumMakeGift),
		"ID_MSG_L2C_ActivitySumShootGameFight":                        reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumShootGameFight),
		"ID_MSG_L2C_ActivitySumTaskReward":                            reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumTaskReward),
		"ID_MSG_L2C_ActivitySumTaskUpdate":                            reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumTaskUpdate),
		"ID_MSG_L2C_ActivitySumTicketBuy":                             reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumTicketBuy),
		"ID_MSG_L2C_ActivitySumTurnTableSelectBuff":                   reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumTurnTableSelectBuff),
		"ID_MSG_L2C_ActivitySumTurnTableSummon":                       reflect.ValueOf(cl.ID_MSG_L2C_ActivitySumTurnTableSummon),
		"ID_MSG_L2C_ActivityTurnTableGetData":                         reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableGetData),
		"ID_MSG_L2C_ActivityTurnTableRecvLogin":                       reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableRecvLogin),
		"ID_MSG_L2C_ActivityTurnTableRecvTask":                        reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableRecvTask),
		"ID_MSG_L2C_ActivityTurnTableSelectBuff":                      reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableSelectBuff),
		"ID_MSG_L2C_ActivityTurnTableSummon":                          reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableSummon),
		"ID_MSG_L2C_ActivityTurnTableTicketBuy":                       reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableTicketBuy),
		"ID_MSG_L2C_ActivityTurnTableUpdateTask":                      reflect.ValueOf(cl.ID_MSG_L2C_ActivityTurnTableUpdateTask),
		"ID_MSG_L2C_AddPurchaseNum":                                   reflect.ValueOf(cl.ID_MSG_L2C_AddPurchaseNum),
		"ID_MSG_L2C_AnnouncementGetData":                              reflect.ValueOf(cl.ID_MSG_L2C_AnnouncementGetData),
		"ID_MSG_L2C_AnnouncementUpdate":                               reflect.ValueOf(cl.ID_MSG_L2C_AnnouncementUpdate),
		"ID_MSG_L2C_ArenaFight":                                       reflect.ValueOf(cl.ID_MSG_L2C_ArenaFight),
		"ID_MSG_L2C_ArenaInfo":                                        reflect.ValueOf(cl.ID_MSG_L2C_ArenaInfo),
		"ID_MSG_L2C_ArenaLike":                                        reflect.ValueOf(cl.ID_MSG_L2C_ArenaLike),
		"ID_MSG_L2C_ArenaLogList":                                     reflect.ValueOf(cl.ID_MSG_L2C_ArenaLogList),
		"ID_MSG_L2C_ArenaRank":                                        reflect.ValueOf(cl.ID_MSG_L2C_ArenaRank),
		"ID_MSG_L2C_ArenaRecvAward":                                   reflect.ValueOf(cl.ID_MSG_L2C_ArenaRecvAward),
		"ID_MSG_L2C_ArenaRefresh":                                     reflect.ValueOf(cl.ID_MSG_L2C_ArenaRefresh),
		"ID_MSG_L2C_ArtifactActivate":                                 reflect.ValueOf(cl.ID_MSG_L2C_ArtifactActivate),
		"ID_MSG_L2C_ArtifactDebutGetActivity":                         reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutGetActivity),
		"ID_MSG_L2C_ArtifactDebutMainInfo":                            reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutMainInfo),
		"ID_MSG_L2C_ArtifactDebutOpenPuzzle":                          reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutOpenPuzzle),
		"ID_MSG_L2C_ArtifactDebutRecvActAward":                        reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutRecvActAward),
		"ID_MSG_L2C_ArtifactDebutRecvTaskAward":                       reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutRecvTaskAward),
		"ID_MSG_L2C_ArtifactDebutSetWish":                             reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutSetWish),
		"ID_MSG_L2C_ArtifactDebutSummon":                              reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutSummon),
		"ID_MSG_L2C_ArtifactDebutTestSummon":                          reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutTestSummon),
		"ID_MSG_L2C_ArtifactDebutUpdateActivity":                      reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutUpdateActivity),
		"ID_MSG_L2C_ArtifactDebutUpdateTask":                          reflect.ValueOf(cl.ID_MSG_L2C_ArtifactDebutUpdateTask),
		"ID_MSG_L2C_ArtifactForge":                                    reflect.ValueOf(cl.ID_MSG_L2C_ArtifactForge),
		"ID_MSG_L2C_ArtifactList":                                     reflect.ValueOf(cl.ID_MSG_L2C_ArtifactList),
		"ID_MSG_L2C_ArtifactRevive":                                   reflect.ValueOf(cl.ID_MSG_L2C_ArtifactRevive),
		"ID_MSG_L2C_ArtifactStarUp":                                   reflect.ValueOf(cl.ID_MSG_L2C_ArtifactStarUp),
		"ID_MSG_L2C_ArtifactStrength":                                 reflect.ValueOf(cl.ID_MSG_L2C_ArtifactStrength),
		"ID_MSG_L2C_AssistanceActivityGetAward":                       reflect.ValueOf(cl.ID_MSG_L2C_AssistanceActivityGetAward),
		"ID_MSG_L2C_AssistanceActivityGetData":                        reflect.ValueOf(cl.ID_MSG_L2C_AssistanceActivityGetData),
		"ID_MSG_L2C_AssistantGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_AssistantGetData),
		"ID_MSG_L2C_AvatarGetInfo":                                    reflect.ValueOf(cl.ID_MSG_L2C_AvatarGetInfo),
		"ID_MSG_L2C_AvatarNew":                                        reflect.ValueOf(cl.ID_MSG_L2C_AvatarNew),
		"ID_MSG_L2C_AvatarSetIcon":                                    reflect.ValueOf(cl.ID_MSG_L2C_AvatarSetIcon),
		"ID_MSG_L2C_BattleReport":                                     reflect.ValueOf(cl.ID_MSG_L2C_BattleReport),
		"ID_MSG_L2C_BattleTest":                                       reflect.ValueOf(cl.ID_MSG_L2C_BattleTest),
		"ID_MSG_L2C_BossRushBuyStamina":                               reflect.ValueOf(cl.ID_MSG_L2C_BossRushBuyStamina),
		"ID_MSG_L2C_BossRushFight":                                    reflect.ValueOf(cl.ID_MSG_L2C_BossRushFight),
		"ID_MSG_L2C_BossRushGetData":                                  reflect.ValueOf(cl.ID_MSG_L2C_BossRushGetData),
		"ID_MSG_L2C_BossRushTaskAward":                                reflect.ValueOf(cl.ID_MSG_L2C_BossRushTaskAward),
		"ID_MSG_L2C_BossRushTaskUpdate":                               reflect.ValueOf(cl.ID_MSG_L2C_BossRushTaskUpdate),
		"ID_MSG_L2C_BoxExchange":                                      reflect.ValueOf(cl.ID_MSG_L2C_BoxExchange),
		"ID_MSG_L2C_BoxGet":                                           reflect.ValueOf(cl.ID_MSG_L2C_BoxGet),
		"ID_MSG_L2C_BoxOpen":                                          reflect.ValueOf(cl.ID_MSG_L2C_BoxOpen),
		"ID_MSG_L2C_CarnivalGetData":                                  reflect.ValueOf(cl.ID_MSG_L2C_CarnivalGetData),
		"ID_MSG_L2C_CarnivalNotify":                                   reflect.ValueOf(cl.ID_MSG_L2C_CarnivalNotify),
		"ID_MSG_L2C_CarnivalReceiveAward":                             reflect.ValueOf(cl.ID_MSG_L2C_CarnivalReceiveAward),
		"ID_MSG_L2C_CarnivalUpdate":                                   reflect.ValueOf(cl.ID_MSG_L2C_CarnivalUpdate),
		"ID_MSG_L2C_ChatCostShareCount":                               reflect.ValueOf(cl.ID_MSG_L2C_ChatCostShareCount),
		"ID_MSG_L2C_ChatGetToken":                                     reflect.ValueOf(cl.ID_MSG_L2C_ChatGetToken),
		"ID_MSG_L2C_ChatGroupUpdateNotify":                            reflect.ValueOf(cl.ID_MSG_L2C_ChatGroupUpdateNotify),
		"ID_MSG_L2C_ChatSyncChatTag":                                  reflect.ValueOf(cl.ID_MSG_L2C_ChatSyncChatTag),
		"ID_MSG_L2C_ClientGetMultiLang":                               reflect.ValueOf(cl.ID_MSG_L2C_ClientGetMultiLang),
		"ID_MSG_L2C_ClientSetMultiLang":                               reflect.ValueOf(cl.ID_MSG_L2C_ClientSetMultiLang),
		"ID_MSG_L2C_CommonRankLike":                                   reflect.ValueOf(cl.ID_MSG_L2C_CommonRankLike),
		"ID_MSG_L2C_ComplianceTasksGetData":                           reflect.ValueOf(cl.ID_MSG_L2C_ComplianceTasksGetData),
		"ID_MSG_L2C_ComplianceTasksRecvTask":                          reflect.ValueOf(cl.ID_MSG_L2C_ComplianceTasksRecvTask),
		"ID_MSG_L2C_ComplianceTasksUpdate":                            reflect.ValueOf(cl.ID_MSG_L2C_ComplianceTasksUpdate),
		"ID_MSG_L2C_CrystalActiveAchievement":                         reflect.ValueOf(cl.ID_MSG_L2C_CrystalActiveAchievement),
		"ID_MSG_L2C_CrystalAddHero":                                   reflect.ValueOf(cl.ID_MSG_L2C_CrystalAddHero),
		"ID_MSG_L2C_CrystalGetAllData":                                reflect.ValueOf(cl.ID_MSG_L2C_CrystalGetAllData),
		"ID_MSG_L2C_CrystalGetShareAttr":                              reflect.ValueOf(cl.ID_MSG_L2C_CrystalGetShareAttr),
		"ID_MSG_L2C_CrystalHeroesUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_CrystalHeroesUpdate),
		"ID_MSG_L2C_CrystalRemoveHero":                                reflect.ValueOf(cl.ID_MSG_L2C_CrystalRemoveHero),
		"ID_MSG_L2C_CrystalSpeedSlotCD":                               reflect.ValueOf(cl.ID_MSG_L2C_CrystalSpeedSlotCD),
		"ID_MSG_L2C_CrystalUnlockSlot":                                reflect.ValueOf(cl.ID_MSG_L2C_CrystalUnlockSlot),
		"ID_MSG_L2C_CrystalUpdateAchievement":                         reflect.ValueOf(cl.ID_MSG_L2C_CrystalUpdateAchievement),
		"ID_MSG_L2C_DailyAttendanceGetData":                           reflect.ValueOf(cl.ID_MSG_L2C_DailyAttendanceGetData),
		"ID_MSG_L2C_DailyAttendanceHeroGetData":                       reflect.ValueOf(cl.ID_MSG_L2C_DailyAttendanceHeroGetData),
		"ID_MSG_L2C_DailyAttendanceHeroRecvAward":                     reflect.ValueOf(cl.ID_MSG_L2C_DailyAttendanceHeroRecvAward),
		"ID_MSG_L2C_DailyAttendanceRecvAward":                         reflect.ValueOf(cl.ID_MSG_L2C_DailyAttendanceRecvAward),
		"ID_MSG_L2C_DailyAttendanceUpdate":                            reflect.ValueOf(cl.ID_MSG_L2C_DailyAttendanceUpdate),
		"ID_MSG_L2C_DailySpecialGetData":                              reflect.ValueOf(cl.ID_MSG_L2C_DailySpecialGetData),
		"ID_MSG_L2C_DailySpecialRecvAward":                            reflect.ValueOf(cl.ID_MSG_L2C_DailySpecialRecvAward),
		"ID_MSG_L2C_DailyWishGet":                                     reflect.ValueOf(cl.ID_MSG_L2C_DailyWishGet),
		"ID_MSG_L2C_DailyWishSummon":                                  reflect.ValueOf(cl.ID_MSG_L2C_DailyWishSummon),
		"ID_MSG_L2C_DailyWishUpdate":                                  reflect.ValueOf(cl.ID_MSG_L2C_DailyWishUpdate),
		"ID_MSG_L2C_DailyWishXmlGet":                                  reflect.ValueOf(cl.ID_MSG_L2C_DailyWishXmlGet),
		"ID_MSG_L2C_DailyWishXmlUpdate":                               reflect.ValueOf(cl.ID_MSG_L2C_DailyWishXmlUpdate),
		"ID_MSG_L2C_DeleteMails":                                      reflect.ValueOf(cl.ID_MSG_L2C_DeleteMails),
		"ID_MSG_L2C_DisorderlandBuyStamina":                           reflect.ValueOf(cl.ID_MSG_L2C_DisorderlandBuyStamina),
		"ID_MSG_L2C_DisorderlandGetData":                              reflect.ValueOf(cl.ID_MSG_L2C_DisorderlandGetData),
		"ID_MSG_L2C_DisorderlandRank":                                 reflect.ValueOf(cl.ID_MSG_L2C_DisorderlandRank),
		"ID_MSG_L2C_DisorderlandTestSweep":                            reflect.ValueOf(cl.ID_MSG_L2C_DisorderlandTestSweep),
		"ID_MSG_L2C_DisorderlandTriggerEvent":                         reflect.ValueOf(cl.ID_MSG_L2C_DisorderlandTriggerEvent),
		"ID_MSG_L2C_DispatchGetAwards":                                reflect.ValueOf(cl.ID_MSG_L2C_DispatchGetAwards),
		"ID_MSG_L2C_DispatchLevelUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_DispatchLevelUpdate),
		"ID_MSG_L2C_DispatchReceiveTask":                              reflect.ValueOf(cl.ID_MSG_L2C_DispatchReceiveTask),
		"ID_MSG_L2C_DispatchRefreshTask":                              reflect.ValueOf(cl.ID_MSG_L2C_DispatchRefreshTask),
		"ID_MSG_L2C_DispatchTasks":                                    reflect.ValueOf(cl.ID_MSG_L2C_DispatchTasks),
		"ID_MSG_L2C_DivineDemonGetOpenActivity":                       reflect.ValueOf(cl.ID_MSG_L2C_DivineDemonGetOpenActivity),
		"ID_MSG_L2C_DivineDemonReceiveTaskAward":                      reflect.ValueOf(cl.ID_MSG_L2C_DivineDemonReceiveTaskAward),
		"ID_MSG_L2C_DivineDemonSummon":                                reflect.ValueOf(cl.ID_MSG_L2C_DivineDemonSummon),
		"ID_MSG_L2C_DivineDemonUpdate":                                reflect.ValueOf(cl.ID_MSG_L2C_DivineDemonUpdate),
		"ID_MSG_L2C_DivineDemonUpdateTaskProgress":                    reflect.ValueOf(cl.ID_MSG_L2C_DivineDemonUpdateTaskProgress),
		"ID_MSG_L2C_DrawMails":                                        reflect.ValueOf(cl.ID_MSG_L2C_DrawMails),
		"ID_MSG_L2C_DropActivityDailyReward":                          reflect.ValueOf(cl.ID_MSG_L2C_DropActivityDailyReward),
		"ID_MSG_L2C_DropActivityExchange":                             reflect.ValueOf(cl.ID_MSG_L2C_DropActivityExchange),
		"ID_MSG_L2C_DropActivityGetActivity":                          reflect.ValueOf(cl.ID_MSG_L2C_DropActivityGetActivity),
		"ID_MSG_L2C_DropActivityMainInfo":                             reflect.ValueOf(cl.ID_MSG_L2C_DropActivityMainInfo),
		"ID_MSG_L2C_DropActivityUpdateActivity":                       reflect.ValueOf(cl.ID_MSG_L2C_DropActivityUpdateActivity),
		"ID_MSG_L2C_Duel":                                             reflect.ValueOf(cl.ID_MSG_L2C_Duel),
		"ID_MSG_L2C_DuelFight":                                        reflect.ValueOf(cl.ID_MSG_L2C_DuelFight),
		"ID_MSG_L2C_DuelSetStatus":                                    reflect.ValueOf(cl.ID_MSG_L2C_DuelSetStatus),
		"ID_MSG_L2C_Dungeon":                                          reflect.ValueOf(cl.ID_MSG_L2C_Dungeon),
		"ID_MSG_L2C_DungeonFight":                                     reflect.ValueOf(cl.ID_MSG_L2C_DungeonFight),
		"ID_MSG_L2C_DungeonPreview":                                   reflect.ValueOf(cl.ID_MSG_L2C_DungeonPreview),
		"ID_MSG_L2C_DungeonRecvAward":                                 reflect.ValueOf(cl.ID_MSG_L2C_DungeonRecvAward),
		"ID_MSG_L2C_DungeonSpeedRecvAward":                            reflect.ValueOf(cl.ID_MSG_L2C_DungeonSpeedRecvAward),
		"ID_MSG_L2C_EmblemBuySlot":                                    reflect.ValueOf(cl.ID_MSG_L2C_EmblemBuySlot),
		"ID_MSG_L2C_EmblemCustomize":                                  reflect.ValueOf(cl.ID_MSG_L2C_EmblemCustomize),
		"ID_MSG_L2C_EmblemDecompose":                                  reflect.ValueOf(cl.ID_MSG_L2C_EmblemDecompose),
		"ID_MSG_L2C_EmblemGet":                                        reflect.ValueOf(cl.ID_MSG_L2C_EmblemGet),
		"ID_MSG_L2C_EmblemGrowTransfer":                               reflect.ValueOf(cl.ID_MSG_L2C_EmblemGrowTransfer),
		"ID_MSG_L2C_EmblemLevelUp":                                    reflect.ValueOf(cl.ID_MSG_L2C_EmblemLevelUp),
		"ID_MSG_L2C_EmblemSetAutoDecompose":                           reflect.ValueOf(cl.ID_MSG_L2C_EmblemSetAutoDecompose),
		"ID_MSG_L2C_EmblemSuccinct":                                   reflect.ValueOf(cl.ID_MSG_L2C_EmblemSuccinct),
		"ID_MSG_L2C_EmblemSuccinctItemConflate":                       reflect.ValueOf(cl.ID_MSG_L2C_EmblemSuccinctItemConflate),
		"ID_MSG_L2C_EmblemSuccinctLockOrSave":                         reflect.ValueOf(cl.ID_MSG_L2C_EmblemSuccinctLockOrSave),
		"ID_MSG_L2C_EmblemUpdate":                                     reflect.ValueOf(cl.ID_MSG_L2C_EmblemUpdate),
		"ID_MSG_L2C_EmblemUpgrade":                                    reflect.ValueOf(cl.ID_MSG_L2C_EmblemUpgrade),
		"ID_MSG_L2C_EmblemWear":                                       reflect.ValueOf(cl.ID_MSG_L2C_EmblemWear),
		"ID_MSG_L2C_EquipDecompose":                                   reflect.ValueOf(cl.ID_MSG_L2C_EquipDecompose),
		"ID_MSG_L2C_EquipEnchant":                                     reflect.ValueOf(cl.ID_MSG_L2C_EquipEnchant),
		"ID_MSG_L2C_EquipEvolution":                                   reflect.ValueOf(cl.ID_MSG_L2C_EquipEvolution),
		"ID_MSG_L2C_EquipGet":                                         reflect.ValueOf(cl.ID_MSG_L2C_EquipGet),
		"ID_MSG_L2C_EquipGrowTransfer":                                reflect.ValueOf(cl.ID_MSG_L2C_EquipGrowTransfer),
		"ID_MSG_L2C_EquipMultipleStrength":                            reflect.ValueOf(cl.ID_MSG_L2C_EquipMultipleStrength),
		"ID_MSG_L2C_EquipRefine":                                      reflect.ValueOf(cl.ID_MSG_L2C_EquipRefine),
		"ID_MSG_L2C_EquipRevive":                                      reflect.ValueOf(cl.ID_MSG_L2C_EquipRevive),
		"ID_MSG_L2C_EquipSetAutoDecompose":                            reflect.ValueOf(cl.ID_MSG_L2C_EquipSetAutoDecompose),
		"ID_MSG_L2C_EquipStrength":                                    reflect.ValueOf(cl.ID_MSG_L2C_EquipStrength),
		"ID_MSG_L2C_EquipUpdate":                                      reflect.ValueOf(cl.ID_MSG_L2C_EquipUpdate),
		"ID_MSG_L2C_EquipWear":                                        reflect.ValueOf(cl.ID_MSG_L2C_EquipWear),
		"ID_MSG_L2C_FlowerAssistNotify":                               reflect.ValueOf(cl.ID_MSG_L2C_FlowerAssistNotify),
		"ID_MSG_L2C_FlowerAssistRecvLike":                             reflect.ValueOf(cl.ID_MSG_L2C_FlowerAssistRecvLike),
		"ID_MSG_L2C_FlowerAssistSendLike":                             reflect.ValueOf(cl.ID_MSG_L2C_FlowerAssistSendLike),
		"ID_MSG_L2C_FlowerAttackLevelGuard":                           reflect.ValueOf(cl.ID_MSG_L2C_FlowerAttackLevelGuard),
		"ID_MSG_L2C_FlowerBuyOccupyAttackNum":                         reflect.ValueOf(cl.ID_MSG_L2C_FlowerBuyOccupyAttackNum),
		"ID_MSG_L2C_FlowerChangeGoblin":                               reflect.ValueOf(cl.ID_MSG_L2C_FlowerChangeGoblin),
		"ID_MSG_L2C_FlowerEnemiesInfo":                                reflect.ValueOf(cl.ID_MSG_L2C_FlowerEnemiesInfo),
		"ID_MSG_L2C_FlowerExtendOccupyTime":                           reflect.ValueOf(cl.ID_MSG_L2C_FlowerExtendOccupyTime),
		"ID_MSG_L2C_FlowerFeedGoblin":                                 reflect.ValueOf(cl.ID_MSG_L2C_FlowerFeedGoblin),
		"ID_MSG_L2C_FlowerFeedSpecial":                                reflect.ValueOf(cl.ID_MSG_L2C_FlowerFeedSpecial),
		"ID_MSG_L2C_FlowerHarvest":                                    reflect.ValueOf(cl.ID_MSG_L2C_FlowerHarvest),
		"ID_MSG_L2C_FlowerJungleInfo":                                 reflect.ValueOf(cl.ID_MSG_L2C_FlowerJungleInfo),
		"ID_MSG_L2C_FlowerLevelUp":                                    reflect.ValueOf(cl.ID_MSG_L2C_FlowerLevelUp),
		"ID_MSG_L2C_FlowerLogList":                                    reflect.ValueOf(cl.ID_MSG_L2C_FlowerLogList),
		"ID_MSG_L2C_FlowerMainInfo":                                   reflect.ValueOf(cl.ID_MSG_L2C_FlowerMainInfo),
		"ID_MSG_L2C_FlowerOccupyAssistList":                           reflect.ValueOf(cl.ID_MSG_L2C_FlowerOccupyAssistList),
		"ID_MSG_L2C_FlowerOccupyAttack":                               reflect.ValueOf(cl.ID_MSG_L2C_FlowerOccupyAttack),
		"ID_MSG_L2C_FlowerOccupyHistory":                              reflect.ValueOf(cl.ID_MSG_L2C_FlowerOccupyHistory),
		"ID_MSG_L2C_FlowerOccupyRecommend":                            reflect.ValueOf(cl.ID_MSG_L2C_FlowerOccupyRecommend),
		"ID_MSG_L2C_FlowerPreviewOccupyAward":                         reflect.ValueOf(cl.ID_MSG_L2C_FlowerPreviewOccupyAward),
		"ID_MSG_L2C_FlowerRecvOccupyAward":                            reflect.ValueOf(cl.ID_MSG_L2C_FlowerRecvOccupyAward),
		"ID_MSG_L2C_FlowerRecvPreviewOccupyAward":                     reflect.ValueOf(cl.ID_MSG_L2C_FlowerRecvPreviewOccupyAward),
		"ID_MSG_L2C_FlowerRevengeOccupy":                              reflect.ValueOf(cl.ID_MSG_L2C_FlowerRevengeOccupy),
		"ID_MSG_L2C_FlowerRevengeSnatch":                              reflect.ValueOf(cl.ID_MSG_L2C_FlowerRevengeSnatch),
		"ID_MSG_L2C_FlowerSearch":                                     reflect.ValueOf(cl.ID_MSG_L2C_FlowerSearch),
		"ID_MSG_L2C_FlowerShareFlowerbed":                             reflect.ValueOf(cl.ID_MSG_L2C_FlowerShareFlowerbed),
		"ID_MSG_L2C_FlowerSnatch":                                     reflect.ValueOf(cl.ID_MSG_L2C_FlowerSnatch),
		"ID_MSG_L2C_FlowerSpeedGrow":                                  reflect.ValueOf(cl.ID_MSG_L2C_FlowerSpeedGrow),
		"ID_MSG_L2C_FlowerStartFeed":                                  reflect.ValueOf(cl.ID_MSG_L2C_FlowerStartFeed),
		"ID_MSG_L2C_FlowerStartPlant":                                 reflect.ValueOf(cl.ID_MSG_L2C_FlowerStartPlant),
		"ID_MSG_L2C_FlowerTimberInfo":                                 reflect.ValueOf(cl.ID_MSG_L2C_FlowerTimberInfo),
		"ID_MSG_L2C_Flush":                                            reflect.ValueOf(cl.ID_MSG_L2C_Flush),
		"ID_MSG_L2C_FlushRedPoint":                                    reflect.ValueOf(cl.ID_MSG_L2C_FlushRedPoint),
		"ID_MSG_L2C_ForecastGetData":                                  reflect.ValueOf(cl.ID_MSG_L2C_ForecastGetData),
		"ID_MSG_L2C_ForecastReceiveAward":                             reflect.ValueOf(cl.ID_MSG_L2C_ForecastReceiveAward),
		"ID_MSG_L2C_ForecastUpdate":                                   reflect.ValueOf(cl.ID_MSG_L2C_ForecastUpdate),
		"ID_MSG_L2C_ForestChangeGoblin":                               reflect.ValueOf(cl.ID_MSG_L2C_ForestChangeGoblin),
		"ID_MSG_L2C_ForestFeedGoblin":                                 reflect.ValueOf(cl.ID_MSG_L2C_ForestFeedGoblin),
		"ID_MSG_L2C_ForestFeedSpecial":                                reflect.ValueOf(cl.ID_MSG_L2C_ForestFeedSpecial),
		"ID_MSG_L2C_ForestHarvest":                                    reflect.ValueOf(cl.ID_MSG_L2C_ForestHarvest),
		"ID_MSG_L2C_ForestInfo":                                       reflect.ValueOf(cl.ID_MSG_L2C_ForestInfo),
		"ID_MSG_L2C_ForestLogList":                                    reflect.ValueOf(cl.ID_MSG_L2C_ForestLogList),
		"ID_MSG_L2C_ForestLoot":                                       reflect.ValueOf(cl.ID_MSG_L2C_ForestLoot),
		"ID_MSG_L2C_ForestRecvLvAward":                                reflect.ValueOf(cl.ID_MSG_L2C_ForestRecvLvAward),
		"ID_MSG_L2C_ForestRevenge":                                    reflect.ValueOf(cl.ID_MSG_L2C_ForestRevenge),
		"ID_MSG_L2C_ForestSearch":                                     reflect.ValueOf(cl.ID_MSG_L2C_ForestSearch),
		"ID_MSG_L2C_ForestSpeedGrow":                                  reflect.ValueOf(cl.ID_MSG_L2C_ForestSpeedGrow),
		"ID_MSG_L2C_ForestStartFeed":                                  reflect.ValueOf(cl.ID_MSG_L2C_ForestStartFeed),
		"ID_MSG_L2C_ForestStartPlant":                                 reflect.ValueOf(cl.ID_MSG_L2C_ForestStartPlant),
		"ID_MSG_L2C_ForestUpdateSlot":                                 reflect.ValueOf(cl.ID_MSG_L2C_ForestUpdateSlot),
		"ID_MSG_L2C_Formation":                                        reflect.ValueOf(cl.ID_MSG_L2C_Formation),
		"ID_MSG_L2C_FragmentCompose":                                  reflect.ValueOf(cl.ID_MSG_L2C_FragmentCompose),
		"ID_MSG_L2C_FragmentComposeAll":                               reflect.ValueOf(cl.ID_MSG_L2C_FragmentComposeAll),
		"ID_MSG_L2C_FragmentComposeTest":                              reflect.ValueOf(cl.ID_MSG_L2C_FragmentComposeTest),
		"ID_MSG_L2C_FragmentEmblemCompose":                            reflect.ValueOf(cl.ID_MSG_L2C_FragmentEmblemCompose),
		"ID_MSG_L2C_FriendAdd":                                        reflect.ValueOf(cl.ID_MSG_L2C_FriendAdd),
		"ID_MSG_L2C_FriendBlacklist":                                  reflect.ValueOf(cl.ID_MSG_L2C_FriendBlacklist),
		"ID_MSG_L2C_FriendConfirm":                                    reflect.ValueOf(cl.ID_MSG_L2C_FriendConfirm),
		"ID_MSG_L2C_FriendDelete":                                     reflect.ValueOf(cl.ID_MSG_L2C_FriendDelete),
		"ID_MSG_L2C_FriendGetBlacklist":                               reflect.ValueOf(cl.ID_MSG_L2C_FriendGetBlacklist),
		"ID_MSG_L2C_FriendInfo":                                       reflect.ValueOf(cl.ID_MSG_L2C_FriendInfo),
		"ID_MSG_L2C_FriendNotify":                                     reflect.ValueOf(cl.ID_MSG_L2C_FriendNotify),
		"ID_MSG_L2C_FriendRecommend":                                  reflect.ValueOf(cl.ID_MSG_L2C_FriendRecommend),
		"ID_MSG_L2C_FriendRecvLike":                                   reflect.ValueOf(cl.ID_MSG_L2C_FriendRecvLike),
		"ID_MSG_L2C_FriendRemBlacklist":                               reflect.ValueOf(cl.ID_MSG_L2C_FriendRemBlacklist),
		"ID_MSG_L2C_FriendRequestInfo":                                reflect.ValueOf(cl.ID_MSG_L2C_FriendRequestInfo),
		"ID_MSG_L2C_FriendSearch":                                     reflect.ValueOf(cl.ID_MSG_L2C_FriendSearch),
		"ID_MSG_L2C_FriendSendLike":                                   reflect.ValueOf(cl.ID_MSG_L2C_FriendSendLike),
		"ID_MSG_L2C_FunctionGetStatus":                                reflect.ValueOf(cl.ID_MSG_L2C_FunctionGetStatus),
		"ID_MSG_L2C_GM":                                               reflect.ValueOf(cl.ID_MSG_L2C_GM),
		"ID_MSG_L2C_GSTArenaVote":                                     reflect.ValueOf(cl.ID_MSG_L2C_GSTArenaVote),
		"ID_MSG_L2C_GSTBossAward":                                     reflect.ValueOf(cl.ID_MSG_L2C_GSTBossAward),
		"ID_MSG_L2C_GSTBossBuyChallenge":                              reflect.ValueOf(cl.ID_MSG_L2C_GSTBossBuyChallenge),
		"ID_MSG_L2C_GSTBossFight":                                     reflect.ValueOf(cl.ID_MSG_L2C_GSTBossFight),
		"ID_MSG_L2C_GSTBossGet":                                       reflect.ValueOf(cl.ID_MSG_L2C_GSTBossGet),
		"ID_MSG_L2C_GSTBossRank":                                      reflect.ValueOf(cl.ID_MSG_L2C_GSTBossRank),
		"ID_MSG_L2C_GSTChallengeBuffChoose":                           reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeBuffChoose),
		"ID_MSG_L2C_GSTChallengeFight":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeFight),
		"ID_MSG_L2C_GSTChallengeFightLogList":                         reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeFightLogList),
		"ID_MSG_L2C_GSTChallengeGetData":                              reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeGetData),
		"ID_MSG_L2C_GSTChallengeMatch":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeMatch),
		"ID_MSG_L2C_GSTChallengeRank":                                 reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeRank),
		"ID_MSG_L2C_GSTChallengeTaskReward":                           reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeTaskReward),
		"ID_MSG_L2C_GSTChallengeTaskUpdate":                           reflect.ValueOf(cl.ID_MSG_L2C_GSTChallengeTaskUpdate),
		"ID_MSG_L2C_GSTDonate":                                        reflect.ValueOf(cl.ID_MSG_L2C_GSTDonate),
		"ID_MSG_L2C_GSTDragonEvolve":                                  reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonEvolve),
		"ID_MSG_L2C_GSTDragonFight":                                   reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonFight),
		"ID_MSG_L2C_GSTDragonFightBuyCount":                           reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonFightBuyCount),
		"ID_MSG_L2C_GSTDragonFightUseTicket":                          reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonFightUseTicket),
		"ID_MSG_L2C_GSTDragonGetCultivation":                          reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonGetCultivation),
		"ID_MSG_L2C_GSTDragonGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonGetData),
		"ID_MSG_L2C_GSTDragonRank":                                    reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonRank),
		"ID_MSG_L2C_GSTDragonShow":                                    reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonShow),
		"ID_MSG_L2C_GSTDragonSkillOperate":                            reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonSkillOperate),
		"ID_MSG_L2C_GSTDragonStrategySkill":                           reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonStrategySkill),
		"ID_MSG_L2C_GSTDragonTaskAward":                               reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonTaskAward),
		"ID_MSG_L2C_GSTDragonTaskGetData":                             reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonTaskGetData),
		"ID_MSG_L2C_GSTDragonTaskUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_GSTDragonTaskUpdate),
		"ID_MSG_L2C_GSTExchangeGroundTeam":                            reflect.ValueOf(cl.ID_MSG_L2C_GSTExchangeGroundTeam),
		"ID_MSG_L2C_GSTGetArenaVoteRecord":                            reflect.ValueOf(cl.ID_MSG_L2C_GSTGetArenaVoteRecord),
		"ID_MSG_L2C_GSTGetData":                                       reflect.ValueOf(cl.ID_MSG_L2C_GSTGetData),
		"ID_MSG_L2C_GSTGetGroundData":                                 reflect.ValueOf(cl.ID_MSG_L2C_GSTGetGroundData),
		"ID_MSG_L2C_GSTGetGuildDonateData":                            reflect.ValueOf(cl.ID_MSG_L2C_GSTGetGuildDonateData),
		"ID_MSG_L2C_GSTGetGuildDonateMemData":                         reflect.ValueOf(cl.ID_MSG_L2C_GSTGetGuildDonateMemData),
		"ID_MSG_L2C_GSTGetHangUpReward":                               reflect.ValueOf(cl.ID_MSG_L2C_GSTGetHangUpReward),
		"ID_MSG_L2C_GSTGetLogData":                                    reflect.ValueOf(cl.ID_MSG_L2C_GSTGetLogData),
		"ID_MSG_L2C_GSTGetTasksData":                                  reflect.ValueOf(cl.ID_MSG_L2C_GSTGetTasksData),
		"ID_MSG_L2C_GSTGetTasksReward":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTGetTasksReward),
		"ID_MSG_L2C_GSTGetTeamsData":                                  reflect.ValueOf(cl.ID_MSG_L2C_GSTGetTeamsData),
		"ID_MSG_L2C_GSTGroupUsersRank":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTGroupUsersRank),
		"ID_MSG_L2C_GSTGroupUsersRankLike":                            reflect.ValueOf(cl.ID_MSG_L2C_GSTGroupUsersRankLike),
		"ID_MSG_L2C_GSTGuildBuildDispatchHero":                        reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildDispatchHero),
		"ID_MSG_L2C_GSTGuildBuildDonate":                              reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildDonate),
		"ID_MSG_L2C_GSTGuildBuildDonateRank":                          reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildDonateRank),
		"ID_MSG_L2C_GSTGuildBuildGetData":                             reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildGetData),
		"ID_MSG_L2C_GSTGuildBuildGetTaskData":                         reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildGetTaskData),
		"ID_MSG_L2C_GSTGuildBuildRecvTaskAward":                       reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildRecvTaskAward),
		"ID_MSG_L2C_GSTGuildBuildTaskUpdate":                          reflect.ValueOf(cl.ID_MSG_L2C_GSTGuildBuildTaskUpdate),
		"ID_MSG_L2C_GSTMessageEdit":                                   reflect.ValueOf(cl.ID_MSG_L2C_GSTMessageEdit),
		"ID_MSG_L2C_GSTOreBuyTimes":                                   reflect.ValueOf(cl.ID_MSG_L2C_GSTOreBuyTimes),
		"ID_MSG_L2C_GSTOreFight":                                      reflect.ValueOf(cl.ID_MSG_L2C_GSTOreFight),
		"ID_MSG_L2C_GSTOreGetData":                                    reflect.ValueOf(cl.ID_MSG_L2C_GSTOreGetData),
		"ID_MSG_L2C_GSTOreGetOreData":                                 reflect.ValueOf(cl.ID_MSG_L2C_GSTOreGetOreData),
		"ID_MSG_L2C_GSTOreOccupy":                                     reflect.ValueOf(cl.ID_MSG_L2C_GSTOreOccupy),
		"ID_MSG_L2C_GSTOreSearchAssist":                               reflect.ValueOf(cl.ID_MSG_L2C_GSTOreSearchAssist),
		"ID_MSG_L2C_GSTPreviewHangUpReward":                           reflect.ValueOf(cl.ID_MSG_L2C_GSTPreviewHangUpReward),
		"ID_MSG_L2C_GSTPushSta":                                       reflect.ValueOf(cl.ID_MSG_L2C_GSTPushSta),
		"ID_MSG_L2C_GSTRank":                                          reflect.ValueOf(cl.ID_MSG_L2C_GSTRank),
		"ID_MSG_L2C_GSTScorePreview":                                  reflect.ValueOf(cl.ID_MSG_L2C_GSTScorePreview),
		"ID_MSG_L2C_GSTSkillAssemble":                                 reflect.ValueOf(cl.ID_MSG_L2C_GSTSkillAssemble),
		"ID_MSG_L2C_GSTTaskTypeUpdate":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTTaskTypeUpdate),
		"ID_MSG_L2C_GSTTeamOperate":                                   reflect.ValueOf(cl.ID_MSG_L2C_GSTTeamOperate),
		"ID_MSG_L2C_GSTTechDonate":                                    reflect.ValueOf(cl.ID_MSG_L2C_GSTTechDonate),
		"ID_MSG_L2C_GSTTechGetData":                                   reflect.ValueOf(cl.ID_MSG_L2C_GSTTechGetData),
		"ID_MSG_L2C_GSTTechGuildUserRank":                             reflect.ValueOf(cl.ID_MSG_L2C_GSTTechGuildUserRank),
		"ID_MSG_L2C_GSTTechSign":                                      reflect.ValueOf(cl.ID_MSG_L2C_GSTTechSign),
		"ID_MSG_L2C_GSTTechTaskReward":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTTechTaskReward),
		"ID_MSG_L2C_GSTTechTaskUpdate":                                reflect.ValueOf(cl.ID_MSG_L2C_GSTTechTaskUpdate),
		"ID_MSG_L2C_GemCompose":                                       reflect.ValueOf(cl.ID_MSG_L2C_GemCompose),
		"ID_MSG_L2C_GemConvert":                                       reflect.ValueOf(cl.ID_MSG_L2C_GemConvert),
		"ID_MSG_L2C_GemDecompose":                                     reflect.ValueOf(cl.ID_MSG_L2C_GemDecompose),
		"ID_MSG_L2C_GemWear":                                          reflect.ValueOf(cl.ID_MSG_L2C_GemWear),
		"ID_MSG_L2C_GetAchieveShowcase":                               reflect.ValueOf(cl.ID_MSG_L2C_GetAchieveShowcase),
		"ID_MSG_L2C_GetBags":                                          reflect.ValueOf(cl.ID_MSG_L2C_GetBags),
		"ID_MSG_L2C_GetBattleReport":                                  reflect.ValueOf(cl.ID_MSG_L2C_GetBattleReport),
		"ID_MSG_L2C_GetClientInfo":                                    reflect.ValueOf(cl.ID_MSG_L2C_GetClientInfo),
		"ID_MSG_L2C_GetCognitionLog":                                  reflect.ValueOf(cl.ID_MSG_L2C_GetCognitionLog),
		"ID_MSG_L2C_GetCommonRank":                                    reflect.ValueOf(cl.ID_MSG_L2C_GetCommonRank),
		"ID_MSG_L2C_GetCommonRankFirst":                               reflect.ValueOf(cl.ID_MSG_L2C_GetCommonRankFirst),
		"ID_MSG_L2C_GetCrossRankFirst":                                reflect.ValueOf(cl.ID_MSG_L2C_GetCrossRankFirst),
		"ID_MSG_L2C_GetDefFormationPower":                             reflect.ValueOf(cl.ID_MSG_L2C_GetDefFormationPower),
		"ID_MSG_L2C_GetFormation":                                     reflect.ValueOf(cl.ID_MSG_L2C_GetFormation),
		"ID_MSG_L2C_GetGems":                                          reflect.ValueOf(cl.ID_MSG_L2C_GetGems),
		"ID_MSG_L2C_GetGiftCodeAward":                                 reflect.ValueOf(cl.ID_MSG_L2C_GetGiftCodeAward),
		"ID_MSG_L2C_GetMails":                                         reflect.ValueOf(cl.ID_MSG_L2C_GetMails),
		"ID_MSG_L2C_GetPush":                                          reflect.ValueOf(cl.ID_MSG_L2C_GetPush),
		"ID_MSG_L2C_GetSeasonFlashBackData":                           reflect.ValueOf(cl.ID_MSG_L2C_GetSeasonFlashBackData),
		"ID_MSG_L2C_GetSeasonRankFirst":                               reflect.ValueOf(cl.ID_MSG_L2C_GetSeasonRankFirst),
		"ID_MSG_L2C_GetSeasonRankList":                                reflect.ValueOf(cl.ID_MSG_L2C_GetSeasonRankList),
		"ID_MSG_L2C_GetUser":                                          reflect.ValueOf(cl.ID_MSG_L2C_GetUser),
		"ID_MSG_L2C_GetUserBattleData":                                reflect.ValueOf(cl.ID_MSG_L2C_GetUserBattleData),
		"ID_MSG_L2C_GetUserSnapshots":                                 reflect.ValueOf(cl.ID_MSG_L2C_GetUserSnapshots),
		"ID_MSG_L2C_GlobalAttrGet":                                    reflect.ValueOf(cl.ID_MSG_L2C_GlobalAttrGet),
		"ID_MSG_L2C_GlobalAttrScoreGet":                               reflect.ValueOf(cl.ID_MSG_L2C_GlobalAttrScoreGet),
		"ID_MSG_L2C_GodPresentGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_GodPresentGetData),
		"ID_MSG_L2C_GodPresentRecvAwards":                             reflect.ValueOf(cl.ID_MSG_L2C_GodPresentRecvAwards),
		"ID_MSG_L2C_GodPresentRecvItem":                               reflect.ValueOf(cl.ID_MSG_L2C_GodPresentRecvItem),
		"ID_MSG_L2C_GodPresentSummon":                                 reflect.ValueOf(cl.ID_MSG_L2C_GodPresentSummon),
		"ID_MSG_L2C_GoddessChangeSuit":                                reflect.ValueOf(cl.ID_MSG_L2C_GoddessChangeSuit),
		"ID_MSG_L2C_GoddessChapterFight":                              reflect.ValueOf(cl.ID_MSG_L2C_GoddessChapterFight),
		"ID_MSG_L2C_GoddessCollect":                                   reflect.ValueOf(cl.ID_MSG_L2C_GoddessCollect),
		"ID_MSG_L2C_GoddessContractGetData":                           reflect.ValueOf(cl.ID_MSG_L2C_GoddessContractGetData),
		"ID_MSG_L2C_GoddessFeed":                                      reflect.ValueOf(cl.ID_MSG_L2C_GoddessFeed),
		"ID_MSG_L2C_GoddessRecovery":                                  reflect.ValueOf(cl.ID_MSG_L2C_GoddessRecovery),
		"ID_MSG_L2C_GoddessStoryAward":                                reflect.ValueOf(cl.ID_MSG_L2C_GoddessStoryAward),
		"ID_MSG_L2C_GoddessSuitUnlock":                                reflect.ValueOf(cl.ID_MSG_L2C_GoddessSuitUnlock),
		"ID_MSG_L2C_GoddessTouch":                                     reflect.ValueOf(cl.ID_MSG_L2C_GoddessTouch),
		"ID_MSG_L2C_GoddessUnlock":                                    reflect.ValueOf(cl.ID_MSG_L2C_GoddessUnlock),
		"ID_MSG_L2C_GoddessUpdateSuitIds":                             reflect.ValueOf(cl.ID_MSG_L2C_GoddessUpdateSuitIds),
		"ID_MSG_L2C_GoldBuyGet":                                       reflect.ValueOf(cl.ID_MSG_L2C_GoldBuyGet),
		"ID_MSG_L2C_GoldBuyGetGold":                                   reflect.ValueOf(cl.ID_MSG_L2C_GoldBuyGetGold),
		"ID_MSG_L2C_GuidanceFinishGroup":                              reflect.ValueOf(cl.ID_MSG_L2C_GuidanceFinishGroup),
		"ID_MSG_L2C_GuidanceFinishStep":                               reflect.ValueOf(cl.ID_MSG_L2C_GuidanceFinishStep),
		"ID_MSG_L2C_GuidanceList":                                     reflect.ValueOf(cl.ID_MSG_L2C_GuidanceList),
		"ID_MSG_L2C_GuidanceSkip":                                     reflect.ValueOf(cl.ID_MSG_L2C_GuidanceSkip),
		"ID_MSG_L2C_GuildApplyList":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildApplyList),
		"ID_MSG_L2C_GuildApplyRatify":                                 reflect.ValueOf(cl.ID_MSG_L2C_GuildApplyRatify),
		"ID_MSG_L2C_GuildChestActivate":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildChestActivate),
		"ID_MSG_L2C_GuildChestGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_GuildChestGetData),
		"ID_MSG_L2C_GuildChestLike":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildChestLike),
		"ID_MSG_L2C_GuildChestRecv":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildChestRecv),
		"ID_MSG_L2C_GuildCombineApply":                                reflect.ValueOf(cl.ID_MSG_L2C_GuildCombineApply),
		"ID_MSG_L2C_GuildCombineCheck":                                reflect.ValueOf(cl.ID_MSG_L2C_GuildCombineCheck),
		"ID_MSG_L2C_GuildCombineRatify":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildCombineRatify),
		"ID_MSG_L2C_GuildCreate":                                      reflect.ValueOf(cl.ID_MSG_L2C_GuildCreate),
		"ID_MSG_L2C_GuildDisband":                                     reflect.ValueOf(cl.ID_MSG_L2C_GuildDisband),
		"ID_MSG_L2C_GuildDonate":                                      reflect.ValueOf(cl.ID_MSG_L2C_GuildDonate),
		"ID_MSG_L2C_GuildDonateLogList":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildDonateLogList),
		"ID_MSG_L2C_GuildDungeonBuyChallengeTimes":                    reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonBuyChallengeTimes),
		"ID_MSG_L2C_GuildDungeonChapterInfo":                          reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonChapterInfo),
		"ID_MSG_L2C_GuildDungeonFight":                                reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonFight),
		"ID_MSG_L2C_GuildDungeonGetMembersFightInfo":                  reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonGetMembersFightInfo),
		"ID_MSG_L2C_GuildDungeonGetStrategy":                          reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonGetStrategy),
		"ID_MSG_L2C_GuildDungeonHallOfFame":                           reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonHallOfFame),
		"ID_MSG_L2C_GuildDungeonInfo":                                 reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonInfo),
		"ID_MSG_L2C_GuildDungeonLogList":                              reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonLogList),
		"ID_MSG_L2C_GuildDungeonNotify":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonNotify),
		"ID_MSG_L2C_GuildDungeonRecvAllBossBoxAward":                  reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonRecvAllBossBoxAward),
		"ID_MSG_L2C_GuildDungeonRecvBossBoxAward":                     reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonRecvBossBoxAward),
		"ID_MSG_L2C_GuildDungeonRecvChapterTaskAward":                 reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonRecvChapterTaskAward),
		"ID_MSG_L2C_GuildDungeonSeasonDivisionAward":                  reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonSeasonDivisionAward),
		"ID_MSG_L2C_GuildDungeonSetFocus":                             reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonSetFocus),
		"ID_MSG_L2C_GuildDungeonStrategyUseNotify":                    reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonStrategyUseNotify),
		"ID_MSG_L2C_GuildDungeonTop3Guild":                            reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonTop3Guild),
		"ID_MSG_L2C_GuildDungeonUseStrategy":                          reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonUseStrategy),
		"ID_MSG_L2C_GuildDungeonUserDamageRank":                       reflect.ValueOf(cl.ID_MSG_L2C_GuildDungeonUserDamageRank),
		"ID_MSG_L2C_GuildGetBadgeList":                                reflect.ValueOf(cl.ID_MSG_L2C_GuildGetBadgeList),
		"ID_MSG_L2C_GuildGetDeclaration":                              reflect.ValueOf(cl.ID_MSG_L2C_GuildGetDeclaration),
		"ID_MSG_L2C_GuildGetDivisionAwardInfo":                        reflect.ValueOf(cl.ID_MSG_L2C_GuildGetDivisionAwardInfo),
		"ID_MSG_L2C_GuildGetDonateAward":                              reflect.ValueOf(cl.ID_MSG_L2C_GuildGetDonateAward),
		"ID_MSG_L2C_GuildGetMedals":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildGetMedals),
		"ID_MSG_L2C_GuildGetMembers":                                  reflect.ValueOf(cl.ID_MSG_L2C_GuildGetMembers),
		"ID_MSG_L2C_GuildGetMyInfo":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildGetMyInfo),
		"ID_MSG_L2C_GuildList":                                        reflect.ValueOf(cl.ID_MSG_L2C_GuildList),
		"ID_MSG_L2C_GuildListGetDetail":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildListGetDetail),
		"ID_MSG_L2C_GuildLogList":                                     reflect.ValueOf(cl.ID_MSG_L2C_GuildLogList),
		"ID_MSG_L2C_GuildMainInfo":                                    reflect.ValueOf(cl.ID_MSG_L2C_GuildMainInfo),
		"ID_MSG_L2C_GuildManagerMember":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildManagerMember),
		"ID_MSG_L2C_GuildMedalLike":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildMedalLike),
		"ID_MSG_L2C_GuildMobilizationAcceptTask":                      reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationAcceptTask),
		"ID_MSG_L2C_GuildMobilizationBuyTimes":                        reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationBuyTimes),
		"ID_MSG_L2C_GuildMobilizationEditMessageBoard":                reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationEditMessageBoard),
		"ID_MSG_L2C_GuildMobilizationFinishTaskLogs":                  reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationFinishTaskLogs),
		"ID_MSG_L2C_GuildMobilizationFreshTask":                       reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationFreshTask),
		"ID_MSG_L2C_GuildMobilizationGetData":                         reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationGetData),
		"ID_MSG_L2C_GuildMobilizationGiveUpTask":                      reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationGiveUpTask),
		"ID_MSG_L2C_GuildMobilizationGuildRank":                       reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationGuildRank),
		"ID_MSG_L2C_GuildMobilizationPersonalRank":                    reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationPersonalRank),
		"ID_MSG_L2C_GuildMobilizationRecvScoreLevel":                  reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationRecvScoreLevel),
		"ID_MSG_L2C_GuildMobilizationScoreAward":                      reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationScoreAward),
		"ID_MSG_L2C_GuildMobilizationSignTask":                        reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationSignTask),
		"ID_MSG_L2C_GuildMobilizationUpdateTaskProgress":              reflect.ValueOf(cl.ID_MSG_L2C_GuildMobilizationUpdateTaskProgress),
		"ID_MSG_L2C_GuildModifyInfo":                                  reflect.ValueOf(cl.ID_MSG_L2C_GuildModifyInfo),
		"ID_MSG_L2C_GuildModifyNotice":                                reflect.ValueOf(cl.ID_MSG_L2C_GuildModifyNotice),
		"ID_MSG_L2C_GuildNotify":                                      reflect.ValueOf(cl.ID_MSG_L2C_GuildNotify),
		"ID_MSG_L2C_GuildQuit":                                        reflect.ValueOf(cl.ID_MSG_L2C_GuildQuit),
		"ID_MSG_L2C_GuildRank":                                        reflect.ValueOf(cl.ID_MSG_L2C_GuildRank),
		"ID_MSG_L2C_GuildSearch":                                      reflect.ValueOf(cl.ID_MSG_L2C_GuildSearch),
		"ID_MSG_L2C_GuildSendMail":                                    reflect.ValueOf(cl.ID_MSG_L2C_GuildSendMail),
		"ID_MSG_L2C_GuildSendRecruitMsg":                              reflect.ValueOf(cl.ID_MSG_L2C_GuildSendRecruitMsg),
		"ID_MSG_L2C_GuildSetName":                                     reflect.ValueOf(cl.ID_MSG_L2C_GuildSetName),
		"ID_MSG_L2C_GuildSyncKickCount":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildSyncKickCount),
		"ID_MSG_L2C_GuildTalentLevelUp":                               reflect.ValueOf(cl.ID_MSG_L2C_GuildTalentLevelUp),
		"ID_MSG_L2C_GuildTalentList":                                  reflect.ValueOf(cl.ID_MSG_L2C_GuildTalentList),
		"ID_MSG_L2C_GuildTalentReset":                                 reflect.ValueOf(cl.ID_MSG_L2C_GuildTalentReset),
		"ID_MSG_L2C_GuildUpdateInfo":                                  reflect.ValueOf(cl.ID_MSG_L2C_GuildUpdateInfo),
		"ID_MSG_L2C_GuildUserApply":                                   reflect.ValueOf(cl.ID_MSG_L2C_GuildUserApply),
		"ID_MSG_L2C_HandbooksActive":                                  reflect.ValueOf(cl.ID_MSG_L2C_HandbooksActive),
		"ID_MSG_L2C_HandbooksActiveHeroAttr":                          reflect.ValueOf(cl.ID_MSG_L2C_HandbooksActiveHeroAttr),
		"ID_MSG_L2C_HandbooksGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_HandbooksGetData),
		"ID_MSG_L2C_HandbooksReceiveAwards":                           reflect.ValueOf(cl.ID_MSG_L2C_HandbooksReceiveAwards),
		"ID_MSG_L2C_HasRecvH5DesktopReward":                           reflect.ValueOf(cl.ID_MSG_L2C_HasRecvH5DesktopReward),
		"ID_MSG_L2C_HeroAwaken":                                       reflect.ValueOf(cl.ID_MSG_L2C_HeroAwaken),
		"ID_MSG_L2C_HeroBack":                                         reflect.ValueOf(cl.ID_MSG_L2C_HeroBack),
		"ID_MSG_L2C_HeroBuySlot":                                      reflect.ValueOf(cl.ID_MSG_L2C_HeroBuySlot),
		"ID_MSG_L2C_HeroConversion":                                   reflect.ValueOf(cl.ID_MSG_L2C_HeroConversion),
		"ID_MSG_L2C_HeroConvert":                                      reflect.ValueOf(cl.ID_MSG_L2C_HeroConvert),
		"ID_MSG_L2C_HeroConvertAwakenItem":                            reflect.ValueOf(cl.ID_MSG_L2C_HeroConvertAwakenItem),
		"ID_MSG_L2C_HeroDecompose":                                    reflect.ValueOf(cl.ID_MSG_L2C_HeroDecompose),
		"ID_MSG_L2C_HeroExchange":                                     reflect.ValueOf(cl.ID_MSG_L2C_HeroExchange),
		"ID_MSG_L2C_HeroGemLevelUp":                                   reflect.ValueOf(cl.ID_MSG_L2C_HeroGemLevelUp),
		"ID_MSG_L2C_HeroGetStarUpCosts":                               reflect.ValueOf(cl.ID_MSG_L2C_HeroGetStarUpCosts),
		"ID_MSG_L2C_HeroLevelUp":                                      reflect.ValueOf(cl.ID_MSG_L2C_HeroLevelUp),
		"ID_MSG_L2C_HeroList":                                         reflect.ValueOf(cl.ID_MSG_L2C_HeroList),
		"ID_MSG_L2C_HeroRevive":                                       reflect.ValueOf(cl.ID_MSG_L2C_HeroRevive),
		"ID_MSG_L2C_HeroStageUp":                                      reflect.ValueOf(cl.ID_MSG_L2C_HeroStageUp),
		"ID_MSG_L2C_HeroStarUp":                                       reflect.ValueOf(cl.ID_MSG_L2C_HeroStarUp),
		"ID_MSG_L2C_HeroStarUpRedList":                                reflect.ValueOf(cl.ID_MSG_L2C_HeroStarUpRedList),
		"ID_MSG_L2C_HeroTagUpdate":                                    reflect.ValueOf(cl.ID_MSG_L2C_HeroTagUpdate),
		"ID_MSG_L2C_HeroTestAttr":                                     reflect.ValueOf(cl.ID_MSG_L2C_HeroTestAttr),
		"ID_MSG_L2C_HeroTestCalPower":                                 reflect.ValueOf(cl.ID_MSG_L2C_HeroTestCalPower),
		"ID_MSG_L2C_HeroUpdateLockStatus":                             reflect.ValueOf(cl.ID_MSG_L2C_HeroUpdateLockStatus),
		"ID_MSG_L2C_HeroUpdateStarLimit":                              reflect.ValueOf(cl.ID_MSG_L2C_HeroUpdateStarLimit),
		"ID_MSG_L2C_HeroesUpdate":                                     reflect.ValueOf(cl.ID_MSG_L2C_HeroesUpdate),
		"ID_MSG_L2C_HotRankGet":                                       reflect.ValueOf(cl.ID_MSG_L2C_HotRankGet),
		"ID_MSG_L2C_ItemSelect":                                       reflect.ValueOf(cl.ID_MSG_L2C_ItemSelect),
		"ID_MSG_L2C_KeepAlive":                                        reflect.ValueOf(cl.ID_MSG_L2C_KeepAlive),
		"ID_MSG_L2C_LinkHeroSummon":                                   reflect.ValueOf(cl.ID_MSG_L2C_LinkHeroSummon),
		"ID_MSG_L2C_LinkHeroSummonGet":                                reflect.ValueOf(cl.ID_MSG_L2C_LinkHeroSummonGet),
		"ID_MSG_L2C_LinkHeroSummonPoolTimeUpdate":                     reflect.ValueOf(cl.ID_MSG_L2C_LinkHeroSummonPoolTimeUpdate),
		"ID_MSG_L2C_LinkHeroSummonTest":                               reflect.ValueOf(cl.ID_MSG_L2C_LinkHeroSummonTest),
		"ID_MSG_L2C_LinkInfo":                                         reflect.ValueOf(cl.ID_MSG_L2C_LinkInfo),
		"ID_MSG_L2C_LinkSetView":                                      reflect.ValueOf(cl.ID_MSG_L2C_LinkSetView),
		"ID_MSG_L2C_MazeBuyRevive":                                    reflect.ValueOf(cl.ID_MSG_L2C_MazeBuyRevive),
		"ID_MSG_L2C_MazeGetGrid":                                      reflect.ValueOf(cl.ID_MSG_L2C_MazeGetGrid),
		"ID_MSG_L2C_MazeGetMap":                                       reflect.ValueOf(cl.ID_MSG_L2C_MazeGetMap),
		"ID_MSG_L2C_MazeGetSelectMapData":                             reflect.ValueOf(cl.ID_MSG_L2C_MazeGetSelectMapData),
		"ID_MSG_L2C_MazeRecoveryHero":                                 reflect.ValueOf(cl.ID_MSG_L2C_MazeRecoveryHero),
		"ID_MSG_L2C_MazeSelectBuff":                                   reflect.ValueOf(cl.ID_MSG_L2C_MazeSelectBuff),
		"ID_MSG_L2C_MazeSweep":                                        reflect.ValueOf(cl.ID_MSG_L2C_MazeSweep),
		"ID_MSG_L2C_MazeTaskReceiveAward":                             reflect.ValueOf(cl.ID_MSG_L2C_MazeTaskReceiveAward),
		"ID_MSG_L2C_MazeTaskUpdate":                                   reflect.ValueOf(cl.ID_MSG_L2C_MazeTaskUpdate),
		"ID_MSG_L2C_MazeTriggerEvent":                                 reflect.ValueOf(cl.ID_MSG_L2C_MazeTriggerEvent),
		"ID_MSG_L2C_MazeUseItem":                                      reflect.ValueOf(cl.ID_MSG_L2C_MazeUseItem),
		"ID_MSG_L2C_MedalGetData":                                     reflect.ValueOf(cl.ID_MSG_L2C_MedalGetData),
		"ID_MSG_L2C_MedalGoddessCureUpdate":                           reflect.ValueOf(cl.ID_MSG_L2C_MedalGoddessCureUpdate),
		"ID_MSG_L2C_MedalReceiveAward":                                reflect.ValueOf(cl.ID_MSG_L2C_MedalReceiveAward),
		"ID_MSG_L2C_MedalUpdate":                                      reflect.ValueOf(cl.ID_MSG_L2C_MedalUpdate),
		"ID_MSG_L2C_MemoryLatest":                                     reflect.ValueOf(cl.ID_MSG_L2C_MemoryLatest),
		"ID_MSG_L2C_MemoryUnlock":                                     reflect.ValueOf(cl.ID_MSG_L2C_MemoryUnlock),
		"ID_MSG_L2C_MirageBuyCount":                                   reflect.ValueOf(cl.ID_MSG_L2C_MirageBuyCount),
		"ID_MSG_L2C_MirageDetail":                                     reflect.ValueOf(cl.ID_MSG_L2C_MirageDetail),
		"ID_MSG_L2C_MirageFight":                                      reflect.ValueOf(cl.ID_MSG_L2C_MirageFight),
		"ID_MSG_L2C_MirageList":                                       reflect.ValueOf(cl.ID_MSG_L2C_MirageList),
		"ID_MSG_L2C_MiragePowerCrush":                                 reflect.ValueOf(cl.ID_MSG_L2C_MiragePowerCrush),
		"ID_MSG_L2C_MirageReceiveAward":                               reflect.ValueOf(cl.ID_MSG_L2C_MirageReceiveAward),
		"ID_MSG_L2C_MirageSaveAffixes":                                reflect.ValueOf(cl.ID_MSG_L2C_MirageSaveAffixes),
		"ID_MSG_L2C_MirageSweep":                                      reflect.ValueOf(cl.ID_MSG_L2C_MirageSweep),
		"ID_MSG_L2C_MirageTestDrop":                                   reflect.ValueOf(cl.ID_MSG_L2C_MirageTestDrop),
		"ID_MSG_L2C_MonthTasksGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_MonthTasksGetData),
		"ID_MSG_L2C_MonthTasksRecvAwards":                             reflect.ValueOf(cl.ID_MSG_L2C_MonthTasksRecvAwards),
		"ID_MSG_L2C_MonthTasksUpdate":                                 reflect.ValueOf(cl.ID_MSG_L2C_MonthTasksUpdate),
		"ID_MSG_L2C_MonthlyCardGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_MonthlyCardGetData),
		"ID_MSG_L2C_MonthlyCardReceiveAward":                          reflect.ValueOf(cl.ID_MSG_L2C_MonthlyCardReceiveAward),
		"ID_MSG_L2C_MonthlyCardRechargeNotify":                        reflect.ValueOf(cl.ID_MSG_L2C_MonthlyCardRechargeNotify),
		"ID_MSG_L2C_MuteAccount":                                      reflect.ValueOf(cl.ID_MSG_L2C_MuteAccount),
		"ID_MSG_L2C_NewMailTip":                                       reflect.ValueOf(cl.ID_MSG_L2C_NewMailTip),
		"ID_MSG_L2C_NewYearActivityGetData":                           reflect.ValueOf(cl.ID_MSG_L2C_NewYearActivityGetData),
		"ID_MSG_L2C_NewYearActivityLoginAward":                        reflect.ValueOf(cl.ID_MSG_L2C_NewYearActivityLoginAward),
		"ID_MSG_L2C_NotifyBanCmd":                                     reflect.ValueOf(cl.ID_MSG_L2C_NotifyBanCmd),
		"ID_MSG_L2C_OSSUrl":                                           reflect.ValueOf(cl.ID_MSG_L2C_OSSUrl),
		"ID_MSG_L2C_OpGlobalAttr":                                     reflect.ValueOf(cl.ID_MSG_L2C_OpGlobalAttr),
		"ID_MSG_L2C_OpNum":                                            reflect.ValueOf(cl.ID_MSG_L2C_OpNum),
		"ID_MSG_L2C_OpResources":                                      reflect.ValueOf(cl.ID_MSG_L2C_OpResources),
		"ID_MSG_L2C_OperateActivityCanXMLUpdate":                      reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityCanXMLUpdate),
		"ID_MSG_L2C_OperateActivityGetData":                           reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityGetData),
		"ID_MSG_L2C_OperateActivityGetTaskReward":                     reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityGetTaskReward),
		"ID_MSG_L2C_OperateActivityGetXML":                            reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityGetXML),
		"ID_MSG_L2C_OperateActivityInitActivity":                      reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityInitActivity),
		"ID_MSG_L2C_OperateActivityPromotionRechargeCheck":            reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityPromotionRechargeCheck),
		"ID_MSG_L2C_OperateActivityPromotionSelectAward":              reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityPromotionSelectAward),
		"ID_MSG_L2C_OperateActivityRecharge":                          reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityRecharge),
		"ID_MSG_L2C_OperateActivityUpdateData":                        reflect.ValueOf(cl.ID_MSG_L2C_OperateActivityUpdateData),
		"ID_MSG_L2C_OssBattleReport":                                  reflect.ValueOf(cl.ID_MSG_L2C_OssBattleReport),
		"ID_MSG_L2C_PassBuyNotify":                                    reflect.ValueOf(cl.ID_MSG_L2C_PassBuyNotify),
		"ID_MSG_L2C_PassGetData":                                      reflect.ValueOf(cl.ID_MSG_L2C_PassGetData),
		"ID_MSG_L2C_PassLevelBuy":                                     reflect.ValueOf(cl.ID_MSG_L2C_PassLevelBuy),
		"ID_MSG_L2C_PassReceiveAward":                                 reflect.ValueOf(cl.ID_MSG_L2C_PassReceiveAward),
		"ID_MSG_L2C_PassUpdate":                                       reflect.ValueOf(cl.ID_MSG_L2C_PassUpdate),
		"ID_MSG_L2C_PeakBaseData":                                     reflect.ValueOf(cl.ID_MSG_L2C_PeakBaseData),
		"ID_MSG_L2C_PeakDoGuess":                                      reflect.ValueOf(cl.ID_MSG_L2C_PeakDoGuess),
		"ID_MSG_L2C_PeakFighterDetail":                                reflect.ValueOf(cl.ID_MSG_L2C_PeakFighterDetail),
		"ID_MSG_L2C_PeakGetLastBattleReport":                          reflect.ValueOf(cl.ID_MSG_L2C_PeakGetLastBattleReport),
		"ID_MSG_L2C_PeakGetMatch":                                     reflect.ValueOf(cl.ID_MSG_L2C_PeakGetMatch),
		"ID_MSG_L2C_PeakGuessList":                                    reflect.ValueOf(cl.ID_MSG_L2C_PeakGuessList),
		"ID_MSG_L2C_PeakRankList":                                     reflect.ValueOf(cl.ID_MSG_L2C_PeakRankList),
		"ID_MSG_L2C_PeakRecvInviteReward":                             reflect.ValueOf(cl.ID_MSG_L2C_PeakRecvInviteReward),
		"ID_MSG_L2C_PeakUpdateTip":                                    reflect.ValueOf(cl.ID_MSG_L2C_PeakUpdateTip),
		"ID_MSG_L2C_PeakWorship":                                      reflect.ValueOf(cl.ID_MSG_L2C_PeakWorship),
		"ID_MSG_L2C_PreSeasonGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_PreSeasonGetData),
		"ID_MSG_L2C_PreSeasonRecvAward":                               reflect.ValueOf(cl.ID_MSG_L2C_PreSeasonRecvAward),
		"ID_MSG_L2C_PushGiftGetData":                                  reflect.ValueOf(cl.ID_MSG_L2C_PushGiftGetData),
		"ID_MSG_L2C_PushGiftRechargeAward":                            reflect.ValueOf(cl.ID_MSG_L2C_PushGiftRechargeAward),
		"ID_MSG_L2C_PushGiftUpdate":                                   reflect.ValueOf(cl.ID_MSG_L2C_PushGiftUpdate),
		"ID_MSG_L2C_PyramidChooseAward":                               reflect.ValueOf(cl.ID_MSG_L2C_PyramidChooseAward),
		"ID_MSG_L2C_PyramidDraw":                                      reflect.ValueOf(cl.ID_MSG_L2C_PyramidDraw),
		"ID_MSG_L2C_PyramidGetData":                                   reflect.ValueOf(cl.ID_MSG_L2C_PyramidGetData),
		"ID_MSG_L2C_PyramidReceiveAwards":                             reflect.ValueOf(cl.ID_MSG_L2C_PyramidReceiveAwards),
		"ID_MSG_L2C_PyramidTestDraw":                                  reflect.ValueOf(cl.ID_MSG_L2C_PyramidTestDraw),
		"ID_MSG_L2C_PyramidUpdateActivity":                            reflect.ValueOf(cl.ID_MSG_L2C_PyramidUpdateActivity),
		"ID_MSG_L2C_PyramidUpdateTask":                                reflect.ValueOf(cl.ID_MSG_L2C_PyramidUpdateTask),
		"ID_MSG_L2C_QuestionnaireUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_QuestionnaireUpdate),
		"ID_MSG_L2C_RankAchieveList":                                  reflect.ValueOf(cl.ID_MSG_L2C_RankAchieveList),
		"ID_MSG_L2C_RankAchieveNotify":                                reflect.ValueOf(cl.ID_MSG_L2C_RankAchieveNotify),
		"ID_MSG_L2C_RankAchieveRecvAward":                             reflect.ValueOf(cl.ID_MSG_L2C_RankAchieveRecvAward),
		"ID_MSG_L2C_RateGetStatus":                                    reflect.ValueOf(cl.ID_MSG_L2C_RateGetStatus),
		"ID_MSG_L2C_RateScore":                                        reflect.ValueOf(cl.ID_MSG_L2C_RateScore),
		"ID_MSG_L2C_ReadMail":                                         reflect.ValueOf(cl.ID_MSG_L2C_ReadMail),
		"ID_MSG_L2C_RechargeByCoupon":                                 reflect.ValueOf(cl.ID_MSG_L2C_RechargeByCoupon),
		"ID_MSG_L2C_RechargeFirstGiftNotify":                          reflect.ValueOf(cl.ID_MSG_L2C_RechargeFirstGiftNotify),
		"ID_MSG_L2C_RechargeFirstGiftReward":                          reflect.ValueOf(cl.ID_MSG_L2C_RechargeFirstGiftReward),
		"ID_MSG_L2C_RechargeGetData":                                  reflect.ValueOf(cl.ID_MSG_L2C_RechargeGetData),
		"ID_MSG_L2C_RechargeNotify":                                   reflect.ValueOf(cl.ID_MSG_L2C_RechargeNotify),
		"ID_MSG_L2C_RechargeSimulation":                               reflect.ValueOf(cl.ID_MSG_L2C_RechargeSimulation),
		"ID_MSG_L2C_RecvH5DesktopReward":                              reflect.ValueOf(cl.ID_MSG_L2C_RecvH5DesktopReward),
		"ID_MSG_L2C_RecvShareAward":                                   reflect.ValueOf(cl.ID_MSG_L2C_RecvShareAward),
		"ID_MSG_L2C_RemainBookLevelUp":                                reflect.ValueOf(cl.ID_MSG_L2C_RemainBookLevelUp),
		"ID_MSG_L2C_RemainBookRecvExp":                                reflect.ValueOf(cl.ID_MSG_L2C_RemainBookRecvExp),
		"ID_MSG_L2C_RemainGetData":                                    reflect.ValueOf(cl.ID_MSG_L2C_RemainGetData),
		"ID_MSG_L2C_RemainUpdate":                                     reflect.ValueOf(cl.ID_MSG_L2C_RemainUpdate),
		"ID_MSG_L2C_RemainUseItemsNotify":                             reflect.ValueOf(cl.ID_MSG_L2C_RemainUseItemsNotify),
		"ID_MSG_L2C_RiteChanged":                                      reflect.ValueOf(cl.ID_MSG_L2C_RiteChanged),
		"ID_MSG_L2C_RiteGetData":                                      reflect.ValueOf(cl.ID_MSG_L2C_RiteGetData),
		"ID_MSG_L2C_RiteTakeRareAwards":                               reflect.ValueOf(cl.ID_MSG_L2C_RiteTakeRareAwards),
		"ID_MSG_L2C_RobotBattle":                                      reflect.ValueOf(cl.ID_MSG_L2C_RobotBattle),
		"ID_MSG_L2C_RoundActivityGetData":                             reflect.ValueOf(cl.ID_MSG_L2C_RoundActivityGetData),
		"ID_MSG_L2C_RoundActivityRecvTaskAward":                       reflect.ValueOf(cl.ID_MSG_L2C_RoundActivityRecvTaskAward),
		"ID_MSG_L2C_RoundActivityUpdateTask":                          reflect.ValueOf(cl.ID_MSG_L2C_RoundActivityUpdateTask),
		"ID_MSG_L2C_SeasonArenaBuyChallengeCount":                     reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaBuyChallengeCount),
		"ID_MSG_L2C_SeasonArenaDivisionAward":                         reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaDivisionAward),
		"ID_MSG_L2C_SeasonArenaFight":                                 reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaFight),
		"ID_MSG_L2C_SeasonArenaGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaGetData),
		"ID_MSG_L2C_SeasonArenaGetRankList":                           reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaGetRankList),
		"ID_MSG_L2C_SeasonArenaLogList":                               reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaLogList),
		"ID_MSG_L2C_SeasonArenaOfFame":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaOfFame),
		"ID_MSG_L2C_SeasonArenaRefresh":                               reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaRefresh),
		"ID_MSG_L2C_SeasonArenaState":                                 reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaState),
		"ID_MSG_L2C_SeasonArenaTaskAward":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaTaskAward),
		"ID_MSG_L2C_SeasonArenaTaskUpdate":                            reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaTaskUpdate),
		"ID_MSG_L2C_SeasonArenaUseTicket":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonArenaUseTicket),
		"ID_MSG_L2C_SeasonComplianceGetData":                          reflect.ValueOf(cl.ID_MSG_L2C_SeasonComplianceGetData),
		"ID_MSG_L2C_SeasonComplianceList":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonComplianceList),
		"ID_MSG_L2C_SeasonComplianceRecvReward":                       reflect.ValueOf(cl.ID_MSG_L2C_SeasonComplianceRecvReward),
		"ID_MSG_L2C_SeasonComplianceScoreChange":                      reflect.ValueOf(cl.ID_MSG_L2C_SeasonComplianceScoreChange),
		"ID_MSG_L2C_SeasonDoorFight":                                  reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorFight),
		"ID_MSG_L2C_SeasonDoorFightLine":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorFightLine),
		"ID_MSG_L2C_SeasonDoorFightLineReward":                        reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorFightLineReward),
		"ID_MSG_L2C_SeasonDoorFightLineViewReward":                    reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorFightLineViewReward),
		"ID_MSG_L2C_SeasonDoorGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorGetData),
		"ID_MSG_L2C_SeasonDoorLog":                                    reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorLog),
		"ID_MSG_L2C_SeasonDoorTaskReward":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorTaskReward),
		"ID_MSG_L2C_SeasonDoorTaskUpdate":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonDoorTaskUpdate),
		"ID_MSG_L2C_SeasonDungeonFight":                               reflect.ValueOf(cl.ID_MSG_L2C_SeasonDungeonFight),
		"ID_MSG_L2C_SeasonDungeonGetData":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonDungeonGetData),
		"ID_MSG_L2C_SeasonDungeonRecvReward":                          reflect.ValueOf(cl.ID_MSG_L2C_SeasonDungeonRecvReward),
		"ID_MSG_L2C_SeasonDungeonUpdateTask":                          reflect.ValueOf(cl.ID_MSG_L2C_SeasonDungeonUpdateTask),
		"ID_MSG_L2C_SeasonEnter":                                      reflect.ValueOf(cl.ID_MSG_L2C_SeasonEnter),
		"ID_MSG_L2C_SeasonJewelryDecompose":                           reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelryDecompose),
		"ID_MSG_L2C_SeasonJewelryGetData":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelryGetData),
		"ID_MSG_L2C_SeasonJewelryLock":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelryLock),
		"ID_MSG_L2C_SeasonJewelrySetAutoDecompose":                    reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelrySetAutoDecompose),
		"ID_MSG_L2C_SeasonJewelrySkillChange":                         reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelrySkillChange),
		"ID_MSG_L2C_SeasonJewelrySkillChangeConfirm":                  reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelrySkillChangeConfirm),
		"ID_MSG_L2C_SeasonJewelrySkillClassUp":                        reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelrySkillClassUp),
		"ID_MSG_L2C_SeasonJewelrySkillLevelUp":                        reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelrySkillLevelUp),
		"ID_MSG_L2C_SeasonJewelryTestReRollSkill":                     reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelryTestReRollSkill),
		"ID_MSG_L2C_SeasonJewelryUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelryUpdate),
		"ID_MSG_L2C_SeasonJewelryWear":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonJewelryWear),
		"ID_MSG_L2C_SeasonLevelGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_SeasonLevelGetData),
		"ID_MSG_L2C_SeasonLevelRecvLvAwards":                          reflect.ValueOf(cl.ID_MSG_L2C_SeasonLevelRecvLvAwards),
		"ID_MSG_L2C_SeasonLevelRecvTaskAwards":                        reflect.ValueOf(cl.ID_MSG_L2C_SeasonLevelRecvTaskAwards),
		"ID_MSG_L2C_SeasonLevelTaskUpdate":                            reflect.ValueOf(cl.ID_MSG_L2C_SeasonLevelTaskUpdate),
		"ID_MSG_L2C_SeasonLevelUp":                                    reflect.ValueOf(cl.ID_MSG_L2C_SeasonLevelUp),
		"ID_MSG_L2C_SeasonLinkActivate":                               reflect.ValueOf(cl.ID_MSG_L2C_SeasonLinkActivate),
		"ID_MSG_L2C_SeasonLinkGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonLinkGetData),
		"ID_MSG_L2C_SeasonLinkMonumentChange":                         reflect.ValueOf(cl.ID_MSG_L2C_SeasonLinkMonumentChange),
		"ID_MSG_L2C_SeasonLinkMonumentTakeRareAwards":                 reflect.ValueOf(cl.ID_MSG_L2C_SeasonLinkMonumentTakeRareAwards),
		"ID_MSG_L2C_SeasonMapAltar":                                   reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapAltar),
		"ID_MSG_L2C_SeasonMapBuyStamina":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapBuyStamina),
		"ID_MSG_L2C_SeasonMapDialogue":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapDialogue),
		"ID_MSG_L2C_SeasonMapFight":                                   reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapFight),
		"ID_MSG_L2C_SeasonMapGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapGetData),
		"ID_MSG_L2C_SeasonMapGetRankList":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapGetRankList),
		"ID_MSG_L2C_SeasonMapGoodsPrice":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapGoodsPrice),
		"ID_MSG_L2C_SeasonMapMaster":                                  reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapMaster),
		"ID_MSG_L2C_SeasonMapMovePosition":                            reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapMovePosition),
		"ID_MSG_L2C_SeasonMapPassPosition":                            reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapPassPosition),
		"ID_MSG_L2C_SeasonMapPositionLogs":                            reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapPositionLogs),
		"ID_MSG_L2C_SeasonMapPriceChangeLogs":                         reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapPriceChangeLogs),
		"ID_MSG_L2C_SeasonMapRecoverTimeUpdate":                       reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapRecoverTimeUpdate),
		"ID_MSG_L2C_SeasonMapSystemEvent":                             reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapSystemEvent),
		"ID_MSG_L2C_SeasonMapTaskReward":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapTaskReward),
		"ID_MSG_L2C_SeasonMapTaskUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapTaskUpdate),
		"ID_MSG_L2C_SeasonMapTrade":                                   reflect.ValueOf(cl.ID_MSG_L2C_SeasonMapTrade),
		"ID_MSG_L2C_SeasonReturnGetData":                              reflect.ValueOf(cl.ID_MSG_L2C_SeasonReturnGetData),
		"ID_MSG_L2C_SeasonReturnTakeAwards":                           reflect.ValueOf(cl.ID_MSG_L2C_SeasonReturnTakeAwards),
		"ID_MSG_L2C_SeasonShopBuy":                                    reflect.ValueOf(cl.ID_MSG_L2C_SeasonShopBuy),
		"ID_MSG_L2C_SeasonShopGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_SeasonShopGetData),
		"ID_MSG_L2C_SeasonStartTowerRankLike":                         reflect.ValueOf(cl.ID_MSG_L2C_SeasonStartTowerRankLike),
		"ID_MSG_L2C_SeasonStartTowerRankList":                         reflect.ValueOf(cl.ID_MSG_L2C_SeasonStartTowerRankList),
		"ID_MSG_L2C_SelectSummonGetOpenActivity":                      reflect.ValueOf(cl.ID_MSG_L2C_SelectSummonGetOpenActivity),
		"ID_MSG_L2C_SelectSummonSummon":                               reflect.ValueOf(cl.ID_MSG_L2C_SelectSummonSummon),
		"ID_MSG_L2C_SelectSummonTestSummon":                           reflect.ValueOf(cl.ID_MSG_L2C_SelectSummonTestSummon),
		"ID_MSG_L2C_SelectSummonUpdate":                               reflect.ValueOf(cl.ID_MSG_L2C_SelectSummonUpdate),
		"ID_MSG_L2C_SellItem":                                         reflect.ValueOf(cl.ID_MSG_L2C_SellItem),
		"ID_MSG_L2C_SetClientInfo":                                    reflect.ValueOf(cl.ID_MSG_L2C_SetClientInfo),
		"ID_MSG_L2C_SetName":                                          reflect.ValueOf(cl.ID_MSG_L2C_SetName),
		"ID_MSG_L2C_SetPush":                                          reflect.ValueOf(cl.ID_MSG_L2C_SetPush),
		"ID_MSG_L2C_SetServerTime":                                    reflect.ValueOf(cl.ID_MSG_L2C_SetServerTime),
		"ID_MSG_L2C_SevenDayLoginData":                                reflect.ValueOf(cl.ID_MSG_L2C_SevenDayLoginData),
		"ID_MSG_L2C_SevenDayLoginTakeAward":                           reflect.ValueOf(cl.ID_MSG_L2C_SevenDayLoginTakeAward),
		"ID_MSG_L2C_ShopBuy":                                          reflect.ValueOf(cl.ID_MSG_L2C_ShopBuy),
		"ID_MSG_L2C_ShopList":                                         reflect.ValueOf(cl.ID_MSG_L2C_ShopList),
		"ID_MSG_L2C_ShopRefresh":                                      reflect.ValueOf(cl.ID_MSG_L2C_ShopRefresh),
		"ID_MSG_L2C_ShopReset":                                        reflect.ValueOf(cl.ID_MSG_L2C_ShopReset),
		"ID_MSG_L2C_SkinList":                                         reflect.ValueOf(cl.ID_MSG_L2C_SkinList),
		"ID_MSG_L2C_SkinNew":                                          reflect.ValueOf(cl.ID_MSG_L2C_SkinNew),
		"ID_MSG_L2C_SkinUse":                                          reflect.ValueOf(cl.ID_MSG_L2C_SkinUse),
		"ID_MSG_L2C_StoryReviewGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_StoryReviewGetData),
		"ID_MSG_L2C_StoryReviewUnlock":                                reflect.ValueOf(cl.ID_MSG_L2C_StoryReviewUnlock),
		"ID_MSG_L2C_Summon":                                           reflect.ValueOf(cl.ID_MSG_L2C_Summon),
		"ID_MSG_L2C_SummonArtifactPointsExchange":                     reflect.ValueOf(cl.ID_MSG_L2C_SummonArtifactPointsExchange),
		"ID_MSG_L2C_SummonGetData":                                    reflect.ValueOf(cl.ID_MSG_L2C_SummonGetData),
		"ID_MSG_L2C_SummonSetHeroAutoDecompose":                       reflect.ValueOf(cl.ID_MSG_L2C_SummonSetHeroAutoDecompose),
		"ID_MSG_L2C_SummonSetWishList":                                reflect.ValueOf(cl.ID_MSG_L2C_SummonSetWishList),
		"ID_MSG_L2C_SummonSimulation":                                 reflect.ValueOf(cl.ID_MSG_L2C_SummonSimulation),
		"ID_MSG_L2C_SyncQuestionnaire":                                reflect.ValueOf(cl.ID_MSG_L2C_SyncQuestionnaire),
		"ID_MSG_L2C_TalentTreeCultivateUpdate":                        reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeCultivateUpdate),
		"ID_MSG_L2C_TalentTreeGetData":                                reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeGetData),
		"ID_MSG_L2C_TalentTreeHot":                                    reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeHot),
		"ID_MSG_L2C_TalentTreeLevelUp":                                reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeLevelUp),
		"ID_MSG_L2C_TalentTreePlanDelete":                             reflect.ValueOf(cl.ID_MSG_L2C_TalentTreePlanDelete),
		"ID_MSG_L2C_TalentTreePlanSave":                               reflect.ValueOf(cl.ID_MSG_L2C_TalentTreePlanSave),
		"ID_MSG_L2C_TalentTreeReceiveTaskAwards":                      reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeReceiveTaskAwards),
		"ID_MSG_L2C_TalentTreeReset":                                  reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeReset),
		"ID_MSG_L2C_TalentTreeTaskUpdate":                             reflect.ValueOf(cl.ID_MSG_L2C_TalentTreeTaskUpdate),
		"ID_MSG_L2C_TalesChapterFight":                                reflect.ValueOf(cl.ID_MSG_L2C_TalesChapterFight),
		"ID_MSG_L2C_TalesChapterFinish":                               reflect.ValueOf(cl.ID_MSG_L2C_TalesChapterFinish),
		"ID_MSG_L2C_TalesChapterTakeReward":                           reflect.ValueOf(cl.ID_MSG_L2C_TalesChapterTakeReward),
		"ID_MSG_L2C_TalesEliteFight":                                  reflect.ValueOf(cl.ID_MSG_L2C_TalesEliteFight),
		"ID_MSG_L2C_TalesEliteWipe":                                   reflect.ValueOf(cl.ID_MSG_L2C_TalesEliteWipe),
		"ID_MSG_L2C_TalesList":                                        reflect.ValueOf(cl.ID_MSG_L2C_TalesList),
		"ID_MSG_L2C_TaskGetInfo":                                      reflect.ValueOf(cl.ID_MSG_L2C_TaskGetInfo),
		"ID_MSG_L2C_TaskReceiveAward":                                 reflect.ValueOf(cl.ID_MSG_L2C_TaskReceiveAward),
		"ID_MSG_L2C_TaskUpdate":                                       reflect.ValueOf(cl.ID_MSG_L2C_TaskUpdate),
		"ID_MSG_L2C_Test":                                             reflect.ValueOf(cl.ID_MSG_L2C_Test),
		"ID_MSG_L2C_TestBattleData":                                   reflect.ValueOf(cl.ID_MSG_L2C_TestBattleData),
		"ID_MSG_L2C_TestDrop":                                         reflect.ValueOf(cl.ID_MSG_L2C_TestDrop),
		"ID_MSG_L2C_TestEtcdGiftCode":                                 reflect.ValueOf(cl.ID_MSG_L2C_TestEtcdGiftCode),
		"ID_MSG_L2C_TitleList":                                        reflect.ValueOf(cl.ID_MSG_L2C_TitleList),
		"ID_MSG_L2C_TitleNew":                                         reflect.ValueOf(cl.ID_MSG_L2C_TitleNew),
		"ID_MSG_L2C_TitleUse":                                         reflect.ValueOf(cl.ID_MSG_L2C_TitleUse),
		"ID_MSG_L2C_TowerFight":                                       reflect.ValueOf(cl.ID_MSG_L2C_TowerFight),
		"ID_MSG_L2C_TowerJump":                                        reflect.ValueOf(cl.ID_MSG_L2C_TowerJump),
		"ID_MSG_L2C_TowerList":                                        reflect.ValueOf(cl.ID_MSG_L2C_TowerList),
		"ID_MSG_L2C_TowerSeasonCognitionLogs":                         reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonCognitionLogs),
		"ID_MSG_L2C_TowerSeasonFight":                                 reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonFight),
		"ID_MSG_L2C_TowerSeasonGetData":                               reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonGetData),
		"ID_MSG_L2C_TowerSeasonRankLike":                              reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonRankLike),
		"ID_MSG_L2C_TowerSeasonRankList":                              reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonRankList),
		"ID_MSG_L2C_TowerSeasonRecvAward":                             reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonRecvAward),
		"ID_MSG_L2C_TowerSeasonUpdateTask":                            reflect.ValueOf(cl.ID_MSG_L2C_TowerSeasonUpdateTask),
		"ID_MSG_L2C_TowerSweep":                                       reflect.ValueOf(cl.ID_MSG_L2C_TowerSweep),
		"ID_MSG_L2C_TowerstarDailyRecvAward":                          reflect.ValueOf(cl.ID_MSG_L2C_TowerstarDailyRecvAward),
		"ID_MSG_L2C_TowerstarFight":                                   reflect.ValueOf(cl.ID_MSG_L2C_TowerstarFight),
		"ID_MSG_L2C_TowerstarGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_TowerstarGetData),
		"ID_MSG_L2C_TowerstarStarRecvAward":                           reflect.ValueOf(cl.ID_MSG_L2C_TowerstarStarRecvAward),
		"ID_MSG_L2C_TrialAward":                                       reflect.ValueOf(cl.ID_MSG_L2C_TrialAward),
		"ID_MSG_L2C_TrialFight":                                       reflect.ValueOf(cl.ID_MSG_L2C_TrialFight),
		"ID_MSG_L2C_TrialGetInfo":                                     reflect.ValueOf(cl.ID_MSG_L2C_TrialGetInfo),
		"ID_MSG_L2C_TrialPreview":                                     reflect.ValueOf(cl.ID_MSG_L2C_TrialPreview),
		"ID_MSG_L2C_TrialSpeed":                                       reflect.ValueOf(cl.ID_MSG_L2C_TrialSpeed),
		"ID_MSG_L2C_UpdateGems":                                       reflect.ValueOf(cl.ID_MSG_L2C_UpdateGems),
		"ID_MSG_L2C_UpdateTop5":                                       reflect.ValueOf(cl.ID_MSG_L2C_UpdateTop5),
		"ID_MSG_L2C_UseItem":                                          reflect.ValueOf(cl.ID_MSG_L2C_UseItem),
		"ID_MSG_L2C_UserGuildChestItemNotify":                         reflect.ValueOf(cl.ID_MSG_L2C_UserGuildChestItemNotify),
		"ID_MSG_L2C_ViewFormation":                                    reflect.ValueOf(cl.ID_MSG_L2C_ViewFormation),
		"ID_MSG_L2C_ViewUser":                                         reflect.ValueOf(cl.ID_MSG_L2C_ViewUser),
		"ID_MSG_L2C_VipBuyGift":                                       reflect.ValueOf(cl.ID_MSG_L2C_VipBuyGift),
		"ID_MSG_L2C_VipGiftRechargeNotify":                            reflect.ValueOf(cl.ID_MSG_L2C_VipGiftRechargeNotify),
		"ID_MSG_L2C_VipInfoGet":                                       reflect.ValueOf(cl.ID_MSG_L2C_VipInfoGet),
		"ID_MSG_L2C_WebRechargeNotify":                                reflect.ValueOf(cl.ID_MSG_L2C_WebRechargeNotify),
		"ID_MSG_L2C_WorldBossFight":                                   reflect.ValueOf(cl.ID_MSG_L2C_WorldBossFight),
		"ID_MSG_L2C_WorldBossGetData":                                 reflect.ValueOf(cl.ID_MSG_L2C_WorldBossGetData),
		"ID_MSG_L2C_WorldBossGetRoomLog":                              reflect.ValueOf(cl.ID_MSG_L2C_WorldBossGetRoomLog),
		"ID_MSG_L2C_WorldBossRank":                                    reflect.ValueOf(cl.ID_MSG_L2C_WorldBossRank),
		"ID_MSG_L2C_WorldBossRecvAward":                               reflect.ValueOf(cl.ID_MSG_L2C_WorldBossRecvAward),
		"ID_MSG_L2C_WorldBossRoomInfo":                                reflect.ValueOf(cl.ID_MSG_L2C_WorldBossRoomInfo),
		"ID_MSG_L2C_WorldBossSelectLevel":                             reflect.ValueOf(cl.ID_MSG_L2C_WorldBossSelectLevel),
		"ID_MSG_L2C_WorldBossTaskUpdate":                              reflect.ValueOf(cl.ID_MSG_L2C_WorldBossTaskUpdate),
		"ID_MSG_L2C_WorldBossWorship":                                 reflect.ValueOf(cl.ID_MSG_L2C_WorldBossWorship),
		"ID_MSG_L2C_WrestleChangeRoom":                                reflect.ValueOf(cl.ID_MSG_L2C_WrestleChangeRoom),
		"ID_MSG_L2C_WrestleFight":                                     reflect.ValueOf(cl.ID_MSG_L2C_WrestleFight),
		"ID_MSG_L2C_WrestleFightLog":                                  reflect.ValueOf(cl.ID_MSG_L2C_WrestleFightLog),
		"ID_MSG_L2C_WrestleHallOfFame":                                reflect.ValueOf(cl.ID_MSG_L2C_WrestleHallOfFame),
		"ID_MSG_L2C_WrestleInfo":                                      reflect.ValueOf(cl.ID_MSG_L2C_WrestleInfo),
		"ID_MSG_L2C_WrestleLike":                                      reflect.ValueOf(cl.ID_MSG_L2C_WrestleLike),
		"ID_MSG_L2C_WrestleMapInfo":                                   reflect.ValueOf(cl.ID_MSG_L2C_WrestleMapInfo),
		"ID_MSG_L2C_WrestleRankList":                                  reflect.ValueOf(cl.ID_MSG_L2C_WrestleRankList),
		"ID_MSG_L2C_WrestleRecvTaskAward":                             reflect.ValueOf(cl.ID_MSG_L2C_WrestleRecvTaskAward),
		"ID_MSG_L2C_WrestleRoomInfo":                                  reflect.ValueOf(cl.ID_MSG_L2C_WrestleRoomInfo),
		"ID_MSG_L2C_WrestleRoomUpdate":                                reflect.ValueOf(cl.ID_MSG_L2C_WrestleRoomUpdate),
		"ID_MSG_L2C_WrestleTopUserList":                               reflect.ValueOf(cl.ID_MSG_L2C_WrestleTopUserList),
		"ID_MSG_MAX":                                                  reflect.ValueOf(cl.ID_MSG_MAX),
		"ID_MSG_MAX_GuildSandTable":                                   reflect.ValueOf(cl.ID_MSG_MAX_GuildSandTable),
		"ID_MSG_MIN":                                                  reflect.ValueOf(cl.ID_MSG_MIN),
		"ID_MSG_MIN_ActivityCompliance":                               reflect.ValueOf(cl.ID_MSG_MIN_ActivityCompliance),
		"ID_MSG_MIN_ActivityCoupon":                                   reflect.ValueOf(cl.ID_MSG_MIN_ActivityCoupon),
		"ID_MSG_MIN_ActivityLifelongGift":                             reflect.ValueOf(cl.ID_MSG_MIN_ActivityLifelongGift),
		"ID_MSG_MIN_ActivityMix":                                      reflect.ValueOf(cl.ID_MSG_MIN_ActivityMix),
		"ID_MSG_MIN_ActivityRecharge":                                 reflect.ValueOf(cl.ID_MSG_MIN_ActivityRecharge),
		"ID_MSG_MIN_ActivityReturn":                                   reflect.ValueOf(cl.ID_MSG_MIN_ActivityReturn),
		"ID_MSG_MIN_ActivitySchedule":                                 reflect.ValueOf(cl.ID_MSG_MIN_ActivitySchedule),
		"ID_MSG_MIN_ActivityStory":                                    reflect.ValueOf(cl.ID_MSG_MIN_ActivityStory),
		"ID_MSG_MIN_ActivitySum":                                      reflect.ValueOf(cl.ID_MSG_MIN_ActivitySum),
		"ID_MSG_MIN_ActivityTurnTable":                                reflect.ValueOf(cl.ID_MSG_MIN_ActivityTurnTable),
		"ID_MSG_MIN_Announcement":                                     reflect.ValueOf(cl.ID_MSG_MIN_Announcement),
		"ID_MSG_MIN_Arena":                                            reflect.ValueOf(cl.ID_MSG_MIN_Arena),
		"ID_MSG_MIN_Artifact":                                         reflect.ValueOf(cl.ID_MSG_MIN_Artifact),
		"ID_MSG_MIN_ArtifactDebut":                                    reflect.ValueOf(cl.ID_MSG_MIN_ArtifactDebut),
		"ID_MSG_MIN_AssistanceActivity":                               reflect.ValueOf(cl.ID_MSG_MIN_AssistanceActivity),
		"ID_MSG_MIN_Assistant":                                        reflect.ValueOf(cl.ID_MSG_MIN_Assistant),
		"ID_MSG_MIN_Avatar":                                           reflect.ValueOf(cl.ID_MSG_MIN_Avatar),
		"ID_MSG_MIN_Base":                                             reflect.ValueOf(cl.ID_MSG_MIN_Base),
		"ID_MSG_MIN_BaseAdd":                                          reflect.ValueOf(cl.ID_MSG_MIN_BaseAdd),
		"ID_MSG_MIN_BossRush":                                         reflect.ValueOf(cl.ID_MSG_MIN_BossRush),
		"ID_MSG_MIN_Box":                                              reflect.ValueOf(cl.ID_MSG_MIN_Box),
		"ID_MSG_MIN_Carnival":                                         reflect.ValueOf(cl.ID_MSG_MIN_Carnival),
		"ID_MSG_MIN_Chat":                                             reflect.ValueOf(cl.ID_MSG_MIN_Chat),
		"ID_MSG_MIN_ComplianceTasks":                                  reflect.ValueOf(cl.ID_MSG_MIN_ComplianceTasks),
		"ID_MSG_MIN_Crystal":                                          reflect.ValueOf(cl.ID_MSG_MIN_Crystal),
		"ID_MSG_MIN_DailyAttendance":                                  reflect.ValueOf(cl.ID_MSG_MIN_DailyAttendance),
		"ID_MSG_MIN_DailyAttendanceHero":                              reflect.ValueOf(cl.ID_MSG_MIN_DailyAttendanceHero),
		"ID_MSG_MIN_DailySpecial":                                     reflect.ValueOf(cl.ID_MSG_MIN_DailySpecial),
		"ID_MSG_MIN_DailyWish":                                        reflect.ValueOf(cl.ID_MSG_MIN_DailyWish),
		"ID_MSG_MIN_Disorderland":                                     reflect.ValueOf(cl.ID_MSG_MIN_Disorderland),
		"ID_MSG_MIN_Dispatch":                                         reflect.ValueOf(cl.ID_MSG_MIN_Dispatch),
		"ID_MSG_MIN_DivineDemon":                                      reflect.ValueOf(cl.ID_MSG_MIN_DivineDemon),
		"ID_MSG_MIN_DropActivity":                                     reflect.ValueOf(cl.ID_MSG_MIN_DropActivity),
		"ID_MSG_MIN_Duel":                                             reflect.ValueOf(cl.ID_MSG_MIN_Duel),
		"ID_MSG_MIN_Dungeon":                                          reflect.ValueOf(cl.ID_MSG_MIN_Dungeon),
		"ID_MSG_MIN_Emblem":                                           reflect.ValueOf(cl.ID_MSG_MIN_Emblem),
		"ID_MSG_MIN_Equip":                                            reflect.ValueOf(cl.ID_MSG_MIN_Equip),
		"ID_MSG_MIN_Flower":                                           reflect.ValueOf(cl.ID_MSG_MIN_Flower),
		"ID_MSG_MIN_Forecast":                                         reflect.ValueOf(cl.ID_MSG_MIN_Forecast),
		"ID_MSG_MIN_Forest":                                           reflect.ValueOf(cl.ID_MSG_MIN_Forest),
		"ID_MSG_MIN_Fragment":                                         reflect.ValueOf(cl.ID_MSG_MIN_Fragment),
		"ID_MSG_MIN_Friend":                                           reflect.ValueOf(cl.ID_MSG_MIN_Friend),
		"ID_MSG_MIN_Function":                                         reflect.ValueOf(cl.ID_MSG_MIN_Function),
		"ID_MSG_MIN_Gem":                                              reflect.ValueOf(cl.ID_MSG_MIN_Gem),
		"ID_MSG_MIN_GodPresent":                                       reflect.ValueOf(cl.ID_MSG_MIN_GodPresent),
		"ID_MSG_MIN_GoddessContract":                                  reflect.ValueOf(cl.ID_MSG_MIN_GoddessContract),
		"ID_MSG_MIN_GoldBuy":                                          reflect.ValueOf(cl.ID_MSG_MIN_GoldBuy),
		"ID_MSG_MIN_Guidance":                                         reflect.ValueOf(cl.ID_MSG_MIN_Guidance),
		"ID_MSG_MIN_Guild":                                            reflect.ValueOf(cl.ID_MSG_MIN_Guild),
		"ID_MSG_MIN_GuildChest":                                       reflect.ValueOf(cl.ID_MSG_MIN_GuildChest),
		"ID_MSG_MIN_GuildDungeon":                                     reflect.ValueOf(cl.ID_MSG_MIN_GuildDungeon),
		"ID_MSG_MIN_GuildMobilization":                                reflect.ValueOf(cl.ID_MSG_MIN_GuildMobilization),
		"ID_MSG_MIN_GuildSandTable":                                   reflect.ValueOf(cl.ID_MSG_MIN_GuildSandTable),
		"ID_MSG_MIN_GuildSandTableChallenge":                          reflect.ValueOf(cl.ID_MSG_MIN_GuildSandTableChallenge),
		"ID_MSG_MIN_GuildSandTableDragon":                             reflect.ValueOf(cl.ID_MSG_MIN_GuildSandTableDragon),
		"ID_MSG_MIN_GuildSandTableOre":                                reflect.ValueOf(cl.ID_MSG_MIN_GuildSandTableOre),
		"ID_MSG_MIN_GuildSandTableTech":                               reflect.ValueOf(cl.ID_MSG_MIN_GuildSandTableTech),
		"ID_MSG_MIN_GuildTalent":                                      reflect.ValueOf(cl.ID_MSG_MIN_GuildTalent),
		"ID_MSG_MIN_Handbooks":                                        reflect.ValueOf(cl.ID_MSG_MIN_Handbooks),
		"ID_MSG_MIN_Hero":                                             reflect.ValueOf(cl.ID_MSG_MIN_Hero),
		"ID_MSG_MIN_HotRank":                                          reflect.ValueOf(cl.ID_MSG_MIN_HotRank),
		"ID_MSG_MIN_Item":                                             reflect.ValueOf(cl.ID_MSG_MIN_Item),
		"ID_MSG_MIN_Link":                                             reflect.ValueOf(cl.ID_MSG_MIN_Link),
		"ID_MSG_MIN_LinkHeroSummon":                                   reflect.ValueOf(cl.ID_MSG_MIN_LinkHeroSummon),
		"ID_MSG_MIN_Mail":                                             reflect.ValueOf(cl.ID_MSG_MIN_Mail),
		"ID_MSG_MIN_Maze":                                             reflect.ValueOf(cl.ID_MSG_MIN_Maze),
		"ID_MSG_MIN_Medal":                                            reflect.ValueOf(cl.ID_MSG_MIN_Medal),
		"ID_MSG_MIN_Memory":                                           reflect.ValueOf(cl.ID_MSG_MIN_Memory),
		"ID_MSG_MIN_Mirage":                                           reflect.ValueOf(cl.ID_MSG_MIN_Mirage),
		"ID_MSG_MIN_MonthTasks":                                       reflect.ValueOf(cl.ID_MSG_MIN_MonthTasks),
		"ID_MSG_MIN_MonthlyCard":                                      reflect.ValueOf(cl.ID_MSG_MIN_MonthlyCard),
		"ID_MSG_MIN_NewYearActivity":                                  reflect.ValueOf(cl.ID_MSG_MIN_NewYearActivity),
		"ID_MSG_MIN_OperateActivity":                                  reflect.ValueOf(cl.ID_MSG_MIN_OperateActivity),
		"ID_MSG_MIN_Pass":                                             reflect.ValueOf(cl.ID_MSG_MIN_Pass),
		"ID_MSG_MIN_Peak":                                             reflect.ValueOf(cl.ID_MSG_MIN_Peak),
		"ID_MSG_MIN_PreSeason":                                        reflect.ValueOf(cl.ID_MSG_MIN_PreSeason),
		"ID_MSG_MIN_Push":                                             reflect.ValueOf(cl.ID_MSG_MIN_Push),
		"ID_MSG_MIN_PushGift":                                         reflect.ValueOf(cl.ID_MSG_MIN_PushGift),
		"ID_MSG_MIN_Pyramid":                                          reflect.ValueOf(cl.ID_MSG_MIN_Pyramid),
		"ID_MSG_MIN_RankAchieve":                                      reflect.ValueOf(cl.ID_MSG_MIN_RankAchieve),
		"ID_MSG_MIN_Rate":                                             reflect.ValueOf(cl.ID_MSG_MIN_Rate),
		"ID_MSG_MIN_Recharge":                                         reflect.ValueOf(cl.ID_MSG_MIN_Recharge),
		"ID_MSG_MIN_Remain":                                           reflect.ValueOf(cl.ID_MSG_MIN_Remain),
		"ID_MSG_MIN_Rite":                                             reflect.ValueOf(cl.ID_MSG_MIN_Rite),
		"ID_MSG_MIN_RoundActivity":                                    reflect.ValueOf(cl.ID_MSG_MIN_RoundActivity),
		"ID_MSG_MIN_SeasonArena":                                      reflect.ValueOf(cl.ID_MSG_MIN_SeasonArena),
		"ID_MSG_MIN_SeasonCompliance":                                 reflect.ValueOf(cl.ID_MSG_MIN_SeasonCompliance),
		"ID_MSG_MIN_SeasonDoor":                                       reflect.ValueOf(cl.ID_MSG_MIN_SeasonDoor),
		"ID_MSG_MIN_SeasonDungeon":                                    reflect.ValueOf(cl.ID_MSG_MIN_SeasonDungeon),
		"ID_MSG_MIN_SeasonJewelry":                                    reflect.ValueOf(cl.ID_MSG_MIN_SeasonJewelry),
		"ID_MSG_MIN_SeasonLevel":                                      reflect.ValueOf(cl.ID_MSG_MIN_SeasonLevel),
		"ID_MSG_MIN_SeasonLink":                                       reflect.ValueOf(cl.ID_MSG_MIN_SeasonLink),
		"ID_MSG_MIN_SeasonMap":                                        reflect.ValueOf(cl.ID_MSG_MIN_SeasonMap),
		"ID_MSG_MIN_SeasonReturn":                                     reflect.ValueOf(cl.ID_MSG_MIN_SeasonReturn),
		"ID_MSG_MIN_SeasonShop":                                       reflect.ValueOf(cl.ID_MSG_MIN_SeasonShop),
		"ID_MSG_MIN_SeasonStart":                                      reflect.ValueOf(cl.ID_MSG_MIN_SeasonStart),
		"ID_MSG_MIN_SelectSummon":                                     reflect.ValueOf(cl.ID_MSG_MIN_SelectSummon),
		"ID_MSG_MIN_SevenDayLogin":                                    reflect.ValueOf(cl.ID_MSG_MIN_SevenDayLogin),
		"ID_MSG_MIN_Shop":                                             reflect.ValueOf(cl.ID_MSG_MIN_Shop),
		"ID_MSG_MIN_Skin":                                             reflect.ValueOf(cl.ID_MSG_MIN_Skin),
		"ID_MSG_MIN_StoryReview":                                      reflect.ValueOf(cl.ID_MSG_MIN_StoryReview),
		"ID_MSG_MIN_Summon":                                           reflect.ValueOf(cl.ID_MSG_MIN_Summon),
		"ID_MSG_MIN_TalentTree":                                       reflect.ValueOf(cl.ID_MSG_MIN_TalentTree),
		"ID_MSG_MIN_Tales":                                            reflect.ValueOf(cl.ID_MSG_MIN_Tales),
		"ID_MSG_MIN_Task":                                             reflect.ValueOf(cl.ID_MSG_MIN_Task),
		"ID_MSG_MIN_Title":                                            reflect.ValueOf(cl.ID_MSG_MIN_Title),
		"ID_MSG_MIN_Tower":                                            reflect.ValueOf(cl.ID_MSG_MIN_Tower),
		"ID_MSG_MIN_TowerSeason":                                      reflect.ValueOf(cl.ID_MSG_MIN_TowerSeason),
		"ID_MSG_MIN_Towerstar":                                        reflect.ValueOf(cl.ID_MSG_MIN_Towerstar),
		"ID_MSG_MIN_Trial":                                            reflect.ValueOf(cl.ID_MSG_MIN_Trial),
		"ID_MSG_MIN_Vip":                                              reflect.ValueOf(cl.ID_MSG_MIN_Vip),
		"ID_MSG_MIN_WorldBoss":                                        reflect.ValueOf(cl.ID_MSG_MIN_WorldBoss),
		"ID_MSG_MIN_Wrestle":                                          reflect.ValueOf(cl.ID_MSG_MIN_Wrestle),
		"ID_MSG_NONE":                                                 reflect.ValueOf(cl.ID_MSG_NONE),
		"ID_name":                                                     reflect.ValueOf(&cl.ID_name).Elem(),
		"ID_value":                                                    reflect.ValueOf(&cl.ID_value).Elem(),
		"InitAchieveInfo":                                             reflect.ValueOf(cl.InitAchieveInfo),
		"InitAchieveTask":                                             reflect.ValueOf(cl.InitAchieveTask),
		"InitAchievementsShowcase":                                    reflect.ValueOf(cl.InitAchievementsShowcase),
		"InitActivityCompliance":                                      reflect.ValueOf(cl.InitActivityCompliance),
		"InitActivityCoupon":                                          reflect.ValueOf(cl.InitActivityCoupon),
		"InitActivityCouponRes":                                       reflect.ValueOf(cl.InitActivityCouponRes),
		"InitActivityCouponXml":                                       reflect.ValueOf(cl.InitActivityCouponXml),
		"InitActivityDrop":                                            reflect.ValueOf(cl.InitActivityDrop),
		"InitActivityExchange":                                        reflect.ValueOf(cl.InitActivityExchange),
		"InitActivityFeed":                                            reflect.ValueOf(cl.InitActivityFeed),
		"InitActivityLifelongGift":                                    reflect.ValueOf(cl.InitActivityLifelongGift),
		"InitActivityLifelongGifts":                                   reflect.ValueOf(cl.InitActivityLifelongGifts),
		"InitActivityMix":                                             reflect.ValueOf(cl.InitActivityMix),
		"InitActivityPuzzle":                                          reflect.ValueOf(cl.InitActivityPuzzle),
		"InitActivityRecharge":                                        reflect.ValueOf(cl.InitActivityRecharge),
		"InitActivityRechargeShop":                                    reflect.ValueOf(cl.InitActivityRechargeShop),
		"InitActivityReturn":                                          reflect.ValueOf(cl.InitActivityReturn),
		"InitActivityReturnLogin":                                     reflect.ValueOf(cl.InitActivityReturnLogin),
		"InitActivityScheduleData":                                    reflect.ValueOf(cl.InitActivityScheduleData),
		"InitActivityScheduleDatas":                                   reflect.ValueOf(cl.InitActivityScheduleDatas),
		"InitActivityShoot":                                           reflect.ValueOf(cl.InitActivityShoot),
		"InitActivityStory":                                           reflect.ValueOf(cl.InitActivityStory),
		"InitActivityStoryExchange":                                   reflect.ValueOf(cl.InitActivityStoryExchange),
		"InitActivitySubContinueLogin":                                reflect.ValueOf(cl.InitActivitySubContinueLogin),
		"InitActivitySubExchange":                                     reflect.ValueOf(cl.InitActivitySubExchange),
		"InitActivitySubFeed":                                         reflect.ValueOf(cl.InitActivitySubFeed),
		"InitActivitySubPuzzle":                                       reflect.ValueOf(cl.InitActivitySubPuzzle),
		"InitActivitySubShoot":                                        reflect.ValueOf(cl.InitActivitySubShoot),
		"InitActivitySubTask":                                         reflect.ValueOf(cl.InitActivitySubTask),
		"InitActivitySubTurnTable":                                    reflect.ValueOf(cl.InitActivitySubTurnTable),
		"InitActivitySum":                                             reflect.ValueOf(cl.InitActivitySum),
		"InitActivitySumClient":                                       reflect.ValueOf(cl.InitActivitySumClient),
		"InitActivitySummonGuarantee":                                 reflect.ValueOf(cl.InitActivitySummonGuarantee),
		"InitActivityTurnTable":                                       reflect.ValueOf(cl.InitActivityTurnTable),
		"InitActivityWebData":                                         reflect.ValueOf(cl.InitActivityWebData),
		"InitActivityWebDatas":                                        reflect.ValueOf(cl.InitActivityWebDatas),
		"InitAltPassives":                                             reflect.ValueOf(cl.InitAltPassives),
		"InitAnnouncement":                                            reflect.ValueOf(cl.InitAnnouncement),
		"InitArena":                                                   reflect.ValueOf(cl.InitArena),
		"InitArenaLastSeasonTop":                                      reflect.ValueOf(cl.InitArenaLastSeasonTop),
		"InitArenaLog":                                                reflect.ValueOf(cl.InitArenaLog),
		"InitArenaLogDeletes":                                         reflect.ValueOf(cl.InitArenaLogDeletes),
		"InitArenaLogs":                                               reflect.ValueOf(cl.InitArenaLogs),
		"InitArenaRankInfo":                                           reflect.ValueOf(cl.InitArenaRankInfo),
		"InitArenaTask":                                               reflect.ValueOf(cl.InitArenaTask),
		"InitArenaUserInfo":                                           reflect.ValueOf(cl.InitArenaUserInfo),
		"InitArenaUserScoreAndRank":                                   reflect.ValueOf(cl.InitArenaUserScoreAndRank),
		"InitArtifact":                                                reflect.ValueOf(cl.InitArtifact),
		"InitArtifactDebut":                                           reflect.ValueOf(cl.InitArtifactDebut),
		"InitArtifactDebutBase":                                       reflect.ValueOf(cl.InitArtifactDebutBase),
		"InitArtifactDebutGuarantee":                                  reflect.ValueOf(cl.InitArtifactDebutGuarantee),
		"InitArtifactDebutLoginAct":                                   reflect.ValueOf(cl.InitArtifactDebutLoginAct),
		"InitArtifactDebutPuzzleAct":                                  reflect.ValueOf(cl.InitArtifactDebutPuzzleAct),
		"InitArtifactDebutSummon":                                     reflect.ValueOf(cl.InitArtifactDebutSummon),
		"InitArtifactDebutSummonAct":                                  reflect.ValueOf(cl.InitArtifactDebutSummonAct),
		"InitArtifactDebutTask":                                       reflect.ValueOf(cl.InitArtifactDebutTask),
		"InitAssistanceActivity":                                      reflect.ValueOf(cl.InitAssistanceActivity),
		"InitAssistantData":                                           reflect.ValueOf(cl.InitAssistantData),
		"InitAttr":                                                    reflect.ValueOf(cl.InitAttr),
		"InitAttrInfo":                                                reflect.ValueOf(cl.InitAttrInfo),
		"InitAvatar":                                                  reflect.ValueOf(cl.InitAvatar),
		"InitBanInfo":                                                 reflect.ValueOf(cl.InitBanInfo),
		"InitBanUsers":                                                reflect.ValueOf(cl.InitBanUsers),
		"InitBaseGrid":                                                reflect.ValueOf(cl.InitBaseGrid),
		"InitBattleBots":                                              reflect.ValueOf(cl.InitBattleBots),
		"InitBossRush":                                                reflect.ValueOf(cl.InitBossRush),
		"InitBossRushBoss":                                            reflect.ValueOf(cl.InitBossRushBoss),
		"InitBossRushMaxRecord":                                       reflect.ValueOf(cl.InitBossRushMaxRecord),
		"InitBossRushTeamData":                                        reflect.ValueOf(cl.InitBossRushTeamData),
		"InitBotData":                                                 reflect.ValueOf(cl.InitBotData),
		"InitBotEmblem":                                               reflect.ValueOf(cl.InitBotEmblem),
		"InitBotEquip":                                                reflect.ValueOf(cl.InitBotEquip),
		"InitBotGem":                                                  reflect.ValueOf(cl.InitBotGem),
		"InitBotHero":                                                 reflect.ValueOf(cl.InitBotHero),
		"InitBoxOpenInfo":                                             reflect.ValueOf(cl.InitBoxOpenInfo),
		"InitCarnival":                                                reflect.ValueOf(cl.InitCarnival),
		"InitChatLike":                                                reflect.ValueOf(cl.InitChatLike),
		"InitChatMessage":                                             reflect.ValueOf(cl.InitChatMessage),
		"InitChatUserHead":                                            reflect.ValueOf(cl.InitChatUserHead),
		"InitClientInfo":                                              reflect.ValueOf(cl.InitClientInfo),
		"InitClientInfoData":                                          reflect.ValueOf(cl.InitClientInfoData),
		"InitCognitionHeroPassiveSkill":                               reflect.ValueOf(cl.InitCognitionHeroPassiveSkill),
		"InitCognitionLog":                                            reflect.ValueOf(cl.InitCognitionLog),
		"InitCognitionLogLinkInfo":                                    reflect.ValueOf(cl.InitCognitionLogLinkInfo),
		"InitComplianceList":                                          reflect.ValueOf(cl.InitComplianceList),
		"InitComplianceSourcePoint":                                   reflect.ValueOf(cl.InitComplianceSourcePoint),
		"InitComplianceTask":                                          reflect.ValueOf(cl.InitComplianceTask),
		"InitComplianceTasks":                                         reflect.ValueOf(cl.InitComplianceTasks),
		"InitCouponCfg":                                               reflect.ValueOf(cl.InitCouponCfg),
		"InitCouponCondition":                                         reflect.ValueOf(cl.InitCouponCondition),
		"InitCouponRuleContent":                                       reflect.ValueOf(cl.InitCouponRuleContent),
		"InitCrystal":                                                 reflect.ValueOf(cl.InitCrystal),
		"InitCrystalBlessing":                                         reflect.ValueOf(cl.InitCrystalBlessing),
		"InitCrystalBlessingAchieve":                                  reflect.ValueOf(cl.InitCrystalBlessingAchieve),
		"InitCrystalShareAttr":                                        reflect.ValueOf(cl.InitCrystalShareAttr),
		"InitCrystalSlot":                                             reflect.ValueOf(cl.InitCrystalSlot),
		"InitCultivateArtifactData":                                   reflect.ValueOf(cl.InitCultivateArtifactData),
		"InitCultivateEmblemSkillData":                                reflect.ValueOf(cl.InitCultivateEmblemSkillData),
		"InitCultivateFormation":                                      reflect.ValueOf(cl.InitCultivateFormation),
		"InitCultivateFormationData":                                  reflect.ValueOf(cl.InitCultivateFormationData),
		"InitCultivateHeroData":                                       reflect.ValueOf(cl.InitCultivateHeroData),
		"InitCultivateHotRank":                                        reflect.ValueOf(cl.InitCultivateHotRank),
		"InitDailyAttendance":                                         reflect.ValueOf(cl.InitDailyAttendance),
		"InitDailyAttendanceData":                                     reflect.ValueOf(cl.InitDailyAttendanceData),
		"InitDailyAttendanceHero":                                     reflect.ValueOf(cl.InitDailyAttendanceHero),
		"InitDailyAttendanceRecvParam":                                reflect.ValueOf(cl.InitDailyAttendanceRecvParam),
		"InitDailyCondition":                                          reflect.ValueOf(cl.InitDailyCondition),
		"InitDailySpecial":                                            reflect.ValueOf(cl.InitDailySpecial),
		"InitDailyTask":                                               reflect.ValueOf(cl.InitDailyTask),
		"InitDailyWish":                                               reflect.ValueOf(cl.InitDailyWish),
		"InitDailyWishActivityInfo":                                   reflect.ValueOf(cl.InitDailyWishActivityInfo),
		"InitDailyWishAwardInfo":                                      reflect.ValueOf(cl.InitDailyWishAwardInfo),
		"InitDisorderland":                                            reflect.ValueOf(cl.InitDisorderland),
		"InitDisorderlandMap":                                         reflect.ValueOf(cl.InitDisorderlandMap),
		"InitDisorderlandNode":                                        reflect.ValueOf(cl.InitDisorderlandNode),
		"InitDispatch":                                                reflect.ValueOf(cl.InitDispatch),
		"InitDispatchCD":                                              reflect.ValueOf(cl.InitDispatchCD),
		"InitDispatchLevelInfo":                                       reflect.ValueOf(cl.InitDispatchLevelInfo),
		"InitDispatchTask":                                            reflect.ValueOf(cl.InitDispatchTask),
		"InitDivineDemon":                                             reflect.ValueOf(cl.InitDivineDemon),
		"InitDivineDemonOpenActivity":                                 reflect.ValueOf(cl.InitDivineDemonOpenActivity),
		"InitDivineDemonSummon":                                       reflect.ValueOf(cl.InitDivineDemonSummon),
		"InitDivineDemonSummonRedCard":                                reflect.ValueOf(cl.InitDivineDemonSummonRedCard),
		"InitDivineDemonSummonV2":                                     reflect.ValueOf(cl.InitDivineDemonSummonV2),
		"InitDivineDemonTask":                                         reflect.ValueOf(cl.InitDivineDemonTask),
		"InitDivineDemonWishRecord":                                   reflect.ValueOf(cl.InitDivineDemonWishRecord),
		"InitDropActivity":                                            reflect.ValueOf(cl.InitDropActivity),
		"InitDropActivityBase":                                        reflect.ValueOf(cl.InitDropActivityBase),
		"InitDropActivityExchange":                                    reflect.ValueOf(cl.InitDropActivityExchange),
		"InitDropActivityExchangeRule":                                reflect.ValueOf(cl.InitDropActivityExchangeRule),
		"InitDuel":                                                    reflect.ValueOf(cl.InitDuel),
		"InitDungeon":                                                 reflect.ValueOf(cl.InitDungeon),
		"InitEmblemBlessingInfo":                                      reflect.ValueOf(cl.InitEmblemBlessingInfo),
		"InitEmblemGrowTransfer":                                      reflect.ValueOf(cl.InitEmblemGrowTransfer),
		"InitEmblemInfo":                                              reflect.ValueOf(cl.InitEmblemInfo),
		"InitEmblemLevelUpInfo":                                       reflect.ValueOf(cl.InitEmblemLevelUpInfo),
		"InitEmblemSuccinctInfo":                                      reflect.ValueOf(cl.InitEmblemSuccinctInfo),
		"InitEmpty":                                                   reflect.ValueOf(cl.InitEmpty),
		"InitEquipment":                                               reflect.ValueOf(cl.InitEquipment),
		"InitExpiredItem":                                             reflect.ValueOf(cl.InitExpiredItem),
		"InitExpiredItems":                                            reflect.ValueOf(cl.InitExpiredItems),
		"InitFirstGiftRewardStatus":                                   reflect.ValueOf(cl.InitFirstGiftRewardStatus),
		"InitFirstRankValue":                                          reflect.ValueOf(cl.InitFirstRankValue),
		"InitFlower":                                                  reflect.ValueOf(cl.InitFlower),
		"InitFlowerGuide":                                             reflect.ValueOf(cl.InitFlowerGuide),
		"InitFlowerGuildBuff":                                         reflect.ValueOf(cl.InitFlowerGuildBuff),
		"InitFlowerJungleOccupy":                                      reflect.ValueOf(cl.InitFlowerJungleOccupy),
		"InitFlowerLog":                                               reflect.ValueOf(cl.InitFlowerLog),
		"InitFlowerLog2Client":                                        reflect.ValueOf(cl.InitFlowerLog2Client),
		"InitFlowerLogDetail":                                         reflect.ValueOf(cl.InitFlowerLogDetail),
		"InitFlowerLogSpecific":                                       reflect.ValueOf(cl.InitFlowerLogSpecific),
		"InitFlowerNewLogTip":                                         reflect.ValueOf(cl.InitFlowerNewLogTip),
		"InitFlowerOccupy":                                            reflect.ValueOf(cl.InitFlowerOccupy),
		"InitFlowerOccupyAssist":                                      reflect.ValueOf(cl.InitFlowerOccupyAssist),
		"InitFlowerOccupyAward":                                       reflect.ValueOf(cl.InitFlowerOccupyAward),
		"InitFlowerOccupyHistory":                                     reflect.ValueOf(cl.InitFlowerOccupyHistory),
		"InitFlowerOccupyLog":                                         reflect.ValueOf(cl.InitFlowerOccupyLog),
		"InitFlowerOccupyLogDeletes":                                  reflect.ValueOf(cl.InitFlowerOccupyLogDeletes),
		"InitFlowerOccupyLogs":                                        reflect.ValueOf(cl.InitFlowerOccupyLogs),
		"InitFlowerOccupyNum":                                         reflect.ValueOf(cl.InitFlowerOccupyNum),
		"InitFlowerPlant":                                             reflect.ValueOf(cl.InitFlowerPlant),
		"InitFlowerSeed":                                              reflect.ValueOf(cl.InitFlowerSeed),
		"InitFlowerSnatch":                                            reflect.ValueOf(cl.InitFlowerSnatch),
		"InitFlowerSnatchEnemy":                                       reflect.ValueOf(cl.InitFlowerSnatchEnemy),
		"InitFlowerSnatchLog":                                         reflect.ValueOf(cl.InitFlowerSnatchLog),
		"InitFlowerSnatchLogDeletes":                                  reflect.ValueOf(cl.InitFlowerSnatchLogDeletes),
		"InitFlowerSnatchLogs":                                        reflect.ValueOf(cl.InitFlowerSnatchLogs),
		"InitFlowerbed":                                               reflect.ValueOf(cl.InitFlowerbed),
		"InitForecast":                                                reflect.ValueOf(cl.InitForecast),
		"InitForest":                                                  reflect.ValueOf(cl.InitForest),
		"InitForestLog":                                               reflect.ValueOf(cl.InitForestLog),
		"InitForestLogDeletes":                                        reflect.ValueOf(cl.InitForestLogDeletes),
		"InitForestLogs":                                              reflect.ValueOf(cl.InitForestLogs),
		"InitForestTree":                                              reflect.ValueOf(cl.InitForestTree),
		"InitFormation":                                               reflect.ValueOf(cl.InitFormation),
		"InitFormationArtifactInfo":                                   reflect.ValueOf(cl.InitFormationArtifactInfo),
		"InitFormationInfo":                                           reflect.ValueOf(cl.InitFormationInfo),
		"InitFormationRemainInfo":                                     reflect.ValueOf(cl.InitFormationRemainInfo),
		"InitFormationRiteInfo":                                       reflect.ValueOf(cl.InitFormationRiteInfo),
		"InitFormationRitePower":                                      reflect.ValueOf(cl.InitFormationRitePower),
		"InitFormationSidUid":                                         reflect.ValueOf(cl.InitFormationSidUid),
		"InitFormationSidUidPower":                                    reflect.ValueOf(cl.InitFormationSidUidPower),
		"InitFormationTeamInfo":                                       reflect.ValueOf(cl.InitFormationTeamInfo),
		"InitFragmentComposeUnit":                                     reflect.ValueOf(cl.InitFragmentComposeUnit),
		"InitFriend":                                                  reflect.ValueOf(cl.InitFriend),
		"InitFriendRequest":                                           reflect.ValueOf(cl.InitFriendRequest),
		"InitFunctionShowcase":                                        reflect.ValueOf(cl.InitFunctionShowcase),
		"InitGSTArenaFightRecord":                                     reflect.ValueOf(cl.InitGSTArenaFightRecord),
		"InitGSTArenaGuildInfo":                                       reflect.ValueOf(cl.InitGSTArenaGuildInfo),
		"InitGSTArenaGuildRank":                                       reflect.ValueOf(cl.InitGSTArenaGuildRank),
		"InitGSTArenaGuildUserRank":                                   reflect.ValueOf(cl.InitGSTArenaGuildUserRank),
		"InitGSTArenaInfo":                                            reflect.ValueOf(cl.InitGSTArenaInfo),
		"InitGSTArenaState":                                           reflect.ValueOf(cl.InitGSTArenaState),
		"InitGSTArenaVoteRecord":                                      reflect.ValueOf(cl.InitGSTArenaVoteRecord),
		"InitGSTAssistOre":                                            reflect.ValueOf(cl.InitGSTAssistOre),
		"InitGSTAssistOreData":                                        reflect.ValueOf(cl.InitGSTAssistOreData),
		"InitGSTBoss":                                                 reflect.ValueOf(cl.InitGSTBoss),
		"InitGSTBossFightRank":                                        reflect.ValueOf(cl.InitGSTBossFightRank),
		"InitGSTBossFightRecord":                                      reflect.ValueOf(cl.InitGSTBossFightRecord),
		"InitGSTBossGroup":                                            reflect.ValueOf(cl.InitGSTBossGroup),
		"InitGSTBossUser":                                             reflect.ValueOf(cl.InitGSTBossUser),
		"InitGSTBuild":                                                reflect.ValueOf(cl.InitGSTBuild),
		"InitGSTBuildDispatchHero":                                    reflect.ValueOf(cl.InitGSTBuildDispatchHero),
		"InitGSTBuildDonate":                                          reflect.ValueOf(cl.InitGSTBuildDonate),
		"InitGSTBuildDonateDetails":                                   reflect.ValueOf(cl.InitGSTBuildDonateDetails),
		"InitGSTBuildDonateRank":                                      reflect.ValueOf(cl.InitGSTBuildDonateRank),
		"InitGSTBuildDonateRankClient":                                reflect.ValueOf(cl.InitGSTBuildDonateRankClient),
		"InitGSTBuildTaskInfo":                                        reflect.ValueOf(cl.InitGSTBuildTaskInfo),
		"InitGSTBuildUserDispatchHeroes":                              reflect.ValueOf(cl.InitGSTBuildUserDispatchHeroes),
		"InitGSTBuildUserDispatchHeroesClient":                        reflect.ValueOf(cl.InitGSTBuildUserDispatchHeroesClient),
		"InitGSTChallengeFightLog":                                    reflect.ValueOf(cl.InitGSTChallengeFightLog),
		"InitGSTChallengeFightLogDeletes":                             reflect.ValueOf(cl.InitGSTChallengeFightLogDeletes),
		"InitGSTChallengeFightLogs":                                   reflect.ValueOf(cl.InitGSTChallengeFightLogs),
		"InitGSTChallengeGuildRank":                                   reflect.ValueOf(cl.InitGSTChallengeGuildRank),
		"InitGSTChallengeGuildUserRank":                               reflect.ValueOf(cl.InitGSTChallengeGuildUserRank),
		"InitGSTChallengeLastTop":                                     reflect.ValueOf(cl.InitGSTChallengeLastTop),
		"InitGSTChallengeMatchInfo":                                   reflect.ValueOf(cl.InitGSTChallengeMatchInfo),
		"InitGSTChallengeMatched":                                     reflect.ValueOf(cl.InitGSTChallengeMatched),
		"InitGSTChallengeRecord":                                      reflect.ValueOf(cl.InitGSTChallengeRecord),
		"InitGSTChallengeShowLog":                                     reflect.ValueOf(cl.InitGSTChallengeShowLog),
		"InitGSTChallengeTaskData":                                    reflect.ValueOf(cl.InitGSTChallengeTaskData),
		"InitGSTChallengeTeamInfo":                                    reflect.ValueOf(cl.InitGSTChallengeTeamInfo),
		"InitGSTClientDonate":                                         reflect.ValueOf(cl.InitGSTClientDonate),
		"InitGSTComplexData":                                          reflect.ValueOf(cl.InitGSTComplexData),
		"InitGSTDonate":                                               reflect.ValueOf(cl.InitGSTDonate),
		"InitGSTDragonBattle":                                         reflect.ValueOf(cl.InitGSTDragonBattle),
		"InitGSTDragonBattleData":                                     reflect.ValueOf(cl.InitGSTDragonBattleData),
		"InitGSTDragonBattleResult":                                   reflect.ValueOf(cl.InitGSTDragonBattleResult),
		"InitGSTDragonCultivation":                                    reflect.ValueOf(cl.InitGSTDragonCultivation),
		"InitGSTDragonFightRank":                                      reflect.ValueOf(cl.InitGSTDragonFightRank),
		"InitGSTDragonFightRecord":                                    reflect.ValueOf(cl.InitGSTDragonFightRecord),
		"InitGSTDragonFightRecordTeamData":                            reflect.ValueOf(cl.InitGSTDragonFightRecordTeamData),
		"InitGSTDragonGuild":                                          reflect.ValueOf(cl.InitGSTDragonGuild),
		"InitGSTDragonSkill":                                          reflect.ValueOf(cl.InitGSTDragonSkill),
		"InitGSTDragonTaskInfo":                                       reflect.ValueOf(cl.InitGSTDragonTaskInfo),
		"InitGSTDragonUser":                                           reflect.ValueOf(cl.InitGSTDragonUser),
		"InitGSTDragonUserRoundData":                                  reflect.ValueOf(cl.InitGSTDragonUserRoundData),
		"InitGSTDragonUserRoundMaxDamage":                             reflect.ValueOf(cl.InitGSTDragonUserRoundMaxDamage),
		"InitGSTDragonUserSeasonData":                                 reflect.ValueOf(cl.InitGSTDragonUserSeasonData),
		"InitGSTDragonUserTeamData":                                   reflect.ValueOf(cl.InitGSTDragonUserTeamData),
		"InitGSTFightResult":                                          reflect.ValueOf(cl.InitGSTFightResult),
		"InitGSTFightTeam":                                            reflect.ValueOf(cl.InitGSTFightTeam),
		"InitGSTFormationTeamInfo":                                    reflect.ValueOf(cl.InitGSTFormationTeamInfo),
		"InitGSTGetLogArenaFightRecord":                               reflect.ValueOf(cl.InitGSTGetLogArenaFightRecord),
		"InitGSTGetLogBuildDonateDetails":                             reflect.ValueOf(cl.InitGSTGetLogBuildDonateDetails),
		"InitGSTGetLogChallengeRecord":                                reflect.ValueOf(cl.InitGSTGetLogChallengeRecord),
		"InitGSTGetLogDragonFightRecord":                              reflect.ValueOf(cl.InitGSTGetLogDragonFightRecord),
		"InitGSTGetLogGSTBossFightRecord":                             reflect.ValueOf(cl.InitGSTGetLogGSTBossFightRecord),
		"InitGSTGetLogGround":                                         reflect.ValueOf(cl.InitGSTGetLogGround),
		"InitGSTGetLogGuild":                                          reflect.ValueOf(cl.InitGSTGetLogGuild),
		"InitGSTGetLogGuildMobilizationRank":                          reflect.ValueOf(cl.InitGSTGetLogGuildMobilizationRank),
		"InitGSTGetLogGuildSkill":                                     reflect.ValueOf(cl.InitGSTGetLogGuildSkill),
		"InitGSTGetLogOreAssist":                                      reflect.ValueOf(cl.InitGSTGetLogOreAssist),
		"InitGSTGetLogOreChange":                                      reflect.ValueOf(cl.InitGSTGetLogOreChange),
		"InitGSTGetLogPC":                                             reflect.ValueOf(cl.InitGSTGetLogPC),
		"InitGSTGetLogPF":                                             reflect.ValueOf(cl.InitGSTGetLogPF),
		"InitGSTGetLogReq":                                            reflect.ValueOf(cl.InitGSTGetLogReq),
		"InitGSTGetLogReqData":                                        reflect.ValueOf(cl.InitGSTGetLogReqData),
		"InitGSTGetLogTech":                                           reflect.ValueOf(cl.InitGSTGetLogTech),
		"InitGSTGoddessBless":                                         reflect.ValueOf(cl.InitGSTGoddessBless),
		"InitGSTGroundInfo":                                           reflect.ValueOf(cl.InitGSTGroundInfo),
		"InitGSTGroundTeamData":                                       reflect.ValueOf(cl.InitGSTGroundTeamData),
		"InitGSTGroundTeamInfo":                                       reflect.ValueOf(cl.InitGSTGroundTeamInfo),
		"InitGSTGuildTech":                                            reflect.ValueOf(cl.InitGSTGuildTech),
		"InitGSTGuildUser":                                            reflect.ValueOf(cl.InitGSTGuildUser),
		"InitGSTGuildUserBase":                                        reflect.ValueOf(cl.InitGSTGuildUserBase),
		"InitGSTGuildUserChallenge":                                   reflect.ValueOf(cl.InitGSTGuildUserChallenge),
		"InitGSTGuildUserOre":                                         reflect.ValueOf(cl.InitGSTGuildUserOre),
		"InitGSTGuildUserOreData":                                     reflect.ValueOf(cl.InitGSTGuildUserOreData),
		"InitGSTGuildUserOreTimes":                                    reflect.ValueOf(cl.InitGSTGuildUserOreTimes),
		"InitGSTGuildUserRoundSettle":                                 reflect.ValueOf(cl.InitGSTGuildUserRoundSettle),
		"InitGSTGuildUserTeamFightInfo":                               reflect.ValueOf(cl.InitGSTGuildUserTeamFightInfo),
		"InitGSTGuildUserTech":                                        reflect.ValueOf(cl.InitGSTGuildUserTech),
		"InitGSTLastLRoundGuildRank":                                  reflect.ValueOf(cl.InitGSTLastLRoundGuildRank),
		"InitGSTLogData":                                              reflect.ValueOf(cl.InitGSTLogData),
		"InitGSTLogDragonSkill":                                       reflect.ValueOf(cl.InitGSTLogDragonSkill),
		"InitGSTLogDragonSkillGuildInfo":                              reflect.ValueOf(cl.InitGSTLogDragonSkillGuildInfo),
		"InitGSTLogFightTeam":                                         reflect.ValueOf(cl.InitGSTLogFightTeam),
		"InitGSTLogGroupGuildSettle":                                  reflect.ValueOf(cl.InitGSTLogGroupGuildSettle),
		"InitGSTLogGroupSettle":                                       reflect.ValueOf(cl.InitGSTLogGroupSettle),
		"InitGSTLogGuildMobilization":                                 reflect.ValueOf(cl.InitGSTLogGuildMobilization),
		"InitGSTLogGuildSkill":                                        reflect.ValueOf(cl.InitGSTLogGuildSkill),
		"InitGSTLogInfo":                                              reflect.ValueOf(cl.InitGSTLogInfo),
		"InitGSTLogOreAssist":                                         reflect.ValueOf(cl.InitGSTLogOreAssist),
		"InitGSTLogOreChange":                                         reflect.ValueOf(cl.InitGSTLogOreChange),
		"InitGSTLogTech":                                              reflect.ValueOf(cl.InitGSTLogTech),
		"InitGSTManyLog":                                              reflect.ValueOf(cl.InitGSTManyLog),
		"InitGSTMapInfo":                                              reflect.ValueOf(cl.InitGSTMapInfo),
		"InitGSTRoundSettle":                                          reflect.ValueOf(cl.InitGSTRoundSettle),
		"InitGSTSetFormationTeamInfo":                                 reflect.ValueOf(cl.InitGSTSetFormationTeamInfo),
		"InitGSTSimpleData":                                           reflect.ValueOf(cl.InitGSTSimpleData),
		"InitGSTSimpleGuild":                                          reflect.ValueOf(cl.InitGSTSimpleGuild),
		"InitGSTSta":                                                  reflect.ValueOf(cl.InitGSTSta),
		"InitGSTTaskInfo":                                             reflect.ValueOf(cl.InitGSTTaskInfo),
		"InitGSTTeamData":                                             reflect.ValueOf(cl.InitGSTTeamData),
		"InitGSTTeamHeroInfo":                                         reflect.ValueOf(cl.InitGSTTeamHeroInfo),
		"InitGSTTeamOperate":                                          reflect.ValueOf(cl.InitGSTTeamOperate),
		"InitGSTTechData":                                             reflect.ValueOf(cl.InitGSTTechData),
		"InitGSTTechRankData":                                         reflect.ValueOf(cl.InitGSTTechRankData),
		"InitGSTTechTaskData":                                         reflect.ValueOf(cl.InitGSTTechTaskData),
		"InitGSTUserScoreRankShow":                                    reflect.ValueOf(cl.InitGSTUserScoreRankShow),
		"InitGSTUserWinsRank":                                         reflect.ValueOf(cl.InitGSTUserWinsRank),
		"InitGem":                                                     reflect.ValueOf(cl.InitGem),
		"InitGemAttr":                                                 reflect.ValueOf(cl.InitGemAttr),
		"InitGemInfo":                                                 reflect.ValueOf(cl.InitGemInfo),
		"InitGetLikeListParam":                                        reflect.ValueOf(cl.InitGetLikeListParam),
		"InitGlobalAttr":                                              reflect.ValueOf(cl.InitGlobalAttr),
		"InitGlobalScore":                                             reflect.ValueOf(cl.InitGlobalScore),
		"InitGodPresent":                                              reflect.ValueOf(cl.InitGodPresent),
		"InitGodPresentClient":                                        reflect.ValueOf(cl.InitGodPresentClient),
		"InitGodPresentNew":                                           reflect.ValueOf(cl.InitGodPresentNew),
		"InitGodPresentSummon":                                        reflect.ValueOf(cl.InitGodPresentSummon),
		"InitGodPresents":                                             reflect.ValueOf(cl.InitGodPresents),
		"InitGodPresentsNew":                                          reflect.ValueOf(cl.InitGodPresentsNew),
		"InitGoddess":                                                 reflect.ValueOf(cl.InitGoddess),
		"InitGoddessContractInfo":                                     reflect.ValueOf(cl.InitGoddessContractInfo),
		"InitGoldBuy":                                                 reflect.ValueOf(cl.InitGoldBuy),
		"InitGoldChest":                                               reflect.ValueOf(cl.InitGoldChest),
		"InitGoodsState":                                              reflect.ValueOf(cl.InitGoodsState),
		"InitGstGroupUserRankValue":                                   reflect.ValueOf(cl.InitGstGroupUserRankValue),
		"InitGstGuildAddScore":                                        reflect.ValueOf(cl.InitGstGuildAddScore),
		"InitGstLogGeneral":                                           reflect.ValueOf(cl.InitGstLogGeneral),
		"InitGstTripleKillInfo":                                       reflect.ValueOf(cl.InitGstTripleKillInfo),
		"InitGstTripleKillTeamInfo":                                   reflect.ValueOf(cl.InitGstTripleKillTeamInfo),
		"InitGuidance":                                                reflect.ValueOf(cl.InitGuidance),
		"InitGuidanceGroup":                                           reflect.ValueOf(cl.InitGuidanceGroup),
		"InitGuildChest":                                              reflect.ValueOf(cl.InitGuildChest),
		"InitGuildChestData":                                          reflect.ValueOf(cl.InitGuildChestData),
		"InitGuildChestFinishRecv":                                    reflect.ValueOf(cl.InitGuildChestFinishRecv),
		"InitGuildChestSlotDetail":                                    reflect.ValueOf(cl.InitGuildChestSlotDetail),
		"InitGuildCombineApplyItem":                                   reflect.ValueOf(cl.InitGuildCombineApplyItem),
		"InitGuildCombineStatus":                                      reflect.ValueOf(cl.InitGuildCombineStatus),
		"InitGuildDonateDeletes":                                      reflect.ValueOf(cl.InitGuildDonateDeletes),
		"InitGuildDonateLogInfo":                                      reflect.ValueOf(cl.InitGuildDonateLogInfo),
		"InitGuildDonateLogs":                                         reflect.ValueOf(cl.InitGuildDonateLogs),
		"InitGuildDungeonBossBoxInfo":                                 reflect.ValueOf(cl.InitGuildDungeonBossBoxInfo),
		"InitGuildDungeonBossInfo":                                    reflect.ValueOf(cl.InitGuildDungeonBossInfo),
		"InitGuildDungeonChapterRankTopGuild":                         reflect.ValueOf(cl.InitGuildDungeonChapterRankTopGuild),
		"InitGuildDungeonHallOfFameInfo":                              reflect.ValueOf(cl.InitGuildDungeonHallOfFameInfo),
		"InitGuildDungeonLog":                                         reflect.ValueOf(cl.InitGuildDungeonLog),
		"InitGuildDungeonLogDeletes":                                  reflect.ValueOf(cl.InitGuildDungeonLogDeletes),
		"InitGuildDungeonLogs":                                        reflect.ValueOf(cl.InitGuildDungeonLogs),
		"InitGuildDungeonMemberFightInfo":                             reflect.ValueOf(cl.InitGuildDungeonMemberFightInfo),
		"InitGuildDungeonMessageBoardLog":                             reflect.ValueOf(cl.InitGuildDungeonMessageBoardLog),
		"InitGuildDungeonMessageBoardLogDeletes":                      reflect.ValueOf(cl.InitGuildDungeonMessageBoardLogDeletes),
		"InitGuildDungeonMessageBoardLogs":                            reflect.ValueOf(cl.InitGuildDungeonMessageBoardLogs),
		"InitGuildDungeonRoomRankInfo":                                reflect.ValueOf(cl.InitGuildDungeonRoomRankInfo),
		"InitGuildDungeonSelfBossInfo":                                reflect.ValueOf(cl.InitGuildDungeonSelfBossInfo),
		"InitGuildDungeonStrategyEffect":                              reflect.ValueOf(cl.InitGuildDungeonStrategyEffect),
		"InitGuildDungeonTopDailyDamageUser":                          reflect.ValueOf(cl.InitGuildDungeonTopDailyDamageUser),
		"InitGuildInfo":                                               reflect.ValueOf(cl.InitGuildInfo),
		"InitGuildLeaveCount":                                         reflect.ValueOf(cl.InitGuildLeaveCount),
		"InitGuildLogDeletes":                                         reflect.ValueOf(cl.InitGuildLogDeletes),
		"InitGuildLogInfo":                                            reflect.ValueOf(cl.InitGuildLogInfo),
		"InitGuildLogs":                                               reflect.ValueOf(cl.InitGuildLogs),
		"InitGuildMedal":                                              reflect.ValueOf(cl.InitGuildMedal),
		"InitGuildMedalLiked":                                         reflect.ValueOf(cl.InitGuildMedalLiked),
		"InitGuildMedalSeasonArenaBak":                                reflect.ValueOf(cl.InitGuildMedalSeasonArenaBak),
		"InitGuildMemberInfo":                                         reflect.ValueOf(cl.InitGuildMemberInfo),
		"InitGuildMobilizationGuildRank":                              reflect.ValueOf(cl.InitGuildMobilizationGuildRank),
		"InitGuildMobilizationGuildTask":                              reflect.ValueOf(cl.InitGuildMobilizationGuildTask),
		"InitGuildMobilizationMemberTask":                             reflect.ValueOf(cl.InitGuildMobilizationMemberTask),
		"InitGuildMobilizationPersonalRank":                           reflect.ValueOf(cl.InitGuildMobilizationPersonalRank),
		"InitGuildMobilizationTaskData":                               reflect.ValueOf(cl.InitGuildMobilizationTaskData),
		"InitGuildMobilizationTaskLog":                                reflect.ValueOf(cl.InitGuildMobilizationTaskLog),
		"InitGuildMobilizationTaskProgress":                           reflect.ValueOf(cl.InitGuildMobilizationTaskProgress),
		"InitGuildSnapshot":                                           reflect.ValueOf(cl.InitGuildSnapshot),
		"InitGuildTalent":                                             reflect.ValueOf(cl.InitGuildTalent),
		"InitHandbook":                                                reflect.ValueOf(cl.InitHandbook),
		"InitHandbooks":                                               reflect.ValueOf(cl.InitHandbooks),
		"InitHero":                                                    reflect.ValueOf(cl.InitHero),
		"InitHeroBody":                                                reflect.ValueOf(cl.InitHeroBody),
		"InitHeroHandbook":                                            reflect.ValueOf(cl.InitHeroHandbook),
		"InitHeroStarUpCosts":                                         reflect.ValueOf(cl.InitHeroStarUpCosts),
		"InitHeroTag":                                                 reflect.ValueOf(cl.InitHeroTag),
		"InitLattice":                                                 reflect.ValueOf(cl.InitLattice),
		"InitLineTask":                                                reflect.ValueOf(cl.InitLineTask),
		"InitLinkHeroSummon":                                          reflect.ValueOf(cl.InitLinkHeroSummon),
		"InitLinkInfo":                                                reflect.ValueOf(cl.InitLinkInfo),
		"InitLinkSetting":                                             reflect.ValueOf(cl.InitLinkSetting),
		"InitLinkSummonTime":                                          reflect.ValueOf(cl.InitLinkSummonTime),
		"InitLinkSummonTimes":                                         reflect.ValueOf(cl.InitLinkSummonTimes),
		"InitMail":                                                    reflect.ValueOf(cl.InitMail),
		"InitMailCond":                                                reflect.ValueOf(cl.InitMailCond),
		"InitMazeBeSelectBuff":                                        reflect.ValueOf(cl.InitMazeBeSelectBuff),
		"InitMazeBuff":                                                reflect.ValueOf(cl.InitMazeBuff),
		"InitMazeEnemy":                                               reflect.ValueOf(cl.InitMazeEnemy),
		"InitMazeGrid":                                                reflect.ValueOf(cl.InitMazeGrid),
		"InitMazeHp":                                                  reflect.ValueOf(cl.InitMazeHp),
		"InitMazeMap":                                                 reflect.ValueOf(cl.InitMazeMap),
		"InitMazePlayer":                                              reflect.ValueOf(cl.InitMazePlayer),
		"InitMazeTask":                                                reflect.ValueOf(cl.InitMazeTask),
		"InitMedal":                                                   reflect.ValueOf(cl.InitMedal),
		"InitMemory":                                                  reflect.ValueOf(cl.InitMemory),
		"InitMessage":                                                 reflect.ValueOf(cl.InitMessage),
		"InitMessageBoard":                                            reflect.ValueOf(cl.InitMessageBoard),
		"InitMiniUserSnapshot":                                        reflect.ValueOf(cl.InitMiniUserSnapshot),
		"InitMirage":                                                  reflect.ValueOf(cl.InitMirage),
		"InitMirageHeroNum":                                           reflect.ValueOf(cl.InitMirageHeroNum),
		"InitMonthTasks":                                              reflect.ValueOf(cl.InitMonthTasks),
		"InitMonthTasksMsg":                                           reflect.ValueOf(cl.InitMonthTasksMsg),
		"InitMonthlyCard":                                             reflect.ValueOf(cl.InitMonthlyCard),
		"InitMonthlyCardData":                                         reflect.ValueOf(cl.InitMonthlyCardData),
		"InitMonthlyTask":                                             reflect.ValueOf(cl.InitMonthlyTask),
		"InitMonumentRune":                                            reflect.ValueOf(cl.InitMonumentRune),
		"InitMultipleMiniUserSnapshot":                                reflect.ValueOf(cl.InitMultipleMiniUserSnapshot),
		"InitMultipleUserIds":                                         reflect.ValueOf(cl.InitMultipleUserIds),
		"InitNewYearActivity":                                         reflect.ValueOf(cl.InitNewYearActivity),
		"InitNumInfo":                                                 reflect.ValueOf(cl.InitNumInfo),
		"InitOperateActivity":                                         reflect.ValueOf(cl.InitOperateActivity),
		"InitOperateActivityConfig":                                   reflect.ValueOf(cl.InitOperateActivityConfig),
		"InitOperateActivityInfo":                                     reflect.ValueOf(cl.InitOperateActivityInfo),
		"InitOperateGift":                                             reflect.ValueOf(cl.InitOperateGift),
		"InitOperateGiftInfo":                                         reflect.ValueOf(cl.InitOperateGiftInfo),
		"InitOperatePageInfo":                                         reflect.ValueOf(cl.InitOperatePageInfo),
		"InitOperateTask":                                             reflect.ValueOf(cl.InitOperateTask),
		"InitOperateTaskInfo":                                         reflect.ValueOf(cl.InitOperateTaskInfo),
		"InitOrderCustomData":                                         reflect.ValueOf(cl.InitOrderCustomData),
		"InitPassActive":                                              reflect.ValueOf(cl.InitPassActive),
		"InitPassData":                                                reflect.ValueOf(cl.InitPassData),
		"InitPassReceiveSate":                                         reflect.ValueOf(cl.InitPassReceiveSate),
		"InitPeakMatch":                                               reflect.ValueOf(cl.InitPeakMatch),
		"InitPeakMatchFighter":                                        reflect.ValueOf(cl.InitPeakMatchFighter),
		"InitPeakMatchParam":                                          reflect.ValueOf(cl.InitPeakMatchParam),
		"InitPeakRankScore":                                           reflect.ValueOf(cl.InitPeakRankScore),
		"InitPeakResult":                                              reflect.ValueOf(cl.InitPeakResult),
		"InitPeakState":                                               reflect.ValueOf(cl.InitPeakState),
		"InitPeakUser":                                                reflect.ValueOf(cl.InitPeakUser),
		"InitPeakUserGuess":                                           reflect.ValueOf(cl.InitPeakUserGuess),
		"InitPreSeason":                                               reflect.ValueOf(cl.InitPreSeason),
		"InitPromotionGift":                                           reflect.ValueOf(cl.InitPromotionGift),
		"InitPromotionGiftInfo":                                       reflect.ValueOf(cl.InitPromotionGiftInfo),
		"InitPushGift":                                                reflect.ValueOf(cl.InitPushGift),
		"InitPushGiftCondition":                                       reflect.ValueOf(cl.InitPushGiftCondition),
		"InitPushGiftInfo":                                            reflect.ValueOf(cl.InitPushGiftInfo),
		"InitPushGifts":                                               reflect.ValueOf(cl.InitPushGifts),
		"InitPuzzleCell":                                              reflect.ValueOf(cl.InitPuzzleCell),
		"InitPyramid":                                                 reflect.ValueOf(cl.InitPyramid),
		"InitPyramidActivityBase":                                     reflect.ValueOf(cl.InitPyramidActivityBase),
		"InitPyramidTestResult":                                       reflect.ValueOf(cl.InitPyramidTestResult),
		"InitQuestionnaire":                                           reflect.ValueOf(cl.InitQuestionnaire),
		"InitRankAchieve":                                             reflect.ValueOf(cl.InitRankAchieve),
		"InitRankValue":                                               reflect.ValueOf(cl.InitRankValue),
		"InitRate":                                                    reflect.ValueOf(cl.InitRate),
		"InitRedPointIdList":                                          reflect.ValueOf(cl.InitRedPointIdList),
		"InitRemain":                                                  reflect.ValueOf(cl.InitRemain),
		"InitRemainBook":                                              reflect.ValueOf(cl.InitRemainBook),
		"InitRemainItem":                                              reflect.ValueOf(cl.InitRemainItem),
		"InitResource":                                                reflect.ValueOf(cl.InitResource),
		"InitResources":                                               reflect.ValueOf(cl.InitResources),
		"InitRite":                                                    reflect.ValueOf(cl.InitRite),
		"InitRiteGrid":                                                reflect.ValueOf(cl.InitRiteGrid),
		"InitRoundActivity":                                           reflect.ValueOf(cl.InitRoundActivity),
		"InitSeasonAddInfo":                                           reflect.ValueOf(cl.InitSeasonAddInfo),
		"InitSeasonArenaData":                                         reflect.ValueOf(cl.InitSeasonArenaData),
		"InitSeasonArenaLog":                                          reflect.ValueOf(cl.InitSeasonArenaLog),
		"InitSeasonArenaLogDeletes":                                   reflect.ValueOf(cl.InitSeasonArenaLogDeletes),
		"InitSeasonArenaLogs":                                         reflect.ValueOf(cl.InitSeasonArenaLogs),
		"InitSeasonArenaOpponentClient":                               reflect.ValueOf(cl.InitSeasonArenaOpponentClient),
		"InitSeasonArenaSta":                                          reflect.ValueOf(cl.InitSeasonArenaSta),
		"InitSeasonArenaTask":                                         reflect.ValueOf(cl.InitSeasonArenaTask),
		"InitSeasonArenaUserBase":                                     reflect.ValueOf(cl.InitSeasonArenaUserBase),
		"InitSeasonArenaUserScoreAndRank":                             reflect.ValueOf(cl.InitSeasonArenaUserScoreAndRank),
		"InitSeasonCompliance":                                        reflect.ValueOf(cl.InitSeasonCompliance),
		"InitSeasonComplianceStage":                                   reflect.ValueOf(cl.InitSeasonComplianceStage),
		"InitSeasonDoor":                                              reflect.ValueOf(cl.InitSeasonDoor),
		"InitSeasonDoorBatchFightResult":                              reflect.ValueOf(cl.InitSeasonDoorBatchFightResult),
		"InitSeasonDoorFightLine":                                     reflect.ValueOf(cl.InitSeasonDoorFightLine),
		"InitSeasonDoorFightLineLog":                                  reflect.ValueOf(cl.InitSeasonDoorFightLineLog),
		"InitSeasonDoorFightResult":                                   reflect.ValueOf(cl.InitSeasonDoorFightResult),
		"InitSeasonDoorWaveFightResult":                               reflect.ValueOf(cl.InitSeasonDoorWaveFightResult),
		"InitSeasonDungeon":                                           reflect.ValueOf(cl.InitSeasonDungeon),
		"InitSeasonJewelry":                                           reflect.ValueOf(cl.InitSeasonJewelry),
		"InitSeasonJewelryDecomposeCfg":                               reflect.ValueOf(cl.InitSeasonJewelryDecomposeCfg),
		"InitSeasonJewelrySkill":                                      reflect.ValueOf(cl.InitSeasonJewelrySkill),
		"InitSeasonJewelryWearObject":                                 reflect.ValueOf(cl.InitSeasonJewelryWearObject),
		"InitSeasonLevel":                                             reflect.ValueOf(cl.InitSeasonLevel),
		"InitSeasonLink":                                              reflect.ValueOf(cl.InitSeasonLink),
		"InitSeasonLinkMonument":                                      reflect.ValueOf(cl.InitSeasonLinkMonument),
		"InitSeasonMap":                                               reflect.ValueOf(cl.InitSeasonMap),
		"InitSeasonMapAltarEvent":                                     reflect.ValueOf(cl.InitSeasonMapAltarEvent),
		"InitSeasonMapConnectEvent":                                   reflect.ValueOf(cl.InitSeasonMapConnectEvent),
		"InitSeasonMapDialogueEvent":                                  reflect.ValueOf(cl.InitSeasonMapDialogueEvent),
		"InitSeasonMapEvent":                                          reflect.ValueOf(cl.InitSeasonMapEvent),
		"InitSeasonMapEventLog":                                       reflect.ValueOf(cl.InitSeasonMapEventLog),
		"InitSeasonMapMasterEvent":                                    reflect.ValueOf(cl.InitSeasonMapMasterEvent),
		"InitSeasonMapMonster":                                        reflect.ValueOf(cl.InitSeasonMapMonster),
		"InitSeasonMapMonsterRecord":                                  reflect.ValueOf(cl.InitSeasonMapMonsterRecord),
		"InitSeasonMapMoveLog":                                        reflect.ValueOf(cl.InitSeasonMapMoveLog),
		"InitSeasonMapPosition":                                       reflect.ValueOf(cl.InitSeasonMapPosition),
		"InitSeasonMapPositionLog":                                    reflect.ValueOf(cl.InitSeasonMapPositionLog),
		"InitSeasonMapPrice":                                          reflect.ValueOf(cl.InitSeasonMapPrice),
		"InitSeasonMapPriceChangeLog":                                 reflect.ValueOf(cl.InitSeasonMapPriceChangeLog),
		"InitSeasonMapSystemEvent":                                    reflect.ValueOf(cl.InitSeasonMapSystemEvent),
		"InitSeasonMapTeamData":                                       reflect.ValueOf(cl.InitSeasonMapTeamData),
		"InitSeasonMapTradeEvent":                                     reflect.ValueOf(cl.InitSeasonMapTradeEvent),
		"InitSeasonReturn":                                            reflect.ValueOf(cl.InitSeasonReturn),
		"InitSeasonReturnAward":                                       reflect.ValueOf(cl.InitSeasonReturnAward),
		"InitSeasonShop":                                              reflect.ValueOf(cl.InitSeasonShop),
		"InitSeasonUserLog":                                           reflect.ValueOf(cl.InitSeasonUserLog),
		"InitSeasonUserLogPoint":                                      reflect.ValueOf(cl.InitSeasonUserLogPoint),
		"InitSelectSummon":                                            reflect.ValueOf(cl.InitSelectSummon),
		"InitSelectSummonOpenActivity":                                reflect.ValueOf(cl.InitSelectSummonOpenActivity),
		"InitSelectSummonSummon":                                      reflect.ValueOf(cl.InitSelectSummonSummon),
		"InitServerUser":                                              reflect.ValueOf(cl.InitServerUser),
		"InitSevenDayLogin":                                           reflect.ValueOf(cl.InitSevenDayLogin),
		"InitShareEmblem":                                             reflect.ValueOf(cl.InitShareEmblem),
		"InitShareEquipment":                                          reflect.ValueOf(cl.InitShareEquipment),
		"InitShareGem":                                                reflect.ValueOf(cl.InitShareGem),
		"InitShareGrowth":                                             reflect.ValueOf(cl.InitShareGrowth),
		"InitShareHero":                                               reflect.ValueOf(cl.InitShareHero),
		"InitShop":                                                    reflect.ValueOf(cl.InitShop),
		"InitSkin":                                                    reflect.ValueOf(cl.InitSkin),
		"InitStarUpCosts":                                             reflect.ValueOf(cl.InitStarUpCosts),
		"InitStoryReviewData":                                         reflect.ValueOf(cl.InitStoryReviewData),
		"InitStrategyInfo":                                            reflect.ValueOf(cl.InitStrategyInfo),
		"InitStrategyRestoreBoss":                                     reflect.ValueOf(cl.InitStrategyRestoreBoss),
		"InitSubItem":                                                 reflect.ValueOf(cl.InitSubItem),
		"InitSweepTestData":                                           reflect.ValueOf(cl.InitSweepTestData),
		"InitTaleInfo":                                                reflect.ValueOf(cl.InitTaleInfo),
		"InitTalentTreeBase":                                          reflect.ValueOf(cl.InitTalentTreeBase),
		"InitTalentTreeCultivate":                                     reflect.ValueOf(cl.InitTalentTreeCultivate),
		"InitTalentTreeNode":                                          reflect.ValueOf(cl.InitTalentTreeNode),
		"InitTalentTreeNodeHot":                                       reflect.ValueOf(cl.InitTalentTreeNodeHot),
		"InitTalentTreePlan":                                          reflect.ValueOf(cl.InitTalentTreePlan),
		"InitTalentTreeResetUnit":                                     reflect.ValueOf(cl.InitTalentTreeResetUnit),
		"InitTales":                                                   reflect.ValueOf(cl.InitTales),
		"InitTargetAttr":                                              reflect.ValueOf(cl.InitTargetAttr),
		"InitTaskProgress":                                            reflect.ValueOf(cl.InitTaskProgress),
		"InitTaskTypeProgress":                                        reflect.ValueOf(cl.InitTaskTypeProgress),
		"InitTitle":                                                   reflect.ValueOf(cl.InitTitle),
		"InitTower":                                                   reflect.ValueOf(cl.InitTower),
		"InitTowerSeason":                                             reflect.ValueOf(cl.InitTowerSeason),
		"InitTowerstar":                                               reflect.ValueOf(cl.InitTowerstar),
		"InitTowerstarChapterInfo":                                    reflect.ValueOf(cl.InitTowerstarChapterInfo),
		"InitTrialInfo":                                               reflect.ValueOf(cl.InitTrialInfo),
		"InitTrialOnHook":                                             reflect.ValueOf(cl.InitTrialOnHook),
		"InitUser":                                                    reflect.ValueOf(cl.InitUser),
		"InitUserBattleData":                                          reflect.ValueOf(cl.InitUserBattleData),
		"InitUserChatHead":                                            reflect.ValueOf(cl.InitUserChatHead),
		"InitUserGuild":                                               reflect.ValueOf(cl.InitUserGuild),
		"InitUserGuildChestItem":                                      reflect.ValueOf(cl.InitUserGuildChestItem),
		"InitUserSnapshot":                                            reflect.ValueOf(cl.InitUserSnapshot),
		"InitViewLinks":                                               reflect.ValueOf(cl.InitViewLinks),
		"InitVipInfo":                                                 reflect.ValueOf(cl.InitVipInfo),
		"InitWeeklyTask":                                              reflect.ValueOf(cl.InitWeeklyTask),
		"InitWishInfo":                                                reflect.ValueOf(cl.InitWishInfo),
		"InitWishListMes":                                             reflect.ValueOf(cl.InitWishListMes),
		"InitWorldBoss":                                               reflect.ValueOf(cl.InitWorldBoss),
		"InitWorldBossData":                                           reflect.ValueOf(cl.InitWorldBossData),
		"InitWorldBossRoom":                                           reflect.ValueOf(cl.InitWorldBossRoom),
		"InitWorldBossRoomLog":                                        reflect.ValueOf(cl.InitWorldBossRoomLog),
		"InitWorldBossTask":                                           reflect.ValueOf(cl.InitWorldBossTask),
		"InitWrestleLog":                                              reflect.ValueOf(cl.InitWrestleLog),
		"InitWrestleLogDeletes":                                       reflect.ValueOf(cl.InitWrestleLogDeletes),
		"InitWrestleLogs":                                             reflect.ValueOf(cl.InitWrestleLogs),
		"InitWrestleOpponentInfo":                                     reflect.ValueOf(cl.InitWrestleOpponentInfo),
		"InitWrestleUserScoreAndRank":                                 reflect.ValueOf(cl.InitWrestleUserScoreAndRank),
		"OreLogType_OreLogType_Fight":                                 reflect.ValueOf(cl.OreLogType_OreLogType_Fight),
		"OreLogType_OreLogType_Ground":                                reflect.ValueOf(cl.OreLogType_OreLogType_Ground),
		"OreLogType_OreLogType_None":                                  reflect.ValueOf(cl.OreLogType_OreLogType_None),
		"OreLogType_OreLogType_Tech":                                  reflect.ValueOf(cl.OreLogType_OreLogType_Tech),
		"OreLogType_name":                                             reflect.ValueOf(&cl.OreLogType_name).Elem(),
		"OreLogType_value":                                            reflect.ValueOf(&cl.OreLogType_value).Elem(),
		"SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_Diamond":     reflect.ValueOf(cl.SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_Diamond),
		"SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_Item":        reflect.ValueOf(cl.SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_Item),
		"SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_None":        reflect.ValueOf(cl.SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_None),
		"SeasonMapBuyStaminaType_name":                                reflect.ValueOf(&cl.SeasonMapBuyStaminaType_name).Elem(),
		"SeasonMapBuyStaminaType_value":                               reflect.ValueOf(&cl.SeasonMapBuyStaminaType_value).Elem(),
		"SeasonMapEventType_EventType_Altar":                          reflect.ValueOf(cl.SeasonMapEventType_EventType_Altar),
		"SeasonMapEventType_EventType_Connect":                        reflect.ValueOf(cl.SeasonMapEventType_EventType_Connect),
		"SeasonMapEventType_EventType_Dialogue":                       reflect.ValueOf(cl.SeasonMapEventType_EventType_Dialogue),
		"SeasonMapEventType_EventType_Master":                         reflect.ValueOf(cl.SeasonMapEventType_EventType_Master),
		"SeasonMapEventType_EventType_None":                           reflect.ValueOf(cl.SeasonMapEventType_EventType_None),
		"SeasonMapEventType_EventType_Trade":                          reflect.ValueOf(cl.SeasonMapEventType_EventType_Trade),
		"SeasonMapEventType_name":                                     reflect.ValueOf(&cl.SeasonMapEventType_name).Elem(),
		"SeasonMapEventType_value":                                    reflect.ValueOf(&cl.SeasonMapEventType_value).Elem(),
		"SeasonMapPriceChangeType_SeasonMapPriceChangeType_Down_Pct":  reflect.ValueOf(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_Down_Pct),
		"SeasonMapPriceChangeType_SeasonMapPriceChangeType_None":      reflect.ValueOf(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_None),
		"SeasonMapPriceChangeType_SeasonMapPriceChangeType_Up_Pct":    reflect.ValueOf(cl.SeasonMapPriceChangeType_SeasonMapPriceChangeType_Up_Pct),
		"SeasonMapPriceChangeType_name":                               reflect.ValueOf(&cl.SeasonMapPriceChangeType_name).Elem(),
		"SeasonMapPriceChangeType_value":                              reflect.ValueOf(&cl.SeasonMapPriceChangeType_value).Elem(),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_Altar":           reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Altar),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_Buy":             reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Buy),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_BuyX":            reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_BuyX),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_Fight":           reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Fight),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_Master":          reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Master),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_None":            reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_None),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_Sell":            reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_Sell),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_SellX":           reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_SellX),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_System":          reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_System),
		"SeasonMapPriceLogType_SeasonMapPriceLogType_System_Reset":    reflect.ValueOf(cl.SeasonMapPriceLogType_SeasonMapPriceLogType_System_Reset),
		"SeasonMapPriceLogType_name":                                  reflect.ValueOf(&cl.SeasonMapPriceLogType_name).Elem(),
		"SeasonMapPriceLogType_value":                                 reflect.ValueOf(&cl.SeasonMapPriceLogType_value).Elem(),
		"SeasonMapRecoverType_SeasonMapRecoverType_Currency":          reflect.ValueOf(cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency),
		"SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina":     reflect.ValueOf(cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina),
		"SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina":     reflect.ValueOf(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina),
		"SeasonMapRecoverType_SeasonMapRecoverType_None":              reflect.ValueOf(cl.SeasonMapRecoverType_SeasonMapRecoverType_None),
		"SeasonMapRecoverType_name":                                   reflect.ValueOf(&cl.SeasonMapRecoverType_name).Elem(),
		"SeasonMapRecoverType_value":                                  reflect.ValueOf(&cl.SeasonMapRecoverType_value).Elem(),
		"SeasonMapTradeType_TradeType_Buy":                            reflect.ValueOf(cl.SeasonMapTradeType_TradeType_Buy),
		"SeasonMapTradeType_TradeType_None":                           reflect.ValueOf(cl.SeasonMapTradeType_TradeType_None),
		"SeasonMapTradeType_TradeType_Sell":                           reflect.ValueOf(cl.SeasonMapTradeType_TradeType_Sell),
		"SeasonMapTradeType_name":                                     reflect.ValueOf(&cl.SeasonMapTradeType_name).Elem(),
		"SeasonMapTradeType_value":                                    reflect.ValueOf(&cl.SeasonMapTradeType_value).Elem(),

		// type definitions
		"AchieveInfo":                               reflect.ValueOf((*cl.AchieveInfo)(nil)),
		"AchieveTask":                               reflect.ValueOf((*cl.AchieveTask)(nil)),
		"AchievementsShowcase":                      reflect.ValueOf((*cl.AchievementsShowcase)(nil)),
		"ActivityCompliance":                        reflect.ValueOf((*cl.ActivityCompliance)(nil)),
		"ActivityCoupon":                            reflect.ValueOf((*cl.ActivityCoupon)(nil)),
		"ActivityCouponRes":                         reflect.ValueOf((*cl.ActivityCouponRes)(nil)),
		"ActivityCouponXml":                         reflect.ValueOf((*cl.ActivityCouponXml)(nil)),
		"ActivityDrop":                              reflect.ValueOf((*cl.ActivityDrop)(nil)),
		"ActivityExchange":                          reflect.ValueOf((*cl.ActivityExchange)(nil)),
		"ActivityFeed":                              reflect.ValueOf((*cl.ActivityFeed)(nil)),
		"ActivityLifelongGift":                      reflect.ValueOf((*cl.ActivityLifelongGift)(nil)),
		"ActivityLifelongGifts":                     reflect.ValueOf((*cl.ActivityLifelongGifts)(nil)),
		"ActivityMix":                               reflect.ValueOf((*cl.ActivityMix)(nil)),
		"ActivityPuzzle":                            reflect.ValueOf((*cl.ActivityPuzzle)(nil)),
		"ActivityRecharge":                          reflect.ValueOf((*cl.ActivityRecharge)(nil)),
		"ActivityRechargeShop":                      reflect.ValueOf((*cl.ActivityRechargeShop)(nil)),
		"ActivityReturn":                            reflect.ValueOf((*cl.ActivityReturn)(nil)),
		"ActivityReturnLogin":                       reflect.ValueOf((*cl.ActivityReturnLogin)(nil)),
		"ActivityScheduleData":                      reflect.ValueOf((*cl.ActivityScheduleData)(nil)),
		"ActivityScheduleDatas":                     reflect.ValueOf((*cl.ActivityScheduleDatas)(nil)),
		"ActivityShoot":                             reflect.ValueOf((*cl.ActivityShoot)(nil)),
		"ActivityStory":                             reflect.ValueOf((*cl.ActivityStory)(nil)),
		"ActivityStoryExchange":                     reflect.ValueOf((*cl.ActivityStoryExchange)(nil)),
		"ActivitySubContinueLogin":                  reflect.ValueOf((*cl.ActivitySubContinueLogin)(nil)),
		"ActivitySubExchange":                       reflect.ValueOf((*cl.ActivitySubExchange)(nil)),
		"ActivitySubFeed":                           reflect.ValueOf((*cl.ActivitySubFeed)(nil)),
		"ActivitySubPuzzle":                         reflect.ValueOf((*cl.ActivitySubPuzzle)(nil)),
		"ActivitySubShoot":                          reflect.ValueOf((*cl.ActivitySubShoot)(nil)),
		"ActivitySubTask":                           reflect.ValueOf((*cl.ActivitySubTask)(nil)),
		"ActivitySubTurnTable":                      reflect.ValueOf((*cl.ActivitySubTurnTable)(nil)),
		"ActivitySum":                               reflect.ValueOf((*cl.ActivitySum)(nil)),
		"ActivitySumClient":                         reflect.ValueOf((*cl.ActivitySumClient)(nil)),
		"ActivitySummonGuarantee":                   reflect.ValueOf((*cl.ActivitySummonGuarantee)(nil)),
		"ActivityTurnTable":                         reflect.ValueOf((*cl.ActivityTurnTable)(nil)),
		"ActivityWebData":                           reflect.ValueOf((*cl.ActivityWebData)(nil)),
		"ActivityWebDatas":                          reflect.ValueOf((*cl.ActivityWebDatas)(nil)),
		"AltPassives":                               reflect.ValueOf((*cl.AltPassives)(nil)),
		"Announcement":                              reflect.ValueOf((*cl.Announcement)(nil)),
		"Arena":                                     reflect.ValueOf((*cl.Arena)(nil)),
		"ArenaLastSeasonTop":                        reflect.ValueOf((*cl.ArenaLastSeasonTop)(nil)),
		"ArenaLog":                                  reflect.ValueOf((*cl.ArenaLog)(nil)),
		"ArenaLogDeletes":                           reflect.ValueOf((*cl.ArenaLogDeletes)(nil)),
		"ArenaLogs":                                 reflect.ValueOf((*cl.ArenaLogs)(nil)),
		"ArenaRankInfo":                             reflect.ValueOf((*cl.ArenaRankInfo)(nil)),
		"ArenaTask":                                 reflect.ValueOf((*cl.ArenaTask)(nil)),
		"ArenaUserInfo":                             reflect.ValueOf((*cl.ArenaUserInfo)(nil)),
		"ArenaUserScoreAndRank":                     reflect.ValueOf((*cl.ArenaUserScoreAndRank)(nil)),
		"Artifact":                                  reflect.ValueOf((*cl.Artifact)(nil)),
		"ArtifactDebut":                             reflect.ValueOf((*cl.ArtifactDebut)(nil)),
		"ArtifactDebutBase":                         reflect.ValueOf((*cl.ArtifactDebutBase)(nil)),
		"ArtifactDebutGuarantee":                    reflect.ValueOf((*cl.ArtifactDebutGuarantee)(nil)),
		"ArtifactDebutLoginAct":                     reflect.ValueOf((*cl.ArtifactDebutLoginAct)(nil)),
		"ArtifactDebutPuzzleAct":                    reflect.ValueOf((*cl.ArtifactDebutPuzzleAct)(nil)),
		"ArtifactDebutSummon":                       reflect.ValueOf((*cl.ArtifactDebutSummon)(nil)),
		"ArtifactDebutSummonAct":                    reflect.ValueOf((*cl.ArtifactDebutSummonAct)(nil)),
		"ArtifactDebutTask":                         reflect.ValueOf((*cl.ArtifactDebutTask)(nil)),
		"AssistanceActivity":                        reflect.ValueOf((*cl.AssistanceActivity)(nil)),
		"AssistantData":                             reflect.ValueOf((*cl.AssistantData)(nil)),
		"Attr":                                      reflect.ValueOf((*cl.Attr)(nil)),
		"AttrInfo":                                  reflect.ValueOf((*cl.AttrInfo)(nil)),
		"Avatar":                                    reflect.ValueOf((*cl.Avatar)(nil)),
		"BanInfo":                                   reflect.ValueOf((*cl.BanInfo)(nil)),
		"BanUsers":                                  reflect.ValueOf((*cl.BanUsers)(nil)),
		"BaseGrid":                                  reflect.ValueOf((*cl.BaseGrid)(nil)),
		"BattleBots":                                reflect.ValueOf((*cl.BattleBots)(nil)),
		"BossRush":                                  reflect.ValueOf((*cl.BossRush)(nil)),
		"BossRushBoss":                              reflect.ValueOf((*cl.BossRushBoss)(nil)),
		"BossRushMaxRecord":                         reflect.ValueOf((*cl.BossRushMaxRecord)(nil)),
		"BossRushTeamData":                          reflect.ValueOf((*cl.BossRushTeamData)(nil)),
		"BotData":                                   reflect.ValueOf((*cl.BotData)(nil)),
		"BotEmblem":                                 reflect.ValueOf((*cl.BotEmblem)(nil)),
		"BotEquip":                                  reflect.ValueOf((*cl.BotEquip)(nil)),
		"BotGem":                                    reflect.ValueOf((*cl.BotGem)(nil)),
		"BotHero":                                   reflect.ValueOf((*cl.BotHero)(nil)),
		"BoxOpenInfo":                               reflect.ValueOf((*cl.BoxOpenInfo)(nil)),
		"C2L_Accusation":                            reflect.ValueOf((*cl.C2L_Accusation)(nil)),
		"C2L_ActivityComplianceGetData":             reflect.ValueOf((*cl.C2L_ActivityComplianceGetData)(nil)),
		"C2L_ActivityComplianceLikeRank":            reflect.ValueOf((*cl.C2L_ActivityComplianceLikeRank)(nil)),
		"C2L_ActivityComplianceRecvAward":           reflect.ValueOf((*cl.C2L_ActivityComplianceRecvAward)(nil)),
		"C2L_ActivityCouponBuy":                     reflect.ValueOf((*cl.C2L_ActivityCouponBuy)(nil)),
		"C2L_ActivityCouponGetData":                 reflect.ValueOf((*cl.C2L_ActivityCouponGetData)(nil)),
		"C2L_ActivityGoddessActive":                 reflect.ValueOf((*cl.C2L_ActivityGoddessActive)(nil)),
		"C2L_ActivityGoddessRecvRecoveryAward":      reflect.ValueOf((*cl.C2L_ActivityGoddessRecvRecoveryAward)(nil)),
		"C2L_ActivityMixGetData":                    reflect.ValueOf((*cl.C2L_ActivityMixGetData)(nil)),
		"C2L_ActivityRechargeBuy":                   reflect.ValueOf((*cl.C2L_ActivityRechargeBuy)(nil)),
		"C2L_ActivityRechargeGet":                   reflect.ValueOf((*cl.C2L_ActivityRechargeGet)(nil)),
		"C2L_ActivityReturnGetData":                 reflect.ValueOf((*cl.C2L_ActivityReturnGetData)(nil)),
		"C2L_ActivityReturnTakeLoginAwards":         reflect.ValueOf((*cl.C2L_ActivityReturnTakeLoginAwards)(nil)),
		"C2L_ActivityScheduleGetData":               reflect.ValueOf((*cl.C2L_ActivityScheduleGetData)(nil)),
		"C2L_ActivityStoryExchange":                 reflect.ValueOf((*cl.C2L_ActivityStoryExchange)(nil)),
		"C2L_ActivityStoryFight":                    reflect.ValueOf((*cl.C2L_ActivityStoryFight)(nil)),
		"C2L_ActivityStoryGetData":                  reflect.ValueOf((*cl.C2L_ActivityStoryGetData)(nil)),
		"C2L_ActivityStoryLoginAward":               reflect.ValueOf((*cl.C2L_ActivityStoryLoginAward)(nil)),
		"C2L_ActivitySumActivePuzzleCell":           reflect.ValueOf((*cl.C2L_ActivitySumActivePuzzleCell)(nil)),
		"C2L_ActivitySumExchange":                   reflect.ValueOf((*cl.C2L_ActivitySumExchange)(nil)),
		"C2L_ActivitySumFeed":                       reflect.ValueOf((*cl.C2L_ActivitySumFeed)(nil)),
		"C2L_ActivitySumGetData":                    reflect.ValueOf((*cl.C2L_ActivitySumGetData)(nil)),
		"C2L_ActivitySumLoginReward":                reflect.ValueOf((*cl.C2L_ActivitySumLoginReward)(nil)),
		"C2L_ActivitySumMakeGift":                   reflect.ValueOf((*cl.C2L_ActivitySumMakeGift)(nil)),
		"C2L_ActivitySumShootGameFight":             reflect.ValueOf((*cl.C2L_ActivitySumShootGameFight)(nil)),
		"C2L_ActivitySumTaskReward":                 reflect.ValueOf((*cl.C2L_ActivitySumTaskReward)(nil)),
		"C2L_ActivitySumTicketBuy":                  reflect.ValueOf((*cl.C2L_ActivitySumTicketBuy)(nil)),
		"C2L_ActivitySumTurnTableSelectBuff":        reflect.ValueOf((*cl.C2L_ActivitySumTurnTableSelectBuff)(nil)),
		"C2L_ActivitySumTurnTableSummon":            reflect.ValueOf((*cl.C2L_ActivitySumTurnTableSummon)(nil)),
		"C2L_ActivityTurnTableGetData":              reflect.ValueOf((*cl.C2L_ActivityTurnTableGetData)(nil)),
		"C2L_ActivityTurnTableRecvLogin":            reflect.ValueOf((*cl.C2L_ActivityTurnTableRecvLogin)(nil)),
		"C2L_ActivityTurnTableRecvTask":             reflect.ValueOf((*cl.C2L_ActivityTurnTableRecvTask)(nil)),
		"C2L_ActivityTurnTableSelectBuff":           reflect.ValueOf((*cl.C2L_ActivityTurnTableSelectBuff)(nil)),
		"C2L_ActivityTurnTableSummon":               reflect.ValueOf((*cl.C2L_ActivityTurnTableSummon)(nil)),
		"C2L_ActivityTurnTableTicketBuy":            reflect.ValueOf((*cl.C2L_ActivityTurnTableTicketBuy)(nil)),
		"C2L_AddPurchaseNum":                        reflect.ValueOf((*cl.C2L_AddPurchaseNum)(nil)),
		"C2L_AnnouncementGetData":                   reflect.ValueOf((*cl.C2L_AnnouncementGetData)(nil)),
		"C2L_ArenaFight":                            reflect.ValueOf((*cl.C2L_ArenaFight)(nil)),
		"C2L_ArenaInfo":                             reflect.ValueOf((*cl.C2L_ArenaInfo)(nil)),
		"C2L_ArenaLike":                             reflect.ValueOf((*cl.C2L_ArenaLike)(nil)),
		"C2L_ArenaLogList":                          reflect.ValueOf((*cl.C2L_ArenaLogList)(nil)),
		"C2L_ArenaRank":                             reflect.ValueOf((*cl.C2L_ArenaRank)(nil)),
		"C2L_ArenaRecvAward":                        reflect.ValueOf((*cl.C2L_ArenaRecvAward)(nil)),
		"C2L_ArenaRefresh":                          reflect.ValueOf((*cl.C2L_ArenaRefresh)(nil)),
		"C2L_ArtifactActivate":                      reflect.ValueOf((*cl.C2L_ArtifactActivate)(nil)),
		"C2L_ArtifactDebutGetActivity":              reflect.ValueOf((*cl.C2L_ArtifactDebutGetActivity)(nil)),
		"C2L_ArtifactDebutMainInfo":                 reflect.ValueOf((*cl.C2L_ArtifactDebutMainInfo)(nil)),
		"C2L_ArtifactDebutOpenPuzzle":               reflect.ValueOf((*cl.C2L_ArtifactDebutOpenPuzzle)(nil)),
		"C2L_ArtifactDebutRecvActAward":             reflect.ValueOf((*cl.C2L_ArtifactDebutRecvActAward)(nil)),
		"C2L_ArtifactDebutRecvTaskAward":            reflect.ValueOf((*cl.C2L_ArtifactDebutRecvTaskAward)(nil)),
		"C2L_ArtifactDebutSetWish":                  reflect.ValueOf((*cl.C2L_ArtifactDebutSetWish)(nil)),
		"C2L_ArtifactDebutSummon":                   reflect.ValueOf((*cl.C2L_ArtifactDebutSummon)(nil)),
		"C2L_ArtifactDebutTestSummon":               reflect.ValueOf((*cl.C2L_ArtifactDebutTestSummon)(nil)),
		"C2L_ArtifactForge":                         reflect.ValueOf((*cl.C2L_ArtifactForge)(nil)),
		"C2L_ArtifactList":                          reflect.ValueOf((*cl.C2L_ArtifactList)(nil)),
		"C2L_ArtifactRevive":                        reflect.ValueOf((*cl.C2L_ArtifactRevive)(nil)),
		"C2L_ArtifactStarUp":                        reflect.ValueOf((*cl.C2L_ArtifactStarUp)(nil)),
		"C2L_ArtifactStrength":                      reflect.ValueOf((*cl.C2L_ArtifactStrength)(nil)),
		"C2L_AssistanceActivityGetAward":            reflect.ValueOf((*cl.C2L_AssistanceActivityGetAward)(nil)),
		"C2L_AssistanceActivityGetData":             reflect.ValueOf((*cl.C2L_AssistanceActivityGetData)(nil)),
		"C2L_AssistantGetData":                      reflect.ValueOf((*cl.C2L_AssistantGetData)(nil)),
		"C2L_AvatarGetInfo":                         reflect.ValueOf((*cl.C2L_AvatarGetInfo)(nil)),
		"C2L_AvatarSetIcon":                         reflect.ValueOf((*cl.C2L_AvatarSetIcon)(nil)),
		"C2L_BattleTest":                            reflect.ValueOf((*cl.C2L_BattleTest)(nil)),
		"C2L_BossRushBuyStamina":                    reflect.ValueOf((*cl.C2L_BossRushBuyStamina)(nil)),
		"C2L_BossRushFight":                         reflect.ValueOf((*cl.C2L_BossRushFight)(nil)),
		"C2L_BossRushGetData":                       reflect.ValueOf((*cl.C2L_BossRushGetData)(nil)),
		"C2L_BossRushTaskAward":                     reflect.ValueOf((*cl.C2L_BossRushTaskAward)(nil)),
		"C2L_BoxExchange":                           reflect.ValueOf((*cl.C2L_BoxExchange)(nil)),
		"C2L_BoxGet":                                reflect.ValueOf((*cl.C2L_BoxGet)(nil)),
		"C2L_BoxOpen":                               reflect.ValueOf((*cl.C2L_BoxOpen)(nil)),
		"C2L_CarnivalGetData":                       reflect.ValueOf((*cl.C2L_CarnivalGetData)(nil)),
		"C2L_CarnivalReceiveAward":                  reflect.ValueOf((*cl.C2L_CarnivalReceiveAward)(nil)),
		"C2L_Chat":                                  reflect.ValueOf((*cl.C2L_Chat)(nil)),
		"C2L_ChatCostShareCount":                    reflect.ValueOf((*cl.C2L_ChatCostShareCount)(nil)),
		"C2L_ChatGetLikeList":                       reflect.ValueOf((*cl.C2L_ChatGetLikeList)(nil)),
		"C2L_ChatGetMessages":                       reflect.ValueOf((*cl.C2L_ChatGetMessages)(nil)),
		"C2L_ChatGetPrivateMessageNum":              reflect.ValueOf((*cl.C2L_ChatGetPrivateMessageNum)(nil)),
		"C2L_ChatGetToken":                          reflect.ValueOf((*cl.C2L_ChatGetToken)(nil)),
		"C2L_ChatLike":                              reflect.ValueOf((*cl.C2L_ChatLike)(nil)),
		"C2L_ChatSyncChatTag":                       reflect.ValueOf((*cl.C2L_ChatSyncChatTag)(nil)),
		"C2L_ClientGetMultiLang":                    reflect.ValueOf((*cl.C2L_ClientGetMultiLang)(nil)),
		"C2L_ClientSetMultiLang":                    reflect.ValueOf((*cl.C2L_ClientSetMultiLang)(nil)),
		"C2L_CommonRankLike":                        reflect.ValueOf((*cl.C2L_CommonRankLike)(nil)),
		"C2L_ComplianceTasksGetData":                reflect.ValueOf((*cl.C2L_ComplianceTasksGetData)(nil)),
		"C2L_ComplianceTasksRecvTask":               reflect.ValueOf((*cl.C2L_ComplianceTasksRecvTask)(nil)),
		"C2L_CrystalActiveAchievement":              reflect.ValueOf((*cl.C2L_CrystalActiveAchievement)(nil)),
		"C2L_CrystalAddHero":                        reflect.ValueOf((*cl.C2L_CrystalAddHero)(nil)),
		"C2L_CrystalGetAllData":                     reflect.ValueOf((*cl.C2L_CrystalGetAllData)(nil)),
		"C2L_CrystalRemoveHero":                     reflect.ValueOf((*cl.C2L_CrystalRemoveHero)(nil)),
		"C2L_CrystalSpeedSlotCD":                    reflect.ValueOf((*cl.C2L_CrystalSpeedSlotCD)(nil)),
		"C2L_CrystalUnlockSlot":                     reflect.ValueOf((*cl.C2L_CrystalUnlockSlot)(nil)),
		"C2L_DailyAttendanceGetData":                reflect.ValueOf((*cl.C2L_DailyAttendanceGetData)(nil)),
		"C2L_DailyAttendanceHeroGetData":            reflect.ValueOf((*cl.C2L_DailyAttendanceHeroGetData)(nil)),
		"C2L_DailyAttendanceHeroRecvAward":          reflect.ValueOf((*cl.C2L_DailyAttendanceHeroRecvAward)(nil)),
		"C2L_DailyAttendanceRecvAward":              reflect.ValueOf((*cl.C2L_DailyAttendanceRecvAward)(nil)),
		"C2L_DailySpecialGetData":                   reflect.ValueOf((*cl.C2L_DailySpecialGetData)(nil)),
		"C2L_DailySpecialRecvAward":                 reflect.ValueOf((*cl.C2L_DailySpecialRecvAward)(nil)),
		"C2L_DailyWishGet":                          reflect.ValueOf((*cl.C2L_DailyWishGet)(nil)),
		"C2L_DailyWishSummon":                       reflect.ValueOf((*cl.C2L_DailyWishSummon)(nil)),
		"C2L_DailyWishXmlGet":                       reflect.ValueOf((*cl.C2L_DailyWishXmlGet)(nil)),
		"C2L_DeleteMails":                           reflect.ValueOf((*cl.C2L_DeleteMails)(nil)),
		"C2L_DisorderlandBuyStamina":                reflect.ValueOf((*cl.C2L_DisorderlandBuyStamina)(nil)),
		"C2L_DisorderlandGetData":                   reflect.ValueOf((*cl.C2L_DisorderlandGetData)(nil)),
		"C2L_DisorderlandRank":                      reflect.ValueOf((*cl.C2L_DisorderlandRank)(nil)),
		"C2L_DisorderlandTestSweep":                 reflect.ValueOf((*cl.C2L_DisorderlandTestSweep)(nil)),
		"C2L_DisorderlandTriggerEvent":              reflect.ValueOf((*cl.C2L_DisorderlandTriggerEvent)(nil)),
		"C2L_DispatchGetAwards":                     reflect.ValueOf((*cl.C2L_DispatchGetAwards)(nil)),
		"C2L_DispatchReceiveTask":                   reflect.ValueOf((*cl.C2L_DispatchReceiveTask)(nil)),
		"C2L_DispatchRefreshTask":                   reflect.ValueOf((*cl.C2L_DispatchRefreshTask)(nil)),
		"C2L_DispatchTasks":                         reflect.ValueOf((*cl.C2L_DispatchTasks)(nil)),
		"C2L_DivineDemonGetOpenActivity":            reflect.ValueOf((*cl.C2L_DivineDemonGetOpenActivity)(nil)),
		"C2L_DivineDemonReceiveTaskAward":           reflect.ValueOf((*cl.C2L_DivineDemonReceiveTaskAward)(nil)),
		"C2L_DivineDemonSummon":                     reflect.ValueOf((*cl.C2L_DivineDemonSummon)(nil)),
		"C2L_DrawMails":                             reflect.ValueOf((*cl.C2L_DrawMails)(nil)),
		"C2L_DropActivityDailyReward":               reflect.ValueOf((*cl.C2L_DropActivityDailyReward)(nil)),
		"C2L_DropActivityExchange":                  reflect.ValueOf((*cl.C2L_DropActivityExchange)(nil)),
		"C2L_DropActivityGetActivity":               reflect.ValueOf((*cl.C2L_DropActivityGetActivity)(nil)),
		"C2L_DropActivityMainInfo":                  reflect.ValueOf((*cl.C2L_DropActivityMainInfo)(nil)),
		"C2L_Duel":                                  reflect.ValueOf((*cl.C2L_Duel)(nil)),
		"C2L_DuelFight":                             reflect.ValueOf((*cl.C2L_DuelFight)(nil)),
		"C2L_DuelSetStatus":                         reflect.ValueOf((*cl.C2L_DuelSetStatus)(nil)),
		"C2L_Dungeon":                               reflect.ValueOf((*cl.C2L_Dungeon)(nil)),
		"C2L_DungeonFight":                          reflect.ValueOf((*cl.C2L_DungeonFight)(nil)),
		"C2L_DungeonPreview":                        reflect.ValueOf((*cl.C2L_DungeonPreview)(nil)),
		"C2L_DungeonRecvAward":                      reflect.ValueOf((*cl.C2L_DungeonRecvAward)(nil)),
		"C2L_DungeonSpeedRecvAward":                 reflect.ValueOf((*cl.C2L_DungeonSpeedRecvAward)(nil)),
		"C2L_EmblemBuySlot":                         reflect.ValueOf((*cl.C2L_EmblemBuySlot)(nil)),
		"C2L_EmblemCustomize":                       reflect.ValueOf((*cl.C2L_EmblemCustomize)(nil)),
		"C2L_EmblemDecompose":                       reflect.ValueOf((*cl.C2L_EmblemDecompose)(nil)),
		"C2L_EmblemGet":                             reflect.ValueOf((*cl.C2L_EmblemGet)(nil)),
		"C2L_EmblemGrowTransfer":                    reflect.ValueOf((*cl.C2L_EmblemGrowTransfer)(nil)),
		"C2L_EmblemLevelUp":                         reflect.ValueOf((*cl.C2L_EmblemLevelUp)(nil)),
		"C2L_EmblemSetAutoDecompose":                reflect.ValueOf((*cl.C2L_EmblemSetAutoDecompose)(nil)),
		"C2L_EmblemSuccinct":                        reflect.ValueOf((*cl.C2L_EmblemSuccinct)(nil)),
		"C2L_EmblemSuccinctItemConflate":            reflect.ValueOf((*cl.C2L_EmblemSuccinctItemConflate)(nil)),
		"C2L_EmblemSuccinctLockOrSave":              reflect.ValueOf((*cl.C2L_EmblemSuccinctLockOrSave)(nil)),
		"C2L_EmblemUpgrade":                         reflect.ValueOf((*cl.C2L_EmblemUpgrade)(nil)),
		"C2L_EmblemWear":                            reflect.ValueOf((*cl.C2L_EmblemWear)(nil)),
		"C2L_EquipDecompose":                        reflect.ValueOf((*cl.C2L_EquipDecompose)(nil)),
		"C2L_EquipEnchant":                          reflect.ValueOf((*cl.C2L_EquipEnchant)(nil)),
		"C2L_EquipEvolution":                        reflect.ValueOf((*cl.C2L_EquipEvolution)(nil)),
		"C2L_EquipGet":                              reflect.ValueOf((*cl.C2L_EquipGet)(nil)),
		"C2L_EquipGrowTransfer":                     reflect.ValueOf((*cl.C2L_EquipGrowTransfer)(nil)),
		"C2L_EquipMultipleStrength":                 reflect.ValueOf((*cl.C2L_EquipMultipleStrength)(nil)),
		"C2L_EquipRefine":                           reflect.ValueOf((*cl.C2L_EquipRefine)(nil)),
		"C2L_EquipRevive":                           reflect.ValueOf((*cl.C2L_EquipRevive)(nil)),
		"C2L_EquipSetAutoDecompose":                 reflect.ValueOf((*cl.C2L_EquipSetAutoDecompose)(nil)),
		"C2L_EquipStrength":                         reflect.ValueOf((*cl.C2L_EquipStrength)(nil)),
		"C2L_EquipWear":                             reflect.ValueOf((*cl.C2L_EquipWear)(nil)),
		"C2L_FlowerAssistRecvLike":                  reflect.ValueOf((*cl.C2L_FlowerAssistRecvLike)(nil)),
		"C2L_FlowerAssistSendLike":                  reflect.ValueOf((*cl.C2L_FlowerAssistSendLike)(nil)),
		"C2L_FlowerAttackLevelGuard":                reflect.ValueOf((*cl.C2L_FlowerAttackLevelGuard)(nil)),
		"C2L_FlowerBuyOccupyAttackNum":              reflect.ValueOf((*cl.C2L_FlowerBuyOccupyAttackNum)(nil)),
		"C2L_FlowerChangeGoblin":                    reflect.ValueOf((*cl.C2L_FlowerChangeGoblin)(nil)),
		"C2L_FlowerEnemiesInfo":                     reflect.ValueOf((*cl.C2L_FlowerEnemiesInfo)(nil)),
		"C2L_FlowerExtendOccupyTime":                reflect.ValueOf((*cl.C2L_FlowerExtendOccupyTime)(nil)),
		"C2L_FlowerFeedGoblin":                      reflect.ValueOf((*cl.C2L_FlowerFeedGoblin)(nil)),
		"C2L_FlowerFeedSpecial":                     reflect.ValueOf((*cl.C2L_FlowerFeedSpecial)(nil)),
		"C2L_FlowerHarvest":                         reflect.ValueOf((*cl.C2L_FlowerHarvest)(nil)),
		"C2L_FlowerJungleInfo":                      reflect.ValueOf((*cl.C2L_FlowerJungleInfo)(nil)),
		"C2L_FlowerLevelUp":                         reflect.ValueOf((*cl.C2L_FlowerLevelUp)(nil)),
		"C2L_FlowerLogList":                         reflect.ValueOf((*cl.C2L_FlowerLogList)(nil)),
		"C2L_FlowerMainInfo":                        reflect.ValueOf((*cl.C2L_FlowerMainInfo)(nil)),
		"C2L_FlowerOccupyAssistList":                reflect.ValueOf((*cl.C2L_FlowerOccupyAssistList)(nil)),
		"C2L_FlowerOccupyAttack":                    reflect.ValueOf((*cl.C2L_FlowerOccupyAttack)(nil)),
		"C2L_FlowerOccupyHistory":                   reflect.ValueOf((*cl.C2L_FlowerOccupyHistory)(nil)),
		"C2L_FlowerOccupyRecommend":                 reflect.ValueOf((*cl.C2L_FlowerOccupyRecommend)(nil)),
		"C2L_FlowerPreviewOccupyAward":              reflect.ValueOf((*cl.C2L_FlowerPreviewOccupyAward)(nil)),
		"C2L_FlowerRecvOccupyAward":                 reflect.ValueOf((*cl.C2L_FlowerRecvOccupyAward)(nil)),
		"C2L_FlowerRecvPreviewOccupyAward":          reflect.ValueOf((*cl.C2L_FlowerRecvPreviewOccupyAward)(nil)),
		"C2L_FlowerRevengeOccupy":                   reflect.ValueOf((*cl.C2L_FlowerRevengeOccupy)(nil)),
		"C2L_FlowerRevengeSnatch":                   reflect.ValueOf((*cl.C2L_FlowerRevengeSnatch)(nil)),
		"C2L_FlowerSearch":                          reflect.ValueOf((*cl.C2L_FlowerSearch)(nil)),
		"C2L_FlowerShareFlowerbed":                  reflect.ValueOf((*cl.C2L_FlowerShareFlowerbed)(nil)),
		"C2L_FlowerSnatch":                          reflect.ValueOf((*cl.C2L_FlowerSnatch)(nil)),
		"C2L_FlowerSpeedGrow":                       reflect.ValueOf((*cl.C2L_FlowerSpeedGrow)(nil)),
		"C2L_FlowerStartFeed":                       reflect.ValueOf((*cl.C2L_FlowerStartFeed)(nil)),
		"C2L_FlowerStartPlant":                      reflect.ValueOf((*cl.C2L_FlowerStartPlant)(nil)),
		"C2L_FlowerTimberInfo":                      reflect.ValueOf((*cl.C2L_FlowerTimberInfo)(nil)),
		"C2L_Flush":                                 reflect.ValueOf((*cl.C2L_Flush)(nil)),
		"C2L_FlushRedPoint":                         reflect.ValueOf((*cl.C2L_FlushRedPoint)(nil)),
		"C2L_ForecastGetData":                       reflect.ValueOf((*cl.C2L_ForecastGetData)(nil)),
		"C2L_ForecastReceiveAward":                  reflect.ValueOf((*cl.C2L_ForecastReceiveAward)(nil)),
		"C2L_ForestChangeGoblin":                    reflect.ValueOf((*cl.C2L_ForestChangeGoblin)(nil)),
		"C2L_ForestFeedGoblin":                      reflect.ValueOf((*cl.C2L_ForestFeedGoblin)(nil)),
		"C2L_ForestFeedSpecial":                     reflect.ValueOf((*cl.C2L_ForestFeedSpecial)(nil)),
		"C2L_ForestHarvest":                         reflect.ValueOf((*cl.C2L_ForestHarvest)(nil)),
		"C2L_ForestInfo":                            reflect.ValueOf((*cl.C2L_ForestInfo)(nil)),
		"C2L_ForestLogList":                         reflect.ValueOf((*cl.C2L_ForestLogList)(nil)),
		"C2L_ForestLoot":                            reflect.ValueOf((*cl.C2L_ForestLoot)(nil)),
		"C2L_ForestRecvLvAward":                     reflect.ValueOf((*cl.C2L_ForestRecvLvAward)(nil)),
		"C2L_ForestRevenge":                         reflect.ValueOf((*cl.C2L_ForestRevenge)(nil)),
		"C2L_ForestSearch":                          reflect.ValueOf((*cl.C2L_ForestSearch)(nil)),
		"C2L_ForestSpeedGrow":                       reflect.ValueOf((*cl.C2L_ForestSpeedGrow)(nil)),
		"C2L_ForestStartFeed":                       reflect.ValueOf((*cl.C2L_ForestStartFeed)(nil)),
		"C2L_ForestStartPlant":                      reflect.ValueOf((*cl.C2L_ForestStartPlant)(nil)),
		"C2L_ForestUpdateSlot":                      reflect.ValueOf((*cl.C2L_ForestUpdateSlot)(nil)),
		"C2L_Formation":                             reflect.ValueOf((*cl.C2L_Formation)(nil)),
		"C2L_FragmentCompose":                       reflect.ValueOf((*cl.C2L_FragmentCompose)(nil)),
		"C2L_FragmentComposeAll":                    reflect.ValueOf((*cl.C2L_FragmentComposeAll)(nil)),
		"C2L_FragmentComposeTest":                   reflect.ValueOf((*cl.C2L_FragmentComposeTest)(nil)),
		"C2L_FragmentEmblemCompose":                 reflect.ValueOf((*cl.C2L_FragmentEmblemCompose)(nil)),
		"C2L_FriendAdd":                             reflect.ValueOf((*cl.C2L_FriendAdd)(nil)),
		"C2L_FriendBlacklist":                       reflect.ValueOf((*cl.C2L_FriendBlacklist)(nil)),
		"C2L_FriendConfirm":                         reflect.ValueOf((*cl.C2L_FriendConfirm)(nil)),
		"C2L_FriendDelete":                          reflect.ValueOf((*cl.C2L_FriendDelete)(nil)),
		"C2L_FriendGetBlacklist":                    reflect.ValueOf((*cl.C2L_FriendGetBlacklist)(nil)),
		"C2L_FriendInfo":                            reflect.ValueOf((*cl.C2L_FriendInfo)(nil)),
		"C2L_FriendRecommend":                       reflect.ValueOf((*cl.C2L_FriendRecommend)(nil)),
		"C2L_FriendRecvLike":                        reflect.ValueOf((*cl.C2L_FriendRecvLike)(nil)),
		"C2L_FriendRemBlacklist":                    reflect.ValueOf((*cl.C2L_FriendRemBlacklist)(nil)),
		"C2L_FriendRequestInfo":                     reflect.ValueOf((*cl.C2L_FriendRequestInfo)(nil)),
		"C2L_FriendSearch":                          reflect.ValueOf((*cl.C2L_FriendSearch)(nil)),
		"C2L_FriendSendLike":                        reflect.ValueOf((*cl.C2L_FriendSendLike)(nil)),
		"C2L_FunctionGetStatus":                     reflect.ValueOf((*cl.C2L_FunctionGetStatus)(nil)),
		"C2L_GM":                                    reflect.ValueOf((*cl.C2L_GM)(nil)),
		"C2L_GSTArenaVote":                          reflect.ValueOf((*cl.C2L_GSTArenaVote)(nil)),
		"C2L_GSTBossAward":                          reflect.ValueOf((*cl.C2L_GSTBossAward)(nil)),
		"C2L_GSTBossBuyChallenge":                   reflect.ValueOf((*cl.C2L_GSTBossBuyChallenge)(nil)),
		"C2L_GSTBossFight":                          reflect.ValueOf((*cl.C2L_GSTBossFight)(nil)),
		"C2L_GSTBossGet":                            reflect.ValueOf((*cl.C2L_GSTBossGet)(nil)),
		"C2L_GSTBossRank":                           reflect.ValueOf((*cl.C2L_GSTBossRank)(nil)),
		"C2L_GSTChallengeBuffChoose":                reflect.ValueOf((*cl.C2L_GSTChallengeBuffChoose)(nil)),
		"C2L_GSTChallengeFight":                     reflect.ValueOf((*cl.C2L_GSTChallengeFight)(nil)),
		"C2L_GSTChallengeFightLogList":              reflect.ValueOf((*cl.C2L_GSTChallengeFightLogList)(nil)),
		"C2L_GSTChallengeGetData":                   reflect.ValueOf((*cl.C2L_GSTChallengeGetData)(nil)),
		"C2L_GSTChallengeMatch":                     reflect.ValueOf((*cl.C2L_GSTChallengeMatch)(nil)),
		"C2L_GSTChallengeRank":                      reflect.ValueOf((*cl.C2L_GSTChallengeRank)(nil)),
		"C2L_GSTChallengeTaskReward":                reflect.ValueOf((*cl.C2L_GSTChallengeTaskReward)(nil)),
		"C2L_GSTDonate":                             reflect.ValueOf((*cl.C2L_GSTDonate)(nil)),
		"C2L_GSTDragonEvolve":                       reflect.ValueOf((*cl.C2L_GSTDragonEvolve)(nil)),
		"C2L_GSTDragonFight":                        reflect.ValueOf((*cl.C2L_GSTDragonFight)(nil)),
		"C2L_GSTDragonFightBuyCount":                reflect.ValueOf((*cl.C2L_GSTDragonFightBuyCount)(nil)),
		"C2L_GSTDragonFightUseTicket":               reflect.ValueOf((*cl.C2L_GSTDragonFightUseTicket)(nil)),
		"C2L_GSTDragonGetCultivation":               reflect.ValueOf((*cl.C2L_GSTDragonGetCultivation)(nil)),
		"C2L_GSTDragonGetData":                      reflect.ValueOf((*cl.C2L_GSTDragonGetData)(nil)),
		"C2L_GSTDragonRank":                         reflect.ValueOf((*cl.C2L_GSTDragonRank)(nil)),
		"C2L_GSTDragonShow":                         reflect.ValueOf((*cl.C2L_GSTDragonShow)(nil)),
		"C2L_GSTDragonSkillOperate":                 reflect.ValueOf((*cl.C2L_GSTDragonSkillOperate)(nil)),
		"C2L_GSTDragonStrategySkill":                reflect.ValueOf((*cl.C2L_GSTDragonStrategySkill)(nil)),
		"C2L_GSTDragonTaskAward":                    reflect.ValueOf((*cl.C2L_GSTDragonTaskAward)(nil)),
		"C2L_GSTDragonTaskGetData":                  reflect.ValueOf((*cl.C2L_GSTDragonTaskGetData)(nil)),
		"C2L_GSTExchangeGroundTeam":                 reflect.ValueOf((*cl.C2L_GSTExchangeGroundTeam)(nil)),
		"C2L_GSTGetArenaVoteRecord":                 reflect.ValueOf((*cl.C2L_GSTGetArenaVoteRecord)(nil)),
		"C2L_GSTGetData":                            reflect.ValueOf((*cl.C2L_GSTGetData)(nil)),
		"C2L_GSTGetGroundData":                      reflect.ValueOf((*cl.C2L_GSTGetGroundData)(nil)),
		"C2L_GSTGetGuildDonateData":                 reflect.ValueOf((*cl.C2L_GSTGetGuildDonateData)(nil)),
		"C2L_GSTGetGuildDonateMemData":              reflect.ValueOf((*cl.C2L_GSTGetGuildDonateMemData)(nil)),
		"C2L_GSTGetHangUpReward":                    reflect.ValueOf((*cl.C2L_GSTGetHangUpReward)(nil)),
		"C2L_GSTGetLogData":                         reflect.ValueOf((*cl.C2L_GSTGetLogData)(nil)),
		"C2L_GSTGetTasksData":                       reflect.ValueOf((*cl.C2L_GSTGetTasksData)(nil)),
		"C2L_GSTGetTasksReward":                     reflect.ValueOf((*cl.C2L_GSTGetTasksReward)(nil)),
		"C2L_GSTGetTeamsData":                       reflect.ValueOf((*cl.C2L_GSTGetTeamsData)(nil)),
		"C2L_GSTGroupUsersRank":                     reflect.ValueOf((*cl.C2L_GSTGroupUsersRank)(nil)),
		"C2L_GSTGroupUsersRankLike":                 reflect.ValueOf((*cl.C2L_GSTGroupUsersRankLike)(nil)),
		"C2L_GSTGuildBuildDispatchHero":             reflect.ValueOf((*cl.C2L_GSTGuildBuildDispatchHero)(nil)),
		"C2L_GSTGuildBuildDonate":                   reflect.ValueOf((*cl.C2L_GSTGuildBuildDonate)(nil)),
		"C2L_GSTGuildBuildDonateRank":               reflect.ValueOf((*cl.C2L_GSTGuildBuildDonateRank)(nil)),
		"C2L_GSTGuildBuildGetData":                  reflect.ValueOf((*cl.C2L_GSTGuildBuildGetData)(nil)),
		"C2L_GSTGuildBuildGetTaskData":              reflect.ValueOf((*cl.C2L_GSTGuildBuildGetTaskData)(nil)),
		"C2L_GSTGuildBuildRecvTaskAward":            reflect.ValueOf((*cl.C2L_GSTGuildBuildRecvTaskAward)(nil)),
		"C2L_GSTMessageEdit":                        reflect.ValueOf((*cl.C2L_GSTMessageEdit)(nil)),
		"C2L_GSTOreBuyTimes":                        reflect.ValueOf((*cl.C2L_GSTOreBuyTimes)(nil)),
		"C2L_GSTOreFight":                           reflect.ValueOf((*cl.C2L_GSTOreFight)(nil)),
		"C2L_GSTOreGetData":                         reflect.ValueOf((*cl.C2L_GSTOreGetData)(nil)),
		"C2L_GSTOreGetOreData":                      reflect.ValueOf((*cl.C2L_GSTOreGetOreData)(nil)),
		"C2L_GSTOreOccupy":                          reflect.ValueOf((*cl.C2L_GSTOreOccupy)(nil)),
		"C2L_GSTOreSearchAssist":                    reflect.ValueOf((*cl.C2L_GSTOreSearchAssist)(nil)),
		"C2L_GSTPreviewHangUpReward":                reflect.ValueOf((*cl.C2L_GSTPreviewHangUpReward)(nil)),
		"C2L_GSTRank":                               reflect.ValueOf((*cl.C2L_GSTRank)(nil)),
		"C2L_GSTScorePreview":                       reflect.ValueOf((*cl.C2L_GSTScorePreview)(nil)),
		"C2L_GSTSkillAssemble":                      reflect.ValueOf((*cl.C2L_GSTSkillAssemble)(nil)),
		"C2L_GSTTeamOperate":                        reflect.ValueOf((*cl.C2L_GSTTeamOperate)(nil)),
		"C2L_GSTTechDonate":                         reflect.ValueOf((*cl.C2L_GSTTechDonate)(nil)),
		"C2L_GSTTechGetData":                        reflect.ValueOf((*cl.C2L_GSTTechGetData)(nil)),
		"C2L_GSTTechGuildUserRank":                  reflect.ValueOf((*cl.C2L_GSTTechGuildUserRank)(nil)),
		"C2L_GSTTechSign":                           reflect.ValueOf((*cl.C2L_GSTTechSign)(nil)),
		"C2L_GSTTechTaskReward":                     reflect.ValueOf((*cl.C2L_GSTTechTaskReward)(nil)),
		"C2L_GemCompose":                            reflect.ValueOf((*cl.C2L_GemCompose)(nil)),
		"C2L_GemConvert":                            reflect.ValueOf((*cl.C2L_GemConvert)(nil)),
		"C2L_GemDecompose":                          reflect.ValueOf((*cl.C2L_GemDecompose)(nil)),
		"C2L_GemWear":                               reflect.ValueOf((*cl.C2L_GemWear)(nil)),
		"C2L_GetAchieveShowcase":                    reflect.ValueOf((*cl.C2L_GetAchieveShowcase)(nil)),
		"C2L_GetBattleReport":                       reflect.ValueOf((*cl.C2L_GetBattleReport)(nil)),
		"C2L_GetClientInfo":                         reflect.ValueOf((*cl.C2L_GetClientInfo)(nil)),
		"C2L_GetCognitionLog":                       reflect.ValueOf((*cl.C2L_GetCognitionLog)(nil)),
		"C2L_GetCommonRank":                         reflect.ValueOf((*cl.C2L_GetCommonRank)(nil)),
		"C2L_GetCommonRankFirst":                    reflect.ValueOf((*cl.C2L_GetCommonRankFirst)(nil)),
		"C2L_GetCrossRankFirst":                     reflect.ValueOf((*cl.C2L_GetCrossRankFirst)(nil)),
		"C2L_GetDefFormationPower":                  reflect.ValueOf((*cl.C2L_GetDefFormationPower)(nil)),
		"C2L_GetFormation":                          reflect.ValueOf((*cl.C2L_GetFormation)(nil)),
		"C2L_GetGems":                               reflect.ValueOf((*cl.C2L_GetGems)(nil)),
		"C2L_GetGiftCodeAward":                      reflect.ValueOf((*cl.C2L_GetGiftCodeAward)(nil)),
		"C2L_GetMails":                              reflect.ValueOf((*cl.C2L_GetMails)(nil)),
		"C2L_GetPush":                               reflect.ValueOf((*cl.C2L_GetPush)(nil)),
		"C2L_GetSeasonFlashBackData":                reflect.ValueOf((*cl.C2L_GetSeasonFlashBackData)(nil)),
		"C2L_GetSeasonRankFirst":                    reflect.ValueOf((*cl.C2L_GetSeasonRankFirst)(nil)),
		"C2L_GetSeasonRankList":                     reflect.ValueOf((*cl.C2L_GetSeasonRankList)(nil)),
		"C2L_GetUserBattleData":                     reflect.ValueOf((*cl.C2L_GetUserBattleData)(nil)),
		"C2L_GetUserSnapshots":                      reflect.ValueOf((*cl.C2L_GetUserSnapshots)(nil)),
		"C2L_GlobalAttrGet":                         reflect.ValueOf((*cl.C2L_GlobalAttrGet)(nil)),
		"C2L_GlobalAttrScoreGet":                    reflect.ValueOf((*cl.C2L_GlobalAttrScoreGet)(nil)),
		"C2L_GodPresentGetData":                     reflect.ValueOf((*cl.C2L_GodPresentGetData)(nil)),
		"C2L_GodPresentRecvAwards":                  reflect.ValueOf((*cl.C2L_GodPresentRecvAwards)(nil)),
		"C2L_GodPresentRecvItem":                    reflect.ValueOf((*cl.C2L_GodPresentRecvItem)(nil)),
		"C2L_GodPresentSummon":                      reflect.ValueOf((*cl.C2L_GodPresentSummon)(nil)),
		"C2L_GoddessChangeSuit":                     reflect.ValueOf((*cl.C2L_GoddessChangeSuit)(nil)),
		"C2L_GoddessChapterFight":                   reflect.ValueOf((*cl.C2L_GoddessChapterFight)(nil)),
		"C2L_GoddessCollect":                        reflect.ValueOf((*cl.C2L_GoddessCollect)(nil)),
		"C2L_GoddessContractGetData":                reflect.ValueOf((*cl.C2L_GoddessContractGetData)(nil)),
		"C2L_GoddessFeed":                           reflect.ValueOf((*cl.C2L_GoddessFeed)(nil)),
		"C2L_GoddessRecovery":                       reflect.ValueOf((*cl.C2L_GoddessRecovery)(nil)),
		"C2L_GoddessStoryAward":                     reflect.ValueOf((*cl.C2L_GoddessStoryAward)(nil)),
		"C2L_GoddessSuitUnlock":                     reflect.ValueOf((*cl.C2L_GoddessSuitUnlock)(nil)),
		"C2L_GoddessTouch":                          reflect.ValueOf((*cl.C2L_GoddessTouch)(nil)),
		"C2L_GoddessUnlock":                         reflect.ValueOf((*cl.C2L_GoddessUnlock)(nil)),
		"C2L_GoldBuyGet":                            reflect.ValueOf((*cl.C2L_GoldBuyGet)(nil)),
		"C2L_GoldBuyGetGold":                        reflect.ValueOf((*cl.C2L_GoldBuyGetGold)(nil)),
		"C2L_GuidanceFinishGroup":                   reflect.ValueOf((*cl.C2L_GuidanceFinishGroup)(nil)),
		"C2L_GuidanceFinishStep":                    reflect.ValueOf((*cl.C2L_GuidanceFinishStep)(nil)),
		"C2L_GuidanceList":                          reflect.ValueOf((*cl.C2L_GuidanceList)(nil)),
		"C2L_GuidanceSkip":                          reflect.ValueOf((*cl.C2L_GuidanceSkip)(nil)),
		"C2L_GuildApplyList":                        reflect.ValueOf((*cl.C2L_GuildApplyList)(nil)),
		"C2L_GuildApplyRatify":                      reflect.ValueOf((*cl.C2L_GuildApplyRatify)(nil)),
		"C2L_GuildChestActivate":                    reflect.ValueOf((*cl.C2L_GuildChestActivate)(nil)),
		"C2L_GuildChestGetData":                     reflect.ValueOf((*cl.C2L_GuildChestGetData)(nil)),
		"C2L_GuildChestLike":                        reflect.ValueOf((*cl.C2L_GuildChestLike)(nil)),
		"C2L_GuildChestRecv":                        reflect.ValueOf((*cl.C2L_GuildChestRecv)(nil)),
		"C2L_GuildCombineApply":                     reflect.ValueOf((*cl.C2L_GuildCombineApply)(nil)),
		"C2L_GuildCombineCheck":                     reflect.ValueOf((*cl.C2L_GuildCombineCheck)(nil)),
		"C2L_GuildCombineRatify":                    reflect.ValueOf((*cl.C2L_GuildCombineRatify)(nil)),
		"C2L_GuildCreate":                           reflect.ValueOf((*cl.C2L_GuildCreate)(nil)),
		"C2L_GuildDisband":                          reflect.ValueOf((*cl.C2L_GuildDisband)(nil)),
		"C2L_GuildDonate":                           reflect.ValueOf((*cl.C2L_GuildDonate)(nil)),
		"C2L_GuildDonateLogList":                    reflect.ValueOf((*cl.C2L_GuildDonateLogList)(nil)),
		"C2L_GuildDungeonBuyChallengeTimes":         reflect.ValueOf((*cl.C2L_GuildDungeonBuyChallengeTimes)(nil)),
		"C2L_GuildDungeonChapterInfo":               reflect.ValueOf((*cl.C2L_GuildDungeonChapterInfo)(nil)),
		"C2L_GuildDungeonDelMessageBoard":           reflect.ValueOf((*cl.C2L_GuildDungeonDelMessageBoard)(nil)),
		"C2L_GuildDungeonFight":                     reflect.ValueOf((*cl.C2L_GuildDungeonFight)(nil)),
		"C2L_GuildDungeonGetMembersFightInfo":       reflect.ValueOf((*cl.C2L_GuildDungeonGetMembersFightInfo)(nil)),
		"C2L_GuildDungeonGetMessageBoard":           reflect.ValueOf((*cl.C2L_GuildDungeonGetMessageBoard)(nil)),
		"C2L_GuildDungeonGetStrategy":               reflect.ValueOf((*cl.C2L_GuildDungeonGetStrategy)(nil)),
		"C2L_GuildDungeonHallOfFame":                reflect.ValueOf((*cl.C2L_GuildDungeonHallOfFame)(nil)),
		"C2L_GuildDungeonInfo":                      reflect.ValueOf((*cl.C2L_GuildDungeonInfo)(nil)),
		"C2L_GuildDungeonLogList":                   reflect.ValueOf((*cl.C2L_GuildDungeonLogList)(nil)),
		"C2L_GuildDungeonRankLike":                  reflect.ValueOf((*cl.C2L_GuildDungeonRankLike)(nil)),
		"C2L_GuildDungeonRecvAllBossBoxAward":       reflect.ValueOf((*cl.C2L_GuildDungeonRecvAllBossBoxAward)(nil)),
		"C2L_GuildDungeonRecvBossBoxAward":          reflect.ValueOf((*cl.C2L_GuildDungeonRecvBossBoxAward)(nil)),
		"C2L_GuildDungeonRecvChapterTaskAward":      reflect.ValueOf((*cl.C2L_GuildDungeonRecvChapterTaskAward)(nil)),
		"C2L_GuildDungeonSeasonDivisionAward":       reflect.ValueOf((*cl.C2L_GuildDungeonSeasonDivisionAward)(nil)),
		"C2L_GuildDungeonSetFocus":                  reflect.ValueOf((*cl.C2L_GuildDungeonSetFocus)(nil)),
		"C2L_GuildDungeonSetMessageBoard":           reflect.ValueOf((*cl.C2L_GuildDungeonSetMessageBoard)(nil)),
		"C2L_GuildDungeonTop3Guild":                 reflect.ValueOf((*cl.C2L_GuildDungeonTop3Guild)(nil)),
		"C2L_GuildDungeonUseStrategy":               reflect.ValueOf((*cl.C2L_GuildDungeonUseStrategy)(nil)),
		"C2L_GuildDungeonUserDamageRank":            reflect.ValueOf((*cl.C2L_GuildDungeonUserDamageRank)(nil)),
		"C2L_GuildGetBadgeList":                     reflect.ValueOf((*cl.C2L_GuildGetBadgeList)(nil)),
		"C2L_GuildGetDeclaration":                   reflect.ValueOf((*cl.C2L_GuildGetDeclaration)(nil)),
		"C2L_GuildGetDivisionAwardInfo":             reflect.ValueOf((*cl.C2L_GuildGetDivisionAwardInfo)(nil)),
		"C2L_GuildGetDonateAward":                   reflect.ValueOf((*cl.C2L_GuildGetDonateAward)(nil)),
		"C2L_GuildGetMedals":                        reflect.ValueOf((*cl.C2L_GuildGetMedals)(nil)),
		"C2L_GuildGetMembers":                       reflect.ValueOf((*cl.C2L_GuildGetMembers)(nil)),
		"C2L_GuildGetMyInfo":                        reflect.ValueOf((*cl.C2L_GuildGetMyInfo)(nil)),
		"C2L_GuildList":                             reflect.ValueOf((*cl.C2L_GuildList)(nil)),
		"C2L_GuildListGetDetail":                    reflect.ValueOf((*cl.C2L_GuildListGetDetail)(nil)),
		"C2L_GuildLogList":                          reflect.ValueOf((*cl.C2L_GuildLogList)(nil)),
		"C2L_GuildMainInfo":                         reflect.ValueOf((*cl.C2L_GuildMainInfo)(nil)),
		"C2L_GuildManagerMember":                    reflect.ValueOf((*cl.C2L_GuildManagerMember)(nil)),
		"C2L_GuildMedalLike":                        reflect.ValueOf((*cl.C2L_GuildMedalLike)(nil)),
		"C2L_GuildMobilizationAcceptTask":           reflect.ValueOf((*cl.C2L_GuildMobilizationAcceptTask)(nil)),
		"C2L_GuildMobilizationBuyTimes":             reflect.ValueOf((*cl.C2L_GuildMobilizationBuyTimes)(nil)),
		"C2L_GuildMobilizationEditMessageBoard":     reflect.ValueOf((*cl.C2L_GuildMobilizationEditMessageBoard)(nil)),
		"C2L_GuildMobilizationFinishTaskLogs":       reflect.ValueOf((*cl.C2L_GuildMobilizationFinishTaskLogs)(nil)),
		"C2L_GuildMobilizationFreshTask":            reflect.ValueOf((*cl.C2L_GuildMobilizationFreshTask)(nil)),
		"C2L_GuildMobilizationGetData":              reflect.ValueOf((*cl.C2L_GuildMobilizationGetData)(nil)),
		"C2L_GuildMobilizationGiveUpTask":           reflect.ValueOf((*cl.C2L_GuildMobilizationGiveUpTask)(nil)),
		"C2L_GuildMobilizationGuildRank":            reflect.ValueOf((*cl.C2L_GuildMobilizationGuildRank)(nil)),
		"C2L_GuildMobilizationPersonalRank":         reflect.ValueOf((*cl.C2L_GuildMobilizationPersonalRank)(nil)),
		"C2L_GuildMobilizationRecvScoreLevel":       reflect.ValueOf((*cl.C2L_GuildMobilizationRecvScoreLevel)(nil)),
		"C2L_GuildMobilizationScoreAward":           reflect.ValueOf((*cl.C2L_GuildMobilizationScoreAward)(nil)),
		"C2L_GuildMobilizationSignTask":             reflect.ValueOf((*cl.C2L_GuildMobilizationSignTask)(nil)),
		"C2L_GuildModifyInfo":                       reflect.ValueOf((*cl.C2L_GuildModifyInfo)(nil)),
		"C2L_GuildModifyNotice":                     reflect.ValueOf((*cl.C2L_GuildModifyNotice)(nil)),
		"C2L_GuildQuit":                             reflect.ValueOf((*cl.C2L_GuildQuit)(nil)),
		"C2L_GuildRank":                             reflect.ValueOf((*cl.C2L_GuildRank)(nil)),
		"C2L_GuildSearch":                           reflect.ValueOf((*cl.C2L_GuildSearch)(nil)),
		"C2L_GuildSendMail":                         reflect.ValueOf((*cl.C2L_GuildSendMail)(nil)),
		"C2L_GuildSendRecruitMsg":                   reflect.ValueOf((*cl.C2L_GuildSendRecruitMsg)(nil)),
		"C2L_GuildSetName":                          reflect.ValueOf((*cl.C2L_GuildSetName)(nil)),
		"C2L_GuildTalentLevelUp":                    reflect.ValueOf((*cl.C2L_GuildTalentLevelUp)(nil)),
		"C2L_GuildTalentList":                       reflect.ValueOf((*cl.C2L_GuildTalentList)(nil)),
		"C2L_GuildTalentReset":                      reflect.ValueOf((*cl.C2L_GuildTalentReset)(nil)),
		"C2L_GuildUserApply":                        reflect.ValueOf((*cl.C2L_GuildUserApply)(nil)),
		"C2L_HandbooksActive":                       reflect.ValueOf((*cl.C2L_HandbooksActive)(nil)),
		"C2L_HandbooksActiveHeroAttr":               reflect.ValueOf((*cl.C2L_HandbooksActiveHeroAttr)(nil)),
		"C2L_HandbooksGetData":                      reflect.ValueOf((*cl.C2L_HandbooksGetData)(nil)),
		"C2L_HandbooksReceiveAwards":                reflect.ValueOf((*cl.C2L_HandbooksReceiveAwards)(nil)),
		"C2L_HasRecvH5DesktopReward":                reflect.ValueOf((*cl.C2L_HasRecvH5DesktopReward)(nil)),
		"C2L_HeroAwaken":                            reflect.ValueOf((*cl.C2L_HeroAwaken)(nil)),
		"C2L_HeroBack":                              reflect.ValueOf((*cl.C2L_HeroBack)(nil)),
		"C2L_HeroBuySlot":                           reflect.ValueOf((*cl.C2L_HeroBuySlot)(nil)),
		"C2L_HeroChangeRandom":                      reflect.ValueOf((*cl.C2L_HeroChangeRandom)(nil)),
		"C2L_HeroChangeSave":                        reflect.ValueOf((*cl.C2L_HeroChangeSave)(nil)),
		"C2L_HeroConversion":                        reflect.ValueOf((*cl.C2L_HeroConversion)(nil)),
		"C2L_HeroConvert":                           reflect.ValueOf((*cl.C2L_HeroConvert)(nil)),
		"C2L_HeroConvertAwakenItem":                 reflect.ValueOf((*cl.C2L_HeroConvertAwakenItem)(nil)),
		"C2L_HeroDecompose":                         reflect.ValueOf((*cl.C2L_HeroDecompose)(nil)),
		"C2L_HeroExchange":                          reflect.ValueOf((*cl.C2L_HeroExchange)(nil)),
		"C2L_HeroGemLevelUp":                        reflect.ValueOf((*cl.C2L_HeroGemLevelUp)(nil)),
		"C2L_HeroGetStarUpCosts":                    reflect.ValueOf((*cl.C2L_HeroGetStarUpCosts)(nil)),
		"C2L_HeroLevelUp":                           reflect.ValueOf((*cl.C2L_HeroLevelUp)(nil)),
		"C2L_HeroList":                              reflect.ValueOf((*cl.C2L_HeroList)(nil)),
		"C2L_HeroRevive":                            reflect.ValueOf((*cl.C2L_HeroRevive)(nil)),
		"C2L_HeroStageUp":                           reflect.ValueOf((*cl.C2L_HeroStageUp)(nil)),
		"C2L_HeroStarUp":                            reflect.ValueOf((*cl.C2L_HeroStarUp)(nil)),
		"C2L_HeroStarUpRedList":                     reflect.ValueOf((*cl.C2L_HeroStarUpRedList)(nil)),
		"C2L_HeroTagUpdate":                         reflect.ValueOf((*cl.C2L_HeroTagUpdate)(nil)),
		"C2L_HeroTestAttr":                          reflect.ValueOf((*cl.C2L_HeroTestAttr)(nil)),
		"C2L_HeroTestCalPower":                      reflect.ValueOf((*cl.C2L_HeroTestCalPower)(nil)),
		"C2L_HeroUpdateLockStatus":                  reflect.ValueOf((*cl.C2L_HeroUpdateLockStatus)(nil)),
		"C2L_HeroesUpdate":                          reflect.ValueOf((*cl.C2L_HeroesUpdate)(nil)),
		"C2L_HotRankGet":                            reflect.ValueOf((*cl.C2L_HotRankGet)(nil)),
		"C2L_ItemSelect":                            reflect.ValueOf((*cl.C2L_ItemSelect)(nil)),
		"C2L_KeepAlive":                             reflect.ValueOf((*cl.C2L_KeepAlive)(nil)),
		"C2L_LinkHeroSummon":                        reflect.ValueOf((*cl.C2L_LinkHeroSummon)(nil)),
		"C2L_LinkHeroSummonGet":                     reflect.ValueOf((*cl.C2L_LinkHeroSummonGet)(nil)),
		"C2L_LinkHeroSummonTest":                    reflect.ValueOf((*cl.C2L_LinkHeroSummonTest)(nil)),
		"C2L_LinkInfo":                              reflect.ValueOf((*cl.C2L_LinkInfo)(nil)),
		"C2L_LinkSetView":                           reflect.ValueOf((*cl.C2L_LinkSetView)(nil)),
		"C2L_MazeBuyRevive":                         reflect.ValueOf((*cl.C2L_MazeBuyRevive)(nil)),
		"C2L_MazeGetGrid":                           reflect.ValueOf((*cl.C2L_MazeGetGrid)(nil)),
		"C2L_MazeGetMap":                            reflect.ValueOf((*cl.C2L_MazeGetMap)(nil)),
		"C2L_MazeGetSelectMapData":                  reflect.ValueOf((*cl.C2L_MazeGetSelectMapData)(nil)),
		"C2L_MazeRecoveryHero":                      reflect.ValueOf((*cl.C2L_MazeRecoveryHero)(nil)),
		"C2L_MazeSelectBuff":                        reflect.ValueOf((*cl.C2L_MazeSelectBuff)(nil)),
		"C2L_MazeSweep":                             reflect.ValueOf((*cl.C2L_MazeSweep)(nil)),
		"C2L_MazeTaskReceiveAward":                  reflect.ValueOf((*cl.C2L_MazeTaskReceiveAward)(nil)),
		"C2L_MazeTriggerEvent":                      reflect.ValueOf((*cl.C2L_MazeTriggerEvent)(nil)),
		"C2L_MazeUseItem":                           reflect.ValueOf((*cl.C2L_MazeUseItem)(nil)),
		"C2L_MedalGetData":                          reflect.ValueOf((*cl.C2L_MedalGetData)(nil)),
		"C2L_MedalReceiveAward":                     reflect.ValueOf((*cl.C2L_MedalReceiveAward)(nil)),
		"C2L_MemoryLatest":                          reflect.ValueOf((*cl.C2L_MemoryLatest)(nil)),
		"C2L_MemoryUnlock":                          reflect.ValueOf((*cl.C2L_MemoryUnlock)(nil)),
		"C2L_MirageBuyCount":                        reflect.ValueOf((*cl.C2L_MirageBuyCount)(nil)),
		"C2L_MirageDetail":                          reflect.ValueOf((*cl.C2L_MirageDetail)(nil)),
		"C2L_MirageFight":                           reflect.ValueOf((*cl.C2L_MirageFight)(nil)),
		"C2L_MirageList":                            reflect.ValueOf((*cl.C2L_MirageList)(nil)),
		"C2L_MiragePowerCrush":                      reflect.ValueOf((*cl.C2L_MiragePowerCrush)(nil)),
		"C2L_MirageReceiveAward":                    reflect.ValueOf((*cl.C2L_MirageReceiveAward)(nil)),
		"C2L_MirageSaveAffixes":                     reflect.ValueOf((*cl.C2L_MirageSaveAffixes)(nil)),
		"C2L_MirageSweep":                           reflect.ValueOf((*cl.C2L_MirageSweep)(nil)),
		"C2L_MirageTestDrop":                        reflect.ValueOf((*cl.C2L_MirageTestDrop)(nil)),
		"C2L_MonthTasksGetData":                     reflect.ValueOf((*cl.C2L_MonthTasksGetData)(nil)),
		"C2L_MonthTasksRecvAwards":                  reflect.ValueOf((*cl.C2L_MonthTasksRecvAwards)(nil)),
		"C2L_MonthlyCardGetData":                    reflect.ValueOf((*cl.C2L_MonthlyCardGetData)(nil)),
		"C2L_MonthlyCardReceiveAward":               reflect.ValueOf((*cl.C2L_MonthlyCardReceiveAward)(nil)),
		"C2L_MuteAccount":                           reflect.ValueOf((*cl.C2L_MuteAccount)(nil)),
		"C2L_NewYearActivityGetData":                reflect.ValueOf((*cl.C2L_NewYearActivityGetData)(nil)),
		"C2L_NewYearActivityLoginAward":             reflect.ValueOf((*cl.C2L_NewYearActivityLoginAward)(nil)),
		"C2L_OSSUrl":                                reflect.ValueOf((*cl.C2L_OSSUrl)(nil)),
		"C2L_OperateActivityGetData":                reflect.ValueOf((*cl.C2L_OperateActivityGetData)(nil)),
		"C2L_OperateActivityGetTaskReward":          reflect.ValueOf((*cl.C2L_OperateActivityGetTaskReward)(nil)),
		"C2L_OperateActivityGetXML":                 reflect.ValueOf((*cl.C2L_OperateActivityGetXML)(nil)),
		"C2L_OperateActivityInitActivity":           reflect.ValueOf((*cl.C2L_OperateActivityInitActivity)(nil)),
		"C2L_OperateActivityPromotionRechargeCheck": reflect.ValueOf((*cl.C2L_OperateActivityPromotionRechargeCheck)(nil)),
		"C2L_OperateActivityPromotionSelectAward":   reflect.ValueOf((*cl.C2L_OperateActivityPromotionSelectAward)(nil)),
		"C2L_OssBattleReport":                       reflect.ValueOf((*cl.C2L_OssBattleReport)(nil)),
		"C2L_PassGetData":                           reflect.ValueOf((*cl.C2L_PassGetData)(nil)),
		"C2L_PassLevelBuy":                          reflect.ValueOf((*cl.C2L_PassLevelBuy)(nil)),
		"C2L_PassReceiveAward":                      reflect.ValueOf((*cl.C2L_PassReceiveAward)(nil)),
		"C2L_PeakBaseData":                          reflect.ValueOf((*cl.C2L_PeakBaseData)(nil)),
		"C2L_PeakDoGuess":                           reflect.ValueOf((*cl.C2L_PeakDoGuess)(nil)),
		"C2L_PeakFighterDetail":                     reflect.ValueOf((*cl.C2L_PeakFighterDetail)(nil)),
		"C2L_PeakGetLastBattleReport":               reflect.ValueOf((*cl.C2L_PeakGetLastBattleReport)(nil)),
		"C2L_PeakGetMatch":                          reflect.ValueOf((*cl.C2L_PeakGetMatch)(nil)),
		"C2L_PeakGuessList":                         reflect.ValueOf((*cl.C2L_PeakGuessList)(nil)),
		"C2L_PeakRankList":                          reflect.ValueOf((*cl.C2L_PeakRankList)(nil)),
		"C2L_PeakRecvInviteReward":                  reflect.ValueOf((*cl.C2L_PeakRecvInviteReward)(nil)),
		"C2L_PeakWorship":                           reflect.ValueOf((*cl.C2L_PeakWorship)(nil)),
		"C2L_PreSeasonGetData":                      reflect.ValueOf((*cl.C2L_PreSeasonGetData)(nil)),
		"C2L_PreSeasonRecvAward":                    reflect.ValueOf((*cl.C2L_PreSeasonRecvAward)(nil)),
		"C2L_PushGiftGetData":                       reflect.ValueOf((*cl.C2L_PushGiftGetData)(nil)),
		"C2L_PyramidChooseAward":                    reflect.ValueOf((*cl.C2L_PyramidChooseAward)(nil)),
		"C2L_PyramidDraw":                           reflect.ValueOf((*cl.C2L_PyramidDraw)(nil)),
		"C2L_PyramidGetData":                        reflect.ValueOf((*cl.C2L_PyramidGetData)(nil)),
		"C2L_PyramidReceiveAwards":                  reflect.ValueOf((*cl.C2L_PyramidReceiveAwards)(nil)),
		"C2L_PyramidTestDraw":                       reflect.ValueOf((*cl.C2L_PyramidTestDraw)(nil)),
		"C2L_RankAchieveList":                       reflect.ValueOf((*cl.C2L_RankAchieveList)(nil)),
		"C2L_RankAchieveRecvAward":                  reflect.ValueOf((*cl.C2L_RankAchieveRecvAward)(nil)),
		"C2L_RateGetStatus":                         reflect.ValueOf((*cl.C2L_RateGetStatus)(nil)),
		"C2L_RateScore":                             reflect.ValueOf((*cl.C2L_RateScore)(nil)),
		"C2L_ReadMail":                              reflect.ValueOf((*cl.C2L_ReadMail)(nil)),
		"C2L_RechargeByCoupon":                      reflect.ValueOf((*cl.C2L_RechargeByCoupon)(nil)),
		"C2L_RechargeFirstGiftReward":               reflect.ValueOf((*cl.C2L_RechargeFirstGiftReward)(nil)),
		"C2L_RechargeGetData":                       reflect.ValueOf((*cl.C2L_RechargeGetData)(nil)),
		"C2L_RechargeSimulation":                    reflect.ValueOf((*cl.C2L_RechargeSimulation)(nil)),
		"C2L_RecvH5DesktopReward":                   reflect.ValueOf((*cl.C2L_RecvH5DesktopReward)(nil)),
		"C2L_RecvShareAward":                        reflect.ValueOf((*cl.C2L_RecvShareAward)(nil)),
		"C2L_RemainBookLevelUp":                     reflect.ValueOf((*cl.C2L_RemainBookLevelUp)(nil)),
		"C2L_RemainBookRecvExp":                     reflect.ValueOf((*cl.C2L_RemainBookRecvExp)(nil)),
		"C2L_RemainGetData":                         reflect.ValueOf((*cl.C2L_RemainGetData)(nil)),
		"C2L_RiteGetData":                           reflect.ValueOf((*cl.C2L_RiteGetData)(nil)),
		"C2L_RiteRareUp":                            reflect.ValueOf((*cl.C2L_RiteRareUp)(nil)),
		"C2L_RiteTakeRareAwards":                    reflect.ValueOf((*cl.C2L_RiteTakeRareAwards)(nil)),
		"C2L_RobotBattle":                           reflect.ValueOf((*cl.C2L_RobotBattle)(nil)),
		"C2L_RoundActivityGetData":                  reflect.ValueOf((*cl.C2L_RoundActivityGetData)(nil)),
		"C2L_RoundActivityRecvTaskAward":            reflect.ValueOf((*cl.C2L_RoundActivityRecvTaskAward)(nil)),
		"C2L_SeasonArenaBuyChallengeCount":          reflect.ValueOf((*cl.C2L_SeasonArenaBuyChallengeCount)(nil)),
		"C2L_SeasonArenaDivisionAward":              reflect.ValueOf((*cl.C2L_SeasonArenaDivisionAward)(nil)),
		"C2L_SeasonArenaFight":                      reflect.ValueOf((*cl.C2L_SeasonArenaFight)(nil)),
		"C2L_SeasonArenaGetData":                    reflect.ValueOf((*cl.C2L_SeasonArenaGetData)(nil)),
		"C2L_SeasonArenaGetRankList":                reflect.ValueOf((*cl.C2L_SeasonArenaGetRankList)(nil)),
		"C2L_SeasonArenaGetTaskData":                reflect.ValueOf((*cl.C2L_SeasonArenaGetTaskData)(nil)),
		"C2L_SeasonArenaLogList":                    reflect.ValueOf((*cl.C2L_SeasonArenaLogList)(nil)),
		"C2L_SeasonArenaOfFame":                     reflect.ValueOf((*cl.C2L_SeasonArenaOfFame)(nil)),
		"C2L_SeasonArenaRefresh":                    reflect.ValueOf((*cl.C2L_SeasonArenaRefresh)(nil)),
		"C2L_SeasonArenaTaskAward":                  reflect.ValueOf((*cl.C2L_SeasonArenaTaskAward)(nil)),
		"C2L_SeasonArenaUseTicket":                  reflect.ValueOf((*cl.C2L_SeasonArenaUseTicket)(nil)),
		"C2L_SeasonComplianceGetData":               reflect.ValueOf((*cl.C2L_SeasonComplianceGetData)(nil)),
		"C2L_SeasonComplianceList":                  reflect.ValueOf((*cl.C2L_SeasonComplianceList)(nil)),
		"C2L_SeasonComplianceRecvReward":            reflect.ValueOf((*cl.C2L_SeasonComplianceRecvReward)(nil)),
		"C2L_SeasonDoorFight":                       reflect.ValueOf((*cl.C2L_SeasonDoorFight)(nil)),
		"C2L_SeasonDoorFightLine":                   reflect.ValueOf((*cl.C2L_SeasonDoorFightLine)(nil)),
		"C2L_SeasonDoorFightLineReward":             reflect.ValueOf((*cl.C2L_SeasonDoorFightLineReward)(nil)),
		"C2L_SeasonDoorFightLineViewReward":         reflect.ValueOf((*cl.C2L_SeasonDoorFightLineViewReward)(nil)),
		"C2L_SeasonDoorGetData":                     reflect.ValueOf((*cl.C2L_SeasonDoorGetData)(nil)),
		"C2L_SeasonDoorLog":                         reflect.ValueOf((*cl.C2L_SeasonDoorLog)(nil)),
		"C2L_SeasonDoorTaskReward":                  reflect.ValueOf((*cl.C2L_SeasonDoorTaskReward)(nil)),
		"C2L_SeasonDungeonFight":                    reflect.ValueOf((*cl.C2L_SeasonDungeonFight)(nil)),
		"C2L_SeasonDungeonGetData":                  reflect.ValueOf((*cl.C2L_SeasonDungeonGetData)(nil)),
		"C2L_SeasonDungeonRecvReward":               reflect.ValueOf((*cl.C2L_SeasonDungeonRecvReward)(nil)),
		"C2L_SeasonEnter":                           reflect.ValueOf((*cl.C2L_SeasonEnter)(nil)),
		"C2L_SeasonJewelryDecompose":                reflect.ValueOf((*cl.C2L_SeasonJewelryDecompose)(nil)),
		"C2L_SeasonJewelryGetData":                  reflect.ValueOf((*cl.C2L_SeasonJewelryGetData)(nil)),
		"C2L_SeasonJewelryLock":                     reflect.ValueOf((*cl.C2L_SeasonJewelryLock)(nil)),
		"C2L_SeasonJewelrySetAutoDecompose":         reflect.ValueOf((*cl.C2L_SeasonJewelrySetAutoDecompose)(nil)),
		"C2L_SeasonJewelrySkillChange":              reflect.ValueOf((*cl.C2L_SeasonJewelrySkillChange)(nil)),
		"C2L_SeasonJewelrySkillChangeConfirm":       reflect.ValueOf((*cl.C2L_SeasonJewelrySkillChangeConfirm)(nil)),
		"C2L_SeasonJewelrySkillClassUp":             reflect.ValueOf((*cl.C2L_SeasonJewelrySkillClassUp)(nil)),
		"C2L_SeasonJewelrySkillLevelUp":             reflect.ValueOf((*cl.C2L_SeasonJewelrySkillLevelUp)(nil)),
		"C2L_SeasonJewelryTestReRollSkill":          reflect.ValueOf((*cl.C2L_SeasonJewelryTestReRollSkill)(nil)),
		"C2L_SeasonJewelryWear":                     reflect.ValueOf((*cl.C2L_SeasonJewelryWear)(nil)),
		"C2L_SeasonLevelGetData":                    reflect.ValueOf((*cl.C2L_SeasonLevelGetData)(nil)),
		"C2L_SeasonLevelRecvLvAwards":               reflect.ValueOf((*cl.C2L_SeasonLevelRecvLvAwards)(nil)),
		"C2L_SeasonLevelRecvTaskAwards":             reflect.ValueOf((*cl.C2L_SeasonLevelRecvTaskAwards)(nil)),
		"C2L_SeasonLevelUp":                         reflect.ValueOf((*cl.C2L_SeasonLevelUp)(nil)),
		"C2L_SeasonLinkActivate":                    reflect.ValueOf((*cl.C2L_SeasonLinkActivate)(nil)),
		"C2L_SeasonLinkGetData":                     reflect.ValueOf((*cl.C2L_SeasonLinkGetData)(nil)),
		"C2L_SeasonLinkMonumentTakeRareAwards":      reflect.ValueOf((*cl.C2L_SeasonLinkMonumentTakeRareAwards)(nil)),
		"C2L_SeasonMapAltar":                        reflect.ValueOf((*cl.C2L_SeasonMapAltar)(nil)),
		"C2L_SeasonMapBuyStamina":                   reflect.ValueOf((*cl.C2L_SeasonMapBuyStamina)(nil)),
		"C2L_SeasonMapDialogue":                     reflect.ValueOf((*cl.C2L_SeasonMapDialogue)(nil)),
		"C2L_SeasonMapFight":                        reflect.ValueOf((*cl.C2L_SeasonMapFight)(nil)),
		"C2L_SeasonMapGetData":                      reflect.ValueOf((*cl.C2L_SeasonMapGetData)(nil)),
		"C2L_SeasonMapGetRankList":                  reflect.ValueOf((*cl.C2L_SeasonMapGetRankList)(nil)),
		"C2L_SeasonMapGoodsPrice":                   reflect.ValueOf((*cl.C2L_SeasonMapGoodsPrice)(nil)),
		"C2L_SeasonMapMaster":                       reflect.ValueOf((*cl.C2L_SeasonMapMaster)(nil)),
		"C2L_SeasonMapMovePosition":                 reflect.ValueOf((*cl.C2L_SeasonMapMovePosition)(nil)),
		"C2L_SeasonMapPassPosition":                 reflect.ValueOf((*cl.C2L_SeasonMapPassPosition)(nil)),
		"C2L_SeasonMapPositionLogs":                 reflect.ValueOf((*cl.C2L_SeasonMapPositionLogs)(nil)),
		"C2L_SeasonMapPriceChangeLogs":              reflect.ValueOf((*cl.C2L_SeasonMapPriceChangeLogs)(nil)),
		"C2L_SeasonMapSystemEvent":                  reflect.ValueOf((*cl.C2L_SeasonMapSystemEvent)(nil)),
		"C2L_SeasonMapTaskReward":                   reflect.ValueOf((*cl.C2L_SeasonMapTaskReward)(nil)),
		"C2L_SeasonMapTrade":                        reflect.ValueOf((*cl.C2L_SeasonMapTrade)(nil)),
		"C2L_SeasonReturnGetData":                   reflect.ValueOf((*cl.C2L_SeasonReturnGetData)(nil)),
		"C2L_SeasonReturnTakeAwards":                reflect.ValueOf((*cl.C2L_SeasonReturnTakeAwards)(nil)),
		"C2L_SeasonShopBuy":                         reflect.ValueOf((*cl.C2L_SeasonShopBuy)(nil)),
		"C2L_SeasonShopGetData":                     reflect.ValueOf((*cl.C2L_SeasonShopGetData)(nil)),
		"C2L_SeasonStartTowerRankLike":              reflect.ValueOf((*cl.C2L_SeasonStartTowerRankLike)(nil)),
		"C2L_SeasonStartTowerRankList":              reflect.ValueOf((*cl.C2L_SeasonStartTowerRankList)(nil)),
		"C2L_SelectSummonGetOpenActivity":           reflect.ValueOf((*cl.C2L_SelectSummonGetOpenActivity)(nil)),
		"C2L_SelectSummonSummon":                    reflect.ValueOf((*cl.C2L_SelectSummonSummon)(nil)),
		"C2L_SelectSummonTestSummon":                reflect.ValueOf((*cl.C2L_SelectSummonTestSummon)(nil)),
		"C2L_SellItem":                              reflect.ValueOf((*cl.C2L_SellItem)(nil)),
		"C2L_SetClientInfo":                         reflect.ValueOf((*cl.C2L_SetClientInfo)(nil)),
		"C2L_SetName":                               reflect.ValueOf((*cl.C2L_SetName)(nil)),
		"C2L_SetPush":                               reflect.ValueOf((*cl.C2L_SetPush)(nil)),
		"C2L_SetServerTime":                         reflect.ValueOf((*cl.C2L_SetServerTime)(nil)),
		"C2L_SevenDayLoginData":                     reflect.ValueOf((*cl.C2L_SevenDayLoginData)(nil)),
		"C2L_SevenDayLoginTakeAward":                reflect.ValueOf((*cl.C2L_SevenDayLoginTakeAward)(nil)),
		"C2L_ShopBuy":                               reflect.ValueOf((*cl.C2L_ShopBuy)(nil)),
		"C2L_ShopList":                              reflect.ValueOf((*cl.C2L_ShopList)(nil)),
		"C2L_ShopRefresh":                           reflect.ValueOf((*cl.C2L_ShopRefresh)(nil)),
		"C2L_ShopReset":                             reflect.ValueOf((*cl.C2L_ShopReset)(nil)),
		"C2L_SkinList":                              reflect.ValueOf((*cl.C2L_SkinList)(nil)),
		"C2L_SkinUse":                               reflect.ValueOf((*cl.C2L_SkinUse)(nil)),
		"C2L_StoryReviewGetData":                    reflect.ValueOf((*cl.C2L_StoryReviewGetData)(nil)),
		"C2L_StoryReviewUnlock":                     reflect.ValueOf((*cl.C2L_StoryReviewUnlock)(nil)),
		"C2L_Summon":                                reflect.ValueOf((*cl.C2L_Summon)(nil)),
		"C2L_SummonArtifactPointsExchange":          reflect.ValueOf((*cl.C2L_SummonArtifactPointsExchange)(nil)),
		"C2L_SummonGetData":                         reflect.ValueOf((*cl.C2L_SummonGetData)(nil)),
		"C2L_SummonSetHeroAutoDecompose":            reflect.ValueOf((*cl.C2L_SummonSetHeroAutoDecompose)(nil)),
		"C2L_SummonSetWishList":                     reflect.ValueOf((*cl.C2L_SummonSetWishList)(nil)),
		"C2L_SummonSimulation":                      reflect.ValueOf((*cl.C2L_SummonSimulation)(nil)),
		"C2L_SyncQuestionnaire":                     reflect.ValueOf((*cl.C2L_SyncQuestionnaire)(nil)),
		"C2L_TalentTreeGetData":                     reflect.ValueOf((*cl.C2L_TalentTreeGetData)(nil)),
		"C2L_TalentTreeHot":                         reflect.ValueOf((*cl.C2L_TalentTreeHot)(nil)),
		"C2L_TalentTreeLevelUp":                     reflect.ValueOf((*cl.C2L_TalentTreeLevelUp)(nil)),
		"C2L_TalentTreePlanDelete":                  reflect.ValueOf((*cl.C2L_TalentTreePlanDelete)(nil)),
		"C2L_TalentTreePlanSave":                    reflect.ValueOf((*cl.C2L_TalentTreePlanSave)(nil)),
		"C2L_TalentTreeReceiveTaskAwards":           reflect.ValueOf((*cl.C2L_TalentTreeReceiveTaskAwards)(nil)),
		"C2L_TalentTreeReset":                       reflect.ValueOf((*cl.C2L_TalentTreeReset)(nil)),
		"C2L_TalesChapterFight":                     reflect.ValueOf((*cl.C2L_TalesChapterFight)(nil)),
		"C2L_TalesChapterFinish":                    reflect.ValueOf((*cl.C2L_TalesChapterFinish)(nil)),
		"C2L_TalesChapterTakeReward":                reflect.ValueOf((*cl.C2L_TalesChapterTakeReward)(nil)),
		"C2L_TalesEliteFight":                       reflect.ValueOf((*cl.C2L_TalesEliteFight)(nil)),
		"C2L_TalesEliteWipe":                        reflect.ValueOf((*cl.C2L_TalesEliteWipe)(nil)),
		"C2L_TalesList":                             reflect.ValueOf((*cl.C2L_TalesList)(nil)),
		"C2L_TaskGetInfo":                           reflect.ValueOf((*cl.C2L_TaskGetInfo)(nil)),
		"C2L_TaskReceiveAward":                      reflect.ValueOf((*cl.C2L_TaskReceiveAward)(nil)),
		"C2L_Test":                                  reflect.ValueOf((*cl.C2L_Test)(nil)),
		"C2L_TestBattleData":                        reflect.ValueOf((*cl.C2L_TestBattleData)(nil)),
		"C2L_TestDrop":                              reflect.ValueOf((*cl.C2L_TestDrop)(nil)),
		"C2L_TestEtcdGiftCode":                      reflect.ValueOf((*cl.C2L_TestEtcdGiftCode)(nil)),
		"C2L_TitleList":                             reflect.ValueOf((*cl.C2L_TitleList)(nil)),
		"C2L_TitleUse":                              reflect.ValueOf((*cl.C2L_TitleUse)(nil)),
		"C2L_TowerFight":                            reflect.ValueOf((*cl.C2L_TowerFight)(nil)),
		"C2L_TowerJump":                             reflect.ValueOf((*cl.C2L_TowerJump)(nil)),
		"C2L_TowerList":                             reflect.ValueOf((*cl.C2L_TowerList)(nil)),
		"C2L_TowerSeasonCognitionLogs":              reflect.ValueOf((*cl.C2L_TowerSeasonCognitionLogs)(nil)),
		"C2L_TowerSeasonFight":                      reflect.ValueOf((*cl.C2L_TowerSeasonFight)(nil)),
		"C2L_TowerSeasonGetData":                    reflect.ValueOf((*cl.C2L_TowerSeasonGetData)(nil)),
		"C2L_TowerSeasonRankLike":                   reflect.ValueOf((*cl.C2L_TowerSeasonRankLike)(nil)),
		"C2L_TowerSeasonRankList":                   reflect.ValueOf((*cl.C2L_TowerSeasonRankList)(nil)),
		"C2L_TowerSeasonRecvAward":                  reflect.ValueOf((*cl.C2L_TowerSeasonRecvAward)(nil)),
		"C2L_TowerSweep":                            reflect.ValueOf((*cl.C2L_TowerSweep)(nil)),
		"C2L_TowerstarDailyRecvAward":               reflect.ValueOf((*cl.C2L_TowerstarDailyRecvAward)(nil)),
		"C2L_TowerstarFight":                        reflect.ValueOf((*cl.C2L_TowerstarFight)(nil)),
		"C2L_TowerstarGetData":                      reflect.ValueOf((*cl.C2L_TowerstarGetData)(nil)),
		"C2L_TowerstarStarRecvAward":                reflect.ValueOf((*cl.C2L_TowerstarStarRecvAward)(nil)),
		"C2L_TrialAward":                            reflect.ValueOf((*cl.C2L_TrialAward)(nil)),
		"C2L_TrialFight":                            reflect.ValueOf((*cl.C2L_TrialFight)(nil)),
		"C2L_TrialGetInfo":                          reflect.ValueOf((*cl.C2L_TrialGetInfo)(nil)),
		"C2L_TrialPreview":                          reflect.ValueOf((*cl.C2L_TrialPreview)(nil)),
		"C2L_TrialSpeed":                            reflect.ValueOf((*cl.C2L_TrialSpeed)(nil)),
		"C2L_UpdateGems":                            reflect.ValueOf((*cl.C2L_UpdateGems)(nil)),
		"C2L_UpdateTop5":                            reflect.ValueOf((*cl.C2L_UpdateTop5)(nil)),
		"C2L_UseItem":                               reflect.ValueOf((*cl.C2L_UseItem)(nil)),
		"C2L_ViewFormation":                         reflect.ValueOf((*cl.C2L_ViewFormation)(nil)),
		"C2L_ViewUser":                              reflect.ValueOf((*cl.C2L_ViewUser)(nil)),
		"C2L_VipBuyGift":                            reflect.ValueOf((*cl.C2L_VipBuyGift)(nil)),
		"C2L_VipInfoGet":                            reflect.ValueOf((*cl.C2L_VipInfoGet)(nil)),
		"C2L_WorldBossFight":                        reflect.ValueOf((*cl.C2L_WorldBossFight)(nil)),
		"C2L_WorldBossGetData":                      reflect.ValueOf((*cl.C2L_WorldBossGetData)(nil)),
		"C2L_WorldBossGetRoomLog":                   reflect.ValueOf((*cl.C2L_WorldBossGetRoomLog)(nil)),
		"C2L_WorldBossRank":                         reflect.ValueOf((*cl.C2L_WorldBossRank)(nil)),
		"C2L_WorldBossRecvAward":                    reflect.ValueOf((*cl.C2L_WorldBossRecvAward)(nil)),
		"C2L_WorldBossRoomInfo":                     reflect.ValueOf((*cl.C2L_WorldBossRoomInfo)(nil)),
		"C2L_WorldBossSelectLevel":                  reflect.ValueOf((*cl.C2L_WorldBossSelectLevel)(nil)),
		"C2L_WorldBossWorship":                      reflect.ValueOf((*cl.C2L_WorldBossWorship)(nil)),
		"C2L_WrestleChangeRoom":                     reflect.ValueOf((*cl.C2L_WrestleChangeRoom)(nil)),
		"C2L_WrestleFight":                          reflect.ValueOf((*cl.C2L_WrestleFight)(nil)),
		"C2L_WrestleFightLog":                       reflect.ValueOf((*cl.C2L_WrestleFightLog)(nil)),
		"C2L_WrestleHallOfFame":                     reflect.ValueOf((*cl.C2L_WrestleHallOfFame)(nil)),
		"C2L_WrestleInfo":                           reflect.ValueOf((*cl.C2L_WrestleInfo)(nil)),
		"C2L_WrestleLike":                           reflect.ValueOf((*cl.C2L_WrestleLike)(nil)),
		"C2L_WrestleMapInfo":                        reflect.ValueOf((*cl.C2L_WrestleMapInfo)(nil)),
		"C2L_WrestleRankList":                       reflect.ValueOf((*cl.C2L_WrestleRankList)(nil)),
		"C2L_WrestleRecvTaskAward":                  reflect.ValueOf((*cl.C2L_WrestleRecvTaskAward)(nil)),
		"C2L_WrestleRoomInfo":                       reflect.ValueOf((*cl.C2L_WrestleRoomInfo)(nil)),
		"C2L_WrestleTopUserList":                    reflect.ValueOf((*cl.C2L_WrestleTopUserList)(nil)),
		"Carnival":                                  reflect.ValueOf((*cl.Carnival)(nil)),
		"ChatLike":                                  reflect.ValueOf((*cl.ChatLike)(nil)),
		"ChatMessage":                               reflect.ValueOf((*cl.ChatMessage)(nil)),
		"ChatUserHead":                              reflect.ValueOf((*cl.ChatUserHead)(nil)),
		"ClientInfo":                                reflect.ValueOf((*cl.ClientInfo)(nil)),
		"ClientInfoData":                            reflect.ValueOf((*cl.ClientInfoData)(nil)),
		"CognitionHeroPassiveSkill":                 reflect.ValueOf((*cl.CognitionHeroPassiveSkill)(nil)),
		"CognitionLog":                              reflect.ValueOf((*cl.CognitionLog)(nil)),
		"CognitionLogLinkInfo":                      reflect.ValueOf((*cl.CognitionLogLinkInfo)(nil)),
		"ComplianceList":                            reflect.ValueOf((*cl.ComplianceList)(nil)),
		"ComplianceSourcePoint":                     reflect.ValueOf((*cl.ComplianceSourcePoint)(nil)),
		"ComplianceTask":                            reflect.ValueOf((*cl.ComplianceTask)(nil)),
		"ComplianceTasks":                           reflect.ValueOf((*cl.ComplianceTasks)(nil)),
		"CouponCfg":                                 reflect.ValueOf((*cl.CouponCfg)(nil)),
		"CouponCondition":                           reflect.ValueOf((*cl.CouponCondition)(nil)),
		"CouponRuleContent":                         reflect.ValueOf((*cl.CouponRuleContent)(nil)),
		"Crystal":                                   reflect.ValueOf((*cl.Crystal)(nil)),
		"CrystalBlessing":                           reflect.ValueOf((*cl.CrystalBlessing)(nil)),
		"CrystalBlessingAchieve":                    reflect.ValueOf((*cl.CrystalBlessingAchieve)(nil)),
		"CrystalShareAttr":                          reflect.ValueOf((*cl.CrystalShareAttr)(nil)),
		"CrystalSlot":                               reflect.ValueOf((*cl.CrystalSlot)(nil)),
		"CultivateArtifactData":                     reflect.ValueOf((*cl.CultivateArtifactData)(nil)),
		"CultivateEmblemSkillData":                  reflect.ValueOf((*cl.CultivateEmblemSkillData)(nil)),
		"CultivateFormation":                        reflect.ValueOf((*cl.CultivateFormation)(nil)),
		"CultivateFormationData":                    reflect.ValueOf((*cl.CultivateFormationData)(nil)),
		"CultivateHeroData":                         reflect.ValueOf((*cl.CultivateHeroData)(nil)),
		"CultivateHotRank":                          reflect.ValueOf((*cl.CultivateHotRank)(nil)),
		"DailyAttendance":                           reflect.ValueOf((*cl.DailyAttendance)(nil)),
		"DailyAttendanceData":                       reflect.ValueOf((*cl.DailyAttendanceData)(nil)),
		"DailyAttendanceHero":                       reflect.ValueOf((*cl.DailyAttendanceHero)(nil)),
		"DailyAttendanceRecvParam":                  reflect.ValueOf((*cl.DailyAttendanceRecvParam)(nil)),
		"DailyCondition":                            reflect.ValueOf((*cl.DailyCondition)(nil)),
		"DailySpecial":                              reflect.ValueOf((*cl.DailySpecial)(nil)),
		"DailyTask":                                 reflect.ValueOf((*cl.DailyTask)(nil)),
		"DailyWish":                                 reflect.ValueOf((*cl.DailyWish)(nil)),
		"DailyWishActivityInfo":                     reflect.ValueOf((*cl.DailyWishActivityInfo)(nil)),
		"DailyWishAwardInfo":                        reflect.ValueOf((*cl.DailyWishAwardInfo)(nil)),
		"Disorderland":                              reflect.ValueOf((*cl.Disorderland)(nil)),
		"DisorderlandMap":                           reflect.ValueOf((*cl.DisorderlandMap)(nil)),
		"DisorderlandNode":                          reflect.ValueOf((*cl.DisorderlandNode)(nil)),
		"Dispatch":                                  reflect.ValueOf((*cl.Dispatch)(nil)),
		"DispatchCD":                                reflect.ValueOf((*cl.DispatchCD)(nil)),
		"DispatchLevelInfo":                         reflect.ValueOf((*cl.DispatchLevelInfo)(nil)),
		"DispatchTask":                              reflect.ValueOf((*cl.DispatchTask)(nil)),
		"DivineDemon":                               reflect.ValueOf((*cl.DivineDemon)(nil)),
		"DivineDemonOpenActivity":                   reflect.ValueOf((*cl.DivineDemonOpenActivity)(nil)),
		"DivineDemonSummon":                         reflect.ValueOf((*cl.DivineDemonSummon)(nil)),
		"DivineDemonSummonRedCard":                  reflect.ValueOf((*cl.DivineDemonSummonRedCard)(nil)),
		"DivineDemonSummonV2":                       reflect.ValueOf((*cl.DivineDemonSummonV2)(nil)),
		"DivineDemonTask":                           reflect.ValueOf((*cl.DivineDemonTask)(nil)),
		"DivineDemonWishRecord":                     reflect.ValueOf((*cl.DivineDemonWishRecord)(nil)),
		"DropActivity":                              reflect.ValueOf((*cl.DropActivity)(nil)),
		"DropActivityBase":                          reflect.ValueOf((*cl.DropActivityBase)(nil)),
		"DropActivityExchange":                      reflect.ValueOf((*cl.DropActivityExchange)(nil)),
		"DropActivityExchangeRule":                  reflect.ValueOf((*cl.DropActivityExchangeRule)(nil)),
		"Duel":                                      reflect.ValueOf((*cl.Duel)(nil)),
		"Dungeon":                                   reflect.ValueOf((*cl.Dungeon)(nil)),
		"EmblemBlessingInfo":                        reflect.ValueOf((*cl.EmblemBlessingInfo)(nil)),
		"EmblemGrowTransfer":                        reflect.ValueOf((*cl.EmblemGrowTransfer)(nil)),
		"EmblemInfo":                                reflect.ValueOf((*cl.EmblemInfo)(nil)),
		"EmblemLevelUpInfo":                         reflect.ValueOf((*cl.EmblemLevelUpInfo)(nil)),
		"EmblemSuccinctInfo":                        reflect.ValueOf((*cl.EmblemSuccinctInfo)(nil)),
		"Empty":                                     reflect.ValueOf((*cl.Empty)(nil)),
		"Equipment":                                 reflect.ValueOf((*cl.Equipment)(nil)),
		"ExpiredItem":                               reflect.ValueOf((*cl.ExpiredItem)(nil)),
		"ExpiredItems":                              reflect.ValueOf((*cl.ExpiredItems)(nil)),
		"FirstGiftRewardStatus":                     reflect.ValueOf((*cl.FirstGiftRewardStatus)(nil)),
		"FirstRankValue":                            reflect.ValueOf((*cl.FirstRankValue)(nil)),
		"Flower":                                    reflect.ValueOf((*cl.Flower)(nil)),
		"FlowerGuide":                               reflect.ValueOf((*cl.FlowerGuide)(nil)),
		"FlowerGuildBuff":                           reflect.ValueOf((*cl.FlowerGuildBuff)(nil)),
		"FlowerJungleOccupy":                        reflect.ValueOf((*cl.FlowerJungleOccupy)(nil)),
		"FlowerLog":                                 reflect.ValueOf((*cl.FlowerLog)(nil)),
		"FlowerLog2Client":                          reflect.ValueOf((*cl.FlowerLog2Client)(nil)),
		"FlowerLogDetail":                           reflect.ValueOf((*cl.FlowerLogDetail)(nil)),
		"FlowerLogSpecific":                         reflect.ValueOf((*cl.FlowerLogSpecific)(nil)),
		"FlowerNewLogTip":                           reflect.ValueOf((*cl.FlowerNewLogTip)(nil)),
		"FlowerOccupy":                              reflect.ValueOf((*cl.FlowerOccupy)(nil)),
		"FlowerOccupyAssist":                        reflect.ValueOf((*cl.FlowerOccupyAssist)(nil)),
		"FlowerOccupyAward":                         reflect.ValueOf((*cl.FlowerOccupyAward)(nil)),
		"FlowerOccupyHistory":                       reflect.ValueOf((*cl.FlowerOccupyHistory)(nil)),
		"FlowerOccupyLog":                           reflect.ValueOf((*cl.FlowerOccupyLog)(nil)),
		"FlowerOccupyLogDeletes":                    reflect.ValueOf((*cl.FlowerOccupyLogDeletes)(nil)),
		"FlowerOccupyLogs":                          reflect.ValueOf((*cl.FlowerOccupyLogs)(nil)),
		"FlowerOccupyNum":                           reflect.ValueOf((*cl.FlowerOccupyNum)(nil)),
		"FlowerPlant":                               reflect.ValueOf((*cl.FlowerPlant)(nil)),
		"FlowerSeed":                                reflect.ValueOf((*cl.FlowerSeed)(nil)),
		"FlowerSnatch":                              reflect.ValueOf((*cl.FlowerSnatch)(nil)),
		"FlowerSnatchEnemy":                         reflect.ValueOf((*cl.FlowerSnatchEnemy)(nil)),
		"FlowerSnatchLog":                           reflect.ValueOf((*cl.FlowerSnatchLog)(nil)),
		"FlowerSnatchLogDeletes":                    reflect.ValueOf((*cl.FlowerSnatchLogDeletes)(nil)),
		"FlowerSnatchLogs":                          reflect.ValueOf((*cl.FlowerSnatchLogs)(nil)),
		"Flowerbed":                                 reflect.ValueOf((*cl.Flowerbed)(nil)),
		"Forecast":                                  reflect.ValueOf((*cl.Forecast)(nil)),
		"Forest":                                    reflect.ValueOf((*cl.Forest)(nil)),
		"ForestLog":                                 reflect.ValueOf((*cl.ForestLog)(nil)),
		"ForestLogDeletes":                          reflect.ValueOf((*cl.ForestLogDeletes)(nil)),
		"ForestLogs":                                reflect.ValueOf((*cl.ForestLogs)(nil)),
		"ForestTree":                                reflect.ValueOf((*cl.ForestTree)(nil)),
		"Formation":                                 reflect.ValueOf((*cl.Formation)(nil)),
		"FormationArtifactInfo":                     reflect.ValueOf((*cl.FormationArtifactInfo)(nil)),
		"FormationInfo":                             reflect.ValueOf((*cl.FormationInfo)(nil)),
		"FormationRemainInfo":                       reflect.ValueOf((*cl.FormationRemainInfo)(nil)),
		"FormationRiteInfo":                         reflect.ValueOf((*cl.FormationRiteInfo)(nil)),
		"FormationRitePower":                        reflect.ValueOf((*cl.FormationRitePower)(nil)),
		"FormationSidUid":                           reflect.ValueOf((*cl.FormationSidUid)(nil)),
		"FormationSidUidPower":                      reflect.ValueOf((*cl.FormationSidUidPower)(nil)),
		"FormationTeamInfo":                         reflect.ValueOf((*cl.FormationTeamInfo)(nil)),
		"FragmentComposeUnit":                       reflect.ValueOf((*cl.FragmentComposeUnit)(nil)),
		"Friend":                                    reflect.ValueOf((*cl.Friend)(nil)),
		"FriendRequest":                             reflect.ValueOf((*cl.FriendRequest)(nil)),
		"FunctionShowcase":                          reflect.ValueOf((*cl.FunctionShowcase)(nil)),
		"GSTArenaFightRecord":                       reflect.ValueOf((*cl.GSTArenaFightRecord)(nil)),
		"GSTArenaGuildInfo":                         reflect.ValueOf((*cl.GSTArenaGuildInfo)(nil)),
		"GSTArenaGuildRank":                         reflect.ValueOf((*cl.GSTArenaGuildRank)(nil)),
		"GSTArenaGuildUserRank":                     reflect.ValueOf((*cl.GSTArenaGuildUserRank)(nil)),
		"GSTArenaInfo":                              reflect.ValueOf((*cl.GSTArenaInfo)(nil)),
		"GSTArenaState":                             reflect.ValueOf((*cl.GSTArenaState)(nil)),
		"GSTArenaVoteRecord":                        reflect.ValueOf((*cl.GSTArenaVoteRecord)(nil)),
		"GSTAssistOre":                              reflect.ValueOf((*cl.GSTAssistOre)(nil)),
		"GSTAssistOreData":                          reflect.ValueOf((*cl.GSTAssistOreData)(nil)),
		"GSTBoss":                                   reflect.ValueOf((*cl.GSTBoss)(nil)),
		"GSTBossFightRank":                          reflect.ValueOf((*cl.GSTBossFightRank)(nil)),
		"GSTBossFightRecord":                        reflect.ValueOf((*cl.GSTBossFightRecord)(nil)),
		"GSTBossGroup":                              reflect.ValueOf((*cl.GSTBossGroup)(nil)),
		"GSTBossRecordType":                         reflect.ValueOf((*cl.GSTBossRecordType)(nil)),
		"GSTBossUser":                               reflect.ValueOf((*cl.GSTBossUser)(nil)),
		"GSTBuild":                                  reflect.ValueOf((*cl.GSTBuild)(nil)),
		"GSTBuildDispatchHero":                      reflect.ValueOf((*cl.GSTBuildDispatchHero)(nil)),
		"GSTBuildDonate":                            reflect.ValueOf((*cl.GSTBuildDonate)(nil)),
		"GSTBuildDonateDetails":                     reflect.ValueOf((*cl.GSTBuildDonateDetails)(nil)),
		"GSTBuildDonateRank":                        reflect.ValueOf((*cl.GSTBuildDonateRank)(nil)),
		"GSTBuildDonateRankClient":                  reflect.ValueOf((*cl.GSTBuildDonateRankClient)(nil)),
		"GSTBuildTaskInfo":                          reflect.ValueOf((*cl.GSTBuildTaskInfo)(nil)),
		"GSTBuildUserDispatchHeroes":                reflect.ValueOf((*cl.GSTBuildUserDispatchHeroes)(nil)),
		"GSTBuildUserDispatchHeroesClient":          reflect.ValueOf((*cl.GSTBuildUserDispatchHeroesClient)(nil)),
		"GSTChallengeFightLog":                      reflect.ValueOf((*cl.GSTChallengeFightLog)(nil)),
		"GSTChallengeFightLogDeletes":               reflect.ValueOf((*cl.GSTChallengeFightLogDeletes)(nil)),
		"GSTChallengeFightLogs":                     reflect.ValueOf((*cl.GSTChallengeFightLogs)(nil)),
		"GSTChallengeGuildRank":                     reflect.ValueOf((*cl.GSTChallengeGuildRank)(nil)),
		"GSTChallengeGuildUserRank":                 reflect.ValueOf((*cl.GSTChallengeGuildUserRank)(nil)),
		"GSTChallengeLastTop":                       reflect.ValueOf((*cl.GSTChallengeLastTop)(nil)),
		"GSTChallengeMatchInfo":                     reflect.ValueOf((*cl.GSTChallengeMatchInfo)(nil)),
		"GSTChallengeMatched":                       reflect.ValueOf((*cl.GSTChallengeMatched)(nil)),
		"GSTChallengeRecord":                        reflect.ValueOf((*cl.GSTChallengeRecord)(nil)),
		"GSTChallengeShowLog":                       reflect.ValueOf((*cl.GSTChallengeShowLog)(nil)),
		"GSTChallengeTaskData":                      reflect.ValueOf((*cl.GSTChallengeTaskData)(nil)),
		"GSTChallengeTeamInfo":                      reflect.ValueOf((*cl.GSTChallengeTeamInfo)(nil)),
		"GSTClientDonate":                           reflect.ValueOf((*cl.GSTClientDonate)(nil)),
		"GSTComplexData":                            reflect.ValueOf((*cl.GSTComplexData)(nil)),
		"GSTDonate":                                 reflect.ValueOf((*cl.GSTDonate)(nil)),
		"GSTDragonBattle":                           reflect.ValueOf((*cl.GSTDragonBattle)(nil)),
		"GSTDragonBattleData":                       reflect.ValueOf((*cl.GSTDragonBattleData)(nil)),
		"GSTDragonBattleResult":                     reflect.ValueOf((*cl.GSTDragonBattleResult)(nil)),
		"GSTDragonCultivation":                      reflect.ValueOf((*cl.GSTDragonCultivation)(nil)),
		"GSTDragonFightRank":                        reflect.ValueOf((*cl.GSTDragonFightRank)(nil)),
		"GSTDragonFightRecord":                      reflect.ValueOf((*cl.GSTDragonFightRecord)(nil)),
		"GSTDragonFightRecordTeamData":              reflect.ValueOf((*cl.GSTDragonFightRecordTeamData)(nil)),
		"GSTDragonFightRecordType":                  reflect.ValueOf((*cl.GSTDragonFightRecordType)(nil)),
		"GSTDragonGuild":                            reflect.ValueOf((*cl.GSTDragonGuild)(nil)),
		"GSTDragonSkill":                            reflect.ValueOf((*cl.GSTDragonSkill)(nil)),
		"GSTDragonTaskInfo":                         reflect.ValueOf((*cl.GSTDragonTaskInfo)(nil)),
		"GSTDragonUser":                             reflect.ValueOf((*cl.GSTDragonUser)(nil)),
		"GSTDragonUserRoundData":                    reflect.ValueOf((*cl.GSTDragonUserRoundData)(nil)),
		"GSTDragonUserRoundMaxDamage":               reflect.ValueOf((*cl.GSTDragonUserRoundMaxDamage)(nil)),
		"GSTDragonUserSeasonData":                   reflect.ValueOf((*cl.GSTDragonUserSeasonData)(nil)),
		"GSTDragonUserTeamData":                     reflect.ValueOf((*cl.GSTDragonUserTeamData)(nil)),
		"GSTFightResult":                            reflect.ValueOf((*cl.GSTFightResult)(nil)),
		"GSTFightTeam":                              reflect.ValueOf((*cl.GSTFightTeam)(nil)),
		"GSTFormationTeamInfo":                      reflect.ValueOf((*cl.GSTFormationTeamInfo)(nil)),
		"GSTGetLogArenaFightRecord":                 reflect.ValueOf((*cl.GSTGetLogArenaFightRecord)(nil)),
		"GSTGetLogBuildDonateDetails":               reflect.ValueOf((*cl.GSTGetLogBuildDonateDetails)(nil)),
		"GSTGetLogChallengeRecord":                  reflect.ValueOf((*cl.GSTGetLogChallengeRecord)(nil)),
		"GSTGetLogDragonFightRecord":                reflect.ValueOf((*cl.GSTGetLogDragonFightRecord)(nil)),
		"GSTGetLogGSTBossFightRecord":               reflect.ValueOf((*cl.GSTGetLogGSTBossFightRecord)(nil)),
		"GSTGetLogGround":                           reflect.ValueOf((*cl.GSTGetLogGround)(nil)),
		"GSTGetLogGuild":                            reflect.ValueOf((*cl.GSTGetLogGuild)(nil)),
		"GSTGetLogGuildMobilizationRank":            reflect.ValueOf((*cl.GSTGetLogGuildMobilizationRank)(nil)),
		"GSTGetLogGuildSkill":                       reflect.ValueOf((*cl.GSTGetLogGuildSkill)(nil)),
		"GSTGetLogOreAssist":                        reflect.ValueOf((*cl.GSTGetLogOreAssist)(nil)),
		"GSTGetLogOreChange":                        reflect.ValueOf((*cl.GSTGetLogOreChange)(nil)),
		"GSTGetLogPC":                               reflect.ValueOf((*cl.GSTGetLogPC)(nil)),
		"GSTGetLogPF":                               reflect.ValueOf((*cl.GSTGetLogPF)(nil)),
		"GSTGetLogReq":                              reflect.ValueOf((*cl.GSTGetLogReq)(nil)),
		"GSTGetLogReqData":                          reflect.ValueOf((*cl.GSTGetLogReqData)(nil)),
		"GSTGetLogReqType":                          reflect.ValueOf((*cl.GSTGetLogReqType)(nil)),
		"GSTGetLogTech":                             reflect.ValueOf((*cl.GSTGetLogTech)(nil)),
		"GSTGoddessBless":                           reflect.ValueOf((*cl.GSTGoddessBless)(nil)),
		"GSTGroundInfo":                             reflect.ValueOf((*cl.GSTGroundInfo)(nil)),
		"GSTGroundTeamData":                         reflect.ValueOf((*cl.GSTGroundTeamData)(nil)),
		"GSTGroundTeamInfo":                         reflect.ValueOf((*cl.GSTGroundTeamInfo)(nil)),
		"GSTGuildScoreType":                         reflect.ValueOf((*cl.GSTGuildScoreType)(nil)),
		"GSTGuildTech":                              reflect.ValueOf((*cl.GSTGuildTech)(nil)),
		"GSTGuildUser":                              reflect.ValueOf((*cl.GSTGuildUser)(nil)),
		"GSTGuildUserBase":                          reflect.ValueOf((*cl.GSTGuildUserBase)(nil)),
		"GSTGuildUserChallenge":                     reflect.ValueOf((*cl.GSTGuildUserChallenge)(nil)),
		"GSTGuildUserOre":                           reflect.ValueOf((*cl.GSTGuildUserOre)(nil)),
		"GSTGuildUserOreData":                       reflect.ValueOf((*cl.GSTGuildUserOreData)(nil)),
		"GSTGuildUserOreTimes":                      reflect.ValueOf((*cl.GSTGuildUserOreTimes)(nil)),
		"GSTGuildUserRoundSettle":                   reflect.ValueOf((*cl.GSTGuildUserRoundSettle)(nil)),
		"GSTGuildUserTeamFightInfo":                 reflect.ValueOf((*cl.GSTGuildUserTeamFightInfo)(nil)),
		"GSTGuildUserTech":                          reflect.ValueOf((*cl.GSTGuildUserTech)(nil)),
		"GSTLastLRoundGuildRank":                    reflect.ValueOf((*cl.GSTLastLRoundGuildRank)(nil)),
		"GSTLogData":                                reflect.ValueOf((*cl.GSTLogData)(nil)),
		"GSTLogDragonSkill":                         reflect.ValueOf((*cl.GSTLogDragonSkill)(nil)),
		"GSTLogDragonSkillGuildInfo":                reflect.ValueOf((*cl.GSTLogDragonSkillGuildInfo)(nil)),
		"GSTLogFightTeam":                           reflect.ValueOf((*cl.GSTLogFightTeam)(nil)),
		"GSTLogGroupGuildSettle":                    reflect.ValueOf((*cl.GSTLogGroupGuildSettle)(nil)),
		"GSTLogGroupSettle":                         reflect.ValueOf((*cl.GSTLogGroupSettle)(nil)),
		"GSTLogGuildMobilization":                   reflect.ValueOf((*cl.GSTLogGuildMobilization)(nil)),
		"GSTLogGuildSkill":                          reflect.ValueOf((*cl.GSTLogGuildSkill)(nil)),
		"GSTLogInfo":                                reflect.ValueOf((*cl.GSTLogInfo)(nil)),
		"GSTLogOreAssist":                           reflect.ValueOf((*cl.GSTLogOreAssist)(nil)),
		"GSTLogOreChange":                           reflect.ValueOf((*cl.GSTLogOreChange)(nil)),
		"GSTLogTech":                                reflect.ValueOf((*cl.GSTLogTech)(nil)),
		"GSTLogType":                                reflect.ValueOf((*cl.GSTLogType)(nil)),
		"GSTManyLog":                                reflect.ValueOf((*cl.GSTManyLog)(nil)),
		"GSTMapInfo":                                reflect.ValueOf((*cl.GSTMapInfo)(nil)),
		"GSTRoundSettle":                            reflect.ValueOf((*cl.GSTRoundSettle)(nil)),
		"GSTSetFormationTeamInfo":                   reflect.ValueOf((*cl.GSTSetFormationTeamInfo)(nil)),
		"GSTSimpleData":                             reflect.ValueOf((*cl.GSTSimpleData)(nil)),
		"GSTSimpleGuild":                            reflect.ValueOf((*cl.GSTSimpleGuild)(nil)),
		"GSTSkillType":                              reflect.ValueOf((*cl.GSTSkillType)(nil)),
		"GSTSta":                                    reflect.ValueOf((*cl.GSTSta)(nil)),
		"GSTTaskInfo":                               reflect.ValueOf((*cl.GSTTaskInfo)(nil)),
		"GSTTeamData":                               reflect.ValueOf((*cl.GSTTeamData)(nil)),
		"GSTTeamHeroInfo":                           reflect.ValueOf((*cl.GSTTeamHeroInfo)(nil)),
		"GSTTeamOperate":                            reflect.ValueOf((*cl.GSTTeamOperate)(nil)),
		"GSTTechData":                               reflect.ValueOf((*cl.GSTTechData)(nil)),
		"GSTTechRankData":                           reflect.ValueOf((*cl.GSTTechRankData)(nil)),
		"GSTTechTaskData":                           reflect.ValueOf((*cl.GSTTechTaskData)(nil)),
		"GSTUserScoreRankShow":                      reflect.ValueOf((*cl.GSTUserScoreRankShow)(nil)),
		"GSTUserScoreType":                          reflect.ValueOf((*cl.GSTUserScoreType)(nil)),
		"GSTUserWinsRank":                           reflect.ValueOf((*cl.GSTUserWinsRank)(nil)),
		"GST_STAGE":                                 reflect.ValueOf((*cl.GST_STAGE)(nil)),
		"GST_TEAM_STAGE":                            reflect.ValueOf((*cl.GST_TEAM_STAGE)(nil)),
		"Gem":                                       reflect.ValueOf((*cl.Gem)(nil)),
		"GemAttr":                                   reflect.ValueOf((*cl.GemAttr)(nil)),
		"GemInfo":                                   reflect.ValueOf((*cl.GemInfo)(nil)),
		"GetLikeListParam":                          reflect.ValueOf((*cl.GetLikeListParam)(nil)),
		"GlobalAttr":                                reflect.ValueOf((*cl.GlobalAttr)(nil)),
		"GlobalScore":                               reflect.ValueOf((*cl.GlobalScore)(nil)),
		"GodPresent":                                reflect.ValueOf((*cl.GodPresent)(nil)),
		"GodPresentClient":                          reflect.ValueOf((*cl.GodPresentClient)(nil)),
		"GodPresentNew":                             reflect.ValueOf((*cl.GodPresentNew)(nil)),
		"GodPresentSummon":                          reflect.ValueOf((*cl.GodPresentSummon)(nil)),
		"GodPresents":                               reflect.ValueOf((*cl.GodPresents)(nil)),
		"GodPresentsNew":                            reflect.ValueOf((*cl.GodPresentsNew)(nil)),
		"Goddess":                                   reflect.ValueOf((*cl.Goddess)(nil)),
		"GoddessContractInfo":                       reflect.ValueOf((*cl.GoddessContractInfo)(nil)),
		"GoldBuy":                                   reflect.ValueOf((*cl.GoldBuy)(nil)),
		"GoldChest":                                 reflect.ValueOf((*cl.GoldChest)(nil)),
		"GoodsState":                                reflect.ValueOf((*cl.GoodsState)(nil)),
		"GstGroupUserRankValue":                     reflect.ValueOf((*cl.GstGroupUserRankValue)(nil)),
		"GstGuildAddScore":                          reflect.ValueOf((*cl.GstGuildAddScore)(nil)),
		"GstLogGeneral":                             reflect.ValueOf((*cl.GstLogGeneral)(nil)),
		"GstTripleKillInfo":                         reflect.ValueOf((*cl.GstTripleKillInfo)(nil)),
		"GstTripleKillTeamInfo":                     reflect.ValueOf((*cl.GstTripleKillTeamInfo)(nil)),
		"Guidance":                                  reflect.ValueOf((*cl.Guidance)(nil)),
		"GuidanceGroup":                             reflect.ValueOf((*cl.GuidanceGroup)(nil)),
		"GuildChest":                                reflect.ValueOf((*cl.GuildChest)(nil)),
		"GuildChestData":                            reflect.ValueOf((*cl.GuildChestData)(nil)),
		"GuildChestFinishRecv":                      reflect.ValueOf((*cl.GuildChestFinishRecv)(nil)),
		"GuildChestSlotDetail":                      reflect.ValueOf((*cl.GuildChestSlotDetail)(nil)),
		"GuildCombineApplyItem":                     reflect.ValueOf((*cl.GuildCombineApplyItem)(nil)),
		"GuildCombineStatus":                        reflect.ValueOf((*cl.GuildCombineStatus)(nil)),
		"GuildDonateDeletes":                        reflect.ValueOf((*cl.GuildDonateDeletes)(nil)),
		"GuildDonateLogInfo":                        reflect.ValueOf((*cl.GuildDonateLogInfo)(nil)),
		"GuildDonateLogs":                           reflect.ValueOf((*cl.GuildDonateLogs)(nil)),
		"GuildDungeonBossBoxInfo":                   reflect.ValueOf((*cl.GuildDungeonBossBoxInfo)(nil)),
		"GuildDungeonBossInfo":                      reflect.ValueOf((*cl.GuildDungeonBossInfo)(nil)),
		"GuildDungeonChapterRankTopGuild":           reflect.ValueOf((*cl.GuildDungeonChapterRankTopGuild)(nil)),
		"GuildDungeonHallOfFameInfo":                reflect.ValueOf((*cl.GuildDungeonHallOfFameInfo)(nil)),
		"GuildDungeonLog":                           reflect.ValueOf((*cl.GuildDungeonLog)(nil)),
		"GuildDungeonLogDeletes":                    reflect.ValueOf((*cl.GuildDungeonLogDeletes)(nil)),
		"GuildDungeonLogs":                          reflect.ValueOf((*cl.GuildDungeonLogs)(nil)),
		"GuildDungeonMemberFightInfo":               reflect.ValueOf((*cl.GuildDungeonMemberFightInfo)(nil)),
		"GuildDungeonMessageBoardLog":               reflect.ValueOf((*cl.GuildDungeonMessageBoardLog)(nil)),
		"GuildDungeonMessageBoardLogDeletes":        reflect.ValueOf((*cl.GuildDungeonMessageBoardLogDeletes)(nil)),
		"GuildDungeonMessageBoardLogs":              reflect.ValueOf((*cl.GuildDungeonMessageBoardLogs)(nil)),
		"GuildDungeonRoomRankInfo":                  reflect.ValueOf((*cl.GuildDungeonRoomRankInfo)(nil)),
		"GuildDungeonSelfBossInfo":                  reflect.ValueOf((*cl.GuildDungeonSelfBossInfo)(nil)),
		"GuildDungeonStrategyEffect":                reflect.ValueOf((*cl.GuildDungeonStrategyEffect)(nil)),
		"GuildDungeonTopDailyDamageUser":            reflect.ValueOf((*cl.GuildDungeonTopDailyDamageUser)(nil)),
		"GuildInfo":                                 reflect.ValueOf((*cl.GuildInfo)(nil)),
		"GuildLeaveCount":                           reflect.ValueOf((*cl.GuildLeaveCount)(nil)),
		"GuildLogDeletes":                           reflect.ValueOf((*cl.GuildLogDeletes)(nil)),
		"GuildLogInfo":                              reflect.ValueOf((*cl.GuildLogInfo)(nil)),
		"GuildLogs":                                 reflect.ValueOf((*cl.GuildLogs)(nil)),
		"GuildMedal":                                reflect.ValueOf((*cl.GuildMedal)(nil)),
		"GuildMedalLiked":                           reflect.ValueOf((*cl.GuildMedalLiked)(nil)),
		"GuildMedalSeasonArenaBak":                  reflect.ValueOf((*cl.GuildMedalSeasonArenaBak)(nil)),
		"GuildMemberInfo":                           reflect.ValueOf((*cl.GuildMemberInfo)(nil)),
		"GuildMobilizationGuildRank":                reflect.ValueOf((*cl.GuildMobilizationGuildRank)(nil)),
		"GuildMobilizationGuildTask":                reflect.ValueOf((*cl.GuildMobilizationGuildTask)(nil)),
		"GuildMobilizationMemberTask":               reflect.ValueOf((*cl.GuildMobilizationMemberTask)(nil)),
		"GuildMobilizationPersonalRank":             reflect.ValueOf((*cl.GuildMobilizationPersonalRank)(nil)),
		"GuildMobilizationTaskData":                 reflect.ValueOf((*cl.GuildMobilizationTaskData)(nil)),
		"GuildMobilizationTaskLog":                  reflect.ValueOf((*cl.GuildMobilizationTaskLog)(nil)),
		"GuildMobilizationTaskProgress":             reflect.ValueOf((*cl.GuildMobilizationTaskProgress)(nil)),
		"GuildMobilizationTaskStatus":               reflect.ValueOf((*cl.GuildMobilizationTaskStatus)(nil)),
		"GuildSnapshot":                             reflect.ValueOf((*cl.GuildSnapshot)(nil)),
		"GuildTalent":                               reflect.ValueOf((*cl.GuildTalent)(nil)),
		"Handbook":                                  reflect.ValueOf((*cl.Handbook)(nil)),
		"Handbooks":                                 reflect.ValueOf((*cl.Handbooks)(nil)),
		"Hero":                                      reflect.ValueOf((*cl.Hero)(nil)),
		"HeroBody":                                  reflect.ValueOf((*cl.HeroBody)(nil)),
		"HeroHandbook":                              reflect.ValueOf((*cl.HeroHandbook)(nil)),
		"HeroStarUpCosts":                           reflect.ValueOf((*cl.HeroStarUpCosts)(nil)),
		"HeroTag":                                   reflect.ValueOf((*cl.HeroTag)(nil)),
		"ID":                                        reflect.ValueOf((*cl.ID)(nil)),
		"L2C_Accusation":                            reflect.ValueOf((*cl.L2C_Accusation)(nil)),
		"L2C_ActivityComplianceGetData":             reflect.ValueOf((*cl.L2C_ActivityComplianceGetData)(nil)),
		"L2C_ActivityComplianceLikeRank":            reflect.ValueOf((*cl.L2C_ActivityComplianceLikeRank)(nil)),
		"L2C_ActivityComplianceRecvAward":           reflect.ValueOf((*cl.L2C_ActivityComplianceRecvAward)(nil)),
		"L2C_ActivityComplianceScoreUpdate":         reflect.ValueOf((*cl.L2C_ActivityComplianceScoreUpdate)(nil)),
		"L2C_ActivityCouponBuy":                     reflect.ValueOf((*cl.L2C_ActivityCouponBuy)(nil)),
		"L2C_ActivityCouponGetData":                 reflect.ValueOf((*cl.L2C_ActivityCouponGetData)(nil)),
		"L2C_ActivityCouponXmlUpdate":               reflect.ValueOf((*cl.L2C_ActivityCouponXmlUpdate)(nil)),
		"L2C_ActivityGoddessActive":                 reflect.ValueOf((*cl.L2C_ActivityGoddessActive)(nil)),
		"L2C_ActivityGoddessRecvRecoveryAward":      reflect.ValueOf((*cl.L2C_ActivityGoddessRecvRecoveryAward)(nil)),
		"L2C_ActivityLifelongGiftUpdate":            reflect.ValueOf((*cl.L2C_ActivityLifelongGiftUpdate)(nil)),
		"L2C_ActivityMixGetData":                    reflect.ValueOf((*cl.L2C_ActivityMixGetData)(nil)),
		"L2C_ActivityRechargeBuy":                   reflect.ValueOf((*cl.L2C_ActivityRechargeBuy)(nil)),
		"L2C_ActivityRechargeGet":                   reflect.ValueOf((*cl.L2C_ActivityRechargeGet)(nil)),
		"L2C_ActivityReturnGetData":                 reflect.ValueOf((*cl.L2C_ActivityReturnGetData)(nil)),
		"L2C_ActivityReturnTakeLoginAwards":         reflect.ValueOf((*cl.L2C_ActivityReturnTakeLoginAwards)(nil)),
		"L2C_ActivityScheduleGetData":               reflect.ValueOf((*cl.L2C_ActivityScheduleGetData)(nil)),
		"L2C_ActivityStoryExchange":                 reflect.ValueOf((*cl.L2C_ActivityStoryExchange)(nil)),
		"L2C_ActivityStoryFight":                    reflect.ValueOf((*cl.L2C_ActivityStoryFight)(nil)),
		"L2C_ActivityStoryGetData":                  reflect.ValueOf((*cl.L2C_ActivityStoryGetData)(nil)),
		"L2C_ActivityStoryLoginAward":               reflect.ValueOf((*cl.L2C_ActivityStoryLoginAward)(nil)),
		"L2C_ActivitySumActivePuzzleCell":           reflect.ValueOf((*cl.L2C_ActivitySumActivePuzzleCell)(nil)),
		"L2C_ActivitySumExchange":                   reflect.ValueOf((*cl.L2C_ActivitySumExchange)(nil)),
		"L2C_ActivitySumFeed":                       reflect.ValueOf((*cl.L2C_ActivitySumFeed)(nil)),
		"L2C_ActivitySumGetData":                    reflect.ValueOf((*cl.L2C_ActivitySumGetData)(nil)),
		"L2C_ActivitySumLoginReward":                reflect.ValueOf((*cl.L2C_ActivitySumLoginReward)(nil)),
		"L2C_ActivitySumMakeGift":                   reflect.ValueOf((*cl.L2C_ActivitySumMakeGift)(nil)),
		"L2C_ActivitySumShootGameFight":             reflect.ValueOf((*cl.L2C_ActivitySumShootGameFight)(nil)),
		"L2C_ActivitySumTaskReward":                 reflect.ValueOf((*cl.L2C_ActivitySumTaskReward)(nil)),
		"L2C_ActivitySumTaskUpdate":                 reflect.ValueOf((*cl.L2C_ActivitySumTaskUpdate)(nil)),
		"L2C_ActivitySumTicketBuy":                  reflect.ValueOf((*cl.L2C_ActivitySumTicketBuy)(nil)),
		"L2C_ActivitySumTurnTableSelectBuff":        reflect.ValueOf((*cl.L2C_ActivitySumTurnTableSelectBuff)(nil)),
		"L2C_ActivitySumTurnTableSummon":            reflect.ValueOf((*cl.L2C_ActivitySumTurnTableSummon)(nil)),
		"L2C_ActivityTurnTableGetData":              reflect.ValueOf((*cl.L2C_ActivityTurnTableGetData)(nil)),
		"L2C_ActivityTurnTableRecvLogin":            reflect.ValueOf((*cl.L2C_ActivityTurnTableRecvLogin)(nil)),
		"L2C_ActivityTurnTableRecvTask":             reflect.ValueOf((*cl.L2C_ActivityTurnTableRecvTask)(nil)),
		"L2C_ActivityTurnTableSelectBuff":           reflect.ValueOf((*cl.L2C_ActivityTurnTableSelectBuff)(nil)),
		"L2C_ActivityTurnTableSummon":               reflect.ValueOf((*cl.L2C_ActivityTurnTableSummon)(nil)),
		"L2C_ActivityTurnTableTicketBuy":            reflect.ValueOf((*cl.L2C_ActivityTurnTableTicketBuy)(nil)),
		"L2C_ActivityTurnTableUpdateTask":           reflect.ValueOf((*cl.L2C_ActivityTurnTableUpdateTask)(nil)),
		"L2C_AddPurchaseNum":                        reflect.ValueOf((*cl.L2C_AddPurchaseNum)(nil)),
		"L2C_AnnouncementGetData":                   reflect.ValueOf((*cl.L2C_AnnouncementGetData)(nil)),
		"L2C_AnnouncementUpdate":                    reflect.ValueOf((*cl.L2C_AnnouncementUpdate)(nil)),
		"L2C_ArenaFight":                            reflect.ValueOf((*cl.L2C_ArenaFight)(nil)),
		"L2C_ArenaInfo":                             reflect.ValueOf((*cl.L2C_ArenaInfo)(nil)),
		"L2C_ArenaLike":                             reflect.ValueOf((*cl.L2C_ArenaLike)(nil)),
		"L2C_ArenaLogList":                          reflect.ValueOf((*cl.L2C_ArenaLogList)(nil)),
		"L2C_ArenaRank":                             reflect.ValueOf((*cl.L2C_ArenaRank)(nil)),
		"L2C_ArenaRecvAward":                        reflect.ValueOf((*cl.L2C_ArenaRecvAward)(nil)),
		"L2C_ArenaRefresh":                          reflect.ValueOf((*cl.L2C_ArenaRefresh)(nil)),
		"L2C_ArtifactActivate":                      reflect.ValueOf((*cl.L2C_ArtifactActivate)(nil)),
		"L2C_ArtifactDebutGetActivity":              reflect.ValueOf((*cl.L2C_ArtifactDebutGetActivity)(nil)),
		"L2C_ArtifactDebutMainInfo":                 reflect.ValueOf((*cl.L2C_ArtifactDebutMainInfo)(nil)),
		"L2C_ArtifactDebutOpenPuzzle":               reflect.ValueOf((*cl.L2C_ArtifactDebutOpenPuzzle)(nil)),
		"L2C_ArtifactDebutRecvActAward":             reflect.ValueOf((*cl.L2C_ArtifactDebutRecvActAward)(nil)),
		"L2C_ArtifactDebutRecvTaskAward":            reflect.ValueOf((*cl.L2C_ArtifactDebutRecvTaskAward)(nil)),
		"L2C_ArtifactDebutSetWish":                  reflect.ValueOf((*cl.L2C_ArtifactDebutSetWish)(nil)),
		"L2C_ArtifactDebutSummon":                   reflect.ValueOf((*cl.L2C_ArtifactDebutSummon)(nil)),
		"L2C_ArtifactDebutTestSummon":               reflect.ValueOf((*cl.L2C_ArtifactDebutTestSummon)(nil)),
		"L2C_ArtifactDebutUpdateActivity":           reflect.ValueOf((*cl.L2C_ArtifactDebutUpdateActivity)(nil)),
		"L2C_ArtifactDebutUpdateTask":               reflect.ValueOf((*cl.L2C_ArtifactDebutUpdateTask)(nil)),
		"L2C_ArtifactForge":                         reflect.ValueOf((*cl.L2C_ArtifactForge)(nil)),
		"L2C_ArtifactList":                          reflect.ValueOf((*cl.L2C_ArtifactList)(nil)),
		"L2C_ArtifactRevive":                        reflect.ValueOf((*cl.L2C_ArtifactRevive)(nil)),
		"L2C_ArtifactStarUp":                        reflect.ValueOf((*cl.L2C_ArtifactStarUp)(nil)),
		"L2C_ArtifactStrength":                      reflect.ValueOf((*cl.L2C_ArtifactStrength)(nil)),
		"L2C_AssistanceActivityGetAward":            reflect.ValueOf((*cl.L2C_AssistanceActivityGetAward)(nil)),
		"L2C_AssistanceActivityGetData":             reflect.ValueOf((*cl.L2C_AssistanceActivityGetData)(nil)),
		"L2C_AssistantGetData":                      reflect.ValueOf((*cl.L2C_AssistantGetData)(nil)),
		"L2C_AvatarGetInfo":                         reflect.ValueOf((*cl.L2C_AvatarGetInfo)(nil)),
		"L2C_AvatarNew":                             reflect.ValueOf((*cl.L2C_AvatarNew)(nil)),
		"L2C_AvatarSetIcon":                         reflect.ValueOf((*cl.L2C_AvatarSetIcon)(nil)),
		"L2C_BattleReport":                          reflect.ValueOf((*cl.L2C_BattleReport)(nil)),
		"L2C_BattleTest":                            reflect.ValueOf((*cl.L2C_BattleTest)(nil)),
		"L2C_BossRushBuyStamina":                    reflect.ValueOf((*cl.L2C_BossRushBuyStamina)(nil)),
		"L2C_BossRushFight":                         reflect.ValueOf((*cl.L2C_BossRushFight)(nil)),
		"L2C_BossRushGetData":                       reflect.ValueOf((*cl.L2C_BossRushGetData)(nil)),
		"L2C_BossRushTaskAward":                     reflect.ValueOf((*cl.L2C_BossRushTaskAward)(nil)),
		"L2C_BossRushTaskUpdate":                    reflect.ValueOf((*cl.L2C_BossRushTaskUpdate)(nil)),
		"L2C_BoxExchange":                           reflect.ValueOf((*cl.L2C_BoxExchange)(nil)),
		"L2C_BoxGet":                                reflect.ValueOf((*cl.L2C_BoxGet)(nil)),
		"L2C_BoxOpen":                               reflect.ValueOf((*cl.L2C_BoxOpen)(nil)),
		"L2C_CarnivalGetData":                       reflect.ValueOf((*cl.L2C_CarnivalGetData)(nil)),
		"L2C_CarnivalReceiveAward":                  reflect.ValueOf((*cl.L2C_CarnivalReceiveAward)(nil)),
		"L2C_CarnivalUpdate":                        reflect.ValueOf((*cl.L2C_CarnivalUpdate)(nil)),
		"L2C_Chat":                                  reflect.ValueOf((*cl.L2C_Chat)(nil)),
		"L2C_ChatCostShareCount":                    reflect.ValueOf((*cl.L2C_ChatCostShareCount)(nil)),
		"L2C_ChatGetLikeList":                       reflect.ValueOf((*cl.L2C_ChatGetLikeList)(nil)),
		"L2C_ChatGetMessages":                       reflect.ValueOf((*cl.L2C_ChatGetMessages)(nil)),
		"L2C_ChatGetPrivateMessageNum":              reflect.ValueOf((*cl.L2C_ChatGetPrivateMessageNum)(nil)),
		"L2C_ChatGetToken":                          reflect.ValueOf((*cl.L2C_ChatGetToken)(nil)),
		"L2C_ChatGroupUpdateNotify":                 reflect.ValueOf((*cl.L2C_ChatGroupUpdateNotify)(nil)),
		"L2C_ChatLike":                              reflect.ValueOf((*cl.L2C_ChatLike)(nil)),
		"L2C_ChatLikeNotify":                        reflect.ValueOf((*cl.L2C_ChatLikeNotify)(nil)),
		"L2C_ChatNotify":                            reflect.ValueOf((*cl.L2C_ChatNotify)(nil)),
		"L2C_ChatSyncChatTag":                       reflect.ValueOf((*cl.L2C_ChatSyncChatTag)(nil)),
		"L2C_ClientGetMultiLang":                    reflect.ValueOf((*cl.L2C_ClientGetMultiLang)(nil)),
		"L2C_ClientSetMultiLang":                    reflect.ValueOf((*cl.L2C_ClientSetMultiLang)(nil)),
		"L2C_CommonRankLike":                        reflect.ValueOf((*cl.L2C_CommonRankLike)(nil)),
		"L2C_ComplianceTasksGetData":                reflect.ValueOf((*cl.L2C_ComplianceTasksGetData)(nil)),
		"L2C_ComplianceTasksRecvTask":               reflect.ValueOf((*cl.L2C_ComplianceTasksRecvTask)(nil)),
		"L2C_ComplianceTasksUpdate":                 reflect.ValueOf((*cl.L2C_ComplianceTasksUpdate)(nil)),
		"L2C_CrystalActiveAchievement":              reflect.ValueOf((*cl.L2C_CrystalActiveAchievement)(nil)),
		"L2C_CrystalAddHero":                        reflect.ValueOf((*cl.L2C_CrystalAddHero)(nil)),
		"L2C_CrystalGetAllData":                     reflect.ValueOf((*cl.L2C_CrystalGetAllData)(nil)),
		"L2C_CrystalGetShareAttr":                   reflect.ValueOf((*cl.L2C_CrystalGetShareAttr)(nil)),
		"L2C_CrystalHeroesUpdate":                   reflect.ValueOf((*cl.L2C_CrystalHeroesUpdate)(nil)),
		"L2C_CrystalRemoveHero":                     reflect.ValueOf((*cl.L2C_CrystalRemoveHero)(nil)),
		"L2C_CrystalSpeedSlotCD":                    reflect.ValueOf((*cl.L2C_CrystalSpeedSlotCD)(nil)),
		"L2C_CrystalUnlockSlot":                     reflect.ValueOf((*cl.L2C_CrystalUnlockSlot)(nil)),
		"L2C_CrystalUpdateAchievement":              reflect.ValueOf((*cl.L2C_CrystalUpdateAchievement)(nil)),
		"L2C_DailyAttendanceGetData":                reflect.ValueOf((*cl.L2C_DailyAttendanceGetData)(nil)),
		"L2C_DailyAttendanceHeroGetData":            reflect.ValueOf((*cl.L2C_DailyAttendanceHeroGetData)(nil)),
		"L2C_DailyAttendanceHeroRecvAward":          reflect.ValueOf((*cl.L2C_DailyAttendanceHeroRecvAward)(nil)),
		"L2C_DailyAttendanceRecvAward":              reflect.ValueOf((*cl.L2C_DailyAttendanceRecvAward)(nil)),
		"L2C_DailyAttendanceUpdate":                 reflect.ValueOf((*cl.L2C_DailyAttendanceUpdate)(nil)),
		"L2C_DailySpecialGetData":                   reflect.ValueOf((*cl.L2C_DailySpecialGetData)(nil)),
		"L2C_DailySpecialRecvAward":                 reflect.ValueOf((*cl.L2C_DailySpecialRecvAward)(nil)),
		"L2C_DailyWishGet":                          reflect.ValueOf((*cl.L2C_DailyWishGet)(nil)),
		"L2C_DailyWishSummon":                       reflect.ValueOf((*cl.L2C_DailyWishSummon)(nil)),
		"L2C_DailyWishUpdate":                       reflect.ValueOf((*cl.L2C_DailyWishUpdate)(nil)),
		"L2C_DailyWishXmlGet":                       reflect.ValueOf((*cl.L2C_DailyWishXmlGet)(nil)),
		"L2C_DailyWishXmlUpdate":                    reflect.ValueOf((*cl.L2C_DailyWishXmlUpdate)(nil)),
		"L2C_DeleteMails":                           reflect.ValueOf((*cl.L2C_DeleteMails)(nil)),
		"L2C_DisorderlandBuyStamina":                reflect.ValueOf((*cl.L2C_DisorderlandBuyStamina)(nil)),
		"L2C_DisorderlandGetData":                   reflect.ValueOf((*cl.L2C_DisorderlandGetData)(nil)),
		"L2C_DisorderlandRank":                      reflect.ValueOf((*cl.L2C_DisorderlandRank)(nil)),
		"L2C_DisorderlandTestSweep":                 reflect.ValueOf((*cl.L2C_DisorderlandTestSweep)(nil)),
		"L2C_DisorderlandTriggerEvent":              reflect.ValueOf((*cl.L2C_DisorderlandTriggerEvent)(nil)),
		"L2C_DispatchGetAwards":                     reflect.ValueOf((*cl.L2C_DispatchGetAwards)(nil)),
		"L2C_DispatchLevelUpdate":                   reflect.ValueOf((*cl.L2C_DispatchLevelUpdate)(nil)),
		"L2C_DispatchReceiveTask":                   reflect.ValueOf((*cl.L2C_DispatchReceiveTask)(nil)),
		"L2C_DispatchRefreshTask":                   reflect.ValueOf((*cl.L2C_DispatchRefreshTask)(nil)),
		"L2C_DispatchTasks":                         reflect.ValueOf((*cl.L2C_DispatchTasks)(nil)),
		"L2C_DivineDemonGetOpenActivity":            reflect.ValueOf((*cl.L2C_DivineDemonGetOpenActivity)(nil)),
		"L2C_DivineDemonReceiveTaskAward":           reflect.ValueOf((*cl.L2C_DivineDemonReceiveTaskAward)(nil)),
		"L2C_DivineDemonSummon":                     reflect.ValueOf((*cl.L2C_DivineDemonSummon)(nil)),
		"L2C_DivineDemonUpdate":                     reflect.ValueOf((*cl.L2C_DivineDemonUpdate)(nil)),
		"L2C_DivineDemonUpdateTaskProgress":         reflect.ValueOf((*cl.L2C_DivineDemonUpdateTaskProgress)(nil)),
		"L2C_DrawMails":                             reflect.ValueOf((*cl.L2C_DrawMails)(nil)),
		"L2C_DropActivityDailyReward":               reflect.ValueOf((*cl.L2C_DropActivityDailyReward)(nil)),
		"L2C_DropActivityExchange":                  reflect.ValueOf((*cl.L2C_DropActivityExchange)(nil)),
		"L2C_DropActivityGetActivity":               reflect.ValueOf((*cl.L2C_DropActivityGetActivity)(nil)),
		"L2C_DropActivityMainInfo":                  reflect.ValueOf((*cl.L2C_DropActivityMainInfo)(nil)),
		"L2C_DropActivityUpdateActivity":            reflect.ValueOf((*cl.L2C_DropActivityUpdateActivity)(nil)),
		"L2C_Duel":                                  reflect.ValueOf((*cl.L2C_Duel)(nil)),
		"L2C_DuelFight":                             reflect.ValueOf((*cl.L2C_DuelFight)(nil)),
		"L2C_DuelSetStatus":                         reflect.ValueOf((*cl.L2C_DuelSetStatus)(nil)),
		"L2C_Dungeon":                               reflect.ValueOf((*cl.L2C_Dungeon)(nil)),
		"L2C_DungeonFight":                          reflect.ValueOf((*cl.L2C_DungeonFight)(nil)),
		"L2C_DungeonPreview":                        reflect.ValueOf((*cl.L2C_DungeonPreview)(nil)),
		"L2C_DungeonRecvAward":                      reflect.ValueOf((*cl.L2C_DungeonRecvAward)(nil)),
		"L2C_DungeonSpeedRecvAward":                 reflect.ValueOf((*cl.L2C_DungeonSpeedRecvAward)(nil)),
		"L2C_EmblemBuySlot":                         reflect.ValueOf((*cl.L2C_EmblemBuySlot)(nil)),
		"L2C_EmblemCustomize":                       reflect.ValueOf((*cl.L2C_EmblemCustomize)(nil)),
		"L2C_EmblemDecompose":                       reflect.ValueOf((*cl.L2C_EmblemDecompose)(nil)),
		"L2C_EmblemGet":                             reflect.ValueOf((*cl.L2C_EmblemGet)(nil)),
		"L2C_EmblemGrowTransfer":                    reflect.ValueOf((*cl.L2C_EmblemGrowTransfer)(nil)),
		"L2C_EmblemLevelUp":                         reflect.ValueOf((*cl.L2C_EmblemLevelUp)(nil)),
		"L2C_EmblemSetAutoDecompose":                reflect.ValueOf((*cl.L2C_EmblemSetAutoDecompose)(nil)),
		"L2C_EmblemSuccinct":                        reflect.ValueOf((*cl.L2C_EmblemSuccinct)(nil)),
		"L2C_EmblemSuccinctItemConflate":            reflect.ValueOf((*cl.L2C_EmblemSuccinctItemConflate)(nil)),
		"L2C_EmblemSuccinctLockOrSave":              reflect.ValueOf((*cl.L2C_EmblemSuccinctLockOrSave)(nil)),
		"L2C_EmblemUpdate":                          reflect.ValueOf((*cl.L2C_EmblemUpdate)(nil)),
		"L2C_EmblemUpgrade":                         reflect.ValueOf((*cl.L2C_EmblemUpgrade)(nil)),
		"L2C_EmblemWear":                            reflect.ValueOf((*cl.L2C_EmblemWear)(nil)),
		"L2C_EquipDecompose":                        reflect.ValueOf((*cl.L2C_EquipDecompose)(nil)),
		"L2C_EquipEnchant":                          reflect.ValueOf((*cl.L2C_EquipEnchant)(nil)),
		"L2C_EquipEvolution":                        reflect.ValueOf((*cl.L2C_EquipEvolution)(nil)),
		"L2C_EquipGet":                              reflect.ValueOf((*cl.L2C_EquipGet)(nil)),
		"L2C_EquipGrowTransfer":                     reflect.ValueOf((*cl.L2C_EquipGrowTransfer)(nil)),
		"L2C_EquipMultipleStrength":                 reflect.ValueOf((*cl.L2C_EquipMultipleStrength)(nil)),
		"L2C_EquipRefine":                           reflect.ValueOf((*cl.L2C_EquipRefine)(nil)),
		"L2C_EquipRevive":                           reflect.ValueOf((*cl.L2C_EquipRevive)(nil)),
		"L2C_EquipSetAutoDecompose":                 reflect.ValueOf((*cl.L2C_EquipSetAutoDecompose)(nil)),
		"L2C_EquipStrength":                         reflect.ValueOf((*cl.L2C_EquipStrength)(nil)),
		"L2C_EquipUpdate":                           reflect.ValueOf((*cl.L2C_EquipUpdate)(nil)),
		"L2C_EquipWear":                             reflect.ValueOf((*cl.L2C_EquipWear)(nil)),
		"L2C_FlowerAssistNotify":                    reflect.ValueOf((*cl.L2C_FlowerAssistNotify)(nil)),
		"L2C_FlowerAssistRecvLike":                  reflect.ValueOf((*cl.L2C_FlowerAssistRecvLike)(nil)),
		"L2C_FlowerAssistSendLike":                  reflect.ValueOf((*cl.L2C_FlowerAssistSendLike)(nil)),
		"L2C_FlowerAttackLevelGuard":                reflect.ValueOf((*cl.L2C_FlowerAttackLevelGuard)(nil)),
		"L2C_FlowerBuyOccupyAttackNum":              reflect.ValueOf((*cl.L2C_FlowerBuyOccupyAttackNum)(nil)),
		"L2C_FlowerChangeGoblin":                    reflect.ValueOf((*cl.L2C_FlowerChangeGoblin)(nil)),
		"L2C_FlowerEnemiesInfo":                     reflect.ValueOf((*cl.L2C_FlowerEnemiesInfo)(nil)),
		"L2C_FlowerExtendOccupyTime":                reflect.ValueOf((*cl.L2C_FlowerExtendOccupyTime)(nil)),
		"L2C_FlowerFeedGoblin":                      reflect.ValueOf((*cl.L2C_FlowerFeedGoblin)(nil)),
		"L2C_FlowerFeedSpecial":                     reflect.ValueOf((*cl.L2C_FlowerFeedSpecial)(nil)),
		"L2C_FlowerHarvest":                         reflect.ValueOf((*cl.L2C_FlowerHarvest)(nil)),
		"L2C_FlowerJungleInfo":                      reflect.ValueOf((*cl.L2C_FlowerJungleInfo)(nil)),
		"L2C_FlowerLevelUp":                         reflect.ValueOf((*cl.L2C_FlowerLevelUp)(nil)),
		"L2C_FlowerLogList":                         reflect.ValueOf((*cl.L2C_FlowerLogList)(nil)),
		"L2C_FlowerMainInfo":                        reflect.ValueOf((*cl.L2C_FlowerMainInfo)(nil)),
		"L2C_FlowerOccupyAssistList":                reflect.ValueOf((*cl.L2C_FlowerOccupyAssistList)(nil)),
		"L2C_FlowerOccupyAttack":                    reflect.ValueOf((*cl.L2C_FlowerOccupyAttack)(nil)),
		"L2C_FlowerOccupyHistory":                   reflect.ValueOf((*cl.L2C_FlowerOccupyHistory)(nil)),
		"L2C_FlowerOccupyRecommend":                 reflect.ValueOf((*cl.L2C_FlowerOccupyRecommend)(nil)),
		"L2C_FlowerPreviewOccupyAward":              reflect.ValueOf((*cl.L2C_FlowerPreviewOccupyAward)(nil)),
		"L2C_FlowerRecvOccupyAward":                 reflect.ValueOf((*cl.L2C_FlowerRecvOccupyAward)(nil)),
		"L2C_FlowerRecvPreviewOccupyAward":          reflect.ValueOf((*cl.L2C_FlowerRecvPreviewOccupyAward)(nil)),
		"L2C_FlowerRevengeOccupy":                   reflect.ValueOf((*cl.L2C_FlowerRevengeOccupy)(nil)),
		"L2C_FlowerRevengeSnatch":                   reflect.ValueOf((*cl.L2C_FlowerRevengeSnatch)(nil)),
		"L2C_FlowerSearch":                          reflect.ValueOf((*cl.L2C_FlowerSearch)(nil)),
		"L2C_FlowerShareFlowerbed":                  reflect.ValueOf((*cl.L2C_FlowerShareFlowerbed)(nil)),
		"L2C_FlowerSnatch":                          reflect.ValueOf((*cl.L2C_FlowerSnatch)(nil)),
		"L2C_FlowerSpeedGrow":                       reflect.ValueOf((*cl.L2C_FlowerSpeedGrow)(nil)),
		"L2C_FlowerStartFeed":                       reflect.ValueOf((*cl.L2C_FlowerStartFeed)(nil)),
		"L2C_FlowerStartPlant":                      reflect.ValueOf((*cl.L2C_FlowerStartPlant)(nil)),
		"L2C_FlowerTimberInfo":                      reflect.ValueOf((*cl.L2C_FlowerTimberInfo)(nil)),
		"L2C_Flush":                                 reflect.ValueOf((*cl.L2C_Flush)(nil)),
		"L2C_FlushRedPoint":                         reflect.ValueOf((*cl.L2C_FlushRedPoint)(nil)),
		"L2C_ForecastGetData":                       reflect.ValueOf((*cl.L2C_ForecastGetData)(nil)),
		"L2C_ForecastReceiveAward":                  reflect.ValueOf((*cl.L2C_ForecastReceiveAward)(nil)),
		"L2C_ForecastUpdate":                        reflect.ValueOf((*cl.L2C_ForecastUpdate)(nil)),
		"L2C_ForestChangeGoblin":                    reflect.ValueOf((*cl.L2C_ForestChangeGoblin)(nil)),
		"L2C_ForestFeedGoblin":                      reflect.ValueOf((*cl.L2C_ForestFeedGoblin)(nil)),
		"L2C_ForestFeedSpecial":                     reflect.ValueOf((*cl.L2C_ForestFeedSpecial)(nil)),
		"L2C_ForestHarvest":                         reflect.ValueOf((*cl.L2C_ForestHarvest)(nil)),
		"L2C_ForestInfo":                            reflect.ValueOf((*cl.L2C_ForestInfo)(nil)),
		"L2C_ForestLogList":                         reflect.ValueOf((*cl.L2C_ForestLogList)(nil)),
		"L2C_ForestLoot":                            reflect.ValueOf((*cl.L2C_ForestLoot)(nil)),
		"L2C_ForestRecvLvAward":                     reflect.ValueOf((*cl.L2C_ForestRecvLvAward)(nil)),
		"L2C_ForestRevenge":                         reflect.ValueOf((*cl.L2C_ForestRevenge)(nil)),
		"L2C_ForestSearch":                          reflect.ValueOf((*cl.L2C_ForestSearch)(nil)),
		"L2C_ForestSpeedGrow":                       reflect.ValueOf((*cl.L2C_ForestSpeedGrow)(nil)),
		"L2C_ForestStartFeed":                       reflect.ValueOf((*cl.L2C_ForestStartFeed)(nil)),
		"L2C_ForestStartPlant":                      reflect.ValueOf((*cl.L2C_ForestStartPlant)(nil)),
		"L2C_ForestUpdateSlot":                      reflect.ValueOf((*cl.L2C_ForestUpdateSlot)(nil)),
		"L2C_Formation":                             reflect.ValueOf((*cl.L2C_Formation)(nil)),
		"L2C_FragmentCompose":                       reflect.ValueOf((*cl.L2C_FragmentCompose)(nil)),
		"L2C_FragmentComposeAll":                    reflect.ValueOf((*cl.L2C_FragmentComposeAll)(nil)),
		"L2C_FragmentComposeTest":                   reflect.ValueOf((*cl.L2C_FragmentComposeTest)(nil)),
		"L2C_FragmentEmblemCompose":                 reflect.ValueOf((*cl.L2C_FragmentEmblemCompose)(nil)),
		"L2C_FriendAdd":                             reflect.ValueOf((*cl.L2C_FriendAdd)(nil)),
		"L2C_FriendBlacklist":                       reflect.ValueOf((*cl.L2C_FriendBlacklist)(nil)),
		"L2C_FriendConfirm":                         reflect.ValueOf((*cl.L2C_FriendConfirm)(nil)),
		"L2C_FriendDelete":                          reflect.ValueOf((*cl.L2C_FriendDelete)(nil)),
		"L2C_FriendGetBlacklist":                    reflect.ValueOf((*cl.L2C_FriendGetBlacklist)(nil)),
		"L2C_FriendInfo":                            reflect.ValueOf((*cl.L2C_FriendInfo)(nil)),
		"L2C_FriendNotify":                          reflect.ValueOf((*cl.L2C_FriendNotify)(nil)),
		"L2C_FriendRecommend":                       reflect.ValueOf((*cl.L2C_FriendRecommend)(nil)),
		"L2C_FriendRecvLike":                        reflect.ValueOf((*cl.L2C_FriendRecvLike)(nil)),
		"L2C_FriendRemBlacklist":                    reflect.ValueOf((*cl.L2C_FriendRemBlacklist)(nil)),
		"L2C_FriendRequestInfo":                     reflect.ValueOf((*cl.L2C_FriendRequestInfo)(nil)),
		"L2C_FriendSearch":                          reflect.ValueOf((*cl.L2C_FriendSearch)(nil)),
		"L2C_FriendSendLike":                        reflect.ValueOf((*cl.L2C_FriendSendLike)(nil)),
		"L2C_FunctionGetStatus":                     reflect.ValueOf((*cl.L2C_FunctionGetStatus)(nil)),
		"L2C_GM":                                    reflect.ValueOf((*cl.L2C_GM)(nil)),
		"L2C_GSTArenaVote":                          reflect.ValueOf((*cl.L2C_GSTArenaVote)(nil)),
		"L2C_GSTBossAward":                          reflect.ValueOf((*cl.L2C_GSTBossAward)(nil)),
		"L2C_GSTBossBuyChallenge":                   reflect.ValueOf((*cl.L2C_GSTBossBuyChallenge)(nil)),
		"L2C_GSTBossFight":                          reflect.ValueOf((*cl.L2C_GSTBossFight)(nil)),
		"L2C_GSTBossGet":                            reflect.ValueOf((*cl.L2C_GSTBossGet)(nil)),
		"L2C_GSTBossRank":                           reflect.ValueOf((*cl.L2C_GSTBossRank)(nil)),
		"L2C_GSTChallengeBuffChoose":                reflect.ValueOf((*cl.L2C_GSTChallengeBuffChoose)(nil)),
		"L2C_GSTChallengeFight":                     reflect.ValueOf((*cl.L2C_GSTChallengeFight)(nil)),
		"L2C_GSTChallengeFightLogList":              reflect.ValueOf((*cl.L2C_GSTChallengeFightLogList)(nil)),
		"L2C_GSTChallengeGetData":                   reflect.ValueOf((*cl.L2C_GSTChallengeGetData)(nil)),
		"L2C_GSTChallengeMatch":                     reflect.ValueOf((*cl.L2C_GSTChallengeMatch)(nil)),
		"L2C_GSTChallengeRank":                      reflect.ValueOf((*cl.L2C_GSTChallengeRank)(nil)),
		"L2C_GSTChallengeTaskReward":                reflect.ValueOf((*cl.L2C_GSTChallengeTaskReward)(nil)),
		"L2C_GSTChallengeTaskUpdate":                reflect.ValueOf((*cl.L2C_GSTChallengeTaskUpdate)(nil)),
		"L2C_GSTDonate":                             reflect.ValueOf((*cl.L2C_GSTDonate)(nil)),
		"L2C_GSTDragonEvolve":                       reflect.ValueOf((*cl.L2C_GSTDragonEvolve)(nil)),
		"L2C_GSTDragonFight":                        reflect.ValueOf((*cl.L2C_GSTDragonFight)(nil)),
		"L2C_GSTDragonFightBuyCount":                reflect.ValueOf((*cl.L2C_GSTDragonFightBuyCount)(nil)),
		"L2C_GSTDragonFightUseTicket":               reflect.ValueOf((*cl.L2C_GSTDragonFightUseTicket)(nil)),
		"L2C_GSTDragonGetCultivation":               reflect.ValueOf((*cl.L2C_GSTDragonGetCultivation)(nil)),
		"L2C_GSTDragonGetData":                      reflect.ValueOf((*cl.L2C_GSTDragonGetData)(nil)),
		"L2C_GSTDragonRank":                         reflect.ValueOf((*cl.L2C_GSTDragonRank)(nil)),
		"L2C_GSTDragonShow":                         reflect.ValueOf((*cl.L2C_GSTDragonShow)(nil)),
		"L2C_GSTDragonSkillOperate":                 reflect.ValueOf((*cl.L2C_GSTDragonSkillOperate)(nil)),
		"L2C_GSTDragonStrategySkill":                reflect.ValueOf((*cl.L2C_GSTDragonStrategySkill)(nil)),
		"L2C_GSTDragonTaskAward":                    reflect.ValueOf((*cl.L2C_GSTDragonTaskAward)(nil)),
		"L2C_GSTDragonTaskGetData":                  reflect.ValueOf((*cl.L2C_GSTDragonTaskGetData)(nil)),
		"L2C_GSTDragonTaskUpdate":                   reflect.ValueOf((*cl.L2C_GSTDragonTaskUpdate)(nil)),
		"L2C_GSTExchangeGroundTeam":                 reflect.ValueOf((*cl.L2C_GSTExchangeGroundTeam)(nil)),
		"L2C_GSTGetArenaVoteRecord":                 reflect.ValueOf((*cl.L2C_GSTGetArenaVoteRecord)(nil)),
		"L2C_GSTGetData":                            reflect.ValueOf((*cl.L2C_GSTGetData)(nil)),
		"L2C_GSTGetGroundData":                      reflect.ValueOf((*cl.L2C_GSTGetGroundData)(nil)),
		"L2C_GSTGetGuildDonateData":                 reflect.ValueOf((*cl.L2C_GSTGetGuildDonateData)(nil)),
		"L2C_GSTGetGuildDonateMemData":              reflect.ValueOf((*cl.L2C_GSTGetGuildDonateMemData)(nil)),
		"L2C_GSTGetHangUpReward":                    reflect.ValueOf((*cl.L2C_GSTGetHangUpReward)(nil)),
		"L2C_GSTGetLogData":                         reflect.ValueOf((*cl.L2C_GSTGetLogData)(nil)),
		"L2C_GSTGetTasksData":                       reflect.ValueOf((*cl.L2C_GSTGetTasksData)(nil)),
		"L2C_GSTGetTasksReward":                     reflect.ValueOf((*cl.L2C_GSTGetTasksReward)(nil)),
		"L2C_GSTGetTeamsData":                       reflect.ValueOf((*cl.L2C_GSTGetTeamsData)(nil)),
		"L2C_GSTGroupUsersRank":                     reflect.ValueOf((*cl.L2C_GSTGroupUsersRank)(nil)),
		"L2C_GSTGroupUsersRankLike":                 reflect.ValueOf((*cl.L2C_GSTGroupUsersRankLike)(nil)),
		"L2C_GSTGuildBuildDispatchHero":             reflect.ValueOf((*cl.L2C_GSTGuildBuildDispatchHero)(nil)),
		"L2C_GSTGuildBuildDonate":                   reflect.ValueOf((*cl.L2C_GSTGuildBuildDonate)(nil)),
		"L2C_GSTGuildBuildDonateRank":               reflect.ValueOf((*cl.L2C_GSTGuildBuildDonateRank)(nil)),
		"L2C_GSTGuildBuildGetData":                  reflect.ValueOf((*cl.L2C_GSTGuildBuildGetData)(nil)),
		"L2C_GSTGuildBuildGetTaskData":              reflect.ValueOf((*cl.L2C_GSTGuildBuildGetTaskData)(nil)),
		"L2C_GSTGuildBuildRecvTaskAward":            reflect.ValueOf((*cl.L2C_GSTGuildBuildRecvTaskAward)(nil)),
		"L2C_GSTGuildBuildTaskUpdate":               reflect.ValueOf((*cl.L2C_GSTGuildBuildTaskUpdate)(nil)),
		"L2C_GSTMessageEdit":                        reflect.ValueOf((*cl.L2C_GSTMessageEdit)(nil)),
		"L2C_GSTOreBuyTimes":                        reflect.ValueOf((*cl.L2C_GSTOreBuyTimes)(nil)),
		"L2C_GSTOreFight":                           reflect.ValueOf((*cl.L2C_GSTOreFight)(nil)),
		"L2C_GSTOreGetData":                         reflect.ValueOf((*cl.L2C_GSTOreGetData)(nil)),
		"L2C_GSTOreGetOreData":                      reflect.ValueOf((*cl.L2C_GSTOreGetOreData)(nil)),
		"L2C_GSTOreOccupy":                          reflect.ValueOf((*cl.L2C_GSTOreOccupy)(nil)),
		"L2C_GSTOreSearchAssist":                    reflect.ValueOf((*cl.L2C_GSTOreSearchAssist)(nil)),
		"L2C_GSTPreviewHangUpReward":                reflect.ValueOf((*cl.L2C_GSTPreviewHangUpReward)(nil)),
		"L2C_GSTPushSta":                            reflect.ValueOf((*cl.L2C_GSTPushSta)(nil)),
		"L2C_GSTRank":                               reflect.ValueOf((*cl.L2C_GSTRank)(nil)),
		"L2C_GSTScorePreview":                       reflect.ValueOf((*cl.L2C_GSTScorePreview)(nil)),
		"L2C_GSTSkillAssemble":                      reflect.ValueOf((*cl.L2C_GSTSkillAssemble)(nil)),
		"L2C_GSTTaskTypeUpdate":                     reflect.ValueOf((*cl.L2C_GSTTaskTypeUpdate)(nil)),
		"L2C_GSTTeamOperate":                        reflect.ValueOf((*cl.L2C_GSTTeamOperate)(nil)),
		"L2C_GSTTechDonate":                         reflect.ValueOf((*cl.L2C_GSTTechDonate)(nil)),
		"L2C_GSTTechGetData":                        reflect.ValueOf((*cl.L2C_GSTTechGetData)(nil)),
		"L2C_GSTTechGuildUserRank":                  reflect.ValueOf((*cl.L2C_GSTTechGuildUserRank)(nil)),
		"L2C_GSTTechSign":                           reflect.ValueOf((*cl.L2C_GSTTechSign)(nil)),
		"L2C_GSTTechTaskReward":                     reflect.ValueOf((*cl.L2C_GSTTechTaskReward)(nil)),
		"L2C_GSTTechTaskUpdate":                     reflect.ValueOf((*cl.L2C_GSTTechTaskUpdate)(nil)),
		"L2C_GemCompose":                            reflect.ValueOf((*cl.L2C_GemCompose)(nil)),
		"L2C_GemConvert":                            reflect.ValueOf((*cl.L2C_GemConvert)(nil)),
		"L2C_GemDecompose":                          reflect.ValueOf((*cl.L2C_GemDecompose)(nil)),
		"L2C_GemWear":                               reflect.ValueOf((*cl.L2C_GemWear)(nil)),
		"L2C_GetAchieveShowcase":                    reflect.ValueOf((*cl.L2C_GetAchieveShowcase)(nil)),
		"L2C_GetBags":                               reflect.ValueOf((*cl.L2C_GetBags)(nil)),
		"L2C_GetBattleReport":                       reflect.ValueOf((*cl.L2C_GetBattleReport)(nil)),
		"L2C_GetClientInfo":                         reflect.ValueOf((*cl.L2C_GetClientInfo)(nil)),
		"L2C_GetCognitionLog":                       reflect.ValueOf((*cl.L2C_GetCognitionLog)(nil)),
		"L2C_GetCommonRank":                         reflect.ValueOf((*cl.L2C_GetCommonRank)(nil)),
		"L2C_GetCommonRankFirst":                    reflect.ValueOf((*cl.L2C_GetCommonRankFirst)(nil)),
		"L2C_GetCrossRankFirst":                     reflect.ValueOf((*cl.L2C_GetCrossRankFirst)(nil)),
		"L2C_GetDefFormationPower":                  reflect.ValueOf((*cl.L2C_GetDefFormationPower)(nil)),
		"L2C_GetFormation":                          reflect.ValueOf((*cl.L2C_GetFormation)(nil)),
		"L2C_GetGems":                               reflect.ValueOf((*cl.L2C_GetGems)(nil)),
		"L2C_GetGiftCodeAward":                      reflect.ValueOf((*cl.L2C_GetGiftCodeAward)(nil)),
		"L2C_GetMails":                              reflect.ValueOf((*cl.L2C_GetMails)(nil)),
		"L2C_GetPush":                               reflect.ValueOf((*cl.L2C_GetPush)(nil)),
		"L2C_GetSeasonFlashBackData":                reflect.ValueOf((*cl.L2C_GetSeasonFlashBackData)(nil)),
		"L2C_GetSeasonRankFirst":                    reflect.ValueOf((*cl.L2C_GetSeasonRankFirst)(nil)),
		"L2C_GetSeasonRankList":                     reflect.ValueOf((*cl.L2C_GetSeasonRankList)(nil)),
		"L2C_GetUser":                               reflect.ValueOf((*cl.L2C_GetUser)(nil)),
		"L2C_GetUserBattleData":                     reflect.ValueOf((*cl.L2C_GetUserBattleData)(nil)),
		"L2C_GetUserSnapshots":                      reflect.ValueOf((*cl.L2C_GetUserSnapshots)(nil)),
		"L2C_GlobalAttrGet":                         reflect.ValueOf((*cl.L2C_GlobalAttrGet)(nil)),
		"L2C_GlobalAttrScoreGet":                    reflect.ValueOf((*cl.L2C_GlobalAttrScoreGet)(nil)),
		"L2C_GodPresentGetData":                     reflect.ValueOf((*cl.L2C_GodPresentGetData)(nil)),
		"L2C_GodPresentRecvAwards":                  reflect.ValueOf((*cl.L2C_GodPresentRecvAwards)(nil)),
		"L2C_GodPresentRecvItem":                    reflect.ValueOf((*cl.L2C_GodPresentRecvItem)(nil)),
		"L2C_GodPresentSummon":                      reflect.ValueOf((*cl.L2C_GodPresentSummon)(nil)),
		"L2C_GoddessChangeSuit":                     reflect.ValueOf((*cl.L2C_GoddessChangeSuit)(nil)),
		"L2C_GoddessChapterFight":                   reflect.ValueOf((*cl.L2C_GoddessChapterFight)(nil)),
		"L2C_GoddessCollect":                        reflect.ValueOf((*cl.L2C_GoddessCollect)(nil)),
		"L2C_GoddessContractGetData":                reflect.ValueOf((*cl.L2C_GoddessContractGetData)(nil)),
		"L2C_GoddessFeed":                           reflect.ValueOf((*cl.L2C_GoddessFeed)(nil)),
		"L2C_GoddessRecovery":                       reflect.ValueOf((*cl.L2C_GoddessRecovery)(nil)),
		"L2C_GoddessStoryAward":                     reflect.ValueOf((*cl.L2C_GoddessStoryAward)(nil)),
		"L2C_GoddessSuitUnlock":                     reflect.ValueOf((*cl.L2C_GoddessSuitUnlock)(nil)),
		"L2C_GoddessTouch":                          reflect.ValueOf((*cl.L2C_GoddessTouch)(nil)),
		"L2C_GoddessUnlock":                         reflect.ValueOf((*cl.L2C_GoddessUnlock)(nil)),
		"L2C_GoddessUpdateSuitIds":                  reflect.ValueOf((*cl.L2C_GoddessUpdateSuitIds)(nil)),
		"L2C_GoldBuyGet":                            reflect.ValueOf((*cl.L2C_GoldBuyGet)(nil)),
		"L2C_GoldBuyGetGold":                        reflect.ValueOf((*cl.L2C_GoldBuyGetGold)(nil)),
		"L2C_GuidanceFinishGroup":                   reflect.ValueOf((*cl.L2C_GuidanceFinishGroup)(nil)),
		"L2C_GuidanceFinishStep":                    reflect.ValueOf((*cl.L2C_GuidanceFinishStep)(nil)),
		"L2C_GuidanceList":                          reflect.ValueOf((*cl.L2C_GuidanceList)(nil)),
		"L2C_GuidanceSkip":                          reflect.ValueOf((*cl.L2C_GuidanceSkip)(nil)),
		"L2C_GuildApplyList":                        reflect.ValueOf((*cl.L2C_GuildApplyList)(nil)),
		"L2C_GuildApplyRatify":                      reflect.ValueOf((*cl.L2C_GuildApplyRatify)(nil)),
		"L2C_GuildChestActivate":                    reflect.ValueOf((*cl.L2C_GuildChestActivate)(nil)),
		"L2C_GuildChestGetData":                     reflect.ValueOf((*cl.L2C_GuildChestGetData)(nil)),
		"L2C_GuildChestLike":                        reflect.ValueOf((*cl.L2C_GuildChestLike)(nil)),
		"L2C_GuildChestRecv":                        reflect.ValueOf((*cl.L2C_GuildChestRecv)(nil)),
		"L2C_GuildCombineApply":                     reflect.ValueOf((*cl.L2C_GuildCombineApply)(nil)),
		"L2C_GuildCombineCheck":                     reflect.ValueOf((*cl.L2C_GuildCombineCheck)(nil)),
		"L2C_GuildCombineRatify":                    reflect.ValueOf((*cl.L2C_GuildCombineRatify)(nil)),
		"L2C_GuildCreate":                           reflect.ValueOf((*cl.L2C_GuildCreate)(nil)),
		"L2C_GuildDisband":                          reflect.ValueOf((*cl.L2C_GuildDisband)(nil)),
		"L2C_GuildDonate":                           reflect.ValueOf((*cl.L2C_GuildDonate)(nil)),
		"L2C_GuildDonateLogList":                    reflect.ValueOf((*cl.L2C_GuildDonateLogList)(nil)),
		"L2C_GuildDungeonBuyChallengeTimes":         reflect.ValueOf((*cl.L2C_GuildDungeonBuyChallengeTimes)(nil)),
		"L2C_GuildDungeonChapterInfo":               reflect.ValueOf((*cl.L2C_GuildDungeonChapterInfo)(nil)),
		"L2C_GuildDungeonDelMessageBoard":           reflect.ValueOf((*cl.L2C_GuildDungeonDelMessageBoard)(nil)),
		"L2C_GuildDungeonFight":                     reflect.ValueOf((*cl.L2C_GuildDungeonFight)(nil)),
		"L2C_GuildDungeonGetMembersFightInfo":       reflect.ValueOf((*cl.L2C_GuildDungeonGetMembersFightInfo)(nil)),
		"L2C_GuildDungeonGetMessageBoard":           reflect.ValueOf((*cl.L2C_GuildDungeonGetMessageBoard)(nil)),
		"L2C_GuildDungeonGetStrategy":               reflect.ValueOf((*cl.L2C_GuildDungeonGetStrategy)(nil)),
		"L2C_GuildDungeonHallOfFame":                reflect.ValueOf((*cl.L2C_GuildDungeonHallOfFame)(nil)),
		"L2C_GuildDungeonInfo":                      reflect.ValueOf((*cl.L2C_GuildDungeonInfo)(nil)),
		"L2C_GuildDungeonLogList":                   reflect.ValueOf((*cl.L2C_GuildDungeonLogList)(nil)),
		"L2C_GuildDungeonNotify":                    reflect.ValueOf((*cl.L2C_GuildDungeonNotify)(nil)),
		"L2C_GuildDungeonRankLike":                  reflect.ValueOf((*cl.L2C_GuildDungeonRankLike)(nil)),
		"L2C_GuildDungeonRecvAllBossBoxAward":       reflect.ValueOf((*cl.L2C_GuildDungeonRecvAllBossBoxAward)(nil)),
		"L2C_GuildDungeonRecvBossBoxAward":          reflect.ValueOf((*cl.L2C_GuildDungeonRecvBossBoxAward)(nil)),
		"L2C_GuildDungeonRecvChapterTaskAward":      reflect.ValueOf((*cl.L2C_GuildDungeonRecvChapterTaskAward)(nil)),
		"L2C_GuildDungeonSeasonDivisionAward":       reflect.ValueOf((*cl.L2C_GuildDungeonSeasonDivisionAward)(nil)),
		"L2C_GuildDungeonSetFocus":                  reflect.ValueOf((*cl.L2C_GuildDungeonSetFocus)(nil)),
		"L2C_GuildDungeonSetMessageBoard":           reflect.ValueOf((*cl.L2C_GuildDungeonSetMessageBoard)(nil)),
		"L2C_GuildDungeonStrategyUseNotify":         reflect.ValueOf((*cl.L2C_GuildDungeonStrategyUseNotify)(nil)),
		"L2C_GuildDungeonTop3Guild":                 reflect.ValueOf((*cl.L2C_GuildDungeonTop3Guild)(nil)),
		"L2C_GuildDungeonUseStrategy":               reflect.ValueOf((*cl.L2C_GuildDungeonUseStrategy)(nil)),
		"L2C_GuildDungeonUserDamageRank":            reflect.ValueOf((*cl.L2C_GuildDungeonUserDamageRank)(nil)),
		"L2C_GuildGetBadgeList":                     reflect.ValueOf((*cl.L2C_GuildGetBadgeList)(nil)),
		"L2C_GuildGetDeclaration":                   reflect.ValueOf((*cl.L2C_GuildGetDeclaration)(nil)),
		"L2C_GuildGetDivisionAwardInfo":             reflect.ValueOf((*cl.L2C_GuildGetDivisionAwardInfo)(nil)),
		"L2C_GuildGetDonateAward":                   reflect.ValueOf((*cl.L2C_GuildGetDonateAward)(nil)),
		"L2C_GuildGetMedals":                        reflect.ValueOf((*cl.L2C_GuildGetMedals)(nil)),
		"L2C_GuildGetMembers":                       reflect.ValueOf((*cl.L2C_GuildGetMembers)(nil)),
		"L2C_GuildGetMyInfo":                        reflect.ValueOf((*cl.L2C_GuildGetMyInfo)(nil)),
		"L2C_GuildList":                             reflect.ValueOf((*cl.L2C_GuildList)(nil)),
		"L2C_GuildListGetDetail":                    reflect.ValueOf((*cl.L2C_GuildListGetDetail)(nil)),
		"L2C_GuildLogList":                          reflect.ValueOf((*cl.L2C_GuildLogList)(nil)),
		"L2C_GuildMainInfo":                         reflect.ValueOf((*cl.L2C_GuildMainInfo)(nil)),
		"L2C_GuildManagerMember":                    reflect.ValueOf((*cl.L2C_GuildManagerMember)(nil)),
		"L2C_GuildMedalLike":                        reflect.ValueOf((*cl.L2C_GuildMedalLike)(nil)),
		"L2C_GuildMobilizationAcceptTask":           reflect.ValueOf((*cl.L2C_GuildMobilizationAcceptTask)(nil)),
		"L2C_GuildMobilizationBuyTimes":             reflect.ValueOf((*cl.L2C_GuildMobilizationBuyTimes)(nil)),
		"L2C_GuildMobilizationEditMessageBoard":     reflect.ValueOf((*cl.L2C_GuildMobilizationEditMessageBoard)(nil)),
		"L2C_GuildMobilizationFinishTaskLogs":       reflect.ValueOf((*cl.L2C_GuildMobilizationFinishTaskLogs)(nil)),
		"L2C_GuildMobilizationFreshTask":            reflect.ValueOf((*cl.L2C_GuildMobilizationFreshTask)(nil)),
		"L2C_GuildMobilizationGetData":              reflect.ValueOf((*cl.L2C_GuildMobilizationGetData)(nil)),
		"L2C_GuildMobilizationGiveUpTask":           reflect.ValueOf((*cl.L2C_GuildMobilizationGiveUpTask)(nil)),
		"L2C_GuildMobilizationGuildRank":            reflect.ValueOf((*cl.L2C_GuildMobilizationGuildRank)(nil)),
		"L2C_GuildMobilizationPersonalRank":         reflect.ValueOf((*cl.L2C_GuildMobilizationPersonalRank)(nil)),
		"L2C_GuildMobilizationRecvScoreLevel":       reflect.ValueOf((*cl.L2C_GuildMobilizationRecvScoreLevel)(nil)),
		"L2C_GuildMobilizationScoreAward":           reflect.ValueOf((*cl.L2C_GuildMobilizationScoreAward)(nil)),
		"L2C_GuildMobilizationSignTask":             reflect.ValueOf((*cl.L2C_GuildMobilizationSignTask)(nil)),
		"L2C_GuildMobilizationUpdateTaskProgress":   reflect.ValueOf((*cl.L2C_GuildMobilizationUpdateTaskProgress)(nil)),
		"L2C_GuildModifyInfo":                       reflect.ValueOf((*cl.L2C_GuildModifyInfo)(nil)),
		"L2C_GuildModifyNotice":                     reflect.ValueOf((*cl.L2C_GuildModifyNotice)(nil)),
		"L2C_GuildNotify":                           reflect.ValueOf((*cl.L2C_GuildNotify)(nil)),
		"L2C_GuildQuit":                             reflect.ValueOf((*cl.L2C_GuildQuit)(nil)),
		"L2C_GuildRank":                             reflect.ValueOf((*cl.L2C_GuildRank)(nil)),
		"L2C_GuildSearch":                           reflect.ValueOf((*cl.L2C_GuildSearch)(nil)),
		"L2C_GuildSendMail":                         reflect.ValueOf((*cl.L2C_GuildSendMail)(nil)),
		"L2C_GuildSendRecruitMsg":                   reflect.ValueOf((*cl.L2C_GuildSendRecruitMsg)(nil)),
		"L2C_GuildSetName":                          reflect.ValueOf((*cl.L2C_GuildSetName)(nil)),
		"L2C_GuildSyncKickCount":                    reflect.ValueOf((*cl.L2C_GuildSyncKickCount)(nil)),
		"L2C_GuildTalentLevelUp":                    reflect.ValueOf((*cl.L2C_GuildTalentLevelUp)(nil)),
		"L2C_GuildTalentList":                       reflect.ValueOf((*cl.L2C_GuildTalentList)(nil)),
		"L2C_GuildTalentReset":                      reflect.ValueOf((*cl.L2C_GuildTalentReset)(nil)),
		"L2C_GuildUpdateInfo":                       reflect.ValueOf((*cl.L2C_GuildUpdateInfo)(nil)),
		"L2C_GuildUserApply":                        reflect.ValueOf((*cl.L2C_GuildUserApply)(nil)),
		"L2C_HandbooksActive":                       reflect.ValueOf((*cl.L2C_HandbooksActive)(nil)),
		"L2C_HandbooksActiveHeroAttr":               reflect.ValueOf((*cl.L2C_HandbooksActiveHeroAttr)(nil)),
		"L2C_HandbooksGetData":                      reflect.ValueOf((*cl.L2C_HandbooksGetData)(nil)),
		"L2C_HandbooksReceiveAwards":                reflect.ValueOf((*cl.L2C_HandbooksReceiveAwards)(nil)),
		"L2C_HasRecvH5DesktopReward":                reflect.ValueOf((*cl.L2C_HasRecvH5DesktopReward)(nil)),
		"L2C_HeroAwaken":                            reflect.ValueOf((*cl.L2C_HeroAwaken)(nil)),
		"L2C_HeroBack":                              reflect.ValueOf((*cl.L2C_HeroBack)(nil)),
		"L2C_HeroBuySlot":                           reflect.ValueOf((*cl.L2C_HeroBuySlot)(nil)),
		"L2C_HeroChangeRandom":                      reflect.ValueOf((*cl.L2C_HeroChangeRandom)(nil)),
		"L2C_HeroChangeSave":                        reflect.ValueOf((*cl.L2C_HeroChangeSave)(nil)),
		"L2C_HeroConversion":                        reflect.ValueOf((*cl.L2C_HeroConversion)(nil)),
		"L2C_HeroConvert":                           reflect.ValueOf((*cl.L2C_HeroConvert)(nil)),
		"L2C_HeroConvertAwakenItem":                 reflect.ValueOf((*cl.L2C_HeroConvertAwakenItem)(nil)),
		"L2C_HeroDecompose":                         reflect.ValueOf((*cl.L2C_HeroDecompose)(nil)),
		"L2C_HeroExchange":                          reflect.ValueOf((*cl.L2C_HeroExchange)(nil)),
		"L2C_HeroGemLevelUp":                        reflect.ValueOf((*cl.L2C_HeroGemLevelUp)(nil)),
		"L2C_HeroGetStarUpCosts":                    reflect.ValueOf((*cl.L2C_HeroGetStarUpCosts)(nil)),
		"L2C_HeroLevelUp":                           reflect.ValueOf((*cl.L2C_HeroLevelUp)(nil)),
		"L2C_HeroList":                              reflect.ValueOf((*cl.L2C_HeroList)(nil)),
		"L2C_HeroRevive":                            reflect.ValueOf((*cl.L2C_HeroRevive)(nil)),
		"L2C_HeroStageUp":                           reflect.ValueOf((*cl.L2C_HeroStageUp)(nil)),
		"L2C_HeroStarUp":                            reflect.ValueOf((*cl.L2C_HeroStarUp)(nil)),
		"L2C_HeroStarUpRedList":                     reflect.ValueOf((*cl.L2C_HeroStarUpRedList)(nil)),
		"L2C_HeroTagUpdate":                         reflect.ValueOf((*cl.L2C_HeroTagUpdate)(nil)),
		"L2C_HeroTestAttr":                          reflect.ValueOf((*cl.L2C_HeroTestAttr)(nil)),
		"L2C_HeroTestCalPower":                      reflect.ValueOf((*cl.L2C_HeroTestCalPower)(nil)),
		"L2C_HeroUpdateLockStatus":                  reflect.ValueOf((*cl.L2C_HeroUpdateLockStatus)(nil)),
		"L2C_HeroUpdateStarLimit":                   reflect.ValueOf((*cl.L2C_HeroUpdateStarLimit)(nil)),
		"L2C_HeroesUpdate":                          reflect.ValueOf((*cl.L2C_HeroesUpdate)(nil)),
		"L2C_HotRankGet":                            reflect.ValueOf((*cl.L2C_HotRankGet)(nil)),
		"L2C_ItemSelect":                            reflect.ValueOf((*cl.L2C_ItemSelect)(nil)),
		"L2C_KeepAlive":                             reflect.ValueOf((*cl.L2C_KeepAlive)(nil)),
		"L2C_LinkHeroSummon":                        reflect.ValueOf((*cl.L2C_LinkHeroSummon)(nil)),
		"L2C_LinkHeroSummonGet":                     reflect.ValueOf((*cl.L2C_LinkHeroSummonGet)(nil)),
		"L2C_LinkHeroSummonPoolTimeUpdate":          reflect.ValueOf((*cl.L2C_LinkHeroSummonPoolTimeUpdate)(nil)),
		"L2C_LinkHeroSummonTest":                    reflect.ValueOf((*cl.L2C_LinkHeroSummonTest)(nil)),
		"L2C_LinkInfo":                              reflect.ValueOf((*cl.L2C_LinkInfo)(nil)),
		"L2C_LinkSetView":                           reflect.ValueOf((*cl.L2C_LinkSetView)(nil)),
		"L2C_MazeBuyRevive":                         reflect.ValueOf((*cl.L2C_MazeBuyRevive)(nil)),
		"L2C_MazeGetGrid":                           reflect.ValueOf((*cl.L2C_MazeGetGrid)(nil)),
		"L2C_MazeGetMap":                            reflect.ValueOf((*cl.L2C_MazeGetMap)(nil)),
		"L2C_MazeGetSelectMapData":                  reflect.ValueOf((*cl.L2C_MazeGetSelectMapData)(nil)),
		"L2C_MazeRecoveryHero":                      reflect.ValueOf((*cl.L2C_MazeRecoveryHero)(nil)),
		"L2C_MazeSelectBuff":                        reflect.ValueOf((*cl.L2C_MazeSelectBuff)(nil)),
		"L2C_MazeSweep":                             reflect.ValueOf((*cl.L2C_MazeSweep)(nil)),
		"L2C_MazeTaskReceiveAward":                  reflect.ValueOf((*cl.L2C_MazeTaskReceiveAward)(nil)),
		"L2C_MazeTaskUpdate":                        reflect.ValueOf((*cl.L2C_MazeTaskUpdate)(nil)),
		"L2C_MazeTriggerEvent":                      reflect.ValueOf((*cl.L2C_MazeTriggerEvent)(nil)),
		"L2C_MazeUseItem":                           reflect.ValueOf((*cl.L2C_MazeUseItem)(nil)),
		"L2C_MedalGetData":                          reflect.ValueOf((*cl.L2C_MedalGetData)(nil)),
		"L2C_MedalGoddessCureUpdate":                reflect.ValueOf((*cl.L2C_MedalGoddessCureUpdate)(nil)),
		"L2C_MedalReceiveAward":                     reflect.ValueOf((*cl.L2C_MedalReceiveAward)(nil)),
		"L2C_MedalUpdate":                           reflect.ValueOf((*cl.L2C_MedalUpdate)(nil)),
		"L2C_MemoryLatest":                          reflect.ValueOf((*cl.L2C_MemoryLatest)(nil)),
		"L2C_MemoryUnlock":                          reflect.ValueOf((*cl.L2C_MemoryUnlock)(nil)),
		"L2C_MirageBuyCount":                        reflect.ValueOf((*cl.L2C_MirageBuyCount)(nil)),
		"L2C_MirageDetail":                          reflect.ValueOf((*cl.L2C_MirageDetail)(nil)),
		"L2C_MirageFight":                           reflect.ValueOf((*cl.L2C_MirageFight)(nil)),
		"L2C_MirageList":                            reflect.ValueOf((*cl.L2C_MirageList)(nil)),
		"L2C_MiragePowerCrush":                      reflect.ValueOf((*cl.L2C_MiragePowerCrush)(nil)),
		"L2C_MirageReceiveAward":                    reflect.ValueOf((*cl.L2C_MirageReceiveAward)(nil)),
		"L2C_MirageSaveAffixes":                     reflect.ValueOf((*cl.L2C_MirageSaveAffixes)(nil)),
		"L2C_MirageSweep":                           reflect.ValueOf((*cl.L2C_MirageSweep)(nil)),
		"L2C_MirageTestDrop":                        reflect.ValueOf((*cl.L2C_MirageTestDrop)(nil)),
		"L2C_MonthTasksGetData":                     reflect.ValueOf((*cl.L2C_MonthTasksGetData)(nil)),
		"L2C_MonthTasksRecvAwards":                  reflect.ValueOf((*cl.L2C_MonthTasksRecvAwards)(nil)),
		"L2C_MonthTasksUpdate":                      reflect.ValueOf((*cl.L2C_MonthTasksUpdate)(nil)),
		"L2C_MonthlyCardGetData":                    reflect.ValueOf((*cl.L2C_MonthlyCardGetData)(nil)),
		"L2C_MonthlyCardReceiveAward":               reflect.ValueOf((*cl.L2C_MonthlyCardReceiveAward)(nil)),
		"L2C_MonthlyCardRechargeNotify":             reflect.ValueOf((*cl.L2C_MonthlyCardRechargeNotify)(nil)),
		"L2C_MuteAccount":                           reflect.ValueOf((*cl.L2C_MuteAccount)(nil)),
		"L2C_NewMailTip":                            reflect.ValueOf((*cl.L2C_NewMailTip)(nil)),
		"L2C_NewYearActivityGetData":                reflect.ValueOf((*cl.L2C_NewYearActivityGetData)(nil)),
		"L2C_NewYearActivityLoginAward":             reflect.ValueOf((*cl.L2C_NewYearActivityLoginAward)(nil)),
		"L2C_NotifyBanCmd":                          reflect.ValueOf((*cl.L2C_NotifyBanCmd)(nil)),
		"L2C_OSSUrl":                                reflect.ValueOf((*cl.L2C_OSSUrl)(nil)),
		"L2C_OpGlobalAttr":                          reflect.ValueOf((*cl.L2C_OpGlobalAttr)(nil)),
		"L2C_OpNum":                                 reflect.ValueOf((*cl.L2C_OpNum)(nil)),
		"L2C_OpResources":                           reflect.ValueOf((*cl.L2C_OpResources)(nil)),
		"L2C_OperateActivityCanXMLUpdate":           reflect.ValueOf((*cl.L2C_OperateActivityCanXMLUpdate)(nil)),
		"L2C_OperateActivityGetData":                reflect.ValueOf((*cl.L2C_OperateActivityGetData)(nil)),
		"L2C_OperateActivityGetTaskReward":          reflect.ValueOf((*cl.L2C_OperateActivityGetTaskReward)(nil)),
		"L2C_OperateActivityGetXML":                 reflect.ValueOf((*cl.L2C_OperateActivityGetXML)(nil)),
		"L2C_OperateActivityInitActivity":           reflect.ValueOf((*cl.L2C_OperateActivityInitActivity)(nil)),
		"L2C_OperateActivityPromotionRechargeCheck": reflect.ValueOf((*cl.L2C_OperateActivityPromotionRechargeCheck)(nil)),
		"L2C_OperateActivityPromotionSelectAward":   reflect.ValueOf((*cl.L2C_OperateActivityPromotionSelectAward)(nil)),
		"L2C_OperateActivityRecharge":               reflect.ValueOf((*cl.L2C_OperateActivityRecharge)(nil)),
		"L2C_OperateActivityUpdateData":             reflect.ValueOf((*cl.L2C_OperateActivityUpdateData)(nil)),
		"L2C_OssBattleReport":                       reflect.ValueOf((*cl.L2C_OssBattleReport)(nil)),
		"L2C_PassBuyNotify":                         reflect.ValueOf((*cl.L2C_PassBuyNotify)(nil)),
		"L2C_PassGetData":                           reflect.ValueOf((*cl.L2C_PassGetData)(nil)),
		"L2C_PassLevelBuy":                          reflect.ValueOf((*cl.L2C_PassLevelBuy)(nil)),
		"L2C_PassReceiveAward":                      reflect.ValueOf((*cl.L2C_PassReceiveAward)(nil)),
		"L2C_PassUpdate":                            reflect.ValueOf((*cl.L2C_PassUpdate)(nil)),
		"L2C_PeakBaseData":                          reflect.ValueOf((*cl.L2C_PeakBaseData)(nil)),
		"L2C_PeakDoGuess":                           reflect.ValueOf((*cl.L2C_PeakDoGuess)(nil)),
		"L2C_PeakFighterDetail":                     reflect.ValueOf((*cl.L2C_PeakFighterDetail)(nil)),
		"L2C_PeakGetLastBattleReport":               reflect.ValueOf((*cl.L2C_PeakGetLastBattleReport)(nil)),
		"L2C_PeakGetMatch":                          reflect.ValueOf((*cl.L2C_PeakGetMatch)(nil)),
		"L2C_PeakGuessList":                         reflect.ValueOf((*cl.L2C_PeakGuessList)(nil)),
		"L2C_PeakRankList":                          reflect.ValueOf((*cl.L2C_PeakRankList)(nil)),
		"L2C_PeakRecvInviteReward":                  reflect.ValueOf((*cl.L2C_PeakRecvInviteReward)(nil)),
		"L2C_PeakUpdateTip":                         reflect.ValueOf((*cl.L2C_PeakUpdateTip)(nil)),
		"L2C_PeakWorship":                           reflect.ValueOf((*cl.L2C_PeakWorship)(nil)),
		"L2C_PreSeasonGetData":                      reflect.ValueOf((*cl.L2C_PreSeasonGetData)(nil)),
		"L2C_PreSeasonRecvAward":                    reflect.ValueOf((*cl.L2C_PreSeasonRecvAward)(nil)),
		"L2C_PushGiftGetData":                       reflect.ValueOf((*cl.L2C_PushGiftGetData)(nil)),
		"L2C_PushGiftRechargeAward":                 reflect.ValueOf((*cl.L2C_PushGiftRechargeAward)(nil)),
		"L2C_PushGiftUpdate":                        reflect.ValueOf((*cl.L2C_PushGiftUpdate)(nil)),
		"L2C_PyramidChooseAward":                    reflect.ValueOf((*cl.L2C_PyramidChooseAward)(nil)),
		"L2C_PyramidDraw":                           reflect.ValueOf((*cl.L2C_PyramidDraw)(nil)),
		"L2C_PyramidGetData":                        reflect.ValueOf((*cl.L2C_PyramidGetData)(nil)),
		"L2C_PyramidReceiveAwards":                  reflect.ValueOf((*cl.L2C_PyramidReceiveAwards)(nil)),
		"L2C_PyramidTestDraw":                       reflect.ValueOf((*cl.L2C_PyramidTestDraw)(nil)),
		"L2C_PyramidUpdateActivity":                 reflect.ValueOf((*cl.L2C_PyramidUpdateActivity)(nil)),
		"L2C_PyramidUpdateTask":                     reflect.ValueOf((*cl.L2C_PyramidUpdateTask)(nil)),
		"L2C_QuestionnaireUpdate":                   reflect.ValueOf((*cl.L2C_QuestionnaireUpdate)(nil)),
		"L2C_RankAchieveList":                       reflect.ValueOf((*cl.L2C_RankAchieveList)(nil)),
		"L2C_RankAchieveNotify":                     reflect.ValueOf((*cl.L2C_RankAchieveNotify)(nil)),
		"L2C_RankAchieveRecvAward":                  reflect.ValueOf((*cl.L2C_RankAchieveRecvAward)(nil)),
		"L2C_RateGetStatus":                         reflect.ValueOf((*cl.L2C_RateGetStatus)(nil)),
		"L2C_RateScore":                             reflect.ValueOf((*cl.L2C_RateScore)(nil)),
		"L2C_ReadMail":                              reflect.ValueOf((*cl.L2C_ReadMail)(nil)),
		"L2C_RechargeByCoupon":                      reflect.ValueOf((*cl.L2C_RechargeByCoupon)(nil)),
		"L2C_RechargeFirstGiftNotify":               reflect.ValueOf((*cl.L2C_RechargeFirstGiftNotify)(nil)),
		"L2C_RechargeFirstGiftReward":               reflect.ValueOf((*cl.L2C_RechargeFirstGiftReward)(nil)),
		"L2C_RechargeGetData":                       reflect.ValueOf((*cl.L2C_RechargeGetData)(nil)),
		"L2C_RechargeNotify":                        reflect.ValueOf((*cl.L2C_RechargeNotify)(nil)),
		"L2C_RechargeSimulation":                    reflect.ValueOf((*cl.L2C_RechargeSimulation)(nil)),
		"L2C_RecvH5DesktopReward":                   reflect.ValueOf((*cl.L2C_RecvH5DesktopReward)(nil)),
		"L2C_RecvShareAward":                        reflect.ValueOf((*cl.L2C_RecvShareAward)(nil)),
		"L2C_RemainBookLevelUp":                     reflect.ValueOf((*cl.L2C_RemainBookLevelUp)(nil)),
		"L2C_RemainBookRecvExp":                     reflect.ValueOf((*cl.L2C_RemainBookRecvExp)(nil)),
		"L2C_RemainGetData":                         reflect.ValueOf((*cl.L2C_RemainGetData)(nil)),
		"L2C_RemainUpdate":                          reflect.ValueOf((*cl.L2C_RemainUpdate)(nil)),
		"L2C_RemainUseItemsNotify":                  reflect.ValueOf((*cl.L2C_RemainUseItemsNotify)(nil)),
		"L2C_RiteChanged":                           reflect.ValueOf((*cl.L2C_RiteChanged)(nil)),
		"L2C_RiteGetData":                           reflect.ValueOf((*cl.L2C_RiteGetData)(nil)),
		"L2C_RiteRareUp":                            reflect.ValueOf((*cl.L2C_RiteRareUp)(nil)),
		"L2C_RiteTakeRareAwards":                    reflect.ValueOf((*cl.L2C_RiteTakeRareAwards)(nil)),
		"L2C_RobotBattle":                           reflect.ValueOf((*cl.L2C_RobotBattle)(nil)),
		"L2C_RoundActivityGetData":                  reflect.ValueOf((*cl.L2C_RoundActivityGetData)(nil)),
		"L2C_RoundActivityRecvTaskAward":            reflect.ValueOf((*cl.L2C_RoundActivityRecvTaskAward)(nil)),
		"L2C_RoundActivityUpdateTask":               reflect.ValueOf((*cl.L2C_RoundActivityUpdateTask)(nil)),
		"L2C_SeasonArenaBuyChallengeCount":          reflect.ValueOf((*cl.L2C_SeasonArenaBuyChallengeCount)(nil)),
		"L2C_SeasonArenaDivisionAward":              reflect.ValueOf((*cl.L2C_SeasonArenaDivisionAward)(nil)),
		"L2C_SeasonArenaFight":                      reflect.ValueOf((*cl.L2C_SeasonArenaFight)(nil)),
		"L2C_SeasonArenaGetData":                    reflect.ValueOf((*cl.L2C_SeasonArenaGetData)(nil)),
		"L2C_SeasonArenaGetRankList":                reflect.ValueOf((*cl.L2C_SeasonArenaGetRankList)(nil)),
		"L2C_SeasonArenaLogList":                    reflect.ValueOf((*cl.L2C_SeasonArenaLogList)(nil)),
		"L2C_SeasonArenaOfFame":                     reflect.ValueOf((*cl.L2C_SeasonArenaOfFame)(nil)),
		"L2C_SeasonArenaRefresh":                    reflect.ValueOf((*cl.L2C_SeasonArenaRefresh)(nil)),
		"L2C_SeasonArenaState":                      reflect.ValueOf((*cl.L2C_SeasonArenaState)(nil)),
		"L2C_SeasonArenaTaskAward":                  reflect.ValueOf((*cl.L2C_SeasonArenaTaskAward)(nil)),
		"L2C_SeasonArenaTaskUpdate":                 reflect.ValueOf((*cl.L2C_SeasonArenaTaskUpdate)(nil)),
		"L2C_SeasonArenaUseTicket":                  reflect.ValueOf((*cl.L2C_SeasonArenaUseTicket)(nil)),
		"L2C_SeasonComplianceGetData":               reflect.ValueOf((*cl.L2C_SeasonComplianceGetData)(nil)),
		"L2C_SeasonComplianceList":                  reflect.ValueOf((*cl.L2C_SeasonComplianceList)(nil)),
		"L2C_SeasonComplianceRecvReward":            reflect.ValueOf((*cl.L2C_SeasonComplianceRecvReward)(nil)),
		"L2C_SeasonComplianceScoreChange":           reflect.ValueOf((*cl.L2C_SeasonComplianceScoreChange)(nil)),
		"L2C_SeasonDoorFight":                       reflect.ValueOf((*cl.L2C_SeasonDoorFight)(nil)),
		"L2C_SeasonDoorFightLine":                   reflect.ValueOf((*cl.L2C_SeasonDoorFightLine)(nil)),
		"L2C_SeasonDoorFightLineReward":             reflect.ValueOf((*cl.L2C_SeasonDoorFightLineReward)(nil)),
		"L2C_SeasonDoorFightLineViewReward":         reflect.ValueOf((*cl.L2C_SeasonDoorFightLineViewReward)(nil)),
		"L2C_SeasonDoorGetData":                     reflect.ValueOf((*cl.L2C_SeasonDoorGetData)(nil)),
		"L2C_SeasonDoorLog":                         reflect.ValueOf((*cl.L2C_SeasonDoorLog)(nil)),
		"L2C_SeasonDoorTaskReward":                  reflect.ValueOf((*cl.L2C_SeasonDoorTaskReward)(nil)),
		"L2C_SeasonDoorTaskUpdate":                  reflect.ValueOf((*cl.L2C_SeasonDoorTaskUpdate)(nil)),
		"L2C_SeasonDungeonFight":                    reflect.ValueOf((*cl.L2C_SeasonDungeonFight)(nil)),
		"L2C_SeasonDungeonGetData":                  reflect.ValueOf((*cl.L2C_SeasonDungeonGetData)(nil)),
		"L2C_SeasonDungeonRecvReward":               reflect.ValueOf((*cl.L2C_SeasonDungeonRecvReward)(nil)),
		"L2C_SeasonDungeonUpdateTask":               reflect.ValueOf((*cl.L2C_SeasonDungeonUpdateTask)(nil)),
		"L2C_SeasonEnter":                           reflect.ValueOf((*cl.L2C_SeasonEnter)(nil)),
		"L2C_SeasonJewelryDecompose":                reflect.ValueOf((*cl.L2C_SeasonJewelryDecompose)(nil)),
		"L2C_SeasonJewelryGetData":                  reflect.ValueOf((*cl.L2C_SeasonJewelryGetData)(nil)),
		"L2C_SeasonJewelryLock":                     reflect.ValueOf((*cl.L2C_SeasonJewelryLock)(nil)),
		"L2C_SeasonJewelrySetAutoDecompose":         reflect.ValueOf((*cl.L2C_SeasonJewelrySetAutoDecompose)(nil)),
		"L2C_SeasonJewelrySkillChange":              reflect.ValueOf((*cl.L2C_SeasonJewelrySkillChange)(nil)),
		"L2C_SeasonJewelrySkillChangeConfirm":       reflect.ValueOf((*cl.L2C_SeasonJewelrySkillChangeConfirm)(nil)),
		"L2C_SeasonJewelrySkillClassUp":             reflect.ValueOf((*cl.L2C_SeasonJewelrySkillClassUp)(nil)),
		"L2C_SeasonJewelrySkillLevelUp":             reflect.ValueOf((*cl.L2C_SeasonJewelrySkillLevelUp)(nil)),
		"L2C_SeasonJewelryTestReRollSkill":          reflect.ValueOf((*cl.L2C_SeasonJewelryTestReRollSkill)(nil)),
		"L2C_SeasonJewelryUpdate":                   reflect.ValueOf((*cl.L2C_SeasonJewelryUpdate)(nil)),
		"L2C_SeasonJewelryWear":                     reflect.ValueOf((*cl.L2C_SeasonJewelryWear)(nil)),
		"L2C_SeasonLevelGetData":                    reflect.ValueOf((*cl.L2C_SeasonLevelGetData)(nil)),
		"L2C_SeasonLevelRecvLvAwards":               reflect.ValueOf((*cl.L2C_SeasonLevelRecvLvAwards)(nil)),
		"L2C_SeasonLevelRecvTaskAwards":             reflect.ValueOf((*cl.L2C_SeasonLevelRecvTaskAwards)(nil)),
		"L2C_SeasonLevelTaskUpdate":                 reflect.ValueOf((*cl.L2C_SeasonLevelTaskUpdate)(nil)),
		"L2C_SeasonLevelUp":                         reflect.ValueOf((*cl.L2C_SeasonLevelUp)(nil)),
		"L2C_SeasonLinkActivate":                    reflect.ValueOf((*cl.L2C_SeasonLinkActivate)(nil)),
		"L2C_SeasonLinkGetData":                     reflect.ValueOf((*cl.L2C_SeasonLinkGetData)(nil)),
		"L2C_SeasonLinkMonumentChange":              reflect.ValueOf((*cl.L2C_SeasonLinkMonumentChange)(nil)),
		"L2C_SeasonLinkMonumentTakeRareAwards":      reflect.ValueOf((*cl.L2C_SeasonLinkMonumentTakeRareAwards)(nil)),
		"L2C_SeasonMapAltar":                        reflect.ValueOf((*cl.L2C_SeasonMapAltar)(nil)),
		"L2C_SeasonMapBuyStamina":                   reflect.ValueOf((*cl.L2C_SeasonMapBuyStamina)(nil)),
		"L2C_SeasonMapDialogue":                     reflect.ValueOf((*cl.L2C_SeasonMapDialogue)(nil)),
		"L2C_SeasonMapFight":                        reflect.ValueOf((*cl.L2C_SeasonMapFight)(nil)),
		"L2C_SeasonMapGetData":                      reflect.ValueOf((*cl.L2C_SeasonMapGetData)(nil)),
		"L2C_SeasonMapGetRankList":                  reflect.ValueOf((*cl.L2C_SeasonMapGetRankList)(nil)),
		"L2C_SeasonMapGoodsPrice":                   reflect.ValueOf((*cl.L2C_SeasonMapGoodsPrice)(nil)),
		"L2C_SeasonMapMaster":                       reflect.ValueOf((*cl.L2C_SeasonMapMaster)(nil)),
		"L2C_SeasonMapMovePosition":                 reflect.ValueOf((*cl.L2C_SeasonMapMovePosition)(nil)),
		"L2C_SeasonMapPassPosition":                 reflect.ValueOf((*cl.L2C_SeasonMapPassPosition)(nil)),
		"L2C_SeasonMapPositionLogs":                 reflect.ValueOf((*cl.L2C_SeasonMapPositionLogs)(nil)),
		"L2C_SeasonMapPriceChangeLogs":              reflect.ValueOf((*cl.L2C_SeasonMapPriceChangeLogs)(nil)),
		"L2C_SeasonMapRecoverTimeUpdate":            reflect.ValueOf((*cl.L2C_SeasonMapRecoverTimeUpdate)(nil)),
		"L2C_SeasonMapSystemEvent":                  reflect.ValueOf((*cl.L2C_SeasonMapSystemEvent)(nil)),
		"L2C_SeasonMapTaskReward":                   reflect.ValueOf((*cl.L2C_SeasonMapTaskReward)(nil)),
		"L2C_SeasonMapTaskUpdate":                   reflect.ValueOf((*cl.L2C_SeasonMapTaskUpdate)(nil)),
		"L2C_SeasonMapTrade":                        reflect.ValueOf((*cl.L2C_SeasonMapTrade)(nil)),
		"L2C_SeasonReturnGetData":                   reflect.ValueOf((*cl.L2C_SeasonReturnGetData)(nil)),
		"L2C_SeasonReturnTakeAwards":                reflect.ValueOf((*cl.L2C_SeasonReturnTakeAwards)(nil)),
		"L2C_SeasonShopBuy":                         reflect.ValueOf((*cl.L2C_SeasonShopBuy)(nil)),
		"L2C_SeasonShopGetData":                     reflect.ValueOf((*cl.L2C_SeasonShopGetData)(nil)),
		"L2C_SeasonStartTowerRankLike":              reflect.ValueOf((*cl.L2C_SeasonStartTowerRankLike)(nil)),
		"L2C_SeasonStartTowerRankList":              reflect.ValueOf((*cl.L2C_SeasonStartTowerRankList)(nil)),
		"L2C_SelectSummonGetOpenActivity":           reflect.ValueOf((*cl.L2C_SelectSummonGetOpenActivity)(nil)),
		"L2C_SelectSummonSummon":                    reflect.ValueOf((*cl.L2C_SelectSummonSummon)(nil)),
		"L2C_SelectSummonTestSummon":                reflect.ValueOf((*cl.L2C_SelectSummonTestSummon)(nil)),
		"L2C_SelectSummonUpdate":                    reflect.ValueOf((*cl.L2C_SelectSummonUpdate)(nil)),
		"L2C_SellItem":                              reflect.ValueOf((*cl.L2C_SellItem)(nil)),
		"L2C_SetClientInfo":                         reflect.ValueOf((*cl.L2C_SetClientInfo)(nil)),
		"L2C_SetName":                               reflect.ValueOf((*cl.L2C_SetName)(nil)),
		"L2C_SetPush":                               reflect.ValueOf((*cl.L2C_SetPush)(nil)),
		"L2C_SetServerTime":                         reflect.ValueOf((*cl.L2C_SetServerTime)(nil)),
		"L2C_SevenDayLoginData":                     reflect.ValueOf((*cl.L2C_SevenDayLoginData)(nil)),
		"L2C_SevenDayLoginTakeAward":                reflect.ValueOf((*cl.L2C_SevenDayLoginTakeAward)(nil)),
		"L2C_ShopBuy":                               reflect.ValueOf((*cl.L2C_ShopBuy)(nil)),
		"L2C_ShopList":                              reflect.ValueOf((*cl.L2C_ShopList)(nil)),
		"L2C_ShopRefresh":                           reflect.ValueOf((*cl.L2C_ShopRefresh)(nil)),
		"L2C_ShopReset":                             reflect.ValueOf((*cl.L2C_ShopReset)(nil)),
		"L2C_SkinList":                              reflect.ValueOf((*cl.L2C_SkinList)(nil)),
		"L2C_SkinNew":                               reflect.ValueOf((*cl.L2C_SkinNew)(nil)),
		"L2C_SkinUse":                               reflect.ValueOf((*cl.L2C_SkinUse)(nil)),
		"L2C_StoryReviewGetData":                    reflect.ValueOf((*cl.L2C_StoryReviewGetData)(nil)),
		"L2C_StoryReviewUnlock":                     reflect.ValueOf((*cl.L2C_StoryReviewUnlock)(nil)),
		"L2C_Summon":                                reflect.ValueOf((*cl.L2C_Summon)(nil)),
		"L2C_SummonArtifactPointsExchange":          reflect.ValueOf((*cl.L2C_SummonArtifactPointsExchange)(nil)),
		"L2C_SummonGetData":                         reflect.ValueOf((*cl.L2C_SummonGetData)(nil)),
		"L2C_SummonSetHeroAutoDecompose":            reflect.ValueOf((*cl.L2C_SummonSetHeroAutoDecompose)(nil)),
		"L2C_SummonSetWishList":                     reflect.ValueOf((*cl.L2C_SummonSetWishList)(nil)),
		"L2C_SummonSimulation":                      reflect.ValueOf((*cl.L2C_SummonSimulation)(nil)),
		"L2C_SyncQuestionnaire":                     reflect.ValueOf((*cl.L2C_SyncQuestionnaire)(nil)),
		"L2C_TalentTreeCultivateUpdate":             reflect.ValueOf((*cl.L2C_TalentTreeCultivateUpdate)(nil)),
		"L2C_TalentTreeGetData":                     reflect.ValueOf((*cl.L2C_TalentTreeGetData)(nil)),
		"L2C_TalentTreeHot":                         reflect.ValueOf((*cl.L2C_TalentTreeHot)(nil)),
		"L2C_TalentTreeLevelUp":                     reflect.ValueOf((*cl.L2C_TalentTreeLevelUp)(nil)),
		"L2C_TalentTreePlanDelete":                  reflect.ValueOf((*cl.L2C_TalentTreePlanDelete)(nil)),
		"L2C_TalentTreePlanSave":                    reflect.ValueOf((*cl.L2C_TalentTreePlanSave)(nil)),
		"L2C_TalentTreeReceiveTaskAwards":           reflect.ValueOf((*cl.L2C_TalentTreeReceiveTaskAwards)(nil)),
		"L2C_TalentTreeReset":                       reflect.ValueOf((*cl.L2C_TalentTreeReset)(nil)),
		"L2C_TalentTreeTaskUpdate":                  reflect.ValueOf((*cl.L2C_TalentTreeTaskUpdate)(nil)),
		"L2C_TalesChapterFight":                     reflect.ValueOf((*cl.L2C_TalesChapterFight)(nil)),
		"L2C_TalesChapterFinish":                    reflect.ValueOf((*cl.L2C_TalesChapterFinish)(nil)),
		"L2C_TalesChapterTakeReward":                reflect.ValueOf((*cl.L2C_TalesChapterTakeReward)(nil)),
		"L2C_TalesEliteFight":                       reflect.ValueOf((*cl.L2C_TalesEliteFight)(nil)),
		"L2C_TalesEliteWipe":                        reflect.ValueOf((*cl.L2C_TalesEliteWipe)(nil)),
		"L2C_TalesList":                             reflect.ValueOf((*cl.L2C_TalesList)(nil)),
		"L2C_TaskGetInfo":                           reflect.ValueOf((*cl.L2C_TaskGetInfo)(nil)),
		"L2C_TaskReceiveAward":                      reflect.ValueOf((*cl.L2C_TaskReceiveAward)(nil)),
		"L2C_TaskUpdate":                            reflect.ValueOf((*cl.L2C_TaskUpdate)(nil)),
		"L2C_Test":                                  reflect.ValueOf((*cl.L2C_Test)(nil)),
		"L2C_TestBattleData":                        reflect.ValueOf((*cl.L2C_TestBattleData)(nil)),
		"L2C_TestDrop":                              reflect.ValueOf((*cl.L2C_TestDrop)(nil)),
		"L2C_TestEtcdGiftCode":                      reflect.ValueOf((*cl.L2C_TestEtcdGiftCode)(nil)),
		"L2C_TitleList":                             reflect.ValueOf((*cl.L2C_TitleList)(nil)),
		"L2C_TitleNew":                              reflect.ValueOf((*cl.L2C_TitleNew)(nil)),
		"L2C_TitleUse":                              reflect.ValueOf((*cl.L2C_TitleUse)(nil)),
		"L2C_TowerFight":                            reflect.ValueOf((*cl.L2C_TowerFight)(nil)),
		"L2C_TowerJump":                             reflect.ValueOf((*cl.L2C_TowerJump)(nil)),
		"L2C_TowerList":                             reflect.ValueOf((*cl.L2C_TowerList)(nil)),
		"L2C_TowerSeasonCognitionLogs":              reflect.ValueOf((*cl.L2C_TowerSeasonCognitionLogs)(nil)),
		"L2C_TowerSeasonFight":                      reflect.ValueOf((*cl.L2C_TowerSeasonFight)(nil)),
		"L2C_TowerSeasonGetData":                    reflect.ValueOf((*cl.L2C_TowerSeasonGetData)(nil)),
		"L2C_TowerSeasonRankLike":                   reflect.ValueOf((*cl.L2C_TowerSeasonRankLike)(nil)),
		"L2C_TowerSeasonRankList":                   reflect.ValueOf((*cl.L2C_TowerSeasonRankList)(nil)),
		"L2C_TowerSeasonRecvAward":                  reflect.ValueOf((*cl.L2C_TowerSeasonRecvAward)(nil)),
		"L2C_TowerSeasonUpdateTask":                 reflect.ValueOf((*cl.L2C_TowerSeasonUpdateTask)(nil)),
		"L2C_TowerSweep":                            reflect.ValueOf((*cl.L2C_TowerSweep)(nil)),
		"L2C_TowerstarDailyRecvAward":               reflect.ValueOf((*cl.L2C_TowerstarDailyRecvAward)(nil)),
		"L2C_TowerstarFight":                        reflect.ValueOf((*cl.L2C_TowerstarFight)(nil)),
		"L2C_TowerstarGetData":                      reflect.ValueOf((*cl.L2C_TowerstarGetData)(nil)),
		"L2C_TowerstarStarRecvAward":                reflect.ValueOf((*cl.L2C_TowerstarStarRecvAward)(nil)),
		"L2C_TrialAward":                            reflect.ValueOf((*cl.L2C_TrialAward)(nil)),
		"L2C_TrialFight":                            reflect.ValueOf((*cl.L2C_TrialFight)(nil)),
		"L2C_TrialGetInfo":                          reflect.ValueOf((*cl.L2C_TrialGetInfo)(nil)),
		"L2C_TrialPreview":                          reflect.ValueOf((*cl.L2C_TrialPreview)(nil)),
		"L2C_TrialSpeed":                            reflect.ValueOf((*cl.L2C_TrialSpeed)(nil)),
		"L2C_UpdateGems":                            reflect.ValueOf((*cl.L2C_UpdateGems)(nil)),
		"L2C_UpdateTop5":                            reflect.ValueOf((*cl.L2C_UpdateTop5)(nil)),
		"L2C_UseItem":                               reflect.ValueOf((*cl.L2C_UseItem)(nil)),
		"L2C_UserGuildChestItemNotify":              reflect.ValueOf((*cl.L2C_UserGuildChestItemNotify)(nil)),
		"L2C_ViewFormation":                         reflect.ValueOf((*cl.L2C_ViewFormation)(nil)),
		"L2C_ViewUser":                              reflect.ValueOf((*cl.L2C_ViewUser)(nil)),
		"L2C_VipBuyGift":                            reflect.ValueOf((*cl.L2C_VipBuyGift)(nil)),
		"L2C_VipGiftRechargeNotify":                 reflect.ValueOf((*cl.L2C_VipGiftRechargeNotify)(nil)),
		"L2C_VipInfoGet":                            reflect.ValueOf((*cl.L2C_VipInfoGet)(nil)),
		"L2C_WebRechargeNotify":                     reflect.ValueOf((*cl.L2C_WebRechargeNotify)(nil)),
		"L2C_WorldBossFight":                        reflect.ValueOf((*cl.L2C_WorldBossFight)(nil)),
		"L2C_WorldBossGetData":                      reflect.ValueOf((*cl.L2C_WorldBossGetData)(nil)),
		"L2C_WorldBossGetRoomLog":                   reflect.ValueOf((*cl.L2C_WorldBossGetRoomLog)(nil)),
		"L2C_WorldBossRank":                         reflect.ValueOf((*cl.L2C_WorldBossRank)(nil)),
		"L2C_WorldBossRecvAward":                    reflect.ValueOf((*cl.L2C_WorldBossRecvAward)(nil)),
		"L2C_WorldBossRoomInfo":                     reflect.ValueOf((*cl.L2C_WorldBossRoomInfo)(nil)),
		"L2C_WorldBossSelectLevel":                  reflect.ValueOf((*cl.L2C_WorldBossSelectLevel)(nil)),
		"L2C_WorldBossTaskUpdate":                   reflect.ValueOf((*cl.L2C_WorldBossTaskUpdate)(nil)),
		"L2C_WorldBossWorship":                      reflect.ValueOf((*cl.L2C_WorldBossWorship)(nil)),
		"L2C_WrestleChangeRoom":                     reflect.ValueOf((*cl.L2C_WrestleChangeRoom)(nil)),
		"L2C_WrestleFight":                          reflect.ValueOf((*cl.L2C_WrestleFight)(nil)),
		"L2C_WrestleFightLog":                       reflect.ValueOf((*cl.L2C_WrestleFightLog)(nil)),
		"L2C_WrestleHallOfFame":                     reflect.ValueOf((*cl.L2C_WrestleHallOfFame)(nil)),
		"L2C_WrestleInfo":                           reflect.ValueOf((*cl.L2C_WrestleInfo)(nil)),
		"L2C_WrestleLike":                           reflect.ValueOf((*cl.L2C_WrestleLike)(nil)),
		"L2C_WrestleMapInfo":                        reflect.ValueOf((*cl.L2C_WrestleMapInfo)(nil)),
		"L2C_WrestleRankList":                       reflect.ValueOf((*cl.L2C_WrestleRankList)(nil)),
		"L2C_WrestleRecvTaskAward":                  reflect.ValueOf((*cl.L2C_WrestleRecvTaskAward)(nil)),
		"L2C_WrestleRoomInfo":                       reflect.ValueOf((*cl.L2C_WrestleRoomInfo)(nil)),
		"L2C_WrestleRoomUpdate":                     reflect.ValueOf((*cl.L2C_WrestleRoomUpdate)(nil)),
		"L2C_WrestleTopUserList":                    reflect.ValueOf((*cl.L2C_WrestleTopUserList)(nil)),
		"Lattice":                                   reflect.ValueOf((*cl.Lattice)(nil)),
		"LineTask":                                  reflect.ValueOf((*cl.LineTask)(nil)),
		"LinkHeroSummon":                            reflect.ValueOf((*cl.LinkHeroSummon)(nil)),
		"LinkInfo":                                  reflect.ValueOf((*cl.LinkInfo)(nil)),
		"LinkSetting":                               reflect.ValueOf((*cl.LinkSetting)(nil)),
		"LinkSummonTime":                            reflect.ValueOf((*cl.LinkSummonTime)(nil)),
		"LinkSummonTimes":                           reflect.ValueOf((*cl.LinkSummonTimes)(nil)),
		"Mail":                                      reflect.ValueOf((*cl.Mail)(nil)),
		"MailCond":                                  reflect.ValueOf((*cl.MailCond)(nil)),
		"MazeBeSelectBuff":                          reflect.ValueOf((*cl.MazeBeSelectBuff)(nil)),
		"MazeBuff":                                  reflect.ValueOf((*cl.MazeBuff)(nil)),
		"MazeEnemy":                                 reflect.ValueOf((*cl.MazeEnemy)(nil)),
		"MazeGrid":                                  reflect.ValueOf((*cl.MazeGrid)(nil)),
		"MazeHp":                                    reflect.ValueOf((*cl.MazeHp)(nil)),
		"MazeMap":                                   reflect.ValueOf((*cl.MazeMap)(nil)),
		"MazePlayer":                                reflect.ValueOf((*cl.MazePlayer)(nil)),
		"MazeTask":                                  reflect.ValueOf((*cl.MazeTask)(nil)),
		"Medal":                                     reflect.ValueOf((*cl.Medal)(nil)),
		"Memory":                                    reflect.ValueOf((*cl.Memory)(nil)),
		"Message":                                   reflect.ValueOf((*cl.Message)(nil)),
		"MessageBoard":                              reflect.ValueOf((*cl.MessageBoard)(nil)),
		"MiniUserSnapshot":                          reflect.ValueOf((*cl.MiniUserSnapshot)(nil)),
		"Mirage":                                    reflect.ValueOf((*cl.Mirage)(nil)),
		"MirageHeroNum":                             reflect.ValueOf((*cl.MirageHeroNum)(nil)),
		"MonthTasks":                                reflect.ValueOf((*cl.MonthTasks)(nil)),
		"MonthTasksMsg":                             reflect.ValueOf((*cl.MonthTasksMsg)(nil)),
		"MonthlyCard":                               reflect.ValueOf((*cl.MonthlyCard)(nil)),
		"MonthlyCardData":                           reflect.ValueOf((*cl.MonthlyCardData)(nil)),
		"MonthlyTask":                               reflect.ValueOf((*cl.MonthlyTask)(nil)),
		"MonumentRune":                              reflect.ValueOf((*cl.MonumentRune)(nil)),
		"MultipleMiniUserSnapshot":                  reflect.ValueOf((*cl.MultipleMiniUserSnapshot)(nil)),
		"MultipleUserIds":                           reflect.ValueOf((*cl.MultipleUserIds)(nil)),
		"NewYearActivity":                           reflect.ValueOf((*cl.NewYearActivity)(nil)),
		"NumInfo":                                   reflect.ValueOf((*cl.NumInfo)(nil)),
		"OperateActivity":                           reflect.ValueOf((*cl.OperateActivity)(nil)),
		"OperateActivityConfig":                     reflect.ValueOf((*cl.OperateActivityConfig)(nil)),
		"OperateActivityInfo":                       reflect.ValueOf((*cl.OperateActivityInfo)(nil)),
		"OperateGift":                               reflect.ValueOf((*cl.OperateGift)(nil)),
		"OperateGiftInfo":                           reflect.ValueOf((*cl.OperateGiftInfo)(nil)),
		"OperatePageInfo":                           reflect.ValueOf((*cl.OperatePageInfo)(nil)),
		"OperateTask":                               reflect.ValueOf((*cl.OperateTask)(nil)),
		"OperateTaskInfo":                           reflect.ValueOf((*cl.OperateTaskInfo)(nil)),
		"OrderCustomData":                           reflect.ValueOf((*cl.OrderCustomData)(nil)),
		"OreLogType":                                reflect.ValueOf((*cl.OreLogType)(nil)),
		"PassActive":                                reflect.ValueOf((*cl.PassActive)(nil)),
		"PassData":                                  reflect.ValueOf((*cl.PassData)(nil)),
		"PassReceiveSate":                           reflect.ValueOf((*cl.PassReceiveSate)(nil)),
		"PeakMatch":                                 reflect.ValueOf((*cl.PeakMatch)(nil)),
		"PeakMatchFighter":                          reflect.ValueOf((*cl.PeakMatchFighter)(nil)),
		"PeakMatchParam":                            reflect.ValueOf((*cl.PeakMatchParam)(nil)),
		"PeakRankScore":                             reflect.ValueOf((*cl.PeakRankScore)(nil)),
		"PeakResult":                                reflect.ValueOf((*cl.PeakResult)(nil)),
		"PeakState":                                 reflect.ValueOf((*cl.PeakState)(nil)),
		"PeakUser":                                  reflect.ValueOf((*cl.PeakUser)(nil)),
		"PeakUserGuess":                             reflect.ValueOf((*cl.PeakUserGuess)(nil)),
		"PreSeason":                                 reflect.ValueOf((*cl.PreSeason)(nil)),
		"PromotionGift":                             reflect.ValueOf((*cl.PromotionGift)(nil)),
		"PromotionGiftInfo":                         reflect.ValueOf((*cl.PromotionGiftInfo)(nil)),
		"PushGift":                                  reflect.ValueOf((*cl.PushGift)(nil)),
		"PushGiftCondition":                         reflect.ValueOf((*cl.PushGiftCondition)(nil)),
		"PushGiftInfo":                              reflect.ValueOf((*cl.PushGiftInfo)(nil)),
		"PushGifts":                                 reflect.ValueOf((*cl.PushGifts)(nil)),
		"PuzzleCell":                                reflect.ValueOf((*cl.PuzzleCell)(nil)),
		"Pyramid":                                   reflect.ValueOf((*cl.Pyramid)(nil)),
		"PyramidActivityBase":                       reflect.ValueOf((*cl.PyramidActivityBase)(nil)),
		"PyramidTestResult":                         reflect.ValueOf((*cl.PyramidTestResult)(nil)),
		"Questionnaire":                             reflect.ValueOf((*cl.Questionnaire)(nil)),
		"RankAchieve":                               reflect.ValueOf((*cl.RankAchieve)(nil)),
		"RankValue":                                 reflect.ValueOf((*cl.RankValue)(nil)),
		"Rate":                                      reflect.ValueOf((*cl.Rate)(nil)),
		"RedPointIdList":                            reflect.ValueOf((*cl.RedPointIdList)(nil)),
		"Remain":                                    reflect.ValueOf((*cl.Remain)(nil)),
		"RemainBook":                                reflect.ValueOf((*cl.RemainBook)(nil)),
		"RemainItem":                                reflect.ValueOf((*cl.RemainItem)(nil)),
		"Resource":                                  reflect.ValueOf((*cl.Resource)(nil)),
		"Resources":                                 reflect.ValueOf((*cl.Resources)(nil)),
		"Rite":                                      reflect.ValueOf((*cl.Rite)(nil)),
		"RiteGrid":                                  reflect.ValueOf((*cl.RiteGrid)(nil)),
		"RoundActivity":                             reflect.ValueOf((*cl.RoundActivity)(nil)),
		"SeasonAddInfo":                             reflect.ValueOf((*cl.SeasonAddInfo)(nil)),
		"SeasonArenaData":                           reflect.ValueOf((*cl.SeasonArenaData)(nil)),
		"SeasonArenaLog":                            reflect.ValueOf((*cl.SeasonArenaLog)(nil)),
		"SeasonArenaLogDeletes":                     reflect.ValueOf((*cl.SeasonArenaLogDeletes)(nil)),
		"SeasonArenaLogs":                           reflect.ValueOf((*cl.SeasonArenaLogs)(nil)),
		"SeasonArenaOpponentClient":                 reflect.ValueOf((*cl.SeasonArenaOpponentClient)(nil)),
		"SeasonArenaSta":                            reflect.ValueOf((*cl.SeasonArenaSta)(nil)),
		"SeasonArenaTask":                           reflect.ValueOf((*cl.SeasonArenaTask)(nil)),
		"SeasonArenaUserBase":                       reflect.ValueOf((*cl.SeasonArenaUserBase)(nil)),
		"SeasonArenaUserScoreAndRank":               reflect.ValueOf((*cl.SeasonArenaUserScoreAndRank)(nil)),
		"SeasonCompliance":                          reflect.ValueOf((*cl.SeasonCompliance)(nil)),
		"SeasonComplianceStage":                     reflect.ValueOf((*cl.SeasonComplianceStage)(nil)),
		"SeasonDoor":                                reflect.ValueOf((*cl.SeasonDoor)(nil)),
		"SeasonDoorBatchFightResult":                reflect.ValueOf((*cl.SeasonDoorBatchFightResult)(nil)),
		"SeasonDoorFightLine":                       reflect.ValueOf((*cl.SeasonDoorFightLine)(nil)),
		"SeasonDoorFightLineLog":                    reflect.ValueOf((*cl.SeasonDoorFightLineLog)(nil)),
		"SeasonDoorFightResult":                     reflect.ValueOf((*cl.SeasonDoorFightResult)(nil)),
		"SeasonDoorWaveFightResult":                 reflect.ValueOf((*cl.SeasonDoorWaveFightResult)(nil)),
		"SeasonDungeon":                             reflect.ValueOf((*cl.SeasonDungeon)(nil)),
		"SeasonJewelry":                             reflect.ValueOf((*cl.SeasonJewelry)(nil)),
		"SeasonJewelryDecomposeCfg":                 reflect.ValueOf((*cl.SeasonJewelryDecomposeCfg)(nil)),
		"SeasonJewelrySkill":                        reflect.ValueOf((*cl.SeasonJewelrySkill)(nil)),
		"SeasonJewelryWearObject":                   reflect.ValueOf((*cl.SeasonJewelryWearObject)(nil)),
		"SeasonLevel":                               reflect.ValueOf((*cl.SeasonLevel)(nil)),
		"SeasonLink":                                reflect.ValueOf((*cl.SeasonLink)(nil)),
		"SeasonLinkMonument":                        reflect.ValueOf((*cl.SeasonLinkMonument)(nil)),
		"SeasonMap":                                 reflect.ValueOf((*cl.SeasonMap)(nil)),
		"SeasonMapAltarEvent":                       reflect.ValueOf((*cl.SeasonMapAltarEvent)(nil)),
		"SeasonMapBuyStaminaType":                   reflect.ValueOf((*cl.SeasonMapBuyStaminaType)(nil)),
		"SeasonMapConnectEvent":                     reflect.ValueOf((*cl.SeasonMapConnectEvent)(nil)),
		"SeasonMapDialogueEvent":                    reflect.ValueOf((*cl.SeasonMapDialogueEvent)(nil)),
		"SeasonMapEvent":                            reflect.ValueOf((*cl.SeasonMapEvent)(nil)),
		"SeasonMapEventLog":                         reflect.ValueOf((*cl.SeasonMapEventLog)(nil)),
		"SeasonMapEventType":                        reflect.ValueOf((*cl.SeasonMapEventType)(nil)),
		"SeasonMapMasterEvent":                      reflect.ValueOf((*cl.SeasonMapMasterEvent)(nil)),
		"SeasonMapMonster":                          reflect.ValueOf((*cl.SeasonMapMonster)(nil)),
		"SeasonMapMonsterRecord":                    reflect.ValueOf((*cl.SeasonMapMonsterRecord)(nil)),
		"SeasonMapMoveLog":                          reflect.ValueOf((*cl.SeasonMapMoveLog)(nil)),
		"SeasonMapPosition":                         reflect.ValueOf((*cl.SeasonMapPosition)(nil)),
		"SeasonMapPositionLog":                      reflect.ValueOf((*cl.SeasonMapPositionLog)(nil)),
		"SeasonMapPrice":                            reflect.ValueOf((*cl.SeasonMapPrice)(nil)),
		"SeasonMapPriceChangeLog":                   reflect.ValueOf((*cl.SeasonMapPriceChangeLog)(nil)),
		"SeasonMapPriceChangeType":                  reflect.ValueOf((*cl.SeasonMapPriceChangeType)(nil)),
		"SeasonMapPriceLogType":                     reflect.ValueOf((*cl.SeasonMapPriceLogType)(nil)),
		"SeasonMapRecoverType":                      reflect.ValueOf((*cl.SeasonMapRecoverType)(nil)),
		"SeasonMapSystemEvent":                      reflect.ValueOf((*cl.SeasonMapSystemEvent)(nil)),
		"SeasonMapTeamData":                         reflect.ValueOf((*cl.SeasonMapTeamData)(nil)),
		"SeasonMapTradeEvent":                       reflect.ValueOf((*cl.SeasonMapTradeEvent)(nil)),
		"SeasonMapTradeType":                        reflect.ValueOf((*cl.SeasonMapTradeType)(nil)),
		"SeasonReturn":                              reflect.ValueOf((*cl.SeasonReturn)(nil)),
		"SeasonReturnAward":                         reflect.ValueOf((*cl.SeasonReturnAward)(nil)),
		"SeasonShop":                                reflect.ValueOf((*cl.SeasonShop)(nil)),
		"SeasonUserLog":                             reflect.ValueOf((*cl.SeasonUserLog)(nil)),
		"SeasonUserLogPoint":                        reflect.ValueOf((*cl.SeasonUserLogPoint)(nil)),
		"SelectSummon":                              reflect.ValueOf((*cl.SelectSummon)(nil)),
		"SelectSummonOpenActivity":                  reflect.ValueOf((*cl.SelectSummonOpenActivity)(nil)),
		"SelectSummonSummon":                        reflect.ValueOf((*cl.SelectSummonSummon)(nil)),
		"ServerUser":                                reflect.ValueOf((*cl.ServerUser)(nil)),
		"SevenDayLogin":                             reflect.ValueOf((*cl.SevenDayLogin)(nil)),
		"ShareEmblem":                               reflect.ValueOf((*cl.ShareEmblem)(nil)),
		"ShareEquipment":                            reflect.ValueOf((*cl.ShareEquipment)(nil)),
		"ShareGem":                                  reflect.ValueOf((*cl.ShareGem)(nil)),
		"ShareGrowth":                               reflect.ValueOf((*cl.ShareGrowth)(nil)),
		"ShareHero":                                 reflect.ValueOf((*cl.ShareHero)(nil)),
		"Shop":                                      reflect.ValueOf((*cl.Shop)(nil)),
		"Skin":                                      reflect.ValueOf((*cl.Skin)(nil)),
		"StarUpCosts":                               reflect.ValueOf((*cl.StarUpCosts)(nil)),
		"StoryReviewData":                           reflect.ValueOf((*cl.StoryReviewData)(nil)),
		"StrategyInfo":                              reflect.ValueOf((*cl.StrategyInfo)(nil)),
		"StrategyRestoreBoss":                       reflect.ValueOf((*cl.StrategyRestoreBoss)(nil)),
		"SubItem":                                   reflect.ValueOf((*cl.SubItem)(nil)),
		"SweepTestData":                             reflect.ValueOf((*cl.SweepTestData)(nil)),
		"TaleInfo":                                  reflect.ValueOf((*cl.TaleInfo)(nil)),
		"TalentTreeBase":                            reflect.ValueOf((*cl.TalentTreeBase)(nil)),
		"TalentTreeCultivate":                       reflect.ValueOf((*cl.TalentTreeCultivate)(nil)),
		"TalentTreeNode":                            reflect.ValueOf((*cl.TalentTreeNode)(nil)),
		"TalentTreeNodeHot":                         reflect.ValueOf((*cl.TalentTreeNodeHot)(nil)),
		"TalentTreePlan":                            reflect.ValueOf((*cl.TalentTreePlan)(nil)),
		"TalentTreeResetUnit":                       reflect.ValueOf((*cl.TalentTreeResetUnit)(nil)),
		"Tales":                                     reflect.ValueOf((*cl.Tales)(nil)),
		"TargetAttr":                                reflect.ValueOf((*cl.TargetAttr)(nil)),
		"TaskProgress":                              reflect.ValueOf((*cl.TaskProgress)(nil)),
		"TaskTypeProgress":                          reflect.ValueOf((*cl.TaskTypeProgress)(nil)),
		"Title":                                     reflect.ValueOf((*cl.Title)(nil)),
		"Tower":                                     reflect.ValueOf((*cl.Tower)(nil)),
		"TowerSeason":                               reflect.ValueOf((*cl.TowerSeason)(nil)),
		"Towerstar":                                 reflect.ValueOf((*cl.Towerstar)(nil)),
		"TowerstarChapterInfo":                      reflect.ValueOf((*cl.TowerstarChapterInfo)(nil)),
		"TrialInfo":                                 reflect.ValueOf((*cl.TrialInfo)(nil)),
		"TrialOnHook":                               reflect.ValueOf((*cl.TrialOnHook)(nil)),
		"User":                                      reflect.ValueOf((*cl.User)(nil)),
		"UserBattleData":                            reflect.ValueOf((*cl.UserBattleData)(nil)),
		"UserChatHead":                              reflect.ValueOf((*cl.UserChatHead)(nil)),
		"UserGuild":                                 reflect.ValueOf((*cl.UserGuild)(nil)),
		"UserGuildChestItem":                        reflect.ValueOf((*cl.UserGuildChestItem)(nil)),
		"UserSnapshot":                              reflect.ValueOf((*cl.UserSnapshot)(nil)),
		"ViewLinks":                                 reflect.ValueOf((*cl.ViewLinks)(nil)),
		"VipInfo":                                   reflect.ValueOf((*cl.VipInfo)(nil)),
		"WeeklyTask":                                reflect.ValueOf((*cl.WeeklyTask)(nil)),
		"WishInfo":                                  reflect.ValueOf((*cl.WishInfo)(nil)),
		"WishListMes":                               reflect.ValueOf((*cl.WishListMes)(nil)),
		"WorldBoss":                                 reflect.ValueOf((*cl.WorldBoss)(nil)),
		"WorldBossData":                             reflect.ValueOf((*cl.WorldBossData)(nil)),
		"WorldBossRoom":                             reflect.ValueOf((*cl.WorldBossRoom)(nil)),
		"WorldBossRoomLog":                          reflect.ValueOf((*cl.WorldBossRoomLog)(nil)),
		"WorldBossTask":                             reflect.ValueOf((*cl.WorldBossTask)(nil)),
		"WrestleLog":                                reflect.ValueOf((*cl.WrestleLog)(nil)),
		"WrestleLogDeletes":                         reflect.ValueOf((*cl.WrestleLogDeletes)(nil)),
		"WrestleLogs":                               reflect.ValueOf((*cl.WrestleLogs)(nil)),
		"WrestleOpponentInfo":                       reflect.ValueOf((*cl.WrestleOpponentInfo)(nil)),
		"WrestleUserScoreAndRank":                   reflect.ValueOf((*cl.WrestleUserScoreAndRank)(nil)),
	}
}
