// Code generated by 'yaegi extract app/logic/character'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/goxml"
	"app/hotfix"
	"app/logic/account"
	"app/logic/character"
	"app/logic/cross"
	"app/logic/etcdcache"
	"app/logic/mongo"
	"app/logic/multilang"
	"app/logic/oss"
	"app/logic/rank"
	"app/logic/rankachieve"
	"app/logic/request"
	"app/logic/session"
	"app/protos/in/config"
	"app/protos/in/db"
	"app/protos/in/gm"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/platform/proto/da"
	"go/constant"
	"go/token"
	"reflect"
)

func init() {
	Symbols["app/logic/character/character"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"AccountTagNone":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"AccountTagWelfare":                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"AccountTypeAssault":                        reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"AccountTypeBlitz":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"AccountTypeCorrosion":                      reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"AccountTypeFullLevel":                      reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"AccountTypeNoControl":                      reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"AccountTypeRebound":                        reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"AccountTypeResurrect":                      reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"ActiveSkillHoles":                          reflect.ValueOf(&character.ActiveSkillHoles).Elem(),
		"ActivityComplianceMail":                    reflect.ValueOf(character.ActivityComplianceMail),
		"ActivityComplianceReparationMail":          reflect.ValueOf(character.ActivityComplianceReparationMail),
		"ActivityMirageCompliance":                  reflect.ValueOf(character.ActivityMirageCompliance),
		"ActivityMirageRankAwardMail":               reflect.ValueOf(character.ActivityMirageRankAwardMail),
		"ActivityStoryMail":                         reflect.ValueOf(character.ActivityStoryMail),
		"ActivitySumFuncManager":                    reflect.ValueOf(&character.ActivitySumFuncManager).Elem(),
		"ActivitySumPuzzleResetMail":                reflect.ValueOf(character.ActivitySumPuzzleResetMail),
		"ActivityTowerCompliance":                   reflect.ValueOf(character.ActivityTowerCompliance),
		"ActivityTowerRankAwardMail":                reflect.ValueOf(character.ActivityTowerRankAwardMail),
		"ActivityTowerSeasonRankAwardMail":          reflect.ValueOf(character.ActivityTowerSeasonRankAwardMail),
		"ActivityTurnTableResetMail":                reflect.ValueOf(character.ActivityTurnTableResetMail),
		"ActivityWebMail":                           reflect.ValueOf(character.ActivityWebMail),
		"AiAttackPool":                              reflect.ValueOf(&character.AiAttackPool).Elem(),
		"AiDefensePool":                             reflect.ValueOf(&character.AiDefensePool).Elem(),
		"ArenaMail":                                 reflect.ValueOf(character.ArenaMail),
		"ArtifactDebutActivityGetLimit":             reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"ArtifactDebutGuaranteeTypeNone":            reflect.ValueOf(character.ArtifactDebutGuaranteeTypeNone),
		"ArtifactDebutGuaranteeTypeRegular":         reflect.ValueOf(character.ArtifactDebutGuaranteeTypeRegular),
		"ArtifactDebutGuaranteeTypeRound":           reflect.ValueOf(character.ArtifactDebutGuaranteeTypeRound),
		"ArtifactDebutMail":                         reflect.ValueOf(character.ArtifactDebutMail),
		"ArtifactDebutRecvAwardCountLimit":          reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"ArtifactDebutSummonCap":                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ArtifactOneKeyForgeLimit":                  reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"ArtifactOneKeyStrengthLimit":               reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"ArtifactRarePurple":                        reflect.ValueOf(constant.MakeFromLiteral("40", token.INT, 0)),
		"ArtifactRareRed":                           reflect.ValueOf(constant.MakeFromLiteral("60", token.INT, 0)),
		"ArtifactSkill2OpenStar":                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ArtifactSkill3OpenStar":                    reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"AssocSummon":                               reflect.ValueOf(&character.AssocSummon).Elem(),
		"AwardRedPointID2MirageCopyID":              reflect.ValueOf(&character.AwardRedPointID2MirageCopyID).Elem(),
		"AwardTagMail":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"AwardTagMerged":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"AwardTagNone":                              reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"BagSpaceLimitTypeNum":                      reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"BattleEVE":                                 reflect.ValueOf(character.BattleEVE),
		"BattleFormationTestBestHero":               reflect.ValueOf(&character.BattleFormationTestBestHero).Elem(),
		"BattleListRequireHeroRare":                 reflect.ValueOf(character.BattleListRequireHeroRare),
		"BattleOpTypeAutoSweep":                     reflect.ValueOf(character.BattleOpTypeAutoSweep),
		"BattleOpTypeCrush":                         reflect.ValueOf(character.BattleOpTypeCrush),
		"BattleOpTypeFight":                         reflect.ValueOf(character.BattleOpTypeFight),
		"BattleOpTypeSweep":                         reflect.ValueOf(character.BattleOpTypeSweep),
		"BattlePVE":                                 reflect.ValueOf(character.BattlePVE),
		"BattlePVP":                                 reflect.ValueOf(character.BattlePVP),
		"BattleReportCompressBytes":                 reflect.ValueOf(constant.MakeFromLiteral("51200", token.INT, 0)),
		"BattleReportSegmentSize":                   reflect.ValueOf(constant.MakeFromLiteral("64500", token.INT, 0)),
		"BattleWinTypeAllWin":                       reflect.ValueOf(character.BattleWinTypeAllWin),
		"BattleWinTypeForceWin":                     reflect.ValueOf(character.BattleWinTypeForceWin),
		"BattleWinTypeHalfWin":                      reflect.ValueOf(character.BattleWinTypeHalfWin),
		"BodyPoolID1":                               reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"BodyPoolID2":                               reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"BodyPoolID3":                               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"BodyPoolID4":                               reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"BodyPoolID5":                               reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"BodyPoolValueAdd":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"BodyPoolValueDec":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"BodyPoolValueStay":                         reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"BodyTrainNumOne":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"BodyTrainNumTen":                           reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"CalcLeftHpPct":                             reflect.ValueOf(character.CalcLeftHpPct),
		"CarnivalFourteen":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"CarnivalFourteenPointsTaskTypeID":          reflect.ValueOf(constant.MakeFromLiteral("5100002", token.INT, 0)),
		"CarnivalSeasonPointsTaskTypeID":            reflect.ValueOf(constant.MakeFromLiteral("5100003", token.INT, 0)),
		"CarnivalSeven":                             reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CarnivalSevenPointsTaskTypeID":             reflect.ValueOf(constant.MakeFromLiteral("5100001", token.INT, 0)),
		"ChangeTagFlushPill":                        reflect.ValueOf(character.ChangeTagFlushPill),
		"ChangeTagFlushPower":                       reflect.ValueOf(character.ChangeTagFlushPower),
		"CharacterMetrics":                          reflect.ValueOf(&character.CharacterMetrics).Elem(),
		"ChatAutoJoinGroupToPlatform":               reflect.ValueOf(character.ChatAutoJoinGroupToPlatform),
		"ChatContentLength":                         reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"ChatGroupOpJoin":                           reflect.ValueOf(character.ChatGroupOpJoin),
		"ChatGroupOpQuit":                           reflect.ValueOf(character.ChatGroupOpQuit),
		"ChatGroupOptSeven":                         reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"ChatGroupOptSix":                           reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"ChatGroupType":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ChatGroupTypeBig":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ChatGroupTypeCommon":                       reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"ChatGroupVerificationNeed":                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ChatGroupVerificationNo":                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ChatGstPushMessageToPlatform":              reflect.ValueOf(character.ChatGstPushMessageToPlatform),
		"ChatJoinAllWorldGroupToPlatform":           reflect.ValueOf(character.ChatJoinAllWorldGroupToPlatform),
		"ChatJoinGST":                               reflect.ValueOf(character.ChatJoinGST),
		"ChatJoinGuildDungeonGroupToPlatform":       reflect.ValueOf(character.ChatJoinGuildDungeonGroupToPlatform),
		"ChatJoinGuildGroupToPlatform":              reflect.ValueOf(character.ChatJoinGuildGroupToPlatform),
		"ChatNotificationType":                      reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"ChatQuitGST":                               reflect.ValueOf(character.ChatQuitGST),
		"ChatSendMsgToPlatform":                     reflect.ValueOf(character.ChatSendMsgToPlatform),
		"ChatSendMsgToPlatformForOtherGuild":        reflect.ValueOf(character.ChatSendMsgToPlatformForOtherGuild),
		"ChatSingleType":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ChatSuperGroupType":                        reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"ChatSyncUserToPlatform":                    reflect.ValueOf(character.ChatSyncUserToPlatform),
		"CheckActivityTypeOpen":                     reflect.ValueOf(character.CheckActivityTypeOpen),
		"CheckBattleDebug":                          reflect.ValueOf(character.CheckBattleDebug),
		"CheckDropOpen":                             reflect.ValueOf(character.CheckDropOpen),
		"CheckFeedOpen":                             reflect.ValueOf(character.CheckFeedOpen),
		"CheckPuzzleOpen":                           reflect.ValueOf(character.CheckPuzzleOpen),
		"CheckServerDropOpen":                       reflect.ValueOf(character.CheckServerDropOpen),
		"CheckTaskFinish":                           reflect.ValueOf(character.CheckTaskFinish),
		"CheckTurnTableOpen":                        reflect.ValueOf(character.CheckTurnTableOpen),
		"CloneResources":                            reflect.ValueOf(character.CloneResources),
		"CommandMaxParamLen":                        reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"CompExpectType":                            reflect.ValueOf(character.CompExpectType),
		"CompRandomType":                            reflect.ValueOf(character.CompRandomType),
		"ComplianceTasksEventCheckFunc":             reflect.ValueOf(&character.ComplianceTasksEventCheckFunc).Elem(),
		"Content":                                   reflect.ValueOf(character.Content),
		"CouponAwardTypeExtra":                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"CouponAwardTypeNormal":                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CouponRechargeMail":                        reflect.ValueOf(character.CouponRechargeMail),
		"CreateAiTool":                              reflect.ValueOf(character.CreateAiTool),
		"DailyAttendanceMail":                       reflect.ValueOf(character.DailyAttendanceMail),
		"DailyPlayTimeMax":                          reflect.ValueOf(character.DailyPlayTimeMax),
		"DailyWishMail":                             reflect.ValueOf(character.DailyWishMail),
		"DataLoadNone":                              reflect.ValueOf(character.DataLoadNone),
		"DataLoaded":                                reflect.ValueOf(character.DataLoaded),
		"DataLoading":                               reflect.ValueOf(character.DataLoading),
		"DaySecs":                                   reflect.ValueOf(constant.MakeFromLiteral("86400", token.INT, 0)),
		"DefaultUnlockSlotCount":                    reflect.ValueOf(character.DefaultUnlockSlotCount),
		"DispatchFormulaParam":                      reflect.ValueOf(character.DispatchFormulaParam),
		"DivineDemonMail":                           reflect.ValueOf(character.DivineDemonMail),
		"DoBattleResult":                            reflect.ValueOf(character.DoBattleResult),
		"DoDrop":                                    reflect.ValueOf(character.DoDrop),
		"DropActivityMail":                          reflect.ValueOf(character.DropActivityMail),
		"EightHourSecs":                             reflect.ValueOf(constant.MakeFromLiteral("28800", token.INT, 0)),
		"EmblemInitialBlessingLevel":                reflect.ValueOf(character.EmblemInitialBlessingLevel),
		"EmblemInitialStrengthLevel":                reflect.ValueOf(character.EmblemInitialStrengthLevel),
		"EmblemNum":                                 reflect.ValueOf(character.EmblemNum),
		"EmblemSlotNone":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"EmblemSlotOne":                             reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"EmblemSlotTwo":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"EmblemWearMaxNum":                          reflect.ValueOf(character.EmblemWearMaxNum),
		"EquipClothes":                              reflect.ValueOf(character.EquipClothes),
		"EquipHelmet":                               reflect.ValueOf(character.EquipHelmet),
		"EquipInitialEnchantLevel":                  reflect.ValueOf(character.EquipInitialEnchantLevel),
		"EquipInitialEnchantSeed":                   reflect.ValueOf(character.EquipInitialEnchantSeed),
		"EquipInitialEvolutionLevel":                reflect.ValueOf(character.EquipInitialEvolutionLevel),
		"EquipInitialRefineExp":                     reflect.ValueOf(character.EquipInitialRefineExp),
		"EquipInitialRefineLevel":                   reflect.ValueOf(character.EquipInitialRefineLevel),
		"EquipInitialStrengthLevel":                 reflect.ValueOf(character.EquipInitialStrengthLevel),
		"EquipNum":                                  reflect.ValueOf(character.EquipNum),
		"EquipOneKeyRemove":                         reflect.ValueOf(character.EquipOneKeyRemove),
		"EquipOneKeyWear":                           reflect.ValueOf(character.EquipOneKeyWear),
		"EquipQuickLimit":                           reflect.ValueOf(character.EquipQuickLimit),
		"EquipRemove":                               reflect.ValueOf(character.EquipRemove),
		"EquipShoes":                                reflect.ValueOf(character.EquipShoes),
		"EquipWeapon":                               reflect.ValueOf(character.EquipWeapon),
		"EquipWear":                                 reflect.ValueOf(character.EquipWear),
		"EquipWearMaxNum":                           reflect.ValueOf(character.EquipWearMaxNum),
		"EquipmentMaxStage":                         reflect.ValueOf(character.EquipmentMaxStage),
		"EquipmentSuitExtra":                        reflect.ValueOf(character.EquipmentSuitExtra),
		"EquipmentSuitFour":                         reflect.ValueOf(character.EquipmentSuitFour),
		"EquipmentSuitTwo":                          reflect.ValueOf(character.EquipmentSuitTwo),
		"EquipmentUpgradeStone":                     reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"Event2ActivityType":                        reflect.ValueOf(&character.Event2ActivityType).Elem(),
		"FacContestAppointMaxNum":                   reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"FacContestFloorAppoint":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FacContestFloorBattle":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FacContestWinLeftHpXp":                     reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"FacContestWinLiveRoundX":                   reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"FacContestWinLiveX":                        reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FacContestWinRoundX":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FairyDivineSkillMax":                       reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"FairySpringHealAll":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FairySpringHealSingle":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FairySpringRebirth":                        reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"FairylandFirstStage":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FightRedPointID2MirageCopyID":              reflect.ValueOf(&character.FightRedPointID2MirageCopyID).Elem(),
		"FightTimeLimit":                            reflect.ValueOf(character.FightTimeLimit),
		"FirstCrystalSlotID":                        reflect.ValueOf(character.FirstCrystalSlotID),
		"FlowerOccupyAssistLimitCount":              reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"FlowerOccupyMail":                          reflect.ValueOf(character.FlowerOccupyMail),
		"FlowerPushMsg":                             reflect.ValueOf(character.FlowerPushMsg),
		"FlushDrop2Client":                          reflect.ValueOf(character.FlushDrop2Client),
		"FlushFeed2Client":                          reflect.ValueOf(character.FlushFeed2Client),
		"FlushPuzzle2Client":                        reflect.ValueOf(character.FlushPuzzle2Client),
		"FlushShoot2Client":                         reflect.ValueOf(character.FlushShoot2Client),
		"FlushTurnTable2Client":                     reflect.ValueOf(character.FlushTurnTable2Client),
		"ForbiddenFunction":                         reflect.ValueOf(&character.ForbiddenFunction).Elem(),
		"FormatEquipmentMaxSlot":                    reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"FormatEquipmentMinSlot":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FormatPetMaxSlot":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"FormatPetMinSlot":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FormationArtifMaxPos":                      reflect.ValueOf(character.FormationArtifMaxPos),
		"FormationMaxPos":                           reflect.ValueOf(character.FormationMaxPos),
		"FormationPowerUpdateByGlobalAttr":          reflect.ValueOf(character.FormationPowerUpdateByGlobalAttr),
		"FormationPowerUpdateByHero":                reflect.ValueOf(character.FormationPowerUpdateByHero),
		"FormationPowerUpdateByIds":                 reflect.ValueOf(character.FormationPowerUpdateByIds),
		"FriendRequestsMaxNum":                      reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"FuncLevelConAnd":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FuncLevelConOr":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GMDeleteServerMail":                        reflect.ValueOf(character.GMDeleteServerMail),
		"GMMail":                                    reflect.ValueOf(character.GMMail),
		"GMServerMail":                              reflect.ValueOf(character.GMServerMail),
		"GSTArenaFix":                               reflect.ValueOf(character.GSTArenaFix),
		"GSTBossMaxRound":                           reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"GSTChallengeFightFix":                      reflect.ValueOf(character.GSTChallengeFightFix),
		"GSTChallengeSettlementMail":                reflect.ValueOf(character.GSTChallengeSettlementMail),
		"GSTDragonMaxRound":                         reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"GSTDragonSettlementMail":                   reflect.ValueOf(character.GSTDragonSettlementMail),
		"GSTDragonTaskMail":                         reflect.ValueOf(character.GSTDragonTaskMail),
		"GSTFightFix":                               reflect.ValueOf(character.GSTFightFix),
		"GSTFightUpdate":                            reflect.ValueOf(character.GSTFightUpdate),
		"GSTFightUpdateHpPct":                       reflect.ValueOf(character.GSTFightUpdateHpPct),
		"GSTFirstOccupyMail":                        reflect.ValueOf(character.GSTFirstOccupyMail),
		"GSTLeaderMail":                             reflect.ValueOf(character.GSTLeaderMail),
		"GSTNormalFix":                              reflect.ValueOf(character.GSTNormalFix),
		"GSTPVEFight":                               reflect.ValueOf(character.GSTPVEFight),
		"GSTPVPFight":                               reflect.ValueOf(character.GSTPVPFight),
		"GSTRankMail":                               reflect.ValueOf(character.GSTRankMail),
		"GameYearsPerDay":                           reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"GemLeftSlot":                               reflect.ValueOf(character.GemLeftSlot),
		"GemNoRebuild":                              reflect.ValueOf(character.GemNoRebuild),
		"GemRightSlot":                              reflect.ValueOf(character.GemRightSlot),
		"GemSlotNum":                                reflect.ValueOf(character.GemSlotNum),
		"GemTypeRemove":                             reflect.ValueOf(character.GemTypeRemove),
		"GemTypeWear":                               reflect.ValueOf(character.GemTypeWear),
		"GemWearMaxNum":                             reflect.ValueOf(character.GemWearMaxNum),
		"GetBattleDataFromUser":                     reflect.ValueOf(character.GetBattleDataFromUser),
		"GetDropActivity":                           reflect.ValueOf(character.GetDropActivity),
		"GetEvent":                                  reflect.ValueOf(character.GetEvent),
		"GetEventHandler":                           reflect.ValueOf(character.GetEventHandler),
		"GetGodPresentCheckOpenI":                   reflect.ValueOf(character.GetGodPresentCheckOpenI),
		"GetMailIDByResourceReason":                 reflect.ValueOf(character.GetMailIDByResourceReason),
		"GetMinRequireStar":                         reflect.ValueOf(character.GetMinRequireStar),
		"GetOperateFrequencyTime":                   reflect.ValueOf(character.GetOperateFrequencyTime),
		"GetServiceDropActivity":                    reflect.ValueOf(character.GetServiceDropActivity),
		"GetShopGoodOpenI":                          reflect.ValueOf(character.GetShopGoodOpenI),
		"GetStarLimitMinRequireStarByStar":          reflect.ValueOf(character.GetStarLimitMinRequireStarByStar),
		"GetStarLimitRequireStar":                   reflect.ValueOf(character.GetStarLimitRequireStar),
		"GetUserFromUserBattleData":                 reflect.ValueOf(character.GetUserFromUserBattleData),
		"GiftCodeMaxLength":                         reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"GodPresentMail":                            reflect.ValueOf(character.GodPresentMail),
		"GroupMail":                                 reflect.ValueOf(character.GroupMail),
		"GuildBadgeBackground":                      reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildBadgeIcon":                            reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildDungeonAwardReceiveAllThroughChapter": reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GuildDungeonAwardReceiveBoxes":             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildDungeonAwardReceiveOneChapter":        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildDungeonBoxCount":                      reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GuildDungeonMail":                          reflect.ValueOf(character.GuildDungeonMail),
		"GuildDungeonMaxRound":                      reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"GuildGradeDeputy":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildGradeLeader":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildGradeMember":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GuildGradeRegiment":                        reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GuildMail":                                 reflect.ValueOf(character.GuildMail),
		"GuildManagerDeputy":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildManagerKick":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildManagerLeader":                        reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GuildManagerRegiment":                      reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GuildMobMail":                              reflect.ValueOf(character.GuildMobMail),
		"GuildUserApply":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildUserCancelApply":                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildUserJoinIn":                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GuildUserQuickJoin":                        reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GuildUserQuickTransfer":                    reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"HalfHourSecs":                              reflect.ValueOf(constant.MakeFromLiteral("1800", token.INT, 0)),
		"HallOfFameShow":                            reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"HallOfFameTopThree":                        reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"HasIllegalNameRune":                        reflect.ValueOf(&character.HasIllegalNameRune).Elem(),
		"Head":                                      reflect.ValueOf(character.Head),
		"HeroBackLegalStar":                         reflect.ValueOf(&character.HeroBackLegalStar).Elem(),
		"HeroChange2SummonGroupID":                  reflect.ValueOf(&character.HeroChange2SummonGroupID).Elem(),
		"HeroChangeLegalRace":                       reflect.ValueOf(&character.HeroChangeLegalRace).Elem(),
		"HeroChangeLegalStar":                       reflect.ValueOf(&character.HeroChangeLegalStar).Elem(),
		"HeroHandbookActiveAuto":                    reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"HeroHandbookAwardStar":                     reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"HeroOpTypeAddNew":                          reflect.ValueOf(character.HeroOpTypeAddNew),
		"HeroOpTypeBack":                            reflect.ValueOf(character.HeroOpTypeBack),
		"HeroOpTypeDelete":                          reflect.ValueOf(character.HeroOpTypeDelete),
		"HeroOpTypeGemLevelUp":                      reflect.ValueOf(character.HeroOpTypeGemLevelUp),
		"HeroOpTypeLevelUp":                         reflect.ValueOf(character.HeroOpTypeLevelUp),
		"HeroOpTypeRevive":                          reflect.ValueOf(character.HeroOpTypeRevive),
		"HeroOpTypeStageUp":                         reflect.ValueOf(character.HeroOpTypeStageUp),
		"HeroOpTypeStarUp":                          reflect.ValueOf(character.HeroOpTypeStarUp),
		"HeroStarUpCostInitCap":                     reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"Id":                                        reflect.ValueOf(character.Id),
		"IllegalNameRune":                           reflect.ValueOf(&character.IllegalNameRune).Elem(),
		"IndexFour":                                 reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"IndexOne":                                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"IndexThree":                                reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"IndexTwo":                                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"InitAiToolConfig":                          reflect.ValueOf(character.InitAiToolConfig),
		"InitTestData":                              reflect.ValueOf(character.InitTestData),
		"InitTriggerEvent":                          reflect.ValueOf(&character.InitTriggerEvent).Elem(),
		"IsDropActivityRechargeShopOpen":            reflect.ValueOf(character.IsDropActivityRechargeShopOpen),
		"IsMirageOpen":                              reflect.ValueOf(character.IsMirageOpen),
		"IsNewServerDefaultActivity":                reflect.ValueOf(character.IsNewServerDefaultActivity),
		"IsSeasonAddHero":                           reflect.ValueOf(character.IsSeasonAddHero),
		"ItemHeroExp":                               reflect.ValueOf(constant.MakeFromLiteral("10001", token.INT, 0)),
		"JSONMarshal":                               reflect.ValueOf(character.JSONMarshal),
		"KnightMainRole":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"KnightPet":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"LeftCloseRightCloseSearch":                 reflect.ValueOf(character.LeftCloseRightCloseSearch),
		"LeftOpenRightCloseSearch":                  reflect.ValueOf(character.LeftOpenRightCloseSearch),
		"LevelupInviteMaxProp":                      reflect.ValueOf(character.LevelupInviteMaxProp),
		"LinkSummonGuaranteeLen":                    reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"LuckyChanceBattleMonster":                  reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"LuckyChanceBattleSelf":                     reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"LuckyChanceBeton":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"LuckyChanceMaxNum":                         reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"LuckyChanceSelect":                         reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"LuckyChanceShop":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"MailIDActivityComplianceRank":              reflect.ValueOf(character.MailIDActivityComplianceRank),
		"MailIDActivityComplianceReparation":        reflect.ValueOf(character.MailIDActivityComplianceReparation),
		"MailIDActivityMirageRank":                  reflect.ValueOf(character.MailIDActivityMirageRank),
		"MailIDActivityStory":                       reflect.ValueOf(character.MailIDActivityStory),
		"MailIDActivityTowerRank":                   reflect.ValueOf(character.MailIDActivityTowerRank),
		"MailIDActivityTowerSeasonRank":             reflect.ValueOf(character.MailIDActivityTowerSeasonRank),
		"MailIDActivityWeb":                         reflect.ValueOf(character.MailIDActivityWeb),
		"MailIDArenaAvatarFrame":                    reflect.ValueOf(character.MailIDArenaAvatarFrame),
		"MailIDArenaDaily":                          reflect.ValueOf(character.MailIDArenaDaily),
		"MailIDArenaSeason":                         reflect.ValueOf(character.MailIDArenaSeason),
		"MailIDArtifactDebut":                       reflect.ValueOf(character.MailIDArtifactDebut),
		"MailIDBagFull":                             reflect.ValueOf(character.MailIDBagFull),
		"MailIDBagFullDungeon":                      reflect.ValueOf(character.MailIDBagFullDungeon),
		"MailIDBagFullForest":                       reflect.ValueOf(character.MailIDBagFullForest),
		"MailIDBagFullMirage":                       reflect.ValueOf(character.MailIDBagFullMirage),
		"MailIDDailyAttendance":                     reflect.ValueOf(character.MailIDDailyAttendance),
		"MailIDDailyWishCloseActivity":              reflect.ValueOf(character.MailIDDailyWishCloseActivity),
		"MailIDDivineDemonTask":                     reflect.ValueOf(character.MailIDDivineDemonTask),
		"MailIDFlowerOccupyAwards":                  reflect.ValueOf(character.MailIDFlowerOccupyAwards),
		"MailIDGM":                                  reflect.ValueOf(character.MailIDGM),
		"MailIDGSTChallengeTotalScoreRank":          reflect.ValueOf(character.MailIDGSTChallengeTotalScoreRank),
		"MailIDGodPresentThird":                     reflect.ValueOf(character.MailIDGodPresentThird),
		"MailIDGstDragonLose":                       reflect.ValueOf(character.MailIDGstDragonLose),
		"MailIDGstDragonTask":                       reflect.ValueOf(character.MailIDGstDragonTask),
		"MailIDGstDragonWin":                        reflect.ValueOf(character.MailIDGstDragonWin),
		"MailIDGstFirstOccupy":                      reflect.ValueOf(character.MailIDGstFirstOccupy),
		"MailIDGstGroupRank":                        reflect.ValueOf(character.MailIDGstGroupRank),
		"MailIDGstLeader":                           reflect.ValueOf(character.MailIDGstLeader),
		"MailIDGuildBeApprove":                      reflect.ValueOf(character.MailIDGuildBeApprove),
		"MailIDGuildBeKick":                         reflect.ValueOf(character.MailIDGuildBeKick),
		"MailIDGuildDonateAward":                    reflect.ValueOf(character.MailIDGuildDonateAward),
		"MailIDGuildDungeonBossBoxCompensateAwards": reflect.ValueOf(character.MailIDGuildDungeonBossBoxCompensateAwards),
		"MailIDGuildDungeonMonthlyRank":             reflect.ValueOf(character.MailIDGuildDungeonMonthlyRank),
		"MailIDGuildDungeonTaskCompensateAwards":    reflect.ValueOf(character.MailIDGuildDungeonTaskCompensateAwards),
		"MailIDGuildDungeonWeeklyRank":              reflect.ValueOf(character.MailIDGuildDungeonWeeklyRank),
		"MailIDGuildLeaderAutoTransfer":             reflect.ValueOf(character.MailIDGuildLeaderAutoTransfer),
		"MailIDGuildLeaderAutoTransferForNewLeader": reflect.ValueOf(character.MailIDGuildLeaderAutoTransferForNewLeader),
		"MailIDGuildLeaderAutoTransferForOldLeader": reflect.ValueOf(character.MailIDGuildLeaderAutoTransferForOldLeader),
		"MailIDGuildLeaderTransfer":                 reflect.ValueOf(character.MailIDGuildLeaderTransfer),
		"MailIDGuildLeaderTransferForNewLeader":     reflect.ValueOf(character.MailIDGuildLeaderTransferForNewLeader),
		"MailIDGuildSendMail":                       reflect.ValueOf(character.MailIDGuildSendMail),
		"MailIDLevelup":                             reflect.ValueOf(character.MailIDLevelup),
		"MailIDMirageCompliance":                    reflect.ValueOf(character.MailIDMirageCompliance),
		"MailIDMonthlyCardGrowUpAward":              reflect.ValueOf(character.MailIDMonthlyCardGrowUpAward),
		"MailIDMonthlyCardGrowUpExpired":            reflect.ValueOf(character.MailIDMonthlyCardGrowUpExpired),
		"MailIDMonthlyCardGrowUpSoonExpire":         reflect.ValueOf(character.MailIDMonthlyCardGrowUpSoonExpire),
		"MailIDMonthlyCardSupplyAward":              reflect.ValueOf(character.MailIDMonthlyCardSupplyAward),
		"MailIDMonthlyCardSupplyExpired":            reflect.ValueOf(character.MailIDMonthlyCardSupplyExpired),
		"MailIDMonthlyCardSupplySoonExpire":         reflect.ValueOf(character.MailIDMonthlyCardSupplySoonExpire),
		"MailIDPassDailyActive":                     reflect.ValueOf(character.MailIDPassDailyActive),
		"MailIDPassDiamondActive":                   reflect.ValueOf(character.MailIDPassDiamondActive),
		"MailIDPassMazeActive":                      reflect.ValueOf(character.MailIDPassMazeActive),
		"MailIDPassProphetActive":                   reflect.ValueOf(character.MailIDPassProphetActive),
		"MailIDPeakGuess":                           reflect.ValueOf(character.MailIDPeakGuess),
		"MailIDPeakPhase":                           reflect.ValueOf(character.MailIDPeakPhase),
		"MailIDPeakSeason":                          reflect.ValueOf(character.MailIDPeakSeason),
		"MailIDQuestionnaire":                       reflect.ValueOf(character.MailIDQuestionnaire),
		"MailIDRebase":                              reflect.ValueOf(character.MailIDRebase),
		"MailIDRechargeCouponExtra":                 reflect.ValueOf(character.MailIDRechargeCouponExtra),
		"MailIDRiteRecycle":                         reflect.ValueOf(character.MailIDRiteRecycle),
		"MailIDSADivisionAward":                     reflect.ValueOf(character.MailIDSADivisionAward),
		"MailIDSADivisionAwardCompensate":           reflect.ValueOf(character.MailIDSADivisionAwardCompensate),
		"MailIDSARankAward":                         reflect.ValueOf(character.MailIDSARankAward),
		"MailIDSeasonComplianceStageID1RankReward":  reflect.ValueOf(character.MailIDSeasonComplianceStageID1RankReward),
		"MailIDSeasonComplianceStageID1ScoreReward": reflect.ValueOf(character.MailIDSeasonComplianceStageID1ScoreReward),
		"MailIDSeasonComplianceStageID2RankReward":  reflect.ValueOf(character.MailIDSeasonComplianceStageID2RankReward),
		"MailIDSeasonComplianceStageID2ScoreReward": reflect.ValueOf(character.MailIDSeasonComplianceStageID2ScoreReward),
		"MailIDSeasonComplianceStageID3RankReward":  reflect.ValueOf(character.MailIDSeasonComplianceStageID3RankReward),
		"MailIDSeasonComplianceStageID3ScoreReward": reflect.ValueOf(character.MailIDSeasonComplianceStageID3ScoreReward),
		"MailIDSeasonComplianceStageID4RankReward":  reflect.ValueOf(character.MailIDSeasonComplianceStageID4RankReward),
		"MailIDSeasonComplianceStageID4ScoreReward": reflect.ValueOf(character.MailIDSeasonComplianceStageID4ScoreReward),
		"MailIDSeasonComplianceTotalRankReward":     reflect.ValueOf(character.MailIDSeasonComplianceTotalRankReward),
		"MailIDSeasonDivisionAward":                 reflect.ValueOf(character.MailIDSeasonDivisionAward),
		"MailIDSeasonDivisionRank":                  reflect.ValueOf(character.MailIDSeasonDivisionRank),
		"MailIDSeasonDoorRest":                      reflect.ValueOf(character.MailIDSeasonDoorRest),
		"MailIDSeasonJewelryRecycle":                reflect.ValueOf(character.MailIDSeasonJewelryRecycle),
		"MailIDSeasonLinkRecycle":                   reflect.ValueOf(character.MailIDSeasonLinkRecycle),
		"MailIDSeasonTopDivision":                   reflect.ValueOf(character.MailIDSeasonTopDivision),
		"MailIDSociety":                             reflect.ValueOf(character.MailIDSociety),
		"MailIDTalentTreeRecycle":                   reflect.ValueOf(character.MailIDTalentTreeRecycle),
		"MailIDTowerCompliance":                     reflect.ValueOf(character.MailIDTowerCompliance),
		"MailIDTowerSeason":                         reflect.ValueOf(character.MailIDTowerSeason),
		"MailIDTowerSeasonRank":                     reflect.ValueOf(character.MailIDTowerSeasonRank),
		"MailIDWebRechargeCoupon":                   reflect.ValueOf(character.MailIDWebRechargeCoupon),
		"MailIDWeeklyDivisionAward":                 reflect.ValueOf(character.MailIDWeeklyDivisionAward),
		"MailIDWorldBossPartitionRank":              reflect.ValueOf(character.MailIDWorldBossPartitionRank),
		"MailIDWorldBossRoomRank":                   reflect.ValueOf(character.MailIDWorldBossRoomRank),
		"MailIDWrestleDaily":                        reflect.ValueOf(character.MailIDWrestleDaily),
		"MailIDWrestleLevelAwardsReissue":           reflect.ValueOf(character.MailIDWrestleLevelAwardsReissue),
		"MailIDWrestleSeason":                       reflect.ValueOf(character.MailIDWrestleSeason),
		"MailIDWrestleSeasonRank":                   reflect.ValueOf(character.MailIDWrestleSeasonRank),
		"MaxBodyPoolNum":                            reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"MaxCharactUtf8Len":                         reflect.ValueOf(character.MaxCharactUtf8Len),
		"MaxClientDataLen":                          reflect.ValueOf(constant.MakeFromLiteral("1024", token.INT, 0)),
		"MaxFragmentCompose":                        reflect.ValueOf(constant.MakeFromLiteral("99", token.INT, 0)),
		"MaxItemUseCount":                           reflect.ValueOf(constant.MakeFromLiteral("999", token.INT, 0)),
		"MaxShopBuySize":                            reflect.ValueOf(constant.MakeFromLiteral("99", token.INT, 0)),
		"MaxSkillFormat":                            reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"MazeLockTm":                                reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"MergeResources":                            reflect.ValueOf(character.MergeResources),
		"MirageComplianceEndCheck":                  reflect.ValueOf(character.MirageComplianceEndCheck),
		"MirageComplianceOpenCheck":                 reflect.ValueOf(character.MirageComplianceOpenCheck),
		"MirageComplianceShowCheck":                 reflect.ValueOf(character.MirageComplianceShowCheck),
		"MirageDefaultMinPassRate":                  reflect.ValueOf(character.MirageDefaultMinPassRate),
		"MiragePlayerCount":                         reflect.ValueOf(character.MiragePlayerCount),
		"ModuleComplianceCheck":                     reflect.ValueOf(&character.ModuleComplianceCheck).Elem(),
		"MonthlyCardMail":                           reflect.ValueOf(character.MonthlyCardMail),
		"NeedSendReport":                            reflect.ValueOf(character.NeedSendReport),
		"NewActivityCoupon":                         reflect.ValueOf(character.NewActivityCoupon),
		"NewChatGroupTag":                           reflect.ValueOf(character.NewChatGroupTag),
		"NewChatGroupTagAllWorld":                   reflect.ValueOf(character.NewChatGroupTagAllWorld),
		"NewChatUser":                               reflect.ValueOf(character.NewChatUser),
		"NewDbUserFromDbBattleUser":                 reflect.ValueOf(character.NewDbUserFromDbBattleUser),
		"NewEmblem":                                 reflect.ValueOf(character.NewEmblem),
		"NewEmblemWithHero":                         reflect.ValueOf(character.NewEmblemWithHero),
		"NewEquipM":                                 reflect.ValueOf(character.NewEquipM),
		"NewFormationM":                             reflect.ValueOf(character.NewFormationM),
		"NewHeroInit":                               reflect.ValueOf(character.NewHeroInit),
		"NewMaybeOfflineUserLog":                    reflect.ValueOf(character.NewMaybeOfflineUserLog),
		"NewMiniUserSnapshot":                       reflect.ValueOf(character.NewMiniUserSnapshot),
		"NewMsg":                                    reflect.ValueOf(character.NewMsg),
		"NewSeasonJewelry":                          reflect.ValueOf(character.NewSeasonJewelry),
		"NewSeasonJewelryM":                         reflect.ValueOf(character.NewSeasonJewelryM),
		"NewSelectSummon":                           reflect.ValueOf(character.NewSelectSummon),
		"NewSkinM":                                  reflect.ValueOf(character.NewSkinM),
		"NewTitleM":                                 reflect.ValueOf(character.NewTitleM),
		"NewUBDOnlyPowerFromUBD":                    reflect.ValueOf(character.NewUBDOnlyPowerFromUBD),
		"NewUBDOnlyPowerFromUser":                   reflect.ValueOf(character.NewUBDOnlyPowerFromUser),
		"NewUser":                                   reflect.ValueOf(character.NewUser),
		"NewUserManager":                            reflect.ValueOf(character.NewUserManager),
		"NewUserSnapshot":                           reflect.ValueOf(character.NewUserSnapshot),
		"NoNeedSendReport":                          reflect.ValueOf(character.NoNeedSendReport),
		"NormalMail":                                reflect.ValueOf(character.NormalMail),
		"NpcAddBusinessByGroup":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NpcAddBusinessByID":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NpcCharacterDailyDecAffinity":              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NpcCharacterInitAffinity":                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NpcCharacterPresentAddAffinity":            reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"NpcCharacterPresentDecAffinity":            reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"NpcCharacterPresentXAddAffinity":           reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"NpcCharacterPresentXDecAffinity":           reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"NpcCharacterTalkAddAffinity":               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"NpcCharacterTalkDecAffinity":               reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"NpcCharacterWedAddProp":                    reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"NpcCharacterWedDecProp":                    reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"NpcDailyTalkNum":                           reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"NpcRoomMaxTeamEffectCnt":                   reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"NpcRoomMaxTeamID":                          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"NpcTaskBattle":                             reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NpcTaskCost":                               reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NpcTaskLockAffinity":                       reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"NpcTaskLockEvil":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NpcTaskLockGoodness":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NpcTaskLockGoodnessOrEvil":                 reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"NpcTaskLockWed":                            reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"NpcWedAffinity":                            reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"OIActivityStory":                           reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"OIBattle":                                  reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"OIChatFrequencyTime":                       reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"OIChatGuild":                               reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"OIChatPrivate":                             reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"OIChatWorld":                               reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"OIClientInfo":                              reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"OIDuel":                                    reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"OIDungeon":                                 reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"OIFlowerOccupyAssist":                      reflect.ValueOf(constant.MakeFromLiteral("24", token.INT, 0)),
		"OIFlowerbedRecommend":                      reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"OIMax":                                     reflect.ValueOf(constant.MakeFromLiteral("28", token.INT, 0)),
		"OIMirage":                                  reflect.ValueOf(constant.MakeFromLiteral("17", token.INT, 0)),
		"OIRecommendFriend":                         reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"OIRevive":                                  reflect.ValueOf(constant.MakeFromLiteral("13", token.INT, 0)),
		"OISeasonArenaFight":                        reflect.ValueOf(constant.MakeFromLiteral("23", token.INT, 0)),
		"OISeasonArenaRefresh":                      reflect.ValueOf(constant.MakeFromLiteral("22", token.INT, 0)),
		"OISeasonDoor":                              reflect.ValueOf(constant.MakeFromLiteral("26", token.INT, 0)),
		"OISeasonDungeon":                           reflect.ValueOf(constant.MakeFromLiteral("21", token.INT, 0)),
		"OISeasonJewelryWear":                       reflect.ValueOf(constant.MakeFromLiteral("27", token.INT, 0)),
		"OITower":                                   reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"OITowerSeason":                             reflect.ValueOf(constant.MakeFromLiteral("18", token.INT, 0)),
		"OITowerstar":                               reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"OITrial":                                   reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"OIViewFormation":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"OIViewUser":                                reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"OIViewUserDefPower":                        reflect.ValueOf(constant.MakeFromLiteral("25", token.INT, 0)),
		"OIWorldBoss":                               reflect.ValueOf(constant.MakeFromLiteral("19", token.INT, 0)),
		"OIWrestle":                                 reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"OnhookMaxTime":                             reflect.ValueOf(character.OnhookMaxTime),
		"OnhookSpeedTime":                           reflect.ValueOf(character.OnhookSpeedTime),
		"OpMax":                                     reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"OpResourceAdd":                             reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"OpResourceDec":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"OpWorldChat":                               reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"OperateIntervals":                          reflect.ValueOf(&character.OperateIntervals).Elem(),
		"OptimizeAwards":                            reflect.ValueOf(character.OptimizeAwards),
		"OptimizeCosts":                             reflect.ValueOf(character.OptimizeCosts),
		"Params":                                    reflect.ValueOf(character.Params),
		"PassActiveMail":                            reflect.ValueOf(character.PassActiveMail),
		"PassiveSkillHoles":                         reflect.ValueOf(&character.PassiveSkillHoles).Elem(),
		"PassiveSkillTargetAll":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PassiveSkillTargetCareerHun":               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PassiveSkillTargetCareerShu":               reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"PassiveSkillTargetCareerTi":                reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"PassiveSkillTargetSelf":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PeakFight":                                 reflect.ValueOf(character.PeakFight),
		"PeakMail":                                  reflect.ValueOf(character.PeakMail),
		"PetUpStarCostID":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PetUpStarCostQuality":                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PluralSummonDiamondCost":                   reflect.ValueOf(character.PluralSummonDiamondCost),
		"PluralSummonItemCost":                      reflect.ValueOf(character.PluralSummonItemCost),
		"PowerUpdateByNormalRaise":                  reflect.ValueOf(character.PowerUpdateByNormalRaise),
		"PowerUpdateBySeasonRaise":                  reflect.ValueOf(character.PowerUpdateBySeasonRaise),
		"PracticeAddPercentEnergy":                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PracticeAddPercentExp":                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PracticeAddPercentExpAndEnergy":            reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PracticeAdditionEnergy":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PracticeAdditionExp":                       reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"PracticeAdditionMax":                       reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PracticeBoundMax":                          reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"PracticeBoundMaxEnergy":                    reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PracticeBoundMaxExp":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PracticeBoundMinEnergy":                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PracticeBoundMinExp":                       reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"PracticeBuffAdd":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PracticeBuffDec":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PracticeTickInterval":                      reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"PracticeTickOfflineMaxNum":                 reflect.ValueOf(constant.MakeFromLiteral("5760", token.INT, 0)),
		"PracticeTickResetNum":                      reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"PrevSeasonRiteIdBase":                      reflect.ValueOf(constant.MakeFromLiteral("100000000", token.INT, 0)),
		"ProcessOrderRetFailure":                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ProcessOrderRetSuccess":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"QuestionnaireMail":                         reflect.ValueOf(character.QuestionnaireMail),
		"RaisePSTypeEmblemAffix":                    reflect.ValueOf(character.RaisePSTypeEmblemAffix),
		"RaisePSTypeEmblemHero":                     reflect.ValueOf(character.RaisePSTypeEmblemHero),
		"RaisePSTypeEmblemLink":                     reflect.ValueOf(character.RaisePSTypeEmblemLink),
		"RaisePSTypeEmblemSelfAndSuit":              reflect.ValueOf(character.RaisePSTypeEmblemSelfAndSuit),
		"RaisePSTypeEquipEnchant":                   reflect.ValueOf(character.RaisePSTypeEquipEnchant),
		"RaisePSTypeHeroAwaken":                     reflect.ValueOf(character.RaisePSTypeHeroAwaken),
		"RaisePSTypeHeroStage":                      reflect.ValueOf(character.RaisePSTypeHeroStage),
		"RaisePSTypeHeroStar":                       reflect.ValueOf(character.RaisePSTypeHeroStar),
		"RaisePSTypeHeroTalent":                     reflect.ValueOf(character.RaisePSTypeHeroTalent),
		"RaisePSTypeHeroTalentLink":                 reflect.ValueOf(character.RaisePSTypeHeroTalentLink),
		"RaisePSTypeSeasonLink":                     reflect.ValueOf(character.RaisePSTypeSeasonLink),
		"RaisePSTypeSkin":                           reflect.ValueOf(character.RaisePSTypeSkin),
		"RateContentLength":                         reflect.ValueOf(constant.MakeFromLiteral("140", token.INT, 0)),
		"RateMaxScore":                              reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"RebaseMail":                                reflect.ValueOf(character.RebaseMail),
		"RecommendedFriendInterval":                 reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"RecommendedFriendResetTime":                reflect.ValueOf(constant.MakeFromLiteral("60", token.INT, 0)),
		"RecoverTypeEquipMelting":                   reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"RecoverTypeEquipRebirth":                   reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"RecoverTypeHerbsRecycle":                   reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"RecoverTypePetDestroy":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"RecoverTypePetFragmentDestory":             reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"RecoverTypePetReborn":                      reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"RecoverTypePillRecycle":                    reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"RecoverTypeThroneRecycle":                  reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"RecvAwardFromSys":                          reflect.ValueOf(character.RecvAwardFromSys),
		"RecvAwardFromUser":                         reflect.ValueOf(character.RecvAwardFromUser),
		"RedPointFlushNumLimit":                     reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"RedPointID2FuncID":                         reflect.ValueOf(&character.RedPointID2FuncID).Elem(),
		"RedPointID2RankID":                         reflect.ValueOf(&character.RedPointID2RankID).Elem(),
		"RemainSeasonRestMail":                      reflect.ValueOf(character.RemainSeasonRestMail),
		"RemainWithFormationID":                     reflect.ValueOf(&character.RemainWithFormationID).Elem(),
		"RepairBattleData":                          reflect.ValueOf(character.RepairBattleData),
		"ReportVersion":                             reflect.ValueOf(character.ReportVersion),
		"RequireHeroCountForContract":               reflect.ValueOf(character.RequireHeroCountForContract),
		"ResetLinkSummonGuarantee":                  reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"RiteRecycleMail":                           reflect.ValueOf(character.RiteRecycleMail),
		"RivalBattleSnapshot":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"RivalFormation":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"RobotType360":                              reflect.ValueOf(character.RobotType360),
		"RobotTypeFourteen":                         reflect.ValueOf(character.RobotTypeFourteen),
		"RobotTypeNinety":                           reflect.ValueOf(character.RobotTypeNinety),
		"RobotTypeOne":                              reflect.ValueOf(character.RobotTypeOne),
		"RobotTypeSeventh":                          reflect.ValueOf(character.RobotTypeSeventh),
		"RobotTypeThird":                            reflect.ValueOf(character.RobotTypeThird),
		"RobotTypeThiry":                            reflect.ValueOf(character.RobotTypeThiry),
		"RoundActivityMail":                         reflect.ValueOf(character.RoundActivityMail),
		"RoundActivityRecvAwardCountLimit":          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"RoundActivityRecvAwardFromSys":             reflect.ValueOf(character.RoundActivityRecvAwardFromSys),
		"RoundActivityRecvAwardFromUser":            reflect.ValueOf(character.RoundActivityRecvAwardFromUser),
		"SeasonArenaDivisionCompensateMail":         reflect.ValueOf(character.SeasonArenaDivisionCompensateMail),
		"SeasonArenaDivisionMail":                   reflect.ValueOf(character.SeasonArenaDivisionMail),
		"SeasonArenaRankMail":                       reflect.ValueOf(character.SeasonArenaRankMail),
		"SeasonComplianceMail":                      reflect.ValueOf(character.SeasonComplianceMail),
		"SeasonComplianceScoreMail":                 reflect.ValueOf(character.SeasonComplianceScoreMail),
		"SeasonDoorTypeEquip":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"SeasonDoorTypeMat":                         reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"SeasonJewelryReRollSkills":                 reflect.ValueOf(character.SeasonJewelryReRollSkills),
		"SeasonJewelryRecycleMail":                  reflect.ValueOf(character.SeasonJewelryRecycleMail),
		"SeasonLinkRecycleMail":                     reflect.ValueOf(character.SeasonLinkRecycleMail),
		"SeasonMapBuffBuyPrice":                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"SeasonMapBuffBuyStamina":                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"SeasonMapBuffFightDamage":                  reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"SeasonMapTradeTimeLimit":                   reflect.ValueOf(character.SeasonMapTradeTimeLimit),
		"SendTopResource":                           reflect.ValueOf(character.SendTopResource),
		"Sender":                                    reflect.ValueOf(character.Sender),
		"SensitiveGuildDeclaration":                 reflect.ValueOf(constant.MakeFromLiteral("\"14\"", token.STRING, 0)),
		"SensitiveGuildName":                        reflect.ValueOf(constant.MakeFromLiteral("\"12\"", token.STRING, 0)),
		"SensitiveUserName":                         reflect.ValueOf(constant.MakeFromLiteral("\"13\"", token.STRING, 0)),
		"ShopBuySizeDailyReset":                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ShopBuySizeFix":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"ShopRefreshTypeFree":                       reflect.ValueOf(character.ShopRefreshTypeFree),
		"ShopRefreshTypeNone":                       reflect.ValueOf(character.ShopRefreshTypeNone),
		"ShopRefreshTypePaid":                       reflect.ValueOf(character.ShopRefreshTypePaid),
		"SingleSummonDiamondCost":                   reflect.ValueOf(character.SingleSummonDiamondCost),
		"SingleSummonFreeCost":                      reflect.ValueOf(character.SingleSummonFreeCost),
		"SingleSummonItemCost":                      reflect.ValueOf(character.SingleSummonItemCost),
		"SkillBookTypeActive":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"SkillBookTypePassive":                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"SkillHolesPos":                             reflect.ValueOf(&character.SkillHolesPos).Elem(),
		"SkillNumLegend":                            reflect.ValueOf(character.SkillNumLegend),
		"SkillNumOthers":                            reflect.ValueOf(character.SkillNumOthers),
		"SocietyMail":                               reflect.ValueOf(character.SocietyMail),
		"SpecialCheck":                              reflect.ValueOf(character.SpecialCheck),
		"StarLimitDiffNum":                          reflect.ValueOf(&character.StarLimitDiffNum).Elem(),
		"StarLimitMaxStarLv":                        reflect.ValueOf(&character.StarLimitMaxStarLv).Elem(),
		"StarLimitStarLvRequireCount":               reflect.ValueOf(&character.StarLimitStarLvRequireCount).Elem(),
		"StateCache":                                reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"StateCreate":                               reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"StateException":                            reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"StateLogin":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"StateNone":                                 reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"StateOffline":                              reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"StateOnline":                               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"StoryTypeActivity":                         reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"StoryTypeSeason":                           reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"SummonArtifactFragmentGuarantee":           reflect.ValueOf(character.SummonArtifactFragmentGuarantee),
		"SummonChangeGuarantee":                     reflect.ValueOf(character.SummonChangeGuarantee),
		"SummonColorfulGuarantee":                   reflect.ValueOf(character.SummonColorfulGuarantee),
		"SummonFixedGuarantee":                      reflect.ValueOf(character.SummonFixedGuarantee),
		"SummonNormal":                              reflect.ValueOf(character.SummonNormal),
		"SummonRoundActivityGuarantee":              reflect.ValueOf(character.SummonRoundActivityGuarantee),
		"SummonWishGuarantee":                       reflect.ValueOf(character.SummonWishGuarantee),
		"SyncUserAndGroup":                          reflect.ValueOf(character.SyncUserAndGroup),
		"TalentTreeRecycle":                         reflect.ValueOf(character.TalentTreeRecycle),
		"TaskProgressOnEvent":                       reflect.ValueOf(character.TaskProgressOnEvent),
		"TestAiToolsArtifactM":                      reflect.ValueOf(&character.TestAiToolsArtifactM).Elem(),
		"TestAiToolsHeroM":                          reflect.ValueOf(&character.TestAiToolsHeroM).Elem(),
		"TestSweep":                                 reflect.ValueOf(character.TestSweep),
		"Top5HeroNum":                               reflect.ValueOf(character.Top5HeroNum),
		"TowerBeginID":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TowerComplianceEndCheck":                   reflect.ValueOf(character.TowerComplianceEndCheck),
		"TowerComplianceOpenCheck":                  reflect.ValueOf(character.TowerComplianceOpenCheck),
		"TowerComplianceShowCheck":                  reflect.ValueOf(character.TowerComplianceShowCheck),
		"TowerSeasonMail":                           reflect.ValueOf(character.TowerSeasonMail),
		"TowerSeasonRankMail":                       reflect.ValueOf(character.TowerSeasonRankMail),
		"TowerstarCheckStarFuncs":                   reflect.ValueOf(&character.TowerstarCheckStarFuncs).Elem(),
		"TowerstarFullStar":                         reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"TrialInitialLevel":                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TrialOnHookDuration":                       reflect.ValueOf(constant.MakeFromLiteral("3600", token.INT, 0)),
		"TrialOneStar":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TrialTargetFive":                           reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"TrialTargetFour":                           reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"TrialTargetNone":                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"TrialTargetOne":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TrialTargetSix":                            reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"TrialTargetThree":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TrialTargetTwo":                            reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TrialTaskType":                             reflect.ValueOf(constant.MakeFromLiteral("1035", token.INT, 0)),
		"TrialThreeStar":                            reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TrialTwoStar":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"Type":                                      reflect.ValueOf(character.Type),
		"UserCreateBaseID":                          reflect.ValueOf(character.UserCreateBaseID),
		"UserGroup":                                 reflect.ValueOf(constant.MakeFromLiteral("32", token.INT, 0)),
		"UserInitFromBattleUser":                    reflect.ValueOf(character.UserInitFromBattleUser),
		"UserInitFromRealUser":                      reflect.ValueOf(character.UserInitFromRealUser),
		"UserNameMaxLength":                         reflect.ValueOf(constant.MakeFromLiteral("35", token.INT, 0)),
		"UserSnapshotReqMax":                        reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"WatchFeedEvent":                            reflect.ValueOf(character.WatchFeedEvent),
		"WatchPuzzleEvent":                          reflect.ValueOf(character.WatchPuzzleEvent),
		"WatchTurnTableEvent":                       reflect.ValueOf(character.WatchTurnTableEvent),
		"WebLargeRechargeGmMailType":                reflect.ValueOf(character.WebLargeRechargeGmMailType),
		"WebRechargeCouponActivityWebGiftId":        reflect.ValueOf(constant.MakeFromLiteral("10007", token.INT, 0)),
		"WebRechargeCouponCustomRechargeNumMax":     reflect.ValueOf(constant.MakeFromLiteral("100000", token.INT, 0)),
		"WebRechargeCouponCustomRechargeNumMin":     reflect.ValueOf(constant.MakeFromLiteral("10000", token.INT, 0)),
		"WebRechargeCouponInternalId":               reflect.ValueOf(constant.MakeFromLiteral("20000", token.INT, 0)),
		"WebRechargeCouponSdkPid":                   reflect.ValueOf(character.WebRechargeCouponSdkPid),
		"WebRechargeOpIdTW":                         reflect.ValueOf(constant.MakeFromLiteral("18", token.INT, 0)),
		"WishSlotLeft":                              reflect.ValueOf(character.WishSlotLeft),
		"WishSlotRight":                             reflect.ValueOf(character.WishSlotRight),
		"WmOsAndroid":                               reflect.ValueOf(constant.MakeFromLiteral("\"2\"", token.STRING, 0)),
		"WmOsOfficialIos":                           reflect.ValueOf(constant.MakeFromLiteral("\"3\"", token.STRING, 0)),
		"WorldBeginChapter":                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"WorldBeginChapterCol":                      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"WorldBeginChapterRow":                      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"WorldBossSettleMail":                       reflect.ValueOf(character.WorldBossSettleMail),
		"WorldDisturbMonsterBuffs":                  reflect.ValueOf(&character.WorldDisturbMonsterBuffs).Elem(),
		"WorldDisturbMonsterPriceGroup":             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"WorldMessageLoadTimeout":                   reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"WorldMessageMaxLength":                     reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"WorldMessageMaxNum":                        reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"WrestleMail":                               reflect.ValueOf(character.WrestleMail),
		"WriteTraceLog":                             reflect.ValueOf(character.WriteTraceLog),
		"WrongUserLimitTime":                        reflect.ValueOf(character.WrongUserLimitTime),
		"WudaoResetGold":                            reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"XmlDataCheck":                              reflect.ValueOf(character.XmlDataCheck),

		// type definitions
		"Achieve":                            reflect.ValueOf((*character.Achieve)(nil)),
		"AchievementsShowcase":               reflect.ValueOf((*character.AchievementsShowcase)(nil)),
		"ActivityCompliance":                 reflect.ValueOf((*character.ActivityCompliance)(nil)),
		"ActivityCoupon":                     reflect.ValueOf((*character.ActivityCoupon)(nil)),
		"ActivityLifeLongGifts":              reflect.ValueOf((*character.ActivityLifeLongGifts)(nil)),
		"ActivityRecharge":                   reflect.ValueOf((*character.ActivityRecharge)(nil)),
		"ActivityReturn":                     reflect.ValueOf((*character.ActivityReturn)(nil)),
		"ActivityStory":                      reflect.ValueOf((*character.ActivityStory)(nil)),
		"ActivitySubContinueLoginS":          reflect.ValueOf((*character.ActivitySubContinueLoginS)(nil)),
		"ActivitySubControl":                 reflect.ValueOf((*character.ActivitySubControl)(nil)),
		"ActivitySubDropS":                   reflect.ValueOf((*character.ActivitySubDropS)(nil)),
		"ActivitySubExchangeS":               reflect.ValueOf((*character.ActivitySubExchangeS)(nil)),
		"ActivitySubFeedS":                   reflect.ValueOf((*character.ActivitySubFeedS)(nil)),
		"ActivitySubPuzzleS":                 reflect.ValueOf((*character.ActivitySubPuzzleS)(nil)),
		"ActivitySubShootS":                  reflect.ValueOf((*character.ActivitySubShootS)(nil)),
		"ActivitySubTaskS":                   reflect.ValueOf((*character.ActivitySubTaskS)(nil)),
		"ActivitySubTurnTableS":              reflect.ValueOf((*character.ActivitySubTurnTableS)(nil)),
		"ActivitySum":                        reflect.ValueOf((*character.ActivitySum)(nil)),
		"ActivitySumManager":                 reflect.ValueOf((*character.ActivitySumManager)(nil)),
		"ActivityTurnTable":                  reflect.ValueOf((*character.ActivityTurnTable)(nil)),
		"ActivityWeb":                        reflect.ValueOf((*character.ActivityWeb)(nil)),
		"AiToolsDevelop":                     reflect.ValueOf((*character.AiToolsDevelop)(nil)),
		"AiToolsDevelopManager":              reflect.ValueOf((*character.AiToolsDevelopManager)(nil)),
		"AiToolsDevelops":                    reflect.ValueOf((*character.AiToolsDevelops)(nil)),
		"AiToolsHeroExclusive":               reflect.ValueOf((*character.AiToolsHeroExclusive)(nil)),
		"AiToolsHeroExclusiveManager":        reflect.ValueOf((*character.AiToolsHeroExclusiveManager)(nil)),
		"AiToolsHeroExclusives":              reflect.ValueOf((*character.AiToolsHeroExclusives)(nil)),
		"AiToolsHeroGrade":                   reflect.ValueOf((*character.AiToolsHeroGrade)(nil)),
		"AiToolsHeroGradeManager":            reflect.ValueOf((*character.AiToolsHeroGradeManager)(nil)),
		"AiToolsHeroGrades":                  reflect.ValueOf((*character.AiToolsHeroGrades)(nil)),
		"AiToolsLineup":                      reflect.ValueOf((*character.AiToolsLineup)(nil)),
		"AiToolsLineupManager":               reflect.ValueOf((*character.AiToolsLineupManager)(nil)),
		"AiToolsLineups":                     reflect.ValueOf((*character.AiToolsLineups)(nil)),
		"AiToolsOverallSituation":            reflect.ValueOf((*character.AiToolsOverallSituation)(nil)),
		"AiToolsOverallSituationManager":     reflect.ValueOf((*character.AiToolsOverallSituationManager)(nil)),
		"AiToolsOverallSituations":           reflect.ValueOf((*character.AiToolsOverallSituations)(nil)),
		"ArenaOpponent":                      reflect.ValueOf((*character.ArenaOpponent)(nil)),
		"Args":                               reflect.ValueOf((*character.Args)(nil)),
		"Artifact":                           reflect.ValueOf((*character.Artifact)(nil)),
		"ArtifactDebutActHandler":            reflect.ValueOf((*character.ArtifactDebutActHandler)(nil)),
		"ArtifactDebutDrawRet":               reflect.ValueOf((*character.ArtifactDebutDrawRet)(nil)),
		"ArtifactDebutLoginAct":              reflect.ValueOf((*character.ArtifactDebutLoginAct)(nil)),
		"ArtifactDebutManager":               reflect.ValueOf((*character.ArtifactDebutManager)(nil)),
		"ArtifactDebutPuzzleAct":             reflect.ValueOf((*character.ArtifactDebutPuzzleAct)(nil)),
		"ArtifactDebutSummonAct":             reflect.ValueOf((*character.ArtifactDebutSummonAct)(nil)),
		"ArtifactM":                          reflect.ValueOf((*character.ArtifactM)(nil)),
		"ArtifactOwnershipAdvancedMazeToken": reflect.ValueOf((*character.ArtifactOwnershipAdvancedMazeToken)(nil)),
		"AssistanceActivity":                 reflect.ValueOf((*character.AssistanceActivity)(nil)),
		"AsyncLoadMessage":                   reflect.ValueOf((*character.AsyncLoadMessage)(nil)),
		"Avatar":                             reflect.ValueOf((*character.Avatar)(nil)),
		"AwardInfo":                          reflect.ValueOf((*character.AwardInfo)(nil)),
		"AwardInfos":                         reflect.ValueOf((*character.AwardInfos)(nil)),
		"BaseHandbookM":                      reflect.ValueOf((*character.BaseHandbookM)(nil)),
		"BattleDataCacheKey":                 reflect.ValueOf((*character.BattleDataCacheKey)(nil)),
		"BattleWinType":                      reflect.ValueOf((*character.BattleWinType)(nil)),
		"BossRushBattleResult":               reflect.ValueOf((*character.BossRushBattleResult)(nil)),
		"BossRushM":                          reflect.ValueOf((*character.BossRushM)(nil)),
		"Carnival":                           reflect.ValueOf((*character.Carnival)(nil)),
		"ChatGroupOp":                        reflect.ValueOf((*character.ChatGroupOp)(nil)),
		"ChatGroupTag":                       reflect.ValueOf((*character.ChatGroupTag)(nil)),
		"ClientInfo":                         reflect.ValueOf((*character.ClientInfo)(nil)),
		"CognitionManager":                   reflect.ValueOf((*character.CognitionManager)(nil)),
		"CommonNumberType":                   reflect.ValueOf((*character.CommonNumberType)(nil)),
		"ComplianceCheck":                    reflect.ValueOf((*character.ComplianceCheck)(nil)),
		"ComplianceTask":                     reflect.ValueOf((*character.ComplianceTask)(nil)),
		"ComplianceTasks":                    reflect.ValueOf((*character.ComplianceTasks)(nil)),
		"Crystal":                            reflect.ValueOf((*character.Crystal)(nil)),
		"DailyAttendance":                    reflect.ValueOf((*character.DailyAttendance)(nil)),
		"DailyAttendanceHero":                reflect.ValueOf((*character.DailyAttendanceHero)(nil)),
		"DailySpecial":                       reflect.ValueOf((*character.DailySpecial)(nil)),
		"DailyTask":                          reflect.ValueOf((*character.DailyTask)(nil)),
		"DailyWish":                          reflect.ValueOf((*character.DailyWish)(nil)),
		"DailyWishManager":                   reflect.ValueOf((*character.DailyWishManager)(nil)),
		"DisorderAwardSrv":                   reflect.ValueOf((*character.DisorderAwardSrv)(nil)),
		"DisorderBattleBattle":               reflect.ValueOf((*character.DisorderBattleBattle)(nil)),
		"DisorderBattleSrv":                  reflect.ValueOf((*character.DisorderBattleSrv)(nil)),
		"DisorderBattleSweep":                reflect.ValueOf((*character.DisorderBattleSweep)(nil)),
		"DisorderFightAward":                 reflect.ValueOf((*character.DisorderFightAward)(nil)),
		"DisorderLand":                       reflect.ValueOf((*character.DisorderLand)(nil)),
		"DisorderLandMap":                    reflect.ValueOf((*character.DisorderLandMap)(nil)),
		"DisorderLandNode":                   reflect.ValueOf((*character.DisorderLandNode)(nil)),
		"DisorderLandTopLevel":               reflect.ValueOf((*character.DisorderLandTopLevel)(nil)),
		"DisorderSweepAward":                 reflect.ValueOf((*character.DisorderSweepAward)(nil)),
		"Dispatch":                           reflect.ValueOf((*character.Dispatch)(nil)),
		"DivineDemon":                        reflect.ValueOf((*character.DivineDemon)(nil)),
		"DivineDemonActiveTask":              reflect.ValueOf((*character.DivineDemonActiveTask)(nil)),
		"DivineDemonCost":                    reflect.ValueOf((*character.DivineDemonCost)(nil)),
		"DivineDemonStarTask":                reflect.ValueOf((*character.DivineDemonStarTask)(nil)),
		"DivineDemonSummonTask":              reflect.ValueOf((*character.DivineDemonSummonTask)(nil)),
		"DivineDemonTask":                    reflect.ValueOf((*character.DivineDemonTask)(nil)),
		"DivineDemonWishRecord":              reflect.ValueOf((*character.DivineDemonWishRecord)(nil)),
		"Drop":                               reflect.ValueOf((*character.Drop)(nil)),
		"DropActivityManager":                reflect.ValueOf((*character.DropActivityManager)(nil)),
		"Duel":                               reflect.ValueOf((*character.Duel)(nil)),
		"Dungeon":                            reflect.ValueOf((*character.Dungeon)(nil)),
		"Emblem":                             reflect.ValueOf((*character.Emblem)(nil)),
		"EmblemM":                            reflect.ValueOf((*character.EmblemM)(nil)),
		"Equip":                              reflect.ValueOf((*character.Equip)(nil)),
		"EquipM":                             reflect.ValueOf((*character.EquipM)(nil)),
		"EventExecutor":                      reflect.ValueOf((*character.EventExecutor)(nil)),
		"EventHandler":                       reflect.ValueOf((*character.EventHandler)(nil)),
		"Extra":                              reflect.ValueOf((*character.Extra)(nil)),
		"Flower":                             reflect.ValueOf((*character.Flower)(nil)),
		"Forecast":                           reflect.ValueOf((*character.Forecast)(nil)),
		"FormationLinkInfo":                  reflect.ValueOf((*character.FormationLinkInfo)(nil)),
		"FormationM":                         reflect.ValueOf((*character.FormationM)(nil)),
		"GST":                                reflect.ValueOf((*character.GST)(nil)),
		"Gem":                                reflect.ValueOf((*character.Gem)(nil)),
		"GemM":                               reflect.ValueOf((*character.GemM)(nil)),
		"GodPresent":                         reflect.ValueOf((*character.GodPresent)(nil)),
		"GodPresentCheckOpen":                reflect.ValueOf((*character.GodPresentCheckOpen)(nil)),
		"GodPresentManager":                  reflect.ValueOf((*character.GodPresentManager)(nil)),
		"GoddessContractManager":             reflect.ValueOf((*character.GoddessContractManager)(nil)),
		"GoldBuy":                            reflect.ValueOf((*character.GoldBuy)(nil)),
		"GoodsGuildLevelLimit":               reflect.ValueOf((*character.GoodsGuildLevelLimit)(nil)),
		"Guidance":                           reflect.ValueOf((*character.Guidance)(nil)),
		"GuildTalent":                        reflect.ValueOf((*character.GuildTalent)(nil)),
		"GuildTalentFirstFloor":              reflect.ValueOf((*character.GuildTalentFirstFloor)(nil)),
		"GuildTalentLevelUp":                 reflect.ValueOf((*character.GuildTalentLevelUp)(nil)),
		"GuildTalentSecondFloor":             reflect.ValueOf((*character.GuildTalentSecondFloor)(nil)),
		"GuildTalentThirdFloor":              reflect.ValueOf((*character.GuildTalentThirdFloor)(nil)),
		"HandbookHandle":                     reflect.ValueOf((*character.HandbookHandle)(nil)),
		"HandbookManger":                     reflect.ValueOf((*character.HandbookManger)(nil)),
		"Hero":                               reflect.ValueOf((*character.Hero)(nil)),
		"HeroHandbookM":                      reflect.ValueOf((*character.HeroHandbookM)(nil)),
		"HeroIDAndPower":                     reflect.ValueOf((*character.HeroIDAndPower)(nil)),
		"HeroM":                              reflect.ValueOf((*character.HeroM)(nil)),
		"HeroModify":                         reflect.ValueOf((*character.HeroModify)(nil)),
		"HeroSlice":                          reflect.ValueOf((*character.HeroSlice)(nil)),
		"HeroStarUpCostsM":                   reflect.ValueOf((*character.HeroStarUpCostsM)(nil)),
		"InitAfterEventParam":                reflect.ValueOf((*character.InitAfterEventParam)(nil)),
		"IsOpen":                             reflect.ValueOf((*character.IsOpen)(nil)),
		"LineTask":                           reflect.ValueOf((*character.LineTask)(nil)),
		"Link":                               reflect.ValueOf((*character.Link)(nil)),
		"LinkSummon":                         reflect.ValueOf((*character.LinkSummon)(nil)),
		"LogComposeCostGem":                  reflect.ValueOf((*character.LogComposeCostGem)(nil)),
		"MaybeOfflineUserLog":                reflect.ValueOf((*character.MaybeOfflineUserLog)(nil)),
		"MazeGrid":                           reflect.ValueOf((*character.MazeGrid)(nil)),
		"MazeM":                              reflect.ValueOf((*character.MazeM)(nil)),
		"Medal":                              reflect.ValueOf((*character.Medal)(nil)),
		"MemGoddess":                         reflect.ValueOf((*character.MemGoddess)(nil)),
		"Memory":                             reflect.ValueOf((*character.Memory)(nil)),
		"Mirage":                             reflect.ValueOf((*character.Mirage)(nil)),
		"MirageCopyTopFloor":                 reflect.ValueOf((*character.MirageCopyTopFloor)(nil)),
		"MirageM":                            reflect.ValueOf((*character.MirageM)(nil)),
		"MirageTopFloor":                     reflect.ValueOf((*character.MirageTopFloor)(nil)),
		"MonthTasks":                         reflect.ValueOf((*character.MonthTasks)(nil)),
		"MonthTasksManager":                  reflect.ValueOf((*character.MonthTasksManager)(nil)),
		"MonthlyCard":                        reflect.ValueOf((*character.MonthlyCard)(nil)),
		"MonthlyTask":                        reflect.ValueOf((*character.MonthlyTask)(nil)),
		"Monument":                           reflect.ValueOf((*character.Monument)(nil)),
		"NewYearActivity":                    reflect.ValueOf((*character.NewYearActivity)(nil)),
		"NumUse":                             reflect.ValueOf((*character.NumUse)(nil)),
		"OfflineUserBattleDataCache":         reflect.ValueOf((*character.OfflineUserBattleDataCache)(nil)),
		"OperateActivity":                    reflect.ValueOf((*character.OperateActivity)(nil)),
		"OperateActivityM":                   reflect.ValueOf((*character.OperateActivityM)(nil)),
		"Option":                             reflect.ValueOf((*character.Option)(nil)),
		"Orders":                             reflect.ValueOf((*character.Orders)(nil)),
		"Pass":                               reflect.ValueOf((*character.Pass)(nil)),
		"PassData":                           reflect.ValueOf((*character.PassData)(nil)),
		"PassI":                              reflect.ValueOf((*character.PassI)(nil)),
		"PluralSummonDiamond":                reflect.ValueOf((*character.PluralSummonDiamond)(nil)),
		"PluralSummonItem":                   reflect.ValueOf((*character.PluralSummonItem)(nil)),
		"PowerUpdateTyp":                     reflect.ValueOf((*character.PowerUpdateTyp)(nil)),
		"PreSeason":                          reflect.ValueOf((*character.PreSeason)(nil)),
		"PushGift":                           reflect.ValueOf((*character.PushGift)(nil)),
		"PyramidLattice":                     reflect.ValueOf((*character.PyramidLattice)(nil)),
		"PyramidM":                           reflect.ValueOf((*character.PyramidM)(nil)),
		"Questionnaires":                     reflect.ValueOf((*character.Questionnaires)(nil)),
		"RaisePSType":                        reflect.ValueOf((*character.RaisePSType)(nil)),
		"RankAchieveAward":                   reflect.ValueOf((*character.RankAchieveAward)(nil)),
		"Rate":                               reflect.ValueOf((*character.Rate)(nil)),
		"Recharge":                           reflect.ValueOf((*character.Recharge)(nil)),
		"Remain":                             reflect.ValueOf((*character.Remain)(nil)),
		"RemainBook":                         reflect.ValueOf((*character.RemainBook)(nil)),
		"RemainM":                            reflect.ValueOf((*character.RemainM)(nil)),
		"ReportLogInfo":                      reflect.ValueOf((*character.ReportLogInfo)(nil)),
		"ReturnAward":                        reflect.ValueOf((*character.ReturnAward)(nil)),
		"Rite":                               reflect.ValueOf((*character.Rite)(nil)),
		"RiteM":                              reflect.ValueOf((*character.RiteM)(nil)),
		"RoundActivityManager":               reflect.ValueOf((*character.RoundActivityManager)(nil)),
		"SeasonArenaTask":                    reflect.ValueOf((*character.SeasonArenaTask)(nil)),
		"SeasonCompliance":                   reflect.ValueOf((*character.SeasonCompliance)(nil)),
		"SeasonComplianceM":                  reflect.ValueOf((*character.SeasonComplianceM)(nil)),
		"SeasonDoor":                         reflect.ValueOf((*character.SeasonDoor)(nil)),
		"SeasonDungeon":                      reflect.ValueOf((*character.SeasonDungeon)(nil)),
		"SeasonJewelry":                      reflect.ValueOf((*character.SeasonJewelry)(nil)),
		"SeasonJewelryM":                     reflect.ValueOf((*character.SeasonJewelryM)(nil)),
		"SeasonJewelryRecycleSortUnit":       reflect.ValueOf((*character.SeasonJewelryRecycleSortUnit)(nil)),
		"SeasonLevel":                        reflect.ValueOf((*character.SeasonLevel)(nil)),
		"SeasonLinkBattleData":               reflect.ValueOf((*character.SeasonLinkBattleData)(nil)),
		"SeasonLinkM":                        reflect.ValueOf((*character.SeasonLinkM)(nil)),
		"SeasonMap":                          reflect.ValueOf((*character.SeasonMap)(nil)),
		"SeasonMapBattleResult":              reflect.ValueOf((*character.SeasonMapBattleResult)(nil)),
		"SeasonReturn":                       reflect.ValueOf((*character.SeasonReturn)(nil)),
		"SeasonShopM":                        reflect.ValueOf((*character.SeasonShopM)(nil)),
		"SelectSummon":                       reflect.ValueOf((*character.SelectSummon)(nil)),
		"SelectSummonLog":                    reflect.ValueOf((*character.SelectSummonLog)(nil)),
		"Servicer":                           reflect.ValueOf((*character.Servicer)(nil)),
		"SevenDayLogin":                      reflect.ValueOf((*character.SevenDayLogin)(nil)),
		"ShareEmblemExt":                     reflect.ValueOf((*character.ShareEmblemExt)(nil)),
		"ShareGrowth":                        reflect.ValueOf((*character.ShareGrowth)(nil)),
		"Shop":                               reflect.ValueOf((*character.Shop)(nil)),
		"ShopBuy":                            reflect.ValueOf((*character.ShopBuy)(nil)),
		"ShopBuyRandom":                      reflect.ValueOf((*character.ShopBuyRandom)(nil)),
		"ShopBuyRound":                       reflect.ValueOf((*character.ShopBuyRound)(nil)),
		"ShopGoodOpen":                       reflect.ValueOf((*character.ShopGoodOpen)(nil)),
		"ShopInit":                           reflect.ValueOf((*character.ShopInit)(nil)),
		"ShopInitRandom":                     reflect.ValueOf((*character.ShopInitRandom)(nil)),
		"ShopInitRound":                      reflect.ValueOf((*character.ShopInitRound)(nil)),
		"ShopM":                              reflect.ValueOf((*character.ShopM)(nil)),
		"ShopReset":                          reflect.ValueOf((*character.ShopReset)(nil)),
		"ShopResetRandom":                    reflect.ValueOf((*character.ShopResetRandom)(nil)),
		"ShopResetRound":                     reflect.ValueOf((*character.ShopResetRound)(nil)),
		"SingleSummonDiamond":                reflect.ValueOf((*character.SingleSummonDiamond)(nil)),
		"SingleSummonFree":                   reflect.ValueOf((*character.SingleSummonFree)(nil)),
		"SingleSummonItem":                   reflect.ValueOf((*character.SingleSummonItem)(nil)),
		"Skin":                               reflect.ValueOf((*character.Skin)(nil)),
		"SkinM":                              reflect.ValueOf((*character.SkinM)(nil)),
		"SkinNoRepeatOwnership":              reflect.ValueOf((*character.SkinNoRepeatOwnership)(nil)),
		"SpecialCheckInstance":               reflect.ValueOf((*character.SpecialCheckInstance)(nil)),
		"SpecialNumberType":                  reflect.ValueOf((*character.SpecialNumberType)(nil)),
		"StoryReview":                        reflect.ValueOf((*character.StoryReview)(nil)),
		"SuitUnit":                           reflect.ValueOf((*character.SuitUnit)(nil)),
		"Summon":                             reflect.ValueOf((*character.Summon)(nil)),
		"SummonWay":                          reflect.ValueOf((*character.SummonWay)(nil)),
		"SyncGstFormationCondition":          reflect.ValueOf((*character.SyncGstFormationCondition)(nil)),
		"TalentTree":                         reflect.ValueOf((*character.TalentTree)(nil)),
		"Tales":                              reflect.ValueOf((*character.Tales)(nil)),
		"TaskHandler":                        reflect.ValueOf((*character.TaskHandler)(nil)),
		"TestAiToolsArtifact":                reflect.ValueOf((*character.TestAiToolsArtifact)(nil)),
		"TestAiToolsArtifactManager":         reflect.ValueOf((*character.TestAiToolsArtifactManager)(nil)),
		"TestAiToolsArtifacts":               reflect.ValueOf((*character.TestAiToolsArtifacts)(nil)),
		"TestAiToolsHero":                    reflect.ValueOf((*character.TestAiToolsHero)(nil)),
		"TestAiToolsHeroManager":             reflect.ValueOf((*character.TestAiToolsHeroManager)(nil)),
		"TestAiToolsHeros":                   reflect.ValueOf((*character.TestAiToolsHeros)(nil)),
		"TitleM":                             reflect.ValueOf((*character.TitleM)(nil)),
		"TowerSeason":                        reflect.ValueOf((*character.TowerSeason)(nil)),
		"Towers":                             reflect.ValueOf((*character.Towers)(nil)),
		"Towerstar":                          reflect.ValueOf((*character.Towerstar)(nil)),
		"TowestarCheckStarFunc":              reflect.ValueOf((*character.TowestarCheckStarFunc)(nil)),
		"Trials":                             reflect.ValueOf((*character.Trials)(nil)),
		"User":                               reflect.ValueOf((*character.User)(nil)),
		"UserGuild":                          reflect.ValueOf((*character.UserGuild)(nil)),
		"UserGuildChestItem":                 reflect.ValueOf((*character.UserGuildChestItem)(nil)),
		"UserManager":                        reflect.ValueOf((*character.UserManager)(nil)),
		"UserModuler":                        reflect.ValueOf((*character.UserModuler)(nil)),
		"UsersCache":                         reflect.ValueOf((*character.UsersCache)(nil)),
		"VipM":                               reflect.ValueOf((*character.VipM)(nil)),
		"WeeklyTask":                         reflect.ValueOf((*character.WeeklyTask)(nil)),
		"WeightLattice":                      reflect.ValueOf((*character.WeightLattice)(nil)),
		"WorldBoss":                          reflect.ValueOf((*character.WorldBoss)(nil)),
		"XmlDataCheckInstance":               reflect.ValueOf((*character.XmlDataCheckInstance)(nil)),

		// interface wrapper definitions
		"_ActivitySubControl":      reflect.ValueOf((*_app_logic_character_ActivitySubControl)(nil)),
		"_ArtifactDebutActHandler": reflect.ValueOf((*_app_logic_character_ArtifactDebutActHandler)(nil)),
		"_DisorderAwardSrv":        reflect.ValueOf((*_app_logic_character_DisorderAwardSrv)(nil)),
		"_DisorderBattleSrv":       reflect.ValueOf((*_app_logic_character_DisorderBattleSrv)(nil)),
		"_DivineDemonCost":         reflect.ValueOf((*_app_logic_character_DivineDemonCost)(nil)),
		"_DivineDemonTask":         reflect.ValueOf((*_app_logic_character_DivineDemonTask)(nil)),
		"_GodPresentCheckOpen":     reflect.ValueOf((*_app_logic_character_GodPresentCheckOpen)(nil)),
		"_GuildTalentLevelUp":      reflect.ValueOf((*_app_logic_character_GuildTalentLevelUp)(nil)),
		"_HandbookHandle":          reflect.ValueOf((*_app_logic_character_HandbookHandle)(nil)),
		"_MaybeOfflineUserLog":     reflect.ValueOf((*_app_logic_character_MaybeOfflineUserLog)(nil)),
		"_NumUse":                  reflect.ValueOf((*_app_logic_character_NumUse)(nil)),
		"_PassI":                   reflect.ValueOf((*_app_logic_character_PassI)(nil)),
		"_ReturnAward":             reflect.ValueOf((*_app_logic_character_ReturnAward)(nil)),
		"_Servicer":                reflect.ValueOf((*_app_logic_character_Servicer)(nil)),
		"_ShopBuy":                 reflect.ValueOf((*_app_logic_character_ShopBuy)(nil)),
		"_ShopGoodOpen":            reflect.ValueOf((*_app_logic_character_ShopGoodOpen)(nil)),
		"_ShopInit":                reflect.ValueOf((*_app_logic_character_ShopInit)(nil)),
		"_ShopReset":               reflect.ValueOf((*_app_logic_character_ShopReset)(nil)),
		"_TaskHandler":             reflect.ValueOf((*_app_logic_character_TaskHandler)(nil)),
		"_UserModuler":             reflect.ValueOf((*_app_logic_character_UserModuler)(nil)),
	}
}

// _app_logic_character_ActivitySubControl is an interface wrapper for ActivitySubControl type
type _app_logic_character_ActivitySubControl struct {
	IValue            interface{}
	WAfterInitSubFunc func(a0 *cl.ActivitySum, a1 *character.User, a2 character.Servicer, a3 bool)
	WDestroyFunc      func(a0 *cl.ActivitySum, a1 *character.User, a2 character.Servicer)
	WInitSubFunc      func(a0 *cl.ActivitySum)
	WRestDaily        func(a0 *cl.ActivitySum, a1 int64, a2 character.Servicer)
}

func (W _app_logic_character_ActivitySubControl) AfterInitSubFunc(a0 *cl.ActivitySum, a1 *character.User, a2 character.Servicer, a3 bool) {
	W.WAfterInitSubFunc(a0, a1, a2, a3)
}
func (W _app_logic_character_ActivitySubControl) DestroyFunc(a0 *cl.ActivitySum, a1 *character.User, a2 character.Servicer) {
	W.WDestroyFunc(a0, a1, a2)
}
func (W _app_logic_character_ActivitySubControl) InitSubFunc(a0 *cl.ActivitySum) {
	W.WInitSubFunc(a0)
}
func (W _app_logic_character_ActivitySubControl) RestDaily(a0 *cl.ActivitySum, a1 int64, a2 character.Servicer) {
	W.WRestDaily(a0, a1, a2)
}

// _app_logic_character_ArtifactDebutActHandler is an interface wrapper for ArtifactDebutActHandler type
type _app_logic_character_ArtifactDebutActHandler struct {
	IValue           interface{}
	WCanReceiveAward func(a0 uint32, a1 []uint32) bool
	WGetAward        func(actID uint32, ids []uint32) []*cl.Resource
	WReceiveAward    func(a0 []uint32) uint64
}

func (W _app_logic_character_ArtifactDebutActHandler) CanReceiveAward(a0 uint32, a1 []uint32) bool {
	return W.WCanReceiveAward(a0, a1)
}
func (W _app_logic_character_ArtifactDebutActHandler) GetAward(actID uint32, ids []uint32) []*cl.Resource {
	return W.WGetAward(actID, ids)
}
func (W _app_logic_character_ArtifactDebutActHandler) ReceiveAward(a0 []uint32) uint64 {
	return W.WReceiveAward(a0)
}

// _app_logic_character_DisorderAwardSrv is an interface wrapper for DisorderAwardSrv type
type _app_logic_character_DisorderAwardSrv struct {
	IValue interface{}
}

// _app_logic_character_DisorderBattleSrv is an interface wrapper for DisorderBattleSrv type
type _app_logic_character_DisorderBattleSrv struct {
	IValue interface{}
	WDo    func(a0 character.Servicer, a1 *character.DisorderLand, a2 *character.DisorderLandMap, a3 *character.DisorderLandNode, a4 *cl.L2C_DisorderlandTriggerEvent, a5 *goxml.DisorderlandDungeonInfoExt, a6 *goxml.DisorderlandMapInfoExt, a7 []byte) uint32
}

func (W _app_logic_character_DisorderBattleSrv) Do(a0 character.Servicer, a1 *character.DisorderLand, a2 *character.DisorderLandMap, a3 *character.DisorderLandNode, a4 *cl.L2C_DisorderlandTriggerEvent, a5 *goxml.DisorderlandDungeonInfoExt, a6 *goxml.DisorderlandMapInfoExt, a7 []byte) uint32 {
	return W.WDo(a0, a1, a2, a3, a4, a5, a6, a7)
}

// _app_logic_character_DivineDemonCost is an interface wrapper for DivineDemonCost type
type _app_logic_character_DivineDemonCost struct {
	IValue interface{}
}

// _app_logic_character_DivineDemonTask is an interface wrapper for DivineDemonTask type
type _app_logic_character_DivineDemonTask struct {
	IValue            interface{}
	WGetTaskAwards    func(activityID uint32, taskIDs []uint32) (awards []*cl.Resource)
	WGetTaskData      func(activityID uint32, taskID uint32) (uint32, uint64, *cl.TaskTypeProgress)
	WGetTaskProgress  func() map[uint32]*cl.TaskTypeProgress
	WHandlerReceive   func(taskIDs []uint32)
	WIsReceiveAwarded func(taskID uint32) bool
}

func (W _app_logic_character_DivineDemonTask) GetTaskAwards(activityID uint32, taskIDs []uint32) (awards []*cl.Resource) {
	return W.WGetTaskAwards(activityID, taskIDs)
}
func (W _app_logic_character_DivineDemonTask) GetTaskData(activityID uint32, taskID uint32) (uint32, uint64, *cl.TaskTypeProgress) {
	return W.WGetTaskData(activityID, taskID)
}
func (W _app_logic_character_DivineDemonTask) GetTaskProgress() map[uint32]*cl.TaskTypeProgress {
	return W.WGetTaskProgress()
}
func (W _app_logic_character_DivineDemonTask) HandlerReceive(taskIDs []uint32) {
	W.WHandlerReceive(taskIDs)
}
func (W _app_logic_character_DivineDemonTask) IsReceiveAwarded(taskID uint32) bool {
	return W.WIsReceiveAwarded(taskID)
}

// _app_logic_character_GodPresentCheckOpen is an interface wrapper for GodPresentCheckOpen type
type _app_logic_character_GodPresentCheckOpen struct {
	IValue  interface{}
	WIsOpen func(a0 *character.GodPresentManager, a1 uint32, a2 *goxml.GodPresentInfoExt) bool
}

func (W _app_logic_character_GodPresentCheckOpen) IsOpen(a0 *character.GodPresentManager, a1 uint32, a2 *goxml.GodPresentInfoExt) bool {
	return W.WIsOpen(a0, a1, a2)
}

// _app_logic_character_GuildTalentLevelUp is an interface wrapper for GuildTalentLevelUp type
type _app_logic_character_GuildTalentLevelUp struct {
	IValue             interface{}
	WCheckLevelUpLimit func(a0 uint32, a1 uint32, a2 uint32, a3 *goxml.GuildTalentInfoExt, a4 *character.GuildTalent) uint32
	WGetCosts          func(a0 uint32, a1 uint32, a2 uint32, a3 *goxml.GuildTalentInfoExt) []*cl.Resource
	WLevelUp           func(a0 uint32, a1 uint32, a2 *character.GuildTalent)
}

func (W _app_logic_character_GuildTalentLevelUp) CheckLevelUpLimit(a0 uint32, a1 uint32, a2 uint32, a3 *goxml.GuildTalentInfoExt, a4 *character.GuildTalent) uint32 {
	return W.WCheckLevelUpLimit(a0, a1, a2, a3, a4)
}
func (W _app_logic_character_GuildTalentLevelUp) GetCosts(a0 uint32, a1 uint32, a2 uint32, a3 *goxml.GuildTalentInfoExt) []*cl.Resource {
	return W.WGetCosts(a0, a1, a2, a3)
}
func (W _app_logic_character_GuildTalentLevelUp) LevelUp(a0 uint32, a1 uint32, a2 *character.GuildTalent) {
	W.WLevelUp(a0, a1, a2)
}

// _app_logic_character_HandbookHandle is an interface wrapper for HandbookHandle type
type _app_logic_character_HandbookHandle struct {
	IValue         interface{}
	WActivate      func(a0 []uint32)
	WAdd           func(a0 uint32) uint32
	WAddGlobalAttr func()
	WGetAttr       func(a0 []uint32) []*goxml.AttrTypeAndValue
	WGetAwards     func(a0 uint32) []*cl.Resource
	WGetCount      func() int
	WIsActivated   func(a0 uint32) bool
	WIsAwarded     func(a0 uint32) bool
	WIsExist       func(a0 uint32) bool
	WSave          func()
	WSetAwarded    func(a0 []uint32)
}

func (W _app_logic_character_HandbookHandle) Activate(a0 []uint32) {
	W.WActivate(a0)
}
func (W _app_logic_character_HandbookHandle) Add(a0 uint32) uint32 {
	return W.WAdd(a0)
}
func (W _app_logic_character_HandbookHandle) AddGlobalAttr() {
	W.WAddGlobalAttr()
}
func (W _app_logic_character_HandbookHandle) GetAttr(a0 []uint32) []*goxml.AttrTypeAndValue {
	return W.WGetAttr(a0)
}
func (W _app_logic_character_HandbookHandle) GetAwards(a0 uint32) []*cl.Resource {
	return W.WGetAwards(a0)
}
func (W _app_logic_character_HandbookHandle) GetCount() int {
	return W.WGetCount()
}
func (W _app_logic_character_HandbookHandle) IsActivated(a0 uint32) bool {
	return W.WIsActivated(a0)
}
func (W _app_logic_character_HandbookHandle) IsAwarded(a0 uint32) bool {
	return W.WIsAwarded(a0)
}
func (W _app_logic_character_HandbookHandle) IsExist(a0 uint32) bool {
	return W.WIsExist(a0)
}
func (W _app_logic_character_HandbookHandle) Save() {
	W.WSave()
}
func (W _app_logic_character_HandbookHandle) SetAwarded(a0 []uint32) {
	W.WSetAwarded(a0)
}

// _app_logic_character_MaybeOfflineUserLog is an interface wrapper for MaybeOfflineUserLog type
type _app_logic_character_MaybeOfflineUserLog struct {
	IValue                         interface{}
	WLogDeleteUserResources        func(srv character.Servicer, delCurrencies []*cl.Resource)
	WLogFriendAdded                func(srv character.Servicer, userID uint64)
	WLogFriendBlacklisted          func(srv character.Servicer, userID uint64)
	WLogFriendDeleted              func(srv character.Servicer, userID uint64)
	WLogFriendRemovedFromBlacklist func(srv character.Servicer, userID uint64)
	WLogGuildManage                func(srv character.Servicer, param *log.GuildManage)
	WLogGuildPositionChange        func(srv character.Servicer, param *log.GuildPosition)
	WLogGuildUserJoinOrQuit        func(srv character.Servicer, param *log.GuildUserJoinOrQuit)
	WLogSeasonArenaBeFight         func(srv character.Servicer, attackID uint64, attackSID uint64, attackArena uint64, attackGid uint64, round uint32, myChange *cl.SeasonArenaUserScoreAndRank, opChange *cl.SeasonArenaUserScoreAndRank, myDivision uint32, opponentDivision uint32, win bool, reportID string)
	WLogSeasonArenaDivisionChange  func(srv character.Servicer, oldDivision uint32, newDivision uint32, round uint32, zone uint32)
	WLogWebLargeRecharge           func(srv character.Servicer, params *gm.WebLargeRechargeMailParams, mailID uint64)
}

func (W _app_logic_character_MaybeOfflineUserLog) LogDeleteUserResources(srv character.Servicer, delCurrencies []*cl.Resource) {
	W.WLogDeleteUserResources(srv, delCurrencies)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogFriendAdded(srv character.Servicer, userID uint64) {
	W.WLogFriendAdded(srv, userID)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogFriendBlacklisted(srv character.Servicer, userID uint64) {
	W.WLogFriendBlacklisted(srv, userID)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogFriendDeleted(srv character.Servicer, userID uint64) {
	W.WLogFriendDeleted(srv, userID)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogFriendRemovedFromBlacklist(srv character.Servicer, userID uint64) {
	W.WLogFriendRemovedFromBlacklist(srv, userID)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogGuildManage(srv character.Servicer, param *log.GuildManage) {
	W.WLogGuildManage(srv, param)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogGuildPositionChange(srv character.Servicer, param *log.GuildPosition) {
	W.WLogGuildPositionChange(srv, param)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogGuildUserJoinOrQuit(srv character.Servicer, param *log.GuildUserJoinOrQuit) {
	W.WLogGuildUserJoinOrQuit(srv, param)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogSeasonArenaBeFight(srv character.Servicer, attackID uint64, attackSID uint64, attackArena uint64, attackGid uint64, round uint32, myChange *cl.SeasonArenaUserScoreAndRank, opChange *cl.SeasonArenaUserScoreAndRank, myDivision uint32, opponentDivision uint32, win bool, reportID string) {
	W.WLogSeasonArenaBeFight(srv, attackID, attackSID, attackArena, attackGid, round, myChange, opChange, myDivision, opponentDivision, win, reportID)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogSeasonArenaDivisionChange(srv character.Servicer, oldDivision uint32, newDivision uint32, round uint32, zone uint32) {
	W.WLogSeasonArenaDivisionChange(srv, oldDivision, newDivision, round, zone)
}
func (W _app_logic_character_MaybeOfflineUserLog) LogWebLargeRecharge(srv character.Servicer, params *gm.WebLargeRechargeMailParams, mailID uint64) {
	W.WLogWebLargeRecharge(srv, params, mailID)
}

// _app_logic_character_NumUse is an interface wrapper for NumUse type
type _app_logic_character_NumUse struct {
	IValue interface{}
}

// _app_logic_character_PassI is an interface wrapper for PassI type
type _app_logic_character_PassI struct {
	IValue interface{}
	WIsEnd func(srv character.Servicer, user *character.User, passInfo *goxml.PassInfoExt, passData *character.PassData) bool
}

func (W _app_logic_character_PassI) IsEnd(srv character.Servicer, user *character.User, passInfo *goxml.PassInfoExt, passData *character.PassData) bool {
	return W.WIsEnd(srv, user, passInfo, passData)
}

// _app_logic_character_ReturnAward is an interface wrapper for ReturnAward type
type _app_logic_character_ReturnAward struct {
	IValue           interface{}
	WGetTaskProgress func() map[uint32]*cl.TaskTypeProgress
}

func (W _app_logic_character_ReturnAward) GetTaskProgress() map[uint32]*cl.TaskTypeProgress {
	return W.WGetTaskProgress()
}

// _app_logic_character_Servicer is an interface wrapper for Servicer type
type _app_logic_character_Servicer struct {
	IValue                           interface{}
	WAccountM                        func() *account.Manager
	WActivityMixM                    func() *etcdcache.ActivityMixManager
	WAddAchievementsShowcase         func(a0 uint64, a1 *cl.AchievementsShowcase)
	WAddTargetCmdMes                 func(a0 uint32, a1 proto.Message)
	WAppID                           func() uint64
	WBattleDebug                     func() bool
	WBroadCastCmdByUids              func(a0 []uint64, a1 cl.ID, a2 interface{})
	WBroadcastCmdByOpIDAndChannel    func(a0 cl.ID, a1 interface{}, a2 []uint32, a3 []uint32)
	WBroadcastCmdToClient            func(a0 cl.ID, a1 interface{})
	WCheckLocalServer                func(a0 uint64) bool
	WCommonRankM                     func() *rank.CommonRankManager
	WCouponCfgM                      func() *etcdcache.CouponCfgManager
	WCreateAsyncMessage              func(a0 interface{}, a1 int64) uint64
	WCreateLogID                     func() uint64
	WCreateUniqueID                  func() uint64
	WCreateUserLogID                 func() uint64
	WCrossM                          func() *cross.Manager
	WCrossMasterM                    func() *cross.CrossMasterManager
	WDelDropActivityFlowerOccupy     func(uid uint64, itemRes map[uint32]map[uint32]*cl.Resource) []*cl.Resource
	WDeleteTimerID                   func(a0 uint32)
	WEnableTraceLog                  func(uid uint64) bool
	WEventM                          func() *event.Manager
	WFixActivityTime                 func(now int64, startTm int64, endTm int64, protectDay uint32) (bool, int64)
	WFlowerPushFrequencyLimitDelete  func(a0 uint64)
	WGateM                           func() *session.Manager
	WGetActivityScheduleConfig       func() *cl.ActivityScheduleDatas
	WGetCrossArea                    func(a0 uint32) uint32
	WGetFunctionStatus               func(a0 uint32) bool
	WGetGlobalRank                   func(a0 rank.ID) *rank.Global
	WGetGstChatRoomID                func(uid uint64) string
	WGetGuildDungeonChatRoomID       func(uid uint64) uint64
	WGetGuildID                      func(uid uint64) uint64
	WGetGuildMedal                   func(uid uint64) *db.LogicGuildMedal
	WGetHotfixManager                func() *hotfix.HotfixManager
	WGetLogicGuild                   func(uid uint64) *db.LogicGuild
	WGetNormalPartSids               func() []uint64
	WGetPgpIdsByUser                 func(uid uint64) []uint32
	WGetSeasonComplianceCurStage     func(user *character.User) *cl.SeasonComplianceStage
	WGetTimeZoneAndOffest            func() string
	WGetUserCacheInterval            func() int64
	WGetUserGuildLevel               func(a0 uint64) uint32
	WGetUserSaveInterval             func() int64
	WLinkSetting                     func() *etcdcache.LinkSettingManager
	WLogTopicDC                      func() string
	WLogTopicResource                func() string
	WMongoM                          func() *mongo.Manager
	WMultiLangM                      func() *multilang.Manager
	WNewLogMessage                   func() *da.Log
	WOSSM                            func() *oss.OSSManager
	WOpGroup                         func() uint64
	WPlatformConfig                  func() *config.Platform
	WRand                            func() *rand.Rand
	WRankAchieveM                    func() *rankachieve.Manager
	WRefundMailTemplateM             func() *etcdcache.RefundMailTemplateManager
	WReqTimeM                        func() *request.TimeOutManager
	WSendCmdToCross                  func(a0 l2c.ID, a1 uint64, a2 interface{}) bool
	WSendCmdToCrossActivityPartition func(a0 uint32, a1 uint32, a2 uint32, a3 interface{}) bool
	WSendCmdToDB                     func(a0 uint32, a1 uint64, a2 interface{})
	WSendCmdToOSS                    func(a0 uint32, a1 uint64, a2 interface{}, a3 common.FUNCID)
	WSendCmdToPlatform               func(a0 uint32, a1 uint64, a2 interface{})
	WSendRemoteLogic                 func(a0 uint64, a1 l2c.REMOTE_LOGIC, a2 interface{}) bool
	WServerDay                       func(now int64) uint32
	WServerID                        func() uint64
	WServerName                      func() string
	WServerOpenDay                   func(now int64) uint32
	WServerType                      func() string
	WServiceStatus                   func() int32
	WSimulationSendCmdToCross        func(a0 l2c.ID, a1 l2c.ID, a2 uint64) bool
	WSmallRankM                      func() *rank.SmallRankManager
	WStartServiceTm                  func() int64
	WSyncGstBuildDispatch            func(user *character.User, hid uint64)
	WSyncGstFormation                func(user *character.User)
	WSyncGstUserInfo                 func(user *character.User)
	WSyncSeasonArenaSnapshot         func(user *character.User)
	WUserM                           func() *character.UserManager
	WWriteLogMessage                 func(msg *da.Log)
}

func (W _app_logic_character_Servicer) AccountM() *account.Manager {
	return W.WAccountM()
}
func (W _app_logic_character_Servicer) ActivityMixM() *etcdcache.ActivityMixManager {
	return W.WActivityMixM()
}
func (W _app_logic_character_Servicer) AddAchievementsShowcase(a0 uint64, a1 *cl.AchievementsShowcase) {
	W.WAddAchievementsShowcase(a0, a1)
}
func (W _app_logic_character_Servicer) AddTargetCmdMes(a0 uint32, a1 proto.Message) {
	W.WAddTargetCmdMes(a0, a1)
}
func (W _app_logic_character_Servicer) AppID() uint64 {
	return W.WAppID()
}
func (W _app_logic_character_Servicer) BattleDebug() bool {
	return W.WBattleDebug()
}
func (W _app_logic_character_Servicer) BroadCastCmdByUids(a0 []uint64, a1 cl.ID, a2 interface{}) {
	W.WBroadCastCmdByUids(a0, a1, a2)
}
func (W _app_logic_character_Servicer) BroadcastCmdByOpIDAndChannel(a0 cl.ID, a1 interface{}, a2 []uint32, a3 []uint32) {
	W.WBroadcastCmdByOpIDAndChannel(a0, a1, a2, a3)
}
func (W _app_logic_character_Servicer) BroadcastCmdToClient(a0 cl.ID, a1 interface{}) {
	W.WBroadcastCmdToClient(a0, a1)
}
func (W _app_logic_character_Servicer) CheckLocalServer(a0 uint64) bool {
	return W.WCheckLocalServer(a0)
}
func (W _app_logic_character_Servicer) CommonRankM() *rank.CommonRankManager {
	return W.WCommonRankM()
}
func (W _app_logic_character_Servicer) CouponCfgM() *etcdcache.CouponCfgManager {
	return W.WCouponCfgM()
}
func (W _app_logic_character_Servicer) CreateAsyncMessage(a0 interface{}, a1 int64) uint64 {
	return W.WCreateAsyncMessage(a0, a1)
}
func (W _app_logic_character_Servicer) CreateLogID() uint64 {
	return W.WCreateLogID()
}
func (W _app_logic_character_Servicer) CreateUniqueID() uint64 {
	return W.WCreateUniqueID()
}
func (W _app_logic_character_Servicer) CreateUserLogID() uint64 {
	return W.WCreateUserLogID()
}
func (W _app_logic_character_Servicer) CrossM() *cross.Manager {
	return W.WCrossM()
}
func (W _app_logic_character_Servicer) CrossMasterM() *cross.CrossMasterManager {
	return W.WCrossMasterM()
}
func (W _app_logic_character_Servicer) DelDropActivityFlowerOccupy(uid uint64, itemRes map[uint32]map[uint32]*cl.Resource) []*cl.Resource {
	return W.WDelDropActivityFlowerOccupy(uid, itemRes)
}
func (W _app_logic_character_Servicer) DeleteTimerID(a0 uint32) {
	W.WDeleteTimerID(a0)
}
func (W _app_logic_character_Servicer) EnableTraceLog(uid uint64) bool {
	return W.WEnableTraceLog(uid)
}
func (W _app_logic_character_Servicer) EventM() *event.Manager {
	return W.WEventM()
}
func (W _app_logic_character_Servicer) FixActivityTime(now int64, startTm int64, endTm int64, protectDay uint32) (bool, int64) {
	return W.WFixActivityTime(now, startTm, endTm, protectDay)
}
func (W _app_logic_character_Servicer) FlowerPushFrequencyLimitDelete(a0 uint64) {
	W.WFlowerPushFrequencyLimitDelete(a0)
}
func (W _app_logic_character_Servicer) GateM() *session.Manager {
	return W.WGateM()
}
func (W _app_logic_character_Servicer) GetActivityScheduleConfig() *cl.ActivityScheduleDatas {
	return W.WGetActivityScheduleConfig()
}
func (W _app_logic_character_Servicer) GetCrossArea(a0 uint32) uint32 {
	return W.WGetCrossArea(a0)
}
func (W _app_logic_character_Servicer) GetFunctionStatus(a0 uint32) bool {
	return W.WGetFunctionStatus(a0)
}
func (W _app_logic_character_Servicer) GetGlobalRank(a0 rank.ID) *rank.Global {
	return W.WGetGlobalRank(a0)
}
func (W _app_logic_character_Servicer) GetGstChatRoomID(uid uint64) string {
	return W.WGetGstChatRoomID(uid)
}
func (W _app_logic_character_Servicer) GetGuildDungeonChatRoomID(uid uint64) uint64 {
	return W.WGetGuildDungeonChatRoomID(uid)
}
func (W _app_logic_character_Servicer) GetGuildID(uid uint64) uint64 {
	return W.WGetGuildID(uid)
}
func (W _app_logic_character_Servicer) GetGuildMedal(uid uint64) *db.LogicGuildMedal {
	return W.WGetGuildMedal(uid)
}
func (W _app_logic_character_Servicer) GetHotfixManager() *hotfix.HotfixManager {
	return W.WGetHotfixManager()
}
func (W _app_logic_character_Servicer) GetLogicGuild(uid uint64) *db.LogicGuild {
	return W.WGetLogicGuild(uid)
}
func (W _app_logic_character_Servicer) GetNormalPartSids() []uint64 {
	return W.WGetNormalPartSids()
}
func (W _app_logic_character_Servicer) GetPgpIdsByUser(uid uint64) []uint32 {
	return W.WGetPgpIdsByUser(uid)
}
func (W _app_logic_character_Servicer) GetSeasonComplianceCurStage(user *character.User) *cl.SeasonComplianceStage {
	return W.WGetSeasonComplianceCurStage(user)
}
func (W _app_logic_character_Servicer) GetTimeZoneAndOffest() string {
	return W.WGetTimeZoneAndOffest()
}
func (W _app_logic_character_Servicer) GetUserCacheInterval() int64 {
	return W.WGetUserCacheInterval()
}
func (W _app_logic_character_Servicer) GetUserGuildLevel(a0 uint64) uint32 {
	return W.WGetUserGuildLevel(a0)
}
func (W _app_logic_character_Servicer) GetUserSaveInterval() int64 {
	return W.WGetUserSaveInterval()
}
func (W _app_logic_character_Servicer) LinkSetting() *etcdcache.LinkSettingManager {
	return W.WLinkSetting()
}
func (W _app_logic_character_Servicer) LogTopicDC() string {
	return W.WLogTopicDC()
}
func (W _app_logic_character_Servicer) LogTopicResource() string {
	return W.WLogTopicResource()
}
func (W _app_logic_character_Servicer) MongoM() *mongo.Manager {
	return W.WMongoM()
}
func (W _app_logic_character_Servicer) MultiLangM() *multilang.Manager {
	return W.WMultiLangM()
}
func (W _app_logic_character_Servicer) NewLogMessage() *da.Log {
	return W.WNewLogMessage()
}
func (W _app_logic_character_Servicer) OSSM() *oss.OSSManager {
	return W.WOSSM()
}
func (W _app_logic_character_Servicer) OpGroup() uint64 {
	return W.WOpGroup()
}
func (W _app_logic_character_Servicer) PlatformConfig() *config.Platform {
	return W.WPlatformConfig()
}
func (W _app_logic_character_Servicer) Rand() *rand.Rand {
	return W.WRand()
}
func (W _app_logic_character_Servicer) RankAchieveM() *rankachieve.Manager {
	return W.WRankAchieveM()
}
func (W _app_logic_character_Servicer) RefundMailTemplateM() *etcdcache.RefundMailTemplateManager {
	return W.WRefundMailTemplateM()
}
func (W _app_logic_character_Servicer) ReqTimeM() *request.TimeOutManager {
	return W.WReqTimeM()
}
func (W _app_logic_character_Servicer) SendCmdToCross(a0 l2c.ID, a1 uint64, a2 interface{}) bool {
	return W.WSendCmdToCross(a0, a1, a2)
}
func (W _app_logic_character_Servicer) SendCmdToCrossActivityPartition(a0 uint32, a1 uint32, a2 uint32, a3 interface{}) bool {
	return W.WSendCmdToCrossActivityPartition(a0, a1, a2, a3)
}
func (W _app_logic_character_Servicer) SendCmdToDB(a0 uint32, a1 uint64, a2 interface{}) {
	W.WSendCmdToDB(a0, a1, a2)
}
func (W _app_logic_character_Servicer) SendCmdToOSS(a0 uint32, a1 uint64, a2 interface{}, a3 common.FUNCID) {
	W.WSendCmdToOSS(a0, a1, a2, a3)
}
func (W _app_logic_character_Servicer) SendCmdToPlatform(a0 uint32, a1 uint64, a2 interface{}) {
	W.WSendCmdToPlatform(a0, a1, a2)
}
func (W _app_logic_character_Servicer) SendRemoteLogic(a0 uint64, a1 l2c.REMOTE_LOGIC, a2 interface{}) bool {
	return W.WSendRemoteLogic(a0, a1, a2)
}
func (W _app_logic_character_Servicer) ServerDay(now int64) uint32 {
	return W.WServerDay(now)
}
func (W _app_logic_character_Servicer) ServerID() uint64 {
	return W.WServerID()
}
func (W _app_logic_character_Servicer) ServerName() string {
	return W.WServerName()
}
func (W _app_logic_character_Servicer) ServerOpenDay(now int64) uint32 {
	return W.WServerOpenDay(now)
}
func (W _app_logic_character_Servicer) ServerType() string {
	return W.WServerType()
}
func (W _app_logic_character_Servicer) ServiceStatus() int32 {
	return W.WServiceStatus()
}
func (W _app_logic_character_Servicer) SimulationSendCmdToCross(a0 l2c.ID, a1 l2c.ID, a2 uint64) bool {
	return W.WSimulationSendCmdToCross(a0, a1, a2)
}
func (W _app_logic_character_Servicer) SmallRankM() *rank.SmallRankManager {
	return W.WSmallRankM()
}
func (W _app_logic_character_Servicer) StartServiceTm() int64 {
	return W.WStartServiceTm()
}
func (W _app_logic_character_Servicer) SyncGstBuildDispatch(user *character.User, hid uint64) {
	W.WSyncGstBuildDispatch(user, hid)
}
func (W _app_logic_character_Servicer) SyncGstFormation(user *character.User) {
	W.WSyncGstFormation(user)
}
func (W _app_logic_character_Servicer) SyncGstUserInfo(user *character.User) {
	W.WSyncGstUserInfo(user)
}
func (W _app_logic_character_Servicer) SyncSeasonArenaSnapshot(user *character.User) {
	W.WSyncSeasonArenaSnapshot(user)
}
func (W _app_logic_character_Servicer) UserM() *character.UserManager {
	return W.WUserM()
}
func (W _app_logic_character_Servicer) WriteLogMessage(msg *da.Log) {
	W.WWriteLogMessage(msg)
}

// _app_logic_character_ShopBuy is an interface wrapper for ShopBuy type
type _app_logic_character_ShopBuy struct {
	IValue               interface{}
	WCheckGoodAndCalcRes func(a0 character.Servicer, a1 *character.User, a2 uint32, a3 uint32) (uint32, []*cl.Resource, []*cl.Resource)
	WUpdate              func(a0 uint32, a1 uint32) bool
}

func (W _app_logic_character_ShopBuy) CheckGoodAndCalcRes(a0 character.Servicer, a1 *character.User, a2 uint32, a3 uint32) (uint32, []*cl.Resource, []*cl.Resource) {
	return W.WCheckGoodAndCalcRes(a0, a1, a2, a3)
}
func (W _app_logic_character_ShopBuy) Update(a0 uint32, a1 uint32) bool {
	return W.WUpdate(a0, a1)
}

// _app_logic_character_ShopGoodOpen is an interface wrapper for ShopGoodOpen type
type _app_logic_character_ShopGoodOpen struct {
	IValue          interface{}
	WIsShopGoodOpen func(a0 *goxml.ShopRegularGoodsInfoExt, a1 character.Servicer) bool
}

func (W _app_logic_character_ShopGoodOpen) IsShopGoodOpen(a0 *goxml.ShopRegularGoodsInfoExt, a1 character.Servicer) bool {
	return W.WIsShopGoodOpen(a0, a1)
}

// _app_logic_character_ShopInit is an interface wrapper for ShopInit type
type _app_logic_character_ShopInit struct {
	IValue interface{}
}

// _app_logic_character_ShopReset is an interface wrapper for ShopReset type
type _app_logic_character_ShopReset struct {
	IValue interface{}
}

// _app_logic_character_TaskHandler is an interface wrapper for TaskHandler type
type _app_logic_character_TaskHandler struct {
	IValue              interface{}
	WGetOneTypeProgress func(a0 *goxml.TaskTypeInfo) *cl.TaskTypeProgress
	WGetProgress        func() map[uint32]*cl.TaskTypeProgress
	WIsAwarded          func(a0 uint32) bool
	WReceiveAward       func(a0 []uint32)
}

func (W _app_logic_character_TaskHandler) GetOneTypeProgress(a0 *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	return W.WGetOneTypeProgress(a0)
}
func (W _app_logic_character_TaskHandler) GetProgress() map[uint32]*cl.TaskTypeProgress {
	return W.WGetProgress()
}
func (W _app_logic_character_TaskHandler) IsAwarded(a0 uint32) bool {
	return W.WIsAwarded(a0)
}
func (W _app_logic_character_TaskHandler) ReceiveAward(a0 []uint32) {
	W.WReceiveAward(a0)
}

// _app_logic_character_UserModuler is an interface wrapper for UserModuler type
type _app_logic_character_UserModuler struct {
	IValue interface{}
}
