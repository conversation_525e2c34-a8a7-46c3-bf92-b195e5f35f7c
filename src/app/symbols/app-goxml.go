// Code generated by 'yaegi extract app/goxml'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package symbols

import (
	"app/goxml"
	"app/protos/out/cl"
	"go/constant"
	"go/token"
	"reflect"
)

func init() {
	Symbols["app/goxml/goxml"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"AbsEffectAttrAttack":                                reflect.ValueOf(&goxml.AbsEffectAttrAttack).Elem(),
		"AbsEffectAttrDef":                                   reflect.ValueOf(&goxml.AbsEffectAttrDef).Elem(),
		"ActivityComplianceID":                               reflect.ValueOf(goxml.ActivityComplianceID),
		"ActivityCouponDiamond":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ActivityCouponFree":                                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityCouponRecharge":                             reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"ActivityCouponType":                                 reflect.ValueOf(&goxml.ActivityCouponType).Elem(),
		"ActivityMaxIndex":                                   reflect.ValueOf(goxml.ActivityMaxIndex),
		"ActivityMirageRankID":                               reflect.ValueOf(goxml.ActivityMirageRankID),
		"ActivityPyramidInitialFloor":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidInitialRound":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidLatticeBigPrize":                     reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"ActivityPyramidLatticeBigPrizeDrawMax":              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidLatticeChoosePrize":                  reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"ActivityPyramidLatticeChoosePrizeDrawMax":           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidLatticeNormalPrize":                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidLatticeNormalPrizeDrawMax":           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidLatticeRisePrizeOne":                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ActivityPyramidLatticeRisePrizeOneDrawMax":          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ActivityPyramidTestDrawResultNum":                   reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"ActivityRechargeActivityDrop":                       reflect.ValueOf(goxml.ActivityRechargeActivityDrop),
		"ActivityRechargeActivityFeed":                       reflect.ValueOf(goxml.ActivityRechargeActivityFeed),
		"ActivityRechargeActivityPuzzle":                     reflect.ValueOf(goxml.ActivityRechargeActivityPuzzle),
		"ActivityRechargeActivityReturn":                     reflect.ValueOf(goxml.ActivityRechargeActivityReturn),
		"ActivityRechargeActivitySelect":                     reflect.ValueOf(goxml.ActivityRechargeActivitySelect),
		"ActivityRechargeActivityShoot":                      reflect.ValueOf(goxml.ActivityRechargeActivityShoot),
		"ActivityRechargeActivityStory":                      reflect.ValueOf(goxml.ActivityRechargeActivityStory),
		"ActivityRechargeActivityTurnTable":                  reflect.ValueOf(goxml.ActivityRechargeActivityTurnTable),
		"ActivityRechargeArtifactDebut":                      reflect.ValueOf(goxml.ActivityRechargeArtifactDebut),
		"ActivityRechargeDivineDemon":                        reflect.ValueOf(goxml.ActivityRechargeDivineDemon),
		"ActivityRechargeDropActivity":                       reflect.ValueOf(goxml.ActivityRechargeDropActivity),
		"ActivityRechargeGiftTypeNormal":                     reflect.ValueOf(goxml.ActivityRechargeGiftTypeNormal),
		"ActivityRechargeGiftTypeOneClick":                   reflect.ValueOf(goxml.ActivityRechargeGiftTypeOneClick),
		"ActivityRechargeLinkSummon":                         reflect.ValueOf(goxml.ActivityRechargeLinkSummon),
		"ActivityRechargeLinkSummonShopId":                   reflect.ValueOf(goxml.ActivityRechargeLinkSummonShopId),
		"ActivityRechargeNewServerPushGift":                  reflect.ValueOf(goxml.ActivityRechargeNewServerPushGift),
		"ActivityRechargePyramid":                            reflect.ValueOf(goxml.ActivityRechargePyramid),
		"ActivityRechargeServerDayOpen":                      reflect.ValueOf(goxml.ActivityRechargeServerDayOpen),
		"ActivityRechargeShopArtifactDebutDraw":              reflect.ValueOf(goxml.ActivityRechargeShopArtifactDebutDraw),
		"ActivityRechargeShopDivineDemonDraw":                reflect.ValueOf(goxml.ActivityRechargeShopDivineDemonDraw),
		"ActivityRechargeUserDayOpen":                        reflect.ValueOf(goxml.ActivityRechargeUserDayOpen),
		"ActivityStoryLayerTypeHero":                         reflect.ValueOf(goxml.ActivityStoryLayerTypeHero),
		"ActivityStoryLayerTypeSuit":                         reflect.ValueOf(goxml.ActivityStoryLayerTypeSuit),
		"ActivityStoryRankID":                                reflect.ValueOf(goxml.ActivityStoryRankID),
		"ActivitySumAllTypes":                                reflect.ValueOf(&goxml.ActivitySumAllTypes).Elem(),
		"ActivitySumDrop":                                    reflect.ValueOf(goxml.ActivitySumDrop),
		"ActivitySumFeed":                                    reflect.ValueOf(goxml.ActivitySumFeed),
		"ActivitySumFeedDefaultCond":                         reflect.ValueOf(goxml.ActivitySumFeedDefaultCond),
		"ActivitySumFeedDefaultLevel":                        reflect.ValueOf(goxml.ActivitySumFeedDefaultLevel),
		"ActivitySumMinCheckTm":                              reflect.ValueOf(goxml.ActivitySumMinCheckTm),
		"ActivitySumPuzzle":                                  reflect.ValueOf(goxml.ActivitySumPuzzle),
		"ActivitySumShoot":                                   reflect.ValueOf(goxml.ActivitySumShoot),
		"ActivitySumTurnTable":                               reflect.ValueOf(goxml.ActivitySumTurnTable),
		"ActivitySumTypeDivineDemonSummon":                   reflect.ValueOf(goxml.ActivitySumTypeDivineDemonSummon),
		"ActivitySumTypeNewServerPushGift":                   reflect.ValueOf(goxml.ActivitySumTypeNewServerPushGift),
		"ActivitySumTypeRound":                               reflect.ValueOf(&goxml.ActivitySumTypeRound).Elem(),
		"ActivitySumTypeRoundAdvanced":                       reflect.ValueOf(goxml.ActivitySumTypeRoundAdvanced),
		"ActivitySumTypeRoundArtifact":                       reflect.ValueOf(goxml.ActivitySumTypeRoundArtifact),
		"ActivitySumTypeRoundArtifactDebut":                  reflect.ValueOf(goxml.ActivitySumTypeRoundArtifactDebut),
		"ActivitySumTypeRoundAssoc":                          reflect.ValueOf(goxml.ActivitySumTypeRoundAssoc),
		"ActivitySumTypeRoundLink":                           reflect.ValueOf(goxml.ActivitySumTypeRoundLink),
		"ActivityTowerRankID":                                reflect.ValueOf(goxml.ActivityTowerRankID),
		"ActivityTowerSeasonCalcCurrentAwardTime":            reflect.ValueOf(goxml.ActivityTowerSeasonCalcCurrentAwardTime),
		"ActivityTowerSeasonCalcLastAwardTime":               reflect.ValueOf(goxml.ActivityTowerSeasonCalcLastAwardTime),
		"ActivityTowerSeasonRankID":                          reflect.ValueOf(goxml.ActivityTowerSeasonRankID),
		"AddPctAttr":                                         reflect.ValueOf(goxml.AddPctAttr),
		"AddRateCondMax":                                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"AddRateCondNone":                                    reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"AddRateCondOwnerTeamLinkNum":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"AddResourcesForFree":                                reflect.ValueOf(goxml.AddResourcesForFree),
		"AddResourcesForRecharge":                            reflect.ValueOf(goxml.AddResourcesForRecharge),
		"AdvancedSummon":                                     reflect.ValueOf(goxml.AdvancedSummon),
		"AdvancedSummonCount":                                reflect.ValueOf(goxml.AdvancedSummonCount),
		"ArenaBotNumPerLv":                                   reflect.ValueOf(goxml.ArenaBotNumPerLv),
		"ArenaDivisionAwardCap":                              reflect.ValueOf(goxml.ArenaDivisionAwardCap),
		"ArenaDivisionUserCap":                               reflect.ValueOf(goxml.ArenaDivisionUserCap),
		"ArenaFightLose":                                     reflect.ValueOf(goxml.ArenaFightLose),
		"ArenaFightWin":                                      reflect.ValueOf(goxml.ArenaFightWin),
		"ArenaLogMaxNum":                                     reflect.ValueOf(goxml.ArenaLogMaxNum),
		"ArenaMatchExtMaxNum":                                reflect.ValueOf(goxml.ArenaMatchExtMaxNum),
		"ArenaMatchExtModeDown":                              reflect.ValueOf(goxml.ArenaMatchExtModeDown),
		"ArenaMatchExtModeNone":                              reflect.ValueOf(goxml.ArenaMatchExtModeNone),
		"ArenaMatchExtModeUpThenDown":                        reflect.ValueOf(goxml.ArenaMatchExtModeUpThenDown),
		"ArenaOpponentNum":                                   reflect.ValueOf(goxml.ArenaOpponentNum),
		"ArenaRankId":                                        reflect.ValueOf(goxml.ArenaRankId),
		"ArenaRankRewardNum":                                 reflect.ValueOf(goxml.ArenaRankRewardNum),
		"ArenaRankTypeDaily":                                 reflect.ValueOf(goxml.ArenaRankTypeDaily),
		"ArenaRankTypeSeason":                                reflect.ValueOf(goxml.ArenaRankTypeSeason),
		"ArtifactDebut":                                      reflect.ValueOf(goxml.ArtifactDebut),
		"ArtifactDebutDrawCategoryCount":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ArtifactDebutDrawKindCommon":                        reflect.ValueOf(goxml.ArtifactDebutDrawKindCommon),
		"ArtifactDebutDrawKindWishArtifact":                  reflect.ValueOf(goxml.ArtifactDebutDrawKindWishArtifact),
		"ArtifactDebutDrawKindWishArtifactFragment":          reflect.ValueOf(goxml.ArtifactDebutDrawKindWishArtifactFragment),
		"ArtifactDebutDrawLegalKind":                         reflect.ValueOf(&goxml.ArtifactDebutDrawLegalKind).Elem(),
		"ArtifactDebutDrawRechargeCountCap":                  reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"ArtifactDebutGMMinUniqID":                           reflect.ValueOf(goxml.ArtifactDebutGMMinUniqID),
		"ArtifactDebutJuniorDrawItemID":                      reflect.ValueOf(goxml.ArtifactDebutJuniorDrawItemID),
		"ArtifactDebutJuniorDrawMinGropID":                   reflect.ValueOf(goxml.ArtifactDebutJuniorDrawMinGropID),
		"ArtifactDebutProtectDay":                            reflect.ValueOf(goxml.ArtifactDebutProtectDay),
		"ArtifactDebutPuzzleAllPos":                          reflect.ValueOf(&goxml.ArtifactDebutPuzzleAllPos).Elem(),
		"ArtifactDebutPuzzleAwardID2Condition":               reflect.ValueOf(&goxml.ArtifactDebutPuzzleAwardID2Condition).Elem(),
		"ArtifactDebutPuzzleItemID":                          reflect.ValueOf(goxml.ArtifactDebutPuzzleItemID),
		"ArtifactDebutPuzzleMaxAwardID":                      reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"ArtifactDebutSeniorDrawItemID":                      reflect.ValueOf(goxml.ArtifactDebutSeniorDrawItemID),
		"ArtifactDebutTaskTypeAchieve":                       reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"ArtifactDebutTaskTypeDaily":                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ArtifactFragmentGuaranteeClass":                     reflect.ValueOf(goxml.ArtifactFragmentGuaranteeClass),
		"ArtifactFragmentGuaranteeCount":                     reflect.ValueOf(goxml.ArtifactFragmentGuaranteeCount),
		"ArtifactInitialForge":                               reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"ArtifactInitialStar":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ArtifactInitialStrength":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ArtifactPoinsSummon":                                reflect.ValueOf(goxml.ArtifactPoinsSummon),
		"ArtifactSummon":                                     reflect.ValueOf(goxml.ArtifactSummon),
		"ArtifactSummonGetPointValue":                        reflect.ValueOf(goxml.ArtifactSummonGetPointValue),
		"AssocExchangeAdvanceOne":                            reflect.ValueOf(goxml.AssocExchangeAdvanceOne),
		"AssocExchangeAdvanceTwo":                            reflect.ValueOf(goxml.AssocExchangeAdvanceTwo),
		"AssocExchangeBaseOne":                               reflect.ValueOf(goxml.AssocExchangeBaseOne),
		"AssocExchangeBaseThree":                             reflect.ValueOf(goxml.AssocExchangeBaseThree),
		"AssocExchangeBaseTwo":                               reflect.ValueOf(goxml.AssocExchangeBaseTwo),
		"AssocSummonDemon":                                   reflect.ValueOf(goxml.AssocSummonDemon),
		"AssocSummonEclipse":                                 reflect.ValueOf(goxml.AssocSummonEclipse),
		"AssocSummonEmpire":                                  reflect.ValueOf(goxml.AssocSummonEmpire),
		"AssocSummonWoodland":                                reflect.ValueOf(goxml.AssocSummonWoodland),
		"AttrAbsoluteAttack":                                 reflect.ValueOf(constant.MakeFromLiteral("72", token.INT, 0)),
		"AttrAbsoluteDef":                                    reflect.ValueOf(constant.MakeFromLiteral("73", token.INT, 0)),
		"AttrAbsoluteMaxHp":                                  reflect.ValueOf(constant.MakeFromLiteral("78", token.INT, 0)),
		"AttrArtifactDamAddRate":                             reflect.ValueOf(constant.MakeFromLiteral("80", token.INT, 0)),
		"AttrArtifactDamReduceRate":                          reflect.ValueOf(constant.MakeFromLiteral("81", token.INT, 0)),
		"AttrAttack":                                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"AttrAttackIndependentPcts":                          reflect.ValueOf(&goxml.AttrAttackIndependentPcts).Elem(),
		"AttrAttackPct":                                      reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"AttrAttackPct1":                                     reflect.ValueOf(constant.MakeFromLiteral("49", token.INT, 0)),
		"AttrAttackPctForSeasonHero":                         reflect.ValueOf(constant.MakeFromLiteral("66", token.INT, 0)),
		"AttrAttackPctForSeasonTeam":                         reflect.ValueOf(constant.MakeFromLiteral("63", token.INT, 0)),
		"AttrAttackPcts":                                     reflect.ValueOf(&goxml.AttrAttackPcts).Elem(),
		"AttrChangeFlagGroupCount":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"AttrChangeFlagParam":                                reflect.ValueOf(constant.MakeFromLiteral("63", token.INT, 0)),
		"AttrControlRate":                                    reflect.ValueOf(constant.MakeFromLiteral("22", token.INT, 0)),
		"AttrControlReduceRate":                              reflect.ValueOf(constant.MakeFromLiteral("23", token.INT, 0)),
		"AttrCritDamAddRate":                                 reflect.ValueOf(constant.MakeFromLiteral("34", token.INT, 0)),
		"AttrCritDamAddRatePct":                              reflect.ValueOf(constant.MakeFromLiteral("76", token.INT, 0)),
		"AttrCritDamAddRatePcts":                             reflect.ValueOf(&goxml.AttrCritDamAddRatePcts).Elem(),
		"AttrCritDamReduceRate":                              reflect.ValueOf(constant.MakeFromLiteral("35", token.INT, 0)),
		"AttrCritRate":                                       reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"AttrCritReduceRate":                                 reflect.ValueOf(constant.MakeFromLiteral("21", token.INT, 0)),
		"AttrDamAddRate":                                     reflect.ValueOf(constant.MakeFromLiteral("26", token.INT, 0)),
		"AttrDamReduceRate":                                  reflect.ValueOf(constant.MakeFromLiteral("27", token.INT, 0)),
		"AttrDamReflectRate":                                 reflect.ValueOf(constant.MakeFromLiteral("38", token.INT, 0)),
		"AttrDamReflectReduceRate":                           reflect.ValueOf(constant.MakeFromLiteral("39", token.INT, 0)),
		"AttrDef":                                            reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"AttrDefIndependentPcts":                             reflect.ValueOf(&goxml.AttrDefIndependentPcts).Elem(),
		"AttrDefMerged":                                      reflect.ValueOf(&goxml.AttrDefMerged).Elem(),
		"AttrDefPct":                                         reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"AttrDefPct1":                                        reflect.ValueOf(constant.MakeFromLiteral("51", token.INT, 0)),
		"AttrDefPctForSeasonHero":                            reflect.ValueOf(constant.MakeFromLiteral("67", token.INT, 0)),
		"AttrDefPctForSeasonTeam":                            reflect.ValueOf(constant.MakeFromLiteral("64", token.INT, 0)),
		"AttrDefPctMerged":                                   reflect.ValueOf(&goxml.AttrDefPctMerged).Elem(),
		"AttrDefPunctureRate":                                reflect.ValueOf(constant.MakeFromLiteral("36", token.INT, 0)),
		"AttrDefPunctureReduceRate":                          reflect.ValueOf(constant.MakeFromLiteral("37", token.INT, 0)),
		"AttrDodgeRate":                                      reflect.ValueOf(constant.MakeFromLiteral("19", token.INT, 0)),
		"AttrDodgeRate2":                                     reflect.ValueOf(constant.MakeFromLiteral("75", token.INT, 0)),
		"AttrFinalDamAddRate":                                reflect.ValueOf(constant.MakeFromLiteral("42", token.INT, 0)),
		"AttrFinalDamReduceRate":                             reflect.ValueOf(constant.MakeFromLiteral("43", token.INT, 0)),
		"AttrFinalHealRate":                                  reflect.ValueOf(constant.MakeFromLiteral("44", token.INT, 0)),
		"AttrFixMaxHpPct":                                    reflect.ValueOf(constant.MakeFromLiteral("47", token.INT, 0)),
		"AttrFixMaxHpPct2":                                   reflect.ValueOf(constant.MakeFromLiteral("60", token.INT, 0)),
		"AttrFixMaxHpPct3":                                   reflect.ValueOf(constant.MakeFromLiteral("77", token.INT, 0)),
		"AttrFixedDam":                                       reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"AttrFixedDamPct":                                    reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"AttrFixedDamPcts":                                   reflect.ValueOf(&goxml.AttrFixedDamPcts).Elem(),
		"AttrFixedDamReduce":                                 reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"AttrFixedDamReducePct":                              reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"AttrFixedDamReducePcts":                             reflect.ValueOf(&goxml.AttrFixedDamReducePcts).Elem(),
		"AttrGVGDamAddRate":                                  reflect.ValueOf(constant.MakeFromLiteral("69", token.INT, 0)),
		"AttrGVGDamReduceRate":                               reflect.ValueOf(constant.MakeFromLiteral("70", token.INT, 0)),
		"AttrGVGMoraleDamReduceRate":                         reflect.ValueOf(constant.MakeFromLiteral("71", token.INT, 0)),
		"AttrGetEnergyPct":                                   reflect.ValueOf(constant.MakeFromLiteral("41", token.INT, 0)),
		"AttrGetHealRate":                                    reflect.ValueOf(constant.MakeFromLiteral("25", token.INT, 0)),
		"AttrHealRate":                                       reflect.ValueOf(constant.MakeFromLiteral("24", token.INT, 0)),
		"AttrHitRate":                                        reflect.ValueOf(constant.MakeFromLiteral("18", token.INT, 0)),
		"AttrHitRate2":                                       reflect.ValueOf(constant.MakeFromLiteral("74", token.INT, 0)),
		"AttrHp":                                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"AttrHpPct":                                          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"AttrHpPct1":                                         reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"AttrHpPctForSeasonHero":                             reflect.ValueOf(constant.MakeFromLiteral("68", token.INT, 0)),
		"AttrHpPctForSeasonTeam":                             reflect.ValueOf(constant.MakeFromLiteral("65", token.INT, 0)),
		"AttrHpPcts":                                         reflect.ValueOf(&goxml.AttrHpPcts).Elem(),
		"AttrLockBlood":                                      reflect.ValueOf(constant.MakeFromLiteral("58", token.INT, 0)),
		"AttrMagDamAddRate":                                  reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"AttrMagDamReduceRate":                               reflect.ValueOf(constant.MakeFromLiteral("31", token.INT, 0)),
		"AttrMagDef":                                         reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"AttrMagDefPct":                                      reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"AttrMagDefPcts":                                     reflect.ValueOf(&goxml.AttrMagDefPcts).Elem(),
		"AttrMaxNum":                                         reflect.ValueOf(constant.MakeFromLiteral("83", token.INT, 0)),
		"AttrNone":                                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"AttrPVPDamAddRate":                                  reflect.ValueOf(constant.MakeFromLiteral("45", token.INT, 0)),
		"AttrPVPDamReduceRate":                               reflect.ValueOf(constant.MakeFromLiteral("46", token.INT, 0)),
		"AttrPhyDamAddRate":                                  reflect.ValueOf(constant.MakeFromLiteral("28", token.INT, 0)),
		"AttrPhyDamReduceRate":                               reflect.ValueOf(constant.MakeFromLiteral("29", token.INT, 0)),
		"AttrPhyDef":                                         reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"AttrPhyDefPct":                                      reflect.ValueOf(constant.MakeFromLiteral("13", token.INT, 0)),
		"AttrPhyDefPcts":                                     reflect.ValueOf(&goxml.AttrPhyDefPcts).Elem(),
		"AttrRealDam":                                        reflect.ValueOf(constant.MakeFromLiteral("17", token.INT, 0)),
		"AttrRealDefenseAddDam":                              reflect.ValueOf(constant.MakeFromLiteral("40", token.INT, 0)),
		"AttrSeasonDamAddRate":                               reflect.ValueOf(constant.MakeFromLiteral("56", token.INT, 0)),
		"AttrSeasonDamAddRateForLink":                        reflect.ValueOf(constant.MakeFromLiteral("61", token.INT, 0)),
		"AttrSeasonDamReduceForLink":                         reflect.ValueOf(constant.MakeFromLiteral("62", token.INT, 0)),
		"AttrSeasonDamReduceRate":                            reflect.ValueOf(constant.MakeFromLiteral("57", token.INT, 0)),
		"AttrSeasonMagDamAddRate":                            reflect.ValueOf(constant.MakeFromLiteral("54", token.INT, 0)),
		"AttrSeasonMagDamReduceRate":                         reflect.ValueOf(constant.MakeFromLiteral("55", token.INT, 0)),
		"AttrSeasonPhyDamAddRate":                            reflect.ValueOf(constant.MakeFromLiteral("52", token.INT, 0)),
		"AttrSeasonPhyDamReduceRate":                         reflect.ValueOf(constant.MakeFromLiteral("53", token.INT, 0)),
		"AttrSeasonShieldDamAddRate":                         reflect.ValueOf(constant.MakeFromLiteral("82", token.INT, 0)),
		"AttrShieldDamAddRate":                               reflect.ValueOf(constant.MakeFromLiteral("48", token.INT, 0)),
		"AttrSpeed":                                          reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"AttrSpeedPct":                                       reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"AttrSpeedPcts":                                      reflect.ValueOf(&goxml.AttrSpeedPcts).Elem(),
		"AttrSuckBloodRate":                                  reflect.ValueOf(constant.MakeFromLiteral("32", token.INT, 0)),
		"AttrSuckBloodReduceRate":                            reflect.ValueOf(constant.MakeFromLiteral("33", token.INT, 0)),
		"AttrUndefined":                                      reflect.ValueOf(constant.MakeFromLiteral("59", token.INT, 0)),
		"AttrYiShang":                                        reflect.ValueOf(constant.MakeFromLiteral("79", token.INT, 0)),
		"AvatarTimeTypeDuration":                             reflect.ValueOf(goxml.AvatarTimeTypeDuration),
		"AvatarTimeTypeEndTime":                              reflect.ValueOf(goxml.AvatarTimeTypeEndTime),
		"AvatarTimeTypeForever":                              reflect.ValueOf(goxml.AvatarTimeTypeForever),
		"AvatarTypeChatBubble":                               reflect.ValueOf(goxml.AvatarTypeChatBubble),
		"AvatarTypeFrame":                                    reflect.ValueOf(goxml.AvatarTypeFrame),
		"AvatarTypeIcon":                                     reflect.ValueOf(goxml.AvatarTypeIcon),
		"AvatarTypeImage":                                    reflect.ValueOf(goxml.AvatarTypeImage),
		"AvatarUnlockTypeArenaLevel":                         reflect.ValueOf(goxml.AvatarUnlockTypeArenaLevel),
		"AvatarUnlockTypeHero":                               reflect.ValueOf(goxml.AvatarUnlockTypeHero),
		"AvatarUnlockTypeNone":                               reflect.ValueOf(goxml.AvatarUnlockTypeNone),
		"AvatarUnlockTypeSkin":                               reflect.ValueOf(goxml.AvatarUnlockTypeSkin),
		"BaseChooseConditionBuffType":                        reflect.ValueOf(goxml.BaseChooseConditionBuffType),
		"BaseChooseConditionControl":                         reflect.ValueOf(goxml.BaseChooseConditionControl),
		"BaseChooseConditionExcludeAllCallMonster":           reflect.ValueOf(goxml.BaseChooseConditionExcludeAllCallMonster),
		"BaseChooseConditionExcludeBuffType":                 reflect.ValueOf(goxml.BaseChooseConditionExcludeBuffType),
		"BaseChooseConditionExcludeHeroSysId":                reflect.ValueOf(goxml.BaseChooseConditionExcludeHeroSysId),
		"BaseChooseConditionHeroSysId":                       reflect.ValueOf(goxml.BaseChooseConditionHeroSysId),
		"BaseChooseConditionJob":                             reflect.ValueOf(goxml.BaseChooseConditionJob),
		"BaseChooseConditionLink":                            reflect.ValueOf(goxml.BaseChooseConditionLink),
		"BaseChooseConditionMonsterType":                     reflect.ValueOf(goxml.BaseChooseConditionMonsterType),
		"BaseChooseConditionNo":                              reflect.ValueOf(goxml.BaseChooseConditionNo),
		"BaseChooseConditionPosition":                        reflect.ValueOf(goxml.BaseChooseConditionPosition),
		"BaseChooseConditionRace":                            reflect.ValueOf(goxml.BaseChooseConditionRace),
		"BaseChooseConditionSex":                             reflect.ValueOf(goxml.BaseChooseConditionSex),
		"BaseChooseSortAttackPriority":                       reflect.ValueOf(goxml.BaseChooseSortAttackPriority),
		"BaseChooseSortAttrHighToLow":                        reflect.ValueOf(goxml.BaseChooseSortAttrHighToLow),
		"BaseChooseSortAttrLowToHigh":                        reflect.ValueOf(goxml.BaseChooseSortAttrLowToHigh),
		"BaseChooseSortBuffBigTypeNumHighToLow":              reflect.ValueOf(goxml.BaseChooseSortBuffBigTypeNumHighToLow),
		"BaseChooseSortBuffBigTypeNumLowToHigh":              reflect.ValueOf(goxml.BaseChooseSortBuffBigTypeNumLowToHigh),
		"BaseChooseSortBuffNumHighToLow":                     reflect.ValueOf(goxml.BaseChooseSortBuffNumHighToLow),
		"BaseChooseSortBuffNumLowToHigh":                     reflect.ValueOf(goxml.BaseChooseSortBuffNumLowToHigh),
		"BaseChooseSortByAttackPosition":                     reflect.ValueOf(goxml.BaseChooseSortByAttackPosition),
		"BaseChooseSortDefault":                              reflect.ValueOf(goxml.BaseChooseSortDefault),
		"BaseChooseSortDuplicatesRandom":                     reflect.ValueOf(goxml.BaseChooseSortDuplicatesRandom),
		"BaseChooseSortRandom":                               reflect.ValueOf(goxml.BaseChooseSortRandom),
		"BaseChooseSortRandomExclude":                        reflect.ValueOf(goxml.BaseChooseSortRandomExclude),
		"BaseChooseSortTeamPriority":                         reflect.ValueOf(goxml.BaseChooseSortTeamPriority),
		"BaseCritDamParaID":                                  reflect.ValueOf(goxml.BaseCritDamParaID),
		"BaseCritParaID":                                     reflect.ValueOf(goxml.BaseCritParaID),
		"BaseFilterAlive":                                    reflect.ValueOf(goxml.BaseFilterAlive),
		"BaseFilterArtifact":                                 reflect.ValueOf(goxml.BaseFilterArtifact),
		"BaseFilterInheritAttackAlive":                       reflect.ValueOf(goxml.BaseFilterInheritAttackAlive),
		"BaseFilterInheritDefenseAlive":                      reflect.ValueOf(goxml.BaseFilterInheritDefenseAlive),
		"BaseFilterInheritPsStageAlive":                      reflect.ValueOf(goxml.BaseFilterInheritPsStageAlive),
		"BaseFilterInheritStage1Alive":                       reflect.ValueOf(goxml.BaseFilterInheritStage1Alive),
		"BaseFilterInheritStage2Alive":                       reflect.ValueOf(goxml.BaseFilterInheritStage2Alive),
		"BaseFilterInheritStage3Alive":                       reflect.ValueOf(goxml.BaseFilterInheritStage3Alive),
		"BaseFilterInheritStageAllAlive":                     reflect.ValueOf(goxml.BaseFilterInheritStageAllAlive),
		"BaseFilterNoInherit":                                reflect.ValueOf(goxml.BaseFilterNoInherit),
		"BaseFilterOpTeamAlive":                              reflect.ValueOf(goxml.BaseFilterOpTeamAlive),
		"BaseFilterOpTeamAliveNoSelf":                        reflect.ValueOf(goxml.BaseFilterOpTeamAliveNoSelf),
		"BaseFilterOpTeamArtifact":                           reflect.ValueOf(goxml.BaseFilterOpTeamArtifact),
		"BaseFilterSelfTeamAlive":                            reflect.ValueOf(goxml.BaseFilterSelfTeamAlive),
		"BaseFilterSelfTeamAll":                              reflect.ValueOf(goxml.BaseFilterSelfTeamAll),
		"BaseFilterSelfTeamArtifact":                         reflect.ValueOf(goxml.BaseFilterSelfTeamArtifact),
		"BaseFilterSelfTeamDeadNoNoAliveBuff":                reflect.ValueOf(goxml.BaseFilterSelfTeamDeadNoNoAliveBuff),
		"BaseFilterSelfTeamDeadNoNoAliveBuffNoSelf":          reflect.ValueOf(goxml.BaseFilterSelfTeamDeadNoNoAliveBuffNoSelf),
		"BaseFloat":                                          reflect.ValueOf(goxml.BaseFloat),
		"BaseHitParaID":                                      reflect.ValueOf(goxml.BaseHitParaID),
		"BaseInt":                                            reflect.ValueOf(goxml.BaseInt),
		"BaseInt64":                                          reflect.ValueOf(goxml.BaseInt64),
		"BaseUInt32":                                         reflect.ValueOf(goxml.BaseUInt32),
		"BasicSummon":                                        reflect.ValueOf(goxml.BasicSummon),
		"BattleAttackTargetTypeSingle":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"BattleParaHurtReflectMax":                           reflect.ValueOf(goxml.BattleParaHurtReflectMax),
		"BattleParaHurtReflectMin":                           reflect.ValueOf(goxml.BattleParaHurtReflectMin),
		"BattleParaSuckBloodMax":                             reflect.ValueOf(goxml.BattleParaSuckBloodMax),
		"BattleParaSuckBloodMin":                             reflect.ValueOf(goxml.BattleParaSuckBloodMin),
		"BattleReflectFixRate":                               reflect.ValueOf(goxml.BattleReflectFixRate),
		"BinaryUint64Max":                                    reflect.ValueOf(constant.MakeFromLiteral("63", token.INT, 0)),
		"BlessingAttrCount":                                  reflect.ValueOf(goxml.BlessingAttrCount),
		"BossRush1RankID":                                    reflect.ValueOf(goxml.BossRush1RankID),
		"BossRush2RankID":                                    reflect.ValueOf(goxml.BossRush2RankID),
		"BossRush3RankID":                                    reflect.ValueOf(goxml.BossRush3RankID),
		"BossRush4RankID":                                    reflect.ValueOf(goxml.BossRush4RankID),
		"BossRush5RankID":                                    reflect.ValueOf(goxml.BossRush5RankID),
		"BossRush6RankID":                                    reflect.ValueOf(goxml.BossRush6RankID),
		"BossRush7RankID":                                    reflect.ValueOf(goxml.BossRush7RankID),
		"BossRush8RankID":                                    reflect.ValueOf(goxml.BossRush8RankID),
		"BossRushFormationPool":                              reflect.ValueOf(&goxml.BossRushFormationPool).Elem(),
		"BossRushStageID":                                    reflect.ValueOf(goxml.BossRushStageID),
		"BotNumPerLevel":                                     reflect.ValueOf(goxml.BotNumPerLevel),
		"BuffCondAlive":                                      reflect.ValueOf(goxml.BuffCondAlive),
		"BuffCondMax":                                        reflect.ValueOf(goxml.BuffCondMax),
		"BuffCondNone":                                       reflect.ValueOf(goxml.BuffCondNone),
		"BuffCondOnEvent":                                    reflect.ValueOf(goxml.BuffCondOnEvent),
		"BuffEffectActive":                                   reflect.ValueOf(goxml.BuffEffectActive),
		"BuffEffectBeRemove":                                 reflect.ValueOf(goxml.BuffEffectBeRemove),
		"BuffEffectDecrLayer":                                reflect.ValueOf(goxml.BuffEffectDecrLayer),
		"BuffEffectMax":                                      reflect.ValueOf(goxml.BuffEffectMax),
		"BuffEffectNone":                                     reflect.ValueOf(goxml.BuffEffectNone),
		"BuffEffectNotActive":                                reflect.ValueOf(goxml.BuffEffectNotActive),
		"BuffGroupTypeSeparate":                              reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"BuffGroupTypeTogether":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CalcLastDailyResetTime":                             reflect.ValueOf(goxml.CalcLastDailyResetTime),
		"CalcPeakFighterCount":                               reflect.ValueOf(goxml.CalcPeakFighterCount),
		"CalcPeakMatchCount":                                 reflect.ValueOf(goxml.CalcPeakMatchCount),
		"CalcRandomNum":                                      reflect.ValueOf(goxml.CalcRandomNum),
		"CarnivalSeasonEnterDay":                             reflect.ValueOf(goxml.CarnivalSeasonEnterDay),
		"CarnivalTaskEventRecordFromCarnivalDayOpen":         reflect.ValueOf(goxml.CarnivalTaskEventRecordFromCarnivalDayOpen),
		"CarnivalTaskEventRecordFromCarnivalOpen":            reflect.ValueOf(goxml.CarnivalTaskEventRecordFromCarnivalOpen),
		"CarnivalTaskEventRecordFromSeasonEnter":             reflect.ValueOf(goxml.CarnivalTaskEventRecordFromSeasonEnter),
		"CarnivalTaskEventRecordFromServiceOpen":             reflect.ValueOf(goxml.CarnivalTaskEventRecordFromServiceOpen),
		"CarnivalTaskScore":                                  reflect.ValueOf(goxml.CarnivalTaskScore),
		"CarnivalTypeNormal":                                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CarnivalTypeSeason":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ChallengeLogMaxNum":                                 reflect.ValueOf(goxml.ChallengeLogMaxNum),
		"ChatAllWorld":                                       reflect.ValueOf(&goxml.ChatAllWorld).Elem(),
		"ChatChestNewChest":                                  reflect.ValueOf(goxml.ChatChestNewChest),
		"ChatGST":                                            reflect.ValueOf(&goxml.ChatGST).Elem(),
		"ChatGstBlessGuildChange":                            reflect.ValueOf(goxml.ChatGstBlessGuildChange),
		"ChatGstBossAllFail":                                 reflect.ValueOf(goxml.ChatGstBossAllFail),
		"ChatGstBossSingleDie":                               reflect.ValueOf(goxml.ChatGstBossSingleDie),
		"ChatGstChallengeWins":                               reflect.ValueOf(goxml.ChatGstChallengeWins),
		"ChatGstFightWins":                                   reflect.ValueOf(goxml.ChatGstFightWins),
		"ChatGstGuildGetStronghold":                          reflect.ValueOf(goxml.ChatGstGuildGetStronghold),
		"ChatGstMainBuildLevel10":                            reflect.ValueOf(goxml.ChatGstMainBuildLevel10),
		"ChatGstMainBuildLevel20":                            reflect.ValueOf(goxml.ChatGstMainBuildLevel20),
		"ChatGuild":                                          reflect.ValueOf(&goxml.ChatGuild).Elem(),
		"ChatGuildDungeon":                                   reflect.ValueOf(&goxml.ChatGuildDungeon).Elem(),
		"ChatGuildDungeonRoomRankDown":                       reflect.ValueOf(goxml.ChatGuildDungeonRoomRankDown),
		"ChatGuildDungeonRoomRankUp":                         reflect.ValueOf(goxml.ChatGuildDungeonRoomRankUp),
		"ChatGuildDungeonSeasonTop3Guild":                    reflect.ValueOf(goxml.ChatGuildDungeonSeasonTop3Guild),
		"ChatGuildDungeonSeasonTopDivision":                  reflect.ValueOf(goxml.ChatGuildDungeonSeasonTopDivision),
		"ChatGuildDungeonSetFocus":                           reflect.ValueOf(goxml.ChatGuildDungeonSetFocus),
		"ChatGuildDungeonSignUp":                             reflect.ValueOf(goxml.ChatGuildDungeonSignUp),
		"ChatGuildDungeonUseStrategyAddChallengeCount":       reflect.ValueOf(goxml.ChatGuildDungeonUseStrategyAddChallengeCount),
		"ChatGuildDungeonUseStrategyAddDamage":               reflect.ValueOf(goxml.ChatGuildDungeonUseStrategyAddDamage),
		"ChatGuildDungeonUseStrategyRestoreBoss":             reflect.ValueOf(goxml.ChatGuildDungeonUseStrategyRestoreBoss),
		"ChatGuildGstBossSingleDie":                          reflect.ValueOf(goxml.ChatGuildGstBossSingleDie),
		"ChatLocalWorld":                                     reflect.ValueOf(&goxml.ChatLocalWorld).Elem(),
		"ChatNeedChangeSendID":                               reflect.ValueOf(&goxml.ChatNeedChangeSendID).Elem(),
		"ChatPartition":                                      reflect.ValueOf(&goxml.ChatPartition).Elem(),
		"ChatPeakPhaseTop1":                                  reflect.ValueOf(goxml.ChatPeakPhaseTop1),
		"ChatPeakSeasonTop1":                                 reflect.ValueOf(goxml.ChatPeakSeasonTop1),
		"ChatPeakTop1Change":                                 reflect.ValueOf(goxml.ChatPeakTop1Change),
		"ChatPrivate":                                        reflect.ValueOf(&goxml.ChatPrivate).Elem(),
		"ChatSystem":                                         reflect.ValueOf(&goxml.ChatSystem).Elem(),
		"ChatSystemGuild":                                    reflect.ValueOf(&goxml.ChatSystemGuild).Elem(),
		"ChatTypeAdvancedSummonMulti":                        reflect.ValueOf(goxml.ChatTypeAdvancedSummonMulti),
		"ChatTypeAdvancedSummonSingle":                       reflect.ValueOf(goxml.ChatTypeAdvancedSummonSingle),
		"ChatTypeArenaRank":                                  reflect.ValueOf(goxml.ChatTypeArenaRank),
		"ChatTypeArenaWin":                                   reflect.ValueOf(goxml.ChatTypeArenaWin),
		"ChatTypeArtifactDebutDraw":                          reflect.ValueOf(goxml.ChatTypeArtifactDebutDraw),
		"ChatTypeArtifactDebutPointsExchange":                reflect.ValueOf(goxml.ChatTypeArtifactDebutPointsExchange),
		"ChatTypeArtifactPointSummon":                        reflect.ValueOf(goxml.ChatTypeArtifactPointSummon),
		"ChatTypeArtifactSummonMulti":                        reflect.ValueOf(goxml.ChatTypeArtifactSummonMulti),
		"ChatTypeArtifactSummonSingle":                       reflect.ValueOf(goxml.ChatTypeArtifactSummonSingle),
		"ChatTypeBasicSummonMulti":                           reflect.ValueOf(goxml.ChatTypeBasicSummonMulti),
		"ChatTypeBasicSummonSingle":                          reflect.ValueOf(goxml.ChatTypeBasicSummonSingle),
		"ChatTypeDebutShopBuyHero":                           reflect.ValueOf(goxml.ChatTypeDebutShopBuyHero),
		"ChatTypeDispatchMoreThanOrange":                     reflect.ValueOf(goxml.ChatTypeDispatchMoreThanOrange),
		"ChatTypeDivineDemonSummon":                          reflect.ValueOf(goxml.ChatTypeDivineDemonSummon),
		"ChatTypeFirstCome":                                  reflect.ValueOf(goxml.ChatTypeFirstCome),
		"ChatTypeFlowerOccupyBestFlowerbed":                  reflect.ValueOf(goxml.ChatTypeFlowerOccupyBestFlowerbed),
		"ChatTypeFlowerOccupyBestJungle":                     reflect.ValueOf(goxml.ChatTypeFlowerOccupyBestJungle),
		"ChatTypeFlowerOccupyByAlly":                         reflect.ValueOf(goxml.ChatTypeFlowerOccupyByAlly),
		"ChatTypeFlowerShareFlowerbed":                       reflect.ValueOf(goxml.ChatTypeFlowerShareFlowerbed),
		"ChatTypeGuild":                                      reflect.ValueOf(goxml.ChatTypeGuild),
		"ChatTypeGuildDungeonBoxOpenAmazing":                 reflect.ValueOf(goxml.ChatTypeGuildDungeonBoxOpenAmazing),
		"ChatTypeGuildDungeonChapterThrough":                 reflect.ValueOf(goxml.ChatTypeGuildDungeonChapterThrough),
		"ChatTypeGuildDungeonNewSeasonForArena":              reflect.ValueOf(goxml.ChatTypeGuildDungeonNewSeasonForArena),
		"ChatTypeGuildDungeonNewSeasonForGuild":              reflect.ValueOf(goxml.ChatTypeGuildDungeonNewSeasonForGuild),
		"ChatTypeGuildJoin":                                  reflect.ValueOf(goxml.ChatTypeGuildJoin),
		"ChatTypeGuildRecruit":                               reflect.ValueOf(goxml.ChatTypeGuildRecruit),
		"ChatTypeLinkSummonMulti":                            reflect.ValueOf(goxml.ChatTypeLinkSummonMulti),
		"ChatTypeLinkSummonSingle":                           reflect.ValueOf(goxml.ChatTypeLinkSummonSingle),
		"ChatTypeMedalLevelUp":                               reflect.ValueOf(goxml.ChatTypeMedalLevelUp),
		"ChatTypeMonthTasksPoints":                           reflect.ValueOf(goxml.ChatTypeMonthTasksPoints),
		"ChatTypePrivate":                                    reflect.ValueOf(goxml.ChatTypePrivate),
		"ChatTypeWorld":                                      reflect.ValueOf(goxml.ChatTypeWorld),
		"ChatWorldBossPartitionRankFirstChange":              reflect.ValueOf(goxml.ChatWorldBossPartitionRankFirstChange),
		"ChatWorldBossPartitionRankSettle":                   reflect.ValueOf(goxml.ChatWorldBossPartitionRankSettle),
		"ClientHandleTask":                                   reflect.ValueOf(goxml.ClientHandleTask),
		"ClientLanguage":                                     reflect.ValueOf(&goxml.ClientLanguage).Elem(),
		"CommonThreeOrangeEmblemStageID":                     reflect.ValueOf(goxml.CommonThreeOrangeEmblemStageID),
		"CommonThreeRedEmblemStageID":                        reflect.ValueOf(goxml.CommonThreeRedEmblemStageID),
		"CommonThreeSeasonOrangeEmblemStageID":               reflect.ValueOf(goxml.CommonThreeSeasonOrangeEmblemStageID),
		"CommonThreeSeasonRedEmblemStageID":                  reflect.ValueOf(goxml.CommonThreeSeasonRedEmblemStageID),
		"CompareEqual":                                       reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"CompareMoreThan":                                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CondAccumulateBeHurt":                               reflect.ValueOf(constant.MakeFromLiteral("81", token.INT, 0)),
		"CondAliveNum":                                       reflect.ValueOf(constant.MakeFromLiteral("74", token.INT, 0)),
		"CondAttackBenefitBuffCount":                         reflect.ValueOf(constant.MakeFromLiteral("37", token.INT, 0)),
		"CondAttackBuffState":                                reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"CondAttackBuffStateOrDefenseControled":              reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"CondAttackHasAddControlBuff":                        reflect.ValueOf(constant.MakeFromLiteral("27", token.INT, 0)),
		"CondAttackHasCrit":                                  reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"CondAttackHasKill":                                  reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"CondAttackHasNoCrit":                                reflect.ValueOf(constant.MakeFromLiteral("19", token.INT, 0)),
		"CondAttackHasNoCritAndOnlyOne":                      reflect.ValueOf(constant.MakeFromLiteral("18", token.INT, 0)),
		"CondAttackHasOverCure":                              reflect.ValueOf(constant.MakeFromLiteral("17", token.INT, 0)),
		"CondAttackHpLess":                                   reflect.ValueOf(constant.MakeFromLiteral("22", token.INT, 0)),
		"CondAttackHpMore":                                   reflect.ValueOf(constant.MakeFromLiteral("21", token.INT, 0)),
		"CondAttackHurtOrCureLessDefenseHp":                  reflect.ValueOf(constant.MakeFromLiteral("34", token.INT, 0)),
		"CondAttackHurtOrCureMoreDefenseHp":                  reflect.ValueOf(constant.MakeFromLiteral("35", token.INT, 0)),
		"CondAttackInOwnerTarget":                            reflect.ValueOf(constant.MakeFromLiteral("36", token.INT, 0)),
		"CondAttackIsControled":                              reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"CondAttackJob":                                      reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"CondAttackNoBenefitBuffCount":                       reflect.ValueOf(constant.MakeFromLiteral("38", token.INT, 0)),
		"CondAttackNoBuffState":                              reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"CondAttackRace":                                     reflect.ValueOf(constant.MakeFromLiteral("42", token.INT, 0)),
		"CondAttackTargetNumLess":                            reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"CondAttackTeamBenefitBuffCount":                     reflect.ValueOf(constant.MakeFromLiteral("47", token.INT, 0)),
		"CondAttackTeamLinkNum":                              reflect.ValueOf(constant.MakeFromLiteral("49", token.INT, 0)),
		"CondAttr":                                           reflect.ValueOf(constant.MakeFromLiteral("67", token.INT, 0)),
		"CondBuffActiveSkillHurtEffective":                   reflect.ValueOf(constant.MakeFromLiteral("79", token.INT, 0)),
		"CondBuffLayerCountLess":                             reflect.ValueOf(constant.MakeFromLiteral("77", token.INT, 0)),
		"CondBuffLayerCountMoreEqual":                        reflect.ValueOf(constant.MakeFromLiteral("54", token.INT, 0)),
		"CondBuffLayerRecord":                                reflect.ValueOf(constant.MakeFromLiteral("78", token.INT, 0)),
		"CondBuffTypeCountMoreEqual":                         reflect.ValueOf(constant.MakeFromLiteral("55", token.INT, 0)),
		"CondControlNumMore":                                 reflect.ValueOf(constant.MakeFromLiteral("25", token.INT, 0)),
		"CondCurRoundBigSkillCount":                          reflect.ValueOf(constant.MakeFromLiteral("64", token.INT, 0)),
		"CondCurrentRoundIsOddOrEven":                        reflect.ValueOf(constant.MakeFromLiteral("82", token.INT, 0)),
		"CondDefenseAttrLessOwner":                           reflect.ValueOf(constant.MakeFromLiteral("23", token.INT, 0)),
		"CondDefenseAttrMoreOwner":                           reflect.ValueOf(constant.MakeFromLiteral("24", token.INT, 0)),
		"CondDefenseBenefitBuffCount":                        reflect.ValueOf(constant.MakeFromLiteral("39", token.INT, 0)),
		"CondDefenseBuffState":                               reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"CondDefenseBuffStateOrControled":                    reflect.ValueOf(constant.MakeFromLiteral("13", token.INT, 0)),
		"CondDefenseCorruptionCountMore":                     reflect.ValueOf(constant.MakeFromLiteral("32", token.INT, 0)),
		"CondDefenseDefCompare":                              reflect.ValueOf(constant.MakeFromLiteral("41", token.INT, 0)),
		"CondDefenseHpLess":                                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"CondDefenseHpMore":                                  reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"CondDefenseInOwnerTarget":                           reflect.ValueOf(constant.MakeFromLiteral("31", token.INT, 0)),
		"CondDefenseIsControled":                             reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"CondDefenseJob":                                     reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"CondDefenseNoBenefitBuffCount":                      reflect.ValueOf(constant.MakeFromLiteral("40", token.INT, 0)),
		"CondDefenseNoBuffState":                             reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"CondDefenseRace":                                    reflect.ValueOf(constant.MakeFromLiteral("45", token.INT, 0)),
		"CondDefenseTeamLinkNum":                             reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"CondHasPsAttr":                                      reflect.ValueOf(constant.MakeFromLiteral("58", token.INT, 0)),
		"CondHaveEmptyPosOrMonsterCall":                      reflect.ValueOf(constant.MakeFromLiteral("73", token.INT, 0)),
		"CondHavePsID":                                       reflect.ValueOf(constant.MakeFromLiteral("70", token.INT, 0)),
		"CondHeroID":                                         reflect.ValueOf(constant.MakeFromLiteral("76", token.INT, 0)),
		"CondHeroSex":                                        reflect.ValueOf(constant.MakeFromLiteral("69", token.INT, 0)),
		"CondHeroStar":                                       reflect.ValueOf(constant.MakeFromLiteral("66", token.INT, 0)),
		"CondIsTargetExist":                                  reflect.ValueOf(constant.MakeFromLiteral("63", token.INT, 0)),
		"CondLeftMemAndNoMonsterCallHero":                    reflect.ValueOf(constant.MakeFromLiteral("72", token.INT, 0)),
		"CondLink":                                           reflect.ValueOf(constant.MakeFromLiteral("61", token.INT, 0)),
		"CondLinkTypeCount":                                  reflect.ValueOf(constant.MakeFromLiteral("68", token.INT, 0)),
		"CondMax":                                            reflect.ValueOf(constant.MakeFromLiteral("83", token.INT, 0)),
		"CondNoBuffID":                                       reflect.ValueOf(constant.MakeFromLiteral("75", token.INT, 0)),
		"CondNone":                                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"CondOneSkillAttackHasKill":                          reflect.ValueOf(constant.MakeFromLiteral("46", token.INT, 0)),
		"CondOwnerBuffState":                                 reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"CondOwnerCanResurr":                                 reflect.ValueOf(constant.MakeFromLiteral("26", token.INT, 0)),
		"CondOwnerDeathState":                                reflect.ValueOf(constant.MakeFromLiteral("59", token.INT, 0)),
		"CondOwnerHpHigher":                                  reflect.ValueOf(constant.MakeFromLiteral("44", token.INT, 0)),
		"CondOwnerHpLower":                                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CondOwnerIsBenefitBuffCount":                        reflect.ValueOf(constant.MakeFromLiteral("43", token.INT, 0)),
		"CondOwnerNoBuffState":                               reflect.ValueOf(constant.MakeFromLiteral("60", token.INT, 0)),
		"CondOwnerOpTeamLinkNum":                             reflect.ValueOf(constant.MakeFromLiteral("80", token.INT, 0)),
		"CondOwnerPos":                                       reflect.ValueOf(constant.MakeFromLiteral("71", token.INT, 0)),
		"CondOwnerTeamLinkNum":                               reflect.ValueOf(constant.MakeFromLiteral("56", token.INT, 0)),
		"CondResurrection":                                   reflect.ValueOf(constant.MakeFromLiteral("48", token.INT, 0)),
		"CondRoundGE":                                        reflect.ValueOf(constant.MakeFromLiteral("29", token.INT, 0)),
		"CondRoundLess":                                      reflect.ValueOf(constant.MakeFromLiteral("28", token.INT, 0)),
		"CondSameActionAddedBuffCount":                       reflect.ValueOf(constant.MakeFromLiteral("57", token.INT, 0)),
		"CondSeasonEnergy":                                   reflect.ValueOf(constant.MakeFromLiteral("62", token.INT, 0)),
		"CondSeasonEnergyExtra":                              reflect.ValueOf(constant.MakeFromLiteral("65", token.INT, 0)),
		"CondSkillGroupEqual":                                reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"CondTargetAttack":                                   reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"CondTargetDefense":                                  reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"CondTargetOpTeam":                                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"CondTargetSelfTeam":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"CondTeamAttrMoreEqual":                              reflect.ValueOf(constant.MakeFromLiteral("51", token.INT, 0)),
		"CondTeamDeathNumMoreEqual":                          reflect.ValueOf(constant.MakeFromLiteral("53", token.INT, 0)),
		"CondTeamResurrectionMoreEqual":                      reflect.ValueOf(constant.MakeFromLiteral("52", token.INT, 0)),
		"CondTriggerBuffIdCountMoreThan1":                    reflect.ValueOf(constant.MakeFromLiteral("33", token.INT, 0)),
		"ContractStageMaxCount":                              reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"DailyAttendanceEachGroupMaxDay":                     reflect.ValueOf(goxml.DailyAttendanceEachGroupMaxDay),
		"DailyAttendanceEachGroupMinDay":                     reflect.ValueOf(goxml.DailyAttendanceEachGroupMinDay),
		"DailySpecialScoreAwardCount":                        reflect.ValueOf(goxml.DailySpecialScoreAwardCount),
		"DailyTaskTypeID":                                    reflect.ValueOf(goxml.DailyTaskTypeID),
		"DaysAfterStartOfService":                            reflect.ValueOf(goxml.DaysAfterStartOfService),
		"DebutBagShopType":                                   reflect.ValueOf(constant.MakeFromLiteral("201", token.INT, 0)),
		"DebutHaveHall":                                      reflect.ValueOf(goxml.DebutHaveHall),
		"DebutNoHall":                                        reflect.ValueOf(goxml.DebutNoHall),
		"DebutSummonGroupID":                                 reflect.ValueOf(goxml.DebutSummonGroupID),
		"DefaultBaseId":                                      reflect.ValueOf(goxml.DefaultBaseId),
		"DisorderLandDropGroupNoSelect":                      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"DisorderLandDropGroupSelect":                        reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DisorderLandFormationPool":                          reflect.ValueOf(&goxml.DisorderLandFormationPool).Elem(),
		"DisorderLandHurdle1RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle1RankID),
		"DisorderLandHurdle2RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle2RankID),
		"DisorderLandHurdle3RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle3RankID),
		"DisorderLandHurdle4RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle4RankID),
		"DisorderLandHurdle5RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle5RankID),
		"DisorderLandHurdle6RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle6RankID),
		"DisorderLandHurdle7RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle7RankID),
		"DisorderLandHurdle8RankID":                          reflect.ValueOf(goxml.DisorderLandHurdle8RankID),
		"DispatchHeroConditionTypeLink":                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"DispatchHeroConditionTypeRace":                      reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DispatchLevelUpClear":                               reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"DispatchLevelUpKeep":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DispatchTaskRecordType":                             reflect.ValueOf(&goxml.DispatchTaskRecordType).Elem(),
		"DivineDemonActivityTypeCommonServer":                reflect.ValueOf(goxml.DivineDemonActivityTypeCommonServer),
		"DivineDemonActivityTypeCommonServerDebut":           reflect.ValueOf(goxml.DivineDemonActivityTypeCommonServerDebut),
		"DivineDemonActivityTypeNewServer":                   reflect.ValueOf(goxml.DivineDemonActivityTypeNewServer),
		"DivineDemonGradeNoUpRedHero":                        reflect.ValueOf(goxml.DivineDemonGradeNoUpRedHero),
		"DivineDemonGradeTypeOther":                          reflect.ValueOf(goxml.DivineDemonGradeTypeOther),
		"DivineDemonGradeUpRedHero":                          reflect.ValueOf(goxml.DivineDemonGradeUpRedHero),
		"DivineDemonMaxGuaranteeLen":                         reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"DivineDemonNewServerProtectionDay":                  reflect.ValueOf(goxml.DivineDemonNewServerProtectionDay),
		"DivineDemonOrangeFragmentGuaranteeIndex":            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"DivineDemonOrangeGuaranteeIndex":                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"DivineDemonPurpleGuaranteeIndex":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DivineDemonRedGuaranteeIndex":                       reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"DivineDemonSCDefault":                               reflect.ValueOf(goxml.DivineDemonSCDefault),
		"DivineDemonSCOne":                                   reflect.ValueOf(goxml.DivineDemonSCOne),
		"DivineDemonSCStatus":                                reflect.ValueOf(&goxml.DivineDemonSCStatus).Elem(),
		"DivineDemonSCThree":                                 reflect.ValueOf(goxml.DivineDemonSCThree),
		"DivineDemonSCTwo":                                   reflect.ValueOf(goxml.DivineDemonSCTwo),
		"DivineDemonSummonNoRedCard":                         reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"DivineDemonSummonRedCard":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DivineDemonSummonUpRedCard":                         reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"DivineDemonTypeDebut":                               reflect.ValueOf(goxml.DivineDemonTypeDebut),
		"DivineDemonTypeNoDebut":                             reflect.ValueOf(goxml.DivineDemonTypeNoDebut),
		"DivineDemonUniqIDMax":                               reflect.ValueOf(goxml.DivineDemonUniqIDMax),
		"DivineDemonUpRedCardCount":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"DivineHeroStageID":                                  reflect.ValueOf(goxml.DivineHeroStageID),
		"DivineSeasonHeroStageID":                            reflect.ValueOf(goxml.DivineSeasonHeroStageID),
		"DivineSummonStageID":                                reflect.ValueOf(goxml.DivineSummonStageID),
		"DragonContinuousFailedCountMatchRobot":              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DragonFightStageID":                                 reflect.ValueOf(goxml.DragonFightStageID),
		"DragonShowPosInit":                                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DropActivityFuncCount":                              reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"DropActivitySubFunc":                                reflect.ValueOf(&goxml.DropActivitySubFunc).Elem(),
		"DropExclusiveRand":                                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"DropIndependentRand":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"DropTypeRandEnum":                                   reflect.ValueOf(&goxml.DropTypeRandEnum).Elem(),
		"DungeonAwardRate":                                   reflect.ValueOf(goxml.DungeonAwardRate),
		"DungeonCalcRate":                                    reflect.ValueOf(goxml.DungeonCalcRate),
		"DungeonRankId":                                      reflect.ValueOf(goxml.DungeonRankId),
		"EffectAddArtifactEnergy":                            reflect.ValueOf(goxml.EffectAddArtifactEnergy),
		"EffectAddBuff":                                      reflect.ValueOf(goxml.EffectAddBuff),
		"EffectAddBuffByBuffLayer":                           reflect.ValueOf(goxml.EffectAddBuffByBuffLayer),
		"EffectAddBuffByBuffType":                            reflect.ValueOf(goxml.EffectAddBuffByBuffType),
		"EffectAddBuffByEffTarget":                           reflect.ValueOf(goxml.EffectAddBuffByEffTarget),
		"EffectAddPS":                                        reflect.ValueOf(goxml.EffectAddPS),
		"EffectAddSeasonLinkEnergy":                          reflect.ValueOf(goxml.EffectAddSeasonLinkEnergy),
		"EffectAddShield":                                    reflect.ValueOf(goxml.EffectAddShield),
		"EffectAddShieldByInherit":                           reflect.ValueOf(goxml.EffectAddShieldByInherit),
		"EffectAddSkillAttackCount":                          reflect.ValueOf(goxml.EffectAddSkillAttackCount),
		"EffectAliveNum":                                     reflect.ValueOf(goxml.EffectAliveNum),
		"EffectBindSkill":                                    reflect.ValueOf(goxml.EffectBindSkill),
		"EffectBless":                                        reflect.ValueOf(goxml.EffectBless),
		"EffectBuffLayer":                                    reflect.ValueOf(goxml.EffectBuffLayer),
		"EffectBuffType":                                     reflect.ValueOf(goxml.EffectBuffType),
		"EffectCall":                                         reflect.ValueOf(goxml.EffectCall),
		"EffectCallDepartedSpirit":                           reflect.ValueOf(goxml.EffectCallDepartedSpirit),
		"EffectCallList":                                     reflect.ValueOf(&goxml.EffectCallList).Elem(),
		"EffectChangeArtifactCD":                             reflect.ValueOf(goxml.EffectChangeArtifactCD),
		"EffectChangeAttr":                                   reflect.ValueOf(goxml.EffectChangeAttr),
		"EffectChangeAttrWithLimit":                          reflect.ValueOf(goxml.EffectChangeAttrWithLimit),
		"EffectChangeForeveAttr":                             reflect.ValueOf(goxml.EffectChangeForeveAttr),
		"EffectChangeForeveAttrWithLimit":                    reflect.ValueOf(goxml.EffectChangeForeveAttrWithLimit),
		"EffectChangeForeveAttrWithLimit2":                   reflect.ValueOf(goxml.EffectChangeForeveAttrWithLimit2),
		"EffectChangeForevePsAttr":                           reflect.ValueOf(goxml.EffectChangeForevePsAttr),
		"EffectChangeHaloAttr":                               reflect.ValueOf(goxml.EffectChangeHaloAttr),
		"EffectChangeHaloPsAttr":                             reflect.ValueOf(goxml.EffectChangeHaloPsAttr),
		"EffectChangePS":                                     reflect.ValueOf(goxml.EffectChangePS),
		"EffectChangePsAttr":                                 reflect.ValueOf(goxml.EffectChangePsAttr),
		"EffectChangeSkillAttackDefenser":                    reflect.ValueOf(goxml.EffectChangeSkillAttackDefenser),
		"EffectCleanBuffAddAttr":                             reflect.ValueOf(goxml.EffectCleanBuffAddAttr),
		"EffectComboBySkill":                                 reflect.ValueOf(goxml.EffectComboBySkill),
		"EffectComboNormalSkill":                             reflect.ValueOf(goxml.EffectComboNormalSkill),
		"EffectComboNormalUnion":                             reflect.ValueOf(goxml.EffectComboNormalUnion),
		"EffectComboSkill":                                   reflect.ValueOf(goxml.EffectComboSkill),
		"EffectCopyBuff":                                     reflect.ValueOf(goxml.EffectCopyBuff),
		"EffectCopyBuffWhenSwapPos":                          reflect.ValueOf(goxml.EffectCopyBuffWhenSwapPos),
		"EffectCure":                                         reflect.ValueOf(goxml.EffectCure),
		"EffectDecArtifactEnergy":                            reflect.ValueOf(goxml.EffectDecArtifactEnergy),
		"EffectDecSeasonLinkEnergy":                          reflect.ValueOf(goxml.EffectDecSeasonLinkEnergy),
		"EffectDevour":                                       reflect.ValueOf(goxml.EffectDevour),
		"EffectDoKill":                                       reflect.ValueOf(goxml.EffectDoKill),
		"EffectDuel":                                         reflect.ValueOf(goxml.EffectDuel),
		"EffectEat":                                          reflect.ValueOf(goxml.EffectEat),
		"EffectExplosiveShield":                              reflect.ValueOf(goxml.EffectExplosiveShield),
		"EffectFightBack":                                    reflect.ValueOf(goxml.EffectFightBack),
		"EffectFullHurt":                                     reflect.ValueOf(goxml.EffectFullHurt),
		"EffectGuard":                                        reflect.ValueOf(goxml.EffectGuard),
		"EffectHasBuff":                                      reflect.ValueOf(goxml.EffectHasBuff),
		"EffectHpHigherAttr":                                 reflect.ValueOf(goxml.EffectHpHigherAttr),
		"EffectHpLowerAttr":                                  reflect.ValueOf(goxml.EffectHpLowerAttr),
		"EffectHurt":                                         reflect.ValueOf(goxml.EffectHurt),
		"EffectHurt2Cure":                                    reflect.ValueOf(goxml.EffectHurt2Cure),
		"EffectHurtSelf":                                     reflect.ValueOf(goxml.EffectHurtSelf),
		"EffectHurtSelfDamageToTrigger":                      reflect.ValueOf(goxml.EffectHurtSelfDamageToTrigger),
		"EffectKillNotDead":                                  reflect.ValueOf(goxml.EffectKillNotDead),
		"EffectMax":                                          reflect.ValueOf(goxml.EffectMax),
		"EffectNewSkill":                                     reflect.ValueOf(goxml.EffectNewSkill),
		"EffectNextPsAttr":                                   reflect.ValueOf(goxml.EffectNextPsAttr),
		"EffectNextRoundSkill":                               reflect.ValueOf(goxml.EffectNextRoundSkill),
		"EffectRemoveBuff":                                   reflect.ValueOf(goxml.EffectRemoveBuff),
		"EffectReplaceSkill":                                 reflect.ValueOf(goxml.EffectReplaceSkill),
		"EffectResurrection":                                 reflect.ValueOf(goxml.EffectResurrection),
		"EffectReverseTargetsActOrder":                       reflect.ValueOf(goxml.EffectReverseTargetsActOrder),
		"EffectSettleBuff":                                   reflect.ValueOf(goxml.EffectSettleBuff),
		"EffectSplashHurt":                                   reflect.ValueOf(goxml.EffectSplashHurt),
		"EffectStealShield":                                  reflect.ValueOf(goxml.EffectStealShield),
		"EffectStealSoul":                                    reflect.ValueOf(goxml.EffectStealSoul),
		"EffectSwapPos":                                      reflect.ValueOf(goxml.EffectSwapPos),
		"EffectTimeAtOnce":                                   reflect.ValueOf(goxml.EffectTimeAtOnce),
		"EffectTimeLater":                                    reflect.ValueOf(goxml.EffectTimeLater),
		"EffectTransferBuff":                                 reflect.ValueOf(goxml.EffectTransferBuff),
		"EmblemAttrCount":                                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"EmblemBlessingResCostCount":                         reflect.ValueOf(goxml.EmblemBlessingResCostCount),
		"EmblemCustomizeIsHeroGroupBase":                     reflect.ValueOf(goxml.EmblemCustomizeIsHeroGroupBase),
		"EmblemCustomizeLegalPos":                            reflect.ValueOf(&goxml.EmblemCustomizeLegalPos).Elem(),
		"EmblemCustomizeLegalSkill":                          reflect.ValueOf(&goxml.EmblemCustomizeLegalSkill).Elem(),
		"EmblemCustomizeLegalType":                           reflect.ValueOf(&goxml.EmblemCustomizeLegalType).Elem(),
		"EmblemCustomizePick":                                reflect.ValueOf(constant.MakeFromLiteral("99", token.INT, 0)),
		"EmblemCustomizePosBase":                             reflect.ValueOf(goxml.EmblemCustomizePosBase),
		"EmblemCustomizeRaceBase":                            reflect.ValueOf(goxml.EmblemCustomizeRaceBase),
		"EmblemCustomizeRandom":                              reflect.ValueOf(constant.MakeFromLiteral("98", token.INT, 0)),
		"EmblemCustomizeRareBase":                            reflect.ValueOf(goxml.EmblemCustomizeRareBase),
		"EmblemCustomizeTypeBase":                            reflect.ValueOf(goxml.EmblemCustomizeTypeBase),
		"EmblemExclusive1":                                   reflect.ValueOf(goxml.EmblemExclusive1),
		"EmblemExclusive2":                                   reflect.ValueOf(goxml.EmblemExclusive2),
		"EmblemExclusive3":                                   reflect.ValueOf(goxml.EmblemExclusive3),
		"EmblemExclusive4":                                   reflect.ValueOf(goxml.EmblemExclusive4),
		"EmblemExclusiveNumFour":                             reflect.ValueOf(goxml.EmblemExclusiveNumFour),
		"EmblemExclusiveNumOne":                              reflect.ValueOf(goxml.EmblemExclusiveNumOne),
		"EmblemExclusiveNumThree":                            reflect.ValueOf(goxml.EmblemExclusiveNumThree),
		"EmblemExclusiveNumTwo":                              reflect.ValueOf(goxml.EmblemExclusiveNumTwo),
		"EmblemExclusiveSkill1":                              reflect.ValueOf(goxml.EmblemExclusiveSkill1),
		"EmblemExclusiveSkill2":                              reflect.ValueOf(goxml.EmblemExclusiveSkill2),
		"EmblemExclusiveSkill3":                              reflect.ValueOf(goxml.EmblemExclusiveSkill3),
		"EmblemExistSkill":                                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"EmblemFourSuit":                                     reflect.ValueOf(goxml.EmblemFourSuit),
		"EmblemLegalPos":                                     reflect.ValueOf(&goxml.EmblemLegalPos).Elem(),
		"EmblemLegalSkill":                                   reflect.ValueOf(&goxml.EmblemLegalSkill).Elem(),
		"EmblemLevelupResCostCount":                          reflect.ValueOf(goxml.EmblemLevelupResCostCount),
		"EmblemLink":                                         reflect.ValueOf(goxml.EmblemLink),
		"EmblemNoExistSkill":                                 reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"EmblemPasSkill1":                                    reflect.ValueOf(goxml.EmblemPasSkill1),
		"EmblemPasSkill2":                                    reflect.ValueOf(goxml.EmblemPasSkill2),
		"EmblemPasSkill3":                                    reflect.ValueOf(goxml.EmblemPasSkill3),
		"EmblemPos1":                                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"EmblemPos2":                                         reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"EmblemPos3":                                         reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"EmblemPos4":                                         reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"EmblemPosMax":                                       reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"EmblemPosNone":                                      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"EmblemPosStart":                                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"EmblemRareOrange":                                   reflect.ValueOf(goxml.EmblemRareOrange),
		"EmblemRareRed":                                      reflect.ValueOf(goxml.EmblemRareRed),
		"EmblemSkillTypeAdditive":                            reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"EmblemSkillTypeNone":                                reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"EmblemSkillTypeNormal":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"EmblemTwoSuit":                                      reflect.ValueOf(goxml.EmblemTwoSuit),
		"EquipmentEnchantRandomLen":                          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"EveDead":                                            reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"EveDrainHp":                                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"EveHpLess":                                          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"EveMax":                                             reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"EveNone":                                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"EveOneAttack":                                       reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"EveOneAttackDef":                                    reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"EveOneEffect":                                       reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"EveOneEffectDef":                                    reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"EveRebounds":                                        reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"EveSkillEnd":                                        reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"EveSkillEndCombo":                                   reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"EveSkillEndComboNormal":                             reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"Event_ActionEnd":                                    reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"Event_ActionPrepare":                                reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"Event_ActiveHurtedBeforeHurtValueFix":               reflect.ValueOf(constant.MakeFromLiteral("49", token.INT, 0)),
		"Event_BattleBeforeInit":                             reflect.ValueOf(constant.MakeFromLiteral("26", token.INT, 0)),
		"Event_BattleBeforePrepare":                          reflect.ValueOf(constant.MakeFromLiteral("25", token.INT, 0)),
		"Event_BattleInit":                                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"Event_BattlePrepare":                                reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"Event_BeCured":                                      reflect.ValueOf(constant.MakeFromLiteral("27", token.INT, 0)),
		"Event_BeHurtedBeforeCalDam":                         reflect.ValueOf(constant.MakeFromLiteral("22", token.INT, 0)),
		"Event_BeHurtedEnd":                                  reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"Event_BeHurtedPrepare":                              reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"Event_BeforeBeDuel":                                 reflect.ValueOf(constant.MakeFromLiteral("46", token.INT, 0)),
		"Event_BeforeStealHero":                              reflect.ValueOf(constant.MakeFromLiteral("43", token.INT, 0)),
		"Event_BeforeToDuel":                                 reflect.ValueOf(constant.MakeFromLiteral("48", token.INT, 0)),
		"Event_Bless":                                        reflect.ValueOf(constant.MakeFromLiteral("34", token.INT, 0)),
		"Event_BloodSuck":                                    reflect.ValueOf(constant.MakeFromLiteral("31", token.INT, 0)),
		"Event_BuffBeAdd":                                    reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"Event_BuffBeRemove":                                 reflect.ValueOf(constant.MakeFromLiteral("17", token.INT, 0)),
		"Event_BuffMianyi":                                   reflect.ValueOf(constant.MakeFromLiteral("47", token.INT, 0)),
		"Event_BuffReflected":                                reflect.ValueOf(constant.MakeFromLiteral("44", token.INT, 0)),
		"Event_BuffSettle":                                   reflect.ValueOf(constant.MakeFromLiteral("32", token.INT, 0)),
		"Event_BuffToAdd":                                    reflect.ValueOf(constant.MakeFromLiteral("24", token.INT, 0)),
		"Event_Call":                                         reflect.ValueOf(constant.MakeFromLiteral("39", token.INT, 0)),
		"Event_ChooseSkill":                                  reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"Event_Combo":                                        reflect.ValueOf(constant.MakeFromLiteral("29", token.INT, 0)),
		"Event_Dead":                                         reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"Event_DeadHurt":                                     reflect.ValueOf(constant.MakeFromLiteral("19", token.INT, 0)),
		"Event_DecHp":                                        reflect.ValueOf(constant.MakeFromLiteral("18", token.INT, 0)),
		"Event_Devour":                                       reflect.ValueOf(constant.MakeFromLiteral("38", token.INT, 0)),
		"Event_DoHurt":                                       reflect.ValueOf(constant.MakeFromLiteral("23", token.INT, 0)),
		"Event_Dodge":                                        reflect.ValueOf(constant.MakeFromLiteral("28", token.INT, 0)),
		"Event_EatHero":                                      reflect.ValueOf(constant.MakeFromLiteral("42", token.INT, 0)),
		"Event_HurtEnd":                                      reflect.ValueOf(constant.MakeFromLiteral("13", token.INT, 0)),
		"Event_HurtPrepare":                                  reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"Event_Max":                                          reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"Event_None":                                         reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"Event_OneEffectEnd":                                 reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"Event_OneEffectPrepare":                             reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"Event_OneRoundPreEnd":                               reflect.ValueOf(constant.MakeFromLiteral("35", token.INT, 0)),
		"Event_OneRoundPrepare":                              reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"Event_OneRoundPrepareEnd":                           reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"Event_OneSkillEnd":                                  reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"Event_OneSkillPrepare":                              reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"Event_OverCure":                                     reflect.ValueOf(constant.MakeFromLiteral("33", token.INT, 0)),
		"Event_PurifyOrDispelEnd":                            reflect.ValueOf(constant.MakeFromLiteral("37", token.INT, 0)),
		"Event_Revive":                                       reflect.ValueOf(constant.MakeFromLiteral("21", token.INT, 0)),
		"Event_SeasonLinkEnergyReach":                        reflect.ValueOf(constant.MakeFromLiteral("36", token.INT, 0)),
		"Event_SeasonLinkNegEnergyReach":                     reflect.ValueOf(constant.MakeFromLiteral("45", token.INT, 0)),
		"Event_StealHero":                                    reflect.ValueOf(constant.MakeFromLiteral("41", token.INT, 0)),
		"Event_SwapPos":                                      reflect.ValueOf(constant.MakeFromLiteral("40", token.INT, 0)),
		"Event_Union":                                        reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"ExpectRandomDrop":                                   reflect.ValueOf(goxml.ExpectRandomDrop),
		"ExpectSureDrop":                                     reflect.ValueOf(goxml.ExpectSureDrop),
		"FirstGiftLoginMax":                                  reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"FirstGiftTypeMultiReward":                           reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FirstGiftTypeSingleReward":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FixAttr":                                            reflect.ValueOf(goxml.FixAttr),
		"FixCritDamRateHigh":                                 reflect.ValueOf(goxml.FixCritDamRateHigh),
		"FixCritDamRateLow":                                  reflect.ValueOf(goxml.FixCritDamRateLow),
		"FixNegativeNumAttr":                                 reflect.ValueOf(goxml.FixNegativeNumAttr),
		"FlowerAddItem1":                                     reflect.ValueOf(goxml.FlowerAddItem1),
		"FlowerAddItem2":                                     reflect.ValueOf(goxml.FlowerAddItem2),
		"FlowerAddItem3":                                     reflect.ValueOf(goxml.FlowerAddItem3),
		"FlowerAddItem4":                                     reflect.ValueOf(goxml.FlowerAddItem4),
		"FlowerDefaultFirstLv":                               reflect.ValueOf(goxml.FlowerDefaultFirstLv),
		"FlowerGuideStep":                                    reflect.ValueOf(goxml.FlowerGuideStep),
		"FlowerGuildBuffFive":                                reflect.ValueOf(goxml.FlowerGuildBuffFive),
		"FlowerGuildBuffFour":                                reflect.ValueOf(goxml.FlowerGuildBuffFour),
		"FlowerGuildBuffMinAllyCountRequest":                 reflect.ValueOf(goxml.FlowerGuildBuffMinAllyCountRequest),
		"FlowerGuildBuffThree":                               reflect.ValueOf(goxml.FlowerGuildBuffThree),
		"FlowerLogMaxNum":                                    reflect.ValueOf(goxml.FlowerLogMaxNum),
		"FlowerLootResColNum":                                reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FlowerLvUpTypeBeatGuard":                            reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FlowerLvUpTypeCostExp":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FlowerPlantRareCount":                               reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"FlowerRobotPlantRare":                               reflect.ValueOf(goxml.FlowerRobotPlantRare),
		"FlowerbedPosFifth":                                  reflect.ValueOf(goxml.FlowerbedPosFifth),
		"FlowerbedPosFirst":                                  reflect.ValueOf(goxml.FlowerbedPosFirst),
		"FlowerbedPosFourth":                                 reflect.ValueOf(goxml.FlowerbedPosFourth),
		"FlowerbedPosMax":                                    reflect.ValueOf(goxml.FlowerbedPosMax),
		"FlowerbedPosMin":                                    reflect.ValueOf(goxml.FlowerbedPosMin),
		"FlowerbedPosSecond":                                 reflect.ValueOf(goxml.FlowerbedPosSecond),
		"FlowerbedPosThird":                                  reflect.ValueOf(goxml.FlowerbedPosThird),
		"FlowerbedPosTotalCount":                             reflect.ValueOf(goxml.FlowerbedPosTotalCount),
		"FormationLockType1":                                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FormationLockType2":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"FormationOneTeam":                                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"FormationTeamOneIndex":                              reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"FormationTwoTeam":                                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"Friday":                                             reflect.ValueOf(goxml.Friday),
		"FriendshipSummon":                                   reflect.ValueOf(goxml.FriendshipSummon),
		"GSTChallengeBuffAddPct":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GSTChallengeBuffRandomSkill":                        reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GSTChallengeBuffRebirth":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GSTChallengeScoreRank":                              reflect.ValueOf(goxml.GSTChallengeScoreRank),
		"GSTChallengeTeamExpire":                             reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GSTChallengeTeamLose":                               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GSTChallengeTeamNotFight":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GSTChallengeTeamNotMatch":                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GSTChallengeTeamWin":                                reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GSTChallengeTotalScoreRank":                         reflect.ValueOf(goxml.GSTChallengeTotalScoreRank),
		"GemAttrTypeAdvanced":                                reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GemAttrTypeBasic":                                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GemBagMaxLimitId":                                   reflect.ValueOf(goxml.GemBagMaxLimitId),
		"GemComposeCostNum":                                  reflect.ValueOf(goxml.GemComposeCostNum),
		"GemConvert":                                         reflect.ValueOf(goxml.GemConvert),
		"GemConvertAndSave":                                  reflect.ValueOf(goxml.GemConvertAndSave),
		"GemConvertCleanTmpAttr":                             reflect.ValueOf(goxml.GemConvertCleanTmpAttr),
		"GemLeftSlotUnlockID":                                reflect.ValueOf(goxml.GemLeftSlotUnlockID),
		"GemLink":                                            reflect.ValueOf(goxml.GemLink),
		"GemPasSkill1":                                       reflect.ValueOf(goxml.GemPasSkill1),
		"GemPasSkill2":                                       reflect.ValueOf(goxml.GemPasSkill2),
		"GemRightSlotUnlockID":                               reflect.ValueOf(goxml.GemRightSlotUnlockID),
		"GenAttrTypeAndValue":                                reflect.ValueOf(goxml.GenAttrTypeAndValue),
		"GenGstLogID":                                        reflect.ValueOf(goxml.GenGstLogID),
		"GenHeroRes":                                         reflect.ValueOf(goxml.GenHeroRes),
		"GenSimpleResource":                                  reflect.ValueOf(goxml.GenSimpleResource),
		"GenTargetAttr":                                      reflect.ValueOf(goxml.GenTargetAttr),
		"GetAttrAbsType":                                     reflect.ValueOf(goxml.GetAttrAbsType),
		"GetAttrChangeFlagGroupAndID":                        reflect.ValueOf(goxml.GetAttrChangeFlagGroupAndID),
		"GetAttrMerged":                                      reflect.ValueOf(goxml.GetAttrMerged),
		"GetAttrPctType":                                     reflect.ValueOf(goxml.GetAttrPctType),
		"GetAttrTypeToAbsAttrID":                             reflect.ValueOf(goxml.GetAttrTypeToAbsAttrID),
		"GetAttrTypeToIndependentPct":                        reflect.ValueOf(goxml.GetAttrTypeToIndependentPct),
		"GetAttrTypeToPct":                                   reflect.ValueOf(goxml.GetAttrTypeToPct),
		"GetCurrentSeasonID":                                 reflect.ValueOf(goxml.GetCurrentSeasonID),
		"GetData":                                            reflect.ValueOf(goxml.GetData),
		"GetExpectDropAwards":                                reflect.ValueOf(goxml.GetExpectDropAwards),
		"GetFirstDateOfMonth":                                reflect.ValueOf(goxml.GetFirstDateOfMonth),
		"GetGuildDungeonCurrentRound":                        reflect.ValueOf(goxml.GetGuildDungeonCurrentRound),
		"GetGuildDungeonResetDayZero":                        reflect.ValueOf(goxml.GetGuildDungeonResetDayZero),
		"GetGuildDungeonSeasonCloseTime":                     reflect.ValueOf(goxml.GetGuildDungeonSeasonCloseTime),
		"GetGuildDungeonSeasonClosing":                       reflect.ValueOf(goxml.GetGuildDungeonSeasonClosing),
		"GetGuildDungeonSeasonResetEndTime":                  reflect.ValueOf(goxml.GetGuildDungeonSeasonResetEndTime),
		"GetGuildDungeonSeasonResetTime":                     reflect.ValueOf(goxml.GetGuildDungeonSeasonResetTime),
		"GetGuildDungeonSeasonResetting":                     reflect.ValueOf(goxml.GetGuildDungeonSeasonResetting),
		"GetGuildDungeonWeeklyCloseTime":                     reflect.ValueOf(goxml.GetGuildDungeonWeeklyCloseTime),
		"GetGuildDungeonWeeklyClosing":                       reflect.ValueOf(goxml.GetGuildDungeonWeeklyClosing),
		"GetGuildDungeonWeeklyResetEndTime":                  reflect.ValueOf(goxml.GetGuildDungeonWeeklyResetEndTime),
		"GetGuildDungeonWeeklyResetTime":                     reflect.ValueOf(goxml.GetGuildDungeonWeeklyResetTime),
		"GetGuildDungeonWeeklyResetting":                     reflect.ValueOf(goxml.GetGuildDungeonWeeklyResetting),
		"GetHeroAttrCalType":                                 reflect.ValueOf(goxml.GetHeroAttrCalType),
		"GetHeroRaceType":                                    reflect.ValueOf(goxml.GetHeroRaceType),
		"GetLastDateOfLastMonth":                             reflect.ValueOf(goxml.GetLastDateOfLastMonth),
		"GetLastDateOfMonth":                                 reflect.ValueOf(goxml.GetLastDateOfMonth),
		"GetLastGuildDungeonSeasonResetTime":                 reflect.ValueOf(goxml.GetLastGuildDungeonSeasonResetTime),
		"GetLogFormatSeasonId":                               reflect.ValueOf(goxml.GetLogFormatSeasonId),
		"GetMirageCopyIDByRankID":                            reflect.ValueOf(goxml.GetMirageCopyIDByRankID),
		"GetPeakFirstRound":                                  reflect.ValueOf(goxml.GetPeakFirstRound),
		"GetPeakLastPhase":                                   reflect.ValueOf(goxml.GetPeakLastPhase),
		"GetPeakLastRound":                                   reflect.ValueOf(goxml.GetPeakLastRound),
		"GetPeakPeriodByRound":                               reflect.ValueOf(goxml.GetPeakPeriodByRound),
		"GetPeakRoundsByPeriod":                              reflect.ValueOf(goxml.GetPeakRoundsByPeriod),
		"GetPeakTeamNum":                                     reflect.ValueOf(goxml.GetPeakTeamNum),
		"GetRealDropAwards":                                  reflect.ValueOf(goxml.GetRealDropAwards),
		"GetSeasonLastBeginResetTime":                        reflect.ValueOf(goxml.GetSeasonLastBeginResetTime),
		"GetTalentTreeHotNextRefreshTm":                      reflect.ValueOf(goxml.GetTalentTreeHotNextRefreshTm),
		"GetTalentTreeHotNextSyncTm":                         reflect.ValueOf(goxml.GetTalentTreeHotNextSyncTm),
		"GetTowerSeasonMonthlyCloseTime":                     reflect.ValueOf(goxml.GetTowerSeasonMonthlyCloseTime),
		"GetTowerSeasonMonthlyEndTime":                       reflect.ValueOf(goxml.GetTowerSeasonMonthlyEndTime),
		"GetTowerSeasonMonthlyLastCloseTime":                 reflect.ValueOf(goxml.GetTowerSeasonMonthlyLastCloseTime),
		"GetTowerSeasonMonthlyNextCloseTime":                 reflect.ValueOf(goxml.GetTowerSeasonMonthlyNextCloseTime),
		"GetTowerSeasonMonthlyResetting":                     reflect.ValueOf(goxml.GetTowerSeasonMonthlyResetting),
		"GetTowerSeasonNowRoundResetEndTime":                 reflect.ValueOf(goxml.GetTowerSeasonNowRoundResetEndTime),
		"GetTowerSeasonRoundLastBeginResetTime":              reflect.ValueOf(goxml.GetTowerSeasonRoundLastBeginResetTime),
		"GetTowerSeasonRoundNextBeginResetTime":              reflect.ValueOf(goxml.GetTowerSeasonRoundNextBeginResetTime),
		"GetTowerSeasonRoundResetStartTime":                  reflect.ValueOf(goxml.GetTowerSeasonRoundResetStartTime),
		"GetTowerSeasonRoundResetting":                       reflect.ValueOf(goxml.GetTowerSeasonRoundResetting),
		"GetUint32BitPositions":                              reflect.ValueOf(goxml.GetUint32BitPositions),
		"GetWrestleDailyResetEndTime":                        reflect.ValueOf(goxml.GetWrestleDailyResetEndTime),
		"GetWrestleDailyResetTime":                           reflect.ValueOf(goxml.GetWrestleDailyResetTime),
		"GetWrestlePartitionPrevResetTime":                   reflect.ValueOf(goxml.GetWrestlePartitionPrevResetTime),
		"GetWrestlePartitionResetEndTime":                    reflect.ValueOf(goxml.GetWrestlePartitionResetEndTime),
		"GetWrestlePartitionResetTime":                       reflect.ValueOf(goxml.GetWrestlePartitionResetTime),
		"GetWrestleSeasonDuration":                           reflect.ValueOf(goxml.GetWrestleSeasonDuration),
		"GetWrestleSeasonResetTime":                          reflect.ValueOf(goxml.GetWrestleSeasonResetTime),
		"GodPresentGroupFirst":                               reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GodPresentGroupSecond":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GodPresentGroupThird":                               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GodPresentSummonCountPerTime":                       reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"GoddessAddEXPFeed":                                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GoddessAddEXPTouch":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GoddessCollectionAddTypeFixedValue":                 reflect.ValueOf(goxml.GoddessCollectionAddTypeFixedValue),
		"GoddessCollectionAddTypePct":                        reflect.ValueOf(goxml.GoddessCollectionAddTypePct),
		"GoddessCollectionBoxDaily":                          reflect.ValueOf(goxml.GoddessCollectionBoxDaily),
		"GoddessCollectionBoxWeekly":                         reflect.ValueOf(goxml.GoddessCollectionBoxWeekly),
		"GoddessCollectionTypeBox":                           reflect.ValueOf(goxml.GoddessCollectionTypeBox),
		"GoddessCollectionTypeOnhook":                        reflect.ValueOf(goxml.GoddessCollectionTypeOnhook),
		"GoddessContractInitLevel":                           reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GoddessInitLevel":                                   reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"GrpcShopID":                                         reflect.ValueOf(constant.MakeFromLiteral("102", token.INT, 0)),
		"GstGroupRank2NumberType":                            reflect.ValueOf(&goxml.GstGroupRank2NumberType).Elem(),
		"GstGroupUsersContributionRank":                      reflect.ValueOf(goxml.GstGroupUsersContributionRank),
		"GstGroupUsersKillRank":                              reflect.ValueOf(goxml.GstGroupUsersKillRank),
		"GstGroupUsersTripleKillRank":                        reflect.ValueOf(goxml.GstGroupUsersTripleKillRank),
		"GstGuildBuildIron":                                  reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GstGuildBuildMain":                                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GstGuildBuildStone":                                 reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GstGuildBuildTrees":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildActivityRankID":                                reflect.ValueOf(goxml.GuildActivityRankID),
		"GuildDivisionRankID":                                reflect.ValueOf(goxml.GuildDivisionRankID),
		"GuildDungeonChapterRankId":                          reflect.ValueOf(goxml.GuildDungeonChapterRankId),
		"GuildDungeonRecvBoxMessageBroadCastRare":            reflect.ValueOf(goxml.GuildDungeonRecvBoxMessageBroadCastRare),
		"GuildDungeonResetting":                              reflect.ValueOf(goxml.GuildDungeonResetting),
		"GuildDungeonStageID":                                reflect.ValueOf(goxml.GuildDungeonStageID),
		"GuildDungeonStrategyAddChallengeTimes":              reflect.ValueOf(goxml.GuildDungeonStrategyAddChallengeTimes),
		"GuildDungeonStrategyBossRestoreHpPct":               reflect.ValueOf(goxml.GuildDungeonStrategyBossRestoreHpPct),
		"GuildDungeonStrategyDamageAddPct":                   reflect.ValueOf(goxml.GuildDungeonStrategyDamageAddPct),
		"GuildDungeonStrategyForMyGuild":                     reflect.ValueOf(&goxml.GuildDungeonStrategyForMyGuild).Elem(),
		"GuildDungeonStrategyForOtherGuild":                  reflect.ValueOf(&goxml.GuildDungeonStrategyForOtherGuild).Elem(),
		"GuildDungeonStrategySeasonReset":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildDungeonStrategyThroughChapterGet":              reflect.ValueOf(goxml.GuildDungeonStrategyThroughChapterGet),
		"GuildDungeonStrategyWeekResetGet":                   reflect.ValueOf(goxml.GuildDungeonStrategyWeekResetGet),
		"GuildDungeonStrategyWeeklyReset":                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildDungeonUserDamageRankId":                       reflect.ValueOf(goxml.GuildDungeonUserDamageRankId),
		"GuildJoinClose":                                     reflect.ValueOf(goxml.GuildJoinClose),
		"GuildJoinNeedApply":                                 reflect.ValueOf(goxml.GuildJoinNeedApply),
		"GuildJoinOpen":                                      reflect.ValueOf(goxml.GuildJoinOpen),
		"GuildLevelRankID":                                   reflect.ValueOf(goxml.GuildLevelRankID),
		"GuildMedalActivityTypePeakRank":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildMedalActivityTypeSeasonArenaDivision":          reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"GuildMedalActivityTypeSeasonArenaRank":              reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"GuildMedalActivityTypeTowerSeasonFloor":             reflect.ValueOf(goxml.GuildMedalActivityTypeTowerSeasonFloor),
		"GuildMedalActivityTypeWorldBossAreaRank":            reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GuildMedalActivityTypeWorldBossRoomRank":            reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GuildMedalAddBossRush":                              reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"GuildMedalAddFlower":                                reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GuildMedalAddGst":                                   reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GuildMedalAddSeasonMap":                             reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"GuildMedalAddTrial":                                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildMedalDisorderLand":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildMemberDungeonDamageRankId":                     reflect.ValueOf(goxml.GuildMemberDungeonDamageRankId),
		"GuildMobRankID":                                     reflect.ValueOf(goxml.GuildMobRankID),
		"GuildRank":                                          reflect.ValueOf(&goxml.GuildRank).Elem(),
		"GuildSandTableLandTypeArena":                        reflect.ValueOf(constant.MakeFromLiteral("99", token.INT, 0)),
		"GuildSandTableLandTypeBarrier":                      reflect.ValueOf(goxml.GuildSandTableLandTypeBarrier),
		"GuildSandTableLandTypeEmpty":                        reflect.ValueOf(goxml.GuildSandTableLandTypeEmpty),
		"GuildSandTableLandTypeFence":                        reflect.ValueOf(goxml.GuildSandTableLandTypeFence),
		"GuildSandTableLandTypeMainBase":                     reflect.ValueOf(goxml.GuildSandTableLandTypeMainBase),
		"GuildSandTableLandTypeMonster":                      reflect.ValueOf(goxml.GuildSandTableLandTypeMonster),
		"GuildSandTableLandTypeNone":                         reflect.ValueOf(goxml.GuildSandTableLandTypeNone),
		"GuildSandTableLandTypeSenior":                       reflect.ValueOf(goxml.GuildSandTableLandTypeSenior),
		"GuildSandTableLandTypeStronghold":                   reflect.ValueOf(goxml.GuildSandTableLandTypeStronghold),
		"GuildTalentConnectTypeAnd":                          reflect.ValueOf(goxml.GuildTalentConnectTypeAnd),
		"GuildTalentConnectTypeOr":                           reflect.ValueOf(goxml.GuildTalentConnectTypeOr),
		"GuildTalentFloorFirst":                              reflect.ValueOf(goxml.GuildTalentFloorFirst),
		"GuildTalentFloorSecond":                             reflect.ValueOf(goxml.GuildTalentFloorSecond),
		"GuildTalentFloorThird":                              reflect.ValueOf(goxml.GuildTalentFloorThird),
		"GuildTalentRootNode":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"GuildTalentSecondFloorFifth":                        reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"GuildTalentSecondFloorFirst":                        reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"GuildTalentSecondFloorFourth":                       reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"GuildTalentSecondFloorSecond":                       reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"GuildTalentSecondFloorSixth":                        reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"GuildTalentSecondFloorThird":                        reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"GuildTechAddType":                                   reflect.ValueOf(goxml.GuildTechAddType),
		"GuildTechAddTypeGlobalPassive":                      reflect.ValueOf(goxml.GuildTechAddTypeGlobalPassive),
		"GuildTechAddTypeMemberPassive":                      reflect.ValueOf(goxml.GuildTechAddTypeMemberPassive),
		"GuildTechAddTypeOreOccupyNum":                       reflect.ValueOf(goxml.GuildTechAddTypeOreOccupyNum),
		"GuildTechAddTypeOreProduction":                      reflect.ValueOf(goxml.GuildTechAddTypeOreProduction),
		"GuildTechAddTypeReward":                             reflect.ValueOf(goxml.GuildTechAddTypeReward),
		"GuildTechAddTypeSkillTimes":                         reflect.ValueOf(goxml.GuildTechAddTypeSkillTimes),
		"H5DesktopRewardID":                                  reflect.ValueOf(goxml.H5DesktopRewardID),
		"HandbookInitCap":                                    reflect.ValueOf(goxml.HandbookInitCap),
		"HardStartID":                                        reflect.ValueOf(constant.MakeFromLiteral("20001", token.INT, 0)),
		"HaveMoreThanOrangeEmblemStageID":                    reflect.ValueOf(goxml.HaveMoreThanOrangeEmblemStageID),
		"HaveMoreThanRedEmblemStageID":                       reflect.ValueOf(goxml.HaveMoreThanRedEmblemStageID),
		"HeroAttrCalTypeByDefault":                           reflect.ValueOf(goxml.HeroAttrCalTypeByDefault),
		"HeroAttrCalTypeByMonster":                           reflect.ValueOf(goxml.HeroAttrCalTypeByMonster),
		"HeroAttrCalTypeBySeason":                            reflect.ValueOf(goxml.HeroAttrCalTypeBySeason),
		"HeroAttrCalTypeByWorldBoss":                         reflect.ValueOf(goxml.HeroAttrCalTypeByWorldBoss),
		"HeroAwakeAttrNum":                                   reflect.ValueOf(goxml.HeroAwakeAttrNum),
		"HeroAwakenLinkLevel":                                reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"HeroAwakenSkillMaxLevel":                            reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"HeroCanBack":                                        reflect.ValueOf(goxml.HeroCanBack),
		"HeroCanBackMinInitStar":                             reflect.ValueOf(goxml.HeroCanBackMinInitStar),
		"HeroCommon":                                         reflect.ValueOf(goxml.HeroCommon),
		"HeroConvertMaxNum":                                  reflect.ValueOf(goxml.HeroConvertMaxNum),
		"HeroDecomposeAwardResNum":                           reflect.ValueOf(goxml.HeroDecomposeAwardResNum),
		"HeroGemNeedMinInitStar":                             reflect.ValueOf(goxml.HeroGemNeedMinInitStar),
		"HeroHandbookAttrTypeLink2":                          reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"HeroHandbookAttrTypeLink3":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"HeroHandbookAttrTypeLink4":                          reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"HeroHandbookAttrTypeLink5":                          reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"HeroHandbookAttrTypeStar":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"HeroHandbookLinkAttrBit":                            reflect.ValueOf(&goxml.HeroHandbookLinkAttrBit).Elem(),
		"HeroJobAll":                                         reflect.ValueOf(goxml.HeroJobAll),
		"HeroJobAuxiliary":                                   reflect.ValueOf(goxml.HeroJobAuxiliary),
		"HeroJobMaster":                                      reflect.ValueOf(goxml.HeroJobMaster),
		"HeroJobSoldier":                                     reflect.ValueOf(goxml.HeroJobSoldier),
		"HeroJobTank":                                        reflect.ValueOf(goxml.HeroJobTank),
		"HeroLegalRace":                                      reflect.ValueOf(&goxml.HeroLegalRace).Elem(),
		"HeroMaxAwakeNum":                                    reflect.ValueOf(goxml.HeroMaxAwakeNum),
		"HeroNoRaceDefaultStar":                              reflect.ValueOf(goxml.HeroNoRaceDefaultStar),
		"HeroRareFakeFive":                                   reflect.ValueOf(goxml.HeroRareFakeFive),
		"HeroRareFind":                                       reflect.ValueOf(goxml.HeroRareFind),
		"HeroRareLegend":                                     reflect.ValueOf(goxml.HeroRareLegend),
		"HeroRareNormal":                                     reflect.ValueOf(goxml.HeroRareNormal),
		"HeroRareTrueFive":                                   reflect.ValueOf(goxml.HeroRareTrueFive),
		"HeroRareUnusual":                                    reflect.ValueOf(goxml.HeroRareUnusual),
		"HeroShowManual":                                     reflect.ValueOf(goxml.HeroShowManual),
		"HeroSpecial":                                        reflect.ValueOf(goxml.HeroSpecial),
		"HeroSpecialNoRaceLegalStar":                         reflect.ValueOf(&goxml.HeroSpecialNoRaceLegalStar).Elem(),
		"HeroSpecialNormalRaceLegalStar":                     reflect.ValueOf(&goxml.HeroSpecialNormalRaceLegalStar).Elem(),
		"HeroStageAttrNum":                                   reflect.ValueOf(goxml.HeroStageAttrNum),
		"HeroStageSkillNum":                                  reflect.ValueOf(goxml.HeroStageSkillNum),
		"HeroStageUpCostNum":                                 reflect.ValueOf(goxml.HeroStageUpCostNum),
		"HeroStarUpCostHeroNum":                              reflect.ValueOf(goxml.HeroStarUpCostHeroNum),
		"HeroStarUpCostLegalStar":                            reflect.ValueOf(&goxml.HeroStarUpCostLegalStar).Elem(),
		"HeroStarUpCostLegalType":                            reflect.ValueOf(&goxml.HeroStarUpCostLegalType).Elem(),
		"HeroStarUpCostStar5":                                reflect.ValueOf(goxml.HeroStarUpCostStar5),
		"HeroStarUpCostStar6":                                reflect.ValueOf(goxml.HeroStarUpCostStar6),
		"HeroStarUpCostStar9":                                reflect.ValueOf(goxml.HeroStarUpCostStar9),
		"HeroStarUpCostTypeAny":                              reflect.ValueOf(goxml.HeroStarUpCostTypeAny),
		"HeroStarUpCostTypeRace":                             reflect.ValueOf(goxml.HeroStarUpCostTypeRace),
		"HeroStarUpCostTypeSelf":                             reflect.ValueOf(goxml.HeroStarUpCostTypeSelf),
		"HotRankBeginTimeString":                             reflect.ValueOf(constant.MakeFromLiteral("\"2024-05-01 00:00:00\"", token.STRING, 0)),
		"HotRankHero":                                        reflect.ValueOf(constant.MakeFromLiteral("1010", token.INT, 0)),
		"HotRankOrangeArtifacts":                             reflect.ValueOf(constant.MakeFromLiteral("2031", token.INT, 0)),
		"HotRankOrangeElem":                                  reflect.ValueOf(constant.MakeFromLiteral("1021", token.INT, 0)),
		"HotRankReaArtifacts":                                reflect.ValueOf(constant.MakeFromLiteral("2030", token.INT, 0)),
		"HotRankRedEmblem":                                   reflect.ValueOf(constant.MakeFromLiteral("1020", token.INT, 0)),
		"HotRankScoreTypeRank":                               reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"HotRankScoreTypeRatio":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"IdMaxLanguage":                                      reflect.ValueOf(constant.MakeFromLiteral("\"ru\"", token.STRING, 0)),
		"IsCannotNegativeAttr":                               reflect.ValueOf(goxml.IsCannotNegativeAttr),
		"IsCyclePass":                                        reflect.ValueOf(goxml.IsCyclePass),
		"IsDropActivitySubFuncLegal":                         reflect.ValueOf(goxml.IsDropActivitySubFuncLegal),
		"IsFirstPeakPhase":                                   reflect.ValueOf(goxml.IsFirstPeakPhase),
		"IsFuncInSeason":                                     reflect.ValueOf(goxml.IsFuncInSeason),
		"IsGSTChallengeCanFight":                             reflect.ValueOf(goxml.IsGSTChallengeCanFight),
		"IsGSTChallengeOpen":                                 reflect.ValueOf(goxml.IsGSTChallengeOpen),
		"IsHeroExchangeRaceMatch":                            reflect.ValueOf(goxml.IsHeroExchangeRaceMatch),
		"IsInPeakPhase":                                      reflect.ValueOf(goxml.IsInPeakPhase),
		"IsLastPeakPhase":                                    reflect.ValueOf(goxml.IsLastPeakPhase),
		"IsLastRoundToSeason":                                reflect.ValueOf(goxml.IsLastRoundToSeason),
		"IsLegalArenaMatchExtMode":                           reflect.ValueOf(goxml.IsLegalArenaMatchExtMode),
		"IsLegalArenaRewardType":                             reflect.ValueOf(goxml.IsLegalArenaRewardType),
		"IsLegalFlowerbedGuildBuff":                          reflect.ValueOf(goxml.IsLegalFlowerbedGuildBuff),
		"IsLegalFlowerbedPos":                                reflect.ValueOf(goxml.IsLegalFlowerbedPos),
		"IsLegalHeroExchangeRaceType":                        reflect.ValueOf(goxml.IsLegalHeroExchangeRaceType),
		"IsLegalNumRefreshType":                              reflect.ValueOf(goxml.IsLegalNumRefreshType),
		"IsLegalPeakRankType":                                reflect.ValueOf(goxml.IsLegalPeakRankType),
		"IsPeakGuessOpen":                                    reflect.ValueOf(goxml.IsPeakGuessOpen),
		"IsRaceInhibit":                                      reflect.ValueOf(goxml.IsRaceInhibit),
		"IsSeasonFunctionOpen":                               reflect.ValueOf(goxml.IsSeasonFunctionOpen),
		"IsSeasonLinkRankId":                                 reflect.ValueOf(goxml.IsSeasonLinkRankId),
		"IsSeasonOpen":                                       reflect.ValueOf(goxml.IsSeasonOpen),
		"IsSeasonResource":                                   reflect.ValueOf(goxml.IsSeasonResource),
		"IsSeasonResourceExpire":                             reflect.ValueOf(goxml.IsSeasonResourceExpire),
		"IsServerUnlockSeason":                               reflect.ValueOf(goxml.IsServerUnlockSeason),
		"IsSingleTargetAttack":                               reflect.ValueOf(goxml.IsSingleTargetAttack),
		"IsWrestleActive":                                    reflect.ValueOf(goxml.IsWrestleActive),
		"IsWrestleDailyResetting":                            reflect.ValueOf(goxml.IsWrestleDailyResetting),
		"IsWrestleOpen":                                      reflect.ValueOf(goxml.IsWrestleOpen),
		"IsWrestleSeasonResetDay":                            reflect.ValueOf(goxml.IsWrestleSeasonResetDay),
		"IsWrestleTime":                                      reflect.ValueOf(goxml.IsWrestleTime),
		"ItemExpiredInBag":                                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ItemExpiredNone":                                    reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"ItemExpiredTogether":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"LegalArenaMatchExtMode":                             reflect.ValueOf(&goxml.LegalArenaMatchExtMode).Elem(),
		"LegalArenaRewardType":                               reflect.ValueOf(&goxml.LegalArenaRewardType).Elem(),
		"LegalCommonRankId":                                  reflect.ValueOf(&goxml.LegalCommonRankId).Elem(),
		"LegalJob":                                           reflect.ValueOf(&goxml.LegalJob).Elem(),
		"LegalWeekDay":                                       reflect.ValueOf(&goxml.LegalWeekDay).Elem(),
		"Limit":                                              reflect.ValueOf(goxml.Limit),
		"LinkSummon":                                         reflect.ValueOf(goxml.LinkSummon),
		"LinkSummonMaxGuaranteeLen":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"LinkSummonOrangeGuaranteeIndex":                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"LinkSummonPoolSize":                                 reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"LinkSummonPurpleGuaranteeIndex":                     reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"LinkSummonRedGuaranteeIndex":                        reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"LinkTypeCamp":                                       reflect.ValueOf(goxml.LinkTypeCamp),
		"LinkTypeChange":                                     reflect.ValueOf(goxml.LinkTypeChange),
		"LinkTypeCommon":                                     reflect.ValueOf(goxml.LinkTypeCommon),
		"LinkTypeEmblem":                                     reflect.ValueOf(goxml.LinkTypeEmblem),
		"LinkTypeSeason":                                     reflect.ValueOf(goxml.LinkTypeSeason),
		"Load":                                               reflect.ValueOf(goxml.Load),
		"LoadNotStore":                                       reflect.ValueOf(goxml.LoadNotStore),
		"LoginTaskTypeID":                                    reflect.ValueOf(goxml.LoginTaskTypeID),
		"LongTimeout":                                        reflect.ValueOf(goxml.LongTimeout),
		"MailIDSeasonComplianceStageID1RankReward":           reflect.ValueOf(goxml.MailIDSeasonComplianceStageID1RankReward),
		"MailIDSeasonComplianceStageID1ScoreReward":          reflect.ValueOf(goxml.MailIDSeasonComplianceStageID1ScoreReward),
		"MailIDSeasonComplianceStageID2RankReward":           reflect.ValueOf(goxml.MailIDSeasonComplianceStageID2RankReward),
		"MailIDSeasonComplianceStageID2ScoreReward":          reflect.ValueOf(goxml.MailIDSeasonComplianceStageID2ScoreReward),
		"MailIDSeasonComplianceStageID3RankReward":           reflect.ValueOf(goxml.MailIDSeasonComplianceStageID3RankReward),
		"MailIDSeasonComplianceStageID3ScoreReward":          reflect.ValueOf(goxml.MailIDSeasonComplianceStageID3ScoreReward),
		"MailIDSeasonComplianceStageID4RankReward":           reflect.ValueOf(goxml.MailIDSeasonComplianceStageID4RankReward),
		"MailIDSeasonComplianceStageID4ScoreReward":          reflect.ValueOf(goxml.MailIDSeasonComplianceStageID4ScoreReward),
		"MailIDSeasonComplianceTotalRankReward":              reflect.ValueOf(goxml.MailIDSeasonComplianceTotalRankReward),
		"MakeRes":                                            reflect.ValueOf(goxml.MakeRes),
		"MakeSpecialNumInitData":                             reflect.ValueOf(goxml.MakeSpecialNumInitData),
		"MasterEmblemBlessing":                               reflect.ValueOf(goxml.MasterEmblemBlessing),
		"MasterEmblemStrength":                               reflect.ValueOf(goxml.MasterEmblemStrength),
		"MasterEquipRefine":                                  reflect.ValueOf(goxml.MasterEquipRefine),
		"MasterEquipStrength":                                reflect.ValueOf(goxml.MasterEquipStrength),
		"MaxActivityLoginDay":                                reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"MaxCount":                                           reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"MaxTeamCount":                                       reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"MaxTop5UpdateNum":                                   reflect.ValueOf(goxml.MaxTop5UpdateNum),
		"Maze1BuffSuccess":                                   reflect.ValueOf(goxml.Maze1BuffSuccess),
		"Maze1Guess":                                         reflect.ValueOf(goxml.Maze1Guess),
		"Maze2BuffSuccess":                                   reflect.ValueOf(goxml.Maze2BuffSuccess),
		"Maze2Guess":                                         reflect.ValueOf(goxml.Maze2Guess),
		"Maze3BuffSuccess":                                   reflect.ValueOf(goxml.Maze3BuffSuccess),
		"Maze3Guess":                                         reflect.ValueOf(goxml.Maze3Guess),
		"MazeAttrExploreBattle":                              reflect.ValueOf(goxml.MazeAttrExploreBattle),
		"MazeAttrExploreChoice":                              reflect.ValueOf(goxml.MazeAttrExploreChoice),
		"MazeBlackMarketer":                                  reflect.ValueOf(goxml.MazeBlackMarketer),
		"MazeBlackMarketerShopID":                            reflect.ValueOf(goxml.MazeBlackMarketerShopID),
		"MazeBlessed":                                        reflect.ValueOf(goxml.MazeBlessed),
		"MazeBoss":                                           reflect.ValueOf(goxml.MazeBoss),
		"MazeBossAround":                                     reflect.ValueOf(goxml.MazeBossAround),
		"MazeBoxOpen":                                        reflect.ValueOf(goxml.MazeBoxOpen),
		"MazeBoxQuit":                                        reflect.ValueOf(goxml.MazeBoxQuit),
		"MazeBuffCountLimit":                                 reflect.ValueOf(goxml.MazeBuffCountLimit),
		"MazeBuffTagGain":                                    reflect.ValueOf(goxml.MazeBuffTagGain),
		"MazeBuffTagReduce":                                  reflect.ValueOf(goxml.MazeBuffTagReduce),
		"MazeChoiceEvent":                                    reflect.ValueOf(goxml.MazeChoiceEvent),
		"MazeCommonLevelAlterLinkBuffNumID":                  reflect.ValueOf(goxml.MazeCommonLevelAlterLinkBuffNumID),
		"MazeCommonLevelOpenSweepNeedNumID":                  reflect.ValueOf(goxml.MazeCommonLevelOpenSweepNeedNumID),
		"MazeCommonMonster":                                  reflect.ValueOf(goxml.MazeCommonMonster),
		"MazeCurseHurtID":                                    reflect.ValueOf(goxml.MazeCurseHurtID),
		"MazeCurseLand":                                      reflect.ValueOf(goxml.MazeCurseLand),
		"MazeCycleOneDayHours":                               reflect.ValueOf(constant.MakeFromLiteral("24", token.INT, 0)),
		"MazeCycleOneHourSeconds":                            reflect.ValueOf(constant.MakeFromLiteral("3600", token.INT, 0)),
		"MazeDanusHeartID":                                   reflect.ValueOf(goxml.MazeDanusHeartID),
		"MazeDemonSupervisor":                                reflect.ValueOf(goxml.MazeDemonSupervisor),
		"MazeDemonSupervisorAppearTag":                       reflect.ValueOf(goxml.MazeDemonSupervisorAppearTag),
		"MazeDemonSupervisorBuffID":                          reflect.ValueOf(goxml.MazeDemonSupervisorBuffID),
		"MazeDifficultLevelAlterLinkBuffNumID":               reflect.ValueOf(goxml.MazeDifficultLevelAlterLinkBuffNumID),
		"MazeDifficultLevelOpenSweepNeedNumID":               reflect.ValueOf(goxml.MazeDifficultLevelOpenSweepNeedNumID),
		"MazeDifficultMonster":                               reflect.ValueOf(goxml.MazeDifficultMonster),
		"MazeEliteMonster":                                   reflect.ValueOf(goxml.MazeEliteMonster),
		"MazeEnemyEvent":                                     reflect.ValueOf(&goxml.MazeEnemyEvent).Elem(),
		"MazeEvent":                                          reflect.ValueOf(&goxml.MazeEvent).Elem(),
		"MazeExploreEvent":                                   reflect.ValueOf(&goxml.MazeExploreEvent).Elem(),
		"MazeGetPowerRankUserNum":                            reflect.ValueOf(goxml.MazeGetPowerRankUserNum),
		"MazeGoldenHand":                                     reflect.ValueOf(goxml.MazeGoldenHand),
		"MazeGoldenHandRateID":                               reflect.ValueOf(goxml.MazeGoldenHandRateID),
		"MazeGridCompleted":                                  reflect.ValueOf(goxml.MazeGridCompleted),
		"MazeGridNoCompleted":                                reflect.ValueOf(goxml.MazeGridNoCompleted),
		"MazeGuardBattleCountID":                             reflect.ValueOf(goxml.MazeGuardBattleCountID),
		"MazeGuardMaxBattleRoundID":                          reflect.ValueOf(goxml.MazeGuardMaxBattleRoundID),
		"MazeGuessSilhouette":                                reflect.ValueOf(goxml.MazeGuessSilhouette),
		"MazeHellLevelAlterLinkBuffNumID":                    reflect.ValueOf(goxml.MazeHellLevelAlterLinkBuffNumID),
		"MazeHellLevelOpenSweepNeedNumID":                    reflect.ValueOf(goxml.MazeHellLevelOpenSweepNeedNumID),
		"MazeInitPowerDungeonID":                             reflect.ValueOf(constant.MakeFromLiteral("10315", token.INT, 0)),
		"MazeItemGoldenHand":                                 reflect.ValueOf(goxml.MazeItemGoldenHand),
		"MazeItemPurifyPotion":                               reflect.ValueOf(goxml.MazeItemPurifyPotion),
		"MazeItemRuleSword":                                  reflect.ValueOf(goxml.MazeItemRuleSword),
		"MazeItemTrapRemover":                                reflect.ValueOf(goxml.MazeItemTrapRemover),
		"MazeItemTreasuryKey":                                reflect.ValueOf(goxml.MazeItemTreasuryKey),
		"MazeItemTrueEye":                                    reflect.ValueOf(goxml.MazeItemTrueEye),
		"MazeLifeCure":                                       reflect.ValueOf(goxml.MazeLifeCure),
		"MazeLifeCureID":                                     reflect.ValueOf(goxml.MazeLifeCureID),
		"MazeLifeRecovery":                                   reflect.ValueOf(goxml.MazeLifeRecovery),
		"MazeLifeRecoveryID":                                 reflect.ValueOf(goxml.MazeLifeRecoveryID),
		"MazeLifeStream":                                     reflect.ValueOf(goxml.MazeLifeStream),
		"MazeLimitEnemyMinNumID":                             reflect.ValueOf(goxml.MazeLimitEnemyMinNumID),
		"MazeLimitEnemyPowerRatioID":                         reflect.ValueOf(goxml.MazeLimitEnemyPowerRatioID),
		"MazeLittleMonster":                                  reflect.ValueOf(goxml.MazeLittleMonster),
		"MazeMagicBox":                                       reflect.ValueOf(goxml.MazeMagicBox),
		"MazeMagicCrystalOre":                                reflect.ValueOf(goxml.MazeMagicCrystalOre),
		"MazeMapLevelCommon":                                 reflect.ValueOf(goxml.MazeMapLevelCommon),
		"MazeMapLevelDifficult":                              reflect.ValueOf(goxml.MazeMapLevelDifficult),
		"MazeMapLevelHell":                                   reflect.ValueOf(goxml.MazeMapLevelHell),
		"MazeMapNewRefreshCycleID":                           reflect.ValueOf(goxml.MazeMapNewRefreshCycleID),
		"MazeMapRefreshCycleID":                              reflect.ValueOf(goxml.MazeMapRefreshCycleID),
		"MazeNewDaysID":                                      reflect.ValueOf(goxml.MazeNewDaysID),
		"MazeObstacle":                                       reflect.ValueOf(goxml.MazeObstacle),
		"MazeOpenBoxCostResID":                               reflect.ValueOf(goxml.MazeOpenBoxCostResID),
		"MazePowerRatio":                                     reflect.ValueOf(constant.MakeFromLiteral("1000", token.INT, 0)),
		"MazePurifyPotion":                                   reflect.ValueOf(goxml.MazePurifyPotion),
		"MazeReceiveID":                                      reflect.ValueOf(constant.MakeFromLiteral("10020", token.INT, 0)),
		"MazeRoad":                                           reflect.ValueOf(goxml.MazeRoad),
		"MazeRoundNumMatchRobotID":                           reflect.ValueOf(goxml.MazeRoundNumMatchRobotID),
		"MazeRuleSword":                                      reflect.ValueOf(goxml.MazeRuleSword),
		"MazeScroll":                                         reflect.ValueOf(goxml.MazeScroll),
		"MazeSoulAlterBuffNumID":                             reflect.ValueOf(goxml.MazeSoulAlterBuffNumID),
		"MazeSoulBuffType":                                   reflect.ValueOf(goxml.MazeSoulBuffType),
		"MazeSourAltar":                                      reflect.ValueOf(goxml.MazeSourAltar),
		"MazeStartPoint":                                     reflect.ValueOf(goxml.MazeStartPoint),
		"MazeTaskLevelAwardTypeItem":                         reflect.ValueOf(goxml.MazeTaskLevelAwardTypeItem),
		"MazeTaskLevelAwardTypePrivilege":                    reflect.ValueOf(goxml.MazeTaskLevelAwardTypePrivilege),
		"MazeToken":                                          reflect.ValueOf(goxml.MazeToken),
		"MazeTrapRemover":                                    reflect.ValueOf(goxml.MazeTrapRemover),
		"MazeTreasury":                                       reflect.ValueOf(goxml.MazeTreasury),
		"MazeTreasuryGuard":                                  reflect.ValueOf(goxml.MazeTreasuryGuard),
		"MazeTrueEye":                                        reflect.ValueOf(goxml.MazeTrueEye),
		"MedalDailyAwardOpen":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"MedalReceiveAwardTypeByDaily":                       reflect.ValueOf(goxml.MedalReceiveAwardTypeByDaily),
		"MedalReceiveAwardTypeByLevel":                       reflect.ValueOf(goxml.MedalReceiveAwardTypeByLevel),
		"MedalReceiveAwardTypeByTask":                        reflect.ValueOf(goxml.MedalReceiveAwardTypeByTask),
		"MedalTaskRecordTypeByLevelOpen":                     reflect.ValueOf(goxml.MedalTaskRecordTypeByLevelOpen),
		"MedalTaskRecordTypeByServerOpen":                    reflect.ValueOf(goxml.MedalTaskRecordTypeByServerOpen),
		"MemoryChipNum":                                      reflect.ValueOf(goxml.MemoryChipNum),
		"MemoryFirst":                                        reflect.ValueOf(goxml.MemoryFirst),
		"MergeAttr":                                          reflect.ValueOf(goxml.MergeAttr),
		"MirageCopyID2FormationID":                           reflect.ValueOf(&goxml.MirageCopyID2FormationID).Elem(),
		"MirageCopyID2RankId":                                reflect.ValueOf(&goxml.MirageCopyID2RankId).Elem(),
		"MirageCopyID2TaskTypeId":                            reflect.ValueOf(&goxml.MirageCopyID2TaskTypeId).Elem(),
		"MirageDemonRankId":                                  reflect.ValueOf(goxml.MirageDemonRankId),
		"MirageEmpireRankId":                                 reflect.ValueOf(goxml.MirageEmpireRankId),
		"MirageFightEffectArtifactLink":                      reflect.ValueOf(goxml.MirageFightEffectArtifactLink),
		"MirageFightEffectArtifactRareStar":                  reflect.ValueOf(goxml.MirageFightEffectArtifactRareStar),
		"MirageFightEffectHeroLink":                          reflect.ValueOf(goxml.MirageFightEffectHeroLink),
		"MirageFightEffectNone":                              reflect.ValueOf(goxml.MirageFightEffectNone),
		"MirageFightStageID":                                 reflect.ValueOf(goxml.MirageFightStageID),
		"MirageForestRankId":                                 reflect.ValueOf(goxml.MirageForestRankId),
		"MirageInitAffixAttrNum":                             reflect.ValueOf(goxml.MirageInitAffixAttrNum),
		"MirageInitAffixNum":                                 reflect.ValueOf(goxml.MirageInitAffixNum),
		"MirageLegalCopyID":                                  reflect.ValueOf(&goxml.MirageLegalCopyID).Elem(),
		"MirageLegalSkillTarget":                             reflect.ValueOf(&goxml.MirageLegalSkillTarget).Elem(),
		"MirageMoonRankId":                                   reflect.ValueOf(goxml.MirageMoonRankId),
		"MirageProtossRankId":                                reflect.ValueOf(goxml.MirageProtossRankId),
		"MirageRaceDemon":                                    reflect.ValueOf(goxml.MirageRaceDemon),
		"MirageRaceEmpire":                                   reflect.ValueOf(goxml.MirageRaceEmpire),
		"MirageRaceForest":                                   reflect.ValueOf(goxml.MirageRaceForest),
		"MirageRaceMoon":                                     reflect.ValueOf(goxml.MirageRaceMoon),
		"MirageRaceProtoss":                                  reflect.ValueOf(goxml.MirageRaceProtoss),
		"MirageRaceSix":                                      reflect.ValueOf(goxml.MirageRaceSix),
		"MirageSixRankId":                                    reflect.ValueOf(goxml.MirageSixRankId),
		"Monday":                                             reflect.ValueOf(goxml.Monday),
		"MonsterAttrMaxNum":                                  reflect.ValueOf(constant.MakeFromLiteral("27", token.INT, 0)),
		"MonsterTypeBoss":                                    reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"MonthlyCardAsistantUnlock":                          reflect.ValueOf(goxml.MonthlyCardAsistantUnlock),
		"MonthlyCardDispatchMaxQuality1":                     reflect.ValueOf(goxml.MonthlyCardDispatchMaxQuality1),
		"MonthlyCardDispatchMaxQuality2":                     reflect.ValueOf(goxml.MonthlyCardDispatchMaxQuality2),
		"MonthlyCardDispatchOrange":                          reflect.ValueOf(goxml.MonthlyCardDispatchOrange),
		"MonthlyCardDispatchPurple":                          reflect.ValueOf(goxml.MonthlyCardDispatchPurple),
		"MonthlyCardDungeonSpeed":                            reflect.ValueOf(goxml.MonthlyCardDungeonSpeed),
		"MonthlyCardFlowerBox":                               reflect.ValueOf(goxml.MonthlyCardFlowerBox),
		"MonthlyCardForestBox":                               reflect.ValueOf(goxml.MonthlyCardForestBox),
		"MonthlyCardGoddessTouchCount":                       reflect.ValueOf(goxml.MonthlyCardGoddessTouchCount),
		"MonthlyCardMailIntervalDay":                         reflect.ValueOf(goxml.MonthlyCardMailIntervalDay),
		"MonthlyCardMazeSweep":                               reflect.ValueOf(goxml.MonthlyCardMazeSweep),
		"MonthlyCardMazeToken":                               reflect.ValueOf(goxml.MonthlyCardMazeToken),
		"MonthlyCardTypeGrowUp":                              reflect.ValueOf(goxml.MonthlyCardTypeGrowUp),
		"MonthlyCardTypeSupply":                              reflect.ValueOf(goxml.MonthlyCardTypeSupply),
		"MuteAccountType":                                    reflect.ValueOf(&goxml.MuteAccountType).Elem(),
		"MuteForever":                                        reflect.ValueOf(constant.MakeFromLiteral("2000", token.INT, 0)),
		"MuteOneDay":                                         reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"MuteSevenDay":                                       reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"MuteTypeForever":                                    reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"MuteTypeOneDay":                                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"MuteTypeSevenDay":                                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NeedFilterArtifactDebutMailAward":                   reflect.ValueOf(goxml.NeedFilterArtifactDebutMailAward),
		"NightmareStartID":                                   reflect.ValueOf(constant.MakeFromLiteral("30001", token.INT, 0)),
		"NoLimit":                                            reflect.ValueOf(goxml.NoLimit),
		"NormalSeasonHeroStageID":                            reflect.ValueOf(goxml.NormalSeasonHeroStageID),
		"NormalStartID":                                      reflect.ValueOf(constant.MakeFromLiteral("10001", token.INT, 0)),
		"NumBuyAndUse":                                       reflect.ValueOf(goxml.NumBuyAndUse),
		"NumBuyForbid":                                       reflect.ValueOf(goxml.NumBuyForbid),
		"NumBuyNoLimit":                                      reflect.ValueOf(goxml.NumBuyNoLimit),
		"NumBuyOnly":                                         reflect.ValueOf(goxml.NumBuyOnly),
		"NumRefreshDaily":                                    reflect.ValueOf(goxml.NumRefreshDaily),
		"NumRefreshForbid":                                   reflect.ValueOf(goxml.NumRefreshForbid),
		"NumRefreshWeekly":                                   reflect.ValueOf(goxml.NumRefreshWeekly),
		"NumRefreshWeeklyFriday":                             reflect.ValueOf(goxml.NumRefreshWeeklyFriday),
		"OneDaySeconds":                                      reflect.ValueOf(goxml.OneDaySeconds),
		"OnhookRealRandomDropTime":                           reflect.ValueOf(goxml.OnhookRealRandomDropTime),
		"OpStatusOff":                                        reflect.ValueOf(goxml.OpStatusOff),
		"OpStatusRelease":                                    reflect.ValueOf(goxml.OpStatusRelease),
		"Op_Fail":                                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"Op_Success":                                         reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"PassActivityStory1":                                 reflect.ValueOf(goxml.PassActivityStory1),
		"PassActivityStoryNoReset":                           reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PassBastFloat":                                      reflect.ValueOf(goxml.PassBastFloat),
		"PassChristmas":                                      reflect.ValueOf(goxml.PassChristmas),
		"PassCycleTaskResetDaily":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PassCycleTaskResetEveryMonday":                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PassDailyActive":                                    reflect.ValueOf(goxml.PassDailyActive),
		"PassDiamondActive":                                  reflect.ValueOf(goxml.PassDiamondActive),
		"PassDungeon":                                        reflect.ValueOf(goxml.PassDungeon),
		"PassMazeActive":                                     reflect.ValueOf(goxml.PassMazeActive),
		"PassProphetActive":                                  reflect.ValueOf(goxml.PassProphetActive),
		"PassSeason":                                         reflect.ValueOf(goxml.PassSeason),
		"PassSeasonS1":                                       reflect.ValueOf(goxml.PassSeasonS1),
		"PassTower":                                          reflect.ValueOf(goxml.PassTower),
		"PassTypeActivity":                                   reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PassTypeCreateU":                                    reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"PassTypeCycle":                                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PassTypeDungeon":                                    reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"PassTypeOnce":                                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PassTypeOpenServer":                                 reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"PassTypeSeason":                                     reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"PassTypeSeasonWeek":                                 reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"PassTypeSkin":                                       reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"PassTypeTower":                                      reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"PassTypeUserLevel":                                  reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"PctEffectAttrAttack":                                reflect.ValueOf(&goxml.PctEffectAttrAttack).Elem(),
		"PctEffectAttrCritDamAddRate":                        reflect.ValueOf(&goxml.PctEffectAttrCritDamAddRate).Elem(),
		"PctEffectAttrDef":                                   reflect.ValueOf(&goxml.PctEffectAttrDef).Elem(),
		"PctEffectAttrFixedDam":                              reflect.ValueOf(&goxml.PctEffectAttrFixedDam).Elem(),
		"PctEffectAttrFixedDamReduce":                        reflect.ValueOf(&goxml.PctEffectAttrFixedDamReduce).Elem(),
		"PctEffectAttrHp":                                    reflect.ValueOf(&goxml.PctEffectAttrHp).Elem(),
		"PctEffectAttrMagDef":                                reflect.ValueOf(&goxml.PctEffectAttrMagDef).Elem(),
		"PctEffectAttrPhyDef":                                reflect.ValueOf(&goxml.PctEffectAttrPhyDef).Elem(),
		"PctEffectAttrSpeed":                                 reflect.ValueOf(&goxml.PctEffectAttrSpeed).Elem(),
		"PeakAreaLower":                                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PeakAreaUpper":                                      reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PeakAreas":                                          reflect.ValueOf(&goxml.PeakAreas).Elem(),
		"PeakGroupCount":                                     reflect.ValueOf(goxml.PeakGroupCount),
		"PeakGroupRound1MatchCount":                          reflect.ValueOf(goxml.PeakGroupRound1MatchCount),
		"PeakGroupRound2MatchCount":                          reflect.ValueOf(goxml.PeakGroupRound2MatchCount),
		"PeakGuessMatchCount":                                reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"PeakGuessWinParam":                                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PeakPeriodChampion":                                 reflect.ValueOf(goxml.PeakPeriodChampion),
		"PeakPeriodFinal":                                    reflect.ValueOf(goxml.PeakPeriodFinal),
		"PeakPeriodGroup":                                    reflect.ValueOf(goxml.PeakPeriodGroup),
		"PeakPeriodRounds":                                   reflect.ValueOf(&goxml.PeakPeriodRounds).Elem(),
		"PeakPhase1":                                         reflect.ValueOf(goxml.PeakPhase1),
		"PeakPhase2":                                         reflect.ValueOf(goxml.PeakPhase2),
		"PeakPhase3":                                         reflect.ValueOf(goxml.PeakPhase3),
		"PeakPhase4":                                         reflect.ValueOf(goxml.PeakPhase4),
		"PeakPhaseDuration":                                  reflect.ValueOf(&goxml.PeakPhaseDuration).Elem(),
		"PeakPhases":                                         reflect.ValueOf(&goxml.PeakPhases).Elem(),
		"PeakPlayerCount":                                    reflect.ValueOf(goxml.PeakPlayerCount),
		"PeakRank2Pos":                                       reflect.ValueOf(&goxml.PeakRank2Pos).Elem(),
		"PeakRankID":                                         reflect.ValueOf(goxml.PeakRankID),
		"PeakRewardTypePhase":                                reflect.ValueOf(goxml.PeakRewardTypePhase),
		"PeakRewardTypeSeason":                               reflect.ValueOf(goxml.PeakRewardTypeSeason),
		"PeakRewardTypes":                                    reflect.ValueOf(&goxml.PeakRewardTypes).Elem(),
		"PeakRound1":                                         reflect.ValueOf(goxml.PeakRound1),
		"PeakRound1TotalMatchCount":                          reflect.ValueOf(goxml.PeakRound1TotalMatchCount),
		"PeakRound2":                                         reflect.ValueOf(goxml.PeakRound2),
		"PeakRound3":                                         reflect.ValueOf(goxml.PeakRound3),
		"PeakRound4":                                         reflect.ValueOf(goxml.PeakRound4),
		"PeakRound5":                                         reflect.ValueOf(goxml.PeakRound5),
		"PeakRound6":                                         reflect.ValueOf(goxml.PeakRound6),
		"PeakRound7":                                         reflect.ValueOf(goxml.PeakRound7),
		"PeakRounds":                                         reflect.ValueOf(&goxml.PeakRounds).Elem(),
		"PeakTop8Count":                                      reflect.ValueOf(goxml.PeakTop8Count),
		"PointsSummon":                                       reflect.ValueOf(goxml.PointsSummon),
		"PowerAttackParaID":                                  reflect.ValueOf(goxml.PowerAttackParaID),
		"PowerControlRateParaID":                             reflect.ValueOf(goxml.PowerControlRateParaID),
		"PowerControlReduceRateParaID":                       reflect.ValueOf(goxml.PowerControlReduceRateParaID),
		"PowerCritDamAddRateParaID":                          reflect.ValueOf(goxml.PowerCritDamAddRateParaID),
		"PowerCritDamReduceRateParaID":                       reflect.ValueOf(goxml.PowerCritDamReduceRateParaID),
		"PowerCritRateParaID":                                reflect.ValueOf(goxml.PowerCritRateParaID),
		"PowerCritReduceRateParaID":                          reflect.ValueOf(goxml.PowerCritReduceRateParaID),
		"PowerDamAddParaID":                                  reflect.ValueOf(goxml.PowerDamAddParaID),
		"PowerDamReduceParaID":                               reflect.ValueOf(goxml.PowerDamReduceParaID),
		"PowerDamReflectRateParaID":                          reflect.ValueOf(goxml.PowerDamReflectRateParaID),
		"PowerDamReflectReduceRateParaID":                    reflect.ValueOf(goxml.PowerDamReflectReduceRateParaID),
		"PowerDefPunctureRateParaID":                         reflect.ValueOf(goxml.PowerDefPunctureRateParaID),
		"PowerDefPunctureReduceRateParaID":                   reflect.ValueOf(goxml.PowerDefPunctureReduceRateParaID),
		"PowerDodgeRateParaID":                               reflect.ValueOf(goxml.PowerDodgeRateParaID),
		"PowerFixedDamParaID":                                reflect.ValueOf(goxml.PowerFixedDamParaID),
		"PowerFixedDamReduceParaID":                          reflect.ValueOf(goxml.PowerFixedDamReduceParaID),
		"PowerGetHealRateParaID":                             reflect.ValueOf(goxml.PowerGetHealRateParaID),
		"PowerHealRateParaID":                                reflect.ValueOf(goxml.PowerHealRateParaID),
		"PowerHitRateParaID":                                 reflect.ValueOf(goxml.PowerHitRateParaID),
		"PowerHpParaID":                                      reflect.ValueOf(goxml.PowerHpParaID),
		"PowerMagDefParaID":                                  reflect.ValueOf(goxml.PowerMagDefParaID),
		"PowerPhyDefParaID":                                  reflect.ValueOf(goxml.PowerPhyDefParaID),
		"PowerRankId":                                        reflect.ValueOf(goxml.PowerRankId),
		"PowerSpeedFixParaID":                                reflect.ValueOf(goxml.PowerSpeedFixParaID),
		"PowerSpeedParaID":                                   reflect.ValueOf(goxml.PowerSpeedParaID),
		"PowerSuckBloodRateParaID":                           reflect.ValueOf(goxml.PowerSuckBloodRateParaID),
		"PowerSuckBloodReduceRateParaID":                     reflect.ValueOf(goxml.PowerSuckBloodReduceRateParaID),
		"ProtossDemonOrangeEmblemStageID":                    reflect.ValueOf(goxml.ProtossDemonOrangeEmblemStageID),
		"ProtossDemonRedEmblemStageID":                       reflect.ValueOf(goxml.ProtossDemonRedEmblemStageID),
		"ProtossDemonSeasonOrangeEmblemStageID":              reflect.ValueOf(goxml.ProtossDemonSeasonOrangeEmblemStageID),
		"ProtossDemonSeasonRedEmblemStageID":                 reflect.ValueOf(goxml.ProtossDemonSeasonRedEmblemStageID),
		"PushGiftRefreshTypeDaily":                           reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PushGiftRefreshTypeNone":                            reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"PushGiftType":                                       reflect.ValueOf(&goxml.PushGiftType).Elem(),
		"PushGiftTypeGroupBuy":                               reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"PushGiftTypeSingleBuy":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"PuzzleModelM":                                       reflect.ValueOf(&goxml.PuzzleModelM).Elem(),
		"PyramidDrawNumStageID":                              reflect.ValueOf(goxml.PyramidDrawNumStageID),
		"RaceDemon":                                          reflect.ValueOf(goxml.RaceDemon),
		"RaceEmpire":                                         reflect.ValueOf(goxml.RaceEmpire),
		"RaceForest":                                         reflect.ValueOf(goxml.RaceForest),
		"RaceMoon":                                           reflect.ValueOf(goxml.RaceMoon),
		"RaceNone":                                           reflect.ValueOf(goxml.RaceNone),
		"RaceProtoss":                                        reflect.ValueOf(goxml.RaceProtoss),
		"RaceTypeCommonThree":                                reflect.ValueOf(goxml.RaceTypeCommonThree),
		"RaceTypeProtossDemon":                               reflect.ValueOf(goxml.RaceTypeProtossDemon),
		"RaisePsAttrTypeArtifactLink":                        reflect.ValueOf(goxml.RaisePsAttrTypeArtifactLink),
		"RaisePsAttrTypeLink":                                reflect.ValueOf(goxml.RaisePsAttrTypeLink),
		"RaisePsAttrTypePs":                                  reflect.ValueOf(goxml.RaisePsAttrTypePs),
		"RankAchieveAwardsInitCap":                           reflect.ValueOf(goxml.RankAchieveAwardsInitCap),
		"RankAchieveInitCap":                                 reflect.ValueOf(goxml.RankAchieveInitCap),
		"RankId2FuncId":                                      reflect.ValueOf(&goxml.RankId2FuncId).Elem(),
		"RareBlue":                                           reflect.ValueOf(constant.MakeFromLiteral("30", token.INT, 0)),
		"RareGreen":                                          reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"RareOrange":                                         reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"RarePurple":                                         reflect.ValueOf(constant.MakeFromLiteral("40", token.INT, 0)),
		"RareRed":                                            reflect.ValueOf(constant.MakeFromLiteral("60", token.INT, 0)),
		"RealRandomDrop":                                     reflect.ValueOf(goxml.RealRandomDrop),
		"RealSureDrop":                                       reflect.ValueOf(goxml.RealSureDrop),
		"RebaseOperatedAwardAndSet":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"RebaseOperatedSet":                                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"RecordStartAfterActivityOpen":                       reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"RecordStartBeforeActivityOpen":                      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"RedArtifactFragmentStageID":                         reflect.ValueOf(goxml.RedArtifactFragmentStageID),
		"RedArtifactStageID":                                 reflect.ValueOf(goxml.RedArtifactStageID),
		"RedSeasonArtifactFragmentStageID":                   reflect.ValueOf(goxml.RedSeasonArtifactFragmentStageID),
		"RedSeasonArtifactStageID":                           reflect.ValueOf(goxml.RedSeasonArtifactStageID),
		"RemainTypeAll":                                      reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"RemainTypePVE":                                      reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"RemainTypePVP":                                      reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ResourceTypeCoupon":                                 reflect.ValueOf(goxml.ResourceTypeCoupon),
		"ReviseAddCount":                                     reflect.ValueOf(goxml.ReviseAddCount),
		"ReviseAround":                                       reflect.ValueOf(goxml.ReviseAround),
		"ReviseAroundNoSelf":                                 reflect.ValueOf(goxml.ReviseAroundNoSelf),
		"ReviseBack":                                         reflect.ValueOf(goxml.ReviseBack),
		"ReviseFront":                                        reflect.ValueOf(goxml.ReviseFront),
		"ReviseNotAround":                                    reflect.ValueOf(goxml.ReviseNotAround),
		"ReviseNotSameRow":                                   reflect.ValueOf(goxml.ReviseNotSameRow),
		"ReviseSameRow":                                      reflect.ValueOf(goxml.ReviseSameRow),
		"ReviseSameRowNoSelf":                                reflect.ValueOf(goxml.ReviseSameRowNoSelf),
		"ReviseSortByAttackPosition":                         reflect.ValueOf(goxml.ReviseSortByAttackPosition),
		"ReviseSortByBuffBigTypeNumHighToLow":                reflect.ValueOf(goxml.ReviseSortByBuffBigTypeNumHighToLow),
		"ReviseSortByBuffBigTypeNumLowToHigh":                reflect.ValueOf(goxml.ReviseSortByBuffBigTypeNumLowToHigh),
		"ReviseSortByPos":                                    reflect.ValueOf(goxml.ReviseSortByPos),
		"ReviseSortRandom":                                   reflect.ValueOf(goxml.ReviseSortRandom),
		"RobotLowestRatio":                                   reflect.ValueOf(goxml.RobotLowestRatio),
		"RoundActivityTypeAdvanced":                          reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"RoundActivityTypeArtifact":                          reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"RoundActivityTypeAssoc":                             reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"RoundActivityTypeLink":                              reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"SERVER_TYPE_CN":                                     reflect.ValueOf(constant.MakeFromLiteral("\"cn\"", token.STRING, 0)),
		"SERVER_TYPE_DEFAULT":                                reflect.ValueOf(constant.MakeFromLiteral("\"\"", token.STRING, 0)),
		"SERVER_TYPE_TW":                                     reflect.ValueOf(constant.MakeFromLiteral("\"tw\"", token.STRING, 0)),
		"Saturday":                                           reflect.ValueOf(goxml.Saturday),
		"SeasonAddHandbookArtifactStar":                      reflect.ValueOf(goxml.SeasonAddHandbookArtifactStar),
		"SeasonAddHandbookHeroEmblemExclusiveLv":             reflect.ValueOf(goxml.SeasonAddHandbookHeroEmblemExclusiveLv),
		"SeasonAddHandbookHeroStar":                          reflect.ValueOf(goxml.SeasonAddHandbookHeroStar),
		"SeasonAddHandbookTypeNone":                          reflect.ValueOf(goxml.SeasonAddHandbookTypeNone),
		"SeasonAddTypeFirst":                                 reflect.ValueOf(goxml.SeasonAddTypeFirst),
		"SeasonAddTypeGvgFightTimes":                         reflect.ValueOf(goxml.SeasonAddTypeGvgFightTimes),
		"SeasonAddTypeNone":                                  reflect.ValueOf(goxml.SeasonAddTypeNone),
		"SeasonAddTypeSecond":                                reflect.ValueOf(goxml.SeasonAddTypeSecond),
		"SeasonArenaDefender2Attack":                         reflect.ValueOf(&goxml.SeasonArenaDefender2Attack).Elem(),
		"SeasonArenaDivisionRewardTypeClick":                 reflect.ValueOf(goxml.SeasonArenaDivisionRewardTypeClick),
		"SeasonArenaDivisionRewardTypeEmail":                 reflect.ValueOf(goxml.SeasonArenaDivisionRewardTypeEmail),
		"SeasonArenaDivisionRewardTypeMail":                  reflect.ValueOf(goxml.SeasonArenaDivisionRewardTypeMail),
		"SeasonArenaDivisionRewardTypeRecv":                  reflect.ValueOf(goxml.SeasonArenaDivisionRewardTypeRecv),
		"SeasonArenaFameComplexCount":                        reflect.ValueOf(goxml.SeasonArenaFameComplexCount),
		"SeasonArenaFameID":                                  reflect.ValueOf(goxml.SeasonArenaFameID),
		"SeasonArenaFameSimpleCount":                         reflect.ValueOf(goxml.SeasonArenaFameSimpleCount),
		"SeasonArenaLogMaxNum":                               reflect.ValueOf(goxml.SeasonArenaLogMaxNum),
		"SeasonArenaOpponentNum":                             reflect.ValueOf(goxml.SeasonArenaOpponentNum),
		"SeasonArenaOptionPlayer":                            reflect.ValueOf(goxml.SeasonArenaOptionPlayer),
		"SeasonArenaOptionRobot":                             reflect.ValueOf(goxml.SeasonArenaOptionRobot),
		"SeasonArenaRankID":                                  reflect.ValueOf(goxml.SeasonArenaRankID),
		"SeasonComplianceArtifactID":                         reflect.ValueOf(goxml.SeasonComplianceArtifactID),
		"SeasonComplianceArtifactStage":                      reflect.ValueOf(goxml.SeasonComplianceArtifactStage),
		"SeasonComplianceChallengeID":                        reflect.ValueOf(goxml.SeasonComplianceChallengeID),
		"SeasonComplianceChallengeStage":                     reflect.ValueOf(goxml.SeasonComplianceChallengeStage),
		"SeasonComplianceEmblemID":                           reflect.ValueOf(goxml.SeasonComplianceEmblemID),
		"SeasonComplianceEmblemOrangeScoreType":              reflect.ValueOf(goxml.SeasonComplianceEmblemOrangeScoreType),
		"SeasonComplianceEmblemRare2StageID":                 reflect.ValueOf(&goxml.SeasonComplianceEmblemRare2StageID).Elem(),
		"SeasonComplianceEmblemRedScoreType":                 reflect.ValueOf(goxml.SeasonComplianceEmblemRedScoreType),
		"SeasonComplianceEmblemStage":                        reflect.ValueOf(goxml.SeasonComplianceEmblemStage),
		"SeasonComplianceHeroID":                             reflect.ValueOf(goxml.SeasonComplianceHeroID),
		"SeasonComplianceHeroStage":                          reflect.ValueOf(goxml.SeasonComplianceHeroStage),
		"SeasonComplianceMail":                               reflect.ValueOf(&goxml.SeasonComplianceMail).Elem(),
		"SeasonComplianceRankIDs":                            reflect.ValueOf(&goxml.SeasonComplianceRankIDs).Elem(),
		"SeasonComplianceStageRank":                          reflect.ValueOf(&goxml.SeasonComplianceStageRank).Elem(),
		"SeasonComplianceTotalID":                            reflect.ValueOf(goxml.SeasonComplianceTotalID),
		"SeasonComplianceTotalStage":                         reflect.ValueOf(goxml.SeasonComplianceTotalStage),
		"SeasonDungeonLayerTypeHero":                         reflect.ValueOf(goxml.SeasonDungeonLayerTypeHero),
		"SeasonDungeonLayerTypeSuit":                         reflect.ValueOf(goxml.SeasonDungeonLayerTypeSuit),
		"SeasonDungeonRankID":                                reflect.ValueOf(goxml.SeasonDungeonRankID),
		"SeasonFormatParam":                                  reflect.ValueOf(goxml.SeasonFormatParam),
		"SeasonJewelryDecomposeTypeActive":                   reflect.ValueOf(goxml.SeasonJewelryDecomposeTypeActive),
		"SeasonJewelryDecomposeTypePassive":                  reflect.ValueOf(goxml.SeasonJewelryDecomposeTypePassive),
		"SeasonJewelryMaxSendNumPerPack":                     reflect.ValueOf(goxml.SeasonJewelryMaxSendNumPerPack),
		"SeasonJewelryPosMax":                                reflect.ValueOf(goxml.SeasonJewelryPosMax),
		"SeasonJewelryPosMin":                                reflect.ValueOf(goxml.SeasonJewelryPosMin),
		"SeasonJewelryRewardTypeMail":                        reflect.ValueOf(goxml.SeasonJewelryRewardTypeMail),
		"SeasonJewelryRewardTypeRest":                        reflect.ValueOf(goxml.SeasonJewelryRewardTypeRest),
		"SeasonJewelrySkillPosMax":                           reflect.ValueOf(goxml.SeasonJewelrySkillPosMax),
		"SeasonJewelrySkillPosMin":                           reflect.ValueOf(goxml.SeasonJewelrySkillPosMin),
		"SeasonJewelrySkillRandomTypeClassUp":                reflect.ValueOf(goxml.SeasonJewelrySkillRandomTypeClassUp),
		"SeasonJewelrySkillRandomTypeLevelUp":                reflect.ValueOf(goxml.SeasonJewelrySkillRandomTypeLevelUp),
		"SeasonJewelryWearHeroNumMax":                        reflect.ValueOf(goxml.SeasonJewelryWearHeroNumMax),
		"SeasonLeveTaskEventRecordFromSeasonOpen":            reflect.ValueOf(goxml.SeasonLeveTaskEventRecordFromSeasonOpen),
		"SeasonLevelTaskEventRecordFromSeasonDayOpen":        reflect.ValueOf(goxml.SeasonLevelTaskEventRecordFromSeasonDayOpen),
		"SeasonLevelUpToken":                                 reflect.ValueOf(goxml.SeasonLevelUpToken),
		"SeasonLink1RankID":                                  reflect.ValueOf(goxml.SeasonLink1RankID),
		"SeasonLink2RankID":                                  reflect.ValueOf(goxml.SeasonLink2RankID),
		"SeasonLink3RankID":                                  reflect.ValueOf(goxml.SeasonLink3RankID),
		"SeasonLink4RankID":                                  reflect.ValueOf(goxml.SeasonLink4RankID),
		"SeasonLink5RankID":                                  reflect.ValueOf(goxml.SeasonLink5RankID),
		"SeasonLink6RankID":                                  reflect.ValueOf(goxml.SeasonLink6RankID),
		"SeasonLink7RankID":                                  reflect.ValueOf(goxml.SeasonLink7RankID),
		"SeasonLink8RankID":                                  reflect.ValueOf(goxml.SeasonLink8RankID),
		"SeasonLinkPriorityNone":                             reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"SeasonLinkPriorityOne":                              reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"SeasonLinkPriorityTwo":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"SeasonMapCurrency":                                  reflect.ValueOf(goxml.SeasonMapCurrency),
		"SeasonMapExploreRate":                               reflect.ValueOf(goxml.SeasonMapExploreRate),
		"SeasonMapFightStageID":                              reflect.ValueOf(goxml.SeasonMapFightStageID),
		"SeasonPowerRankID":                                  reflect.ValueOf(goxml.SeasonPowerRankID),
		"SeasonRemainBookExpRankID":                          reflect.ValueOf(goxml.SeasonRemainBookExpRankID),
		"SeasonShopCalcActivityTime":                         reflect.ValueOf(goxml.SeasonShopCalcActivityTime),
		"SecondPerMinute":                                    reflect.ValueOf(constant.MakeFromLiteral("60", token.INT, 0)),
		"SelectSummonMaxGuaranteeLen":                        reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"SelectSummonOrangeGuaranteeIndex":                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"SelectSummonPurpleGuaranteeIndex":                   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"SelectSummonRedFragmentGuaranteeIndex":              reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"SelectSummonRedGuaranteeIndex":                      reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"ServerStatusFluency":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ServerStatusFull":                                   reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"ServerStatusHot":                                    reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ServerStatusMaintain":                               reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"ShareEmblemCount":                                   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ShieldTypeArtifact":                                 reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"ShieldTypeArtifactFragment":                         reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"ShieldTypeHero":                                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"ShieldTypeHeroFragment":                             reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"ShopExtTypeCarnival":                                reflect.ValueOf(goxml.ShopExtTypeCarnival),
		"ShopExtTypeDebut":                                   reflect.ValueOf(goxml.ShopExtTypeDebut),
		"ShopGoodsArtifactDebutLimit":                        reflect.ValueOf(goxml.ShopGoodsArtifactDebutLimit),
		"ShopGoodsArtifactOwnershipAdvancedMazeToken":        reflect.ValueOf(goxml.ShopGoodsArtifactOwnershipAdvancedMazeToken),
		"ShopGoodsDisorderLandAllMapHurdleLevelLimit":        reflect.ValueOf(goxml.ShopGoodsDisorderLandAllMapHurdleLevelLimit),
		"ShopGoodsDungeonLimit":                              reflect.ValueOf(goxml.ShopGoodsDungeonLimit),
		"ShopGoodsGuildLevelLimit":                           reflect.ValueOf(goxml.ShopGoodsGuildLevelLimit),
		"ShopGoodsInitCap":                                   reflect.ValueOf(goxml.ShopGoodsInitCap),
		"ShopGoodsLimitType2MirageCopyID":                    reflect.ValueOf(&goxml.ShopGoodsLimitType2MirageCopyID).Elem(),
		"ShopGoodsMirageDemonTopLevelLimit":                  reflect.ValueOf(goxml.ShopGoodsMirageDemonTopLevelLimit),
		"ShopGoodsMirageEmpireTopLevelLimit":                 reflect.ValueOf(goxml.ShopGoodsMirageEmpireTopLevelLimit),
		"ShopGoodsMirageForestTopLevelLimit":                 reflect.ValueOf(goxml.ShopGoodsMirageForestTopLevelLimit),
		"ShopGoodsMirageMoonTopLevelLimit":                   reflect.ValueOf(goxml.ShopGoodsMirageMoonTopLevelLimit),
		"ShopGoodsMirageProtossTopLevelLimit":                reflect.ValueOf(goxml.ShopGoodsMirageProtossTopLevelLimit),
		"ShopGoodsMirageSixTopLevelLimit":                    reflect.ValueOf(goxml.ShopGoodsMirageSixTopLevelLimit),
		"ShopGoodsMirageTopLevelLimit":                       reflect.ValueOf(goxml.ShopGoodsMirageTopLevelLimit),
		"ShopGoodsNoLimit":                                   reflect.ValueOf(goxml.ShopGoodsNoLimit),
		"ShopGoodsSeasonDoorPassLevelLimit1":                 reflect.ValueOf(goxml.ShopGoodsSeasonDoorPassLevelLimit1),
		"ShopGoodsSeasonDoorPassLevelLimit2":                 reflect.ValueOf(goxml.ShopGoodsSeasonDoorPassLevelLimit2),
		"ShopGoodsSeasonMapExploreLimit":                     reflect.ValueOf(goxml.ShopGoodsSeasonMapExploreLimit),
		"ShopGoodsSkinNotRepeatOwnership":                    reflect.ValueOf(goxml.ShopGoodsSkinNotRepeatOwnership),
		"ShopGoodsUserLevelLimit":                            reflect.ValueOf(goxml.ShopGoodsUserLevelLimit),
		"ShopGoodsVipLevelLimit":                             reflect.ValueOf(goxml.ShopGoodsVipLevelLimit),
		"ShopLegalBuyLimitType":                              reflect.ValueOf(&goxml.ShopLegalBuyLimitType).Elem(),
		"ShopLimitTypeDaily":                                 reflect.ValueOf(goxml.ShopLimitTypeDaily),
		"ShopLimitTypeForever":                               reflect.ValueOf(goxml.ShopLimitTypeForever),
		"ShopLimitTypeMonth":                                 reflect.ValueOf(goxml.ShopLimitTypeMonth),
		"ShopLimitTypeNo":                                    reflect.ValueOf(goxml.ShopLimitTypeNo),
		"ShopLimitTypeWeekly":                                reflect.ValueOf(goxml.ShopLimitTypeWeekly),
		"SkillPosActive1":                                    reflect.ValueOf(goxml.SkillPosActive1),
		"SkillPosActive2":                                    reflect.ValueOf(goxml.SkillPosActive2),
		"SkillPosAwake":                                      reflect.ValueOf(goxml.SkillPosAwake),
		"SkillPosMax":                                        reflect.ValueOf(goxml.SkillPosMax),
		"SkillPosNormal":                                     reflect.ValueOf(goxml.SkillPosNormal),
		"SkillPosOther":                                      reflect.ValueOf(goxml.SkillPosOther),
		"SkillPosPassive1":                                   reflect.ValueOf(goxml.SkillPosPassive1),
		"SkillPosPassive2":                                   reflect.ValueOf(goxml.SkillPosPassive2),
		"SkillPosPassive3":                                   reflect.ValueOf(goxml.SkillPosPassive3),
		"SkillPosSeasonAddHero":                              reflect.ValueOf(goxml.SkillPosSeasonAddHero),
		"SpecialNumberTypePool":                              reflect.ValueOf(&goxml.SpecialNumberTypePool).Elem(),
		"StageRankSeasonCompliance":                          reflect.ValueOf(&goxml.StageRankSeasonCompliance).Elem(),
		"StarLimitMinStarLv":                                 reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"Store":                                              reflect.ValueOf(goxml.Store),
		"StringDateToTime":                                   reflect.ValueOf(goxml.StringDateToTime),
		"SubFuncFlowerOccupy":                                reflect.ValueOf(goxml.SubFuncFlowerOccupy),
		"SubFuncFlowerPlat":                                  reflect.ValueOf(goxml.SubFuncFlowerPlat),
		"SubFuncNone":                                        reflect.ValueOf(goxml.SubFuncNone),
		"SummonActivityNew":                                  reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"SummonActivityNormal":                               reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"SummonGroupFuncID":                                  reflect.ValueOf(&goxml.SummonGroupFuncID).Elem(),
		"SummonGroupRoundActivity":                           reflect.ValueOf(&goxml.SummonGroupRoundActivity).Elem(),
		"SummonNeedShowLeftCountGroup":                       reflect.ValueOf(&goxml.SummonNeedShowLeftCountGroup).Elem(),
		"Sunday":                                             reflect.ValueOf(goxml.Sunday),
		"TIME_LAYOUT":                                        reflect.ValueOf(constant.MakeFromLiteral("\"2006-01-02 15:04:05\"", token.STRING, 0)),
		"TalentTreeEggBlessing":                              reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"TalentTreeFunc":                                     reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TalentTreeNodeLevelAdditionGstDragonTeamNum":        reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"TalentTreeNodeLevelAdditionGstTeamNum":              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TalentTreeNodeLevelAdditionLinkHeroNumReduce":       reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TalentTreeNodeLevelAdditionPassive":                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonDoorLineNum":       reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonJewelry":           reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonMapFight":          reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonMapSellPriceDown":  reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonMapSellPriceUp":    reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonMapStaminaMax":     reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonMapStaminaRecover": reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"TalentTreeNodeLevelAdditionSeasonMapTokenRecover":   reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"TalentTreeRoot":                                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TalentTreeRootLevelRank":                            reflect.ValueOf(goxml.TalentTreeRootLevelRank),
		"TalentTreeSeasonMap":                                reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"TalentTreeSeasonTeam":                               reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TeamMaxPos":                                         reflect.ValueOf(goxml.TeamMaxPos),
		"Thursday":                                           reflect.ValueOf(goxml.Thursday),
		"TitleTimeTypeDuration":                              reflect.ValueOf(goxml.TitleTimeTypeDuration),
		"TitleTimeTypeEndTime":                               reflect.ValueOf(goxml.TitleTimeTypeEndTime),
		"TitleTimeTypeForever":                               reflect.ValueOf(goxml.TitleTimeTypeForever),
		"TotalRecoveryReward":                                reflect.ValueOf(goxml.TotalRecoveryReward),
		"TowerInitFloorCount":                                reflect.ValueOf(goxml.TowerInitFloorCount),
		"TowerLegalType":                                     reflect.ValueOf(&goxml.TowerLegalType).Elem(),
		"TowerPassAwardsNum":                                 reflect.ValueOf(goxml.TowerPassAwardsNum),
		"TowerRankId":                                        reflect.ValueOf(goxml.TowerRankId),
		"TowerSeasonRankID":                                  reflect.ValueOf(goxml.TowerSeasonRankID),
		"TowerSweepAwardsNum":                                reflect.ValueOf(goxml.TowerSweepAwardsNum),
		"TowerTypeMain":                                      reflect.ValueOf(goxml.TowerTypeMain),
		"TowerstarRankId":                                    reflect.ValueOf(goxml.TowerstarRankId),
		"TrialOnhookAwardCount":                              reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TriggerCondition_AttackHurtType":                    reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TriggerCondition_AttackIsElite":                     reflect.ValueOf(constant.MakeFromLiteral("19", token.INT, 0)),
		"TriggerCondition_BuffBenefit":                       reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"TriggerCondition_BuffEffectType":                    reflect.ValueOf(constant.MakeFromLiteral("11", token.INT, 0)),
		"TriggerCondition_BuffExist":                         reflect.ValueOf(constant.MakeFromLiteral("17", token.INT, 0)),
		"TriggerCondition_BuffID":                            reflect.ValueOf(constant.MakeFromLiteral("18", token.INT, 0)),
		"TriggerCondition_BuffIsControl":                     reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"TriggerCondition_BuffType":                          reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"TriggerCondition_HurtType":                          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"TriggerCondition_Max":                               reflect.ValueOf(constant.MakeFromLiteral("20", token.INT, 0)),
		"TriggerCondition_MemberRelation":                    reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"TriggerCondition_None":                              reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"TriggerCondition_PurifyOrDispel":                    reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"TriggerCondition_SeasonLinkEnergyValue":             reflect.ValueOf(constant.MakeFromLiteral("15", token.INT, 0)),
		"TriggerCondition_SkillAttackAddBuffFactor":          reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"TriggerCondition_SkillAttackFactor":                 reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"TriggerCondition_SkillAttackType":                   reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TriggerCondition_SkillEffect":                       reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"TriggerCondition_SkillType":                         reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TriggerCondition_SkillType2":                        reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"TriggerCondition_Source":                            reflect.ValueOf(constant.MakeFromLiteral("13", token.INT, 0)),
		"TriggerTarget_None":                                 reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"TriggerTarget_OpTeamAll":                            reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"TriggerTarget_OpTeamUniter":                         reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"TriggerTarget_Self":                                 reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TriggerTarget_TeamAll":                              reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TriggerTarget_TeamAll2":                             reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"TriggerTarget_TeamOther":                            reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TriggerTarget_TeamUniter":                           reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"Tuesday":                                            reflect.ValueOf(goxml.Tuesday),
		"TurnTableDisposable":                                reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TurnTableEffectAddGuarantee":                        reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"TurnTableEffectAward":                               reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"TurnTableEffectDropGroupRandom":                     reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"TurnTableEffectMax":                                 reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"TurnTableEffectMulti":                               reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"TurnTableEffectNone":                                reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"TurnTableEffectTargetId":                            reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"TurnTableNormal":                                    reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"TwoTeamRequireMinHeroCount":                         reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"TypeDrop":                                           reflect.ValueOf(goxml.TypeDrop),
		"TypeRandom":                                         reflect.ValueOf(goxml.TypeRandom),
		"VipPrivilegeShopId":                                 reflect.ValueOf(constant.MakeFromLiteral("102", token.INT, 0)),
		"Wednesday":                                          reflect.ValueOf(goxml.Wednesday),
		"WeekDayNum":                                         reflect.ValueOf(goxml.WeekDayNum),
		"WeeklyTaskTypeID":                                   reflect.ValueOf(goxml.WeeklyTaskTypeID),
		"WishListGroup":                                      reflect.ValueOf(goxml.WishListGroup),
		"WishListSlotMaxCnt":                                 reflect.ValueOf(goxml.WishListSlotMaxCnt),
		"WorldBossFightCostTypeFightCount":                   reflect.ValueOf(goxml.WorldBossFightCostTypeFightCount),
		"WorldBossFightCostTypeFightRoll":                    reflect.ValueOf(goxml.WorldBossFightCostTypeFightRoll),
		"WorldBossGetRoomLogMaxNum":                          reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"WorldBossRankID":                                    reflect.ValueOf(goxml.WorldBossRankID),
		"WorldBossRoomPlayerNum":                             reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"WrestleBotNumPerLevel":                              reflect.ValueOf(goxml.WrestleBotNumPerLevel),
		"WrestleChampionAvatar1":                             reflect.ValueOf(goxml.WrestleChampionAvatar1),
		"WrestleChampionAvatar10":                            reflect.ValueOf(goxml.WrestleChampionAvatar10),
		"WrestleChampionAvatar3":                             reflect.ValueOf(goxml.WrestleChampionAvatar3),
		"WrestleChampionAvatarList":                          reflect.ValueOf(&goxml.WrestleChampionAvatarList).Elem(),
		"WrestleCommonRoomCapacity":                          reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"WrestleCommonRoomTopScore":                          reflect.ValueOf(goxml.WrestleCommonRoomTopScore),
		"WrestleLogMaxNum":                                   reflect.ValueOf(goxml.WrestleLogMaxNum),
		"WrestleRankID":                                      reflect.ValueOf(goxml.WrestleRankID),
		"WrestleRewardType":                                  reflect.ValueOf(&goxml.WrestleRewardType).Elem(),
		"WrestleRewardTypeDaily":                             reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"WrestleRewardTypeSeason":                            reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"WrestleSeasonDuration":                              reflect.ValueOf(goxml.WrestleSeasonDuration),
		"WrestleTopFighterCount":                             reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"WrestleTopLevel":                                    reflect.ValueOf(goxml.WrestleTopLevel),
		"WrestleTopRoomEnemyLimit":                           reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"WrestleTopRoomInitCapacity":                         reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),

		// type definitions
		"ActivityComplianceInfo":                               reflect.ValueOf((*goxml.ActivityComplianceInfo)(nil)),
		"ActivityComplianceInfoManager":                        reflect.ValueOf((*goxml.ActivityComplianceInfoManager)(nil)),
		"ActivityComplianceInfos":                              reflect.ValueOf((*goxml.ActivityComplianceInfos)(nil)),
		"ActivityComplianceRankInfo":                           reflect.ValueOf((*goxml.ActivityComplianceRankInfo)(nil)),
		"ActivityComplianceRankInfoManager":                    reflect.ValueOf((*goxml.ActivityComplianceRankInfoManager)(nil)),
		"ActivityComplianceRankInfos":                          reflect.ValueOf((*goxml.ActivityComplianceRankInfos)(nil)),
		"ActivityCouponInfo":                                   reflect.ValueOf((*goxml.ActivityCouponInfo)(nil)),
		"ActivityCouponInfos":                                  reflect.ValueOf((*goxml.ActivityCouponInfos)(nil)),
		"ActivityDailySpecialInfo":                             reflect.ValueOf((*goxml.ActivityDailySpecialInfo)(nil)),
		"ActivityDailySpecialInfoManager":                      reflect.ValueOf((*goxml.ActivityDailySpecialInfoManager)(nil)),
		"ActivityDailySpecialInfos":                            reflect.ValueOf((*goxml.ActivityDailySpecialInfos)(nil)),
		"ActivityFeedingConditionInfo":                         reflect.ValueOf((*goxml.ActivityFeedingConditionInfo)(nil)),
		"ActivityFeedingConditionInfoManager":                  reflect.ValueOf((*goxml.ActivityFeedingConditionInfoManager)(nil)),
		"ActivityFeedingConditionInfos":                        reflect.ValueOf((*goxml.ActivityFeedingConditionInfos)(nil)),
		"ActivityFeedingGiftsInfo":                             reflect.ValueOf((*goxml.ActivityFeedingGiftsInfo)(nil)),
		"ActivityFeedingGiftsInfoManager":                      reflect.ValueOf((*goxml.ActivityFeedingGiftsInfoManager)(nil)),
		"ActivityFeedingGiftsInfos":                            reflect.ValueOf((*goxml.ActivityFeedingGiftsInfos)(nil)),
		"ActivityFeedingInfo":                                  reflect.ValueOf((*goxml.ActivityFeedingInfo)(nil)),
		"ActivityFeedingInfoManager":                           reflect.ValueOf((*goxml.ActivityFeedingInfoManager)(nil)),
		"ActivityFeedingInfos":                                 reflect.ValueOf((*goxml.ActivityFeedingInfos)(nil)),
		"ActivityGoddessInfo":                                  reflect.ValueOf((*goxml.ActivityGoddessInfo)(nil)),
		"ActivityGoddessInfoManager":                           reflect.ValueOf((*goxml.ActivityGoddessInfoManager)(nil)),
		"ActivityGoddessInfos":                                 reflect.ValueOf((*goxml.ActivityGoddessInfos)(nil)),
		"ActivityIndexI":                                       reflect.ValueOf((*goxml.ActivityIndexI)(nil)),
		"ActivityLifelongGiftInfo":                             reflect.ValueOf((*goxml.ActivityLifelongGiftInfo)(nil)),
		"ActivityLifelongGiftInfoManager":                      reflect.ValueOf((*goxml.ActivityLifelongGiftInfoManager)(nil)),
		"ActivityLifelongGiftInfos":                            reflect.ValueOf((*goxml.ActivityLifelongGiftInfos)(nil)),
		"ActivityLinkSummonInfo":                               reflect.ValueOf((*goxml.ActivityLinkSummonInfo)(nil)),
		"ActivityLinkSummonInfoManager":                        reflect.ValueOf((*goxml.ActivityLinkSummonInfoManager)(nil)),
		"ActivityLinkSummonInfos":                              reflect.ValueOf((*goxml.ActivityLinkSummonInfos)(nil)),
		"ActivityMirageAwardInfo":                              reflect.ValueOf((*goxml.ActivityMirageAwardInfo)(nil)),
		"ActivityMirageRankInfo":                               reflect.ValueOf((*goxml.ActivityMirageRankInfo)(nil)),
		"ActivityMirageRankInfoManager":                        reflect.ValueOf((*goxml.ActivityMirageRankInfoManager)(nil)),
		"ActivityMirageRankInfos":                              reflect.ValueOf((*goxml.ActivityMirageRankInfos)(nil)),
		"ActivityMonthGroupInfo":                               reflect.ValueOf((*goxml.ActivityMonthGroupInfo)(nil)),
		"ActivityMonthGroupInfoManager":                        reflect.ValueOf((*goxml.ActivityMonthGroupInfoManager)(nil)),
		"ActivityMonthGroupInfos":                              reflect.ValueOf((*goxml.ActivityMonthGroupInfos)(nil)),
		"ActivityMonthInfo":                                    reflect.ValueOf((*goxml.ActivityMonthInfo)(nil)),
		"ActivityMonthInfoManager":                             reflect.ValueOf((*goxml.ActivityMonthInfoManager)(nil)),
		"ActivityMonthInfos":                                   reflect.ValueOf((*goxml.ActivityMonthInfos)(nil)),
		"ActivityMonthTasksInfo":                               reflect.ValueOf((*goxml.ActivityMonthTasksInfo)(nil)),
		"ActivityMonthTasksInfoExt":                            reflect.ValueOf((*goxml.ActivityMonthTasksInfoExt)(nil)),
		"ActivityMonthTasksInfoManager":                        reflect.ValueOf((*goxml.ActivityMonthTasksInfoManager)(nil)),
		"ActivityMonthTasksInfos":                              reflect.ValueOf((*goxml.ActivityMonthTasksInfos)(nil)),
		"ActivityNameCheck":                                    reflect.ValueOf((*goxml.ActivityNameCheck)(nil)),
		"ActivityPushGiftGroupInfoManager":                     reflect.ValueOf((*goxml.ActivityPushGiftGroupInfoManager)(nil)),
		"ActivityPushGiftInfoManager":                          reflect.ValueOf((*goxml.ActivityPushGiftInfoManager)(nil)),
		"ActivityPushgiftConfigInfo":                           reflect.ValueOf((*goxml.ActivityPushgiftConfigInfo)(nil)),
		"ActivityPushgiftConfigInfoManager":                    reflect.ValueOf((*goxml.ActivityPushgiftConfigInfoManager)(nil)),
		"ActivityPushgiftConfigInfos":                          reflect.ValueOf((*goxml.ActivityPushgiftConfigInfos)(nil)),
		"ActivityPushgiftGroupInfo":                            reflect.ValueOf((*goxml.ActivityPushgiftGroupInfo)(nil)),
		"ActivityPushgiftGroupInfoExt":                         reflect.ValueOf((*goxml.ActivityPushgiftGroupInfoExt)(nil)),
		"ActivityPushgiftGroupInfos":                           reflect.ValueOf((*goxml.ActivityPushgiftGroupInfos)(nil)),
		"ActivityPushgiftInfo":                                 reflect.ValueOf((*goxml.ActivityPushgiftInfo)(nil)),
		"ActivityPushgiftInfos":                                reflect.ValueOf((*goxml.ActivityPushgiftInfos)(nil)),
		"ActivityPuzzleAimInfo":                                reflect.ValueOf((*goxml.ActivityPuzzleAimInfo)(nil)),
		"ActivityPuzzleAimInfoExt":                             reflect.ValueOf((*goxml.ActivityPuzzleAimInfoExt)(nil)),
		"ActivityPuzzleAimInfoManager":                         reflect.ValueOf((*goxml.ActivityPuzzleAimInfoManager)(nil)),
		"ActivityPuzzleAimInfos":                               reflect.ValueOf((*goxml.ActivityPuzzleAimInfos)(nil)),
		"ActivityPuzzleCopyInfo":                               reflect.ValueOf((*goxml.ActivityPuzzleCopyInfo)(nil)),
		"ActivityPuzzleCopyInfoExt":                            reflect.ValueOf((*goxml.ActivityPuzzleCopyInfoExt)(nil)),
		"ActivityPuzzleCopyInfoManager":                        reflect.ValueOf((*goxml.ActivityPuzzleCopyInfoManager)(nil)),
		"ActivityPuzzleCopyInfos":                              reflect.ValueOf((*goxml.ActivityPuzzleCopyInfos)(nil)),
		"ActivityPuzzleInfo":                                   reflect.ValueOf((*goxml.ActivityPuzzleInfo)(nil)),
		"ActivityPuzzleInfoManager":                            reflect.ValueOf((*goxml.ActivityPuzzleInfoManager)(nil)),
		"ActivityPuzzleInfos":                                  reflect.ValueOf((*goxml.ActivityPuzzleInfos)(nil)),
		"ActivityPyramidChooseInfo":                            reflect.ValueOf((*goxml.ActivityPyramidChooseInfo)(nil)),
		"ActivityPyramidChooseInfoM":                           reflect.ValueOf((*goxml.ActivityPyramidChooseInfoM)(nil)),
		"ActivityPyramidChooseInfoMEx":                         reflect.ValueOf((*goxml.ActivityPyramidChooseInfoMEx)(nil)),
		"ActivityPyramidChooseInfoMGroupIndex":                 reflect.ValueOf((*goxml.ActivityPyramidChooseInfoMGroupIndex)(nil)),
		"ActivityPyramidChooseInfoXml":                         reflect.ValueOf((*goxml.ActivityPyramidChooseInfoXml)(nil)),
		"ActivityPyramidChooseInfoXmls":                        reflect.ValueOf((*goxml.ActivityPyramidChooseInfoXmls)(nil)),
		"ActivityPyramidInfo":                                  reflect.ValueOf((*goxml.ActivityPyramidInfo)(nil)),
		"ActivityPyramidInfoM":                                 reflect.ValueOf((*goxml.ActivityPyramidInfoM)(nil)),
		"ActivityPyramidInfoXml":                               reflect.ValueOf((*goxml.ActivityPyramidInfoXml)(nil)),
		"ActivityPyramidInfoXmls":                              reflect.ValueOf((*goxml.ActivityPyramidInfoXmls)(nil)),
		"ActivityPyramidLatticeInfo":                           reflect.ValueOf((*goxml.ActivityPyramidLatticeInfo)(nil)),
		"ActivityPyramidLatticeInfoM":                          reflect.ValueOf((*goxml.ActivityPyramidLatticeInfoM)(nil)),
		"ActivityPyramidLatticeInfoMEx":                        reflect.ValueOf((*goxml.ActivityPyramidLatticeInfoMEx)(nil)),
		"ActivityPyramidLatticeInfoMFloorIndex":                reflect.ValueOf((*goxml.ActivityPyramidLatticeInfoMFloorIndex)(nil)),
		"ActivityPyramidLatticeInfoXml":                        reflect.ValueOf((*goxml.ActivityPyramidLatticeInfoXml)(nil)),
		"ActivityPyramidLatticeInfoXmls":                       reflect.ValueOf((*goxml.ActivityPyramidLatticeInfoXmls)(nil)),
		"ActivityPyramidTaskInfo":                              reflect.ValueOf((*goxml.ActivityPyramidTaskInfo)(nil)),
		"ActivityPyramidTaskInfoM":                             reflect.ValueOf((*goxml.ActivityPyramidTaskInfoM)(nil)),
		"ActivityPyramidTaskInfoMEx":                           reflect.ValueOf((*goxml.ActivityPyramidTaskInfoMEx)(nil)),
		"ActivityPyramidTaskInfoXml":                           reflect.ValueOf((*goxml.ActivityPyramidTaskInfoXml)(nil)),
		"ActivityPyramidTaskInfoXmls":                          reflect.ValueOf((*goxml.ActivityPyramidTaskInfoXmls)(nil)),
		"ActivityRankTimelimitInfo":                            reflect.ValueOf((*goxml.ActivityRankTimelimitInfo)(nil)),
		"ActivityRankTimelimitInfoManager":                     reflect.ValueOf((*goxml.ActivityRankTimelimitInfoManager)(nil)),
		"ActivityRankTimelimitInfos":                           reflect.ValueOf((*goxml.ActivityRankTimelimitInfos)(nil)),
		"ActivityRechargeGiftInfo":                             reflect.ValueOf((*goxml.ActivityRechargeGiftInfo)(nil)),
		"ActivityRechargeGiftInfoExt":                          reflect.ValueOf((*goxml.ActivityRechargeGiftInfoExt)(nil)),
		"ActivityRechargeGiftInfoManager":                      reflect.ValueOf((*goxml.ActivityRechargeGiftInfoManager)(nil)),
		"ActivityRechargeGiftInfos":                            reflect.ValueOf((*goxml.ActivityRechargeGiftInfos)(nil)),
		"ActivityRechargeShopInfo":                             reflect.ValueOf((*goxml.ActivityRechargeShopInfo)(nil)),
		"ActivityRechargeShopInfoManager":                      reflect.ValueOf((*goxml.ActivityRechargeShopInfoManager)(nil)),
		"ActivityRechargeShopInfos":                            reflect.ValueOf((*goxml.ActivityRechargeShopInfos)(nil)),
		"ActivityReturnConfigInfo":                             reflect.ValueOf((*goxml.ActivityReturnConfigInfo)(nil)),
		"ActivityReturnConfigInfoManager":                      reflect.ValueOf((*goxml.ActivityReturnConfigInfoManager)(nil)),
		"ActivityReturnConfigInfos":                            reflect.ValueOf((*goxml.ActivityReturnConfigInfos)(nil)),
		"ActivityReturnInfo":                                   reflect.ValueOf((*goxml.ActivityReturnInfo)(nil)),
		"ActivityReturnInfoExt":                                reflect.ValueOf((*goxml.ActivityReturnInfoExt)(nil)),
		"ActivityReturnInfoManager":                            reflect.ValueOf((*goxml.ActivityReturnInfoManager)(nil)),
		"ActivityReturnInfos":                                  reflect.ValueOf((*goxml.ActivityReturnInfos)(nil)),
		"ActivityReturnLoginInfo":                              reflect.ValueOf((*goxml.ActivityReturnLoginInfo)(nil)),
		"ActivityReturnLoginInfoManager":                       reflect.ValueOf((*goxml.ActivityReturnLoginInfoManager)(nil)),
		"ActivityReturnLoginInfos":                             reflect.ValueOf((*goxml.ActivityReturnLoginInfos)(nil)),
		"ActivitySeasonShopGoodsInfo":                          reflect.ValueOf((*goxml.ActivitySeasonShopGoodsInfo)(nil)),
		"ActivitySeasonShopGoodsInfoM":                         reflect.ValueOf((*goxml.ActivitySeasonShopGoodsInfoM)(nil)),
		"ActivitySeasonShopGoodsInfoMEx":                       reflect.ValueOf((*goxml.ActivitySeasonShopGoodsInfoMEx)(nil)),
		"ActivitySeasonShopGoodsInfoXml":                       reflect.ValueOf((*goxml.ActivitySeasonShopGoodsInfoXml)(nil)),
		"ActivitySeasonShopGoodsInfoXmls":                      reflect.ValueOf((*goxml.ActivitySeasonShopGoodsInfoXmls)(nil)),
		"ActivitySeasonShopInfo":                               reflect.ValueOf((*goxml.ActivitySeasonShopInfo)(nil)),
		"ActivitySeasonShopInfoM":                              reflect.ValueOf((*goxml.ActivitySeasonShopInfoM)(nil)),
		"ActivitySeasonShopInfoMEx":                            reflect.ValueOf((*goxml.ActivitySeasonShopInfoMEx)(nil)),
		"ActivitySeasonShopInfoXml":                            reflect.ValueOf((*goxml.ActivitySeasonShopInfoXml)(nil)),
		"ActivitySeasonShopInfoXmls":                           reflect.ValueOf((*goxml.ActivitySeasonShopInfoXmls)(nil)),
		"ActivitySevenDayInfo":                                 reflect.ValueOf((*goxml.ActivitySevenDayInfo)(nil)),
		"ActivitySevenDayInfoManager":                          reflect.ValueOf((*goxml.ActivitySevenDayInfoManager)(nil)),
		"ActivitySevenDayInfos":                                reflect.ValueOf((*goxml.ActivitySevenDayInfos)(nil)),
		"ActivityStoryDungeonInfo":                             reflect.ValueOf((*goxml.ActivityStoryDungeonInfo)(nil)),
		"ActivityStoryDungeonInfoManager":                      reflect.ValueOf((*goxml.ActivityStoryDungeonInfoManager)(nil)),
		"ActivityStoryDungeonInfos":                            reflect.ValueOf((*goxml.ActivityStoryDungeonInfos)(nil)),
		"ActivityStoryInfo":                                    reflect.ValueOf((*goxml.ActivityStoryInfo)(nil)),
		"ActivityStoryInfoExt":                                 reflect.ValueOf((*goxml.ActivityStoryInfoExt)(nil)),
		"ActivityStoryInfoManager":                             reflect.ValueOf((*goxml.ActivityStoryInfoManager)(nil)),
		"ActivityStoryInfos":                                   reflect.ValueOf((*goxml.ActivityStoryInfos)(nil)),
		"ActivityStoryLayerInfo":                               reflect.ValueOf((*goxml.ActivityStoryLayerInfo)(nil)),
		"ActivityStoryLayerInfoManager":                        reflect.ValueOf((*goxml.ActivityStoryLayerInfoManager)(nil)),
		"ActivityStoryLayerInfos":                              reflect.ValueOf((*goxml.ActivityStoryLayerInfos)(nil)),
		"ActivityStoryLoginInfo":                               reflect.ValueOf((*goxml.ActivityStoryLoginInfo)(nil)),
		"ActivityStoryLoginInfoManager":                        reflect.ValueOf((*goxml.ActivityStoryLoginInfoManager)(nil)),
		"ActivityStoryLoginInfos":                              reflect.ValueOf((*goxml.ActivityStoryLoginInfos)(nil)),
		"ActivityStoryShopInfo":                                reflect.ValueOf((*goxml.ActivityStoryShopInfo)(nil)),
		"ActivityStoryShopInfoManager":                         reflect.ValueOf((*goxml.ActivityStoryShopInfoManager)(nil)),
		"ActivityStoryShopInfos":                               reflect.ValueOf((*goxml.ActivityStoryShopInfos)(nil)),
		"ActivitySumDetail":                                    reflect.ValueOf((*goxml.ActivitySumDetail)(nil)),
		"ActivitySumInfo":                                      reflect.ValueOf((*goxml.ActivitySumInfo)(nil)),
		"ActivitySumInfoExt":                                   reflect.ValueOf((*goxml.ActivitySumInfoExt)(nil)),
		"ActivitySumInfoManager":                               reflect.ValueOf((*goxml.ActivitySumInfoManager)(nil)),
		"ActivitySumInfos":                                     reflect.ValueOf((*goxml.ActivitySumInfos)(nil)),
		"ActivityTaskTypeInfo":                                 reflect.ValueOf((*goxml.ActivityTaskTypeInfo)(nil)),
		"ActivityTaskTypeInfoManager":                          reflect.ValueOf((*goxml.ActivityTaskTypeInfoManager)(nil)),
		"ActivityTaskTypeInfos":                                reflect.ValueOf((*goxml.ActivityTaskTypeInfos)(nil)),
		"ActivityTowerAwardInfo":                               reflect.ValueOf((*goxml.ActivityTowerAwardInfo)(nil)),
		"ActivityTowerRankInfo":                                reflect.ValueOf((*goxml.ActivityTowerRankInfo)(nil)),
		"ActivityTowerRankInfoManager":                         reflect.ValueOf((*goxml.ActivityTowerRankInfoManager)(nil)),
		"ActivityTowerRankInfos":                               reflect.ValueOf((*goxml.ActivityTowerRankInfos)(nil)),
		"ActivityTurntableBuffInfo":                            reflect.ValueOf((*goxml.ActivityTurntableBuffInfo)(nil)),
		"ActivityTurntableBuffInfoManager":                     reflect.ValueOf((*goxml.ActivityTurntableBuffInfoManager)(nil)),
		"ActivityTurntableBuffInfos":                           reflect.ValueOf((*goxml.ActivityTurntableBuffInfos)(nil)),
		"ActivityTurntableInfo":                                reflect.ValueOf((*goxml.ActivityTurntableInfo)(nil)),
		"ActivityTurntableInfoExt":                             reflect.ValueOf((*goxml.ActivityTurntableInfoExt)(nil)),
		"ActivityTurntableInfoManager":                         reflect.ValueOf((*goxml.ActivityTurntableInfoManager)(nil)),
		"ActivityTurntableInfos":                               reflect.ValueOf((*goxml.ActivityTurntableInfos)(nil)),
		"ActivityTurntableRewardInfo":                          reflect.ValueOf((*goxml.ActivityTurntableRewardInfo)(nil)),
		"ActivityTurntableRewardInfoManager":                   reflect.ValueOf((*goxml.ActivityTurntableRewardInfoManager)(nil)),
		"ActivityTurntableRewardInfos":                         reflect.ValueOf((*goxml.ActivityTurntableRewardInfos)(nil)),
		"ActivityTurntableRewardShowInfo":                      reflect.ValueOf((*goxml.ActivityTurntableRewardShowInfo)(nil)),
		"ActivityTurntableRewardShowInfoManager":               reflect.ValueOf((*goxml.ActivityTurntableRewardShowInfoManager)(nil)),
		"ActivityTurntableRewardShowInfos":                     reflect.ValueOf((*goxml.ActivityTurntableRewardShowInfos)(nil)),
		"ActivityTurntableRewardShowSummonExt":                 reflect.ValueOf((*goxml.ActivityTurntableRewardShowSummonExt)(nil)),
		"ActivityTurntableTaskInfo":                            reflect.ValueOf((*goxml.ActivityTurntableTaskInfo)(nil)),
		"ActivityTurntableTaskInfoManager":                     reflect.ValueOf((*goxml.ActivityTurntableTaskInfoManager)(nil)),
		"ActivityTurntableTaskInfos":                           reflect.ValueOf((*goxml.ActivityTurntableTaskInfos)(nil)),
		"ActivityWebGiftInfo":                                  reflect.ValueOf((*goxml.ActivityWebGiftInfo)(nil)),
		"ActivityWebGiftInfoManager":                           reflect.ValueOf((*goxml.ActivityWebGiftInfoManager)(nil)),
		"ActivityWebGiftInfos":                                 reflect.ValueOf((*goxml.ActivityWebGiftInfos)(nil)),
		"ActivityWebInfo":                                      reflect.ValueOf((*goxml.ActivityWebInfo)(nil)),
		"ActivityWebInfoExt":                                   reflect.ValueOf((*goxml.ActivityWebInfoExt)(nil)),
		"ActivityWebInfoManager":                               reflect.ValueOf((*goxml.ActivityWebInfoManager)(nil)),
		"ActivityWebInfos":                                     reflect.ValueOf((*goxml.ActivityWebInfos)(nil)),
		"AheadConfig":                                          reflect.ValueOf((*goxml.AheadConfig)(nil)),
		"AimInfo":                                              reflect.ValueOf((*goxml.AimInfo)(nil)),
		"ArenaBotInfo":                                         reflect.ValueOf((*goxml.ArenaBotInfo)(nil)),
		"ArenaBotInfoExt":                                      reflect.ValueOf((*goxml.ArenaBotInfoExt)(nil)),
		"ArenaBotInfoManager":                                  reflect.ValueOf((*goxml.ArenaBotInfoManager)(nil)),
		"ArenaBotInfos":                                        reflect.ValueOf((*goxml.ArenaBotInfos)(nil)),
		"ArenaConfigInfo":                                      reflect.ValueOf((*goxml.ArenaConfigInfo)(nil)),
		"ArenaConfigInfoManager":                               reflect.ValueOf((*goxml.ArenaConfigInfoManager)(nil)),
		"ArenaConfigInfos":                                     reflect.ValueOf((*goxml.ArenaConfigInfos)(nil)),
		"ArenaDivisionInfo":                                    reflect.ValueOf((*goxml.ArenaDivisionInfo)(nil)),
		"ArenaDivisionInfoExt":                                 reflect.ValueOf((*goxml.ArenaDivisionInfoExt)(nil)),
		"ArenaDivisionInfoManager":                             reflect.ValueOf((*goxml.ArenaDivisionInfoManager)(nil)),
		"ArenaDivisionInfos":                                   reflect.ValueOf((*goxml.ArenaDivisionInfos)(nil)),
		"ArenaDivisionRewardInfo":                              reflect.ValueOf((*goxml.ArenaDivisionRewardInfo)(nil)),
		"ArenaDivisionRewardInfoExt":                           reflect.ValueOf((*goxml.ArenaDivisionRewardInfoExt)(nil)),
		"ArenaDivisionRewardInfoManager":                       reflect.ValueOf((*goxml.ArenaDivisionRewardInfoManager)(nil)),
		"ArenaDivisionRewardInfos":                             reflect.ValueOf((*goxml.ArenaDivisionRewardInfos)(nil)),
		"ArenaDivisionTaskInfo":                                reflect.ValueOf((*goxml.ArenaDivisionTaskInfo)(nil)),
		"ArenaDivisionTaskInfoExt":                             reflect.ValueOf((*goxml.ArenaDivisionTaskInfoExt)(nil)),
		"ArenaDivisionTaskInfoManager":                         reflect.ValueOf((*goxml.ArenaDivisionTaskInfoManager)(nil)),
		"ArenaDivisionTaskInfos":                               reflect.ValueOf((*goxml.ArenaDivisionTaskInfos)(nil)),
		"ArenaFightRewardInfo":                                 reflect.ValueOf((*goxml.ArenaFightRewardInfo)(nil)),
		"ArenaFightRewardInfoManager":                          reflect.ValueOf((*goxml.ArenaFightRewardInfoManager)(nil)),
		"ArenaFightRewardInfos":                                reflect.ValueOf((*goxml.ArenaFightRewardInfos)(nil)),
		"ArenaMatchInfo":                                       reflect.ValueOf((*goxml.ArenaMatchInfo)(nil)),
		"ArenaMatchInfoManager":                                reflect.ValueOf((*goxml.ArenaMatchInfoManager)(nil)),
		"ArenaMatchInfos":                                      reflect.ValueOf((*goxml.ArenaMatchInfos)(nil)),
		"ArenaPraiseInfo":                                      reflect.ValueOf((*goxml.ArenaPraiseInfo)(nil)),
		"ArenaPraiseInfoExt":                                   reflect.ValueOf((*goxml.ArenaPraiseInfoExt)(nil)),
		"ArenaPraiseInfoManager":                               reflect.ValueOf((*goxml.ArenaPraiseInfoManager)(nil)),
		"ArenaPraiseInfos":                                     reflect.ValueOf((*goxml.ArenaPraiseInfos)(nil)),
		"ArenaRankRewardInfo":                                  reflect.ValueOf((*goxml.ArenaRankRewardInfo)(nil)),
		"ArenaRankRewardInfos":                                 reflect.ValueOf((*goxml.ArenaRankRewardInfos)(nil)),
		"ArtifactDebutActivityInfo":                            reflect.ValueOf((*goxml.ArtifactDebutActivityInfo)(nil)),
		"ArtifactDebutActivityInfoManager":                     reflect.ValueOf((*goxml.ArtifactDebutActivityInfoManager)(nil)),
		"ArtifactDebutActivityInfos":                           reflect.ValueOf((*goxml.ArtifactDebutActivityInfos)(nil)),
		"ArtifactDebutConfigInfo":                              reflect.ValueOf((*goxml.ArtifactDebutConfigInfo)(nil)),
		"ArtifactDebutConfigInfoManager":                       reflect.ValueOf((*goxml.ArtifactDebutConfigInfoManager)(nil)),
		"ArtifactDebutConfigInfos":                             reflect.ValueOf((*goxml.ArtifactDebutConfigInfos)(nil)),
		"ArtifactDebutDrawCategoryInfo":                        reflect.ValueOf((*goxml.ArtifactDebutDrawCategoryInfo)(nil)),
		"ArtifactDebutDrawCategoryInfoManager":                 reflect.ValueOf((*goxml.ArtifactDebutDrawCategoryInfoManager)(nil)),
		"ArtifactDebutDrawCategoryInfos":                       reflect.ValueOf((*goxml.ArtifactDebutDrawCategoryInfos)(nil)),
		"ArtifactDebutDrawDropInfo":                            reflect.ValueOf((*goxml.ArtifactDebutDrawDropInfo)(nil)),
		"ArtifactDebutDrawDropInfoExt":                         reflect.ValueOf((*goxml.ArtifactDebutDrawDropInfoExt)(nil)),
		"ArtifactDebutDrawDropInfoManager":                     reflect.ValueOf((*goxml.ArtifactDebutDrawDropInfoManager)(nil)),
		"ArtifactDebutDrawDropInfos":                           reflect.ValueOf((*goxml.ArtifactDebutDrawDropInfos)(nil)),
		"ArtifactDebutDrawGroupInfo":                           reflect.ValueOf((*goxml.ArtifactDebutDrawGroupInfo)(nil)),
		"ArtifactDebutDrawGroupInfoManager":                    reflect.ValueOf((*goxml.ArtifactDebutDrawGroupInfoManager)(nil)),
		"ArtifactDebutDrawGroupInfos":                          reflect.ValueOf((*goxml.ArtifactDebutDrawGroupInfos)(nil)),
		"ArtifactDebutDrawOptionalInfo":                        reflect.ValueOf((*goxml.ArtifactDebutDrawOptionalInfo)(nil)),
		"ArtifactDebutDrawOptionalInfoManager":                 reflect.ValueOf((*goxml.ArtifactDebutDrawOptionalInfoManager)(nil)),
		"ArtifactDebutDrawOptionalInfos":                       reflect.ValueOf((*goxml.ArtifactDebutDrawOptionalInfos)(nil)),
		"ArtifactDebutDrawRewardInfo":                          reflect.ValueOf((*goxml.ArtifactDebutDrawRewardInfo)(nil)),
		"ArtifactDebutDrawRewardInfoExt":                       reflect.ValueOf((*goxml.ArtifactDebutDrawRewardInfoExt)(nil)),
		"ArtifactDebutDrawRewardInfoManager":                   reflect.ValueOf((*goxml.ArtifactDebutDrawRewardInfoManager)(nil)),
		"ArtifactDebutDrawRewardInfos":                         reflect.ValueOf((*goxml.ArtifactDebutDrawRewardInfos)(nil)),
		"ArtifactDebutGuaranteeInfo":                           reflect.ValueOf((*goxml.ArtifactDebutGuaranteeInfo)(nil)),
		"ArtifactDebutGuaranteeInfoManager":                    reflect.ValueOf((*goxml.ArtifactDebutGuaranteeInfoManager)(nil)),
		"ArtifactDebutGuaranteeInfos":                          reflect.ValueOf((*goxml.ArtifactDebutGuaranteeInfos)(nil)),
		"ArtifactDebutLoginRewardInfo":                         reflect.ValueOf((*goxml.ArtifactDebutLoginRewardInfo)(nil)),
		"ArtifactDebutLoginRewardInfoManager":                  reflect.ValueOf((*goxml.ArtifactDebutLoginRewardInfoManager)(nil)),
		"ArtifactDebutLoginRewardInfos":                        reflect.ValueOf((*goxml.ArtifactDebutLoginRewardInfos)(nil)),
		"ArtifactDebutPuzzleRewardInfo":                        reflect.ValueOf((*goxml.ArtifactDebutPuzzleRewardInfo)(nil)),
		"ArtifactDebutPuzzleRewardInfoManager":                 reflect.ValueOf((*goxml.ArtifactDebutPuzzleRewardInfoManager)(nil)),
		"ArtifactDebutPuzzleRewardInfos":                       reflect.ValueOf((*goxml.ArtifactDebutPuzzleRewardInfos)(nil)),
		"ArtifactDebutTaskInfo":                                reflect.ValueOf((*goxml.ArtifactDebutTaskInfo)(nil)),
		"ArtifactDebutTaskInfoManager":                         reflect.ValueOf((*goxml.ArtifactDebutTaskInfoManager)(nil)),
		"ArtifactDebutTaskInfos":                               reflect.ValueOf((*goxml.ArtifactDebutTaskInfos)(nil)),
		"ArtifactDebutTimeInfo":                                reflect.ValueOf((*goxml.ArtifactDebutTimeInfo)(nil)),
		"ArtifactDebutTimeInfoManager":                         reflect.ValueOf((*goxml.ArtifactDebutTimeInfoManager)(nil)),
		"ArtifactDebutTimeInfos":                               reflect.ValueOf((*goxml.ArtifactDebutTimeInfos)(nil)),
		"ArtifactExchangeInfo":                                 reflect.ValueOf((*goxml.ArtifactExchangeInfo)(nil)),
		"ArtifactExchangeInfoManager":                          reflect.ValueOf((*goxml.ArtifactExchangeInfoManager)(nil)),
		"ArtifactExchangeInfos":                                reflect.ValueOf((*goxml.ArtifactExchangeInfos)(nil)),
		"ArtifactForgeInfo":                                    reflect.ValueOf((*goxml.ArtifactForgeInfo)(nil)),
		"ArtifactForgeInfoExt":                                 reflect.ValueOf((*goxml.ArtifactForgeInfoExt)(nil)),
		"ArtifactForgeInfoManager":                             reflect.ValueOf((*goxml.ArtifactForgeInfoManager)(nil)),
		"ArtifactForgeInfos":                                   reflect.ValueOf((*goxml.ArtifactForgeInfos)(nil)),
		"ArtifactFragmentInfo":                                 reflect.ValueOf((*goxml.ArtifactFragmentInfo)(nil)),
		"ArtifactFragmentInfoManager":                          reflect.ValueOf((*goxml.ArtifactFragmentInfoManager)(nil)),
		"ArtifactFragmentInfos":                                reflect.ValueOf((*goxml.ArtifactFragmentInfos)(nil)),
		"ArtifactInfo":                                         reflect.ValueOf((*goxml.ArtifactInfo)(nil)),
		"ArtifactInfoManager":                                  reflect.ValueOf((*goxml.ArtifactInfoManager)(nil)),
		"ArtifactInfos":                                        reflect.ValueOf((*goxml.ArtifactInfos)(nil)),
		"ArtifactLinkSkillInfo":                                reflect.ValueOf((*goxml.ArtifactLinkSkillInfo)(nil)),
		"ArtifactLinkSkillInfoExt":                             reflect.ValueOf((*goxml.ArtifactLinkSkillInfoExt)(nil)),
		"ArtifactLinkSkillInfoManager":                         reflect.ValueOf((*goxml.ArtifactLinkSkillInfoManager)(nil)),
		"ArtifactLinkSkillInfos":                               reflect.ValueOf((*goxml.ArtifactLinkSkillInfos)(nil)),
		"ArtifactRecycleInfo":                                  reflect.ValueOf((*goxml.ArtifactRecycleInfo)(nil)),
		"ArtifactRecycleInfoManager":                           reflect.ValueOf((*goxml.ArtifactRecycleInfoManager)(nil)),
		"ArtifactRecycleInfos":                                 reflect.ValueOf((*goxml.ArtifactRecycleInfos)(nil)),
		"ArtifactSkillInfo":                                    reflect.ValueOf((*goxml.ArtifactSkillInfo)(nil)),
		"ArtifactSkillInfoManager":                             reflect.ValueOf((*goxml.ArtifactSkillInfoManager)(nil)),
		"ArtifactSkillInfos":                                   reflect.ValueOf((*goxml.ArtifactSkillInfos)(nil)),
		"ArtifactStarInfo":                                     reflect.ValueOf((*goxml.ArtifactStarInfo)(nil)),
		"ArtifactStarInfoExt":                                  reflect.ValueOf((*goxml.ArtifactStarInfoExt)(nil)),
		"ArtifactStarInfoManager":                              reflect.ValueOf((*goxml.ArtifactStarInfoManager)(nil)),
		"ArtifactStarInfos":                                    reflect.ValueOf((*goxml.ArtifactStarInfos)(nil)),
		"ArtifactStrengthInfo":                                 reflect.ValueOf((*goxml.ArtifactStrengthInfo)(nil)),
		"ArtifactStrengthInfoExt":                              reflect.ValueOf((*goxml.ArtifactStrengthInfoExt)(nil)),
		"ArtifactStrengthInfoManager":                          reflect.ValueOf((*goxml.ArtifactStrengthInfoManager)(nil)),
		"ArtifactStrengthInfos":                                reflect.ValueOf((*goxml.ArtifactStrengthInfos)(nil)),
		"ArtifactTalentInfo":                                   reflect.ValueOf((*goxml.ArtifactTalentInfo)(nil)),
		"ArtifactTalentInfoExt":                                reflect.ValueOf((*goxml.ArtifactTalentInfoExt)(nil)),
		"ArtifactTalentInfoManager":                            reflect.ValueOf((*goxml.ArtifactTalentInfoManager)(nil)),
		"ArtifactTalentInfos":                                  reflect.ValueOf((*goxml.ArtifactTalentInfos)(nil)),
		"AssistanceActivityInfo":                               reflect.ValueOf((*goxml.AssistanceActivityInfo)(nil)),
		"AssistanceActivityInfoManager":                        reflect.ValueOf((*goxml.AssistanceActivityInfoManager)(nil)),
		"AssistanceActivityInfos":                              reflect.ValueOf((*goxml.AssistanceActivityInfos)(nil)),
		"AssistanceActivityOpenInfo":                           reflect.ValueOf((*goxml.AssistanceActivityOpenInfo)(nil)),
		"AssistanceActivityOpenInfoExt":                        reflect.ValueOf((*goxml.AssistanceActivityOpenInfoExt)(nil)),
		"AssistanceActivityOpenInfoManager":                    reflect.ValueOf((*goxml.AssistanceActivityOpenInfoManager)(nil)),
		"AssistanceActivityOpenInfos":                          reflect.ValueOf((*goxml.AssistanceActivityOpenInfos)(nil)),
		"AttrTypeAndValue":                                     reflect.ValueOf((*goxml.AttrTypeAndValue)(nil)),
		"AttrTypeValue":                                        reflect.ValueOf((*goxml.AttrTypeValue)(nil)),
		"AttributeInfo":                                        reflect.ValueOf((*goxml.AttributeInfo)(nil)),
		"AttributeInfoManager":                                 reflect.ValueOf((*goxml.AttributeInfoManager)(nil)),
		"AttributeInfos":                                       reflect.ValueOf((*goxml.AttributeInfos)(nil)),
		"AvatarInfo":                                           reflect.ValueOf((*goxml.AvatarInfo)(nil)),
		"AvatarInfoManager":                                    reflect.ValueOf((*goxml.AvatarInfoManager)(nil)),
		"AvatarInfos":                                          reflect.ValueOf((*goxml.AvatarInfos)(nil)),
		"AvatarItemInfo":                                       reflect.ValueOf((*goxml.AvatarItemInfo)(nil)),
		"AvatarItemInfoManager":                                reflect.ValueOf((*goxml.AvatarItemInfoManager)(nil)),
		"AvatarItemInfos":                                      reflect.ValueOf((*goxml.AvatarItemInfos)(nil)),
		"AwardInfo":                                            reflect.ValueOf((*goxml.AwardInfo)(nil)),
		"BattleParaInfo":                                       reflect.ValueOf((*goxml.BattleParaInfo)(nil)),
		"BattleParaInfoManager":                                reflect.ValueOf((*goxml.BattleParaInfoManager)(nil)),
		"BattleParaInfos":                                      reflect.ValueOf((*goxml.BattleParaInfos)(nil)),
		"BattleSuppressInfo":                                   reflect.ValueOf((*goxml.BattleSuppressInfo)(nil)),
		"BattleSuppressInfoManager":                            reflect.ValueOf((*goxml.BattleSuppressInfoManager)(nil)),
		"BattleSuppressInfos":                                  reflect.ValueOf((*goxml.BattleSuppressInfos)(nil)),
		"BelltowerConfigInfo":                                  reflect.ValueOf((*goxml.BelltowerConfigInfo)(nil)),
		"BelltowerConfigInfoManager":                           reflect.ValueOf((*goxml.BelltowerConfigInfoManager)(nil)),
		"BelltowerConfigInfos":                                 reflect.ValueOf((*goxml.BelltowerConfigInfos)(nil)),
		"BossRushConfigInfo":                                   reflect.ValueOf((*goxml.BossRushConfigInfo)(nil)),
		"BossRushConfigInfoManager":                            reflect.ValueOf((*goxml.BossRushConfigInfoManager)(nil)),
		"BossRushConfigInfos":                                  reflect.ValueOf((*goxml.BossRushConfigInfos)(nil)),
		"BossRushHpInfo":                                       reflect.ValueOf((*goxml.BossRushHpInfo)(nil)),
		"BossRushHpInfoM":                                      reflect.ValueOf((*goxml.BossRushHpInfoM)(nil)),
		"BossRushHpInfoMEx":                                    reflect.ValueOf((*goxml.BossRushHpInfoMEx)(nil)),
		"BossRushHpInfoXml":                                    reflect.ValueOf((*goxml.BossRushHpInfoXml)(nil)),
		"BossRushHpInfoXmls":                                   reflect.ValueOf((*goxml.BossRushHpInfoXmls)(nil)),
		"BossRushInfo":                                         reflect.ValueOf((*goxml.BossRushInfo)(nil)),
		"BossRushInfoM":                                        reflect.ValueOf((*goxml.BossRushInfoM)(nil)),
		"BossRushInfoMEx":                                      reflect.ValueOf((*goxml.BossRushInfoMEx)(nil)),
		"BossRushInfoMIdIndex":                                 reflect.ValueOf((*goxml.BossRushInfoMIdIndex)(nil)),
		"BossRushInfoMSeasonIdFormationIdIndex":                reflect.ValueOf((*goxml.BossRushInfoMSeasonIdFormationIdIndex)(nil)),
		"BossRushInfoMSeasonIdGroupIdIndex":                    reflect.ValueOf((*goxml.BossRushInfoMSeasonIdGroupIdIndex)(nil)),
		"BossRushInfoMSeasonIdIndex":                           reflect.ValueOf((*goxml.BossRushInfoMSeasonIdIndex)(nil)),
		"BossRushInfoXml":                                      reflect.ValueOf((*goxml.BossRushInfoXml)(nil)),
		"BossRushInfoXmls":                                     reflect.ValueOf((*goxml.BossRushInfoXmls)(nil)),
		"BossRushRewardInfo":                                   reflect.ValueOf((*goxml.BossRushRewardInfo)(nil)),
		"BossRushRewardInfoM":                                  reflect.ValueOf((*goxml.BossRushRewardInfoM)(nil)),
		"BossRushRewardInfoMEx":                                reflect.ValueOf((*goxml.BossRushRewardInfoMEx)(nil)),
		"BossRushRewardInfoMIdIndex":                           reflect.ValueOf((*goxml.BossRushRewardInfoMIdIndex)(nil)),
		"BossRushRewardInfoXml":                                reflect.ValueOf((*goxml.BossRushRewardInfoXml)(nil)),
		"BossRushRewardInfoXmls":                               reflect.ValueOf((*goxml.BossRushRewardInfoXmls)(nil)),
		"BossRushTaskInfo":                                     reflect.ValueOf((*goxml.BossRushTaskInfo)(nil)),
		"BossRushTaskInfoM":                                    reflect.ValueOf((*goxml.BossRushTaskInfoM)(nil)),
		"BossRushTaskInfoMEx":                                  reflect.ValueOf((*goxml.BossRushTaskInfoMEx)(nil)),
		"BossRushTaskInfoXml":                                  reflect.ValueOf((*goxml.BossRushTaskInfoXml)(nil)),
		"BossRushTaskInfoXmls":                                 reflect.ValueOf((*goxml.BossRushTaskInfoXmls)(nil)),
		"BotHeadInfo":                                          reflect.ValueOf((*goxml.BotHeadInfo)(nil)),
		"BotHeadInfoExt":                                       reflect.ValueOf((*goxml.BotHeadInfoExt)(nil)),
		"BotHeadInfoManager":                                   reflect.ValueOf((*goxml.BotHeadInfoManager)(nil)),
		"BotHeadInfos":                                         reflect.ValueOf((*goxml.BotHeadInfos)(nil)),
		"BotInfo":                                              reflect.ValueOf((*goxml.BotInfo)(nil)),
		"BotInfoExt":                                           reflect.ValueOf((*goxml.BotInfoExt)(nil)),
		"BotInfoManager":                                       reflect.ValueOf((*goxml.BotInfoManager)(nil)),
		"BotInfos":                                             reflect.ValueOf((*goxml.BotInfos)(nil)),
		"BuffConditionInfo":                                    reflect.ValueOf((*goxml.BuffConditionInfo)(nil)),
		"BuffConditionInfoManager":                             reflect.ValueOf((*goxml.BuffConditionInfoManager)(nil)),
		"BuffConditionInfos":                                   reflect.ValueOf((*goxml.BuffConditionInfos)(nil)),
		"BuffGroupInfo":                                        reflect.ValueOf((*goxml.BuffGroupInfo)(nil)),
		"BuffGroupInfoExt":                                     reflect.ValueOf((*goxml.BuffGroupInfoExt)(nil)),
		"BuffGroupInfoManager":                                 reflect.ValueOf((*goxml.BuffGroupInfoManager)(nil)),
		"BuffGroupInfos":                                       reflect.ValueOf((*goxml.BuffGroupInfos)(nil)),
		"BuffInfo":                                             reflect.ValueOf((*goxml.BuffInfo)(nil)),
		"BuffInfoManager":                                      reflect.ValueOf((*goxml.BuffInfoManager)(nil)),
		"BuffInfos":                                            reflect.ValueOf((*goxml.BuffInfos)(nil)),
		"BuffTypeInfo":                                         reflect.ValueOf((*goxml.BuffTypeInfo)(nil)),
		"BuffTypeInfoManager":                                  reflect.ValueOf((*goxml.BuffTypeInfoManager)(nil)),
		"BuffTypeInfos":                                        reflect.ValueOf((*goxml.BuffTypeInfos)(nil)),
		"BuffWeight":                                           reflect.ValueOf((*goxml.BuffWeight)(nil)),
		"Buff_Condition":                                       reflect.ValueOf((*goxml.Buff_Condition)(nil)),
		"Buff_Effect":                                          reflect.ValueOf((*goxml.Buff_Effect)(nil)),
		"BuyPriceInfo":                                         reflect.ValueOf((*goxml.BuyPriceInfo)(nil)),
		"BuyPriceInfoManager":                                  reflect.ValueOf((*goxml.BuyPriceInfoManager)(nil)),
		"BuyPriceInfos":                                        reflect.ValueOf((*goxml.BuyPriceInfos)(nil)),
		"CarnivalInfo":                                         reflect.ValueOf((*goxml.CarnivalInfo)(nil)),
		"CarnivalInfoManager":                                  reflect.ValueOf((*goxml.CarnivalInfoManager)(nil)),
		"CarnivalInfos":                                        reflect.ValueOf((*goxml.CarnivalInfos)(nil)),
		"CarnivalTaskInfo":                                     reflect.ValueOf((*goxml.CarnivalTaskInfo)(nil)),
		"CarnivalTaskInfoManager":                              reflect.ValueOf((*goxml.CarnivalTaskInfoManager)(nil)),
		"CarnivalTaskInfos":                                    reflect.ValueOf((*goxml.CarnivalTaskInfos)(nil)),
		"ChangeWeightMes":                                      reflect.ValueOf((*goxml.ChangeWeightMes)(nil)),
		"ChatAdminInfo":                                        reflect.ValueOf((*goxml.ChatAdminInfo)(nil)),
		"ChatAdminInfoManager":                                 reflect.ValueOf((*goxml.ChatAdminInfoManager)(nil)),
		"ChatAdminInfos":                                       reflect.ValueOf((*goxml.ChatAdminInfos)(nil)),
		"ChatConfigInfo":                                       reflect.ValueOf((*goxml.ChatConfigInfo)(nil)),
		"ChatConfigInfoManager":                                reflect.ValueOf((*goxml.ChatConfigInfoManager)(nil)),
		"ChatConfigInfos":                                      reflect.ValueOf((*goxml.ChatConfigInfos)(nil)),
		"ChatInfo":                                             reflect.ValueOf((*goxml.ChatInfo)(nil)),
		"ChatInfoManager":                                      reflect.ValueOf((*goxml.ChatInfoManager)(nil)),
		"ChatInfos":                                            reflect.ValueOf((*goxml.ChatInfos)(nil)),
		"ChatLanguageInfo":                                     reflect.ValueOf((*goxml.ChatLanguageInfo)(nil)),
		"ChatLanguageInfoManager":                              reflect.ValueOf((*goxml.ChatLanguageInfoManager)(nil)),
		"ChatLanguageInfos":                                    reflect.ValueOf((*goxml.ChatLanguageInfos)(nil)),
		"ClassInfo":                                            reflect.ValueOf((*goxml.ClassInfo)(nil)),
		"CmdInterval":                                          reflect.ValueOf((*goxml.CmdInterval)(nil)),
		"CommandLimit":                                         reflect.ValueOf((*goxml.CommandLimit)(nil)),
		"ConfigInfo":                                           reflect.ValueOf((*goxml.ConfigInfo)(nil)),
		"ConfigInfoManager":                                    reflect.ValueOf((*goxml.ConfigInfoManager)(nil)),
		"ConfigInfos":                                          reflect.ValueOf((*goxml.ConfigInfos)(nil)),
		"ConversionInfo":                                       reflect.ValueOf((*goxml.ConversionInfo)(nil)),
		"ConversionInfoExt":                                    reflect.ValueOf((*goxml.ConversionInfoExt)(nil)),
		"ConversionInfoManager":                                reflect.ValueOf((*goxml.ConversionInfoManager)(nil)),
		"ConversionInfos":                                      reflect.ValueOf((*goxml.ConversionInfos)(nil)),
		"CostPoolItem":                                         reflect.ValueOf((*goxml.CostPoolItem)(nil)),
		"CrossMasterConfig":                                    reflect.ValueOf((*goxml.CrossMasterConfig)(nil)),
		"CrossServiceConfig":                                   reflect.ValueOf((*goxml.CrossServiceConfig)(nil)),
		"CrystalAchieveInfo":                                   reflect.ValueOf((*goxml.CrystalAchieveInfo)(nil)),
		"CrystalAchieveInfoExt":                                reflect.ValueOf((*goxml.CrystalAchieveInfoExt)(nil)),
		"CrystalAchieveInfoManager":                            reflect.ValueOf((*goxml.CrystalAchieveInfoManager)(nil)),
		"CrystalAchieveInfos":                                  reflect.ValueOf((*goxml.CrystalAchieveInfos)(nil)),
		"CrystalConfigInfo":                                    reflect.ValueOf((*goxml.CrystalConfigInfo)(nil)),
		"CrystalConfigInfoManager":                             reflect.ValueOf((*goxml.CrystalConfigInfoManager)(nil)),
		"CrystalConfigInfos":                                   reflect.ValueOf((*goxml.CrystalConfigInfos)(nil)),
		"CrystalEmblemInfo":                                    reflect.ValueOf((*goxml.CrystalEmblemInfo)(nil)),
		"CrystalEmblemInfoManager":                             reflect.ValueOf((*goxml.CrystalEmblemInfoManager)(nil)),
		"CrystalEmblemInfos":                                   reflect.ValueOf((*goxml.CrystalEmblemInfos)(nil)),
		"CrystalGemInfo":                                       reflect.ValueOf((*goxml.CrystalGemInfo)(nil)),
		"CrystalGemInfoManager":                                reflect.ValueOf((*goxml.CrystalGemInfoManager)(nil)),
		"CrystalGemInfos":                                      reflect.ValueOf((*goxml.CrystalGemInfos)(nil)),
		"DailyAttendanceHeroRewardInfo":                        reflect.ValueOf((*goxml.DailyAttendanceHeroRewardInfo)(nil)),
		"DailyAttendanceHeroRewardInfoManager":                 reflect.ValueOf((*goxml.DailyAttendanceHeroRewardInfoManager)(nil)),
		"DailyAttendanceHeroRewardInfos":                       reflect.ValueOf((*goxml.DailyAttendanceHeroRewardInfos)(nil)),
		"DailyAttendanceInfo":                                  reflect.ValueOf((*goxml.DailyAttendanceInfo)(nil)),
		"DailyAttendanceInfoManager":                           reflect.ValueOf((*goxml.DailyAttendanceInfoManager)(nil)),
		"DailyAttendanceInfos":                                 reflect.ValueOf((*goxml.DailyAttendanceInfos)(nil)),
		"DailyAttendanceRewardInfo":                            reflect.ValueOf((*goxml.DailyAttendanceRewardInfo)(nil)),
		"DailyAttendanceRewardInfoManager":                     reflect.ValueOf((*goxml.DailyAttendanceRewardInfoManager)(nil)),
		"DailyAttendanceRewardInfos":                           reflect.ValueOf((*goxml.DailyAttendanceRewardInfos)(nil)),
		"DailyWishInfo":                                        reflect.ValueOf((*goxml.DailyWishInfo)(nil)),
		"DailyWishInfoManager":                                 reflect.ValueOf((*goxml.DailyWishInfoManager)(nil)),
		"DailyWishInfos":                                       reflect.ValueOf((*goxml.DailyWishInfos)(nil)),
		"DailyWishOpenInfo":                                    reflect.ValueOf((*goxml.DailyWishOpenInfo)(nil)),
		"DailyWishOpenInfoManager":                             reflect.ValueOf((*goxml.DailyWishOpenInfoManager)(nil)),
		"DailyWishOpenInfos":                                   reflect.ValueOf((*goxml.DailyWishOpenInfos)(nil)),
		"DailyWishWeightInfo":                                  reflect.ValueOf((*goxml.DailyWishWeightInfo)(nil)),
		"DailyWishWeightInfoManager":                           reflect.ValueOf((*goxml.DailyWishWeightInfoManager)(nil)),
		"DailyWishWeightInfos":                                 reflect.ValueOf((*goxml.DailyWishWeightInfos)(nil)),
		"DisorderlandBossWeakInfo":                             reflect.ValueOf((*goxml.DisorderlandBossWeakInfo)(nil)),
		"DisorderlandBossWeakInfoManager":                      reflect.ValueOf((*goxml.DisorderlandBossWeakInfoManager)(nil)),
		"DisorderlandBossWeakInfos":                            reflect.ValueOf((*goxml.DisorderlandBossWeakInfos)(nil)),
		"DisorderlandBoxInfo":                                  reflect.ValueOf((*goxml.DisorderlandBoxInfo)(nil)),
		"DisorderlandBoxInfoManager":                           reflect.ValueOf((*goxml.DisorderlandBoxInfoManager)(nil)),
		"DisorderlandBoxInfos":                                 reflect.ValueOf((*goxml.DisorderlandBoxInfos)(nil)),
		"DisorderlandBuffInfo":                                 reflect.ValueOf((*goxml.DisorderlandBuffInfo)(nil)),
		"DisorderlandBuffInfoManager":                          reflect.ValueOf((*goxml.DisorderlandBuffInfoManager)(nil)),
		"DisorderlandBuffInfos":                                reflect.ValueOf((*goxml.DisorderlandBuffInfos)(nil)),
		"DisorderlandConfigInfo":                               reflect.ValueOf((*goxml.DisorderlandConfigInfo)(nil)),
		"DisorderlandConfigInfoManager":                        reflect.ValueOf((*goxml.DisorderlandConfigInfoManager)(nil)),
		"DisorderlandConfigInfos":                              reflect.ValueOf((*goxml.DisorderlandConfigInfos)(nil)),
		"DisorderlandDropAddInfo":                              reflect.ValueOf((*goxml.DisorderlandDropAddInfo)(nil)),
		"DisorderlandDropAddInfos":                             reflect.ValueOf((*goxml.DisorderlandDropAddInfos)(nil)),
		"DisorderlandDropGroupInfo":                            reflect.ValueOf((*goxml.DisorderlandDropGroupInfo)(nil)),
		"DisorderlandDropGroupInfoManager":                     reflect.ValueOf((*goxml.DisorderlandDropGroupInfoManager)(nil)),
		"DisorderlandDropGroupInfos":                           reflect.ValueOf((*goxml.DisorderlandDropGroupInfos)(nil)),
		"DisorderlandDropInfo":                                 reflect.ValueOf((*goxml.DisorderlandDropInfo)(nil)),
		"DisorderlandDropInfoManager":                          reflect.ValueOf((*goxml.DisorderlandDropInfoManager)(nil)),
		"DisorderlandDropInfos":                                reflect.ValueOf((*goxml.DisorderlandDropInfos)(nil)),
		"DisorderlandDungeonInfo":                              reflect.ValueOf((*goxml.DisorderlandDungeonInfo)(nil)),
		"DisorderlandDungeonInfoExt":                           reflect.ValueOf((*goxml.DisorderlandDungeonInfoExt)(nil)),
		"DisorderlandDungeonInfoManager":                       reflect.ValueOf((*goxml.DisorderlandDungeonInfoManager)(nil)),
		"DisorderlandDungeonInfos":                             reflect.ValueOf((*goxml.DisorderlandDungeonInfos)(nil)),
		"DisorderlandFirstDropInfo":                            reflect.ValueOf((*goxml.DisorderlandFirstDropInfo)(nil)),
		"DisorderlandFirstDropInfoManager":                     reflect.ValueOf((*goxml.DisorderlandFirstDropInfoManager)(nil)),
		"DisorderlandFirstDropInfos":                           reflect.ValueOf((*goxml.DisorderlandFirstDropInfos)(nil)),
		"DisorderlandInfo":                                     reflect.ValueOf((*goxml.DisorderlandInfo)(nil)),
		"DisorderlandInfoExt":                                  reflect.ValueOf((*goxml.DisorderlandInfoExt)(nil)),
		"DisorderlandInfoManager":                              reflect.ValueOf((*goxml.DisorderlandInfoManager)(nil)),
		"DisorderlandInfos":                                    reflect.ValueOf((*goxml.DisorderlandInfos)(nil)),
		"DisorderlandMapInfo":                                  reflect.ValueOf((*goxml.DisorderlandMapInfo)(nil)),
		"DisorderlandMapInfoExt":                               reflect.ValueOf((*goxml.DisorderlandMapInfoExt)(nil)),
		"DisorderlandMapInfoManager":                           reflect.ValueOf((*goxml.DisorderlandMapInfoManager)(nil)),
		"DisorderlandMapInfos":                                 reflect.ValueOf((*goxml.DisorderlandMapInfos)(nil)),
		"DisorderlandStoneInfo":                                reflect.ValueOf((*goxml.DisorderlandStoneInfo)(nil)),
		"DisorderlandStoneInfoManager":                         reflect.ValueOf((*goxml.DisorderlandStoneInfoManager)(nil)),
		"DisorderlandStoneInfos":                               reflect.ValueOf((*goxml.DisorderlandStoneInfos)(nil)),
		"DispatchGroupInfo":                                    reflect.ValueOf((*goxml.DispatchGroupInfo)(nil)),
		"DispatchGroupInfoManager":                             reflect.ValueOf((*goxml.DispatchGroupInfoManager)(nil)),
		"DispatchGroupInfos":                                   reflect.ValueOf((*goxml.DispatchGroupInfos)(nil)),
		"DispatchInfo":                                         reflect.ValueOf((*goxml.DispatchInfo)(nil)),
		"DispatchInfoExt":                                      reflect.ValueOf((*goxml.DispatchInfoExt)(nil)),
		"DispatchInfoManager":                                  reflect.ValueOf((*goxml.DispatchInfoManager)(nil)),
		"DispatchInfos":                                        reflect.ValueOf((*goxml.DispatchInfos)(nil)),
		"DispatchLevelInfo":                                    reflect.ValueOf((*goxml.DispatchLevelInfo)(nil)),
		"DispatchLevelInfoExt":                                 reflect.ValueOf((*goxml.DispatchLevelInfoExt)(nil)),
		"DispatchLevelInfoManager":                             reflect.ValueOf((*goxml.DispatchLevelInfoManager)(nil)),
		"DispatchLevelInfos":                                   reflect.ValueOf((*goxml.DispatchLevelInfos)(nil)),
		"DivineDemonConfigInfo":                                reflect.ValueOf((*goxml.DivineDemonConfigInfo)(nil)),
		"DivineDemonConfigInfoManager":                         reflect.ValueOf((*goxml.DivineDemonConfigInfoManager)(nil)),
		"DivineDemonConfigInfos":                               reflect.ValueOf((*goxml.DivineDemonConfigInfos)(nil)),
		"DivineDemonDefaultInfo":                               reflect.ValueOf((*goxml.DivineDemonDefaultInfo)(nil)),
		"DivineDemonDefaultInfoManager":                        reflect.ValueOf((*goxml.DivineDemonDefaultInfoManager)(nil)),
		"DivineDemonDefaultInfos":                              reflect.ValueOf((*goxml.DivineDemonDefaultInfos)(nil)),
		"DivineDemonGroupInfo":                                 reflect.ValueOf((*goxml.DivineDemonGroupInfo)(nil)),
		"DivineDemonGroupInfoExt":                              reflect.ValueOf((*goxml.DivineDemonGroupInfoExt)(nil)),
		"DivineDemonGroupInfoManager":                          reflect.ValueOf((*goxml.DivineDemonGroupInfoManager)(nil)),
		"DivineDemonGroupInfos":                                reflect.ValueOf((*goxml.DivineDemonGroupInfos)(nil)),
		"DivineDemonInfo":                                      reflect.ValueOf((*goxml.DivineDemonInfo)(nil)),
		"DivineDemonInfoExt":                                   reflect.ValueOf((*goxml.DivineDemonInfoExt)(nil)),
		"DivineDemonInfoManager":                               reflect.ValueOf((*goxml.DivineDemonInfoManager)(nil)),
		"DivineDemonInfos":                                     reflect.ValueOf((*goxml.DivineDemonInfos)(nil)),
		"DivineDemonSummonGuaranteedInfo":                      reflect.ValueOf((*goxml.DivineDemonSummonGuaranteedInfo)(nil)),
		"DivineDemonSummonGuaranteedInfos":                     reflect.ValueOf((*goxml.DivineDemonSummonGuaranteedInfos)(nil)),
		"DivineDemonTaskActiveInfo":                            reflect.ValueOf((*goxml.DivineDemonTaskActiveInfo)(nil)),
		"DivineDemonTaskActiveInfoManager":                     reflect.ValueOf((*goxml.DivineDemonTaskActiveInfoManager)(nil)),
		"DivineDemonTaskActiveInfos":                           reflect.ValueOf((*goxml.DivineDemonTaskActiveInfos)(nil)),
		"DivineDemonTaskStarInfo":                              reflect.ValueOf((*goxml.DivineDemonTaskStarInfo)(nil)),
		"DivineDemonTaskStarInfoManager":                       reflect.ValueOf((*goxml.DivineDemonTaskStarInfoManager)(nil)),
		"DivineDemonTaskStarInfos":                             reflect.ValueOf((*goxml.DivineDemonTaskStarInfos)(nil)),
		"DivineDemonTaskSummonInfo":                            reflect.ValueOf((*goxml.DivineDemonTaskSummonInfo)(nil)),
		"DivineDemonTaskSummonInfoManager":                     reflect.ValueOf((*goxml.DivineDemonTaskSummonInfoManager)(nil)),
		"DivineDemonTaskSummonInfos":                           reflect.ValueOf((*goxml.DivineDemonTaskSummonInfos)(nil)),
		"DropActivityFunctionInfo":                             reflect.ValueOf((*goxml.DropActivityFunctionInfo)(nil)),
		"DropActivityFunctionInfoManager":                      reflect.ValueOf((*goxml.DropActivityFunctionInfoManager)(nil)),
		"DropActivityFunctionInfos":                            reflect.ValueOf((*goxml.DropActivityFunctionInfos)(nil)),
		"DropActivityInfo":                                     reflect.ValueOf((*goxml.DropActivityInfo)(nil)),
		"DropActivityInfoManager":                              reflect.ValueOf((*goxml.DropActivityInfoManager)(nil)),
		"DropActivityInfos":                                    reflect.ValueOf((*goxml.DropActivityInfos)(nil)),
		"DropActivityOpenExchangeInfo":                         reflect.ValueOf((*goxml.DropActivityOpenExchangeInfo)(nil)),
		"DropActivityOpenExchangeInfoManager":                  reflect.ValueOf((*goxml.DropActivityOpenExchangeInfoManager)(nil)),
		"DropActivityOpenExchangeInfos":                        reflect.ValueOf((*goxml.DropActivityOpenExchangeInfos)(nil)),
		"DropActivityOpenInfo":                                 reflect.ValueOf((*goxml.DropActivityOpenInfo)(nil)),
		"DropActivityOpenInfoManager":                          reflect.ValueOf((*goxml.DropActivityOpenInfoManager)(nil)),
		"DropActivityOpenInfos":                                reflect.ValueOf((*goxml.DropActivityOpenInfos)(nil)),
		"DropActivityTypeInfo":                                 reflect.ValueOf((*goxml.DropActivityTypeInfo)(nil)),
		"DropActivityTypeInfoExt":                              reflect.ValueOf((*goxml.DropActivityTypeInfoExt)(nil)),
		"DropActivityTypeInfoManager":                          reflect.ValueOf((*goxml.DropActivityTypeInfoManager)(nil)),
		"DropActivityTypeInfos":                                reflect.ValueOf((*goxml.DropActivityTypeInfos)(nil)),
		"DropGroupInfo":                                        reflect.ValueOf((*goxml.DropGroupInfo)(nil)),
		"DropGroupInfoExt":                                     reflect.ValueOf((*goxml.DropGroupInfoExt)(nil)),
		"DropGroupInfoManager":                                 reflect.ValueOf((*goxml.DropGroupInfoManager)(nil)),
		"DropGroupInfos":                                       reflect.ValueOf((*goxml.DropGroupInfos)(nil)),
		"DropInfo":                                             reflect.ValueOf((*goxml.DropInfo)(nil)),
		"DropInfoExt":                                          reflect.ValueOf((*goxml.DropInfoExt)(nil)),
		"DropInfoManager":                                      reflect.ValueOf((*goxml.DropInfoManager)(nil)),
		"DropInfoUnit":                                         reflect.ValueOf((*goxml.DropInfoUnit)(nil)),
		"DropInfos":                                            reflect.ValueOf((*goxml.DropInfos)(nil)),
		"DungeonConfigInfo":                                    reflect.ValueOf((*goxml.DungeonConfigInfo)(nil)),
		"DungeonConfigInfoExt":                                 reflect.ValueOf((*goxml.DungeonConfigInfoExt)(nil)),
		"DungeonConfigInfoManager":                             reflect.ValueOf((*goxml.DungeonConfigInfoManager)(nil)),
		"DungeonConfigInfos":                                   reflect.ValueOf((*goxml.DungeonConfigInfos)(nil)),
		"DungeonFirstDropInfo":                                 reflect.ValueOf((*goxml.DungeonFirstDropInfo)(nil)),
		"DungeonFirstDropInfoExt":                              reflect.ValueOf((*goxml.DungeonFirstDropInfoExt)(nil)),
		"DungeonFirstDropInfoManager":                          reflect.ValueOf((*goxml.DungeonFirstDropInfoManager)(nil)),
		"DungeonFirstDropInfos":                                reflect.ValueOf((*goxml.DungeonFirstDropInfos)(nil)),
		"DungeonMonsterSkillExt":                               reflect.ValueOf((*goxml.DungeonMonsterSkillExt)(nil)),
		"EmblemAscendInfo":                                     reflect.ValueOf((*goxml.EmblemAscendInfo)(nil)),
		"EmblemAscendInfoManager":                              reflect.ValueOf((*goxml.EmblemAscendInfoManager)(nil)),
		"EmblemAscendInfos":                                    reflect.ValueOf((*goxml.EmblemAscendInfos)(nil)),
		"EmblemConfigInfo":                                     reflect.ValueOf((*goxml.EmblemConfigInfo)(nil)),
		"EmblemConfigInfoManager":                              reflect.ValueOf((*goxml.EmblemConfigInfoManager)(nil)),
		"EmblemConfigInfos":                                    reflect.ValueOf((*goxml.EmblemConfigInfos)(nil)),
		"EmblemCustomizeHeroInfo":                              reflect.ValueOf((*goxml.EmblemCustomizeHeroInfo)(nil)),
		"EmblemCustomizeHeroInfoManager":                       reflect.ValueOf((*goxml.EmblemCustomizeHeroInfoManager)(nil)),
		"EmblemCustomizeHeroInfos":                             reflect.ValueOf((*goxml.EmblemCustomizeHeroInfos)(nil)),
		"EmblemCustomizeInfo":                                  reflect.ValueOf((*goxml.EmblemCustomizeInfo)(nil)),
		"EmblemCustomizeInfoManager":                           reflect.ValueOf((*goxml.EmblemCustomizeInfoManager)(nil)),
		"EmblemCustomizeInfos":                                 reflect.ValueOf((*goxml.EmblemCustomizeInfos)(nil)),
		"EmblemCustomizeTypeInfo":                              reflect.ValueOf((*goxml.EmblemCustomizeTypeInfo)(nil)),
		"EmblemCustomizeTypeInfoManager":                       reflect.ValueOf((*goxml.EmblemCustomizeTypeInfoManager)(nil)),
		"EmblemCustomizeTypeInfos":                             reflect.ValueOf((*goxml.EmblemCustomizeTypeInfos)(nil)),
		"EmblemFragmentInfo":                                   reflect.ValueOf((*goxml.EmblemFragmentInfo)(nil)),
		"EmblemFragmentInfoManager":                            reflect.ValueOf((*goxml.EmblemFragmentInfoManager)(nil)),
		"EmblemFragmentInfos":                                  reflect.ValueOf((*goxml.EmblemFragmentInfos)(nil)),
		"EmblemHeroGroupInfo":                                  reflect.ValueOf((*goxml.EmblemHeroGroupInfo)(nil)),
		"EmblemHeroGroupInfoM":                                 reflect.ValueOf((*goxml.EmblemHeroGroupInfoM)(nil)),
		"EmblemHeroGroupInfoMEx":                               reflect.ValueOf((*goxml.EmblemHeroGroupInfoMEx)(nil)),
		"EmblemHeroGroupInfoMGroupIdIndex":                     reflect.ValueOf((*goxml.EmblemHeroGroupInfoMGroupIdIndex)(nil)),
		"EmblemHeroGroupInfoXml":                               reflect.ValueOf((*goxml.EmblemHeroGroupInfoXml)(nil)),
		"EmblemHeroGroupInfoXmls":                              reflect.ValueOf((*goxml.EmblemHeroGroupInfoXmls)(nil)),
		"EmblemInfo":                                           reflect.ValueOf((*goxml.EmblemInfo)(nil)),
		"EmblemInfoManager":                                    reflect.ValueOf((*goxml.EmblemInfoManager)(nil)),
		"EmblemInfos":                                          reflect.ValueOf((*goxml.EmblemInfos)(nil)),
		"EmblemInfosExt":                                       reflect.ValueOf((*goxml.EmblemInfosExt)(nil)),
		"EmblemLevelInfo":                                      reflect.ValueOf((*goxml.EmblemLevelInfo)(nil)),
		"EmblemLevelInfoExt":                                   reflect.ValueOf((*goxml.EmblemLevelInfoExt)(nil)),
		"EmblemLevelInfoManager":                               reflect.ValueOf((*goxml.EmblemLevelInfoManager)(nil)),
		"EmblemLevelInfos":                                     reflect.ValueOf((*goxml.EmblemLevelInfos)(nil)),
		"EmblemMagicSkillInfo":                                 reflect.ValueOf((*goxml.EmblemMagicSkillInfo)(nil)),
		"EmblemMagicSkillInfoManager":                          reflect.ValueOf((*goxml.EmblemMagicSkillInfoManager)(nil)),
		"EmblemMagicSkillInfos":                                reflect.ValueOf((*goxml.EmblemMagicSkillInfos)(nil)),
		"EmblemSuccinctConflateInfo":                           reflect.ValueOf((*goxml.EmblemSuccinctConflateInfo)(nil)),
		"EmblemSuccinctConflateInfoM":                          reflect.ValueOf((*goxml.EmblemSuccinctConflateInfoM)(nil)),
		"EmblemSuccinctConflateInfoXml":                        reflect.ValueOf((*goxml.EmblemSuccinctConflateInfoXml)(nil)),
		"EmblemSuccinctConflateInfoXmls":                       reflect.ValueOf((*goxml.EmblemSuccinctConflateInfoXmls)(nil)),
		"EmblemSuccinctInfo":                                   reflect.ValueOf((*goxml.EmblemSuccinctInfo)(nil)),
		"EmblemSuccinctInfoM":                                  reflect.ValueOf((*goxml.EmblemSuccinctInfoM)(nil)),
		"EmblemSuccinctInfoMHeroRaceIndex":                     reflect.ValueOf((*goxml.EmblemSuccinctInfoMHeroRaceIndex)(nil)),
		"EmblemSuccinctInfoXml":                                reflect.ValueOf((*goxml.EmblemSuccinctInfoXml)(nil)),
		"EmblemSuccinctInfoXmls":                               reflect.ValueOf((*goxml.EmblemSuccinctInfoXmls)(nil)),
		"EmblemSuccinctLockInfo":                               reflect.ValueOf((*goxml.EmblemSuccinctLockInfo)(nil)),
		"EmblemSuccinctLockInfoM":                              reflect.ValueOf((*goxml.EmblemSuccinctLockInfoM)(nil)),
		"EmblemSuccinctLockInfoMRaceIndex":                     reflect.ValueOf((*goxml.EmblemSuccinctLockInfoMRaceIndex)(nil)),
		"EmblemSuccinctLockInfoXml":                            reflect.ValueOf((*goxml.EmblemSuccinctLockInfoXml)(nil)),
		"EmblemSuccinctLockInfoXmls":                           reflect.ValueOf((*goxml.EmblemSuccinctLockInfoXmls)(nil)),
		"EmblemSuccinctMagicSkillInfo":                         reflect.ValueOf((*goxml.EmblemSuccinctMagicSkillInfo)(nil)),
		"EmblemSuccinctMagicSkillInfoM":                        reflect.ValueOf((*goxml.EmblemSuccinctMagicSkillInfoM)(nil)),
		"EmblemSuccinctMagicSkillInfoMHeroIdEmblemRareIndex":   reflect.ValueOf((*goxml.EmblemSuccinctMagicSkillInfoMHeroIdEmblemRareIndex)(nil)),
		"EmblemSuccinctMagicSkillInfoMHeroIdIndex":             reflect.ValueOf((*goxml.EmblemSuccinctMagicSkillInfoMHeroIdIndex)(nil)),
		"EmblemSuccinctMagicSkillInfoXml":                      reflect.ValueOf((*goxml.EmblemSuccinctMagicSkillInfoXml)(nil)),
		"EmblemSuccinctMagicSkillInfoXmls":                     reflect.ValueOf((*goxml.EmblemSuccinctMagicSkillInfoXmls)(nil)),
		"EmblemSuitInfo":                                       reflect.ValueOf((*goxml.EmblemSuitInfo)(nil)),
		"EmblemSuitInfoManager":                                reflect.ValueOf((*goxml.EmblemSuitInfoManager)(nil)),
		"EmblemSuitInfos":                                      reflect.ValueOf((*goxml.EmblemSuitInfos)(nil)),
		"EquipAttrCoeInfo":                                     reflect.ValueOf((*goxml.EquipAttrCoeInfo)(nil)),
		"EquipAttrCoeInfoManager":                              reflect.ValueOf((*goxml.EquipAttrCoeInfoManager)(nil)),
		"EquipAttrCoeInfos":                                    reflect.ValueOf((*goxml.EquipAttrCoeInfos)(nil)),
		"EquipEnchantInfo":                                     reflect.ValueOf((*goxml.EquipEnchantInfo)(nil)),
		"EquipEnchantInfoExt":                                  reflect.ValueOf((*goxml.EquipEnchantInfoExt)(nil)),
		"EquipEnchantInfoManager":                              reflect.ValueOf((*goxml.EquipEnchantInfoManager)(nil)),
		"EquipEnchantInfos":                                    reflect.ValueOf((*goxml.EquipEnchantInfos)(nil)),
		"EquipEnchantMasterInfo":                               reflect.ValueOf((*goxml.EquipEnchantMasterInfo)(nil)),
		"EquipEnchantMasterInfoExt":                            reflect.ValueOf((*goxml.EquipEnchantMasterInfoExt)(nil)),
		"EquipEnchantMasterInfoManager":                        reflect.ValueOf((*goxml.EquipEnchantMasterInfoManager)(nil)),
		"EquipEnchantMasterInfos":                              reflect.ValueOf((*goxml.EquipEnchantMasterInfos)(nil)),
		"EquipEnchantRandInfo":                                 reflect.ValueOf((*goxml.EquipEnchantRandInfo)(nil)),
		"EquipEnchantRandInfoManager":                          reflect.ValueOf((*goxml.EquipEnchantRandInfoManager)(nil)),
		"EquipEnchantRandInfos":                                reflect.ValueOf((*goxml.EquipEnchantRandInfos)(nil)),
		"EquipEvolutionInfo":                                   reflect.ValueOf((*goxml.EquipEvolutionInfo)(nil)),
		"EquipEvolutionInfoExt":                                reflect.ValueOf((*goxml.EquipEvolutionInfoExt)(nil)),
		"EquipEvolutionInfoManager":                            reflect.ValueOf((*goxml.EquipEvolutionInfoManager)(nil)),
		"EquipEvolutionInfos":                                  reflect.ValueOf((*goxml.EquipEvolutionInfos)(nil)),
		"EquipInfo":                                            reflect.ValueOf((*goxml.EquipInfo)(nil)),
		"EquipInfoManager":                                     reflect.ValueOf((*goxml.EquipInfoManager)(nil)),
		"EquipInfos":                                           reflect.ValueOf((*goxml.EquipInfos)(nil)),
		"EquipRefineInfo":                                      reflect.ValueOf((*goxml.EquipRefineInfo)(nil)),
		"EquipRefineInfoExt":                                   reflect.ValueOf((*goxml.EquipRefineInfoExt)(nil)),
		"EquipRefineInfoManager":                               reflect.ValueOf((*goxml.EquipRefineInfoManager)(nil)),
		"EquipRefineInfos":                                     reflect.ValueOf((*goxml.EquipRefineInfos)(nil)),
		"EquipRefineTechniqueInfo":                             reflect.ValueOf((*goxml.EquipRefineTechniqueInfo)(nil)),
		"EquipRefineTechniqueInfoManager":                      reflect.ValueOf((*goxml.EquipRefineTechniqueInfoManager)(nil)),
		"EquipRefineTechniqueInfos":                            reflect.ValueOf((*goxml.EquipRefineTechniqueInfos)(nil)),
		"EquipStrengthInfo":                                    reflect.ValueOf((*goxml.EquipStrengthInfo)(nil)),
		"EquipStrengthInfoExt":                                 reflect.ValueOf((*goxml.EquipStrengthInfoExt)(nil)),
		"EquipStrengthInfoManager":                             reflect.ValueOf((*goxml.EquipStrengthInfoManager)(nil)),
		"EquipStrengthInfos":                                   reflect.ValueOf((*goxml.EquipStrengthInfos)(nil)),
		"EtcdConfig":                                           reflect.ValueOf((*goxml.EtcdConfig)(nil)),
		"ExclusiveOreLevelLimit":                               reflect.ValueOf((*goxml.ExclusiveOreLevelLimit)(nil)),
		"FirstRechargeInfoManager":                             reflect.ValueOf((*goxml.FirstRechargeInfoManager)(nil)),
		"FirstrechargeInfo":                                    reflect.ValueOf((*goxml.FirstrechargeInfo)(nil)),
		"FirstrechargeInfos":                                   reflect.ValueOf((*goxml.FirstrechargeInfos)(nil)),
		"FlowerAssistDungeonInfo":                              reflect.ValueOf((*goxml.FlowerAssistDungeonInfo)(nil)),
		"FlowerAssistDungeonInfoManager":                       reflect.ValueOf((*goxml.FlowerAssistDungeonInfoManager)(nil)),
		"FlowerAssistDungeonInfos":                             reflect.ValueOf((*goxml.FlowerAssistDungeonInfos)(nil)),
		"FlowerConfigInfo":                                     reflect.ValueOf((*goxml.FlowerConfigInfo)(nil)),
		"FlowerConfigInfoManager":                              reflect.ValueOf((*goxml.FlowerConfigInfoManager)(nil)),
		"FlowerConfigInfos":                                    reflect.ValueOf((*goxml.FlowerConfigInfos)(nil)),
		"FlowerLvInfo":                                         reflect.ValueOf((*goxml.FlowerLvInfo)(nil)),
		"FlowerLvInfoManager":                                  reflect.ValueOf((*goxml.FlowerLvInfoManager)(nil)),
		"FlowerLvInfos":                                        reflect.ValueOf((*goxml.FlowerLvInfos)(nil)),
		"FlowerMessageInfo":                                    reflect.ValueOf((*goxml.FlowerMessageInfo)(nil)),
		"FlowerMessageInfoManager":                             reflect.ValueOf((*goxml.FlowerMessageInfoManager)(nil)),
		"FlowerMessageInfos":                                   reflect.ValueOf((*goxml.FlowerMessageInfos)(nil)),
		"FlowerOccupyBaseInfo":                                 reflect.ValueOf((*goxml.FlowerOccupyBaseInfo)(nil)),
		"FlowerOccupyBaseInfoManager":                          reflect.ValueOf((*goxml.FlowerOccupyBaseInfoManager)(nil)),
		"FlowerOccupyBaseInfos":                                reflect.ValueOf((*goxml.FlowerOccupyBaseInfos)(nil)),
		"FlowerOccupyJungleInfo":                               reflect.ValueOf((*goxml.FlowerOccupyJungleInfo)(nil)),
		"FlowerOccupyJungleInfoExt":                            reflect.ValueOf((*goxml.FlowerOccupyJungleInfoExt)(nil)),
		"FlowerOccupyJungleInfoManager":                        reflect.ValueOf((*goxml.FlowerOccupyJungleInfoManager)(nil)),
		"FlowerOccupyJungleInfos":                              reflect.ValueOf((*goxml.FlowerOccupyJungleInfos)(nil)),
		"FlowerOccupyTimberInfo":                               reflect.ValueOf((*goxml.FlowerOccupyTimberInfo)(nil)),
		"FlowerOccupyTimberInfoManager":                        reflect.ValueOf((*goxml.FlowerOccupyTimberInfoManager)(nil)),
		"FlowerOccupyTimberInfos":                              reflect.ValueOf((*goxml.FlowerOccupyTimberInfos)(nil)),
		"FlowerPlantGoblinInfo":                                reflect.ValueOf((*goxml.FlowerPlantGoblinInfo)(nil)),
		"FlowerPlantGoblinInfoExt":                             reflect.ValueOf((*goxml.FlowerPlantGoblinInfoExt)(nil)),
		"FlowerPlantGoblinInfoManager":                         reflect.ValueOf((*goxml.FlowerPlantGoblinInfoManager)(nil)),
		"FlowerPlantGoblinInfos":                               reflect.ValueOf((*goxml.FlowerPlantGoblinInfos)(nil)),
		"FlowerPlantInfo":                                      reflect.ValueOf((*goxml.FlowerPlantInfo)(nil)),
		"FlowerPlantInfoExt":                                   reflect.ValueOf((*goxml.FlowerPlantInfoExt)(nil)),
		"FlowerPlantInfoManager":                               reflect.ValueOf((*goxml.FlowerPlantInfoManager)(nil)),
		"FlowerPlantInfos":                                     reflect.ValueOf((*goxml.FlowerPlantInfos)(nil)),
		"FlowerPlantRewardInfo":                                reflect.ValueOf((*goxml.FlowerPlantRewardInfo)(nil)),
		"FlowerPlantRewardInfoExt":                             reflect.ValueOf((*goxml.FlowerPlantRewardInfoExt)(nil)),
		"FlowerPlantRewardInfoManager":                         reflect.ValueOf((*goxml.FlowerPlantRewardInfoManager)(nil)),
		"FlowerPlantRewardInfos":                               reflect.ValueOf((*goxml.FlowerPlantRewardInfos)(nil)),
		"FlowerPlantScoreInfo":                                 reflect.ValueOf((*goxml.FlowerPlantScoreInfo)(nil)),
		"FlowerPlantScoreInfoManager":                          reflect.ValueOf((*goxml.FlowerPlantScoreInfoManager)(nil)),
		"FlowerPlantScoreInfos":                                reflect.ValueOf((*goxml.FlowerPlantScoreInfos)(nil)),
		"FlowerPlantTypeInfo":                                  reflect.ValueOf((*goxml.FlowerPlantTypeInfo)(nil)),
		"FlowerPlantTypeInfoManager":                           reflect.ValueOf((*goxml.FlowerPlantTypeInfoManager)(nil)),
		"FlowerPlantTypeInfos":                                 reflect.ValueOf((*goxml.FlowerPlantTypeInfos)(nil)),
		"ForecastInfo":                                         reflect.ValueOf((*goxml.ForecastInfo)(nil)),
		"ForecastInfoManager":                                  reflect.ValueOf((*goxml.ForecastInfoManager)(nil)),
		"ForecastInfos":                                        reflect.ValueOf((*goxml.ForecastInfos)(nil)),
		"ForecastTask":                                         reflect.ValueOf((*goxml.ForecastTask)(nil)),
		"ForecastTaskInfo":                                     reflect.ValueOf((*goxml.ForecastTaskInfo)(nil)),
		"ForecastTaskInfoManager":                              reflect.ValueOf((*goxml.ForecastTaskInfoManager)(nil)),
		"ForecastTaskInfos":                                    reflect.ValueOf((*goxml.ForecastTaskInfos)(nil)),
		"FormationGroupInfo":                                   reflect.ValueOf((*goxml.FormationGroupInfo)(nil)),
		"FormationGroupInfoExt":                                reflect.ValueOf((*goxml.FormationGroupInfoExt)(nil)),
		"FormationGroupInfoManager":                            reflect.ValueOf((*goxml.FormationGroupInfoManager)(nil)),
		"FormationGroupInfos":                                  reflect.ValueOf((*goxml.FormationGroupInfos)(nil)),
		"FormationInfo":                                        reflect.ValueOf((*goxml.FormationInfo)(nil)),
		"FormationInfoExt":                                     reflect.ValueOf((*goxml.FormationInfoExt)(nil)),
		"FormationInfoManager":                                 reflect.ValueOf((*goxml.FormationInfoManager)(nil)),
		"FormationInfos":                                       reflect.ValueOf((*goxml.FormationInfos)(nil)),
		"FragmentInfo":                                         reflect.ValueOf((*goxml.FragmentInfo)(nil)),
		"FragmentInfoManager":                                  reflect.ValueOf((*goxml.FragmentInfoManager)(nil)),
		"FragmentInfos":                                        reflect.ValueOf((*goxml.FragmentInfos)(nil)),
		"FunctionInfo":                                         reflect.ValueOf((*goxml.FunctionInfo)(nil)),
		"FunctionInfoManager":                                  reflect.ValueOf((*goxml.FunctionInfoManager)(nil)),
		"FunctionInfos":                                        reflect.ValueOf((*goxml.FunctionInfos)(nil)),
		"GameInitialInfo":                                      reflect.ValueOf((*goxml.GameInitialInfo)(nil)),
		"GameInitialInfoManager":                               reflect.ValueOf((*goxml.GameInitialInfoManager)(nil)),
		"GameInitialInfos":                                     reflect.ValueOf((*goxml.GameInitialInfos)(nil)),
		"GemAttrInfo":                                          reflect.ValueOf((*goxml.GemAttrInfo)(nil)),
		"GemAttrInfoManager":                                   reflect.ValueOf((*goxml.GemAttrInfoManager)(nil)),
		"GemAttrInfos":                                         reflect.ValueOf((*goxml.GemAttrInfos)(nil)),
		"GemAttrType":                                          reflect.ValueOf((*goxml.GemAttrType)(nil)),
		"GemComposeInfo":                                       reflect.ValueOf((*goxml.GemComposeInfo)(nil)),
		"GemComposeInfoExt":                                    reflect.ValueOf((*goxml.GemComposeInfoExt)(nil)),
		"GemComposeInfoManager":                                reflect.ValueOf((*goxml.GemComposeInfoManager)(nil)),
		"GemComposeInfos":                                      reflect.ValueOf((*goxml.GemComposeInfos)(nil)),
		"GemInfo":                                              reflect.ValueOf((*goxml.GemInfo)(nil)),
		"GemInfoExt":                                           reflect.ValueOf((*goxml.GemInfoExt)(nil)),
		"GemInfoManager":                                       reflect.ValueOf((*goxml.GemInfoManager)(nil)),
		"GemInfos":                                             reflect.ValueOf((*goxml.GemInfos)(nil)),
		"GemLevelInfo":                                         reflect.ValueOf((*goxml.GemLevelInfo)(nil)),
		"GemLevelInfoExt":                                      reflect.ValueOf((*goxml.GemLevelInfoExt)(nil)),
		"GemLevelInfoManager":                                  reflect.ValueOf((*goxml.GemLevelInfoManager)(nil)),
		"GemLevelInfos":                                        reflect.ValueOf((*goxml.GemLevelInfos)(nil)),
		"GetRecvTask":                                          reflect.ValueOf((*goxml.GetRecvTask)(nil)),
		"GodPresentChangeInfo":                                 reflect.ValueOf((*goxml.GodPresentChangeInfo)(nil)),
		"GodPresentChangeInfoManager":                          reflect.ValueOf((*goxml.GodPresentChangeInfoManager)(nil)),
		"GodPresentChangeInfos":                                reflect.ValueOf((*goxml.GodPresentChangeInfos)(nil)),
		"GodPresentClassInfo":                                  reflect.ValueOf((*goxml.GodPresentClassInfo)(nil)),
		"GodPresentClassInfoExt":                               reflect.ValueOf((*goxml.GodPresentClassInfoExt)(nil)),
		"GodPresentClassInfoManager":                           reflect.ValueOf((*goxml.GodPresentClassInfoManager)(nil)),
		"GodPresentClassInfos":                                 reflect.ValueOf((*goxml.GodPresentClassInfos)(nil)),
		"GodPresentConfigInfo":                                 reflect.ValueOf((*goxml.GodPresentConfigInfo)(nil)),
		"GodPresentConfigInfoManager":                          reflect.ValueOf((*goxml.GodPresentConfigInfoManager)(nil)),
		"GodPresentConfigInfos":                                reflect.ValueOf((*goxml.GodPresentConfigInfos)(nil)),
		"GodPresentGroupExt":                                   reflect.ValueOf((*goxml.GodPresentGroupExt)(nil)),
		"GodPresentGroupInfo":                                  reflect.ValueOf((*goxml.GodPresentGroupInfo)(nil)),
		"GodPresentGroupInfoManager":                           reflect.ValueOf((*goxml.GodPresentGroupInfoManager)(nil)),
		"GodPresentGroupInfos":                                 reflect.ValueOf((*goxml.GodPresentGroupInfos)(nil)),
		"GodPresentInfo":                                       reflect.ValueOf((*goxml.GodPresentInfo)(nil)),
		"GodPresentInfoExt":                                    reflect.ValueOf((*goxml.GodPresentInfoExt)(nil)),
		"GodPresentInfoManager":                                reflect.ValueOf((*goxml.GodPresentInfoManager)(nil)),
		"GodPresentInfos":                                      reflect.ValueOf((*goxml.GodPresentInfos)(nil)),
		"GodPresentRecoveryInfo":                               reflect.ValueOf((*goxml.GodPresentRecoveryInfo)(nil)),
		"GodPresentRecoveryInfoExt":                            reflect.ValueOf((*goxml.GodPresentRecoveryInfoExt)(nil)),
		"GodPresentRecoveryInfoManager":                        reflect.ValueOf((*goxml.GodPresentRecoveryInfoManager)(nil)),
		"GodPresentRecoveryInfos":                              reflect.ValueOf((*goxml.GodPresentRecoveryInfos)(nil)),
		"GoddessCollectionBoxInfo":                             reflect.ValueOf((*goxml.GoddessCollectionBoxInfo)(nil)),
		"GoddessCollectionBoxInfoManager":                      reflect.ValueOf((*goxml.GoddessCollectionBoxInfoManager)(nil)),
		"GoddessCollectionBoxInfos":                            reflect.ValueOf((*goxml.GoddessCollectionBoxInfos)(nil)),
		"GoddessCollectionInfo":                                reflect.ValueOf((*goxml.GoddessCollectionInfo)(nil)),
		"GoddessCollectionInfoManager":                         reflect.ValueOf((*goxml.GoddessCollectionInfoManager)(nil)),
		"GoddessCollectionInfos":                               reflect.ValueOf((*goxml.GoddessCollectionInfos)(nil)),
		"GoddessContractBlessInfo":                             reflect.ValueOf((*goxml.GoddessContractBlessInfo)(nil)),
		"GoddessContractBlessInfoExt":                          reflect.ValueOf((*goxml.GoddessContractBlessInfoExt)(nil)),
		"GoddessContractBlessInfoManager":                      reflect.ValueOf((*goxml.GoddessContractBlessInfoManager)(nil)),
		"GoddessContractBlessInfos":                            reflect.ValueOf((*goxml.GoddessContractBlessInfos)(nil)),
		"GoddessContractEliteInfo":                             reflect.ValueOf((*goxml.GoddessContractEliteInfo)(nil)),
		"GoddessContractEliteInfoManager":                      reflect.ValueOf((*goxml.GoddessContractEliteInfoManager)(nil)),
		"GoddessContractEliteInfos":                            reflect.ValueOf((*goxml.GoddessContractEliteInfos)(nil)),
		"GoddessContractGiftsExt":                              reflect.ValueOf((*goxml.GoddessContractGiftsExt)(nil)),
		"GoddessContractGiftsInfo":                             reflect.ValueOf((*goxml.GoddessContractGiftsInfo)(nil)),
		"GoddessContractGiftsInfoManager":                      reflect.ValueOf((*goxml.GoddessContractGiftsInfoManager)(nil)),
		"GoddessContractGiftsInfos":                            reflect.ValueOf((*goxml.GoddessContractGiftsInfos)(nil)),
		"GoddessContractInfo":                                  reflect.ValueOf((*goxml.GoddessContractInfo)(nil)),
		"GoddessContractInfoManager":                           reflect.ValueOf((*goxml.GoddessContractInfoManager)(nil)),
		"GoddessContractInfos":                                 reflect.ValueOf((*goxml.GoddessContractInfos)(nil)),
		"GoddessContractSkinExt":                               reflect.ValueOf((*goxml.GoddessContractSkinExt)(nil)),
		"GoddessContractSkinInfo":                              reflect.ValueOf((*goxml.GoddessContractSkinInfo)(nil)),
		"GoddessContractSkinInfoManager":                       reflect.ValueOf((*goxml.GoddessContractSkinInfoManager)(nil)),
		"GoddessContractSkinInfos":                             reflect.ValueOf((*goxml.GoddessContractSkinInfos)(nil)),
		"GoddessContractTreatInfo":                             reflect.ValueOf((*goxml.GoddessContractTreatInfo)(nil)),
		"GoddessContractTreatInfoManager":                      reflect.ValueOf((*goxml.GoddessContractTreatInfoManager)(nil)),
		"GoddessContractTreatInfos":                            reflect.ValueOf((*goxml.GoddessContractTreatInfos)(nil)),
		"GoddessContractTrustInfo":                             reflect.ValueOf((*goxml.GoddessContractTrustInfo)(nil)),
		"GoddessContractTrustInfoManager":                      reflect.ValueOf((*goxml.GoddessContractTrustInfoManager)(nil)),
		"GoddessContractTrustInfos":                            reflect.ValueOf((*goxml.GoddessContractTrustInfos)(nil)),
		"GoddessTalesDungeonInfo":                              reflect.ValueOf((*goxml.GoddessTalesDungeonInfo)(nil)),
		"GoddessTalesDungeonInfoExt":                           reflect.ValueOf((*goxml.GoddessTalesDungeonInfoExt)(nil)),
		"GoddessTalesDungeonInfoManager":                       reflect.ValueOf((*goxml.GoddessTalesDungeonInfoManager)(nil)),
		"GoddessTalesDungeonInfos":                             reflect.ValueOf((*goxml.GoddessTalesDungeonInfos)(nil)),
		"GoldBuyInfo":                                          reflect.ValueOf((*goxml.GoldBuyInfo)(nil)),
		"GoldBuyInfoManager":                                   reflect.ValueOf((*goxml.GoldBuyInfoManager)(nil)),
		"GoldBuyInfos":                                         reflect.ValueOf((*goxml.GoldBuyInfos)(nil)),
		"GoldBuyRefreshInfo":                                   reflect.ValueOf((*goxml.GoldBuyRefreshInfo)(nil)),
		"GoldBuyRefreshInfoManager":                            reflect.ValueOf((*goxml.GoldBuyRefreshInfoManager)(nil)),
		"GoldBuyRefreshInfos":                                  reflect.ValueOf((*goxml.GoldBuyRefreshInfos)(nil)),
		"GroupOpenEndTime":                                     reflect.ValueOf((*goxml.GroupOpenEndTime)(nil)),
		"GuidanceInfo":                                         reflect.ValueOf((*goxml.GuidanceInfo)(nil)),
		"GuidanceInfoManager":                                  reflect.ValueOf((*goxml.GuidanceInfoManager)(nil)),
		"GuidanceInfos":                                        reflect.ValueOf((*goxml.GuidanceInfos)(nil)),
		"GuidanceSkipConfigInfo":                               reflect.ValueOf((*goxml.GuidanceSkipConfigInfo)(nil)),
		"GuidanceSkipConfigInfoManager":                        reflect.ValueOf((*goxml.GuidanceSkipConfigInfoManager)(nil)),
		"GuidanceSkipConfigInfos":                              reflect.ValueOf((*goxml.GuidanceSkipConfigInfos)(nil)),
		"GuideGoblin":                                          reflect.ValueOf((*goxml.GuideGoblin)(nil)),
		"GuildBadgeInfo":                                       reflect.ValueOf((*goxml.GuildBadgeInfo)(nil)),
		"GuildBadgeInfoManager":                                reflect.ValueOf((*goxml.GuildBadgeInfoManager)(nil)),
		"GuildBadgeInfos":                                      reflect.ValueOf((*goxml.GuildBadgeInfos)(nil)),
		"GuildBossHpInfo":                                      reflect.ValueOf((*goxml.GuildBossHpInfo)(nil)),
		"GuildBossHpInfoExt":                                   reflect.ValueOf((*goxml.GuildBossHpInfoExt)(nil)),
		"GuildBossHpInfoManager":                               reflect.ValueOf((*goxml.GuildBossHpInfoManager)(nil)),
		"GuildBossHpInfos":                                     reflect.ValueOf((*goxml.GuildBossHpInfos)(nil)),
		"GuildChestInfo":                                       reflect.ValueOf((*goxml.GuildChestInfo)(nil)),
		"GuildChestInfoManager":                                reflect.ValueOf((*goxml.GuildChestInfoManager)(nil)),
		"GuildChestInfos":                                      reflect.ValueOf((*goxml.GuildChestInfos)(nil)),
		"GuildCollectiveDonateInfo":                            reflect.ValueOf((*goxml.GuildCollectiveDonateInfo)(nil)),
		"GuildCollectiveDonateInfoManager":                     reflect.ValueOf((*goxml.GuildCollectiveDonateInfoManager)(nil)),
		"GuildCollectiveDonateInfos":                           reflect.ValueOf((*goxml.GuildCollectiveDonateInfos)(nil)),
		"GuildConfigInfo":                                      reflect.ValueOf((*goxml.GuildConfigInfo)(nil)),
		"GuildConfigInfoManager":                               reflect.ValueOf((*goxml.GuildConfigInfoManager)(nil)),
		"GuildConfigInfos":                                     reflect.ValueOf((*goxml.GuildConfigInfos)(nil)),
		"GuildDonateInfo":                                      reflect.ValueOf((*goxml.GuildDonateInfo)(nil)),
		"GuildDonateInfoManager":                               reflect.ValueOf((*goxml.GuildDonateInfoManager)(nil)),
		"GuildDonateInfos":                                     reflect.ValueOf((*goxml.GuildDonateInfos)(nil)),
		"GuildDungeonArtifactAddInfo":                          reflect.ValueOf((*goxml.GuildDungeonArtifactAddInfo)(nil)),
		"GuildDungeonArtifactAddInfoManager":                   reflect.ValueOf((*goxml.GuildDungeonArtifactAddInfoManager)(nil)),
		"GuildDungeonArtifactAddInfos":                         reflect.ValueOf((*goxml.GuildDungeonArtifactAddInfos)(nil)),
		"GuildDungeonChapterBossInfo":                          reflect.ValueOf((*goxml.GuildDungeonChapterBossInfo)(nil)),
		"GuildDungeonChapterInfo":                              reflect.ValueOf((*goxml.GuildDungeonChapterInfo)(nil)),
		"GuildDungeonChapterInfoExt":                           reflect.ValueOf((*goxml.GuildDungeonChapterInfoExt)(nil)),
		"GuildDungeonChapterInfoManager":                       reflect.ValueOf((*goxml.GuildDungeonChapterInfoManager)(nil)),
		"GuildDungeonChapterInfos":                             reflect.ValueOf((*goxml.GuildDungeonChapterInfos)(nil)),
		"GuildDungeonChapterReward":                            reflect.ValueOf((*goxml.GuildDungeonChapterReward)(nil)),
		"GuildDungeonChapterRewardManager":                     reflect.ValueOf((*goxml.GuildDungeonChapterRewardManager)(nil)),
		"GuildDungeonChapterRewards":                           reflect.ValueOf((*goxml.GuildDungeonChapterRewards)(nil)),
		"GuildDungeonDivisionInfo":                             reflect.ValueOf((*goxml.GuildDungeonDivisionInfo)(nil)),
		"GuildDungeonDivisionInfoManager":                      reflect.ValueOf((*goxml.GuildDungeonDivisionInfoManager)(nil)),
		"GuildDungeonDivisionInfos":                            reflect.ValueOf((*goxml.GuildDungeonDivisionInfos)(nil)),
		"GuildDungeonLevelUpRewardInfo":                        reflect.ValueOf((*goxml.GuildDungeonLevelUpRewardInfo)(nil)),
		"GuildDungeonLevelUpRewardInfoManager":                 reflect.ValueOf((*goxml.GuildDungeonLevelUpRewardInfoManager)(nil)),
		"GuildDungeonLevelUpRewardInfos":                       reflect.ValueOf((*goxml.GuildDungeonLevelUpRewardInfos)(nil)),
		"GuildDungeonPointInfo":                                reflect.ValueOf((*goxml.GuildDungeonPointInfo)(nil)),
		"GuildDungeonPointInfoManager":                         reflect.ValueOf((*goxml.GuildDungeonPointInfoManager)(nil)),
		"GuildDungeonPointInfos":                               reflect.ValueOf((*goxml.GuildDungeonPointInfos)(nil)),
		"GuildDungeonRankRewardInfo":                           reflect.ValueOf((*goxml.GuildDungeonRankRewardInfo)(nil)),
		"GuildDungeonRankRewardInfoManager":                    reflect.ValueOf((*goxml.GuildDungeonRankRewardInfoManager)(nil)),
		"GuildDungeonRankRewardInfos":                          reflect.ValueOf((*goxml.GuildDungeonRankRewardInfos)(nil)),
		"GuildDungeonRewardInfo":                               reflect.ValueOf((*goxml.GuildDungeonRewardInfo)(nil)),
		"GuildDungeonRewardInfoManager":                        reflect.ValueOf((*goxml.GuildDungeonRewardInfoManager)(nil)),
		"GuildDungeonRewardInfos":                              reflect.ValueOf((*goxml.GuildDungeonRewardInfos)(nil)),
		"GuildDungeonSeasonRewardInfo":                         reflect.ValueOf((*goxml.GuildDungeonSeasonRewardInfo)(nil)),
		"GuildDungeonSeasonRewardInfoManager":                  reflect.ValueOf((*goxml.GuildDungeonSeasonRewardInfoManager)(nil)),
		"GuildDungeonSeasonRewardInfos":                        reflect.ValueOf((*goxml.GuildDungeonSeasonRewardInfos)(nil)),
		"GuildDungeonSeasonStrategyInfo":                       reflect.ValueOf((*goxml.GuildDungeonSeasonStrategyInfo)(nil)),
		"GuildDungeonSeasonStrategyInfoManager":                reflect.ValueOf((*goxml.GuildDungeonSeasonStrategyInfoManager)(nil)),
		"GuildDungeonSeasonStrategyInfos":                      reflect.ValueOf((*goxml.GuildDungeonSeasonStrategyInfos)(nil)),
		"GuildDungeonServerInfo":                               reflect.ValueOf((*goxml.GuildDungeonServerInfo)(nil)),
		"GuildDungeonServerInfoManager":                        reflect.ValueOf((*goxml.GuildDungeonServerInfoManager)(nil)),
		"GuildDungeonServerInfos":                              reflect.ValueOf((*goxml.GuildDungeonServerInfos)(nil)),
		"GuildDungeonStrategySkillInfo":                        reflect.ValueOf((*goxml.GuildDungeonStrategySkillInfo)(nil)),
		"GuildDungeonStrategySkillInfoManager":                 reflect.ValueOf((*goxml.GuildDungeonStrategySkillInfoManager)(nil)),
		"GuildDungeonStrategySkillInfos":                       reflect.ValueOf((*goxml.GuildDungeonStrategySkillInfos)(nil)),
		"GuildDungeonTaskInfo":                                 reflect.ValueOf((*goxml.GuildDungeonTaskInfo)(nil)),
		"GuildDungeonTaskInfoManager":                          reflect.ValueOf((*goxml.GuildDungeonTaskInfoManager)(nil)),
		"GuildDungeonTaskInfos":                                reflect.ValueOf((*goxml.GuildDungeonTaskInfos)(nil)),
		"GuildDungeonWeekStrategyInfo":                         reflect.ValueOf((*goxml.GuildDungeonWeekStrategyInfo)(nil)),
		"GuildDungeonWeekStrategyInfoManager":                  reflect.ValueOf((*goxml.GuildDungeonWeekStrategyInfoManager)(nil)),
		"GuildDungeonWeekStrategyInfos":                        reflect.ValueOf((*goxml.GuildDungeonWeekStrategyInfos)(nil)),
		"GuildDungeonWeeklyBaseRewardInfo":                     reflect.ValueOf((*goxml.GuildDungeonWeeklyBaseRewardInfo)(nil)),
		"GuildDungeonWeeklyBaseRewardInfoManager":              reflect.ValueOf((*goxml.GuildDungeonWeeklyBaseRewardInfoManager)(nil)),
		"GuildDungeonWeeklyBaseRewardInfos":                    reflect.ValueOf((*goxml.GuildDungeonWeeklyBaseRewardInfos)(nil)),
		"GuildDungeonWeeklyRewardExt":                          reflect.ValueOf((*goxml.GuildDungeonWeeklyRewardExt)(nil)),
		"GuildDungeonWeeklyRewardInfo":                         reflect.ValueOf((*goxml.GuildDungeonWeeklyRewardInfo)(nil)),
		"GuildDungeonWeeklyRewardInfoManager":                  reflect.ValueOf((*goxml.GuildDungeonWeeklyRewardInfoManager)(nil)),
		"GuildDungeonWeeklyRewardInfos":                        reflect.ValueOf((*goxml.GuildDungeonWeeklyRewardInfos)(nil)),
		"GuildLabelInfo":                                       reflect.ValueOf((*goxml.GuildLabelInfo)(nil)),
		"GuildLabelInfoManager":                                reflect.ValueOf((*goxml.GuildLabelInfoManager)(nil)),
		"GuildLabelInfos":                                      reflect.ValueOf((*goxml.GuildLabelInfos)(nil)),
		"GuildLevelInfo":                                       reflect.ValueOf((*goxml.GuildLevelInfo)(nil)),
		"GuildLevelInfoManager":                                reflect.ValueOf((*goxml.GuildLevelInfoManager)(nil)),
		"GuildLevelInfos":                                      reflect.ValueOf((*goxml.GuildLevelInfos)(nil)),
		"GuildLogInfo":                                         reflect.ValueOf((*goxml.GuildLogInfo)(nil)),
		"GuildLogInfoManager":                                  reflect.ValueOf((*goxml.GuildLogInfoManager)(nil)),
		"GuildLogInfos":                                        reflect.ValueOf((*goxml.GuildLogInfos)(nil)),
		"GuildMedalInfo":                                       reflect.ValueOf((*goxml.GuildMedalInfo)(nil)),
		"GuildMedalInfoManager":                                reflect.ValueOf((*goxml.GuildMedalInfoManager)(nil)),
		"GuildMedalInfos":                                      reflect.ValueOf((*goxml.GuildMedalInfos)(nil)),
		"GuildMedalTaskInfo":                                   reflect.ValueOf((*goxml.GuildMedalTaskInfo)(nil)),
		"GuildMedalTaskInfoManager":                            reflect.ValueOf((*goxml.GuildMedalTaskInfoManager)(nil)),
		"GuildMedalTaskInfos":                                  reflect.ValueOf((*goxml.GuildMedalTaskInfos)(nil)),
		"GuildMobilizationInfo":                                reflect.ValueOf((*goxml.GuildMobilizationInfo)(nil)),
		"GuildMobilizationInfoExt":                             reflect.ValueOf((*goxml.GuildMobilizationInfoExt)(nil)),
		"GuildMobilizationInfoManager":                         reflect.ValueOf((*goxml.GuildMobilizationInfoManager)(nil)),
		"GuildMobilizationInfos":                               reflect.ValueOf((*goxml.GuildMobilizationInfos)(nil)),
		"GuildMobilizationRankRewardInfo":                      reflect.ValueOf((*goxml.GuildMobilizationRankRewardInfo)(nil)),
		"GuildMobilizationRankRewardInfoManager":               reflect.ValueOf((*goxml.GuildMobilizationRankRewardInfoManager)(nil)),
		"GuildMobilizationRankRewardInfos":                     reflect.ValueOf((*goxml.GuildMobilizationRankRewardInfos)(nil)),
		"GuildMobilizationRewardInfo":                          reflect.ValueOf((*goxml.GuildMobilizationRewardInfo)(nil)),
		"GuildMobilizationRewardInfoManager":                   reflect.ValueOf((*goxml.GuildMobilizationRewardInfoManager)(nil)),
		"GuildMobilizationRewardInfos":                         reflect.ValueOf((*goxml.GuildMobilizationRewardInfos)(nil)),
		"GuildMobilizationScoreRewardInfo":                     reflect.ValueOf((*goxml.GuildMobilizationScoreRewardInfo)(nil)),
		"GuildMobilizationScoreRewardInfoManager":              reflect.ValueOf((*goxml.GuildMobilizationScoreRewardInfoManager)(nil)),
		"GuildMobilizationScoreRewardInfos":                    reflect.ValueOf((*goxml.GuildMobilizationScoreRewardInfos)(nil)),
		"GuildMobilizationTaskInfo":                            reflect.ValueOf((*goxml.GuildMobilizationTaskInfo)(nil)),
		"GuildMobilizationTaskInfoManager":                     reflect.ValueOf((*goxml.GuildMobilizationTaskInfoManager)(nil)),
		"GuildMobilizationTaskInfos":                           reflect.ValueOf((*goxml.GuildMobilizationTaskInfos)(nil)),
		"GuildQuitCdInfo":                                      reflect.ValueOf((*goxml.GuildQuitCdInfo)(nil)),
		"GuildQuitCdInfoManager":                               reflect.ValueOf((*goxml.GuildQuitCdInfoManager)(nil)),
		"GuildQuitCdInfos":                                     reflect.ValueOf((*goxml.GuildQuitCdInfos)(nil)),
		"GuildSandTableArenaInfo":                              reflect.ValueOf((*goxml.GuildSandTableArenaInfo)(nil)),
		"GuildSandTableArenaInfoM":                             reflect.ValueOf((*goxml.GuildSandTableArenaInfoM)(nil)),
		"GuildSandTableArenaInfoMArenaGroupIndex":              reflect.ValueOf((*goxml.GuildSandTableArenaInfoMArenaGroupIndex)(nil)),
		"GuildSandTableArenaInfoMArenaGroupMatchIndexIndex":    reflect.ValueOf((*goxml.GuildSandTableArenaInfoMArenaGroupMatchIndexIndex)(nil)),
		"GuildSandTableArenaInfoXml":                           reflect.ValueOf((*goxml.GuildSandTableArenaInfoXml)(nil)),
		"GuildSandTableArenaInfoXmls":                          reflect.ValueOf((*goxml.GuildSandTableArenaInfoXmls)(nil)),
		"GuildSandTableBlessingLevelInfo":                      reflect.ValueOf((*goxml.GuildSandTableBlessingLevelInfo)(nil)),
		"GuildSandTableBlessingLevelInfoManager":               reflect.ValueOf((*goxml.GuildSandTableBlessingLevelInfoManager)(nil)),
		"GuildSandTableBlessingLevelInfos":                     reflect.ValueOf((*goxml.GuildSandTableBlessingLevelInfos)(nil)),
		"GuildSandTableBuildInfo":                              reflect.ValueOf((*goxml.GuildSandTableBuildInfo)(nil)),
		"GuildSandTableBuildInfoManager":                       reflect.ValueOf((*goxml.GuildSandTableBuildInfoManager)(nil)),
		"GuildSandTableBuildInfos":                             reflect.ValueOf((*goxml.GuildSandTableBuildInfos)(nil)),
		"GuildSandTableBuildTaskInfo":                          reflect.ValueOf((*goxml.GuildSandTableBuildTaskInfo)(nil)),
		"GuildSandTableBuildTaskInfoManager":                   reflect.ValueOf((*goxml.GuildSandTableBuildTaskInfoManager)(nil)),
		"GuildSandTableBuildTaskInfos":                         reflect.ValueOf((*goxml.GuildSandTableBuildTaskInfos)(nil)),
		"GuildSandTableChallengeBotInfo":                       reflect.ValueOf((*goxml.GuildSandTableChallengeBotInfo)(nil)),
		"GuildSandTableChallengeBotInfoM":                      reflect.ValueOf((*goxml.GuildSandTableChallengeBotInfoM)(nil)),
		"GuildSandTableChallengeBotInfoMEx":                    reflect.ValueOf((*goxml.GuildSandTableChallengeBotInfoMEx)(nil)),
		"GuildSandTableChallengeBotInfoXml":                    reflect.ValueOf((*goxml.GuildSandTableChallengeBotInfoXml)(nil)),
		"GuildSandTableChallengeBotInfoXmls":                   reflect.ValueOf((*goxml.GuildSandTableChallengeBotInfoXmls)(nil)),
		"GuildSandTableChallengeBuffInfo":                      reflect.ValueOf((*goxml.GuildSandTableChallengeBuffInfo)(nil)),
		"GuildSandTableChallengeBuffInfoM":                     reflect.ValueOf((*goxml.GuildSandTableChallengeBuffInfoM)(nil)),
		"GuildSandTableChallengeBuffInfoMEx":                   reflect.ValueOf((*goxml.GuildSandTableChallengeBuffInfoMEx)(nil)),
		"GuildSandTableChallengeBuffInfoXml":                   reflect.ValueOf((*goxml.GuildSandTableChallengeBuffInfoXml)(nil)),
		"GuildSandTableChallengeBuffInfoXmls":                  reflect.ValueOf((*goxml.GuildSandTableChallengeBuffInfoXmls)(nil)),
		"GuildSandTableChallengeGuildRankInfo":                 reflect.ValueOf((*goxml.GuildSandTableChallengeGuildRankInfo)(nil)),
		"GuildSandTableChallengeGuildRankInfoM":                reflect.ValueOf((*goxml.GuildSandTableChallengeGuildRankInfoM)(nil)),
		"GuildSandTableChallengeGuildRankInfoMMatchIndexIndex": reflect.ValueOf((*goxml.GuildSandTableChallengeGuildRankInfoMMatchIndexIndex)(nil)),
		"GuildSandTableChallengeGuildRankInfoXml":              reflect.ValueOf((*goxml.GuildSandTableChallengeGuildRankInfoXml)(nil)),
		"GuildSandTableChallengeGuildRankInfoXmls":             reflect.ValueOf((*goxml.GuildSandTableChallengeGuildRankInfoXmls)(nil)),
		"GuildSandTableChallengeInfo":                          reflect.ValueOf((*goxml.GuildSandTableChallengeInfo)(nil)),
		"GuildSandTableChallengeInfoM":                         reflect.ValueOf((*goxml.GuildSandTableChallengeInfoM)(nil)),
		"GuildSandTableChallengeInfoMEx":                       reflect.ValueOf((*goxml.GuildSandTableChallengeInfoMEx)(nil)),
		"GuildSandTableChallengeInfoMFightGroupIndex":          reflect.ValueOf((*goxml.GuildSandTableChallengeInfoMFightGroupIndex)(nil)),
		"GuildSandTableChallengeInfoXml":                       reflect.ValueOf((*goxml.GuildSandTableChallengeInfoXml)(nil)),
		"GuildSandTableChallengeInfoXmls":                      reflect.ValueOf((*goxml.GuildSandTableChallengeInfoXmls)(nil)),
		"GuildSandTableChallengeMatchInfo":                     reflect.ValueOf((*goxml.GuildSandTableChallengeMatchInfo)(nil)),
		"GuildSandTableChallengeMatchInfoM":                    reflect.ValueOf((*goxml.GuildSandTableChallengeMatchInfoM)(nil)),
		"GuildSandTableChallengeMatchInfoXml":                  reflect.ValueOf((*goxml.GuildSandTableChallengeMatchInfoXml)(nil)),
		"GuildSandTableChallengeMatchInfoXmls":                 reflect.ValueOf((*goxml.GuildSandTableChallengeMatchInfoXmls)(nil)),
		"GuildSandTableChallengeRankRewardInfo":                reflect.ValueOf((*goxml.GuildSandTableChallengeRankRewardInfo)(nil)),
		"GuildSandTableChallengeRankRewardInfoM":               reflect.ValueOf((*goxml.GuildSandTableChallengeRankRewardInfoM)(nil)),
		"GuildSandTableChallengeRankRewardInfoXml":             reflect.ValueOf((*goxml.GuildSandTableChallengeRankRewardInfoXml)(nil)),
		"GuildSandTableChallengeRankRewardInfoXmls":            reflect.ValueOf((*goxml.GuildSandTableChallengeRankRewardInfoXmls)(nil)),
		"GuildSandTableChallengeTaskInfo":                      reflect.ValueOf((*goxml.GuildSandTableChallengeTaskInfo)(nil)),
		"GuildSandTableChallengeTaskInfoM":                     reflect.ValueOf((*goxml.GuildSandTableChallengeTaskInfoM)(nil)),
		"GuildSandTableChallengeTaskInfoMEx":                   reflect.ValueOf((*goxml.GuildSandTableChallengeTaskInfoMEx)(nil)),
		"GuildSandTableChallengeTaskInfoXml":                   reflect.ValueOf((*goxml.GuildSandTableChallengeTaskInfoXml)(nil)),
		"GuildSandTableChallengeTaskInfoXmls":                  reflect.ValueOf((*goxml.GuildSandTableChallengeTaskInfoXmls)(nil)),
		"GuildSandTableConfigInfo":                             reflect.ValueOf((*goxml.GuildSandTableConfigInfo)(nil)),
		"GuildSandTableConfigInfoManager":                      reflect.ValueOf((*goxml.GuildSandTableConfigInfoManager)(nil)),
		"GuildSandTableConfigInfos":                            reflect.ValueOf((*goxml.GuildSandTableConfigInfos)(nil)),
		"GuildSandTableContributionAwardInfo":                  reflect.ValueOf((*goxml.GuildSandTableContributionAwardInfo)(nil)),
		"GuildSandTableContributionAwardInfoManager":           reflect.ValueOf((*goxml.GuildSandTableContributionAwardInfoManager)(nil)),
		"GuildSandTableContributionAwardInfos":                 reflect.ValueOf((*goxml.GuildSandTableContributionAwardInfos)(nil)),
		"GuildSandTableDragonBossHpInfo":                       reflect.ValueOf((*goxml.GuildSandTableDragonBossHpInfo)(nil)),
		"GuildSandTableDragonBossHpInfoM":                      reflect.ValueOf((*goxml.GuildSandTableDragonBossHpInfoM)(nil)),
		"GuildSandTableDragonBossHpInfoMEx":                    reflect.ValueOf((*goxml.GuildSandTableDragonBossHpInfoMEx)(nil)),
		"GuildSandTableDragonBossHpInfoMIdIndex":               reflect.ValueOf((*goxml.GuildSandTableDragonBossHpInfoMIdIndex)(nil)),
		"GuildSandTableDragonBossHpInfoXml":                    reflect.ValueOf((*goxml.GuildSandTableDragonBossHpInfoXml)(nil)),
		"GuildSandTableDragonBossHpInfoXmls":                   reflect.ValueOf((*goxml.GuildSandTableDragonBossHpInfoXmls)(nil)),
		"GuildSandTableDragonBotInfo":                          reflect.ValueOf((*goxml.GuildSandTableDragonBotInfo)(nil)),
		"GuildSandTableDragonBotInfoM":                         reflect.ValueOf((*goxml.GuildSandTableDragonBotInfoM)(nil)),
		"GuildSandTableDragonBotInfoXml":                       reflect.ValueOf((*goxml.GuildSandTableDragonBotInfoXml)(nil)),
		"GuildSandTableDragonBotInfoXmls":                      reflect.ValueOf((*goxml.GuildSandTableDragonBotInfoXmls)(nil)),
		"GuildSandTableDragonEvolveInfo":                       reflect.ValueOf((*goxml.GuildSandTableDragonEvolveInfo)(nil)),
		"GuildSandTableDragonEvolveInfoM":                      reflect.ValueOf((*goxml.GuildSandTableDragonEvolveInfoM)(nil)),
		"GuildSandTableDragonEvolveInfoMEx":                    reflect.ValueOf((*goxml.GuildSandTableDragonEvolveInfoMEx)(nil)),
		"GuildSandTableDragonEvolveInfoXml":                    reflect.ValueOf((*goxml.GuildSandTableDragonEvolveInfoXml)(nil)),
		"GuildSandTableDragonEvolveInfoXmls":                   reflect.ValueOf((*goxml.GuildSandTableDragonEvolveInfoXmls)(nil)),
		"GuildSandTableDragonInfo":                             reflect.ValueOf((*goxml.GuildSandTableDragonInfo)(nil)),
		"GuildSandTableDragonInfoM":                            reflect.ValueOf((*goxml.GuildSandTableDragonInfoM)(nil)),
		"GuildSandTableDragonInfoMDragonIdIndex":               reflect.ValueOf((*goxml.GuildSandTableDragonInfoMDragonIdIndex)(nil)),
		"GuildSandTableDragonInfoMEx":                          reflect.ValueOf((*goxml.GuildSandTableDragonInfoMEx)(nil)),
		"GuildSandTableDragonInfoXml":                          reflect.ValueOf((*goxml.GuildSandTableDragonInfoXml)(nil)),
		"GuildSandTableDragonInfoXmls":                         reflect.ValueOf((*goxml.GuildSandTableDragonInfoXmls)(nil)),
		"GuildSandTableDragonRewardInfo":                       reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfo)(nil)),
		"GuildSandTableDragonRewardInfoM":                      reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfoM)(nil)),
		"GuildSandTableDragonRewardInfoMDropGroupIndex":        reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfoMDropGroupIndex)(nil)),
		"GuildSandTableDragonRewardInfoMDropGroupLevelIndex":   reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfoMDropGroupLevelIndex)(nil)),
		"GuildSandTableDragonRewardInfoMEx":                    reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfoMEx)(nil)),
		"GuildSandTableDragonRewardInfoXml":                    reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfoXml)(nil)),
		"GuildSandTableDragonRewardInfoXmls":                   reflect.ValueOf((*goxml.GuildSandTableDragonRewardInfoXmls)(nil)),
		"GuildSandTableDragonScheduleInfo":                     reflect.ValueOf((*goxml.GuildSandTableDragonScheduleInfo)(nil)),
		"GuildSandTableDragonScheduleInfoM":                    reflect.ValueOf((*goxml.GuildSandTableDragonScheduleInfoM)(nil)),
		"GuildSandTableDragonScheduleInfoMEx":                  reflect.ValueOf((*goxml.GuildSandTableDragonScheduleInfoMEx)(nil)),
		"GuildSandTableDragonScheduleInfoMIdIndex":             reflect.ValueOf((*goxml.GuildSandTableDragonScheduleInfoMIdIndex)(nil)),
		"GuildSandTableDragonScheduleInfoXml":                  reflect.ValueOf((*goxml.GuildSandTableDragonScheduleInfoXml)(nil)),
		"GuildSandTableDragonScheduleInfoXmls":                 reflect.ValueOf((*goxml.GuildSandTableDragonScheduleInfoXmls)(nil)),
		"GuildSandTableDragonTaskRewardInfo":                   reflect.ValueOf((*goxml.GuildSandTableDragonTaskRewardInfo)(nil)),
		"GuildSandTableDragonTaskRewardInfoM":                  reflect.ValueOf((*goxml.GuildSandTableDragonTaskRewardInfoM)(nil)),
		"GuildSandTableDragonTaskRewardInfoMEx":                reflect.ValueOf((*goxml.GuildSandTableDragonTaskRewardInfoMEx)(nil)),
		"GuildSandTableDragonTaskRewardInfoXml":                reflect.ValueOf((*goxml.GuildSandTableDragonTaskRewardInfoXml)(nil)),
		"GuildSandTableDragonTaskRewardInfoXmls":               reflect.ValueOf((*goxml.GuildSandTableDragonTaskRewardInfoXmls)(nil)),
		"GuildSandTableFatigueInfo":                            reflect.ValueOf((*goxml.GuildSandTableFatigueInfo)(nil)),
		"GuildSandTableFatigueInfoExt":                         reflect.ValueOf((*goxml.GuildSandTableFatigueInfoExt)(nil)),
		"GuildSandTableFatigueInfoManager":                     reflect.ValueOf((*goxml.GuildSandTableFatigueInfoManager)(nil)),
		"GuildSandTableFatigueInfos":                           reflect.ValueOf((*goxml.GuildSandTableFatigueInfos)(nil)),
		"GuildSandTableGoddessInfo":                            reflect.ValueOf((*goxml.GuildSandTableGoddessInfo)(nil)),
		"GuildSandTableGoddessInfoManager":                     reflect.ValueOf((*goxml.GuildSandTableGoddessInfoManager)(nil)),
		"GuildSandTableGoddessInfos":                           reflect.ValueOf((*goxml.GuildSandTableGoddessInfos)(nil)),
		"GuildSandTableHeroWorkInfo":                           reflect.ValueOf((*goxml.GuildSandTableHeroWorkInfo)(nil)),
		"GuildSandTableHeroWorkInfoManager":                    reflect.ValueOf((*goxml.GuildSandTableHeroWorkInfoManager)(nil)),
		"GuildSandTableHeroWorkInfos":                          reflect.ValueOf((*goxml.GuildSandTableHeroWorkInfos)(nil)),
		"GuildSandTableHomeAwardInfo":                          reflect.ValueOf((*goxml.GuildSandTableHomeAwardInfo)(nil)),
		"GuildSandTableHomeAwardInfoManager":                   reflect.ValueOf((*goxml.GuildSandTableHomeAwardInfoManager)(nil)),
		"GuildSandTableHomeAwardInfos":                         reflect.ValueOf((*goxml.GuildSandTableHomeAwardInfos)(nil)),
		"GuildSandTableInfo":                                   reflect.ValueOf((*goxml.GuildSandTableInfo)(nil)),
		"GuildSandTableInfoExt":                                reflect.ValueOf((*goxml.GuildSandTableInfoExt)(nil)),
		"GuildSandTableInfoManager":                            reflect.ValueOf((*goxml.GuildSandTableInfoManager)(nil)),
		"GuildSandTableInfos":                                  reflect.ValueOf((*goxml.GuildSandTableInfos)(nil)),
		"GuildSandTableLandInfo":                               reflect.ValueOf((*goxml.GuildSandTableLandInfo)(nil)),
		"GuildSandTableLandInfoManager":                        reflect.ValueOf((*goxml.GuildSandTableLandInfoManager)(nil)),
		"GuildSandTableLandInfos":                              reflect.ValueOf((*goxml.GuildSandTableLandInfos)(nil)),
		"GuildSandTableLinkInfo":                               reflect.ValueOf((*goxml.GuildSandTableLinkInfo)(nil)),
		"GuildSandTableLinkInfoExt":                            reflect.ValueOf((*goxml.GuildSandTableLinkInfoExt)(nil)),
		"GuildSandTableLinkInfoManager":                        reflect.ValueOf((*goxml.GuildSandTableLinkInfoManager)(nil)),
		"GuildSandTableLinkInfos":                              reflect.ValueOf((*goxml.GuildSandTableLinkInfos)(nil)),
		"GuildSandTableMapGroupInfo":                           reflect.ValueOf((*goxml.GuildSandTableMapGroupInfo)(nil)),
		"GuildSandTableMapGroupInfoManager":                    reflect.ValueOf((*goxml.GuildSandTableMapGroupInfoManager)(nil)),
		"GuildSandTableMapGroupInfos":                          reflect.ValueOf((*goxml.GuildSandTableMapGroupInfos)(nil)),
		"GuildSandTableMapInfo":                                reflect.ValueOf((*goxml.GuildSandTableMapInfo)(nil)),
		"GuildSandTableMapInfoManager":                         reflect.ValueOf((*goxml.GuildSandTableMapInfoManager)(nil)),
		"GuildSandTableMapInfos":                               reflect.ValueOf((*goxml.GuildSandTableMapInfos)(nil)),
		"GuildSandTableMonsterGroupInfo":                       reflect.ValueOf((*goxml.GuildSandTableMonsterGroupInfo)(nil)),
		"GuildSandTableMonsterGroupInfoManager":                reflect.ValueOf((*goxml.GuildSandTableMonsterGroupInfoManager)(nil)),
		"GuildSandTableMonsterGroupInfos":                      reflect.ValueOf((*goxml.GuildSandTableMonsterGroupInfos)(nil)),
		"GuildSandTableMoraleBuffInfo":                         reflect.ValueOf((*goxml.GuildSandTableMoraleBuffInfo)(nil)),
		"GuildSandTableMoraleBuffInfoM":                        reflect.ValueOf((*goxml.GuildSandTableMoraleBuffInfoM)(nil)),
		"GuildSandTableMoraleBuffInfoXml":                      reflect.ValueOf((*goxml.GuildSandTableMoraleBuffInfoXml)(nil)),
		"GuildSandTableMoraleBuffInfoXmls":                     reflect.ValueOf((*goxml.GuildSandTableMoraleBuffInfoXmls)(nil)),
		"GuildSandTableMoraleInfo":                             reflect.ValueOf((*goxml.GuildSandTableMoraleInfo)(nil)),
		"GuildSandTableMoraleInfoM":                            reflect.ValueOf((*goxml.GuildSandTableMoraleInfoM)(nil)),
		"GuildSandTableMoraleInfoMSeasonIdIndex":               reflect.ValueOf((*goxml.GuildSandTableMoraleInfoMSeasonIdIndex)(nil)),
		"GuildSandTableMoraleInfoXml":                          reflect.ValueOf((*goxml.GuildSandTableMoraleInfoXml)(nil)),
		"GuildSandTableMoraleInfoXmls":                         reflect.ValueOf((*goxml.GuildSandTableMoraleInfoXmls)(nil)),
		"GuildSandTableOreBossAwardInfo":                       reflect.ValueOf((*goxml.GuildSandTableOreBossAwardInfo)(nil)),
		"GuildSandTableOreBossAwardInfoManager":                reflect.ValueOf((*goxml.GuildSandTableOreBossAwardInfoManager)(nil)),
		"GuildSandTableOreBossAwardInfos":                      reflect.ValueOf((*goxml.GuildSandTableOreBossAwardInfos)(nil)),
		"GuildSandTableOreBossHpInfo":                          reflect.ValueOf((*goxml.GuildSandTableOreBossHpInfo)(nil)),
		"GuildSandTableOreBossHpInfoEx":                        reflect.ValueOf((*goxml.GuildSandTableOreBossHpInfoEx)(nil)),
		"GuildSandTableOreBossHpInfoManager":                   reflect.ValueOf((*goxml.GuildSandTableOreBossHpInfoManager)(nil)),
		"GuildSandTableOreBossHpInfos":                         reflect.ValueOf((*goxml.GuildSandTableOreBossHpInfos)(nil)),
		"GuildSandTableOreLevelInfo":                           reflect.ValueOf((*goxml.GuildSandTableOreLevelInfo)(nil)),
		"GuildSandTableOreLevelInfoManager":                    reflect.ValueOf((*goxml.GuildSandTableOreLevelInfoManager)(nil)),
		"GuildSandTableOreLevelInfos":                          reflect.ValueOf((*goxml.GuildSandTableOreLevelInfos)(nil)),
		"GuildSandTableOreMonsterInfo":                         reflect.ValueOf((*goxml.GuildSandTableOreMonsterInfo)(nil)),
		"GuildSandTableOreMonsterInfoManager":                  reflect.ValueOf((*goxml.GuildSandTableOreMonsterInfoManager)(nil)),
		"GuildSandTableOreMonsterInfos":                        reflect.ValueOf((*goxml.GuildSandTableOreMonsterInfos)(nil)),
		"GuildSandTablePointInfo":                              reflect.ValueOf((*goxml.GuildSandTablePointInfo)(nil)),
		"GuildSandTablePointInfoManager":                       reflect.ValueOf((*goxml.GuildSandTablePointInfoManager)(nil)),
		"GuildSandTablePointInfos":                             reflect.ValueOf((*goxml.GuildSandTablePointInfos)(nil)),
		"GuildSandTablePopularityInfo":                         reflect.ValueOf((*goxml.GuildSandTablePopularityInfo)(nil)),
		"GuildSandTablePopularityInfoM":                        reflect.ValueOf((*goxml.GuildSandTablePopularityInfoM)(nil)),
		"GuildSandTablePopularityInfoXml":                      reflect.ValueOf((*goxml.GuildSandTablePopularityInfoXml)(nil)),
		"GuildSandTablePopularityInfoXmls":                     reflect.ValueOf((*goxml.GuildSandTablePopularityInfoXmls)(nil)),
		"GuildSandTableSiegeBossAwardInfo":                     reflect.ValueOf((*goxml.GuildSandTableSiegeBossAwardInfo)(nil)),
		"GuildSandTableSiegeBossAwardInfoM":                    reflect.ValueOf((*goxml.GuildSandTableSiegeBossAwardInfoM)(nil)),
		"GuildSandTableSiegeBossAwardInfoMDamageGroupIndex":    reflect.ValueOf((*goxml.GuildSandTableSiegeBossAwardInfoMDamageGroupIndex)(nil)),
		"GuildSandTableSiegeBossAwardInfoXml":                  reflect.ValueOf((*goxml.GuildSandTableSiegeBossAwardInfoXml)(nil)),
		"GuildSandTableSiegeBossAwardInfoXmls":                 reflect.ValueOf((*goxml.GuildSandTableSiegeBossAwardInfoXmls)(nil)),
		"GuildSandTableSiegeBossHpInfo":                        reflect.ValueOf((*goxml.GuildSandTableSiegeBossHpInfo)(nil)),
		"GuildSandTableSiegeBossHpInfoM":                       reflect.ValueOf((*goxml.GuildSandTableSiegeBossHpInfoM)(nil)),
		"GuildSandTableSiegeBossHpInfoMEx":                     reflect.ValueOf((*goxml.GuildSandTableSiegeBossHpInfoMEx)(nil)),
		"GuildSandTableSiegeBossHpInfoXml":                     reflect.ValueOf((*goxml.GuildSandTableSiegeBossHpInfoXml)(nil)),
		"GuildSandTableSiegeBossHpInfoXmls":                    reflect.ValueOf((*goxml.GuildSandTableSiegeBossHpInfoXmls)(nil)),
		"GuildSandTableSiegeGroupInfo":                         reflect.ValueOf((*goxml.GuildSandTableSiegeGroupInfo)(nil)),
		"GuildSandTableSiegeGroupInfoM":                        reflect.ValueOf((*goxml.GuildSandTableSiegeGroupInfoM)(nil)),
		"GuildSandTableSiegeGroupInfoMSiegeGroupIndex":         reflect.ValueOf((*goxml.GuildSandTableSiegeGroupInfoMSiegeGroupIndex)(nil)),
		"GuildSandTableSiegeGroupInfoXml":                      reflect.ValueOf((*goxml.GuildSandTableSiegeGroupInfoXml)(nil)),
		"GuildSandTableSiegeGroupInfoXmls":                     reflect.ValueOf((*goxml.GuildSandTableSiegeGroupInfoXmls)(nil)),
		"GuildSandTableSiegeMonsterInfo":                       reflect.ValueOf((*goxml.GuildSandTableSiegeMonsterInfo)(nil)),
		"GuildSandTableSiegeMonsterInfoM":                      reflect.ValueOf((*goxml.GuildSandTableSiegeMonsterInfoM)(nil)),
		"GuildSandTableSiegeMonsterInfoXml":                    reflect.ValueOf((*goxml.GuildSandTableSiegeMonsterInfoXml)(nil)),
		"GuildSandTableSiegeMonsterInfoXmls":                   reflect.ValueOf((*goxml.GuildSandTableSiegeMonsterInfoXmls)(nil)),
		"GuildSandTableTaskAwardInfo":                          reflect.ValueOf((*goxml.GuildSandTableTaskAwardInfo)(nil)),
		"GuildSandTableTaskAwardInfoManager":                   reflect.ValueOf((*goxml.GuildSandTableTaskAwardInfoManager)(nil)),
		"GuildSandTableTaskAwardInfos":                         reflect.ValueOf((*goxml.GuildSandTableTaskAwardInfos)(nil)),
		"GuildSandTableTechBaseInfo":                           reflect.ValueOf((*goxml.GuildSandTableTechBaseInfo)(nil)),
		"GuildSandTableTechBaseInfos":                          reflect.ValueOf((*goxml.GuildSandTableTechBaseInfos)(nil)),
		"GuildSandTableTechLevelInfo":                          reflect.ValueOf((*goxml.GuildSandTableTechLevelInfo)(nil)),
		"GuildSandTableTechLevelInfoExt":                       reflect.ValueOf((*goxml.GuildSandTableTechLevelInfoExt)(nil)),
		"GuildSandTableTechLevelInfoManager":                   reflect.ValueOf((*goxml.GuildSandTableTechLevelInfoManager)(nil)),
		"GuildSandTableTechLevelInfos":                         reflect.ValueOf((*goxml.GuildSandTableTechLevelInfos)(nil)),
		"GuildSandTableTechTaskInfo":                           reflect.ValueOf((*goxml.GuildSandTableTechTaskInfo)(nil)),
		"GuildSandTableTechTaskInfoManager":                    reflect.ValueOf((*goxml.GuildSandTableTechTaskInfoManager)(nil)),
		"GuildSandTableTechTaskInfos":                          reflect.ValueOf((*goxml.GuildSandTableTechTaskInfos)(nil)),
		"GuildTalentConditionInfo":                             reflect.ValueOf((*goxml.GuildTalentConditionInfo)(nil)),
		"GuildTalentConditionInfoExt":                          reflect.ValueOf((*goxml.GuildTalentConditionInfoExt)(nil)),
		"GuildTalentConditionInfoManager":                      reflect.ValueOf((*goxml.GuildTalentConditionInfoManager)(nil)),
		"GuildTalentConditionInfos":                            reflect.ValueOf((*goxml.GuildTalentConditionInfos)(nil)),
		"GuildTalentInfo":                                      reflect.ValueOf((*goxml.GuildTalentInfo)(nil)),
		"GuildTalentInfoExt":                                   reflect.ValueOf((*goxml.GuildTalentInfoExt)(nil)),
		"GuildTalentInfoManager":                               reflect.ValueOf((*goxml.GuildTalentInfoManager)(nil)),
		"GuildTalentInfos":                                     reflect.ValueOf((*goxml.GuildTalentInfos)(nil)),
		"HasRankBySort":                                        reflect.ValueOf((*goxml.HasRankBySort)(nil)),
		"HeroArchiveInfo":                                      reflect.ValueOf((*goxml.HeroArchiveInfo)(nil)),
		"HeroArchiveInfoExt":                                   reflect.ValueOf((*goxml.HeroArchiveInfoExt)(nil)),
		"HeroArchiveInfoManager":                               reflect.ValueOf((*goxml.HeroArchiveInfoManager)(nil)),
		"HeroArchiveInfos":                                     reflect.ValueOf((*goxml.HeroArchiveInfos)(nil)),
		"HeroAwakeInfo":                                        reflect.ValueOf((*goxml.HeroAwakeInfo)(nil)),
		"HeroAwakeInfoManager":                                 reflect.ValueOf((*goxml.HeroAwakeInfoManager)(nil)),
		"HeroAwakeInfos":                                       reflect.ValueOf((*goxml.HeroAwakeInfos)(nil)),
		"HeroAwakenInfo":                                       reflect.ValueOf((*goxml.HeroAwakenInfo)(nil)),
		"HeroAwakenItemCount":                                  reflect.ValueOf((*goxml.HeroAwakenItemCount)(nil)),
		"HeroAwakenLevelInfo":                                  reflect.ValueOf((*goxml.HeroAwakenLevelInfo)(nil)),
		"HeroAwakenLevelInfoManager":                           reflect.ValueOf((*goxml.HeroAwakenLevelInfoManager)(nil)),
		"HeroAwakenLevelInfos":                                 reflect.ValueOf((*goxml.HeroAwakenLevelInfos)(nil)),
		"HeroBalanceInfo":                                      reflect.ValueOf((*goxml.HeroBalanceInfo)(nil)),
		"HeroBalanceInfoExt":                                   reflect.ValueOf((*goxml.HeroBalanceInfoExt)(nil)),
		"HeroBalanceInfoManager":                               reflect.ValueOf((*goxml.HeroBalanceInfoManager)(nil)),
		"HeroBalanceInfos":                                     reflect.ValueOf((*goxml.HeroBalanceInfos)(nil)),
		"HeroDataInfo":                                         reflect.ValueOf((*goxml.HeroDataInfo)(nil)),
		"HeroDataInfoManager":                                  reflect.ValueOf((*goxml.HeroDataInfoManager)(nil)),
		"HeroDataInfos":                                        reflect.ValueOf((*goxml.HeroDataInfos)(nil)),
		"HeroExchangeInfo":                                     reflect.ValueOf((*goxml.HeroExchangeInfo)(nil)),
		"HeroExchangeInfoManager":                              reflect.ValueOf((*goxml.HeroExchangeInfoManager)(nil)),
		"HeroExchangeInfos":                                    reflect.ValueOf((*goxml.HeroExchangeInfos)(nil)),
		"HeroFetterInfo":                                       reflect.ValueOf((*goxml.HeroFetterInfo)(nil)),
		"HeroFetterInfoExt":                                    reflect.ValueOf((*goxml.HeroFetterInfoExt)(nil)),
		"HeroFetterInfoManager":                                reflect.ValueOf((*goxml.HeroFetterInfoManager)(nil)),
		"HeroFetterInfos":                                      reflect.ValueOf((*goxml.HeroFetterInfos)(nil)),
		"HeroInfo":                                             reflect.ValueOf((*goxml.HeroInfo)(nil)),
		"HeroInfoExt":                                          reflect.ValueOf((*goxml.HeroInfoExt)(nil)),
		"HeroInfoManager":                                      reflect.ValueOf((*goxml.HeroInfoManager)(nil)),
		"HeroInfos":                                            reflect.ValueOf((*goxml.HeroInfos)(nil)),
		"HeroInitDataInfo":                                     reflect.ValueOf((*goxml.HeroInitDataInfo)(nil)),
		"HeroInitDataInfoManager":                              reflect.ValueOf((*goxml.HeroInitDataInfoManager)(nil)),
		"HeroInitDataInfos":                                    reflect.ValueOf((*goxml.HeroInitDataInfos)(nil)),
		"HeroJobStarBaseAttr":                                  reflect.ValueOf((*goxml.HeroJobStarBaseAttr)(nil)),
		"HeroLevelInfo":                                        reflect.ValueOf((*goxml.HeroLevelInfo)(nil)),
		"HeroLevelInfoExt":                                     reflect.ValueOf((*goxml.HeroLevelInfoExt)(nil)),
		"HeroLevelInfoManager":                                 reflect.ValueOf((*goxml.HeroLevelInfoManager)(nil)),
		"HeroLevelInfos":                                       reflect.ValueOf((*goxml.HeroLevelInfos)(nil)),
		"HeroRes":                                              reflect.ValueOf((*goxml.HeroRes)(nil)),
		"HeroStageInfo":                                        reflect.ValueOf((*goxml.HeroStageInfo)(nil)),
		"HeroStageInfoExt":                                     reflect.ValueOf((*goxml.HeroStageInfoExt)(nil)),
		"HeroStageInfoManager":                                 reflect.ValueOf((*goxml.HeroStageInfoManager)(nil)),
		"HeroStageInfos":                                       reflect.ValueOf((*goxml.HeroStageInfos)(nil)),
		"HeroStarInfo":                                         reflect.ValueOf((*goxml.HeroStarInfo)(nil)),
		"HeroStarInfoExt":                                      reflect.ValueOf((*goxml.HeroStarInfoExt)(nil)),
		"HeroStarInfoManager":                                  reflect.ValueOf((*goxml.HeroStarInfoManager)(nil)),
		"HeroStarInfos":                                        reflect.ValueOf((*goxml.HeroStarInfos)(nil)),
		"HotRankInfo":                                          reflect.ValueOf((*goxml.HotRankInfo)(nil)),
		"HotRankInfoExt":                                       reflect.ValueOf((*goxml.HotRankInfoExt)(nil)),
		"HotRankInfoManager":                                   reflect.ValueOf((*goxml.HotRankInfoManager)(nil)),
		"HotRankInfos":                                         reflect.ValueOf((*goxml.HotRankInfos)(nil)),
		"HotRankScoreInfo":                                     reflect.ValueOf((*goxml.HotRankScoreInfo)(nil)),
		"HotRankScoreInfoManager":                              reflect.ValueOf((*goxml.HotRankScoreInfoManager)(nil)),
		"HotRankScoreInfos":                                    reflect.ValueOf((*goxml.HotRankScoreInfos)(nil)),
		"ItemInfo":                                             reflect.ValueOf((*goxml.ItemInfo)(nil)),
		"ItemInfoExt":                                          reflect.ValueOf((*goxml.ItemInfoExt)(nil)),
		"ItemInfoManager":                                      reflect.ValueOf((*goxml.ItemInfoManager)(nil)),
		"ItemInfos":                                            reflect.ValueOf((*goxml.ItemInfos)(nil)),
		"ItemSelectiveGroupInfo":                               reflect.ValueOf((*goxml.ItemSelectiveGroupInfo)(nil)),
		"ItemSelectiveGroupInfoManager":                        reflect.ValueOf((*goxml.ItemSelectiveGroupInfoManager)(nil)),
		"ItemSelectiveGroupInfos":                              reflect.ValueOf((*goxml.ItemSelectiveGroupInfos)(nil)),
		"ItemTokenInfo":                                        reflect.ValueOf((*goxml.ItemTokenInfo)(nil)),
		"ItemTokenInfoExt":                                     reflect.ValueOf((*goxml.ItemTokenInfoExt)(nil)),
		"ItemTokenInfoManager":                                 reflect.ValueOf((*goxml.ItemTokenInfoManager)(nil)),
		"ItemTokenInfos":                                       reflect.ValueOf((*goxml.ItemTokenInfos)(nil)),
		"LinkBookInfo":                                         reflect.ValueOf((*goxml.LinkBookInfo)(nil)),
		"LinkBookInfoExt":                                      reflect.ValueOf((*goxml.LinkBookInfoExt)(nil)),
		"LinkBookInfoManager":                                  reflect.ValueOf((*goxml.LinkBookInfoManager)(nil)),
		"LinkBookInfos":                                        reflect.ValueOf((*goxml.LinkBookInfos)(nil)),
		"LinkBookTaskInfo":                                     reflect.ValueOf((*goxml.LinkBookTaskInfo)(nil)),
		"LinkBookTaskInfoManager":                              reflect.ValueOf((*goxml.LinkBookTaskInfoManager)(nil)),
		"LinkBookTaskInfos":                                    reflect.ValueOf((*goxml.LinkBookTaskInfos)(nil)),
		"LinkInfo":                                             reflect.ValueOf((*goxml.LinkInfo)(nil)),
		"LinkInfoManager":                                      reflect.ValueOf((*goxml.LinkInfoManager)(nil)),
		"LinkInfos":                                            reflect.ValueOf((*goxml.LinkInfos)(nil)),
		"LinkSkillInfo":                                        reflect.ValueOf((*goxml.LinkSkillInfo)(nil)),
		"LinkSkillInfoExt":                                     reflect.ValueOf((*goxml.LinkSkillInfoExt)(nil)),
		"LinkSkillInfoManager":                                 reflect.ValueOf((*goxml.LinkSkillInfoManager)(nil)),
		"LinkSkillInfos":                                       reflect.ValueOf((*goxml.LinkSkillInfos)(nil)),
		"LinkSortUnit":                                         reflect.ValueOf((*goxml.LinkSortUnit)(nil)),
		"LinkSummonConfigInfo":                                 reflect.ValueOf((*goxml.LinkSummonConfigInfo)(nil)),
		"LinkSummonConfigInfoManager":                          reflect.ValueOf((*goxml.LinkSummonConfigInfoManager)(nil)),
		"LinkSummonConfigInfos":                                reflect.ValueOf((*goxml.LinkSummonConfigInfos)(nil)),
		"LinkSummonInfo":                                       reflect.ValueOf((*goxml.LinkSummonInfo)(nil)),
		"LinkSummonInfoManager":                                reflect.ValueOf((*goxml.LinkSummonInfoManager)(nil)),
		"LinkSummonInfos":                                      reflect.ValueOf((*goxml.LinkSummonInfos)(nil)),
		"LogicServiceConfig":                                   reflect.ValueOf((*goxml.LogicServiceConfig)(nil)),
		"LoginLimitInfo":                                       reflect.ValueOf((*goxml.LoginLimitInfo)(nil)),
		"LoginLimitInfoManager":                                reflect.ValueOf((*goxml.LoginLimitInfoManager)(nil)),
		"LoginLimitInfos":                                      reflect.ValueOf((*goxml.LoginLimitInfos)(nil)),
		"MailInfo":                                             reflect.ValueOf((*goxml.MailInfo)(nil)),
		"MailInfoExt":                                          reflect.ValueOf((*goxml.MailInfoExt)(nil)),
		"MailInfoManager":                                      reflect.ValueOf((*goxml.MailInfoManager)(nil)),
		"MailInfos":                                            reflect.ValueOf((*goxml.MailInfos)(nil)),
		"MapGroundExt":                                         reflect.ValueOf((*goxml.MapGroundExt)(nil)),
		"MapGroundInfo":                                        reflect.ValueOf((*goxml.MapGroundInfo)(nil)),
		"MapGroupInfoExt":                                      reflect.ValueOf((*goxml.MapGroupInfoExt)(nil)),
		"MapInfoExt":                                           reflect.ValueOf((*goxml.MapInfoExt)(nil)),
		"MasterDataInfo":                                       reflect.ValueOf((*goxml.MasterDataInfo)(nil)),
		"MasterDataInfoExt":                                    reflect.ValueOf((*goxml.MasterDataInfoExt)(nil)),
		"MasterDataInfoManager":                                reflect.ValueOf((*goxml.MasterDataInfoManager)(nil)),
		"MasterDataInfos":                                      reflect.ValueOf((*goxml.MasterDataInfos)(nil)),
		"MazeAnswerInfo":                                       reflect.ValueOf((*goxml.MazeAnswerInfo)(nil)),
		"MazeAnswerInfoExt":                                    reflect.ValueOf((*goxml.MazeAnswerInfoExt)(nil)),
		"MazeAnswerInfoManager":                                reflect.ValueOf((*goxml.MazeAnswerInfoManager)(nil)),
		"MazeAnswerInfos":                                      reflect.ValueOf((*goxml.MazeAnswerInfos)(nil)),
		"MazeBotInfo":                                          reflect.ValueOf((*goxml.MazeBotInfo)(nil)),
		"MazeBotInfoManager":                                   reflect.ValueOf((*goxml.MazeBotInfoManager)(nil)),
		"MazeBotInfos":                                         reflect.ValueOf((*goxml.MazeBotInfos)(nil)),
		"MazeBoxInfo":                                          reflect.ValueOf((*goxml.MazeBoxInfo)(nil)),
		"MazeBoxInfoManager":                                   reflect.ValueOf((*goxml.MazeBoxInfoManager)(nil)),
		"MazeBoxInfos":                                         reflect.ValueOf((*goxml.MazeBoxInfos)(nil)),
		"MazeBuffAlter":                                        reflect.ValueOf((*goxml.MazeBuffAlter)(nil)),
		"MazeBuffInfo":                                         reflect.ValueOf((*goxml.MazeBuffInfo)(nil)),
		"MazeBuffInfoExt":                                      reflect.ValueOf((*goxml.MazeBuffInfoExt)(nil)),
		"MazeBuffInfoManager":                                  reflect.ValueOf((*goxml.MazeBuffInfoManager)(nil)),
		"MazeBuffInfos":                                        reflect.ValueOf((*goxml.MazeBuffInfos)(nil)),
		"MazeBuffLink":                                         reflect.ValueOf((*goxml.MazeBuffLink)(nil)),
		"MazeBuyReviveInfo":                                    reflect.ValueOf((*goxml.MazeBuyReviveInfo)(nil)),
		"MazeBuyReviveInfoManager":                             reflect.ValueOf((*goxml.MazeBuyReviveInfoManager)(nil)),
		"MazeBuyReviveInfos":                                   reflect.ValueOf((*goxml.MazeBuyReviveInfos)(nil)),
		"MazeConstantInfo":                                     reflect.ValueOf((*goxml.MazeConstantInfo)(nil)),
		"MazeConstantInfoManager":                              reflect.ValueOf((*goxml.MazeConstantInfoManager)(nil)),
		"MazeConstantInfos":                                    reflect.ValueOf((*goxml.MazeConstantInfos)(nil)),
		"MazeCuresInfo":                                        reflect.ValueOf((*goxml.MazeCuresInfo)(nil)),
		"MazeCuresInfoManager":                                 reflect.ValueOf((*goxml.MazeCuresInfoManager)(nil)),
		"MazeCuresInfos":                                       reflect.ValueOf((*goxml.MazeCuresInfos)(nil)),
		"MazeDifficultyInfo":                                   reflect.ValueOf((*goxml.MazeDifficultyInfo)(nil)),
		"MazeDifficultyInfoExt":                                reflect.ValueOf((*goxml.MazeDifficultyInfoExt)(nil)),
		"MazeDifficultyInfoManager":                            reflect.ValueOf((*goxml.MazeDifficultyInfoManager)(nil)),
		"MazeDifficultyInfos":                                  reflect.ValueOf((*goxml.MazeDifficultyInfos)(nil)),
		"MazeGuardianRewardInfo":                               reflect.ValueOf((*goxml.MazeGuardianRewardInfo)(nil)),
		"MazeGuardianRewardInfoManager":                        reflect.ValueOf((*goxml.MazeGuardianRewardInfoManager)(nil)),
		"MazeGuardianRewardInfos":                              reflect.ValueOf((*goxml.MazeGuardianRewardInfos)(nil)),
		"MazeMapGridInfo":                                      reflect.ValueOf((*goxml.MazeMapGridInfo)(nil)),
		"MazeMapInfo":                                          reflect.ValueOf((*goxml.MazeMapInfo)(nil)),
		"MazeMapInfoExt":                                       reflect.ValueOf((*goxml.MazeMapInfoExt)(nil)),
		"MazeMapInfoManager":                                   reflect.ValueOf((*goxml.MazeMapInfoManager)(nil)),
		"MazeMapInfos":                                         reflect.ValueOf((*goxml.MazeMapInfos)(nil)),
		"MazeModelInfo":                                        reflect.ValueOf((*goxml.MazeModelInfo)(nil)),
		"MazeModelInfoManager":                                 reflect.ValueOf((*goxml.MazeModelInfoManager)(nil)),
		"MazeModelInfos":                                       reflect.ValueOf((*goxml.MazeModelInfos)(nil)),
		"MazeQuestionInfo":                                     reflect.ValueOf((*goxml.MazeQuestionInfo)(nil)),
		"MazeQuestionInfoManager":                              reflect.ValueOf((*goxml.MazeQuestionInfoManager)(nil)),
		"MazeQuestionInfos":                                    reflect.ValueOf((*goxml.MazeQuestionInfos)(nil)),
		"MazeScrollInfo":                                       reflect.ValueOf((*goxml.MazeScrollInfo)(nil)),
		"MazeScrollInfoManager":                                reflect.ValueOf((*goxml.MazeScrollInfoManager)(nil)),
		"MazeScrollInfos":                                      reflect.ValueOf((*goxml.MazeScrollInfos)(nil)),
		"MazeTaskInfo":                                         reflect.ValueOf((*goxml.MazeTaskInfo)(nil)),
		"MazeTaskInfoManager":                                  reflect.ValueOf((*goxml.MazeTaskInfoManager)(nil)),
		"MazeTaskInfos":                                        reflect.ValueOf((*goxml.MazeTaskInfos)(nil)),
		"MazeTaskLevelInfo":                                    reflect.ValueOf((*goxml.MazeTaskLevelInfo)(nil)),
		"MazeTaskLevelInfoManager":                             reflect.ValueOf((*goxml.MazeTaskLevelInfoManager)(nil)),
		"MazeTaskLevelInfos":                                   reflect.ValueOf((*goxml.MazeTaskLevelInfos)(nil)),
		"MedalInfo":                                            reflect.ValueOf((*goxml.MedalInfo)(nil)),
		"MedalInfoManager":                                     reflect.ValueOf((*goxml.MedalInfoManager)(nil)),
		"MedalInfos":                                           reflect.ValueOf((*goxml.MedalInfos)(nil)),
		"MedalSkinInfo":                                        reflect.ValueOf((*goxml.MedalSkinInfo)(nil)),
		"MedalSkinInfoManager":                                 reflect.ValueOf((*goxml.MedalSkinInfoManager)(nil)),
		"MedalSkinInfos":                                       reflect.ValueOf((*goxml.MedalSkinInfos)(nil)),
		"MedalTaskInfo":                                        reflect.ValueOf((*goxml.MedalTaskInfo)(nil)),
		"MedalTaskInfoManager":                                 reflect.ValueOf((*goxml.MedalTaskInfoManager)(nil)),
		"MedalTaskInfos":                                       reflect.ValueOf((*goxml.MedalTaskInfos)(nil)),
		"MemoryChipInfo":                                       reflect.ValueOf((*goxml.MemoryChipInfo)(nil)),
		"MemoryChipInfoManager":                                reflect.ValueOf((*goxml.MemoryChipInfoManager)(nil)),
		"MemoryChipInfos":                                      reflect.ValueOf((*goxml.MemoryChipInfos)(nil)),
		"MirageAffixGroupInfo":                                 reflect.ValueOf((*goxml.MirageAffixGroupInfo)(nil)),
		"MirageAffixGroupInfoManager":                          reflect.ValueOf((*goxml.MirageAffixGroupInfoManager)(nil)),
		"MirageAffixGroupInfos":                                reflect.ValueOf((*goxml.MirageAffixGroupInfos)(nil)),
		"MirageAffixInfo":                                      reflect.ValueOf((*goxml.MirageAffixInfo)(nil)),
		"MirageAffixInfoExt":                                   reflect.ValueOf((*goxml.MirageAffixInfoExt)(nil)),
		"MirageAffixInfoManager":                               reflect.ValueOf((*goxml.MirageAffixInfoManager)(nil)),
		"MirageAffixInfos":                                     reflect.ValueOf((*goxml.MirageAffixInfos)(nil)),
		"MirageCopyInfo":                                       reflect.ValueOf((*goxml.MirageCopyInfo)(nil)),
		"MirageCopyInfoManager":                                reflect.ValueOf((*goxml.MirageCopyInfoManager)(nil)),
		"MirageCopyInfos":                                      reflect.ValueOf((*goxml.MirageCopyInfos)(nil)),
		"MirageFightEffectInfo":                                reflect.ValueOf((*goxml.MirageFightEffectInfo)(nil)),
		"MirageFightEffectInfoExt":                             reflect.ValueOf((*goxml.MirageFightEffectInfoExt)(nil)),
		"MirageFightEffectInfoManager":                         reflect.ValueOf((*goxml.MirageFightEffectInfoManager)(nil)),
		"MirageFightEffectInfos":                               reflect.ValueOf((*goxml.MirageFightEffectInfos)(nil)),
		"MirageHurdleInfo":                                     reflect.ValueOf((*goxml.MirageHurdleInfo)(nil)),
		"MirageHurdleInfoManager":                              reflect.ValueOf((*goxml.MirageHurdleInfoManager)(nil)),
		"MirageHurdleInfos":                                    reflect.ValueOf((*goxml.MirageHurdleInfos)(nil)),
		"MirageStarRewardInfo":                                 reflect.ValueOf((*goxml.MirageStarRewardInfo)(nil)),
		"MirageStarRewardInfos":                                reflect.ValueOf((*goxml.MirageStarRewardInfos)(nil)),
		"MirageVictoryInfo":                                    reflect.ValueOf((*goxml.MirageVictoryInfo)(nil)),
		"MirageVictoryInfos":                                   reflect.ValueOf((*goxml.MirageVictoryInfos)(nil)),
		"MonsterArtifact":                                      reflect.ValueOf((*goxml.MonsterArtifact)(nil)),
		"MonsterArtifactInfo":                                  reflect.ValueOf((*goxml.MonsterArtifactInfo)(nil)),
		"MonsterArtifactInfoExt":                               reflect.ValueOf((*goxml.MonsterArtifactInfoExt)(nil)),
		"MonsterArtifactInfoManager":                           reflect.ValueOf((*goxml.MonsterArtifactInfoManager)(nil)),
		"MonsterArtifactInfos":                                 reflect.ValueOf((*goxml.MonsterArtifactInfos)(nil)),
		"MonsterDataInfo":                                      reflect.ValueOf((*goxml.MonsterDataInfo)(nil)),
		"MonsterDataInfoManager":                               reflect.ValueOf((*goxml.MonsterDataInfoManager)(nil)),
		"MonsterDataInfos":                                     reflect.ValueOf((*goxml.MonsterDataInfos)(nil)),
		"MonsterGroupInfo":                                     reflect.ValueOf((*goxml.MonsterGroupInfo)(nil)),
		"MonsterGroupInfoExt":                                  reflect.ValueOf((*goxml.MonsterGroupInfoExt)(nil)),
		"MonsterGroupInfoManager":                              reflect.ValueOf((*goxml.MonsterGroupInfoManager)(nil)),
		"MonsterGroupInfos":                                    reflect.ValueOf((*goxml.MonsterGroupInfos)(nil)),
		"MonsterInfo":                                          reflect.ValueOf((*goxml.MonsterInfo)(nil)),
		"MonsterInfo40":                                        reflect.ValueOf((*goxml.MonsterInfo40)(nil)),
		"MonsterInfo40Manager":                                 reflect.ValueOf((*goxml.MonsterInfo40Manager)(nil)),
		"MonsterInfo40s":                                       reflect.ValueOf((*goxml.MonsterInfo40s)(nil)),
		"MonsterInfo55":                                        reflect.ValueOf((*goxml.MonsterInfo55)(nil)),
		"MonsterInfo55Manager":                                 reflect.ValueOf((*goxml.MonsterInfo55Manager)(nil)),
		"MonsterInfo55s":                                       reflect.ValueOf((*goxml.MonsterInfo55s)(nil)),
		"MonsterInfo7":                                         reflect.ValueOf((*goxml.MonsterInfo7)(nil)),
		"MonsterInfo7Manager":                                  reflect.ValueOf((*goxml.MonsterInfo7Manager)(nil)),
		"MonsterInfo7s":                                        reflect.ValueOf((*goxml.MonsterInfo7s)(nil)),
		"MonsterInfoEx":                                        reflect.ValueOf((*goxml.MonsterInfoEx)(nil)),
		"MonsterInfoManager":                                   reflect.ValueOf((*goxml.MonsterInfoManager)(nil)),
		"MonsterInfos":                                         reflect.ValueOf((*goxml.MonsterInfos)(nil)),
		"MonthlycardInfo":                                      reflect.ValueOf((*goxml.MonthlycardInfo)(nil)),
		"MonthlycardInfoManager":                               reflect.ValueOf((*goxml.MonthlycardInfoManager)(nil)),
		"MonthlycardInfos":                                     reflect.ValueOf((*goxml.MonthlycardInfos)(nil)),
		"MultiRandom":                                          reflect.ValueOf((*goxml.MultiRandom)(nil)),
		"NewDropInfo":                                          reflect.ValueOf((*goxml.NewDropInfo)(nil)),
		"NewDropInfoExt":                                       reflect.ValueOf((*goxml.NewDropInfoExt)(nil)),
		"NewDropInfoManager":                                   reflect.ValueOf((*goxml.NewDropInfoManager)(nil)),
		"NewDropInfos":                                         reflect.ValueOf((*goxml.NewDropInfos)(nil)),
		"NewYearActivityInfoExt":                               reflect.ValueOf((*goxml.NewYearActivityInfoExt)(nil)),
		"NewYearInfo":                                          reflect.ValueOf((*goxml.NewYearInfo)(nil)),
		"NewYearInfoManager":                                   reflect.ValueOf((*goxml.NewYearInfoManager)(nil)),
		"NewYearInfos":                                         reflect.ValueOf((*goxml.NewYearInfos)(nil)),
		"NumberMaxAddTaskInfo":                                 reflect.ValueOf((*goxml.NumberMaxAddTaskInfo)(nil)),
		"NumberMaxAddTaskInfoManager":                          reflect.ValueOf((*goxml.NumberMaxAddTaskInfoManager)(nil)),
		"NumberMaxAddTaskInfos":                                reflect.ValueOf((*goxml.NumberMaxAddTaskInfos)(nil)),
		"NumberMaxInfo":                                        reflect.ValueOf((*goxml.NumberMaxInfo)(nil)),
		"NumberMaxInfoManager":                                 reflect.ValueOf((*goxml.NumberMaxInfoManager)(nil)),
		"NumberMaxInfos":                                       reflect.ValueOf((*goxml.NumberMaxInfos)(nil)),
		"NumberTypeInfo":                                       reflect.ValueOf((*goxml.NumberTypeInfo)(nil)),
		"NumberTypeInfoManager":                                reflect.ValueOf((*goxml.NumberTypeInfoManager)(nil)),
		"NumberTypeInfos":                                      reflect.ValueOf((*goxml.NumberTypeInfos)(nil)),
		"OldEmblemInfo":                                        reflect.ValueOf((*goxml.OldEmblemInfo)(nil)),
		"OldEmblemInfoManager":                                 reflect.ValueOf((*goxml.OldEmblemInfoManager)(nil)),
		"OldEmblemInfos":                                       reflect.ValueOf((*goxml.OldEmblemInfos)(nil)),
		"OneSeasonAddInfo":                                     reflect.ValueOf((*goxml.OneSeasonAddInfo)(nil)),
		"OpenType":                                             reflect.ValueOf((*goxml.OpenType)(nil)),
		"PSEffectData":                                         reflect.ValueOf((*goxml.PSEffectData)(nil)),
		"PassCycleTaskInfo":                                    reflect.ValueOf((*goxml.PassCycleTaskInfo)(nil)),
		"PassCycleTaskInfoManager":                             reflect.ValueOf((*goxml.PassCycleTaskInfoManager)(nil)),
		"PassCycleTaskInfos":                                   reflect.ValueOf((*goxml.PassCycleTaskInfos)(nil)),
		"PassInfo":                                             reflect.ValueOf((*goxml.PassInfo)(nil)),
		"PassInfoExt":                                          reflect.ValueOf((*goxml.PassInfoExt)(nil)),
		"PassInfoManager":                                      reflect.ValueOf((*goxml.PassInfoManager)(nil)),
		"PassInfos":                                            reflect.ValueOf((*goxml.PassInfos)(nil)),
		"PassTaskInfo":                                         reflect.ValueOf((*goxml.PassTaskInfo)(nil)),
		"PassTaskInfoManager":                                  reflect.ValueOf((*goxml.PassTaskInfoManager)(nil)),
		"PassTaskInfos":                                        reflect.ValueOf((*goxml.PassTaskInfos)(nil)),
		"PassiveSkillEffectInfo":                               reflect.ValueOf((*goxml.PassiveSkillEffectInfo)(nil)),
		"PassiveSkillEffectInfoExt":                            reflect.ValueOf((*goxml.PassiveSkillEffectInfoExt)(nil)),
		"PassiveSkillEffectInfoManager":                        reflect.ValueOf((*goxml.PassiveSkillEffectInfoManager)(nil)),
		"PassiveSkillEffectInfos":                              reflect.ValueOf((*goxml.PassiveSkillEffectInfos)(nil)),
		"PassiveSkillInfo":                                     reflect.ValueOf((*goxml.PassiveSkillInfo)(nil)),
		"PassiveSkillInfoExt":                                  reflect.ValueOf((*goxml.PassiveSkillInfoExt)(nil)),
		"PassiveSkillInfoManager":                              reflect.ValueOf((*goxml.PassiveSkillInfoManager)(nil)),
		"PassiveSkillInfos":                                    reflect.ValueOf((*goxml.PassiveSkillInfos)(nil)),
		"PassiveSkillProbabilityInfo":                          reflect.ValueOf((*goxml.PassiveSkillProbabilityInfo)(nil)),
		"PassiveSkillProbabilityInfoManager":                   reflect.ValueOf((*goxml.PassiveSkillProbabilityInfoManager)(nil)),
		"PassiveSkillProbabilityInfos":                         reflect.ValueOf((*goxml.PassiveSkillProbabilityInfos)(nil)),
		"PassiveSkillTriggerInfo":                              reflect.ValueOf((*goxml.PassiveSkillTriggerInfo)(nil)),
		"PassiveSkillTriggerInfoManager":                       reflect.ValueOf((*goxml.PassiveSkillTriggerInfoManager)(nil)),
		"PassiveSkillTriggerInfos":                             reflect.ValueOf((*goxml.PassiveSkillTriggerInfos)(nil)),
		"PeakConfigInfo":                                       reflect.ValueOf((*goxml.PeakConfigInfo)(nil)),
		"PeakConfigInfoManager":                                reflect.ValueOf((*goxml.PeakConfigInfoManager)(nil)),
		"PeakConfigInfos":                                      reflect.ValueOf((*goxml.PeakConfigInfos)(nil)),
		"PeakInfo":                                             reflect.ValueOf((*goxml.PeakInfo)(nil)),
		"PeakInfoExt":                                          reflect.ValueOf((*goxml.PeakInfoExt)(nil)),
		"PeakInfoManager":                                      reflect.ValueOf((*goxml.PeakInfoManager)(nil)),
		"PeakInfos":                                            reflect.ValueOf((*goxml.PeakInfos)(nil)),
		"PeakPhase":                                            reflect.ValueOf((*goxml.PeakPhase)(nil)),
		"PeakRankRewardInfo":                                   reflect.ValueOf((*goxml.PeakRankRewardInfo)(nil)),
		"PeakRankRewardInfoManager":                            reflect.ValueOf((*goxml.PeakRankRewardInfoManager)(nil)),
		"PeakRankRewardInfos":                                  reflect.ValueOf((*goxml.PeakRankRewardInfos)(nil)),
		"PlayerLevelInfo":                                      reflect.ValueOf((*goxml.PlayerLevelInfo)(nil)),
		"PlayerLevelInfoExt":                                   reflect.ValueOf((*goxml.PlayerLevelInfoExt)(nil)),
		"PlayerLevelInfoManager":                               reflect.ValueOf((*goxml.PlayerLevelInfoManager)(nil)),
		"PlayerLevelInfos":                                     reflect.ValueOf((*goxml.PlayerLevelInfos)(nil)),
		"Position":                                             reflect.ValueOf((*goxml.Position)(nil)),
		"PreciousResInfo":                                      reflect.ValueOf((*goxml.PreciousResInfo)(nil)),
		"PreciousResInfoManager":                               reflect.ValueOf((*goxml.PreciousResInfoManager)(nil)),
		"PreciousResInfos":                                     reflect.ValueOf((*goxml.PreciousResInfos)(nil)),
		"PublicHpMembers":                                      reflect.ValueOf((*goxml.PublicHpMembers)(nil)),
		"PuzzleModel":                                          reflect.ValueOf((*goxml.PuzzleModel)(nil)),
		"RaceBuffInfo":                                         reflect.ValueOf((*goxml.RaceBuffInfo)(nil)),
		"RaceBuffInfoManager":                                  reflect.ValueOf((*goxml.RaceBuffInfoManager)(nil)),
		"RaceBuffInfos":                                        reflect.ValueOf((*goxml.RaceBuffInfos)(nil)),
		"RaisePassiveSkillInfo":                                reflect.ValueOf((*goxml.RaisePassiveSkillInfo)(nil)),
		"RaisePassiveSkillInfoExt":                             reflect.ValueOf((*goxml.RaisePassiveSkillInfoExt)(nil)),
		"RaisePassiveSkillInfoManager":                         reflect.ValueOf((*goxml.RaisePassiveSkillInfoManager)(nil)),
		"RaisePassiveSkillInfos":                               reflect.ValueOf((*goxml.RaisePassiveSkillInfos)(nil)),
		"RankAchieveGoalInfo":                                  reflect.ValueOf((*goxml.RankAchieveGoalInfo)(nil)),
		"RankingAchievementInfo":                               reflect.ValueOf((*goxml.RankingAchievementInfo)(nil)),
		"RankingAchievementInfoExt":                            reflect.ValueOf((*goxml.RankingAchievementInfoExt)(nil)),
		"RankingAchievementInfoManager":                        reflect.ValueOf((*goxml.RankingAchievementInfoManager)(nil)),
		"RankingAchievementInfos":                              reflect.ValueOf((*goxml.RankingAchievementInfos)(nil)),
		"RankingInfo":                                          reflect.ValueOf((*goxml.RankingInfo)(nil)),
		"RankingInfoManager":                                   reflect.ValueOf((*goxml.RankingInfoManager)(nil)),
		"RankingInfos":                                         reflect.ValueOf((*goxml.RankingInfos)(nil)),
		"RechargeCouponInfo":                                   reflect.ValueOf((*goxml.RechargeCouponInfo)(nil)),
		"RechargeCouponInfoManager":                            reflect.ValueOf((*goxml.RechargeCouponInfoManager)(nil)),
		"RechargeCouponInfos":                                  reflect.ValueOf((*goxml.RechargeCouponInfos)(nil)),
		"RechargeGoodsInfo":                                    reflect.ValueOf((*goxml.RechargeGoodsInfo)(nil)),
		"RechargeGoodsInfoManager":                             reflect.ValueOf((*goxml.RechargeGoodsInfoManager)(nil)),
		"RechargeGoodsInfos":                                   reflect.ValueOf((*goxml.RechargeGoodsInfos)(nil)),
		"RechargeProductInfo":                                  reflect.ValueOf((*goxml.RechargeProductInfo)(nil)),
		"RechargeProductInfoManager":                           reflect.ValueOf((*goxml.RechargeProductInfoManager)(nil)),
		"RechargeProductInfos":                                 reflect.ValueOf((*goxml.RechargeProductInfos)(nil)),
		"RechargeRefundInfo":                                   reflect.ValueOf((*goxml.RechargeRefundInfo)(nil)),
		"RechargeRefundInfoManager":                            reflect.ValueOf((*goxml.RechargeRefundInfoManager)(nil)),
		"RechargeRefundInfos":                                  reflect.ValueOf((*goxml.RechargeRefundInfos)(nil)),
		"RecvTask":                                             reflect.ValueOf((*goxml.RecvTask)(nil)),
		"RemainBlessInfo":                                      reflect.ValueOf((*goxml.RemainBlessInfo)(nil)),
		"RemainBlessInfoExt":                                   reflect.ValueOf((*goxml.RemainBlessInfoExt)(nil)),
		"RemainBlessInfoManager":                               reflect.ValueOf((*goxml.RemainBlessInfoManager)(nil)),
		"RemainBlessInfos":                                     reflect.ValueOf((*goxml.RemainBlessInfos)(nil)),
		"RemainConfigInfo":                                     reflect.ValueOf((*goxml.RemainConfigInfo)(nil)),
		"RemainConfigInfoManager":                              reflect.ValueOf((*goxml.RemainConfigInfoManager)(nil)),
		"RemainConfigInfos":                                    reflect.ValueOf((*goxml.RemainConfigInfos)(nil)),
		"RemainFragmentInfo":                                   reflect.ValueOf((*goxml.RemainFragmentInfo)(nil)),
		"RemainFragmentInfoManager":                            reflect.ValueOf((*goxml.RemainFragmentInfoManager)(nil)),
		"RemainFragmentInfos":                                  reflect.ValueOf((*goxml.RemainFragmentInfos)(nil)),
		"RemainInfo":                                           reflect.ValueOf((*goxml.RemainInfo)(nil)),
		"RemainInfoManager":                                    reflect.ValueOf((*goxml.RemainInfoManager)(nil)),
		"RemainInfos":                                          reflect.ValueOf((*goxml.RemainInfos)(nil)),
		"RemainRecycleRewardInfo":                              reflect.ValueOf((*goxml.RemainRecycleRewardInfo)(nil)),
		"RemainRecycleRewardInfoManager":                       reflect.ValueOf((*goxml.RemainRecycleRewardInfoManager)(nil)),
		"RemainRecycleRewardInfos":                             reflect.ValueOf((*goxml.RemainRecycleRewardInfos)(nil)),
		"RemainStarInfo":                                       reflect.ValueOf((*goxml.RemainStarInfo)(nil)),
		"RemainStarInfoManager":                                reflect.ValueOf((*goxml.RemainStarInfoManager)(nil)),
		"RemainStarInfos":                                      reflect.ValueOf((*goxml.RemainStarInfos)(nil)),
		"ResourceTypeInfo":                                     reflect.ValueOf((*goxml.ResourceTypeInfo)(nil)),
		"ResourceTypeInfoManager":                              reflect.ValueOf((*goxml.ResourceTypeInfoManager)(nil)),
		"ResourceTypeInfos":                                    reflect.ValueOf((*goxml.ResourceTypeInfos)(nil)),
		"RiteInfo":                                             reflect.ValueOf((*goxml.RiteInfo)(nil)),
		"RiteInfoManager":                                      reflect.ValueOf((*goxml.RiteInfoManager)(nil)),
		"RiteInfos":                                            reflect.ValueOf((*goxml.RiteInfos)(nil)),
		"RiteMarkInfo":                                         reflect.ValueOf((*goxml.RiteMarkInfo)(nil)),
		"RiteMarkInfoManager":                                  reflect.ValueOf((*goxml.RiteMarkInfoManager)(nil)),
		"RiteMarkInfos":                                        reflect.ValueOf((*goxml.RiteMarkInfos)(nil)),
		"RiteMonsterGroupInfo":                                 reflect.ValueOf((*goxml.RiteMonsterGroupInfo)(nil)),
		"RiteMonsterGroupInfoManager":                          reflect.ValueOf((*goxml.RiteMonsterGroupInfoManager)(nil)),
		"RiteMonsterGroupInfos":                                reflect.ValueOf((*goxml.RiteMonsterGroupInfos)(nil)),
		"RitePowerInfo":                                        reflect.ValueOf((*goxml.RitePowerInfo)(nil)),
		"RitePowerInfoManager":                                 reflect.ValueOf((*goxml.RitePowerInfoManager)(nil)),
		"RitePowerInfos":                                       reflect.ValueOf((*goxml.RitePowerInfos)(nil)),
		"RiteRareInfo":                                         reflect.ValueOf((*goxml.RiteRareInfo)(nil)),
		"RiteRareInfoManager":                                  reflect.ValueOf((*goxml.RiteRareInfoManager)(nil)),
		"RiteRareInfos":                                        reflect.ValueOf((*goxml.RiteRareInfos)(nil)),
		"RiteRecycleRewardInfo":                                reflect.ValueOf((*goxml.RiteRecycleRewardInfo)(nil)),
		"RiteRecycleRewardInfoM":                               reflect.ValueOf((*goxml.RiteRecycleRewardInfoM)(nil)),
		"RiteRecycleRewardInfoMSeasonIdIndex":                  reflect.ValueOf((*goxml.RiteRecycleRewardInfoMSeasonIdIndex)(nil)),
		"RiteRecycleRewardInfoXml":                             reflect.ValueOf((*goxml.RiteRecycleRewardInfoXml)(nil)),
		"RiteRecycleRewardInfoXmls":                            reflect.ValueOf((*goxml.RiteRecycleRewardInfoXmls)(nil)),
		"RiteTypeInfo":                                         reflect.ValueOf((*goxml.RiteTypeInfo)(nil)),
		"RiteTypeInfoManager":                                  reflect.ValueOf((*goxml.RiteTypeInfoManager)(nil)),
		"RiteTypeInfos":                                        reflect.ValueOf((*goxml.RiteTypeInfos)(nil)),
		"RoundActivityOpenInfo":                                reflect.ValueOf((*goxml.RoundActivityOpenInfo)(nil)),
		"RoundActivityOpenInfoManager":                         reflect.ValueOf((*goxml.RoundActivityOpenInfoManager)(nil)),
		"RoundActivityOpenInfos":                               reflect.ValueOf((*goxml.RoundActivityOpenInfos)(nil)),
		"RoundActivitySumInfo":                                 reflect.ValueOf((*goxml.RoundActivitySumInfo)(nil)),
		"RoundActivitySumInfoManager":                          reflect.ValueOf((*goxml.RoundActivitySumInfoManager)(nil)),
		"RoundActivitySumInfos":                                reflect.ValueOf((*goxml.RoundActivitySumInfos)(nil)),
		"RoundActivityTaskInfo":                                reflect.ValueOf((*goxml.RoundActivityTaskInfo)(nil)),
		"RoundActivityTaskInfoManager":                         reflect.ValueOf((*goxml.RoundActivityTaskInfoManager)(nil)),
		"RoundActivityTaskInfos":                               reflect.ValueOf((*goxml.RoundActivityTaskInfos)(nil)),
		"SameRareEmblemInfos":                                  reflect.ValueOf((*goxml.SameRareEmblemInfos)(nil)),
		"SampleInfo":                                           reflect.ValueOf((*goxml.SampleInfo)(nil)),
		"SampleInfoM":                                          reflect.ValueOf((*goxml.SampleInfoM)(nil)),
		"SampleInfoMSeasonIdIndex":                             reflect.ValueOf((*goxml.SampleInfoMSeasonIdIndex)(nil)),
		"SampleInfoMSeasonIdRiteIdIndex":                       reflect.ValueOf((*goxml.SampleInfoMSeasonIdRiteIdIndex)(nil)),
		"SampleInfoXml":                                        reflect.ValueOf((*goxml.SampleInfoXml)(nil)),
		"SampleInfoXmls":                                       reflect.ValueOf((*goxml.SampleInfoXmls)(nil)),
		"SeasonAddGroupInfo":                                   reflect.ValueOf((*goxml.SeasonAddGroupInfo)(nil)),
		"SeasonAddGroupInfoManager":                            reflect.ValueOf((*goxml.SeasonAddGroupInfoManager)(nil)),
		"SeasonAddGroupInfos":                                  reflect.ValueOf((*goxml.SeasonAddGroupInfos)(nil)),
		"SeasonAddInfo":                                        reflect.ValueOf((*goxml.SeasonAddInfo)(nil)),
		"SeasonAddInfoManager":                                 reflect.ValueOf((*goxml.SeasonAddInfoManager)(nil)),
		"SeasonAddInfos":                                       reflect.ValueOf((*goxml.SeasonAddInfos)(nil)),
		"SeasonAddOneTypeInfo":                                 reflect.ValueOf((*goxml.SeasonAddOneTypeInfo)(nil)),
		"SeasonArenOpenInfo":                                   reflect.ValueOf((*goxml.SeasonArenOpenInfo)(nil)),
		"SeasonArenaBotInfo":                                   reflect.ValueOf((*goxml.SeasonArenaBotInfo)(nil)),
		"SeasonArenaBotInfoExt":                                reflect.ValueOf((*goxml.SeasonArenaBotInfoExt)(nil)),
		"SeasonArenaBotInfoManager":                            reflect.ValueOf((*goxml.SeasonArenaBotInfoManager)(nil)),
		"SeasonArenaBotInfos":                                  reflect.ValueOf((*goxml.SeasonArenaBotInfos)(nil)),
		"SeasonArenaConfigInfo":                                reflect.ValueOf((*goxml.SeasonArenaConfigInfo)(nil)),
		"SeasonArenaConfigInfoManager":                         reflect.ValueOf((*goxml.SeasonArenaConfigInfoManager)(nil)),
		"SeasonArenaConfigInfos":                               reflect.ValueOf((*goxml.SeasonArenaConfigInfos)(nil)),
		"SeasonArenaDivisionInfo":                              reflect.ValueOf((*goxml.SeasonArenaDivisionInfo)(nil)),
		"SeasonArenaDivisionInfoManager":                       reflect.ValueOf((*goxml.SeasonArenaDivisionInfoManager)(nil)),
		"SeasonArenaDivisionInfos":                             reflect.ValueOf((*goxml.SeasonArenaDivisionInfos)(nil)),
		"SeasonArenaDivisionRewardInfo":                        reflect.ValueOf((*goxml.SeasonArenaDivisionRewardInfo)(nil)),
		"SeasonArenaDivisionRewardInfoManager":                 reflect.ValueOf((*goxml.SeasonArenaDivisionRewardInfoManager)(nil)),
		"SeasonArenaDivisionRewardInfos":                       reflect.ValueOf((*goxml.SeasonArenaDivisionRewardInfos)(nil)),
		"SeasonArenaInfo":                                      reflect.ValueOf((*goxml.SeasonArenaInfo)(nil)),
		"SeasonArenaInfoExt":                                   reflect.ValueOf((*goxml.SeasonArenaInfoExt)(nil)),
		"SeasonArenaInfoManager":                               reflect.ValueOf((*goxml.SeasonArenaInfoManager)(nil)),
		"SeasonArenaInfos":                                     reflect.ValueOf((*goxml.SeasonArenaInfos)(nil)),
		"SeasonArenaMatchInfo":                                 reflect.ValueOf((*goxml.SeasonArenaMatchInfo)(nil)),
		"SeasonArenaMatchInfoManager":                          reflect.ValueOf((*goxml.SeasonArenaMatchInfoManager)(nil)),
		"SeasonArenaMatchInfos":                                reflect.ValueOf((*goxml.SeasonArenaMatchInfos)(nil)),
		"SeasonArenaRankRewardInfo":                            reflect.ValueOf((*goxml.SeasonArenaRankRewardInfo)(nil)),
		"SeasonArenaRankRewardInfoManager":                     reflect.ValueOf((*goxml.SeasonArenaRankRewardInfoManager)(nil)),
		"SeasonArenaRankRewardInfos":                           reflect.ValueOf((*goxml.SeasonArenaRankRewardInfos)(nil)),
		"SeasonArenaTaskInfo":                                  reflect.ValueOf((*goxml.SeasonArenaTaskInfo)(nil)),
		"SeasonArenaTaskInfoManager":                           reflect.ValueOf((*goxml.SeasonArenaTaskInfoManager)(nil)),
		"SeasonArenaTaskInfos":                                 reflect.ValueOf((*goxml.SeasonArenaTaskInfos)(nil)),
		"SeasonComplianceEmblemInfo":                           reflect.ValueOf((*goxml.SeasonComplianceEmblemInfo)(nil)),
		"SeasonComplianceEmblemInfoManager":                    reflect.ValueOf((*goxml.SeasonComplianceEmblemInfoManager)(nil)),
		"SeasonComplianceEmblemInfos":                          reflect.ValueOf((*goxml.SeasonComplianceEmblemInfos)(nil)),
		"SeasonComplianceInfo":                                 reflect.ValueOf((*goxml.SeasonComplianceInfo)(nil)),
		"SeasonComplianceInfoExt":                              reflect.ValueOf((*goxml.SeasonComplianceInfoExt)(nil)),
		"SeasonComplianceInfoManager":                          reflect.ValueOf((*goxml.SeasonComplianceInfoManager)(nil)),
		"SeasonComplianceInfos":                                reflect.ValueOf((*goxml.SeasonComplianceInfos)(nil)),
		"SeasonComplianceRankMailAndScoreMail":                 reflect.ValueOf((*goxml.SeasonComplianceRankMailAndScoreMail)(nil)),
		"SeasonComplianceRankRewardInfo":                       reflect.ValueOf((*goxml.SeasonComplianceRankRewardInfo)(nil)),
		"SeasonComplianceRankRewardInfoManager":                reflect.ValueOf((*goxml.SeasonComplianceRankRewardInfoManager)(nil)),
		"SeasonComplianceRankRewardInfos":                      reflect.ValueOf((*goxml.SeasonComplianceRankRewardInfos)(nil)),
		"SeasonComplianceStageInfo":                            reflect.ValueOf((*goxml.SeasonComplianceStageInfo)(nil)),
		"SeasonComplianceStageInfoManager":                     reflect.ValueOf((*goxml.SeasonComplianceStageInfoManager)(nil)),
		"SeasonComplianceStageInfos":                           reflect.ValueOf((*goxml.SeasonComplianceStageInfos)(nil)),
		"SeasonComplianceStageRewardInfo":                      reflect.ValueOf((*goxml.SeasonComplianceStageRewardInfo)(nil)),
		"SeasonComplianceStageRewardInfoManager":               reflect.ValueOf((*goxml.SeasonComplianceStageRewardInfoManager)(nil)),
		"SeasonComplianceStageRewardInfos":                     reflect.ValueOf((*goxml.SeasonComplianceStageRewardInfos)(nil)),
		"SeasonCountdownInfo":                                  reflect.ValueOf((*goxml.SeasonCountdownInfo)(nil)),
		"SeasonCountdownInfoManager":                           reflect.ValueOf((*goxml.SeasonCountdownInfoManager)(nil)),
		"SeasonCountdownInfos":                                 reflect.ValueOf((*goxml.SeasonCountdownInfos)(nil)),
		"SeasonDataRecordInfo":                                 reflect.ValueOf((*goxml.SeasonDataRecordInfo)(nil)),
		"SeasonDataRecordInfoManager":                          reflect.ValueOf((*goxml.SeasonDataRecordInfoManager)(nil)),
		"SeasonDataRecordInfos":                                reflect.ValueOf((*goxml.SeasonDataRecordInfos)(nil)),
		"SeasonDoorBossWeakInfo":                               reflect.ValueOf((*goxml.SeasonDoorBossWeakInfo)(nil)),
		"SeasonDoorBossWeakInfoM":                              reflect.ValueOf((*goxml.SeasonDoorBossWeakInfoM)(nil)),
		"SeasonDoorBossWeakInfoMIdIndex":                       reflect.ValueOf((*goxml.SeasonDoorBossWeakInfoMIdIndex)(nil)),
		"SeasonDoorBossWeakInfoXml":                            reflect.ValueOf((*goxml.SeasonDoorBossWeakInfoXml)(nil)),
		"SeasonDoorBossWeakInfoXmls":                           reflect.ValueOf((*goxml.SeasonDoorBossWeakInfoXmls)(nil)),
		"SeasonDoorBuffInfo":                                   reflect.ValueOf((*goxml.SeasonDoorBuffInfo)(nil)),
		"SeasonDoorBuffInfoExt":                                reflect.ValueOf((*goxml.SeasonDoorBuffInfoExt)(nil)),
		"SeasonDoorBuffInfoLimit":                              reflect.ValueOf((*goxml.SeasonDoorBuffInfoLimit)(nil)),
		"SeasonDoorBuffInfoManager":                            reflect.ValueOf((*goxml.SeasonDoorBuffInfoManager)(nil)),
		"SeasonDoorBuffInfos":                                  reflect.ValueOf((*goxml.SeasonDoorBuffInfos)(nil)),
		"SeasonDoorConfigInfo":                                 reflect.ValueOf((*goxml.SeasonDoorConfigInfo)(nil)),
		"SeasonDoorConfigInfoManager":                          reflect.ValueOf((*goxml.SeasonDoorConfigInfoManager)(nil)),
		"SeasonDoorConfigInfos":                                reflect.ValueOf((*goxml.SeasonDoorConfigInfos)(nil)),
		"SeasonDoorInfo":                                       reflect.ValueOf((*goxml.SeasonDoorInfo)(nil)),
		"SeasonDoorInfoExt":                                    reflect.ValueOf((*goxml.SeasonDoorInfoExt)(nil)),
		"SeasonDoorInfoManager":                                reflect.ValueOf((*goxml.SeasonDoorInfoManager)(nil)),
		"SeasonDoorInfos":                                      reflect.ValueOf((*goxml.SeasonDoorInfos)(nil)),
		"SeasonDoorRestInfo":                                   reflect.ValueOf((*goxml.SeasonDoorRestInfo)(nil)),
		"SeasonDoorRestInfoManager":                            reflect.ValueOf((*goxml.SeasonDoorRestInfoManager)(nil)),
		"SeasonDoorRestInfos":                                  reflect.ValueOf((*goxml.SeasonDoorRestInfos)(nil)),
		"SeasonDoorTaskInfo":                                   reflect.ValueOf((*goxml.SeasonDoorTaskInfo)(nil)),
		"SeasonDoorTaskInfoManager":                            reflect.ValueOf((*goxml.SeasonDoorTaskInfoManager)(nil)),
		"SeasonDoorTaskInfos":                                  reflect.ValueOf((*goxml.SeasonDoorTaskInfos)(nil)),
		"SeasonDungeonInfo":                                    reflect.ValueOf((*goxml.SeasonDungeonInfo)(nil)),
		"SeasonDungeonInfoExt":                                 reflect.ValueOf((*goxml.SeasonDungeonInfoExt)(nil)),
		"SeasonDungeonInfoManager":                             reflect.ValueOf((*goxml.SeasonDungeonInfoManager)(nil)),
		"SeasonDungeonInfos":                                   reflect.ValueOf((*goxml.SeasonDungeonInfos)(nil)),
		"SeasonDungeonLayerInfo":                               reflect.ValueOf((*goxml.SeasonDungeonLayerInfo)(nil)),
		"SeasonDungeonLayerInfoManager":                        reflect.ValueOf((*goxml.SeasonDungeonLayerInfoManager)(nil)),
		"SeasonDungeonLayerInfos":                              reflect.ValueOf((*goxml.SeasonDungeonLayerInfos)(nil)),
		"SeasonDungeonRewardInfo":                              reflect.ValueOf((*goxml.SeasonDungeonRewardInfo)(nil)),
		"SeasonDungeonRewardInfoManager":                       reflect.ValueOf((*goxml.SeasonDungeonRewardInfoManager)(nil)),
		"SeasonDungeonRewardInfos":                             reflect.ValueOf((*goxml.SeasonDungeonRewardInfos)(nil)),
		"SeasonFlashBackConfigInfo":                            reflect.ValueOf((*goxml.SeasonFlashBackConfigInfo)(nil)),
		"SeasonFlashBackConfigInfoManager":                     reflect.ValueOf((*goxml.SeasonFlashBackConfigInfoManager)(nil)),
		"SeasonFlashBackConfigInfos":                           reflect.ValueOf((*goxml.SeasonFlashBackConfigInfos)(nil)),
		"SeasonFlashBackDataInfo":                              reflect.ValueOf((*goxml.SeasonFlashBackDataInfo)(nil)),
		"SeasonFlashBackDataInfoManager":                       reflect.ValueOf((*goxml.SeasonFlashBackDataInfoManager)(nil)),
		"SeasonFlashBackDataInfos":                             reflect.ValueOf((*goxml.SeasonFlashBackDataInfos)(nil)),
		"SeasonFlashBackParamsInfo":                            reflect.ValueOf((*goxml.SeasonFlashBackParamsInfo)(nil)),
		"SeasonFlashBackParamsInfoManager":                     reflect.ValueOf((*goxml.SeasonFlashBackParamsInfoManager)(nil)),
		"SeasonFlashBackParamsInfos":                           reflect.ValueOf((*goxml.SeasonFlashBackParamsInfos)(nil)),
		"SeasonHeroAddInfo":                                    reflect.ValueOf((*goxml.SeasonHeroAddInfo)(nil)),
		"SeasonHeroAddInfoManager":                             reflect.ValueOf((*goxml.SeasonHeroAddInfoManager)(nil)),
		"SeasonHeroAddInfos":                                   reflect.ValueOf((*goxml.SeasonHeroAddInfos)(nil)),
		"SeasonInfo":                                           reflect.ValueOf((*goxml.SeasonInfo)(nil)),
		"SeasonInfoExt":                                        reflect.ValueOf((*goxml.SeasonInfoExt)(nil)),
		"SeasonInfoManager":                                    reflect.ValueOf((*goxml.SeasonInfoManager)(nil)),
		"SeasonInfos":                                          reflect.ValueOf((*goxml.SeasonInfos)(nil)),
		"SeasonJewelryClassUpInfo":                             reflect.ValueOf((*goxml.SeasonJewelryClassUpInfo)(nil)),
		"SeasonJewelryClassUpInfoM":                            reflect.ValueOf((*goxml.SeasonJewelryClassUpInfoM)(nil)),
		"SeasonJewelryClassUpInfoXml":                          reflect.ValueOf((*goxml.SeasonJewelryClassUpInfoXml)(nil)),
		"SeasonJewelryClassUpInfoXmls":                         reflect.ValueOf((*goxml.SeasonJewelryClassUpInfoXmls)(nil)),
		"SeasonJewelryConfigInfo":                              reflect.ValueOf((*goxml.SeasonJewelryConfigInfo)(nil)),
		"SeasonJewelryConfigInfoM":                             reflect.ValueOf((*goxml.SeasonJewelryConfigInfoM)(nil)),
		"SeasonJewelryConfigInfoMEx":                           reflect.ValueOf((*goxml.SeasonJewelryConfigInfoMEx)(nil)),
		"SeasonJewelryConfigInfoXml":                           reflect.ValueOf((*goxml.SeasonJewelryConfigInfoXml)(nil)),
		"SeasonJewelryConfigInfoXmls":                          reflect.ValueOf((*goxml.SeasonJewelryConfigInfoXmls)(nil)),
		"SeasonJewelryInfo":                                    reflect.ValueOf((*goxml.SeasonJewelryInfo)(nil)),
		"SeasonJewelryInfoM":                                   reflect.ValueOf((*goxml.SeasonJewelryInfoM)(nil)),
		"SeasonJewelryInfoMEx":                                 reflect.ValueOf((*goxml.SeasonJewelryInfoMEx)(nil)),
		"SeasonJewelryInfoXml":                                 reflect.ValueOf((*goxml.SeasonJewelryInfoXml)(nil)),
		"SeasonJewelryInfoXmls":                                reflect.ValueOf((*goxml.SeasonJewelryInfoXmls)(nil)),
		"SeasonJewelryRecycleInfo":                             reflect.ValueOf((*goxml.SeasonJewelryRecycleInfo)(nil)),
		"SeasonJewelryRecycleInfoM":                            reflect.ValueOf((*goxml.SeasonJewelryRecycleInfoM)(nil)),
		"SeasonJewelryRecycleInfoMSeasonIdIndex":               reflect.ValueOf((*goxml.SeasonJewelryRecycleInfoMSeasonIdIndex)(nil)),
		"SeasonJewelryRecycleInfoXml":                          reflect.ValueOf((*goxml.SeasonJewelryRecycleInfoXml)(nil)),
		"SeasonJewelryRecycleInfoXmls":                         reflect.ValueOf((*goxml.SeasonJewelryRecycleInfoXmls)(nil)),
		"SeasonJewelrySkillChangeInfo":                         reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfo)(nil)),
		"SeasonJewelrySkillChangeInfoM":                        reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoM)(nil)),
		"SeasonJewelrySkillChangeInfoMEx":                      reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoMEx)(nil)),
		"SeasonJewelrySkillChangeInfoMJewelryPosIndex":         reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoMJewelryPosIndex)(nil)),
		"SeasonJewelrySkillChangeInfoMJewelryPosSkillScortClassIndex": reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoMJewelryPosSkillScortClassIndex)(nil)),
		"SeasonJewelrySkillChangeInfoMJewelryPosSkillScortIndex":      reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoMJewelryPosSkillScortIndex)(nil)),
		"SeasonJewelrySkillChangeInfoXml":                             reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoXml)(nil)),
		"SeasonJewelrySkillChangeInfoXmls":                            reflect.ValueOf((*goxml.SeasonJewelrySkillChangeInfoXmls)(nil)),
		"SeasonJewelrySkillChangePool":                                reflect.ValueOf((*goxml.SeasonJewelrySkillChangePool)(nil)),
		"SeasonJewelrySkillInfo":                                      reflect.ValueOf((*goxml.SeasonJewelrySkillInfo)(nil)),
		"SeasonJewelrySkillInfoM":                                     reflect.ValueOf((*goxml.SeasonJewelrySkillInfoM)(nil)),
		"SeasonJewelrySkillInfoMSkillTypeIndex":                       reflect.ValueOf((*goxml.SeasonJewelrySkillInfoMSkillTypeIndex)(nil)),
		"SeasonJewelrySkillInfoXml":                                   reflect.ValueOf((*goxml.SeasonJewelrySkillInfoXml)(nil)),
		"SeasonJewelrySkillInfoXmls":                                  reflect.ValueOf((*goxml.SeasonJewelrySkillInfoXmls)(nil)),
		"SeasonJewelrySkillLvInfo":                                    reflect.ValueOf((*goxml.SeasonJewelrySkillLvInfo)(nil)),
		"SeasonJewelrySkillLvInfoM":                                   reflect.ValueOf((*goxml.SeasonJewelrySkillLvInfoM)(nil)),
		"SeasonJewelrySkillLvInfoMSkillTypeIndex":                     reflect.ValueOf((*goxml.SeasonJewelrySkillLvInfoMSkillTypeIndex)(nil)),
		"SeasonJewelrySkillLvInfoXml":                                 reflect.ValueOf((*goxml.SeasonJewelrySkillLvInfoXml)(nil)),
		"SeasonJewelrySkillLvInfoXmls":                                reflect.ValueOf((*goxml.SeasonJewelrySkillLvInfoXmls)(nil)),
		"SeasonJewelrySkillRandomInfo":                                reflect.ValueOf((*goxml.SeasonJewelrySkillRandomInfo)(nil)),
		"SeasonJewelrySkillRandomInfoM":                               reflect.ValueOf((*goxml.SeasonJewelrySkillRandomInfoM)(nil)),
		"SeasonJewelrySkillRandomInfoMEx":                             reflect.ValueOf((*goxml.SeasonJewelrySkillRandomInfoMEx)(nil)),
		"SeasonJewelrySkillRandomInfoMSkillGroupIndex":                reflect.ValueOf((*goxml.SeasonJewelrySkillRandomInfoMSkillGroupIndex)(nil)),
		"SeasonJewelrySkillRandomInfoXml":                             reflect.ValueOf((*goxml.SeasonJewelrySkillRandomInfoXml)(nil)),
		"SeasonJewelrySkillRandomInfoXmls":                            reflect.ValueOf((*goxml.SeasonJewelrySkillRandomInfoXmls)(nil)),
		"SeasonJewelrySuitInfo":                                       reflect.ValueOf((*goxml.SeasonJewelrySuitInfo)(nil)),
		"SeasonJewelrySuitInfoM":                                      reflect.ValueOf((*goxml.SeasonJewelrySuitInfoM)(nil)),
		"SeasonJewelrySuitInfoMSuitTypeIndex":                         reflect.ValueOf((*goxml.SeasonJewelrySuitInfoMSuitTypeIndex)(nil)),
		"SeasonJewelrySuitInfoXml":                                    reflect.ValueOf((*goxml.SeasonJewelrySuitInfoXml)(nil)),
		"SeasonJewelrySuitInfoXmls":                                   reflect.ValueOf((*goxml.SeasonJewelrySuitInfoXmls)(nil)),
		"SeasonLevelAwardInfo":                                        reflect.ValueOf((*goxml.SeasonLevelAwardInfo)(nil)),
		"SeasonLevelAwardInfoManager":                                 reflect.ValueOf((*goxml.SeasonLevelAwardInfoManager)(nil)),
		"SeasonLevelAwardInfos":                                       reflect.ValueOf((*goxml.SeasonLevelAwardInfos)(nil)),
		"SeasonLevelInfo":                                             reflect.ValueOf((*goxml.SeasonLevelInfo)(nil)),
		"SeasonLevelInfoExt":                                          reflect.ValueOf((*goxml.SeasonLevelInfoExt)(nil)),
		"SeasonLevelInfoManager":                                      reflect.ValueOf((*goxml.SeasonLevelInfoManager)(nil)),
		"SeasonLevelInfos":                                            reflect.ValueOf((*goxml.SeasonLevelInfos)(nil)),
		"SeasonLevelTaskInfo":                                         reflect.ValueOf((*goxml.SeasonLevelTaskInfo)(nil)),
		"SeasonLevelTaskInfoManager":                                  reflect.ValueOf((*goxml.SeasonLevelTaskInfoManager)(nil)),
		"SeasonLevelTaskInfos":                                        reflect.ValueOf((*goxml.SeasonLevelTaskInfos)(nil)),
		"SeasonLinkInfo":                                              reflect.ValueOf((*goxml.SeasonLinkInfo)(nil)),
		"SeasonLinkInfoM":                                             reflect.ValueOf((*goxml.SeasonLinkInfoM)(nil)),
		"SeasonLinkInfoMEx":                                           reflect.ValueOf((*goxml.SeasonLinkInfoMEx)(nil)),
		"SeasonLinkInfoMSeasonIdIndex":                                reflect.ValueOf((*goxml.SeasonLinkInfoMSeasonIdIndex)(nil)),
		"SeasonLinkInfoXml":                                           reflect.ValueOf((*goxml.SeasonLinkInfoXml)(nil)),
		"SeasonLinkInfoXmls":                                          reflect.ValueOf((*goxml.SeasonLinkInfoXmls)(nil)),
		"SeasonLinkMonumentInfo":                                      reflect.ValueOf((*goxml.SeasonLinkMonumentInfo)(nil)),
		"SeasonLinkMonumentInfoM":                                     reflect.ValueOf((*goxml.SeasonLinkMonumentInfoM)(nil)),
		"SeasonLinkMonumentInfoMEx":                                   reflect.ValueOf((*goxml.SeasonLinkMonumentInfoMEx)(nil)),
		"SeasonLinkMonumentInfoMSeasonIdIndex":                        reflect.ValueOf((*goxml.SeasonLinkMonumentInfoMSeasonIdIndex)(nil)),
		"SeasonLinkMonumentInfoXml":                                   reflect.ValueOf((*goxml.SeasonLinkMonumentInfoXml)(nil)),
		"SeasonLinkMonumentInfoXmls":                                  reflect.ValueOf((*goxml.SeasonLinkMonumentInfoXmls)(nil)),
		"SeasonLinkMonumentRareInfo":                                  reflect.ValueOf((*goxml.SeasonLinkMonumentRareInfo)(nil)),
		"SeasonLinkMonumentRareInfoM":                                 reflect.ValueOf((*goxml.SeasonLinkMonumentRareInfoM)(nil)),
		"SeasonLinkMonumentRareInfoMEx":                               reflect.ValueOf((*goxml.SeasonLinkMonumentRareInfoMEx)(nil)),
		"SeasonLinkMonumentRareInfoMMonumentIdIndex":                  reflect.ValueOf((*goxml.SeasonLinkMonumentRareInfoMMonumentIdIndex)(nil)),
		"SeasonLinkMonumentRareInfoXml":                               reflect.ValueOf((*goxml.SeasonLinkMonumentRareInfoXml)(nil)),
		"SeasonLinkMonumentRareInfoXmls":                              reflect.ValueOf((*goxml.SeasonLinkMonumentRareInfoXmls)(nil)),
		"SeasonLinkRecycleRewardInfo":                                 reflect.ValueOf((*goxml.SeasonLinkRecycleRewardInfo)(nil)),
		"SeasonLinkRecycleRewardInfoM":                                reflect.ValueOf((*goxml.SeasonLinkRecycleRewardInfoM)(nil)),
		"SeasonLinkRecycleRewardInfoMSeasonIdIndex":                   reflect.ValueOf((*goxml.SeasonLinkRecycleRewardInfoMSeasonIdIndex)(nil)),
		"SeasonLinkRecycleRewardInfoXml":                              reflect.ValueOf((*goxml.SeasonLinkRecycleRewardInfoXml)(nil)),
		"SeasonLinkRecycleRewardInfoXmls":                             reflect.ValueOf((*goxml.SeasonLinkRecycleRewardInfoXmls)(nil)),
		"SeasonLinkRuneInfo":                                          reflect.ValueOf((*goxml.SeasonLinkRuneInfo)(nil)),
		"SeasonLinkRuneInfoM":                                         reflect.ValueOf((*goxml.SeasonLinkRuneInfoM)(nil)),
		"SeasonLinkRuneInfoMMonumentIdIndex":                          reflect.ValueOf((*goxml.SeasonLinkRuneInfoMMonumentIdIndex)(nil)),
		"SeasonLinkRuneInfoMMonumentIdPositionIndex":                  reflect.ValueOf((*goxml.SeasonLinkRuneInfoMMonumentIdPositionIndex)(nil)),
		"SeasonLinkRuneInfoMMonumentIdRareIndex":                      reflect.ValueOf((*goxml.SeasonLinkRuneInfoMMonumentIdRareIndex)(nil)),
		"SeasonLinkRuneInfoMMonumentIdUniqueLevelIndex":               reflect.ValueOf((*goxml.SeasonLinkRuneInfoMMonumentIdUniqueLevelIndex)(nil)),
		"SeasonLinkRuneInfoXml":                                       reflect.ValueOf((*goxml.SeasonLinkRuneInfoXml)(nil)),
		"SeasonLinkRuneInfoXmls":                                      reflect.ValueOf((*goxml.SeasonLinkRuneInfoXmls)(nil)),
		"SeasonMapAltarBuffInfo":                                      reflect.ValueOf((*goxml.SeasonMapAltarBuffInfo)(nil)),
		"SeasonMapAltarBuffInfoExt":                                   reflect.ValueOf((*goxml.SeasonMapAltarBuffInfoExt)(nil)),
		"SeasonMapAltarBuffInfoManager":                               reflect.ValueOf((*goxml.SeasonMapAltarBuffInfoManager)(nil)),
		"SeasonMapAltarBuffInfos":                                     reflect.ValueOf((*goxml.SeasonMapAltarBuffInfos)(nil)),
		"SeasonMapAltarInfo":                                          reflect.ValueOf((*goxml.SeasonMapAltarInfo)(nil)),
		"SeasonMapAltarInfoExt":                                       reflect.ValueOf((*goxml.SeasonMapAltarInfoExt)(nil)),
		"SeasonMapAltarInfoManager":                                   reflect.ValueOf((*goxml.SeasonMapAltarInfoManager)(nil)),
		"SeasonMapAltarInfos":                                         reflect.ValueOf((*goxml.SeasonMapAltarInfos)(nil)),
		"SeasonMapConfigInfo":                                         reflect.ValueOf((*goxml.SeasonMapConfigInfo)(nil)),
		"SeasonMapConfigInfoManager":                                  reflect.ValueOf((*goxml.SeasonMapConfigInfoManager)(nil)),
		"SeasonMapConfigInfos":                                        reflect.ValueOf((*goxml.SeasonMapConfigInfos)(nil)),
		"SeasonMapConnectInfo":                                        reflect.ValueOf((*goxml.SeasonMapConnectInfo)(nil)),
		"SeasonMapConnectInfoExt":                                     reflect.ValueOf((*goxml.SeasonMapConnectInfoExt)(nil)),
		"SeasonMapConnectInfoManager":                                 reflect.ValueOf((*goxml.SeasonMapConnectInfoManager)(nil)),
		"SeasonMapConnectInfos":                                       reflect.ValueOf((*goxml.SeasonMapConnectInfos)(nil)),
		"SeasonMapEvent":                                              reflect.ValueOf((*goxml.SeasonMapEvent)(nil)),
		"SeasonMapMasterInfo":                                         reflect.ValueOf((*goxml.SeasonMapMasterInfo)(nil)),
		"SeasonMapMasterInfoExt":                                      reflect.ValueOf((*goxml.SeasonMapMasterInfoExt)(nil)),
		"SeasonMapMasterInfoManager":                                  reflect.ValueOf((*goxml.SeasonMapMasterInfoManager)(nil)),
		"SeasonMapMasterInfos":                                        reflect.ValueOf((*goxml.SeasonMapMasterInfos)(nil)),
		"SeasonMapMonsterHpInfo":                                      reflect.ValueOf((*goxml.SeasonMapMonsterHpInfo)(nil)),
		"SeasonMapMonsterHpInfoM":                                     reflect.ValueOf((*goxml.SeasonMapMonsterHpInfoM)(nil)),
		"SeasonMapMonsterHpInfoXml":                                   reflect.ValueOf((*goxml.SeasonMapMonsterHpInfoXml)(nil)),
		"SeasonMapMonsterHpInfoXmls":                                  reflect.ValueOf((*goxml.SeasonMapMonsterHpInfoXmls)(nil)),
		"SeasonMapMonsterInfo":                                        reflect.ValueOf((*goxml.SeasonMapMonsterInfo)(nil)),
		"SeasonMapMonsterInfoExt":                                     reflect.ValueOf((*goxml.SeasonMapMonsterInfoExt)(nil)),
		"SeasonMapMonsterInfoManager":                                 reflect.ValueOf((*goxml.SeasonMapMonsterInfoManager)(nil)),
		"SeasonMapMonsterInfos":                                       reflect.ValueOf((*goxml.SeasonMapMonsterInfos)(nil)),
		"SeasonMapMonsterRewardInfo":                                  reflect.ValueOf((*goxml.SeasonMapMonsterRewardInfo)(nil)),
		"SeasonMapMonsterRewardInfoM":                                 reflect.ValueOf((*goxml.SeasonMapMonsterRewardInfoM)(nil)),
		"SeasonMapMonsterRewardInfoMEx":                               reflect.ValueOf((*goxml.SeasonMapMonsterRewardInfoMEx)(nil)),
		"SeasonMapMonsterRewardInfoMIdIndex":                          reflect.ValueOf((*goxml.SeasonMapMonsterRewardInfoMIdIndex)(nil)),
		"SeasonMapMonsterRewardInfoXml":                               reflect.ValueOf((*goxml.SeasonMapMonsterRewardInfoXml)(nil)),
		"SeasonMapMonsterRewardInfoXmls":                              reflect.ValueOf((*goxml.SeasonMapMonsterRewardInfoXmls)(nil)),
		"SeasonMapPositionInfo":                                       reflect.ValueOf((*goxml.SeasonMapPositionInfo)(nil)),
		"SeasonMapPositionInfoExt":                                    reflect.ValueOf((*goxml.SeasonMapPositionInfoExt)(nil)),
		"SeasonMapPositionInfoManager":                                reflect.ValueOf((*goxml.SeasonMapPositionInfoManager)(nil)),
		"SeasonMapPositionInfos":                                      reflect.ValueOf((*goxml.SeasonMapPositionInfos)(nil)),
		"SeasonMapTaskInfo":                                           reflect.ValueOf((*goxml.SeasonMapTaskInfo)(nil)),
		"SeasonMapTaskInfoManager":                                    reflect.ValueOf((*goxml.SeasonMapTaskInfoManager)(nil)),
		"SeasonMapTaskInfos":                                          reflect.ValueOf((*goxml.SeasonMapTaskInfos)(nil)),
		"SeasonMapTradeEventGroup":                                    reflect.ValueOf((*goxml.SeasonMapTradeEventGroup)(nil)),
		"SeasonMapTradeEventInfo":                                     reflect.ValueOf((*goxml.SeasonMapTradeEventInfo)(nil)),
		"SeasonMapTradeEventInfoManager":                              reflect.ValueOf((*goxml.SeasonMapTradeEventInfoManager)(nil)),
		"SeasonMapTradeEventInfos":                                    reflect.ValueOf((*goxml.SeasonMapTradeEventInfos)(nil)),
		"SeasonMapTradeGoodsInfo":                                     reflect.ValueOf((*goxml.SeasonMapTradeGoodsInfo)(nil)),
		"SeasonMapTradeGoodsInfoManager":                              reflect.ValueOf((*goxml.SeasonMapTradeGoodsInfoManager)(nil)),
		"SeasonMapTradeGoodsInfos":                                    reflect.ValueOf((*goxml.SeasonMapTradeGoodsInfos)(nil)),
		"SeasonMapTradeInfo":                                          reflect.ValueOf((*goxml.SeasonMapTradeInfo)(nil)),
		"SeasonMapTradeInfoExt":                                       reflect.ValueOf((*goxml.SeasonMapTradeInfoExt)(nil)),
		"SeasonMapTradeInfoManager":                                   reflect.ValueOf((*goxml.SeasonMapTradeInfoManager)(nil)),
		"SeasonMapTradeInfos":                                         reflect.ValueOf((*goxml.SeasonMapTradeInfos)(nil)),
		"SeasonMapTriggerInfo":                                        reflect.ValueOf((*goxml.SeasonMapTriggerInfo)(nil)),
		"SeasonMapTriggerInfoManager":                                 reflect.ValueOf((*goxml.SeasonMapTriggerInfoManager)(nil)),
		"SeasonMapTriggerInfos":                                       reflect.ValueOf((*goxml.SeasonMapTriggerInfos)(nil)),
		"SeasonResInfo":                                               reflect.ValueOf((*goxml.SeasonResInfo)(nil)),
		"SeasonResInfoM":                                              reflect.ValueOf((*goxml.SeasonResInfoM)(nil)),
		"SeasonResInfoXml":                                            reflect.ValueOf((*goxml.SeasonResInfoXml)(nil)),
		"SeasonResInfoXmls":                                           reflect.ValueOf((*goxml.SeasonResInfoXmls)(nil)),
		"SeasonReturnInfo":                                            reflect.ValueOf((*goxml.SeasonReturnInfo)(nil)),
		"SeasonReturnInfoManager":                                     reflect.ValueOf((*goxml.SeasonReturnInfoManager)(nil)),
		"SeasonReturnInfos":                                           reflect.ValueOf((*goxml.SeasonReturnInfos)(nil)),
		"SeasonStartTowerAwardInfo":                                   reflect.ValueOf((*goxml.SeasonStartTowerAwardInfo)(nil)),
		"SeasonStartTowerRankInfo":                                    reflect.ValueOf((*goxml.SeasonStartTowerRankInfo)(nil)),
		"SeasonStartTowerRankInfoManager":                             reflect.ValueOf((*goxml.SeasonStartTowerRankInfoManager)(nil)),
		"SeasonStartTowerRankInfos":                                   reflect.ValueOf((*goxml.SeasonStartTowerRankInfos)(nil)),
		"SeasonTalentTreeBaseInfo":                                    reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfo)(nil)),
		"SeasonTalentTreeBaseInfoM":                                   reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoM)(nil)),
		"SeasonTalentTreeBaseInfoMEx":                                 reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoMEx)(nil)),
		"SeasonTalentTreeBaseInfoMSeasonIdIndex":                      reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoMSeasonIdIndex)(nil)),
		"SeasonTalentTreeBaseInfoMSeasonIdNodeTypeBelongPageIndex":    reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoMSeasonIdNodeTypeBelongPageIndex)(nil)),
		"SeasonTalentTreeBaseInfoMSeasonIdNodeTypeIndex":              reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoMSeasonIdNodeTypeIndex)(nil)),
		"SeasonTalentTreeBaseInfoXml":                                 reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoXml)(nil)),
		"SeasonTalentTreeBaseInfoXmls":                                reflect.ValueOf((*goxml.SeasonTalentTreeBaseInfoXmls)(nil)),
		"SeasonTalentTreeConfigInfo":                                  reflect.ValueOf((*goxml.SeasonTalentTreeConfigInfo)(nil)),
		"SeasonTalentTreeConfigInfoM":                                 reflect.ValueOf((*goxml.SeasonTalentTreeConfigInfoM)(nil)),
		"SeasonTalentTreeConfigInfoMEx":                               reflect.ValueOf((*goxml.SeasonTalentTreeConfigInfoMEx)(nil)),
		"SeasonTalentTreeConfigInfoXml":                               reflect.ValueOf((*goxml.SeasonTalentTreeConfigInfoXml)(nil)),
		"SeasonTalentTreeConfigInfoXmls":                              reflect.ValueOf((*goxml.SeasonTalentTreeConfigInfoXmls)(nil)),
		"SeasonTalentTreeLevelInfo":                                   reflect.ValueOf((*goxml.SeasonTalentTreeLevelInfo)(nil)),
		"SeasonTalentTreeLevelInfoM":                                  reflect.ValueOf((*goxml.SeasonTalentTreeLevelInfoM)(nil)),
		"SeasonTalentTreeLevelInfoMEx":                                reflect.ValueOf((*goxml.SeasonTalentTreeLevelInfoMEx)(nil)),
		"SeasonTalentTreeLevelInfoMIdIndex":                           reflect.ValueOf((*goxml.SeasonTalentTreeLevelInfoMIdIndex)(nil)),
		"SeasonTalentTreeLevelInfoXml":                                reflect.ValueOf((*goxml.SeasonTalentTreeLevelInfoXml)(nil)),
		"SeasonTalentTreeLevelInfoXmls":                               reflect.ValueOf((*goxml.SeasonTalentTreeLevelInfoXmls)(nil)),
		"SeasonTalentTreeRecyleAwardInfo":                             reflect.ValueOf((*goxml.SeasonTalentTreeRecyleAwardInfo)(nil)),
		"SeasonTalentTreeRecyleAwardInfoM":                            reflect.ValueOf((*goxml.SeasonTalentTreeRecyleAwardInfoM)(nil)),
		"SeasonTalentTreeRecyleAwardInfoMSeasonIdIndex":               reflect.ValueOf((*goxml.SeasonTalentTreeRecyleAwardInfoMSeasonIdIndex)(nil)),
		"SeasonTalentTreeRecyleAwardInfoXml":                          reflect.ValueOf((*goxml.SeasonTalentTreeRecyleAwardInfoXml)(nil)),
		"SeasonTalentTreeRecyleAwardInfoXmls":                         reflect.ValueOf((*goxml.SeasonTalentTreeRecyleAwardInfoXmls)(nil)),
		"SeasonTalentTreeTaskAwardInfo":                               reflect.ValueOf((*goxml.SeasonTalentTreeTaskAwardInfo)(nil)),
		"SeasonTalentTreeTaskAwardInfoM":                              reflect.ValueOf((*goxml.SeasonTalentTreeTaskAwardInfoM)(nil)),
		"SeasonTalentTreeTaskAwardInfoMEx":                            reflect.ValueOf((*goxml.SeasonTalentTreeTaskAwardInfoMEx)(nil)),
		"SeasonTalentTreeTaskAwardInfoMSeasonIdIndex":                 reflect.ValueOf((*goxml.SeasonTalentTreeTaskAwardInfoMSeasonIdIndex)(nil)),
		"SeasonTalentTreeTaskAwardInfoXml":                            reflect.ValueOf((*goxml.SeasonTalentTreeTaskAwardInfoXml)(nil)),
		"SeasonTalentTreeTaskAwardInfoXmls":                           reflect.ValueOf((*goxml.SeasonTalentTreeTaskAwardInfoXmls)(nil)),
		"SelectSummonConfigInfo":                                      reflect.ValueOf((*goxml.SelectSummonConfigInfo)(nil)),
		"SelectSummonConfigInfoManager":                               reflect.ValueOf((*goxml.SelectSummonConfigInfoManager)(nil)),
		"SelectSummonConfigInfos":                                     reflect.ValueOf((*goxml.SelectSummonConfigInfos)(nil)),
		"SelectSummonGroupInfo":                                       reflect.ValueOf((*goxml.SelectSummonGroupInfo)(nil)),
		"SelectSummonGroupInfoExt":                                    reflect.ValueOf((*goxml.SelectSummonGroupInfoExt)(nil)),
		"SelectSummonGroupInfoManager":                                reflect.ValueOf((*goxml.SelectSummonGroupInfoManager)(nil)),
		"SelectSummonGroupInfos":                                      reflect.ValueOf((*goxml.SelectSummonGroupInfos)(nil)),
		"SelectSummonHeroInfo":                                        reflect.ValueOf((*goxml.SelectSummonHeroInfo)(nil)),
		"SelectSummonHeroInfoManager":                                 reflect.ValueOf((*goxml.SelectSummonHeroInfoManager)(nil)),
		"SelectSummonHeroInfos":                                       reflect.ValueOf((*goxml.SelectSummonHeroInfos)(nil)),
		"SelectSummonInfo":                                            reflect.ValueOf((*goxml.SelectSummonInfo)(nil)),
		"SelectSummonInfoExt":                                         reflect.ValueOf((*goxml.SelectSummonInfoExt)(nil)),
		"SelectSummonInfoManager":                                     reflect.ValueOf((*goxml.SelectSummonInfoManager)(nil)),
		"SelectSummonInfos":                                           reflect.ValueOf((*goxml.SelectSummonInfos)(nil)),
		"ServerInfo":                                                  reflect.ValueOf((*goxml.ServerInfo)(nil)),
		"ShareConfigInfo":                                             reflect.ValueOf((*goxml.ShareConfigInfo)(nil)),
		"ShareConfigInfoManager":                                      reflect.ValueOf((*goxml.ShareConfigInfoManager)(nil)),
		"ShareConfigInfos":                                            reflect.ValueOf((*goxml.ShareConfigInfos)(nil)),
		"ShieldInfo":                                                  reflect.ValueOf((*goxml.ShieldInfo)(nil)),
		"ShieldInfoM":                                                 reflect.ValueOf((*goxml.ShieldInfoM)(nil)),
		"ShieldInfoMEx":                                               reflect.ValueOf((*goxml.ShieldInfoMEx)(nil)),
		"ShieldInfoXml":                                               reflect.ValueOf((*goxml.ShieldInfoXml)(nil)),
		"ShieldInfoXmls":                                              reflect.ValueOf((*goxml.ShieldInfoXmls)(nil)),
		"ShootGameStageInfo":                                          reflect.ValueOf((*goxml.ShootGameStageInfo)(nil)),
		"ShootGameStageInfoManager":                                   reflect.ValueOf((*goxml.ShootGameStageInfoManager)(nil)),
		"ShootGameStageInfos":                                         reflect.ValueOf((*goxml.ShootGameStageInfos)(nil)),
		"ShopBoxRandomInfo":                                           reflect.ValueOf((*goxml.ShopBoxRandomInfo)(nil)),
		"ShopBoxRandomInfoManager":                                    reflect.ValueOf((*goxml.ShopBoxRandomInfoManager)(nil)),
		"ShopBoxRandomInfos":                                          reflect.ValueOf((*goxml.ShopBoxRandomInfos)(nil)),
		"ShopExtType":                                                 reflect.ValueOf((*goxml.ShopExtType)(nil)),
		"ShopInfo":                                                    reflect.ValueOf((*goxml.ShopInfo)(nil)),
		"ShopInfoExt":                                                 reflect.ValueOf((*goxml.ShopInfoExt)(nil)),
		"ShopInfoManager":                                             reflect.ValueOf((*goxml.ShopInfoManager)(nil)),
		"ShopInfos":                                                   reflect.ValueOf((*goxml.ShopInfos)(nil)),
		"ShopLimitType":                                               reflect.ValueOf((*goxml.ShopLimitType)(nil)),
		"ShopRandomGoodsInfo":                                         reflect.ValueOf((*goxml.ShopRandomGoodsInfo)(nil)),
		"ShopRandomGoodsInfoExt":                                      reflect.ValueOf((*goxml.ShopRandomGoodsInfoExt)(nil)),
		"ShopRandomGoodsInfoManager":                                  reflect.ValueOf((*goxml.ShopRandomGoodsInfoManager)(nil)),
		"ShopRandomGoodsInfos":                                        reflect.ValueOf((*goxml.ShopRandomGoodsInfos)(nil)),
		"ShopRandomInfo":                                              reflect.ValueOf((*goxml.ShopRandomInfo)(nil)),
		"ShopRandomInfoExt":                                           reflect.ValueOf((*goxml.ShopRandomInfoExt)(nil)),
		"ShopRandomInfoManager":                                       reflect.ValueOf((*goxml.ShopRandomInfoManager)(nil)),
		"ShopRandomInfos":                                             reflect.ValueOf((*goxml.ShopRandomInfos)(nil)),
		"ShopRegularGoodsInfo":                                        reflect.ValueOf((*goxml.ShopRegularGoodsInfo)(nil)),
		"ShopRegularGoodsInfoExt":                                     reflect.ValueOf((*goxml.ShopRegularGoodsInfoExt)(nil)),
		"ShopRegularGoodsInfoManager":                                 reflect.ValueOf((*goxml.ShopRegularGoodsInfoManager)(nil)),
		"ShopRegularGoodsInfos":                                       reflect.ValueOf((*goxml.ShopRegularGoodsInfos)(nil)),
		"SkillAttr":                                                   reflect.ValueOf((*goxml.SkillAttr)(nil)),
		"SkillEffectInfo":                                             reflect.ValueOf((*goxml.SkillEffectInfo)(nil)),
		"SkillInfo":                                                   reflect.ValueOf((*goxml.SkillInfo)(nil)),
		"SkillInfoExt":                                                reflect.ValueOf((*goxml.SkillInfoExt)(nil)),
		"SkillInfoManager":                                            reflect.ValueOf((*goxml.SkillInfoManager)(nil)),
		"SkillInfos":                                                  reflect.ValueOf((*goxml.SkillInfos)(nil)),
		"SkillLevelInfo":                                              reflect.ValueOf((*goxml.SkillLevelInfo)(nil)),
		"SkillLevelInfoExt":                                           reflect.ValueOf((*goxml.SkillLevelInfoExt)(nil)),
		"SkillLevelInfoManager":                                       reflect.ValueOf((*goxml.SkillLevelInfoManager)(nil)),
		"SkillLevelInfos":                                             reflect.ValueOf((*goxml.SkillLevelInfos)(nil)),
		"SkillTargetInfo":                                             reflect.ValueOf((*goxml.SkillTargetInfo)(nil)),
		"SkillTargetInfoManager":                                      reflect.ValueOf((*goxml.SkillTargetInfoManager)(nil)),
		"SkillTargetInfos":                                            reflect.ValueOf((*goxml.SkillTargetInfos)(nil)),
		"SkinInfo":                                                    reflect.ValueOf((*goxml.SkinInfo)(nil)),
		"SkinInfoExt":                                                 reflect.ValueOf((*goxml.SkinInfoExt)(nil)),
		"SkinInfoManager":                                             reflect.ValueOf((*goxml.SkinInfoManager)(nil)),
		"SkinInfos":                                                   reflect.ValueOf((*goxml.SkinInfos)(nil)),
		"StageInfo":                                                   reflect.ValueOf((*goxml.StageInfo)(nil)),
		"StoryReviewGroupInfo":                                        reflect.ValueOf((*goxml.StoryReviewGroupInfo)(nil)),
		"StoryReviewGroupInfoM":                                       reflect.ValueOf((*goxml.StoryReviewGroupInfoM)(nil)),
		"StoryReviewGroupInfoMStoryTypeIndex":                         reflect.ValueOf((*goxml.StoryReviewGroupInfoMStoryTypeIndex)(nil)),
		"StoryReviewGroupInfoXml":                                     reflect.ValueOf((*goxml.StoryReviewGroupInfoXml)(nil)),
		"StoryReviewGroupInfoXmls":                                    reflect.ValueOf((*goxml.StoryReviewGroupInfoXmls)(nil)),
		"SummonActivityCountInfo":                                     reflect.ValueOf((*goxml.SummonActivityCountInfo)(nil)),
		"SummonActivityCountInfoManager":                              reflect.ValueOf((*goxml.SummonActivityCountInfoManager)(nil)),
		"SummonActivityCountInfos":                                    reflect.ValueOf((*goxml.SummonActivityCountInfos)(nil)),
		"SummonActivityGuaranteeInfo":                                 reflect.ValueOf((*goxml.SummonActivityGuaranteeInfo)(nil)),
		"SummonActivityGuaranteeInfoManager":                          reflect.ValueOf((*goxml.SummonActivityGuaranteeInfoManager)(nil)),
		"SummonActivityGuaranteeInfos":                                reflect.ValueOf((*goxml.SummonActivityGuaranteeInfos)(nil)),
		"SummonClassInfo":                                             reflect.ValueOf((*goxml.SummonClassInfo)(nil)),
		"SummonClassInfoExt":                                          reflect.ValueOf((*goxml.SummonClassInfoExt)(nil)),
		"SummonClassInfoManager":                                      reflect.ValueOf((*goxml.SummonClassInfoManager)(nil)),
		"SummonClassInfos":                                            reflect.ValueOf((*goxml.SummonClassInfos)(nil)),
		"SummonGroupInfo":                                             reflect.ValueOf((*goxml.SummonGroupInfo)(nil)),
		"SummonGroupInfoExt":                                          reflect.ValueOf((*goxml.SummonGroupInfoExt)(nil)),
		"SummonGroupInfoManager":                                      reflect.ValueOf((*goxml.SummonGroupInfoManager)(nil)),
		"SummonGroupInfos":                                            reflect.ValueOf((*goxml.SummonGroupInfos)(nil)),
		"SummonSpecialClassInfo":                                      reflect.ValueOf((*goxml.SummonSpecialClassInfo)(nil)),
		"SummonSpecialClassInfoManager":                               reflect.ValueOf((*goxml.SummonSpecialClassInfoManager)(nil)),
		"SummonSpecialClassInfos":                                     reflect.ValueOf((*goxml.SummonSpecialClassInfos)(nil)),
		"SummonTypeInfo":                                              reflect.ValueOf((*goxml.SummonTypeInfo)(nil)),
		"SummonTypeInfoExt":                                           reflect.ValueOf((*goxml.SummonTypeInfoExt)(nil)),
		"SummonTypeInfoManager":                                       reflect.ValueOf((*goxml.SummonTypeInfoManager)(nil)),
		"SummonTypeInfos":                                             reflect.ValueOf((*goxml.SummonTypeInfos)(nil)),
		"TalesDungeonInfo":                                            reflect.ValueOf((*goxml.TalesDungeonInfo)(nil)),
		"TalesDungeonInfoExt":                                         reflect.ValueOf((*goxml.TalesDungeonInfoExt)(nil)),
		"TalesDungeonInfoManager":                                     reflect.ValueOf((*goxml.TalesDungeonInfoManager)(nil)),
		"TalesDungeonInfos":                                           reflect.ValueOf((*goxml.TalesDungeonInfos)(nil)),
		"TalesEliteInfo":                                              reflect.ValueOf((*goxml.TalesEliteInfo)(nil)),
		"TalesEliteInfoExt":                                           reflect.ValueOf((*goxml.TalesEliteInfoExt)(nil)),
		"TalesEliteInfoManager":                                       reflect.ValueOf((*goxml.TalesEliteInfoManager)(nil)),
		"TalesEliteInfos":                                             reflect.ValueOf((*goxml.TalesEliteInfos)(nil)),
		"TalesInfo":                                                   reflect.ValueOf((*goxml.TalesInfo)(nil)),
		"TalesInfoManager":                                            reflect.ValueOf((*goxml.TalesInfoManager)(nil)),
		"TalesInfos":                                                  reflect.ValueOf((*goxml.TalesInfos)(nil)),
		"TargetAttr":                                                  reflect.ValueOf((*goxml.TargetAttr)(nil)),
		"TaskInfo":                                                    reflect.ValueOf((*goxml.TaskInfo)(nil)),
		"TaskInfoExt":                                                 reflect.ValueOf((*goxml.TaskInfoExt)(nil)),
		"TaskInfoManager":                                             reflect.ValueOf((*goxml.TaskInfoManager)(nil)),
		"TaskInfos":                                                   reflect.ValueOf((*goxml.TaskInfos)(nil)),
		"TaskTypeInfo":                                                reflect.ValueOf((*goxml.TaskTypeInfo)(nil)),
		"TaskTypeInfoManager":                                         reflect.ValueOf((*goxml.TaskTypeInfoManager)(nil)),
		"TaskTypeInfos":                                               reflect.ValueOf((*goxml.TaskTypeInfos)(nil)),
		"TeamInitDataInfo":                                            reflect.ValueOf((*goxml.TeamInitDataInfo)(nil)),
		"TeamInitDataInfoManager":                                     reflect.ValueOf((*goxml.TeamInitDataInfoManager)(nil)),
		"TeamInitDataInfos":                                           reflect.ValueOf((*goxml.TeamInitDataInfos)(nil)),
		"TitleInfo":                                                   reflect.ValueOf((*goxml.TitleInfo)(nil)),
		"TitleInfoM":                                                  reflect.ValueOf((*goxml.TitleInfoM)(nil)),
		"TitleInfoMEx":                                                reflect.ValueOf((*goxml.TitleInfoMEx)(nil)),
		"TitleInfoXml":                                                reflect.ValueOf((*goxml.TitleInfoXml)(nil)),
		"TitleInfoXmls":                                               reflect.ValueOf((*goxml.TitleInfoXmls)(nil)),
		"TowerConfigInfo":                                             reflect.ValueOf((*goxml.TowerConfigInfo)(nil)),
		"TowerConfigInfoManager":                                      reflect.ValueOf((*goxml.TowerConfigInfoManager)(nil)),
		"TowerConfigInfos":                                            reflect.ValueOf((*goxml.TowerConfigInfos)(nil)),
		"TowerInfo":                                                   reflect.ValueOf((*goxml.TowerInfo)(nil)),
		"TowerInfoExt":                                                reflect.ValueOf((*goxml.TowerInfoExt)(nil)),
		"TowerInfoManager":                                            reflect.ValueOf((*goxml.TowerInfoManager)(nil)),
		"TowerInfos":                                                  reflect.ValueOf((*goxml.TowerInfos)(nil)),
		"TowerSeasonConfigInfo":                                       reflect.ValueOf((*goxml.TowerSeasonConfigInfo)(nil)),
		"TowerSeasonConfigInfoManager":                                reflect.ValueOf((*goxml.TowerSeasonConfigInfoManager)(nil)),
		"TowerSeasonConfigInfos":                                      reflect.ValueOf((*goxml.TowerSeasonConfigInfos)(nil)),
		"TowerSeasonDungeonInfo":                                      reflect.ValueOf((*goxml.TowerSeasonDungeonInfo)(nil)),
		"TowerSeasonDungeonInfoExt":                                   reflect.ValueOf((*goxml.TowerSeasonDungeonInfoExt)(nil)),
		"TowerSeasonDungeonInfoManager":                               reflect.ValueOf((*goxml.TowerSeasonDungeonInfoManager)(nil)),
		"TowerSeasonDungeonInfos":                                     reflect.ValueOf((*goxml.TowerSeasonDungeonInfos)(nil)),
		"TowerSeasonFightEffectInfo":                                  reflect.ValueOf((*goxml.TowerSeasonFightEffectInfo)(nil)),
		"TowerSeasonFightEffectInfoExt":                               reflect.ValueOf((*goxml.TowerSeasonFightEffectInfoExt)(nil)),
		"TowerSeasonFightEffectInfoManager":                           reflect.ValueOf((*goxml.TowerSeasonFightEffectInfoManager)(nil)),
		"TowerSeasonFightEffectInfos":                                 reflect.ValueOf((*goxml.TowerSeasonFightEffectInfos)(nil)),
		"TowerSeasonQuickPrivilegeInfo":                               reflect.ValueOf((*goxml.TowerSeasonQuickPrivilegeInfo)(nil)),
		"TowerSeasonQuickPrivilegeInfoExt":                            reflect.ValueOf((*goxml.TowerSeasonQuickPrivilegeInfoExt)(nil)),
		"TowerSeasonQuickPrivilegeInfoManager":                        reflect.ValueOf((*goxml.TowerSeasonQuickPrivilegeInfoManager)(nil)),
		"TowerSeasonQuickPrivilegeInfos":                              reflect.ValueOf((*goxml.TowerSeasonQuickPrivilegeInfos)(nil)),
		"TowerSeasonRankRewardInfo":                                   reflect.ValueOf((*goxml.TowerSeasonRankRewardInfo)(nil)),
		"TowerSeasonRankRewardInfoManager":                            reflect.ValueOf((*goxml.TowerSeasonRankRewardInfoManager)(nil)),
		"TowerSeasonRankRewardInfos":                                  reflect.ValueOf((*goxml.TowerSeasonRankRewardInfos)(nil)),
		"TowerSeasonTaskInfo":                                         reflect.ValueOf((*goxml.TowerSeasonTaskInfo)(nil)),
		"TowerSeasonTaskInfoExt":                                      reflect.ValueOf((*goxml.TowerSeasonTaskInfoExt)(nil)),
		"TowerSeasonTaskInfoManager":                                  reflect.ValueOf((*goxml.TowerSeasonTaskInfoManager)(nil)),
		"TowerSeasonTaskInfos":                                        reflect.ValueOf((*goxml.TowerSeasonTaskInfos)(nil)),
		"TowerstarChapterInfo":                                        reflect.ValueOf((*goxml.TowerstarChapterInfo)(nil)),
		"TowerstarChapterInfoManager":                                 reflect.ValueOf((*goxml.TowerstarChapterInfoManager)(nil)),
		"TowerstarChapterInfos":                                       reflect.ValueOf((*goxml.TowerstarChapterInfos)(nil)),
		"TowerstarConditionInfo":                                      reflect.ValueOf((*goxml.TowerstarConditionInfo)(nil)),
		"TowerstarConditionInfoManager":                               reflect.ValueOf((*goxml.TowerstarConditionInfoManager)(nil)),
		"TowerstarConditionInfos":                                     reflect.ValueOf((*goxml.TowerstarConditionInfos)(nil)),
		"TowerstarConfigInfo":                                         reflect.ValueOf((*goxml.TowerstarConfigInfo)(nil)),
		"TowerstarConfigInfoManager":                                  reflect.ValueOf((*goxml.TowerstarConfigInfoManager)(nil)),
		"TowerstarConfigInfos":                                        reflect.ValueOf((*goxml.TowerstarConfigInfos)(nil)),
		"TowerstarDungeonInfo":                                        reflect.ValueOf((*goxml.TowerstarDungeonInfo)(nil)),
		"TowerstarDungeonInfoManager":                                 reflect.ValueOf((*goxml.TowerstarDungeonInfoManager)(nil)),
		"TowerstarDungeonInfos":                                       reflect.ValueOf((*goxml.TowerstarDungeonInfos)(nil)),
		"TowerstarRewardInfo":                                         reflect.ValueOf((*goxml.TowerstarRewardInfo)(nil)),
		"TowerstarRewardInfoManager":                                  reflect.ValueOf((*goxml.TowerstarRewardInfoManager)(nil)),
		"TowerstarRewardInfos":                                        reflect.ValueOf((*goxml.TowerstarRewardInfos)(nil)),
		"TrialConfigInfo":                                             reflect.ValueOf((*goxml.TrialConfigInfo)(nil)),
		"TrialConfigInfoManager":                                      reflect.ValueOf((*goxml.TrialConfigInfoManager)(nil)),
		"TrialConfigInfos":                                            reflect.ValueOf((*goxml.TrialConfigInfos)(nil)),
		"TrialCopyInfo":                                               reflect.ValueOf((*goxml.TrialCopyInfo)(nil)),
		"TrialCopyInfoManager":                                        reflect.ValueOf((*goxml.TrialCopyInfoManager)(nil)),
		"TrialCopyInfos":                                              reflect.ValueOf((*goxml.TrialCopyInfos)(nil)),
		"TrialHeroInfo":                                               reflect.ValueOf((*goxml.TrialHeroInfo)(nil)),
		"TrialHeroInfoManager":                                        reflect.ValueOf((*goxml.TrialHeroInfoManager)(nil)),
		"TrialHeroInfos":                                              reflect.ValueOf((*goxml.TrialHeroInfos)(nil)),
		"TrialInfo":                                                   reflect.ValueOf((*goxml.TrialInfo)(nil)),
		"TrialInfoExt":                                                reflect.ValueOf((*goxml.TrialInfoExt)(nil)),
		"TrialInfoManager":                                            reflect.ValueOf((*goxml.TrialInfoManager)(nil)),
		"TrialInfos":                                                  reflect.ValueOf((*goxml.TrialInfos)(nil)),
		"Uint32Slice":                                                 reflect.ValueOf((*goxml.Uint32Slice)(nil)),
		"VipDungeonPrivilege":                                         reflect.ValueOf((*goxml.VipDungeonPrivilege)(nil)),
		"VipInfo":                                                     reflect.ValueOf((*goxml.VipInfo)(nil)),
		"VipInfoManager":                                              reflect.ValueOf((*goxml.VipInfoManager)(nil)),
		"VipInfos":                                                    reflect.ValueOf((*goxml.VipInfos)(nil)),
		"VipPrivilegeInfo":                                            reflect.ValueOf((*goxml.VipPrivilegeInfo)(nil)),
		"VipPrivilegeInfoExt":                                         reflect.ValueOf((*goxml.VipPrivilegeInfoExt)(nil)),
		"VipPrivilegeInfoManager":                                     reflect.ValueOf((*goxml.VipPrivilegeInfoManager)(nil)),
		"VipPrivilegeInfos":                                           reflect.ValueOf((*goxml.VipPrivilegeInfos)(nil)),
		"VipPrivilegeNumber":                                          reflect.ValueOf((*goxml.VipPrivilegeNumber)(nil)),
		"VipProductInfo":                                              reflect.ValueOf((*goxml.VipProductInfo)(nil)),
		"VipProductInfoManager":                                       reflect.ValueOf((*goxml.VipProductInfoManager)(nil)),
		"VipProductInfos":                                             reflect.ValueOf((*goxml.VipProductInfos)(nil)),
		"WebRewardInfo":                                               reflect.ValueOf((*goxml.WebRewardInfo)(nil)),
		"WebRewardInfoManager":                                        reflect.ValueOf((*goxml.WebRewardInfoManager)(nil)),
		"WebRewardInfos":                                              reflect.ValueOf((*goxml.WebRewardInfos)(nil)),
		"WeightAndId":                                                 reflect.ValueOf((*goxml.WeightAndId)(nil)),
		"WeightAndIdRed":                                              reflect.ValueOf((*goxml.WeightAndIdRed)(nil)),
		"WeightValue":                                                 reflect.ValueOf((*goxml.WeightValue)(nil)),
		"WishlistConfigInfo":                                          reflect.ValueOf((*goxml.WishlistConfigInfo)(nil)),
		"WishlistConfigInfoManager":                                   reflect.ValueOf((*goxml.WishlistConfigInfoManager)(nil)),
		"WishlistConfigInfos":                                         reflect.ValueOf((*goxml.WishlistConfigInfos)(nil)),
		"WishlistGuaranteeInfo":                                       reflect.ValueOf((*goxml.WishlistGuaranteeInfo)(nil)),
		"WishlistGuaranteeInfoManager":                                reflect.ValueOf((*goxml.WishlistGuaranteeInfoManager)(nil)),
		"WishlistGuaranteeInfos":                                      reflect.ValueOf((*goxml.WishlistGuaranteeInfos)(nil)),
		"WishlistRangeInfo":                                           reflect.ValueOf((*goxml.WishlistRangeInfo)(nil)),
		"WishlistRangeInfoManager":                                    reflect.ValueOf((*goxml.WishlistRangeInfoManager)(nil)),
		"WishlistRangeInfos":                                          reflect.ValueOf((*goxml.WishlistRangeInfos)(nil)),
		"WorldbossConfigInfo":                                         reflect.ValueOf((*goxml.WorldbossConfigInfo)(nil)),
		"WorldbossConfigInfoManager":                                  reflect.ValueOf((*goxml.WorldbossConfigInfoManager)(nil)),
		"WorldbossConfigInfos":                                        reflect.ValueOf((*goxml.WorldbossConfigInfos)(nil)),
		"WorldbossDifficultyInfo":                                     reflect.ValueOf((*goxml.WorldbossDifficultyInfo)(nil)),
		"WorldbossDifficultyInfoManager":                              reflect.ValueOf((*goxml.WorldbossDifficultyInfoManager)(nil)),
		"WorldbossDifficultyInfos":                                    reflect.ValueOf((*goxml.WorldbossDifficultyInfos)(nil)),
		"WorldbossInfo":                                               reflect.ValueOf((*goxml.WorldbossInfo)(nil)),
		"WorldbossInfoExt":                                            reflect.ValueOf((*goxml.WorldbossInfoExt)(nil)),
		"WorldbossInfoManager":                                        reflect.ValueOf((*goxml.WorldbossInfoManager)(nil)),
		"WorldbossInfos":                                              reflect.ValueOf((*goxml.WorldbossInfos)(nil)),
		"WorldbossRankRewardInfo":                                     reflect.ValueOf((*goxml.WorldbossRankRewardInfo)(nil)),
		"WorldbossRankRewardInfoManager":                              reflect.ValueOf((*goxml.WorldbossRankRewardInfoManager)(nil)),
		"WorldbossRankRewardInfos":                                    reflect.ValueOf((*goxml.WorldbossRankRewardInfos)(nil)),
		"WorldbossTaskInfo":                                           reflect.ValueOf((*goxml.WorldbossTaskInfo)(nil)),
		"WorldbossTaskInfoManager":                                    reflect.ValueOf((*goxml.WorldbossTaskInfoManager)(nil)),
		"WorldbossTaskInfos":                                          reflect.ValueOf((*goxml.WorldbossTaskInfos)(nil)),
		"WrestleBotInfo":                                              reflect.ValueOf((*goxml.WrestleBotInfo)(nil)),
		"WrestleBotInfoExt":                                           reflect.ValueOf((*goxml.WrestleBotInfoExt)(nil)),
		"WrestleBotInfoManager":                                       reflect.ValueOf((*goxml.WrestleBotInfoManager)(nil)),
		"WrestleBotInfos":                                             reflect.ValueOf((*goxml.WrestleBotInfos)(nil)),
		"WrestleBuff":                                                 reflect.ValueOf((*goxml.WrestleBuff)(nil)),
		"WrestleBuffInfo":                                             reflect.ValueOf((*goxml.WrestleBuffInfo)(nil)),
		"WrestleBuffInfoExt":                                          reflect.ValueOf((*goxml.WrestleBuffInfoExt)(nil)),
		"WrestleBuffInfoManager":                                      reflect.ValueOf((*goxml.WrestleBuffInfoManager)(nil)),
		"WrestleBuffInfos":                                            reflect.ValueOf((*goxml.WrestleBuffInfos)(nil)),
		"WrestleConfigInfo":                                           reflect.ValueOf((*goxml.WrestleConfigInfo)(nil)),
		"WrestleConfigInfoManager":                                    reflect.ValueOf((*goxml.WrestleConfigInfoManager)(nil)),
		"WrestleConfigInfos":                                          reflect.ValueOf((*goxml.WrestleConfigInfos)(nil)),
		"WrestleLevelInfo":                                            reflect.ValueOf((*goxml.WrestleLevelInfo)(nil)),
		"WrestleLevelInfoExt":                                         reflect.ValueOf((*goxml.WrestleLevelInfoExt)(nil)),
		"WrestleLevelInfoManager":                                     reflect.ValueOf((*goxml.WrestleLevelInfoManager)(nil)),
		"WrestleLevelInfos":                                           reflect.ValueOf((*goxml.WrestleLevelInfos)(nil)),
		"WrestleLevelTaskInfo":                                        reflect.ValueOf((*goxml.WrestleLevelTaskInfo)(nil)),
		"WrestleLevelTaskInfoManager":                                 reflect.ValueOf((*goxml.WrestleLevelTaskInfoManager)(nil)),
		"WrestleLevelTaskInfos":                                       reflect.ValueOf((*goxml.WrestleLevelTaskInfos)(nil)),
		"WrestleRankInfo":                                             reflect.ValueOf((*goxml.WrestleRankInfo)(nil)),
		"WrestleRankInfoManager":                                      reflect.ValueOf((*goxml.WrestleRankInfoManager)(nil)),
		"WrestleRankInfos":                                            reflect.ValueOf((*goxml.WrestleRankInfos)(nil)),
		"WrestleRankRewardInfo":                                       reflect.ValueOf((*goxml.WrestleRankRewardInfo)(nil)),
		"WrestleRankRewardInfoManager":                                reflect.ValueOf((*goxml.WrestleRankRewardInfoManager)(nil)),
		"WrestleRankRewardInfos":                                      reflect.ValueOf((*goxml.WrestleRankRewardInfos)(nil)),
		"WrestleRewardInfo":                                           reflect.ValueOf((*goxml.WrestleRewardInfo)(nil)),
		"WrestleRewardInfoManager":                                    reflect.ValueOf((*goxml.WrestleRewardInfoManager)(nil)),
		"WrestleRewardInfos":                                          reflect.ValueOf((*goxml.WrestleRewardInfos)(nil)),
		"XmlData":                                                     reflect.ValueOf((*goxml.XmlData)(nil)),
		"XorKey":                                                      reflect.ValueOf((*goxml.XorKey)(nil)),
		"XorKeyManager":                                               reflect.ValueOf((*goxml.XorKeyManager)(nil)),
		"XorKeys":                                                     reflect.ValueOf((*goxml.XorKeys)(nil)),

		// interface wrapper definitions
		"_ActivityIndexI": reflect.ValueOf((*_app_goxml_ActivityIndexI)(nil)),
		"_GetRecvTask":    reflect.ValueOf((*_app_goxml_GetRecvTask)(nil)),
		"_HasRankBySort":  reflect.ValueOf((*_app_goxml_HasRankBySort)(nil)),
		"_MultiRandom":    reflect.ValueOf((*_app_goxml_MultiRandom)(nil)),
		"_RecvTask":       reflect.ValueOf((*_app_goxml_RecvTask)(nil)),
		"_WeightValue":    reflect.ValueOf((*_app_goxml_WeightValue)(nil)),
	}
}

// _app_goxml_ActivityIndexI is an interface wrapper for ActivityIndexI type
type _app_goxml_ActivityIndexI struct {
	IValue       interface{}
	WGetEndDay   func(a0 uint32) uint32
	WGetStartDay func(a0 uint32) uint32
	WIsExist     func(a0 uint32) bool
}

func (W _app_goxml_ActivityIndexI) GetEndDay(a0 uint32) uint32 {
	return W.WGetEndDay(a0)
}
func (W _app_goxml_ActivityIndexI) GetStartDay(a0 uint32) uint32 {
	return W.WGetStartDay(a0)
}
func (W _app_goxml_ActivityIndexI) IsExist(a0 uint32) bool {
	return W.WIsExist(a0)
}

// _app_goxml_GetRecvTask is an interface wrapper for GetRecvTask type
type _app_goxml_GetRecvTask struct {
	IValue         interface{}
	WGetRecvTaskID func(a0 uint32) goxml.RecvTask
}

func (W _app_goxml_GetRecvTask) GetRecvTaskID(a0 uint32) goxml.RecvTask {
	return W.WGetRecvTaskID(a0)
}

// _app_goxml_HasRankBySort is an interface wrapper for HasRankBySort type
type _app_goxml_HasRankBySort struct {
	IValue   interface{}
	WGetRank func() uint32
}

func (W _app_goxml_HasRankBySort) GetRank() uint32 {
	return W.WGetRank()
}

// _app_goxml_MultiRandom is an interface wrapper for MultiRandom type
type _app_goxml_MultiRandom struct {
	IValue     interface{}
	WGetWeight func() int
}

func (W _app_goxml_MultiRandom) GetWeight() int {
	return W.WGetWeight()
}

// _app_goxml_RecvTask is an interface wrapper for RecvTask type
type _app_goxml_RecvTask struct {
	IValue     interface{}
	WGetAward  func() []*cl.Resource
	WGetID     func() uint32
	WGetModule func() uint32
	WTaskValue func() uint32
	WTypeID    func() uint32
}

func (W _app_goxml_RecvTask) GetAward() []*cl.Resource {
	return W.WGetAward()
}
func (W _app_goxml_RecvTask) GetID() uint32 {
	return W.WGetID()
}
func (W _app_goxml_RecvTask) GetModule() uint32 {
	return W.WGetModule()
}
func (W _app_goxml_RecvTask) TaskValue() uint32 {
	return W.WTaskValue()
}
func (W _app_goxml_RecvTask) TypeID() uint32 {
	return W.WTypeID()
}

// _app_goxml_WeightValue is an interface wrapper for WeightValue type
type _app_goxml_WeightValue struct {
	IValue     interface{}
	WGetWeight func() int
}

func (W _app_goxml_WeightValue) GetWeight() int {
	return W.WGetWeight()
}
