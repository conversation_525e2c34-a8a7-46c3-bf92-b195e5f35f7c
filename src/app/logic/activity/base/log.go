package base

import (
	"app/goxml"
	"app/logic/activity"

	l4g "github.com/ivanabc/log4go"
)

type LogM struct {
	logs       map[uint64]*Logs //uid=>logs
	limit      uint32
	logChanges map[uint64]struct{} //哪些用户进行了修改
}

func NewLogM(limit uint32) *LogM {
	return &LogM{
		logs:       make(map[uint64]*Logs),
		limit:      limit,
		logChanges: make(map[uint64]struct{}),
	}
}

type LogOwner interface {
	GetUniqID() uint64
	GetLogIndex() uint32
	SetLogIndex(uint32)
}

type Logger interface {
	SetID(uint32)
	GetID() uint32
	GetUniqID() uint64 //查找的时候用的
}

func (m *LogM) NewLogs(owner LogOwner) *Logs {
	logs := NewLogs(owner, m.limit)
	m.logs[owner.GetUniqID()] = logs
	return logs
}

func (m *LogM) GetOwnerLogs(owner LogOwner) *Logs {
	id := owner.GetUniqID()
	if v, exist := m.logs[id]; exist {
		return v
	}
	return m.NewLogs(owner)
}

func (m *LogM) SetChange(logs *Logs) {
	m.logChanges[logs.owner.GetUniqID()] = struct{}{}
}

//增加日志
/*
func (m *LogM) AddLog(owner LogOwner, log Logger) {
	logs := m.getOwnerLogs(owner)
	logs.AddLog(log)
	m.setLogChange(owner)
}

func (m *LogM) SetLogChange(owner LogOwner) {
	m.logChanges[owner.GetUniqID()] = struct{}{}
}

func (m *LogM) GetAllLogs(owner LogOwner) []Logger {
	logs := m.getOwnerLogs(owner)
	return logs.GetAll()
}

func (m *LogM) CheckLogLoaded(owner LogOwner) bool {
	logs := m.getOwnerLogs(owner)
	return logs.Loaded()
}

func (m *LogM) LoadLogs(owner LogOwner, log Logger) {
	logs := m.getOwnerLogs(owner)
	logs.LoadFromDB(log)
}

func (m *LogM) SetLoadFinish(owner LogOwner) {
	logs := m.getOwnerLogs(owner)
	logs.LoadFinish()
}

func (m *LogM) GetLog(owner LogOwner, id uint64) Logger {
	logs := m.getOwnerLogs(owner)
	log := logs.GetByUniqId(id)
	return log
}

func (m *LogM) ModifyLog(owner LogOwner, log Logger) {
	logs := m.getOwnerLogs(owner)
	logs.ModifyByUniqId(log)
	m.setLogChange(owner)
}


func (m *LogM) DeleteLogs(owner LogOwner, ids []uint64) {
	logs := m.getOwnerLogs(owner)
	logs.DelByUniqId(ids)
	m.setLogChange(owner)
}
*/

func (m *LogM) Save() (map[uint64][]Logger, map[uint64][]uint32) {
	changes := make(map[uint64][]Logger, len(m.logChanges))
	deletes := make(map[uint64][]uint32)
	for id := range m.logChanges {
		logs := m.logs[id]
		if logs != nil {
			cLogs, dLogs := logs.Save()
			if len(cLogs) > 0 {
				changes[id] = cLogs
			}
			if len(dLogs) > 0 {
				deletes[id] = dLogs
			}
		}
	}
	m.logChanges = make(map[uint64]struct{})
	return changes, deletes
}

func (m *LogM) GetChanges() int {
	return len(m.logChanges)
}

type Logs struct {
	owner LogOwner //日志属于谁的
	logs  []Logger //方便从数据库load数据进来判断

	changes map[uint32]Logger   //所有修改的
	deletes map[uint32]struct{} //需要删除的。

	//index  uint32            //目前的序号从0开始。
	limit  uint32 //总个数
	loaded bool
}

func NewLogs(owner LogOwner, limit uint32) *Logs {
	return &Logs{
		owner:   owner,
		limit:   limit,
		logs:    make([]Logger, limit),
		changes: make(map[uint32]Logger),
		deletes: make(map[uint32]struct{}),
	}
}

// 添加一个日志
func (ls *Logs) AddLog(srv activity.Servicer, log Logger) {
	if srv.ServiceStatus() == goxml.ServerStatusMaintain {
		return
	}

	index := ls.owner.GetLogIndex()
	//防止调整日志数量导致数组越界的panic
	if index >= ls.limit {
		l4g.Error("LogM uid:%d index error user index:%d limit:%d", ls.owner.GetUniqID(), index, ls.limit)
		if ls.limit <= 1 {
			return
		}
		index = ls.limit - 1
	}
	log.SetID(index)
	ls.logs[index] = log
	delete(ls.deletes, index)
	ls.changes[index] = log

	index++
	if index >= ls.limit {
		index = 0
	}
	ls.owner.SetLogIndex(index)
}

// 外部在把他转化为具体的指针 返回修改的和要删除的。
func (ls *Logs) Save() ([]Logger, []uint32) {
	changes := make([]Logger, 0, len(ls.changes))
	deletes := make([]uint32, 0, len(ls.deletes))
	for _, v := range ls.changes {
		changes = append(changes, v)
	}
	for id := range ls.deletes {
		deletes = append(deletes, id)
	}
	return changes, deletes
}

func (ls *Logs) LoadFromDB(log Logger) {
	if log.GetID() >= ls.limit {
		return
	}
	if ls.logs[log.GetID()] == nil {
		ls.logs[log.GetID()] = log
	}
}

func (ls *Logs) LoadFinish() {
	ls.loaded = true
}

func (ls *Logs) CheckLoaded() bool {
	return ls.loaded
}

// 在外面转化为具体的指针
func (ls *Logs) GetAll() []Logger {
	//没有的话需要去拉取
	if ls.loaded {
		ownerIndex := ls.owner.GetLogIndex()
		logs := make([]Logger, 0, len(ls.logs))
		for i := uint32(0); i < ls.limit; i++ {
			index := (i + ownerIndex) % ls.limit
			if ls.logs[index] != nil {
				logs = append(logs, ls.logs[index])
			}
		}
		return logs
	}
	return nil
}

// 在外面转化为具体的指针
func (ls *Logs) GetLog(id uint64) Logger {
	for _, v := range ls.logs {
		if v != nil && v.GetUniqID() == id {
			return v
		}
	}
	return nil
}

// 修改某一个id的数据
// uniqID 不需要传
func (ls *Logs) ModifyLog(log Logger) {
	id := log.GetID()
	uniqID := log.GetUniqID()
	oldLog := ls.logs[id]
	if oldLog == nil {
		l4g.Errorf("logs modify log error uid:%d, log id:%d, log uniqID:%d", ls.owner.GetUniqID(), id, uniqID)
		return
	}
	if oldLog.GetUniqID() != uniqID {
		l4g.Errorf("logs modify log error  uid:%d, log id:%d, log uniqID error:%d %d",
			ls.owner.GetUniqID(), id, uniqID, oldLog.GetUniqID())
		return
	}
	ls.logs[id] = log
	ls.changes[id] = log
}

func (ls *Logs) DelLog(uniqIds []uint64) {
	for _, v := range uniqIds {
		for k, l := range ls.logs {
			if l != nil && l.GetUniqID() == v {
				ls.logs[k] = nil
				delete(ls.changes, uint32(k))
				ls.deletes[uint32(k)] = struct{}{}
				break
			}
		}
	}
}

func (ls *Logs) DelAllLog(logM *LogM) {
	for i := uint32(0); i < ls.limit; i++ {
		if len(ls.logs) > int(i) && ls.logs[i] != nil {
			ls.logs[i] = nil
		}
		delete(ls.changes, i)
		ls.deletes[i] = struct{}{}
	}
	ls.LoadFinish() // 全部删除，默认为已经加载过了
	logM.SetChange(ls)
}
