package mail

import (
	"app/goxml"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	"github.com/google/btree"
	l4g "github.com/ivanabc/log4go"
)

const MaxExpireInterval int64 = 7 * 86400 // 邮件过期时长

const (
	TypeNormal uint32 = 0 //普通邮件
	TypeGlobal uint32 = 1 //全服邮件
	TypeGuard  uint32 = 2 //卫兵邮件（记录信息）
)

type Box struct {
	owner    uint64
	ms       map[uint64]*cl.Mail
	gmDelIds []uint64

	index *btree.BTree
	//根据邮件过期时间排序
	expired *btree.BTree
	//红点机制使用
	newMails map[uint64]struct{}

	guard *cl.Mail //记录邮箱信息（最大进度）
	//这个值没有直接落地，是通过玩家身上的guard邮件的processId来复制的
	lastMergedID uint64 //上线合并全服邮件使用,只用来做合并全服邮件使用

	//创建，修改，删除邮件使用，定时保存使用
	changes map[uint64]*cl.Mail
	deletes map[uint64]struct{}
}

const btreeDegree int = 32

//nolint:varnamelen
func NewBox(user uint64) *Box {
	b := &Box{
		owner:    user,
		ms:       make(map[uint64]*cl.Mail),
		index:    btree.New(btreeDegree),
		expired:  btree.New(btreeDegree),
		newMails: make(map[uint64]struct{}),
		//lastMergedID: user, 初始设置为0
		changes: make(map[uint64]*cl.Mail),
		deletes: make(map[uint64]struct{}),
	}
	b.guard = &cl.Mail{
		Id:   user, //这个id就是自己的id,作为守护邮件一直保存的
		Type: TypeGuard,
	}
	return b
}

func (b *Box) Owner() uint64 {
	return b.owner
}

// 构建btree Item
type indexItem struct {
	id uint64
}

func (i *indexItem) Less(than btree.Item) bool {
	return i.id < than.(*indexItem).id
}

func newIndexItem(m *cl.Mail) *indexItem {
	return &indexItem{
		id: m.Id,
	}
}

// 构建btree Item
type expiredItem struct {
	id uint64
	tm int64
}

func (e *expiredItem) Less(than btree.Item) bool {
	other := than.(*expiredItem)
	if e.tm < other.tm {
		return true
	}
	if e.tm == other.tm && e.id < other.id {
		return true
	}
	return false
}

func newExpiredItem(m *cl.Mail) *expiredItem {
	return &expiredItem{
		id: m.Id,
		tm: m.ExpiredTime,
	}
}

// 加载数据
// 注意创角不会调用这个方法
func (b *Box) Load(private map[uint64]*cl.Mail) {
	l4g.Debugf("user: %d load mail: b:%+v %+v", b.owner, b, private)
	//先加载自己的guard邮件
	if guard, exist := private[b.owner]; exist {
		b.guard = guard
		//删除邮箱标记邮件
		delete(private, b.owner)
	}

	l4g.Debugf("user: %d guard mail: %+v", b.owner, b.guard)
	//只在这里做赋值，后面要等到合并完全局邮件之后在重新赋值为最新的
	b.lastMergedID = b.processID()
	l4g.Debugf("user: %d guard mail lasrMergetId is %d", b.owner, b.lastMergedID)

	for id, m := range private {
		b.ms[id] = m
		b.index.ReplaceOrInsert(newIndexItem(m))
		b.expired.ReplaceOrInsert(newExpiredItem(m))
		if isNewMail(m) {
			b.newMails[id] = struct{}{}
		}
		if m.GmDel {
			b.gmDelIds = append(b.gmDelIds, id)
		}
	}
	if item := b.index.Max(); item != nil {
		b.setProcessID(item.(*indexItem).id)
	}
	//l4g.Debugf("mailtest user: %d load mail finish: %+v", b.owner, b)
}

func (b *Box) setProcessID(id uint64) {
	if id > b.processID() {
		b.guard.ProcessId = id
		b.changes[b.guard.Id] = b.guard
	}
}

func (b *Box) processID() uint64 {
	return b.guard.ProcessId
}

// 玩家上线合并全服邮件
func (b *Box) MergeGlobalMails(public *Box, user User) {
	//l4g.Debugf("mailtest user: %d merge global mail id: public %+v", b.owner, public)
	if public == nil {
		return
	}
	now := time.Now().Unix()
	l4g.Debugf("user: %d merge global mail id: %d", b.owner, b.lastMergedID)
	public.GetMailsGreaterThanID(b.lastMergedID, 0, func(m *cl.Mail) bool {
		if isExpiredMail(m, now) || !CheckGlobalMailConds(m, user) || !CheckGlobalMailOpAndChannel(m, user) {
			return false
		}
		b.add(m.Clone())
		return true
	})
	//l4g.Debugf("mailtest user: %d merge global mail finish: %+v", b.owner, b)
}

type MailOpType uint32

const (
	MailOpRead MailOpType = 1 //阅读邮件
	MailOpDraw MailOpType = 2 //领取邮件奖励
)

// 操作邮件后缩短过期时间
func (b *Box) OpMail(m *cl.Mail, opType MailOpType, now int64) {
	switch opType {
	case MailOpRead:
		if len(m.Awards) > 0 {
			return
		}
	case MailOpDraw:
		break
	default:
		return
	}
	fixExpiredTime := now + goxml.GetData().ConfigInfoM.MailTimeLimit*int64(util.DaySecs)
	if m.ExpiredTime != 0 && m.ExpiredTime < fixExpiredTime {
		return
	}
	m.ExpiredTime = fixExpiredTime
	b.expired.ReplaceOrInsert(newExpiredItem(m))
	b.changes[m.Id] = m
}

func (b *Box) add(m *cl.Mail) bool {
	if _, exist := b.ms[m.Id]; exist {
		return false
	}
	b.ms[m.Id] = m
	b.index.ReplaceOrInsert(newIndexItem(m))
	b.expired.ReplaceOrInsert(newExpiredItem(m))
	b.changes[m.Id] = m
	b.setProcessID(m.Id)
	b.newMails[m.Id] = struct{}{}
	//这里是否要加 存疑
	if TypeGlobal == m.Type && m.Id > b.lastMergedID {
		b.lastMergedID = m.Id
	}
	l4g.Debugf("[mail] user: %d add mail: %+v", b.owner, m)
	return true
}

func (b *Box) Add(m *cl.Mail) {
	b.AddWithOptional(m, false)
}

// group为false, 正常添加新邮件，新邮件ID必然要大于最大值
// group为true，针对登录时候漏加载的群组邮件
func (b *Box) AddWithOptional(m *cl.Mail, group bool) {
	if (!group && m.Id <= b.processID()) || !b.add(m) {
		l4g.Errorf("user %d add mail(%d) failed: max id(%d %d), group(%v)",
			b.owner, m.Type, m.Id, b.processID(), group)
	}
}

// 增量保存邮件
func (b *Box) Save(msg *r2l.OpMails) bool {
	success := false
	changes := len(b.changes)
	if changes > 0 {
		msg.Changes = make([]*cl.Mail, 0, changes)
		for id, k := range b.changes {
			msg.Changes = append(msg.Changes, k.Clone())
			delete(b.changes, id)
		}
		success = true
	}
	deletes := len(b.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint64, 0, deletes)
		for id := range b.deletes {
			msg.Deletes = append(msg.Deletes, id)
			delete(b.deletes, id)
		}
		success = true
	}
	return success
}

// 定时10s删除过期邮件
func (b *Box) Update(now int64) {
	var needDeletes []uint64
	pivot := &expiredItem{
		tm: now + 1,
	}
	b.expired.AscendLessThan(pivot, func(i btree.Item) bool {
		needDeletes = append(needDeletes, i.(*expiredItem).id)
		return true
	})
	b.Delete(needDeletes)

	l4g.Debugf("user: %d mails: %d index: %d expired: %d",
		b.owner, len(b.ms), b.index.Len(), b.expired.Len())
	/*
		b.index.Ascend(func(i btree.Item) bool {
						l4g.Debugf("user: %d mail index: %+v", b.owner, i.(*indexItem))
									return true
											})
													b.expired.Ascend(func(i btree.Item) bool {
																	l4g.Debugf("user: %d mail expired: %+v", b.owner, i.(*expiredItem))
																				return true
																						})
	*/
}

func (b *Box) Get(id uint64) *cl.Mail {
	return b.ms[id]
}

func (b *Box) GetAll() map[uint64]*cl.Mail {
	return b.ms
}

func (b *Box) IsMailBeGmDel(id uint64) bool {
	return util.InUint64s(b.gmDelIds, id)
}

func (b *Box) AddGmDel(mail *cl.Mail) {
	if util.InUint64s(b.gmDelIds, mail.GetId()) {
		l4g.Errorf("AddGmDel: mailId:%d mailGmId:%d repeated ", mail.GetId(), mail.GetGmId())
		return
	}
	b.gmDelIds = append(b.gmDelIds, mail.GetId())
}

// 设置改变标签，save时使用
func (b *Box) Change(ms []*cl.Mail) {
	for _, m := range ms {
		b.changes[m.Id] = m
		if !isNewMail(m) {
			delete(b.newMails, m.Id)
		}
	}
}

// 批量删除邮件
func (b *Box) Delete(ms []uint64) {
	for _, id := range ms {
		delete(b.newMails, id)
		m := b.Get(id)
		if m == nil {
			continue
		}
		delete(b.ms, id)
		b.index.Delete(newIndexItem(m))
		b.expired.Delete(newExpiredItem(m))
		b.deletes[id] = struct{}{}
		l4g.Debugf("[mail] user: %d delete mail: %+v", b.owner, m)
	}
}

// 获取所有id大于参数id的邮件
func (b *Box) GetMailsGreaterThanID(id uint64, maxTimes uint32, cb func(*cl.Mail) bool) {
	getTimes := uint32(0)
	b.index.AscendGreaterOrEqual(&indexItem{id + 1}, func(i btree.Item) bool {
		if maxTimes > 0 && getTimes >= maxTimes {
			return false
		}
		m := b.ms[i.(*indexItem).id]
		if m != nil {
			geted := cb(m)
			if geted {
				getTimes++
			}
		}
		return true
	})
}

func HasRead(m *cl.Mail) bool {
	return m.Flag != uint32(common.MAIL_FLAG_NEW)
}

func HasAwards(m *cl.Mail, now int64) bool {
	return len(m.Awards) > 0 &&
		(m.Flag == uint32(common.MAIL_FLAG_READ) || m.Flag == uint32(common.MAIL_FLAG_NEW)) &&
		!isExpiredMail(m, now)
}

func (b *Box) Clone() map[uint64]*cl.Mail {
	ret := make(map[uint64]*cl.Mail, len(b.ms))
	for id, m := range b.ms {
		ret[id] = m.Clone()
	}
	ret[b.Owner()] = b.guard.Clone()
	return ret
}

func isExpiredMail(m *cl.Mail, now int64) bool {
	return m.ExpiredTime <= now
}

func NewMail(mailID uint32, uniqueID uint64, params []string,
	awards []*cl.Resource, tp, reason uint32, genTime int64) *cl.Mail {
	now := time.Now().Unix()
	lifeTm := goxml.GetData().MailInfoM.GetMailLifeTm(mailID, params)

	return &cl.Mail{
		BaseId:      mailID,
		Id:          uniqueID,
		CreateTime:  now,
		Params:      params,
		Awards:      awards,
		Type:        tp,
		ExpiredTime: now + lifeTm,
		Reason:      reason,
		GenTime:     genTime,
	}
}

func isNewMail(m *cl.Mail) bool {
	return m.Flag == uint32(common.MAIL_FLAG_NEW)
}

// 检查玩家是否有新邮件
func (b *Box) HasNewMail() bool {
	return len(b.newMails) > 0
}

func (b *Box) Count() int {
	return len(b.ms)
}

func (b *Box) MailHasAwards() bool {
	now := time.Now().Unix()
	for _, mail := range b.ms {
		if HasAwards(mail, now) {
			return true
		}
	}
	return false
}

func (b *Box) DelMailAward(delFunc func(mail *cl.Mail) uint32) {
	changeMail := make([]*cl.Mail, 0, 2) //nolint:mnd
	deleteMail := make([]uint64, 0, 2)   //nolint:mnd
	for _, mail := range b.ms {
		ret := delFunc(mail)
		switch ret {
		case uint32(common.DEL_MAIL_TYPE_MAIL_CHANGE):
			changeMail = append(changeMail, mail)
		case uint32(common.DEL_MAIL_TYPE_MAIL_DELETE):
			deleteMail = append(deleteMail, mail.Id)
		case uint32(common.DEL_MAIL_TYPE_MAIL_NO_CHANGE):
		default:
			l4g.Errorf("user:%d del func return error:%d", b.owner, ret)
		}
	}
	if len(changeMail) > 0 {
		b.Change(changeMail)
	}
	if len(deleteMail) > 0 {
		b.Delete(deleteMail)
	}
}
