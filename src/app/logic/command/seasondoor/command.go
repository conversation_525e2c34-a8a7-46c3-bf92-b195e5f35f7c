package seasondoor

import (
	"app/logic/command/base"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorGetData), &C2LSeasonDoorGetDataCommand{}, state)
	//cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorPassFight), &C2LSeasonDoorPassFightCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorFightLine), &C2LSeasonDoorFightLineCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorFight), &C2LSeasonDoorFightCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorFightLineViewReward), &C2LSeasonDoorFightLineViewRewardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorFightLineReward), &C2LSeasonDoorFightLineRewardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorTaskReward), &C2LSeasonDoorTaskRewardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonDoorLog), &C2LSeasonDoorLogCommand{}, state)
}

type C2LSeasonDoorFightCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorFightCommand) Return(msg *cl.L2C_SeasonDoorFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorFight, msg)
	return true
}

func (c *C2LSeasonDoorFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonDoorFight: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorFight{
		Id: cmsg.Id,
	}
	result := c.User.SeasonDoor().Fight(c.Srv, cmsg, smsg)
	return c.Return(smsg, result)
}

type C2LSeasonDoorFightLineRewardCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorFightLineRewardCommand) Return(msg *cl.L2C_SeasonDoorFightLineReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorFightLineReward, msg)
	return true
}

func (c *C2LSeasonDoorFightLineRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorFightLineReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorFightLineReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonDoorFightLineReward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorFightLineReward{
		Id: cmsg.Id,
	}
	result := c.User.SeasonDoor().FightLineReward(c.Srv, cmsg, smsg)
	return c.Return(smsg, result)
}

type C2LSeasonDoorLogCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorLogCommand) Return(msg *cl.L2C_SeasonDoorLog, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorLog, msg)
	return true
}

func (c *C2LSeasonDoorLogCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorLog{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorLog Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonDoorLog: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorLog{}
	result := c.User.SeasonDoor().Log(c.Srv, cmsg, smsg)
	return c.Return(smsg, result)
}

type C2LSeasonDoorTaskRewardCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorTaskRewardCommand) Return(msg *cl.L2C_SeasonDoorTaskReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorTaskReward, msg)
	return true
}

func (c *C2LSeasonDoorTaskRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorTaskReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorTaskReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonDoorTaskReward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorTaskReward{}
	result := c.User.SeasonDoor().TaskReward(c.Srv, cmsg, smsg)
	return c.Return(smsg, result)
}

type C2LSeasonDoorFightLineCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorFightLineCommand) Return(msg *cl.L2C_SeasonDoorFightLine, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorFightLine, msg)
	return true
}

func (c *C2LSeasonDoorFightLineCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorFightLine{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorFightLine Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonDoorFightLine: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorFightLine{
		Id:        cmsg.Id,
		OilOpType: cmsg.OilOpType,
		OilId:     cmsg.OilId,
		OilNum:    cmsg.OilNum,
	}
	result := c.User.SeasonDoor().FightLine(c.Srv, cmsg, smsg)
	return c.Return(smsg, result)
}

type C2LSeasonDoorGetDataCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorGetDataCommand) Return(msg *cl.L2C_SeasonDoorGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorGetData, msg)
	return true
}

func (c *C2LSeasonDoorGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonDoorGetData: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorGetData{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_DOOR), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonDoorGetData: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	if c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_SEASON_DOOR_1)) == nil {
		if !c.User.FormationManager().InitFormationByExistOne(c.Srv, uint32(common.FORMATION_ID_FI_DUNGEON),
			uint32(common.FORMATION_ID_FI_SEASON_DOOR_1), 1) {
			l4g.Errorf("init formation failed. uid %d", c.Msg.UID)
			return c.Return(smsg, uint32(ret.RET_ERROR))
		}
	}

	c.User.SeasonDoor().FightLineGetData(c.Srv, smsg)

	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonDoorFightLineViewRewardCommand struct {
	base.UserCommand
}

func (c *C2LSeasonDoorFightLineViewRewardCommand) Return(msg *cl.L2C_SeasonDoorFightLineViewReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorFightLineViewReward, msg)
	return true
}

func (c *C2LSeasonDoorFightLineViewRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonDoorFightLineViewReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonDoorFightLineViewReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonDoorFightLineViewReward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonDoorFightLineViewReward{
		Id: cmsg.Id,
	}
	result := c.User.SeasonDoor().FightLineViewReward(c.Srv, cmsg, smsg)
	return c.Return(smsg, result)
}

//type C2LSeasonDoorPassFightCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LSeasonDoorPassFightCommand) Return(msg *cl.L2C_SeasonDoorPassFight, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorPassFight, msg)
//	return true
//}
//
//func (c *C2LSeasonDoorPassFightCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_SeasonDoorPassFight{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_SeasonDoorPassFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//	l4g.Debugf("user: %d C2L_SeasonDoorPassFight: cmsg:%s", c.Msg.UID, cmsg)
//
//	smsg := &cl.L2C_SeasonDoorPassFight{
//		Id: cmsg.Id,
//	}
//	result := c.User.SeasonDoor().PassFight(c.Srv, cmsg, smsg)
//	return c.Return(smsg, result)
//}
