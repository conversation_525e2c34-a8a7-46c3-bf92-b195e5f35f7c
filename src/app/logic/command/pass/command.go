package pass

import (
	"app/logic/command"
	"context"
	"math"
	"slices"

	"gitlab.qdream.com/kit/sea/time"

	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_PassGetData), &C2LPassGetDataCommand{}, state)           //获取战令信息
	cmds.Register(uint32(cl.ID_MSG_C2L_PassReceiveAward), &C2LPassReceiveAwardCommand{}, state) //获取奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_PassLevelBuy), &C2LPassLevelBuyCommand{}, state)         //等级购买
}

type C2LPassGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LPassGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPassGetDataCommand) Error(msg *cl.L2C_PassGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PassGetData, msg)
	return false
}

func (c *C2LPassGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PassGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PassGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_PassGetData: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_PassGetData{
		Ret:       uint32(cret.RET_OK),
		PassDatas: make([]*cl.PassData, 0, len(cmsg.SysIds)),
		SysIds:    slices.Clone(cmsg.SysIds),
	}

	if len(cmsg.SysIds) == 0 || len(cmsg.SysIds) > goxml.GetData().PassInfoM.Len() {
		l4g.Errorf("user :%d C2L_PassGetData: id num %d is more than xml len,", c.Msg.UID, len(cmsg.SysIds))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	c.User.Pass().CheckCyclePassOpen(c.Srv)
	checkRepeat := make(map[uint32]struct{}, len(cmsg.SysIds))
	for _, id := range cmsg.SysIds {
		if _, ok := checkRepeat[id]; ok {
			l4g.Errorf("user:%d C2LPassGetData: id repeated. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		checkRepeat[id] = struct{}{}
		c.User.Pass().CheckAndInitLoginPass(id, c.Srv)
		pass := c.User.Pass().Flush(id)
		if pass != nil {
			smsg.PassDatas = append(smsg.PassDatas, pass)
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PassGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPassReceiveAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LPassReceiveAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPassReceiveAwardCommand) Error(msg *cl.L2C_PassReceiveAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PassReceiveAward, msg)
	return false
}

func (c *C2LPassReceiveAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PassReceiveAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PassReceiveAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_PassReceiveAward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_PassReceiveAward{
		Ret:          uint32(cret.RET_OK),
		Ids:          cmsg.Ids,
		FreeAwards:   make([]*cl.Resource, 0, len(cmsg.Ids)),
		ChargeAwards: make([]*cl.Resource, 0, len(cmsg.Ids)*2), //nolint:mnd
		SysId:        cmsg.SysId,
	}

	if len(cmsg.Ids) > len(goxml.GetData().PassTaskInfoM.Datas) || len(cmsg.Ids) == 0 {
		l4g.Errorf("user: %d C2L_PassReceiveAward: ids num illegal. num:%d", c.Msg.UID, len(cmsg.Ids))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	passM := c.User.Pass()
	if !passM.IsOpen(c.Srv, cmsg.SysId) {
		l4g.Errorf("user: %d C2L_PassReceiveAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	pass := passM.GetPass(cmsg.SysId)
	if pass == nil {
		l4g.Errorf("user: %d C2L_PassReceiveAward: sys id:%d pass data not init", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_PASS_SYSTEM_ID_NOT_INIT))
	}

	passInfo := goxml.GetData().PassInfoM.GetRecordById(cmsg.SysId)
	if passInfo == nil {
		l4g.Errorf("user: %d C2L_PassReceiveAward: no passInfo, sys id %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 检查前置战令是否已经完成
	isFinished, preId := passM.GetPreviousPassFinished(passInfo.PreRestriction)
	if !isFinished {
		l4g.Errorf("user: %d C2L_PassReceiveAward: sys id:%d previous pass %d not finished", c.Msg.UID, cmsg.SysId, preId)
		return c.Error(smsg, uint32(cret.RET_PASS_PREVIOUS_NOT_FINISHED))
	}

	checkRepeat := make(map[uint32]struct{})
	for _, id := range cmsg.Ids {
		if _, ok := checkRepeat[id]; ok {
			l4g.Errorf("user:%d C2L_PassReceiveAward: id repeated. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		checkRepeat[id] = struct{}{}
		passTaskInfo := goxml.GetData().PassTaskInfoM.Index(id)
		if passTaskInfo == nil {
			l4g.Errorf("user:%d C2L_PassReceiveAward: id error. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		if passTaskInfo.PassId != cmsg.SysId {
			l4g.Errorf("user:%d C2L_PassReceiveAward: task not match. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_PASS_TASK_NOT_MATCH))
		}
		// 任务奖励已领取过
		if !pass.CanReceivedAward(id) {
			l4g.Errorf("user:%d C2L_PassReceiveAward: task award already received. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_PASS_TASK_AWARD_REPEAT_RECEIVE))
		}
		if ret := c.isTaskFinish(pass, passTaskInfo.TypeId, passTaskInfo.Value); ret != cret.RET_OK {
			l4g.Errorf("user:%d C2L_PassReceiveAward: task not finish. id: %d", c.Msg.UID, id)
			return c.Error(smsg, uint32(ret))
		}

		freeAward, chargeAward := pass.GetTaskAward(passTaskInfo)
		smsg.FreeAwards = append(smsg.FreeAwards, freeAward...)
		smsg.ChargeAwards = append(smsg.ChargeAwards, chargeAward...)
	}

	if smsg.Ret = c.sendAward(smsg.FreeAwards, smsg.ChargeAwards); smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PassReceiveAward send reward failed retCode:%d", c.User.ID(), smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.UpdateState = pass.SetReceiveState(cmsg.Ids)
	passM.Save(cmsg.SysId)
	c.User.LogPassReceiveAward(c.Srv, cmsg.SysId, cmsg.Ids, uint64(util.If(pass.Buy, 0, 1)))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PassReceiveAward, smsg)
	return c.ResultOK(smsg.Ret)
}

// 检查任务是否已完成
func (c *C2LPassReceiveAwardCommand) isTaskFinish(pass *character.PassData, typeID, value uint32) cret.RET {
	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(typeID)
	if taskTypeInfo == nil {
		l4g.Errorf("user:%d C2L_PassReceiveAward: taskTypeInfo not exist. typeID:%d", c.Msg.UID, typeID)
		return cret.RET_SYSTEM_DATA_ERROR
	}

	progress := pass.GetTaskProgress(taskTypeInfo, c.User)
	if !c.User.CheckTaskFinish(progress, typeID, uint64(value)) {
		return cret.RET_PASS_TASK_PROGRESS_NOT_FINISH
	}

	return cret.RET_OK
}

func (c *C2LPassReceiveAwardCommand) sendAward(freeAwards, chargeAwards []*cl.Resource) uint32 {
	award := make([]*cl.Resource, 0, len(freeAwards)+len(chargeAwards))
	award = append(award, freeAwards...)
	award = append(award, chargeAwards...)
	ret, _ := c.User.Award(c.Srv, award, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_PASS_RECEIVE), 0)

	return ret
}

type C2LPassLevelBuyCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LPassLevelBuyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPassLevelBuyCommand) Error(msg *cl.L2C_PassLevelBuy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PassLevelBuy, msg)
	return false
}

func (c *C2LPassLevelBuyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PassLevelBuy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PassLevelBuy Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_PassLevelBuy: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_PassLevelBuy{
		Ret:     uint32(cret.RET_OK),
		SysId:   cmsg.SysId,
		TaskIds: cmsg.TaskIds,
	}
	passM := c.User.Pass()
	if !passM.IsOpen(c.Srv, cmsg.SysId) {
		l4g.Errorf("user: %d C2L_PassLevelBuy: function not open. passID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if uint32(len(cmsg.TaskIds)) > goxml.GetData().PassTaskInfoM.GetTaskNum(cmsg.SysId) {
		l4g.Errorf("user: %d C2L_PassLevelBuy: task num not match. passID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	pass := passM.GetPass(cmsg.SysId)
	if pass == nil || (!pass.Buy && !pass.Buy2) {
		l4g.Errorf("user: %d C2L_PassLevelBuy: not recharge. passID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_PASS_ACTIVE_NOT_RECHARGE))
	}
	passInfo := goxml.GetData().PassInfoM.Index(cmsg.SysId)
	if passInfo == nil {
		l4g.Errorf("user: %d C2L_PassLevelBuy: passInfo not exist. passID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !passTypeCanBuyLevel(passInfo) {
		l4g.Errorf("user: %d C2L_PassLevelBuy: passInfo passID:%d type:%d error", c.Msg.UID, cmsg.SysId, passInfo.PassType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 周期剩余天数 不可以大于 等级购买解锁天数
	if !isValidByLeftCycleDay(c.Srv, passInfo, c.User) {
		l4g.Errorf("user: %d C2L_PassLevelBuy: left cycle day not match. passID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	ret, pointMap := checkTaskAndRetPoint(pass, cmsg.TaskIds, c.Msg.UID)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PassLevelBuy: task not match. passID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, ret)
	}

	cost := calcCostRes(pointMap, passInfo.BuylevelRatio)
	smsg.Ret = c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_PASS_LEVEL_BUY), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PassLevelBuy: cost resource failed. retCode:%d", c.User.ID(), smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.TaskProgress = pass.UpdateTaskProgress(pointMap)
	passM.Save(cmsg.SysId)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PassLevelBuy, smsg)
	c.User.LogPassLevelBuy(c.Srv, cmsg.SysId, cmsg.TaskIds)

	return c.ResultOK(smsg.Ret)
}

func passTypeCanBuyLevel(passInfo *goxml.PassInfoExt) bool {
	if passInfo == nil {
		return false
	}
	if passInfo.PassType == uint32(goxml.PassTypeCycle) || passInfo.PassType == uint32(goxml.PassTypeSeason) ||
		passInfo.PassType == uint32(goxml.PassTypeActivity) || passInfo.PassType == uint32(goxml.PassTypeCreateU) ||
		passInfo.PassType == uint32(goxml.PassTypeSkin) || passInfo.PassType == uint32(goxml.PassTypeSeasonWeek) {
		return true
	}
	return false
}

// 剩余的周期天数是否有效
func isValidByLeftCycleDay(srv command.Servicer, passInfo *goxml.PassInfoExt, u *character.User) bool {
	switch passInfo.PassType {
	case uint32(goxml.PassTypeCycle):
		return getCycleLeftDay(srv, passInfo)
	case uint32(goxml.PassTypeSeason):
		return getSeasonPassLeftDay(srv, passInfo)
	case uint32(goxml.PassTypeActivity):
		return getActivityLeftDay(srv, passInfo)
	case uint32(goxml.PassTypeCreateU):
		return getCreateULeftDay(srv, passInfo, u)
	case uint32(goxml.PassTypeSkin):
		return getActivityLeftDay(srv, passInfo)
	case uint32(goxml.PassTypeSeasonWeek):
		return getSeasonWeekPassLeftDay(srv, passInfo)
	default:
		return false
	}
}

// 获取周期剩余的天数
func getCycleLeftDay(srv command.Servicer, passInfo *goxml.PassInfoExt) bool {
	if passInfo == nil {
		return false
	}

	pastDay := (srv.ServerDay(time.Now().Unix()) - passInfo.UnlockDay) % passInfo.Cycle
	if pastDay == 0 {
		return 1 > passInfo.BuylevelUnlockDay
	}

	return (passInfo.Cycle - pastDay) <= passInfo.BuylevelUnlockDay
}

func getSeasonPassLeftDay(_ command.Servicer, passInfo *goxml.PassInfoExt) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()
	if now > passInfo.EndDay {
		return false
	}

	leftDay := util.DaysBetweenTimes(now, passInfo.EndDay)
	return leftDay <= passInfo.BuylevelUnlockDay
}

func getSeasonWeekPassLeftDay(_ command.Servicer, passInfo *goxml.PassInfoExt) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()
	if now > passInfo.EndDay {
		return false
	}

	leftDay := util.DaysBetweenTimes(now, passInfo.EndDay)
	return leftDay <= passInfo.BuylevelUnlockDay
}

func getActivityLeftDay(_ command.Servicer, passInfo *goxml.PassInfoExt) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()
	if now > passInfo.EndDay {
		return false
	}

	leftDay := util.DaysBetweenTimes(now, passInfo.EndDay)
	return leftDay <= passInfo.BuylevelUnlockDay
}

func getCreateULeftDay(_ command.Servicer, passInfo *goxml.PassInfoExt, u *character.User) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()

	createDay := u.CreateDay(now)
	if createDay >= passInfo.CloseDay {
		return false
	}

	return passInfo.CloseDay-createDay <= passInfo.BuylevelUnlockDay
}

func checkTaskAndRetPoint(pass *character.PassData, taskIDs []uint32, userID uint64) (uint32, map[uint32]uint64) {
	isRepeat := make(map[uint32]struct{}, len(taskIDs))
	pointMap := make(map[uint32]uint64, len(taskIDs))
	for _, taskID := range taskIDs {
		if _, exist := isRepeat[taskID]; exist {
			l4g.Errorf("user: %d C2L_PassLevelBuy: task is repeat. taskID: %d", userID, taskID)
			return uint32(cret.RET_REPEATED_PARAM), nil
		}
		isRepeat[taskID] = struct{}{}
		taskInfo := goxml.GetData().PassTaskInfoM.Index(taskID)
		if taskInfo == nil {
			l4g.Errorf("user: %d C2L_PassLevelBuy: taskInfo is nil. taskID: %d", userID, taskID)
			return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
		}
		if taskInfo.PassId != pass.SysId {
			l4g.Errorf("user: %d C2L_PassLevelBuy: task's passID not match. taskID: %d", userID, taskID)
			return uint32(cret.RET_PASS_TASK_NOT_MATCH), nil
		}

		progress := pass.TaskProgress[taskInfo.TypeId]
		if progress == nil {
			pointMap[taskInfo.TypeId] = uint64(taskInfo.Value)
		} else {
			if progress.Progress >= uint64(taskInfo.Value) {
				l4g.Errorf("user: %d C2L_PassLevelBuy: task is finish. taskID: %d", userID, taskID)
				return uint32(cret.RET_PASS_ACTIVE_TASK_FINISH), nil
			}
			pointMap[taskInfo.TypeId] = uint64(taskInfo.Value) - progress.Progress
		}
	}

	return uint32(cret.RET_OK), pointMap
}

func calcCostRes(pointMap map[uint32]uint64, ratio uint32) []*cl.Resource {
	var totalPoint uint64
	for _, point := range pointMap {
		totalPoint += point
	}
	num := math.Ceil(float64(totalPoint) * float64(ratio) / goxml.PassBastFloat)
	if num > 0 {
		return []*cl.Resource{goxml.GenSimpleResource(uint32(common.RESOURCE_DIAMOND), 0, uint32(num))}
	}

	return nil
}
