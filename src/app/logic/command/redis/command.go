package redis

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/arena"
	"app/logic/activity/ban"
	"app/logic/activity/flower"
	"app/logic/activity/gst"
	"app/protos/in/l2c"
	"app/version"

	// "app/logic/activity/flower"

	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command/base"
	"app/logic/db"
	aevent "app/logic/event"
	"context"

	//"app/logic/event"
	rdb "app/protos/in/db"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cg"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(r2l.ID_MSG_R2L_Login), &R2LLoginCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_Create), &R2LCreateCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetUserSnapshot), &R2LGetUserSnapshotCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetUserBattleData), &R2LGetUserBattleDataCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GroupMail), &R2LGroupMailCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetArenaLog), &R2LGetArenaLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_SetName), &R2LSetNameCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_SaveUserBattleSnapshot), &R2LSaveUserBattleSnapshotCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetUserBattleSnapshot), &R2LGetUserBattleSnapshotCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_LoadGuild), &R2LLoadGuildCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildDungeonLog), &R2LGetGuildDungeonLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildLog), &R2LGetGuildLogCommand{}, state)
	//cmds.Register(uint32(r2l.ID_MSG_R2L_GetPrivateMsg), &R2LGetPrivateMsgCommand{}, state)
	//cmds.Register(uint32(r2l.ID_MSG_R2L_SavePrivateMsg), &R2LSavePrivateMsgCommand{}, state)
	//cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildMsg), &R2LGetGuildMsgCommand{}, state)
	//cmds.Register(uint32(r2l.ID_MSG_R2L_GetPrivateMessageNum), &R2LGetPrivateMessageNumCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_SaveArenaLastSeasonTop), &R2LSaveArenaLastSeasonTopCommand{}, state)
	//cmds.Register(uint32(r2l.ID_MSG_R2L_GetChatLikeList), &R2LGetChatLikeListCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetDBUser), &R2LGetDBUserCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetWrestleLog), &R2LGetWrestleLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildDungeonMessageBoardLog), &R2LGetGuildDungeonMessageBoardLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_LoadGuildUserForReset), &R2LLoadGuildUserForResetCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetFlowerSnatchLog), &R2LGetFlowerSnatchLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetFlowerOccupyLog), &R2LGetFlowerOccupyLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildDonateLog), &R2LGetGuildDonateLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildMemberInfo), &R2LGetGuildMemberInfoCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGuildUsers), &R2LGetGuildUsersCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetSeasonArenaLog), &R2LGetSeasonArenaLogCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GetGstChallengeLog), &R2LGetGstChallengeLogCommand{}, state)

	//about grpc
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_Login), &R2LGRPC_LoginCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_GetUserID), &R2LGRPC_GetUserIDCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ChangeDungeonID), &R2LGRPCChangeDungeonIDCommand{}, state)                // 设置主线关卡ID
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_GuidanceClose), &R2LGRPCGuidanceClosCommand{}, state)                     // 关闭新手引导
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetUserLevel), &R2LGRPCSetUserLevelCommand{}, state)                      // 修改玩家等级
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ClearUserResource), &R2LGRPCClearUserResourceCommand{}, state)            // 清除玩家资源
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ActivityInfo), &R2LGRPCActivityInfoCommand{}, state)                      // 清除玩家资源
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetDailyNum), &R2LGRPCResetDailyNumCommand{}, state)                    // 重置各玩法次数回到初始状态
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetMedalLevel), &R2LGRPCSetMedalLevelCommand{}, state)                    // 修改功勋等级
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetMaze), &R2LGRPCResetMazeCommand{}, state)                            // 重置迷宫
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetGuildQuitTm), &R2LGRPCResetGuildQuitTmCommand{}, state)              // 重置退出公会时间
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetTowerFloor), &R2LGRPCResetTowerFloorCommand{}, state)                // 重置Tower floor
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetTrialLevel), &R2LGRPCResetTrialLevelCommand{}, state)                // 重置Trial level
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetMirageFloor), &R2LGRPCResetMirageFloorCommand{}, state)              // 重置Mirage Floor
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetScore), &R2LGRPCResetScoreCommand{}, state)                          // 重置日常、周常、嘉年华积分
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetTaleChapterFinish), &R2LGRPCResetTaleChapterFinishCommand{}, state)  // 设置列传章节完成
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetTaleElite), &R2LGRPCResetTaleEliteCommand{}, state)                  // 设置列传强敌挑战
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetUserResource), &R2LGRPCSetUserResourceCommand{}, state)                // 设置玩家资源
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_AddOrder), &R2LGRPC_AddOrderCommand{}, state)                             // 增加充值订单
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_AddWaitRefundOrder), &R2LGRPC_AddWaitRefundOrderCommand{}, state)         // 新增待退款订单
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetTowerstarDungeonID), &R2LGRPC_SetTowerstarDungeonIDCommand{}, state)   // 设置条件爬塔关卡
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ResetTowerstar), &R2LGRPC_ResetTowerstarCommand{}, state)                 // 重置条件爬塔
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetQuestionnaireFinish), &R2LGRPC_SetQuestionnaireFinishCommand{}, state) // 设置问卷完成                // 重置条件爬塔
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_GetRechargeList), &R2LGRPCGetRechargeListCommand{}, state)                // 获取充值信息
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_CloseGuidance), &R2LGRPCCloseGuidanceCommand{}, state)                    // 跳过新手引导组
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetMazeTaskLevel), &R2LGRPCSetMazeTaskLevelCommand{}, state)              // 设置迷宫任务等级
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetAccountTag), &R2LGRPCSetAccountTagCommand{}, state)                    // 设置账号标识
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ImportUser), &R2LGRPCImportUserCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_DelChatGroupTag), &R2LGRPCDelChatGroupTagCommand{}, state)       // 删除聊天groupTag
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_DeleteCurrencies), &R2LGRPCDeleteCurrenciesCommand{}, state)     // //删除玩家货币
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetDailyAttendance), &R2LGRPCSetDailyAttendanceCommand{}, state) // 累登
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetDisorderLand), &R2LGRPCSetDisorderLandCommand{}, state)       // 失序空间
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_ChangeBags), &R2LGRPCChangeBagsCommand{}, state)
	cmds.Register(uint32(r2l.ID_MSG_R2L_GRPC_SetSeasonLink), &R2LGRPCSetSeasonLinkCommand{}, state)
}

type BaseCommand struct {
	msg *db.RedisRespone
}

func (b *BaseCommand) OnPause(ctx context.Context, msg parse.Messager) {
}

func (b *BaseCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	b.msg = msg.(*db.RedisRespone)
	return true
}

func (b *BaseCommand) OnAfter(ctx context.Context, result bool) {
}

/*************************************************/

type R2LLoginCommand struct {
	BaseCommand
}

//nolint:funlen
func (r *R2LLoginCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_Login)
	l4g.Debugf("R2L_Login, %s", rmsg)

	srv := base.GetService(ctx)
	userm := srv.UserM()
	uid := r.msg.UID

	user := userm.GetUserByCookie(uid)
	if user == nil {
		l4g.Errorf("Login fail, %d", uid)
		return false
	}
	cmsg := &cg.G2C_Login{
		Ret: rmsg.Ret,
	}

	if cmsg.Ret == uint32(cg.RET_USER_NOT_EXIST) {
		base.TraceLog(srv, user, uid, uint32(r2l.ID_MSG_R2L_Login), 0, cmsg)
		//自动创角
		srv.RedisActor().AddMessage(uint32(r2l.ID_MSG_L2R_Create), user.Cookie(), &r2l.L2R_Create{
			Uuid:     user.UUID(),
			ServerId: user.ServerID(),
			Name:     "",
			Id:       srv.CreateUniqueID(),
			BaseId:   goxml.DefaultBaseId,
			Level:    1,
			OpId:     user.OpID(),
			Channel:  user.Channel(),
			Ip:       user.NetAddr(),
			DeviceId: user.DeviceID(),
			Pid:      user.Pid(),
			Gid:      user.Gid(),
		})
		user.SetState(character.StateCreate)
		return true
	} else if cmsg.Ret == uint32(cg.RET_OK) {
		//增加登录异常检测
		if exist := userm.GetUserFromCache(rmsg.User.Uuid, rmsg.User.ServerId); exist != nil {
			l4g.Errorf("login exception: user in cache: %d (%s %d)",
				uid, rmsg.User.Uuid, rmsg.User.ServerId)
			cmsg.Ret = uint32(ret.RET_ERROR)
			user.SendCmdToGateway(cl.ID(cg.ID_MSG_G2C_Login), cmsg)
			return false
		}

		//检查玩家是否被封禁
		banAccountM := srv.GetActivity(activity.BanAccount).(*ban.Manager)
		if banAccountM.Check(rmsg.User.Id, uint32(common.BAN_TYPE_LOGIN)) {
			cmsg.Ret = uint32(ret.RET_BAN_ACCOUNT)
			user.SendCmdToGateway(cl.ID(cg.ID_MSG_G2C_Login), cmsg)
			return false
		}
		if banAccountM.Check(rmsg.User.Id, uint32(common.BAN_TYPE_LOGIN_TEMPORARY)) {
			cmsg.Ret = uint32(ret.RET_BAN_ACCOUNT_TEMPORARY)
			user.SendCmdToGateway(cl.ID(cg.ID_MSG_G2C_Login), cmsg)
			return false
		}

		cmsg.Ret = userm.UserOnline(user, rmsg, srv)
		if cmsg.Ret == uint32(cg.RET_OK) {
			cmsg.Uid = user.ID()
			cmsg.Version = version.GetVersion()
			cmsg.Sync = 0
			id := srv.CreateUniqueID()
			cmsg.SyncCookie = id
			user.InitMessageSync(id)
			l4g.Infof("login user success, %d %s", user.ID(), user.Name())
			//玩家上线成功触发事件
			user.OnlineSuccess(srv)

			guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)

			if rmsg.GuildUser != nil {
				//加载自己的公会信息
				guildM.LoadMyGuildUser(rmsg.GuildUser)
			}

			if gu := guildM.GetGuildUser(user.ID()); gu != nil {
				//guildM.TryLoadGuildDataOfUser(gu.GuildID(), srv)
				if gu.GetNeedClearGstItem() {
					user.CalcQuitGuildResource(srv)
					user.DelMailsForQuitGuild()
					gu.SetNeedClearGstItemFalse()
					guildM.SaveGuildUser(gu)
				}
				guild := guildM.GetGuildByUser(user.ID())
				if guild != nil {
					user.UserGuild().Update(guild.ID(), guild.Name())
					//guild.CandidateLeader.CheckAndUpdate(guild, user) // 检查更新会长候选人
				}
			}

			//密林 - 0.9.8
			flowerM := srv.GetActivity(activity.Flower).(*flower.Manager)
			flower := flowerM.Get(user.ID())
			l4g.Debugf("login debug. uuid:%s flower:%v flowerData:%v", user.Name(), flower, rmsg.Flower)
			if flower != nil {
				//检查更新战力
				flowerM.CheckUpdatePower(srv, flower, user.MaxPower(), true)
			} else if rmsg.Flower != nil {
				//加载个人数据
				flowerM.LoadOne(rmsg.Flower)
			}

			//竞技场 - 更新玩家防守战力
			arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
			arenaUser := arenaM.Get(user.ID())
			if arenaUser != nil {
				arenaUser.CheckUpdatePower(user.ArenaDefensePower(), true)
				arenaM.SetChange(srv, arenaUser)
			}

			//v2.16.5-cn水晶重构后，检查同步赛季战力
			user.SyncSeasonPower(srv)
		}
	}
	flags := uint32(0)
	if srv.EnableCrypto() && goxml.GetData().XorKeyM.NewSize > 0 {
		flags = parse.MsgFlagCrypto
	}
	l4g.Debugf("crypto: %d flags: %d enable: %+v key size: %d", uid, flags, srv.EnableCrypto(), goxml.GetData().XorKeyM.NewSize)
	user.SendLoginCmdToGateway(flags, cmsg)
	return cmsg.Ret == uint32(cg.RET_OK)
}

type R2LCreateCommand struct {
	BaseCommand
}

func (r *R2LCreateCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_Create)
	l4g.Debugf("R2L_Create, %s", rmsg)

	srv := base.GetService(ctx)
	userm := srv.UserM()
	uid := r.msg.UID

	user := userm.GetUserByCookie(uid)
	if user == nil {
		l4g.Errorf("Create fail, uid: %d ret: %d ", uid, rmsg.Ret)
		return false
	}

	cmsg := &cg.G2C_Create{
		Ret: rmsg.Ret,
	}
	if cmsg.Ret == uint32(cg.RET_OK) {
		cmsg.Ret = userm.UserOnline(user, rmsg, srv)
		if cmsg.Ret == uint32(cg.RET_OK) {
			cmsg.Uid = user.ID()
			cmsg.Sync = 0
			id := srv.CreateUniqueID()
			cmsg.SyncCookie = id
			user.InitMessageSync(id)
			l4g.Infof("create user success, %d %s", user.ID(), user.Name())
			//测试数据
			/*
				if  goxml.GetData().ServerInfoM.IsTest() {
					user.InitTest(srv)
				}
			*/
			//触发创角成功之后的事件
			user.CreateSuccess(srv)
		}
	}

	smsg := &cg.G2C_Login{
		Ret:        cmsg.Ret,
		Uid:        cmsg.Uid,
		Version:    version.GetVersion(),
		SyncCookie: cmsg.SyncCookie,
		Sync:       cmsg.Sync,
	}

	flags := uint32(0)
	if srv.EnableCrypto() && goxml.GetData().XorKeyM.NewSize > 0 {
		flags = parse.MsgFlagCrypto
	}
	l4g.Debugf("crypto: %d flags: %d enable: %+v key size: %d", uid, flags, srv.EnableCrypto(), goxml.GetData().XorKeyM.NewSize)

	user.SendLoginCmdToGateway(flags, smsg)
	return rmsg.Ret == uint32(cg.RET_OK)
}

type R2LGetUserSnapshotCommand struct {
	BaseCommand
}

func (r *R2LGetUserSnapshotCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetUserSnapshot)
	l4g.Debugf("R2L_GetUserSnapshot, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	data := amsg.Param.([]*cl.UserSnapshot)
	guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)
	for _, user := range rmsg.Users {
		snap := character.NewUserSnapshot(user, rmsg.FormationId, srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)))
		guild := guildM.GetGuildByUser(user.Id)
		if guild != nil {
			snap.GuildId, snap.GuildName = guild.ID(), guild.Name()
		}
		data = append(data, snap)
	}
	ret := amsg.Work(rmsg.Ret, data)
	return ret
}

type R2LGetUserBattleDataCommand struct {
	BaseCommand
}

func (r *R2LGetUserBattleDataCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetUserBattleData)
	l4g.Debugf("R2L_GetUserBattleData, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	data := amsg.Param.([]*rdb.UserBattleData)
	guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)
	userM := srv.UserM()
	for _, user := range rmsg.Users {
		if user == nil || user.User == nil {
			continue
		}
		guild := guildM.GetGuildByUser(user.User.Id)
		if guild != nil {
			user.GuildMsg = &rdb.GuildMsgForBattle{
				Id:   guild.ID(),
				Name: guild.Name(),
			}
		}
		user.AreaId = srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))
		userM.GetBattleDataCache().AddBattleUser(user.User.Id, rmsg.FormationId, user, userM.GetCacheExpiredTime())
		if rmsg.OnlyPower {
			user = character.NewUBDOnlyPowerFromUBD(srv, user, rmsg.FormationId)
		}
		data = append(data, user)
	}
	l4g.Infof("DoUserBattleData do callback from cmd:%d", amsg.CallCmd)
	ret := amsg.Work(rmsg.Ret, data)
	return ret
}

type R2LGroupMailCommand struct {
	BaseCommand
}

func (r *R2LGroupMailCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GroupMail)
	l4g.Debugf("R2L_GroupMail, %s", rmsg)
	base.GetService(ctx).UserM().RemoveGroupMail(rmsg.Data.Id)
	return true
}

type R2LGetArenaLogCommand struct {
	BaseCommand
}

func (r *R2LGetArenaLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetArenaLog)
	l4g.Debugf("R2L_GetArenaLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LSetNameCommand struct {
	BaseCommand
}

func (r *R2LSetNameCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_SetName)
	l4g.Debugf("R2L_SetName, %s", rmsg)

	srv := base.GetService(ctx)
	userm := srv.UserM()
	uid := rmsg.Id
	var user *character.User
	if user = userm.GetUserWithCache(uid); user == nil {
		l4g.Errorf("R2L_SetName:%d not online", uid)
		return false
	}
	user.UnlockSetName()
	smsg := &cl.L2C_SetName{
		Ret:  uint32(ret.RET_OK),
		Name: rmsg.NewName,
	}

	if rmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("R2L_SetName name error: uid:%d ret:%d", uid, rmsg.Ret)
		smsg.Ret = rmsg.Ret
		user.SendCmdToGateway(cl.ID_MSG_L2C_SetName, smsg)
		return true
	}
	var costs []*cl.Resource
	if user.Name() != "" {
		costs = append(costs, &cl.Resource{
			Type:  uint32(common.RESOURCE_DIAMOND),
			Value: 0,
			Count: goxml.GetData().ConfigInfoM.UserNameSetCost,
		})
	}
	if len(costs) > 0 {
		if nret := user.Consume(srv, costs, uint32(log.RESOURCE_CHANGE_REASON_SET_NAME), 0); nret != uint32(ret.RET_OK) {
			//记录错误不中断
			l4g.Errorf("R2L_SetName name num consume error: uid:%d ret:%d", uid, nret)
		}
	}
	user.SetName(rmsg.NewName)
	user.LogUserInfo(srv)
	guildM, ok := srv.GetActivity(activity.Guild).(*aguild.Manager)
	if ok {
		guildUser := guildM.GetGuildUser(user.ID())
		if guildUser != nil {
			guildUser.SetName(rmsg.NewName)
			guildM.SaveGuildUser(guildUser)
		}
	}
	user.FireCommonEvent(srv.EventM(), aevent.IeSetName, 0)

	gstM, ok := srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		return true
	}
	needSync := gstM.NeedSyncUserBaseInfo(user.ID(), true)
	if needSync {
		if gstM.IsCrossConnected() {
			syncInfo := user.CreateGstUserBaseInfo()
			if !srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSYNCGuildUserInfo, user.ID(), syncInfo) {
				l4g.Errorf("user: %d ID_MSG_L2CS_GSTSYNCGuildUserInfo: cross maintain", user.ID())
			}
		}
	}

	user.SendSelfToClient()
	user.SendCmdToGateway(cl.ID_MSG_L2C_SetName, smsg)
	user.LogSetName(srv, rmsg.OldName, rmsg.NewName)
	return true
}

type R2LSaveUserBattleSnapshotCommand struct {
	BaseCommand
}

func (r *R2LSaveUserBattleSnapshotCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_SaveUserBattleSnapshot)
	l4g.Debugf("R2L_SaveUserBattleSnapshot, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	data := rmsg.NewIds
	l4g.Infof("SaveUserBattleSnapshot do callback from cmd:%d", amsg.CallCmd)
	ret := amsg.Work(rmsg.Ret, data)
	return ret
}

type R2LGetUserBattleSnapshotCommand struct {
	BaseCommand
}

func (r *R2LGetUserBattleSnapshotCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetUserBattleSnapshot)
	l4g.Debugf("R2L_GetUserBattleSnapshot, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	l4g.Infof("GetUserBattleSnapshot do callback from cmd:%d", amsg.CallCmd)
	ret := amsg.Work(rmsg.Ret, rmsg.Infos)
	return ret
}

type R2LLoadGuildCommand struct {
	BaseCommand
}

func (r *R2LLoadGuildCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_LoadGuild)
	l4g.Debugf("R2L_LoadGuild, %s", rmsg)
	srv := base.GetService(ctx)

	guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)
	for _, u := range rmsg.Users {
		guildM.LoadGuildUser(u)
	}
	//标记公会加载完成
	guildM.GuildDataLoaded(rmsg.Guild)
	return true
}

type R2LGetGuildDungeonLogCommand struct {
	BaseCommand
}

func (r *R2LGetGuildDungeonLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGuildDungeonLog)
	l4g.Debugf("R2L_GetGuildDungeonLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGetGuildLogCommand struct {
	BaseCommand
}

func (r *R2LGetGuildLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGuildLog)
	l4g.Debugf("R2L_GetGuildLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LSaveArenaLastSeasonTopCommand struct {
	BaseCommand
}

func (r *R2LSaveArenaLastSeasonTopCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_SaveArenaLastSeasonTop)
	l4g.Debugf("R2L_SaveArenaLastSeasonTop, %s", rmsg)

	srv := base.GetService(ctx)
	arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
	arenaM.SeasonTop.Update(rmsg.Data)
	return true
}

type R2LGetGuildMemberInfoCommand struct {
	BaseCommand
}

func (r *R2LGetGuildMemberInfoCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGuildMemberInfo)
	l4g.Debugf("R2L_GetGuildMemberInfo, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	data := amsg.Param.([]*cl.GuildMemberInfo)
	guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)
	guild := guildM.GetGuildByID(rmsg.Gid)
	if guild == nil {
		l4g.Errorf("R2LGetGuildMemberInfoCommand: guild is nil. %d", rmsg.Gid)
		return amsg.Work(uint32(ret.RET_ERROR), data)
	}
	for _, user := range rmsg.Users {
		snap := character.NewUserSnapshot(user, 0, srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)))
		if guild != nil {
			snap.GuildId, snap.GuildName = guild.ID(), guild.Name()
		}
		memberInfo := &cl.GuildMemberInfo{
			Info: snap,
		}

		var guildUser *aguild.User
		if rmsg.GuildNeedLoad {
			guildUserData := rmsg.GuildUser[user.Id]
			if guildUserData == nil {
				l4g.Errorf("R2LGetGuildMemberInfoCommand: guildUser is nil. %d", user.Id)
				continue
			}
			guildM.LoadGuildUser(guildUserData)
		}
		guildUser = guildM.GetGuildUser(user.Id)

		if guildUser != nil {
			memberInfo.Grade = guildUser.Grade()
			memberInfo.WeeklyActivityPoint = guildUser.GetWeeklyActivityPoint()
		}
		data = append(data, memberInfo)
	}
	if rmsg.GuildNeedLoad {
		guild.SetLoadFinish()
	}
	ret := amsg.Work(rmsg.Ret, data)
	return ret
}

type R2LGetGuildUsersCommand struct {
	BaseCommand
}

func (r *R2LGetGuildUsersCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGuildUsers)
	l4g.Debugf("R2L_GetGuildUsers, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	data := amsg.Param.(map[uint64]*rdb.GuildUser)
	for id, dbGuildUser := range rmsg.Users {
		if dbGuildUser == nil {
			l4g.Errorf("R2LGetGuildUsersCommand: dbGuildUser is nil. id=%d", id)
			continue
		}
		data[id] = dbGuildUser
	}
	ret := amsg.Work(rmsg.Ret, data)
	return ret
}

// ==============================GRPC=============================//
type R2LGRPC_LoginCommand struct {
	BaseCommand
}

func (r *R2LGRPC_LoginCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_Login)
	l4g.Debugf("R2L_GRPC_Login, %v", rmsg)

	//srv := base.GetService(ctx)
	//userm := srv.UserM()
	//user := userm.GetUser(rmsg.ID)
	//if user != nil {
	//	rmsg.Data = user.Clone()
	//}
	rmsg.Callback(rmsg.Data)
	return true
}

type R2LGRPC_GetUserIDCommand struct {
	BaseCommand
}

func (r *R2LGRPC_GetUserIDCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_GetUserID)
	l4g.Debugf("R2L_GRPC_GetUserID, %+v", rmsg)

	rmsg.Callback(rmsg)
	return true
}

type R2LGRPCChangeDungeonIDCommand struct {
	BaseCommand
}

func (r *R2LGRPCChangeDungeonIDCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ChangeDungeonID)
	l4g.Debugf("R2L_GRPC_ChangeDungeonID, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCGuidanceClosCommand struct {
	BaseCommand
}

func (r *R2LGRPCGuidanceClosCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_GuidanceClose)
	l4g.Debugf("R2L_GRPC_GuidanceClose, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCSetUserLevelCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetUserLevelCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetUserLevel)
	l4g.Debugf("R2L_GRPC_SetUserLevel, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCClearUserResourceCommand struct {
	BaseCommand
}

func (r *R2LGRPCClearUserResourceCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ClearUserResource)
	l4g.Debugf("R2L_GRPC_ClearUserResource, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCActivityInfoCommand struct {
	BaseCommand
}

func (r *R2LGRPCActivityInfoCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ActivityInfo)
	l4g.Debugf("R2L_GRPC_ActivityInfo, %v", rmsg)

	rmsg.Callback(rmsg.Data)
	return true
}

type R2LGRPCResetDailyNumCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetDailyNumCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetDailyNum)
	l4g.Debugf("R2L_GRPC_ResetDailyNum, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCSetMedalLevelCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetMedalLevelCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetMedalLevel)
	l4g.Debugf("R2L_GRPC_SetMedalLevel, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetMazeCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetMazeCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetMaze)
	l4g.Debugf("R2L_GRPC_ResetMaze, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetGuildQuitTmCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetGuildQuitTmCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetGuildQuitTm)
	l4g.Debugf("R2L_GRPC_ResetGuildQuitTm, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetTowerFloorCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetTowerFloorCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetTowerFloor)
	l4g.Debugf("R2L_GRPC_ResetTowerFloor, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetTrialLevelCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetTrialLevelCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetTrialLevel)
	l4g.Debugf("R2L_GRPC_ResetTrialLevel, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetMirageFloorCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetMirageFloorCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetMirageFloor)
	l4g.Debugf("R2L_GRPC_ResetMirageFloor, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetScoreCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetScoreCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetScore)
	l4g.Debugf("R2L_GRPC_ResetScore, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetTaleChapterFinishCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetTaleChapterFinishCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetTaleChapterFinish)
	l4g.Debugf("R2L_GRPC_ResetTaleChapterFinish, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCResetTaleEliteCommand struct {
	BaseCommand
}

func (r *R2LGRPCResetTaleEliteCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetTaleElite)
	l4g.Debugf("R2L_GRPC_ResetTaleElite, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCSetUserResourceCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetUserResourceCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetUserResource)
	l4g.Debugf("R2L_GRPC_SetUserResource, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPC_AddOrderCommand struct {
	BaseCommand
}

func (r *R2LGRPC_AddOrderCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_AddOrder)
	l4g.Infof("R2L_GRPC_AddOrder:%+v", rmsg)
	rmsg.Callback(rmsg.Ret, rmsg.Order)
	return true
}

type R2LGRPC_SetTowerstarDungeonIDCommand struct {
	BaseCommand
}

func (r *R2LGRPC_SetTowerstarDungeonIDCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetTowerstarDungeonID)
	l4g.Infof("R2L_GRPC_SetTowerstarDungeonID:%+v", rmsg)
	rmsg.Callback()
	return true
}

type R2LGRPC_ResetTowerstarCommand struct {
	BaseCommand
}

func (r *R2LGRPC_ResetTowerstarCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ResetTowerstar)
	l4g.Infof("R2L_GRPC_ResetTowerstar:%+v", rmsg)
	rmsg.Callback()
	return true
}

type R2LGRPC_SetQuestionnaireFinishCommand struct {
	BaseCommand
}

func (r *R2LGRPC_SetQuestionnaireFinishCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetQuestionnaireFinish)
	l4g.Infof("R2L_GRPC_SetQuestionnaireFinish:%+v", rmsg)
	rmsg.Callback(rmsg.Ret, rmsg.ID, rmsg.QuestionnaireID)
	return true
}

type R2LGRPCGetRechargeListCommand struct {
	BaseCommand
}

func (r *R2LGRPCGetRechargeListCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_GetRechargeList)
	l4g.Debugf("R2L_GRPC_GetRechargeList, %v", rmsg)

	rmsg.Callback(rmsg.Datas)
	return true
}

type R2LGRPCCloseGuidanceCommand struct {
	BaseCommand
}

func (r *R2LGRPCCloseGuidanceCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_CloseGuidance)
	l4g.Debugf("R2L_GRPC_CloseGuidance, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCSetMazeTaskLevelCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetMazeTaskLevelCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetMazeTaskLevel)
	l4g.Debugf("R2L_GRPC_SetMazeTaskLevel, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPCSetAccountTagCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetAccountTagCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetAccountTag)
	l4g.Debugf("R2L_GRPC_SetAccountTag, %+v", rmsg)

	rmsg.Callback(rmsg.Ret, rmsg.Data)
	return true
}

type R2LGRPCImportUserCommand struct {
	BaseCommand
}

func (r *R2LGRPCImportUserCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ImportUser)
	l4g.Debugf("R2L_GRPC_ImportUser, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGetDBUserCommand struct {
	BaseCommand
}

func (r *R2LGetDBUserCommand) Execute(ctx context.Context) (ret bool) {
	rmsg := r.msg.Data.(*r2l.R2L_GetDBUser)
	l4g.Debugf("R2L_GetDBUser, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg, ok := as.(*base.AsyncMessage)
	if ok {
		ret = amsg.GmWork(rmsg.Ret, rmsg.Users)
	}

	return
}

type R2LGetWrestleLogCommand struct {
	BaseCommand
}

func (r *R2LGetWrestleLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetWrestleLog)
	l4g.Debugf("R2L_GetWrestleLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGetGuildDungeonMessageBoardLogCommand struct {
	BaseCommand
}

func (r *R2LGetGuildDungeonMessageBoardLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGuildDungeonMessageBoardLog)
	l4g.Debugf("R2L_GetGuildDungeonMessageBoardLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LLoadGuildUserForResetCommand struct {
	BaseCommand
}

func (r *R2LLoadGuildUserForResetCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_LoadGuildUserForReset)
	l4g.Debugf("R2L_LoadGuildUserForReset, %s", rmsg)
	//srv := base.GetService(ctx)

	//guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)
	//for _, data := range rmsg.Data {
	//for _, user := range data.Users {
	//	guildM.LoadGuildUser(user)
	//}
	////标记公会加载完成
	//guildM.GuildDataLoaded(data.Gid)
	//
	//_, exist := activity.GuildDungeonRestType[rmsg.ResetType]
	//if exist {
	//	guildM.GuildDungeonResetFromDb(srv, rmsg.ResetType, data.Gid)
	//}
	//
	//_, exist = activity.GuildResetType[rmsg.ResetType]
	//if exist {
	//	guildM.GuildDailyResetFromDb(srv, data.Gid)
	//}
	//}

	return true
}

type R2LGetFlowerSnatchLogCommand struct {
	BaseCommand
}

func (r *R2LGetFlowerSnatchLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetFlowerSnatchLog)
	l4g.Debugf("R2L_GetFlowerSnatchLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGetFlowerOccupyLogCommand struct {
	BaseCommand
}

func (r *R2LGetFlowerOccupyLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetFlowerOccupyLog)
	l4g.Debugf("R2L_GetFlowerOccupyLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGetGuildDonateLogCommand struct {
	BaseCommand
}

func (r *R2LGetGuildDonateLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGuildDonateLog)
	l4g.Debugf("R2L_GetGuildDonateLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGRPCDelChatGroupTagCommand struct {
	BaseCommand
}

func (r *R2LGRPCDelChatGroupTagCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_DelChatGroupTag)
	l4g.Debugf("R2L_GRPC_DelChatGroupTag, %+v", rmsg)

	rmsg.Callback()
	return true
}

type R2LGRPC_AddWaitRefundOrderCommand struct {
	BaseCommand
}

func (r *R2LGRPC_AddWaitRefundOrderCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_AddWaitRefundOrder)
	l4g.Infof("R2L_GRPC_AddWaitRefundOrder:%+v", rmsg)
	rmsg.Callback(rmsg.Ret, rmsg.Order)
	return true
}

type R2LGRPCDeleteCurrenciesCommand struct {
	BaseCommand
}

func (r *R2LGRPCDeleteCurrenciesCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_DeleteCurrencies)
	l4g.Infof("R2L_GRPC_DeleteCurrencies:%+v", rmsg)
	rmsg.Callback(rmsg.Ret)
	return true
}

type R2LGRPCChangeBagsCommand struct {
	BaseCommand
}

func (r *R2LGRPCChangeBagsCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ChangeBags)
	l4g.Infof("R2L_GRPC_ChangeBags:%+v", rmsg)
	delResources := make([]*cl.Resource, 0, 5) //nolint:mnd
	for id, count := range rmsg.Bag.Items {
		if count > 0 {
			delResources = append(delResources, &cl.Resource{
				Type:  uint32(common.RESOURCE_ITEM),
				Value: id,
				Count: count,
			})
		}
	}
	for id, count := range rmsg.Bag.Fragments {
		if count > 0 {
			delResources = append(delResources, &cl.Resource{
				Type:  uint32(common.RESOURCE_FRAGMENT),
				Value: id,
				Count: count,
			})
		}
	}
	for id, count := range rmsg.Bag.Tokens {
		if count > 0 {
			delResources = append(delResources, &cl.Resource{
				Type:  uint32(common.RESOURCE_TOKEN),
				Value: id,
				Count: uint32(count),
			})
		}
	}
	for id, count := range rmsg.Bag.ArtifactFragments {
		if count > 0 {
			delResources = append(delResources, &cl.Resource{
				Type:  uint32(common.RESOURCE_ARTIFACT_FRAGMENT),
				Value: id,
				Count: count,
			})
		}
	}
	for id, count := range rmsg.Bag.EmblemFragments {
		if count > 0 {
			delResources = append(delResources, &cl.Resource{
				Type:  uint32(common.RESOURCE_EMBLEM_FRAGMENT),
				Value: id,
				Count: count,
			})
		}
	}
	rmsg.Callback(rmsg.Ret, delResources)
	return true
}

type R2LGRPCSetDailyAttendanceCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetDailyAttendanceCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetDailyAttendance)
	l4g.Infof("R2L_GRPC_SetDailyAttendance:%+v", rmsg)
	rmsg.Callback()
	return true
}

type R2LGRPCSetDisorderLandCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetDisorderLandCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetDisorderLand)
	l4g.Infof("R2L_GRPC_SetDisorderLand:%+v", rmsg)
	rmsg.Callback()
	return true
}

type R2LGetSeasonArenaLogCommand struct {
	BaseCommand
}

func (r *R2LGetSeasonArenaLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetSeasonArenaLog)
	l4g.Debugf("R2L_GetSeasonArenaLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGetGstChallengeLogCommand struct {
	BaseCommand
}

func (r *R2LGetGstChallengeLogCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GetGstChallengeLog)
	l4g.Debugf("R2L_GetGstChallengeLog, %s", rmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(rmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", rmsg.ClientMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(rmsg.ClientMsgId)
	amsg := as.(*base.AsyncMessage)
	ret := amsg.Work(rmsg.Ret, rmsg.Logs)
	return ret
}

type R2LGRPCChangeUserNameCommand struct {
	BaseCommand
}

func (r *R2LGRPCChangeUserNameCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_ChangeUserName)
	l4g.Infof("R2L_GRPC_ChangeUserName:%+v", rmsg)
	rmsg.Callback()
	return true
}

type R2LGRPCSetSeasonLinkCommand struct {
	BaseCommand
}

func (r *R2LGRPCSetSeasonLinkCommand) Execute(ctx context.Context) bool {
	rmsg := r.msg.Data.(*r2l.R2L_GRPC_SetSeasonLink)
	l4g.Infof("R2L_GRPC_SetSeasonLink:%+v", rmsg)
	rmsg.Callback()
	return true
}
