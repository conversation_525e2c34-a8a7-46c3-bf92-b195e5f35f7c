package talenttree

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/gst"
	"app/logic/activity/hotrank"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/p2l"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreeGetData), &C2LTalentTreeGetDataCommand{}, state)                     // 获取数据
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreeLevelUp), &C2LTalentTreeLevelUpCommand{}, state)                     // 天赋树升级
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreeReset), &C2LTalentTreeResetCommand{}, state)                         // 重置天赋树
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreeReceiveTaskAwards), &C2LTalentTreeReceiveTaskAwardsCommand{}, state) // 领取任务奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreeHot), &C2LTalentTreeHotCommand{}, state)                             // 获取天赋树热度数据
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreePlanSave), &C2LTalentTreePlanSaveCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_TalentTreePlanDelete), &C2LTalentTreePlanDeleteCommand{}, state)
}

type C2LTalentTreeGetDataCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreeGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreeGetDataCommand) Error(msg *cl.L2C_TalentTreeGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeGetData, msg)
	return false
}

func (c *C2LTalentTreeGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreeGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreeGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreeGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreeGetData{
		Ret: uint32(cret.RET_OK),
	}

	tt := c.User.TalentTree()
	tt.CheckAddResetTimes(c.Srv)
	smsg.Base, smsg.Cultivate = tt.Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeGetData, smsg)
	return true
}

type C2LTalentTreeLevelUpCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreeLevelUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreeLevelUpCommand) Error(msg *cl.L2C_TalentTreeLevelUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeLevelUp, msg)
	return false
}

func (c *C2LTalentTreeLevelUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreeLevelUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreeLevelUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreeLevelUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreeLevelUp{
		Ret: uint32(cret.RET_OK),
	}
	smsg.Nodes = make([]*cl.TalentTreeNode, len(cmsg.Nodes))
	copy(smsg.Nodes, cmsg.Nodes)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), c.Srv) {
		l4g.Errorf("user: %d C2L_TalentTreeLevelUp: func not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	talentTree := c.User.TalentTree()
	smsg.Ret, smsg.Awards = talentTree.NodesLevelUp(c.Srv, cmsg.Nodes, false)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_TalentTreeLevelUp: node level up error.", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}
	_, smsg.Cultivate = talentTree.Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeLevelUp, smsg)
	return true
}

type C2LTalentTreeResetCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreeResetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreeResetCommand) Error(msg *cl.L2C_TalentTreeReset, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeReset, msg)
	return false
}

func (c *C2LTalentTreeResetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreeReset{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreeReset Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreeReset: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreeReset{
		Ret: uint32(cret.RET_OK),
	}
	smsg.NodeIds = make([]uint32, len(cmsg.NodeIds))
	copy(smsg.NodeIds, cmsg.NodeIds)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), c.Srv) {
		l4g.Errorf("user: %d C2L_TalentTreeReset: func not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	talentTree := c.User.TalentTree()
	if len(cmsg.NodeIds) == 0 || len(cmsg.NodeIds) > talentTree.GetNodeNum() {
		l4g.Errorf("user: %d C2L_TalentTreeReset: request id num error. num:%d", c.Msg.UID, len(cmsg.NodeIds))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	uniqueIds := make(map[uint32]struct{}, len(cmsg.NodeIds))
	rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(c.User.GetSeasonID())
	for _, id := range cmsg.NodeIds {
		_, exist := uniqueIds[id]
		if exist {
			l4g.Errorf("user: %d C2L_TalentTreeReset: request id repeat.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniqueIds[id] = struct{}{}
		if id == rootId {
			l4g.Errorf("user: %d C2L_TalentTreeReset: rootId can't reset.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		level := talentTree.GetLevel(id)
		if level == 0 {
			l4g.Errorf("user: %d C2L_TalentTreeReset: id:%d not exist.", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	talentTree.CheckAddResetTimes(c.Srv)
	itemID := goxml.GetData().SeasonTalentTreeConfigInfoM.GetTalentResetValue()
	oldResetTimes := c.User.GetItemCount(itemID)

	var retCode cret.RET
	var costs []*cl.Resource
	if retCode, costs = c.CheckResetConditions(); retCode != cret.RET_OK {
		l4g.Errorf("user: %d C2L_TalentTreeReset: conditions check failed. ret:%d", c.Msg.UID, retCode)
		return c.Error(smsg, uint32(retCode))
	}

	// 检查返还奖励
	totalReturnAwards := talentTree.CalcResetReturnAwards(cmsg.NodeIds)
	if len(totalReturnAwards) == 0 {
		l4g.Errorf("user: %d C2L_TalentTreeReset: not need reset.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if len(costs) > 0 {
		smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, totalReturnAwards, uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_RESET), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_TalentTreeReset: trade error. ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	} else {
		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalReturnAwards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_RESET), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_TalentTreeReset: award error. ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	c.UpdateHot(cmsg.NodeIds) // 需要在重置前调用
	talentTree.ResetCultivationByNodeIds(cmsg.NodeIds)
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateBySeasonRaise)
	c.HandleTeam()

	_, smsg.Cultivate = talentTree.Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeReset, smsg)

	newResetTimes := c.User.GetItemCount(itemID)
	c.User.LogTalentTreeReset(c.Srv, oldResetTimes, newResetTimes)
	return true
}

func (c *C2LTalentTreeResetCommand) UpdateHot(resetNodeIds []uint32) {
	hotRankM, ok := c.Srv.GetActivity(activity.HotRank).(*hotrank.Manager)
	if !ok {
		l4g.Errorf("user:%d C2L_TalentTreeLevelUp.UpdateHot: gst activity error.", c.Msg.UID)
		return
	}
	talentTree := c.User.TalentTree()
	for _, id := range resetNodeIds {
		if id == goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(c.User.GetSeasonID()) {
			continue
		}
		level := talentTree.GetLevel(id)
		if level == 0 {
			continue
		}
		hotRankM.GetTalentTreeHotM().UpdateTalentTreeNodeLevel(id, level, 0)
	}
}

func (c *C2LTalentTreeResetCommand) CheckResetConditions() (cret.RET, []*cl.Resource) {
	var ret uint32
	var costs []*cl.Resource
	tt := c.User.TalentTree()

	if !tt.IsResetFree() {
		// 检查重置道具
		talentCost := goxml.GetData().SeasonTalentTreeConfigInfoM.GetTalentResetCost()
		ret, costs = c.User.CheckResourcesSize(talentCost)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_TalentTreeReset: have no reset times.", c.Msg.UID)
			return cret.RET(ret), nil
		}
	}

	// 检查公会战
	haveGstNode := false
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(c.User.GetSeasonID(), goxml.TalentTreeNodeLevelAdditionGstTeamNum)
	for _, id := range ids {
		if tt.GetLevel(id) == 0 {
			continue
		}
		haveGstNode = true
		break
	}
	if haveGstNode {
		gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
		if !ok {
			l4g.Errorf("user: %d C2L_TalentTreeReset: checkResetConditions gstManager not exist.", c.Msg.UID)
			return cret.RET_SYSTEM_DATA_ERROR, nil
		}
		if gstM.GetCrossGst() != nil && gstM.GetCrossGst().Stage >= cl.GST_STAGE_RoundFightState_Fight && gstM.GetCrossGst().Stage <= cl.GST_STAGE_RoundFightState_Reward {
			l4g.Errorf("user: %d C2L_TalentTreeReset: checkResetConditions gst can't operate.", c.Msg.UID)
			return cret.RET_TALENT_TREE_RESET_GST_LIMIT, nil
		}
	}
	return cret.RET_OK, costs
}

func (c *C2LTalentTreeResetCommand) HandleTeam() {
	funcIDs := []common.FUNCID{common.FUNCID_MODULE_GST, common.FUNCID_MODULE_GST_DRAGON}
	gstTeamChange := false
	for _, funcID := range funcIDs {
		infos := goxml.GetData().FormationInfoM.GetFuncFormationInfos(uint32(funcID))
		for _, info := range infos {
			ok, teamNum := c.User.GetTeamNum(info.Id)
			if !ok {
				l4g.Errorf("user: %d HandleGstTeam: teamNum error.", c.User.ID())
				return
			}
			formation := c.User.GetFormation(info.Id)
			if formation == nil {
				// l4g.Errorf("user: %d HandleGstTeam: gstFormation is nil.", c.User.ID())
				continue
			}
			if len(formation.Teams) <= teamNum {
				continue
			}
			newFormation := formation.Clone()
			newFormation.Teams = newFormation.Teams[0:teamNum]
			c.User.FormationManager().NewFormation(c.Srv, info.Id, newFormation)
			if funcID == common.FUNCID_MODULE_GST {
				gstTeamChange = true
			}
		}
	}

	if !gstTeamChange {
		return
	}

	// gst阵容改变，同步跨服
	gstM, success := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !success {
		l4g.Errorf("user: %d HandleGstTeam: get activity gstM error.", c.User.ID())
		return
	}
	if gstM.GetCrossGst() == nil {
		l4g.Errorf("user: %d HandleGstTeam: gstM.GetCrossGst is nil.", c.User.ID())
		return
	}
	if gstM.GetCrossGst().Stage >= cl.GST_STAGE_RoundFightState_Fight && gstM.GetCrossGst().Stage <= cl.GST_STAGE_RoundFightState_Reward {
		l4g.Errorf("user: %d HandleGstTeam: gstTeam can't operate.", c.User.ID())
		return
	}
	crossMsg := &l2c.L2CS_GSTSetTeam{}
	crossMsg.FormationId = uint32(common.FORMATION_ID_FI_GST)
	crossMsg.Formation = c.User.Convert2GSTTeam()
	if gstM.IsCrossConnected() {
		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSetTeam, c.Msg.UID, crossMsg) {
			l4g.Errorf("user: %d C2L_HeroStarUp: ID_MSG_L2CS_GSTSetTeam cross maintain", c.Msg.UID)
		}
	}
}

type C2LTalentTreeReceiveTaskAwardsCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreeReceiveTaskAwardsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreeReceiveTaskAwardsCommand) Error(msg *cl.L2C_TalentTreeReceiveTaskAwards, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeReceiveTaskAwards, msg)
	return false
}

func (c *C2LTalentTreeReceiveTaskAwardsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreeReceiveTaskAwards{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreeReceiveTaskAwards Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreeReceiveTaskAwards: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreeReceiveTaskAwards{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), c.Srv) {
		l4g.Errorf("user: %d C2L_TalentTreeReceiveTaskAwards: func not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if len(cmsg.Ids) > goxml.GetData().SeasonTalentTreeTaskAwardInfoM.GetTaskNum(c.User.GetSeasonID()) {
		l4g.Errorf("user: %d C2L_TalentTreeReceiveTaskAwards: ids num illegal. num:%d", c.Msg.UID, len(cmsg.Ids))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	uniq := make(map[uint32]struct{})

	totalAwards := make([]*cl.Resource, 0, len(cmsg.Ids))
	for _, id := range cmsg.Ids {
		if _, exist := uniq[id]; exist {
			l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: id repeated. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[id] = struct{}{}
		taskInfo := goxml.GetData().SeasonTalentTreeTaskAwardInfoM.GetRecordBySeasonIdId(c.User.GetSeasonID(), id)
		if taskInfo == nil {
			l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: id error. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: taskTypeInfo not exist. typeId:%d",
				c.Msg.UID, taskInfo.TypeId)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		pro := c.User.TalentTree().GetOneTypeProgress(taskTypeInfo)
		if !c.User.CheckTaskFinish(pro, taskInfo.TypeId, uint64(taskInfo.Value)) {
			l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: not finish. id:%d pro:%+v typeId:%d value:%d",
				c.Msg.UID, id, pro, taskInfo.TypeId, taskInfo.Value)
			return c.Error(smsg, uint32(cret.RET_CARNIVAL_TASK_PROGRESS_NOT_FINISH))
		}

		if c.User.TalentTree().IsAwarded(id) {
			l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: has receive award. id:%d ", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}

		awards := taskInfo.Rewards
		if awards == nil {
			l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: awards empty. id:%d ", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		totalAwards = append(totalAwards, awards...)
	}
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_RECV_TASK_AWARDS), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_TalentTreeReceiveTaskAwards: award error. ret:%d ", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	c.User.TalentTree().ReceiveAward(cmsg.Ids)
	smsg.Data, _ = c.User.TalentTree().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeReceiveTaskAwards, smsg)
	c.User.LogTalentTreeReceiveTaskAwards(c.Srv, cmsg.Ids)
	return true
}

type C2LTalentTreeHotCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreeHotCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreeHotCommand) Error(msg *cl.L2C_TalentTreeHot, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeHot, msg)
	return false
}

func (c *C2LTalentTreeHotCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreeHot{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreeHot Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreeHot: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreeHot{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), c.Srv) {
		l4g.Errorf("user: %d C2L_TalentTreeHot: func not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_HotRankFlushTalentTreeHot, c.Msg.UID, &l2c.L2CS_HotRankFlushTalentTreeHot{}) {
		l4g.Errorf("user: %d C2L_TalentTreeHot: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LTalentTreePlanSaveCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreePlanSaveCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreePlanSaveCommand) Error(msg *cl.L2C_TalentTreePlanSave, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreePlanSave, msg)
	return false
}

func (c *C2LTalentTreePlanSaveCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreePlanSave{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreePlanSave Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreePlanSave: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreePlanSave{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), c.Srv) {
		l4g.Errorf("user: %d C2L_TalentTreePlanSave: func not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.TalentTree().IsPlanNumEnough() {
		l4g.Errorf("user: %d C2L_TalentTreePlanSave: plan num not enough.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_TALENT_TREE_PLAN_NUM_LIMIT))
	}

	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		var checks []*p2l.SensitiveWordCheckReq
		if cmsg.Plan != nil && len(cmsg.Plan.Topic) != 0 {
			checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Plan.Topic, c.Srv.ServerID(), character.SensitiveTalentTree, platformConfig.Channel))
		}
		if len(checks) > 0 {
			base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LTalentTreePlanSaveReq{
				Smsg: smsg,
				Plan: cmsg.Plan,
			}, checks, nil)
			return true
		}
	}

	c.User.TalentTree().PlanSave(cmsg.Plan)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreePlanSave, smsg)
	return true
}

type C2LTalentTreePlanDeleteCommand struct {
	base.UserCommand
}

func (c *C2LTalentTreePlanDeleteCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTalentTreePlanDeleteCommand) Error(msg *cl.L2C_TalentTreePlanDelete, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreePlanDelete, msg)
	return false
}

func (c *C2LTalentTreePlanDeleteCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TalentTreePlanDelete{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TalentTreePlanDelete Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TalentTreePlanDelete: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_TalentTreePlanDelete{
		Ret:   uint32(cret.RET_OK),
		Index: cmsg.Index,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), c.Srv) {
		l4g.Errorf("user: %d C2L_TalentTreePlanDelete: func not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	result := c.User.TalentTree().PlanDelete(int(cmsg.Index))
	if result != ret.RET_OK {
		return c.Error(smsg, uint32(result))
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreePlanDelete, smsg)
	return true
}

type AsyncC2LTalentTreePlanSaveReq struct {
	Smsg *cl.L2C_TalentTreePlanSave
	Plan *cl.TalentTreePlan
}

func (ar *AsyncC2LTalentTreePlanSaveReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LTalentTreePlanSaveReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreePlanSave, ar.Smsg)
		return false
	}

	args.Caller.TalentTree().PlanSave(ar.Plan)
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreePlanSave, ar.Smsg)
	return true
}
