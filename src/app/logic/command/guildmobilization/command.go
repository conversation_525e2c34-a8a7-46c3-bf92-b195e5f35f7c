package guildmobilization

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"context"
	"unicode/utf8"

	// "app/protos/out/common"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationGetData), &C2LGuildMobilizationGetDataCommand{}, state)               // 获取数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationAcceptTask), &C2LGuildMobilizationAcceptTaskCommand{}, state)         // 接取任务
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationSignTask), &C2LGuildMobilizationSignTaskCommand{}, state)             // 标记任务
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationFinishTaskLogs), &C2LGuildMobilizationFinishTaskLogsCommand{}, state) // 积分日志
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationPersonalRank), &C2LGuildMobilizationPersonalRankCommand{}, state)     // 个人排行
	//cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationRecvScoreLevel), &C2LGuildMobilizationRecvScoreLevelCommand{}, state)     // 领取积分等级奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationFreshTask), &C2LGuildMobilizationFreshTaskCommand{}, state)               // 刷新任务
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationBuyTimes), &C2LGuildMobilizationBuyTimesCommand{}, state)                 // 购买次数
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationEditMessageBoard), &C2LGuildMobilizationEditMessageBoardCommand{}, state) // 编辑公告
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationGiveUpTask), &C2LGuildMobilizationGiveUpTaskCommand{}, state)             // 放弃任务
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationScoreAward), &C2LGuildMobilizationScoreAwardCommand{}, state)             // 领取积分奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationCancelTask), &C2LGuildMobilizationCancelTaskCommand{}, state)             // 公会取消任务
}

type C2LGuildMobilizationGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationGetDataCommand) Return(msg *cl.L2C_GuildMobilizationGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationGetData, msg)
	return true
}

func (c *C2LGuildMobilizationGetDataCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationGetData{}
	cmsg := &cl.C2L_GuildMobilizationGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationGetData: cmsg:%s", c.Msg.UID, cmsg)

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationGetData failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationGetData not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationGetData: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationGetData, c.User.ID(),
		&l2c.L2CS_GuildMobilizationGetData{}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationGetData cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationAcceptTaskCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationAcceptTaskCommand) Return(msg *cl.L2C_GuildMobilizationAcceptTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationAcceptTask, msg)
	return true
}

func (c *C2LGuildMobilizationAcceptTaskCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationAcceptTask{}
	cmsg := &cl.C2L_GuildMobilizationAcceptTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationAcceptTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationAcceptTask: cmsg:%s", c.Msg.UID, cmsg)

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	taskInfo := goxml.GetData().GuildMobilizationTaskInfoM.Index(cmsg.TaskId)
	if taskInfo == nil {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask cant find task %d", c.Msg.UID, cmsg.TaskId)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	if taskInfo.OpenDay > c.Srv.ServerOpenDay(now) {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask task open day limit %d", c.Msg.UID, taskInfo.OpenDay)
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_TASK_OPEN_DAY_LIMIT))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationAcceptTask failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationAcceptTask not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationAcceptTask: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationAcceptTask, c.User.ID(),
		&l2c.L2CS_GuildMobilizationAcceptTask{
			TaskId: cmsg.TaskId,
		}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationAcceptTask cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationSignTaskCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationSignTaskCommand) Return(msg *cl.L2C_GuildMobilizationSignTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationSignTask, msg)
	return true
}

func (c *C2LGuildMobilizationSignTaskCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationSignTask{}
	cmsg := &cl.C2L_GuildMobilizationSignTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationSignTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationSignTask: cmsg:%s", c.Msg.UID, cmsg)

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask: not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationAcceptTask: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationSignTask failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationSignTask not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationSignTask: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationSignTask, c.User.ID(), &l2c.L2CS_GuildMobilizationSignTask{
		TaskId: cmsg.TaskId,
		IsSign: cmsg.IsSign,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationSignTask cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationFinishTaskLogsCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationFinishTaskLogsCommand) Return(msg *cl.L2C_GuildMobilizationFinishTaskLogs, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationFinishTaskLogs, msg)
	return true
}

func (c *C2LGuildMobilizationFinishTaskLogsCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationFinishTaskLogs{}
	cmsg := &cl.C2L_GuildMobilizationFinishTaskLogs{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationFinishTaskLogs Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationFinishTaskLogs: cmsg:%s", c.Msg.UID, cmsg)
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationFinishTaskLogs: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationFinishTaskLogs failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationFinishTaskLogs not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationFinishTaskLogs: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationFinishTaskLogs, c.User.ID(), &l2c.L2CS_GuildMobilizationFinishTaskLogs{}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationFinishTaskLogs cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationPersonalRankCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationPersonalRankCommand) Return(msg *cl.L2C_GuildMobilizationPersonalRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationPersonalRank, msg)
	return true
}

func (c *C2LGuildMobilizationPersonalRankCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationPersonalRank{}
	cmsg := &cl.C2L_GuildMobilizationPersonalRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationPersonalRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationPersonalRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationPersonalRank: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationPersonalRank failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationPersonalRank not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationPersonalRank: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationPersonalRank, c.User.ID(), &l2c.L2CS_GuildMobilizationPersonalRank{}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationPersonalRank cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationRecvScoreLevelCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationRecvScoreLevelCommand) Return(msg *cl.L2C_GuildMobilizationRecvScoreLevel, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationRecvScoreLevel, msg)
	return true
}

func (c *C2LGuildMobilizationRecvScoreLevelCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationRecvScoreLevel{}
	cmsg := &cl.C2L_GuildMobilizationRecvScoreLevel{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationRecvScoreLevel Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationRecvScoreLevel: cmsg:%s", c.Msg.UID, cmsg)
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationRecvScoreLevel: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationRecvScoreLevel failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationRecvScoreLevel not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationRecvScoreLevel: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationRecvScoreLevel, c.User.ID(), &l2c.L2CS_GuildMobilizationRecvScoreLevel{}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationRecvScoreLevel cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationFreshTaskCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationFreshTaskCommand) Return(msg *cl.L2C_GuildMobilizationFreshTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationFreshTask, msg)
	return true
}

func (c *C2LGuildMobilizationFreshTaskCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationFreshTask{
		Ret: uint32(ret.RET_OK),
	}
	cmsg := &cl.C2L_GuildMobilizationFreshTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationFreshTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationFreshTask: cmsg:%s", c.Msg.UID, cmsg)
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationFreshTask: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationFreshTask failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationFreshTask not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationFreshTask guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationFreshTask: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationFreshTask, c.User.ID(), &l2c.L2CS_GuildMobilizationFreshTask{
		TaskId: cmsg.TaskId,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationFreshTask cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationBuyTimesCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationBuyTimesCommand) Return(msg *cl.L2C_GuildMobilizationBuyTimes, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationBuyTimes, msg)
	return true
}

func (c *C2LGuildMobilizationBuyTimesCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationBuyTimes{}
	cmsg := &cl.C2L_GuildMobilizationBuyTimes{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationBuyTimes Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationBuyTimes: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationBuyTimes: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationBuyTimes failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationBuyTimes not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationBuyTimes guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	if cmsg.NeedDiamond == 0 {
		l4g.Errorf("user: %d C2L_GuildMobilizationBuyTimes: GetPrice failed. purchasedNum:%d, num:%d",
			c.User.ID(), cmsg.BuyTimes, cmsg.NeedDiamond)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationBuyTimes: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}
	costs := []*cl.Resource{{
		Type:  uint32(common.RESOURCE_DIAMOND),
		Value: 0,
		Count: cmsg.NeedDiamond,
	}}

	result := c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_BUY_TIMES), 0)
	if result != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildMobilizationBuyTimes: consume failed, ret:%d", c.Msg.UID, result)
		return c.Return(smsg, result)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationBuyTimes, c.User.ID(), &l2c.L2CS_GuildMobilizationBuyTimes{
		Type:        cmsg.Type,
		NeedDiamond: cmsg.NeedDiamond,
		BuyTimes:    cmsg.BuyTimes,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationBuyTimes cross maintain", c.User.ID())
		// 异常需要补发
		c.User.Award(c.Srv, costs, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_BUY_TIMES), 0)
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationEditMessageBoardCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationEditMessageBoardCommand) Return(msg *cl.L2C_GuildMobilizationEditMessageBoard, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationEditMessageBoard, msg)
	return true
}

func (c *C2LGuildMobilizationEditMessageBoardCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationEditMessageBoard{
		Ret: uint32(ret.RET_OK),
	}
	cmsg := &cl.C2L_GuildMobilizationEditMessageBoard{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationEditMessageBoard Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationEditMessageBoard: cmsg:%s", c.Msg.UID, cmsg)
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationEditMessageBoard: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationEditMessageBoard failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationEditMessageBoard not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationEditMessageBoard guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	result := CheckString(cmsg.Message, c.Msg.UID, int(goxml.GetData().GuildConfigInfoM.NoticeLengthMax))
	if result != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildMobilizationEditMessageBoard: message error", c.Msg.UID)
		return c.Return(smsg, result)
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationEditMessageBoard: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationEditMessageBoard, c.User.ID(), &l2c.L2CS_GuildMobilizationEditMessageBoard{
		Message: cmsg.Message,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationEditMessageBoard cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

func CheckString(content string, uid uint64, maxLen int) uint32 {
	if len(content) > maxLen*character.MaxCharactUtf8Len { //  提前检查字节长度
		l4g.Errorf("checkcontent %d content: %s length error len:%d ", uid, content, len(content))
		return uint32(ret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	if !utf8.ValidString(content) { //  这里有遍历
		l4g.Errorf("uid %d content illegal: %s", uid, content)
		return uint32(ret.RET_GUILD_STRING_ILLEGAL_CHARACTER)
	}
	// utf8字符长度
	if length := utf8.RuneCountInString(content); length > maxLen {
		l4g.Errorf("uid %d content: %s length error: %d", uid, content, length)
		return uint32(ret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	return uint32(ret.RET_OK)
}

type C2LGuildMobilizationGiveUpTaskCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationGiveUpTaskCommand) Return(msg *cl.L2C_GuildMobilizationGiveUpTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationGiveUpTask, msg)
	return true
}

func (c *C2LGuildMobilizationGiveUpTaskCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationGiveUpTask{
		Ret: uint32(ret.RET_OK),
	}
	cmsg := &cl.C2L_GuildMobilizationGiveUpTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationGiveUpTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationGiveUpTask: cmsg:%s", c.Msg.UID, cmsg)
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGiveUpTask: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationGiveUpTask failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationGiveUpTask not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}
	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationGiveUpTask guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}
	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationGiveUpTask: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationGiveUpTask, c.User.ID(), &l2c.L2CS_GuildMobilizationGiveUpTask{
		TaskId: cmsg.TaskId,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationGiveUpTask cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationScoreAwardCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationScoreAwardCommand) Return(msg *cl.L2C_GuildMobilizationScoreAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationScoreAward, msg)
	return true
}

func (c *C2LGuildMobilizationScoreAwardCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationScoreAward{
		Ret: uint32(ret.RET_OK),
	}
	cmsg := &cl.C2L_GuildMobilizationScoreAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationScoreAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationScoreAward: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationScoreAward: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationScoreAward failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationScoreAward not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationScoreAward guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationScoreAward: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationScoreAward, c.User.ID(), &l2c.L2CS_GuildMobilizationScoreAward{
		Ids: cmsg.Ids,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationScoreAward cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildMobilizationCancelTaskCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationCancelTaskCommand) Return(msg *cl.L2C_GuildMobilizationCancelTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationCancelTask, msg)
	return true
}

func (c *C2LGuildMobilizationCancelTaskCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationCancelTask{
		Ret: uint32(ret.RET_OK),
	}
	cmsg := &cl.C2L_GuildMobilizationCancelTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationCancelTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationCancelTask: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_MOBILIZATION), c.Srv) {
		l4g.Errorf("user:%d C2L_GuildMobilizationCancelTask: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationCancelTask failed, guildManager not exist", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationCancelTask not in guild", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationCancelTask get guild user failed", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_MEMBER))
	}

	if !goxml.GetData().GuildConfigInfoM.GuildMobilizationCheckLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GuildMobilizationCancelTask guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Return(smsg, uint32(ret.RET_GUILD_MOB_GUILD_LEVEL_ERR))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildMobilizationCancelTask: not manager.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMobilizationCancelTask: cross can't connect.", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationCancelTask, c.User.ID(), &l2c.L2CS_GuildMobilizationCancelTask{
		TaskId: cmsg.TaskId,
		Uid:    cmsg.Uid,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationCancelTask cross maintain", c.User.ID())
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}
