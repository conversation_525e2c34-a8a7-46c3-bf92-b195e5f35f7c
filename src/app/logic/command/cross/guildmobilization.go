package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"context"
	"maps"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitGuildMobilization(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationGetData), &CS2LGuildMobilizationGetDataCommand{}, state)               // 获取数据
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationAcceptTask), &CS2LGuildMobilizationAcceptTaskCommand{}, state)         // 接取任务
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationSignTask), &CS2LGuildMobilizationSignTaskCommand{}, state)             // 标记任务
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationFinishTaskLogs), &CS2LGuildMobilizationFinishTaskLogsCommand{}, state) // 积分日志
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationPersonalRank), &CS2LGuildMobilizationPersonalRankCommand{}, state)     // 个人排行
	//cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationRecvScoreLevel), &CS2LGuildMobilizationRecvScoreLevelCommand{}, state)   // 领取积分等级奖励
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationFreshTask), &CS2LGuildMobilizationFreshTaskCommand{}, state)               // 刷新任务
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationBuyTimes), &CS2LGuildMobilizationBuyTimesCommand{}, state)                 // 购买次数
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationSysFinishedTask), &CS2LGuildMobilizationSysFinishedTaskCommand{}, state)   // 同步任务的完成
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationEditMessageBoard), &CS2LGuildMobilizationEditMessageBoardCommand{}, state) // 编辑公告板
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationGiveUpTask), &CS2LGuildMobilizationGiveUpTaskCommand{}, state)             // 放弃任务
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationScoreAward), &CS2LGuildMobilizationScoreAwardCommand{}, state)             // 积分领奖
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationCancelTask), &CS2LGuildMobilizationCancelTaskCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GuildMobilizationBeCancelTask), &CS2LGuildMobilizationBeCancelTaskCommand{}, state) // 公会任务被取消
}

type CS2LGuildMobilizationGetDataCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationGetDataCommand) Return(msg *cl.L2C_GuildMobilizationGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationGetData, msg)
	return true
}

func (c *CS2LGuildMobilizationGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationGetData{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d CS2L_GuildMobilizationGetData: not open", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_GuildMobilizationGetData failed, guildManager not exist", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d CS2L_GuildMobilizationGetData guildUser is nil", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_ERROR))
	}
	guildUser.MobSyncGuildTasks(roundInfo, now, cmsg.Rsp.Tasks)
	guildUser.CheckFinishedMobilizationTasks(c.Srv, roundInfo, c.User, now)
	cmsg.Rsp.TaskData = maps.Clone(guildUser.MobGetTasks(roundInfo, now))
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationAcceptTaskCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationAcceptTaskCommand) Return(msg *cl.L2C_GuildMobilizationAcceptTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationAcceptTask, msg)
	return true
}

func (c *CS2LGuildMobilizationAcceptTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationAcceptTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationAcceptTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationAcceptTask{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationAcceptTask: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Rsp.Ret == uint32(ret.RET_OK) {
		now := time.Now().Unix()
		roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
		if roundInfo == nil {
			l4g.Errorf("user:%d C2L_GuildMobilizationGetTaskData: not open", c.Msg.UID)
			return c.Return(cmsg.Rsp, uint32(ret.RET_FUNCTION_NOT_OPEN))
		}
		guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
		if !ok {
			l4g.Errorf("user: %d C2L_GuildMobilizationGetTaskData failed, guildManager not exist", c.Msg.UID)
			return c.Return(cmsg.Rsp, uint32(ret.RET_ERROR))
		}
		guildUser := guildM.GetGuildUser(c.User.ID())
		if guildUser == nil {
			l4g.Errorf("user: %d C2L_GuildMobilizationGetTaskData guildUser is nil", c.Msg.UID)
			return c.Return(cmsg.Rsp, uint32(ret.RET_ERROR))
		}
		guildUser.MobAddTask(roundInfo, now, cmsg)
		c.User.LogGuildMobAcceptTask(c.Srv, cmsg.Rsp.TaskId)
	}
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationSignTaskCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationSignTaskCommand) Return(msg *cl.L2C_GuildMobilizationSignTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationSignTask, msg)
	return true
}

func (c *CS2LGuildMobilizationSignTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationSignTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationSignTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationSignTask{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationSignTask: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationFinishTaskLogsCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationFinishTaskLogsCommand) Return(msg *cl.L2C_GuildMobilizationFinishTaskLogs, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationFinishTaskLogs, msg)
	return true
}

func (c *CS2LGuildMobilizationFinishTaskLogsCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationFinishTaskLogs{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationFinishTaskLogs Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationFinishTaskLogs{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationFinishTaskLogs: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationPersonalRankCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationPersonalRankCommand) Return(msg *cl.L2C_GuildMobilizationPersonalRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationPersonalRank, msg)
	return true
}

func (c *CS2LGuildMobilizationPersonalRankCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationPersonalRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationPersonalRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationPersonalRank{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationPersonalRank: cmsg:%s", c.Msg.UID, cmsg)

	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationRecvScoreLevelCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationRecvScoreLevelCommand) Return(msg *cl.L2C_GuildMobilizationRecvScoreLevel, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationRecvScoreLevel, msg)
	return true
}

func (c *CS2LGuildMobilizationRecvScoreLevelCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationRecvScoreLevel{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationRecvScoreLevel Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationRecvScoreLevel{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Infof("user: %d CS2L_GuildMobilizationRecvScoreLevel: cmsg:%s", c.Msg.UID, cmsg)

	if cmsg.Rsp.Ret == uint32(ret.RET_OK) {
		cmsg.Rsp.Ret, cmsg.Rsp.Rewards = c.User.Award(c.Srv, cmsg.Rsp.Rewards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_SCORE_LEVELS), 0)
	}

	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationFreshTaskCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationFreshTaskCommand) Return(msg *cl.L2C_GuildMobilizationFreshTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationFreshTask, msg)
	return true
}

func (c *CS2LGuildMobilizationFreshTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationFreshTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationFreshTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationFreshTask{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationFreshTask: cmsg:%s", c.Msg.UID, cmsg)
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationBuyTimesCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationBuyTimesCommand) Return(msg *cl.L2C_GuildMobilizationBuyTimes, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationBuyTimes, msg)
	return true
}

func (c *CS2LGuildMobilizationBuyTimesCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationBuyTimes{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationBuyTimes Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationBuyTimes{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Infof("user: %d CS2L_GuildMobilizationBuyTimes: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Rsp.Ret != uint32(ret.RET_OK) {
		costs := []*cl.Resource{{
			Type:  uint32(common.RESOURCE_DIAMOND),
			Value: 0,
			Count: cmsg.Rsp.NeedDiamond,
		}}
		c.User.Award(c.Srv, costs, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_BUY_TIMES), 0)
	} else {
		c.User.LogGuildMobBuyTimes(c.Srv, cmsg.Rsp.Type, cmsg.Rsp.BuyTimes)
	}
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationSysFinishedTaskCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationSysFinishedTaskCommand) Return(msg *cl.L2C_GuildMobilizationUpdateTaskProgress, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationUpdateTaskProgress, msg)
	return true
}

func (c *CS2LGuildMobilizationSysFinishedTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationSysFinishedTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationSysFinishedTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationSysFinishedTask: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Rsp.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("sys task err.%d", cmsg.Rsp.Ret)
		return true
	}
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationSysFinishedTask: not open", c.Msg.UID)
		return true
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_GuildMobilizationSysFinishedTask failed, guildManager not exist", c.Msg.UID)
		return true
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d CS2L_GuildMobilizationSysFinishedTask not in guild", c.Msg.UID)
		return true
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d CS2L_GuildMobilizationSysFinishedTask guildUser is nil", c.Msg.UID)
		return true
	}
	guildUser.OnTaskFinishBack(roundInfo, cmsg.Rsp.FinishedTasks)
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationEditMessageBoardCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationEditMessageBoardCommand) Return(msg *cl.L2C_GuildMobilizationEditMessageBoard, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationEditMessageBoard, msg)
	return true
}

func (c *CS2LGuildMobilizationEditMessageBoardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationEditMessageBoard{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationEditMessageBoard Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationEditMessageBoard{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationEditMessageBoard: cmsg:%s", c.Msg.UID, cmsg)
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationGiveUpTaskCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationGiveUpTaskCommand) Return(msg *cl.L2C_GuildMobilizationGiveUpTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationGiveUpTask, msg)
	return true
}

func (c *CS2LGuildMobilizationGiveUpTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationGiveUpTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationGiveUpTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationGiveUpTask{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationGiveUpTask: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Rsp.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("sys task err.%d", cmsg.Rsp.Ret)
		return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
	}
	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d L2CS_GuildMobilizationGiveUpTask: not open", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_GuildMobilizationGiveUpTask failed, guildManager not exist", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d CS2L_GuildMobilizationGiveUpTask not in guild", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d CS2L_GuildMobilizationGiveUpTask guildUser is nil", c.Msg.UID)
		return c.Return(cmsg.Rsp, uint32(ret.RET_NOT_GUILD_MEMBER))
	}
	guildUser.MobOnGiveUpTask(roundInfo, cmsg.Rsp.Task.TaskId)
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationScoreAwardCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationScoreAwardCommand) Return(msg *cl.L2C_GuildMobilizationScoreAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationScoreAward, msg)
	return true
}

func (c *CS2LGuildMobilizationScoreAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationScoreAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationScoreAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationScoreAward{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationScoreAward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Rsp.Ret == uint32(ret.RET_OK) {
		totalAwards := make([]*cl.Resource, 0)
		for _, id := range cmsg.Rsp.Ids {
			rewardConfig := goxml.GetData().GuildMobilizationScoreRewardInfoM.Index(id)
			if rewardConfig == nil {
				l4g.Errorf("user: %d CS2L_GuildMobilizationScoreAward: no ScoreRewardInfo. id %d", c.Msg.UID, id)
				return c.Return(cmsg.Rsp, uint32(ret.RET_SYSTEM_DATA_ERROR))
			}
			totalAwards = append(totalAwards, rewardConfig.RewardClRes...)
		}
		cmsg.Rsp.Ret, cmsg.Rsp.Awards = c.User.Award(c.Srv, totalAwards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_GUILD_MOBILIZATION_USER_SCORE_AWARD), 0)
	}

	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationCancelTaskCommand struct {
	base.UserCommand
}

func (c *CS2LGuildMobilizationCancelTaskCommand) Return(msg *cl.L2C_GuildMobilizationCancelTask, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationCancelTask, msg)
	return true
}

func (c *CS2LGuildMobilizationCancelTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationCancelTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationCancelTask Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GuildMobilizationCancelTask{}, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GuildMobilizationCancelTask: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Rsp.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("sys task err.%d", cmsg.Rsp.Ret)
		return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
	}
	return c.Return(cmsg.Rsp, cmsg.Rsp.Ret)
}

type CS2LGuildMobilizationBeCancelTaskCommand struct {
	base.Command
}

func (c *CS2LGuildMobilizationBeCancelTaskCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GuildMobilizationBeCancelTask{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GuildMobilizationBeCancelTask Unmarshal error. uid:%d err:%s", cmsg.Uid, err)
		return false
	}

	l4g.Debugf("user: %d CS2L_GuildMobilizationBeCancelTask: cmsg:%s", cmsg.Uid, cmsg)
	// edit here
	if cmsg.Ret != uint32(ret.RET_OK) {
		return false
	}

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("user:%d CS2L_GuildMobilizationBeCancelTask: not open", cmsg.Uid)
		return false
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_GuildMobilizationBeCancelTask failed, guildManager not exist", cmsg.Uid)
		return false
	}

	guildUser := guildM.GetGuildUser(cmsg.Uid)
	if guildUser != nil {
		var cancelTask []*cl.GuildMobilizationTaskProgress
		cancelTask = append(cancelTask, &cl.GuildMobilizationTaskProgress{
			TaskId: cmsg.TaskId,
		})
		guildUser.OnTaskFinishBack(roundInfo, cancelTask)
		character.GuildMobilizationTaskBeCancel(c.Srv, c.Srv.UserM().GetUser(cmsg.Uid))
	} else {
		uids := []uint64{cmsg.Uid}
		c.AsyncGetGuildUsers(uids, &AsyncL2CGuildMobilizationBeCancelTaskReq{
			cmsg: cmsg,
		}, nil)
	}

	return true
}

type AsyncL2CGuildMobilizationBeCancelTaskReq struct {
	cmsg *l2c.CS2L_GuildMobilizationBeCancelTask
}

func (ar *AsyncL2CGuildMobilizationBeCancelTaskReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("AsyncL2CGuildMobilizationBeCancelTaskReq resp error. retCode:%d", retCode)
		return false
	}

	dbGuildUsers, ok := data.(map[uint64]*db.GuildUser)
	if !ok {
		l4g.Errorf("AsyncL2CGuildMobilizationBeCancelTaskReq: data to guildUser error")
		return false
	}

	cmsg := ar.cmsg

	now := time.Now().Unix()
	roundInfo := goxml.GetData().GuildMobilizationInfoM.GetByTime(now)
	if roundInfo == nil {
		l4g.Errorf("AsyncL2CGuildMobilizationBeCancelTaskReq user:%d GuildMobilization not open", cmsg.Uid)
		return false
	}

	guildM, ok := srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("AsyncL2CGuildMobilizationBeCancelTaskReq user: %d  failed, guildManager not exist", cmsg.Uid)
		return false
	}

	for _, dbGuildUser := range dbGuildUsers {
		guildM.LoadGuildUser(dbGuildUser)
	}

	guildUser := guildM.GetGuildUser(cmsg.Uid)
	if guildUser != nil {
		var cancelTask []*cl.GuildMobilizationTaskProgress
		cancelTask = append(cancelTask, &cl.GuildMobilizationTaskProgress{
			TaskId: cmsg.TaskId,
		})
		guildUser.OnTaskFinishBack(roundInfo, cancelTask)
		character.GuildMobilizationTaskBeCancel(srv, srv.UserM().GetUser(cmsg.Uid))
	} else {
		l4g.Errorf("AsyncL2CGuildMobilizationBeCancelTaskReq user:%d get guild user failed", cmsg.Uid)
		return false
	}

	return true
}
