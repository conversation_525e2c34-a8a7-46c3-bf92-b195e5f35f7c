package guild

import (
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/p2l"
	"app/protos/in/r2l"
	"context"
	"strings"
	"unicode/utf8"

	"gitlab.qdream.com/kit/sea/util"

	"app/goxml"
	"app/logic/activity"
	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"

	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildList), &C2LGuildListCommand{}, state)                     //获取公会列表
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildRank), &C2LGuildRankCommand{}, state)                     //公会排行
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildCreate), &C2LGuildCreateCommand{}, state)                 //创建公会
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetMyInfo), &C2LGuildGetMyInfoCommand{}, state)           //获取自己的公会信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMainInfo), &C2LGuildMainCommand{}, state)                 // 获取主信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildLogList), &C2LGuildLogListCommand{}, state)               //获取公会日志
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildManagerMember), &C2LGuildManagerMemberCommand{}, state)   //公会成员管理(任命为会长、任命为副会长(撤销)、踢出公会)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildModifyInfo), &C2LGuildModifyInfoCommand{}, state)         //修改公会信息(头像、设置加入条件、宣言)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildSetName), &C2LGuildSetNameCommand{}, state)               //修改昵称
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildModifyNotice), &C2LGuildModifyNoticeCommand{}, state)     //修改公告
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildApplyList), &C2LGuildApplyListCommand{}, state)           //获取公会申请列表(会长or副会长)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildApplyRatify), &C2LGuildApplyRatifyCommand{}, state)       //审核公会申请
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildUserApply), &C2LGuildUserApplyCommand{}, state)           //玩家申请(申请、取消申请、快速加入)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildCombineApply), &C2LGuildCombineApplyCommand{}, state)     //公会合并申请(申请合并、邀请被合并)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildCombineCheck), &C2LGuildCombineCheckCommand{}, state)     //公会合并确认
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildCombineRatify), &C2LGuildCombineRatifyCommand{}, state)   //公会合并审核
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildSendMail), &C2LGuildSendMailCommand{}, state)             //发送公会邮件(会长or副会长)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildQuit), &C2LGuildQuitCommand{}, state)                     //退出公会
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDisband), &C2LGuildDisbandCommand{}, state)               //解散公会
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildSearch), &C2LGuildSearchCommand{}, state)                 //查找公会
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetMembers), &C2LGuildGetMembersCommand{}, state)         //获取公会成员
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetDeclaration), &C2LGuildGetDeclarationCommand{}, state) //获取公会宣言
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildSendRecruitMsg), &C2LGuildSendRecruitMsgCommand{}, state) //发送公会招募信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDonate), &C2LGuildDonateCommand{}, state)                 //公会捐赠
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetDonateAward), &C2LGuildGetDonateAwardCommand{}, state) //公会捐赠领奖
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDonateLogList), &C2LGuildDonateLogListCommand{}, state)   //公会捐赠日志
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildListGetDetail), &C2LGuildListGetDetailCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetBadgeList), &C2LGuildGetBadgeListCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetDivisionAwardInfo), &C2LGuildGetDivisionAwardInfoCommand{}, state) //获取段位奖励相关信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildGetMedals), &C2LGuildGetMedalsCommand{}, state)                       // 获取功勋数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMedalLike), &C2LGuildMedalLikeCommand{}, state)                       // 功勋送花
}

type C2LGuildListCommand struct {
	base.UserCommand
}

func (c *C2LGuildListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildListCommand) Error(msg *cl.L2C_GuildList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildList, msg)
	return false
}

func (c *C2LGuildListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildList: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildList{
		Ret:         uint32(cret.RET_OK),
		Page:        cmsg.Page,
		Screen:      cmsg.Screen,
		Language:    cmsg.Language,
		JoinType:    cmsg.JoinType,
		MemberCount: cmsg.MemberCount,
		Label:       cmsg.Label,
		Level:       cmsg.Level,
		Division:    cmsg.Division,
		Transfer:    cmsg.Transfer,
		Combine:     cmsg.Combine,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildList failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildList: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if cmsg.Page == 0 {
		l4g.Errorf("user: %d C2L_GuildList: req page error. page:%d", c.Msg.UID, cmsg.Page)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !cmsg.Screen && cmsg.Language == "" {
		l4g.Errorf("user: %d C2L_GuildList: language is nil. language:%s", c.Msg.UID, cmsg.Language)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	l2cMsg := &l2c.L2C_GuildList{
		Page:     cmsg.Page,
		Screen:   cmsg.Screen,
		Language: cmsg.Language,
		JoinType: cmsg.JoinType,
		Label:    cmsg.Label,
		Division: cmsg.Division,
		Transfer: cmsg.Transfer,
		Combine:  cmsg.Combine,
	}

	if cmsg.Screen && !c.checkScreenParams(cmsg, l2cMsg) {
		l4g.Errorf("user: %d C2L_GuildList screenParams error. cmsg:%+v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	l2cMsg.UserLv = c.User.Level()
	l2cMsg.UserPower = c.User.GetCrystalPower()
	if l2cMsg.UserPower == 0 {
		l2cMsg.UserPower = c.User.Power()
	}
	if guildUser := guildM.GetGuildUser(c.User.ID()); guildUser != nil {
		l2cMsg.LeaveCnt = guildUser.FlushGuildLevelCnt()
		l2cMsg.LeaveTm = guildUser.LeaveTm()
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildList, c.Msg.UID, l2cMsg) {
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

func (c *C2LGuildListCommand) checkScreenParams(cmsg *cl.C2L_GuildList, l2cMsg *l2c.L2C_GuildList) bool {

	if cmsg.JoinType != goxml.GuildJoinOpen && cmsg.JoinType != goxml.GuildJoinNeedApply &&
		cmsg.JoinType != goxml.GuildJoinClose {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: joinType error. joinType:%d",
			c.Msg.UID, cmsg.JoinType)
		return false
	}

	if len(cmsg.MemberCount) != 2 {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: memberCount error. memberCount:%+v",
			c.Msg.UID, cmsg.MemberCount)
		return false
	}

	if len(cmsg.Level) != 2 {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: level error. level:%+v",
			c.Msg.UID, cmsg.Level)
		return false
	}

	if cmsg.Transfer {
		if len(cmsg.ActivityMember) != 2 {
			l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: activityMember error. activityMember:%+v",
				c.Msg.UID, cmsg.ActivityMember)
			return false
		}
		activityMemberMin, activityMemberMax := cmsg.ActivityMember[0], cmsg.ActivityMember[1]
		if activityMemberMin > activityMemberMax || activityMemberMax > goxml.GetData().GuildLevelInfoM.GetMaxMemberCount() || activityMemberMin < 1 {
			l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: activityMember error. activityMember:%+v ",
				c.Msg.UID, cmsg.ActivityMember)
			return false
		}
		l2cMsg.ActivityMemberMin = activityMemberMin
		l2cMsg.ActivityMemberMax = activityMemberMax
	}

	memberMin, memberMax := cmsg.MemberCount[0], cmsg.MemberCount[1]

	levelMin, levelMax := cmsg.Level[0], cmsg.Level[1]

	if memberMin > memberMax || uint32(memberMax) > goxml.GetData().GuildLevelInfoM.GetMaxMemberCount() || memberMin < 1 {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: memberCount error. memberCount:%+v ",
			c.Msg.UID, cmsg.MemberCount)
		return false
	}

	if levelMin > levelMax || goxml.GetData().GuildLevelInfoM.Index(uint32(levelMin)) == nil || goxml.GetData().GuildLevelInfoM.Index(uint32(levelMax)) == nil {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: level error. level:%+v ",
			c.Msg.UID, cmsg.Level)
		return false
	}

	if cmsg.Division != 0 && goxml.GetData().GuildDungeonDivisionInoM.Index(cmsg.Division) == nil {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: division error. division:%d",
			c.Msg.UID, cmsg.Division)
		return false
	}

	if cmsg.Label != 0 && goxml.GetData().GuildLabelInfoM.Index(cmsg.Label) == nil {
		l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: label error. label:%d",
			c.Msg.UID, cmsg.Label)
		return false
	}

	if cmsg.Language != "" {
		if _, exist := goxml.ClientLanguage[cmsg.Language]; !exist {
			l4g.Errorf("user:%d C2LGuildListCommand.checkScreenParams: language error. language:%s",
				c.Msg.UID, cmsg.Language)
			return false
		}
	}

	l2cMsg.LvMin = levelMin
	l2cMsg.LvMax = levelMax
	l2cMsg.MemberMin = memberMin
	l2cMsg.MemberMax = memberMax
	return true
}

type C2LGuildMainCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildMainCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildMainCommand) Error(msg *cl.L2C_GuildMainInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMainInfo, msg)
	return false
}

func (c *C2LGuildMainCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildMainInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMainInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildMainInfo: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_GuildMainInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMainInfo failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMainInfo: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.Msg.UID)

	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMainInfo: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildMainInfo: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	selfMemberInfo := &cr.GuildMember{
		Id:            c.Msg.UID,
		Name:          c.User.Name(),
		Sid:           c.Srv.ServerID(),
		ActivityPoint: guildUser.GetWeeklyActivityPointList(),
		BaseId:        c.User.BaseID(),
		ExpireTime:    c.User.ExpireTime(),
	}

	l2CGuildInfo := &l2c.L2C_GuildInfo{
		Gid:  guild.ID(),
		Self: selfMemberInfo,
	}
	l2CGuildInfo.Power = c.User.GetCrystalPower()
	if l2CGuildInfo.Power == 0 {
		l2CGuildInfo.Power = c.User.Power()
	}

	l2CGuildInfo.Level = c.User.Level()
	l2CGuildInfo.LeaveCnt = guildUser.FlushGuildLevelCnt()
	l2CGuildInfo.LeaveTm = guildUser.LeaveTm()
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildInfo, c.Msg.UID, l2CGuildInfo) {
		l4g.Errorf("[Guild] user:%d C2L_GuildMainInfo: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetMyInfoCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildGetMyInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetMyInfoCommand) Error(msg *cl.L2C_GuildGetMyInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMyInfo, msg)
	return false
}

func (c *C2LGuildGetMyInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetMyInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetMyInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildGetMyInfo: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_GuildGetMyInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetMyInfo failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser != nil {
		smsg.User = guildUser.Flush()
		smsg.User.MedalLiked = c.User.FlushGuildMedalLike()
	}

	if c.User.UserGuild().IsInGuild() {
		guild := guildM.GetGuildByUser(c.User.ID())
		if guild != nil {
			smsg.Guild = guild.Flush()
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMyInfo, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGuildCreateCommand struct {
	base.UserCommand
}

func (c *C2LGuildCreateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildCreateCommand) Error(msg *cl.L2C_GuildCreate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCreate, msg)
	return false
}

/*
TODO
长度问题，中日韩2个长度，其他1个长度。
*/
func checkName(content string, uid uint64, maxLen int) uint32 {
	if len(content) > maxLen*character.MaxCharactUtf8Len {
		l4g.Errorf("checkcontent %d content: %s length error len:%d ", uid, content, len(content))
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	if !utf8.ValidString(content) {
		l4g.Errorf("uid %d content illegal: %s", uid, content)
		return uint32(cret.RET_GUILD_STRING_ILLEGAL_CHARACTER)
	}
	if strings.Trim(content, " ") == "" {
		l4g.Errorf("uid %d content: %s length error, all empty space", uid, content)
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	if length := utf8.RuneCountInString(content); length == 0 || length > maxLen {
		l4g.Errorf("uid %d content: %s length error: %d", uid, content, length)
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	if strings.IndexFunc(content, character.HasIllegalNameRune) != -1 {
		l4g.Errorf("uid %d content illegal: %s", uid, content)
		return uint32(cret.RET_GUILD_STRING_ILLEGAL_CHARACTER)
	}
	return uint32(cret.RET_OK)
}

func CheckString(content string, uid uint64, maxLen int) uint32 {
	if len(content) > maxLen*character.MaxCharactUtf8Len { //  提前检查字节长度
		l4g.Errorf("checkcontent %d content: %s length error len:%d ", uid, content, len(content))
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	if !utf8.ValidString(content) { //  这里有遍历
		l4g.Errorf("uid %d content illegal: %s", uid, content)
		return uint32(cret.RET_GUILD_STRING_ILLEGAL_CHARACTER)
	}
	/*	if strings.Trim(content, " ") == "" {
		l4g.Errorf("uid %d content: %s length error, all empty space", uid, content)
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}*/
	// utf8字符长度
	if length := utf8.RuneCountInString(content); length > maxLen {
		l4g.Errorf("uid %d content: %s length error: %d", uid, content, length)
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	return uint32(cret.RET_OK)
}

// 检查背景是否存在，检查图案是否存在
// 取高16位的背景做检查， 取低16位的图案做检查
func checkGuildBadge(badge uint32) uint32 {
	background := badge >> 16
	if badgeInfo := goxml.GetData().GuildBadgeInfoM.Index(background); badgeInfo == nil ||
		badgeInfo.Type != character.GuildBadgeBackground {
		l4g.Errorf("background not exist, sysId:%d", background)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	icon := badge & 0xFFFF
	if badgeInfo := goxml.GetData().GuildBadgeInfoM.Index(icon); badgeInfo == nil || badgeInfo.Type != character.GuildBadgeIcon {
		l4g.Errorf("icon not exist, sysId:%d", icon)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	return uint32(cret.RET_OK)
}

//nolint:funlen
func (c *C2LGuildCreateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildCreate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCreate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildCreate: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildCreate{
		Ret:         uint32(cret.RET_OK),
		Name:        cmsg.Name,
		Badge:       cmsg.Badge,
		Language:    cmsg.Language,
		JoinType:    cmsg.JoinType,
		LvLimit:     cmsg.LvLimit,
		PowerLimit:  cmsg.PowerLimit,
		Label:       cmsg.Label,
		Declaration: cmsg.Declaration,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if retCode, _ := c.User.CheckResourcesSize(goxml.GetData().GuildConfigInfoM.CreateCost); retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildCreate: check resource error. retCode:%d", c.Msg.UID, retCode)
		return c.Error(smsg, uint32(cret.RET_NOT_ENOUGH_RESOURCES))
	}

	if !checkJoinType(cmsg.JoinType) {
		l4g.Errorf("user: %d C2L_GuildCreate: joinType error. joinType:%d", c.Msg.UID, cmsg.JoinType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildCreate failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildCreate: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser != nil &&
		goxml.GetData().GuildQuitCdInfoM.IsInQuitCd(guildUser.FlushGuildLevelCnt(), 0, guildUser.LeaveTm(), time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildCreate create guild CD ...", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_CREATE_CD))
	}

	if guildUser == nil {
		guildUser = guildM.NewGuildUser(c.User.ID(), c.User.Name())
	}

	guildM.SaveGuildUser(guildUser)

	if c.User.UserGuild().IsInGuild() {
		l4g.Errorf("user: %d C2L_GuildCreate user has guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_HAS_GUILD))
	}

	//检查名字合法
	if ret := checkName(cmsg.Name, c.User.ID(), int(goxml.GetData().GuildConfigInfoM.NameLengthMax)); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildCreate name invalid, len:%d", c.Msg.UID, len(cmsg.Name))
		return c.Error(smsg, ret)
	}

	// 检查宣言
	if ret := CheckString(cmsg.Declaration, c.User.ID(), int(goxml.GetData().GuildConfigInfoM.DeclarationLengthMax)); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildCreate declaration invalid, declaration:%s", c.Msg.UID, cmsg.Declaration)
		return c.Error(smsg, ret)
	}

	// 检查门槛
	if cmsg.LvLimit > 120 || cmsg.PowerLimit > 1000000000000 {
		l4g.Errorf("user: %d C2L_GuildCreate lvLimit invalid, lvLimit:%d", c.Msg.UID, cmsg.LvLimit)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if _, exist := goxml.ClientLanguage[cmsg.Language]; !exist {
		l4g.Errorf("user: %d C2L_GuildCreate: language invalid. language:%s", c.Msg.UID, cmsg.Language)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//
	if goxml.GetData().GuildLabelInfoM.Index(cmsg.Label) == nil {
		l4g.Errorf("user: %d C2L_GuildCreate: label invalid. label:%d", c.Msg.UID, cmsg.Label)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Badge != 0 {
		if retN := checkGuildBadge(cmsg.Badge); retN != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_GuildCreate badge:%d check error", c.Msg.UID, cmsg.Badge)
			return c.Error(smsg, retN)
		}
	}

	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		var checks []*p2l.SensitiveWordCheckReq
		checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Name, c.Srv.ServerID(), character.SensitiveGuildName, platformConfig.Channel))
		if len(cmsg.Declaration) != 0 {
			checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Declaration, c.Srv.ServerID(), character.SensitiveGuildDeclaration, platformConfig.Channel))
		}
		base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LGuildCreateReq{
			UserName: c.User.Name(),
			Smsg:     smsg,
		}, checks, nil)
		return true
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildCreate, c.Msg.UID, &l2c.L2C_GuildCreate{
		Name:        cmsg.Name,
		Badge:       cmsg.Badge,
		Language:    cmsg.Language,
		JoinType:    cmsg.JoinType,
		LvLimit:     cmsg.LvLimit,
		PowerLimit:  cmsg.PowerLimit,
		Label:       cmsg.Label,
		Declaration: cmsg.Declaration,
		NewGuildId:  c.Srv.CreateUniqueID(),
		UserName:    c.User.Name(),
	}) {
		l4g.Errorf("[Guild] user:%d cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return true
}

func checkJoinType(joinType uint32) bool {
	if joinType != goxml.GuildJoinOpen && joinType != goxml.GuildJoinNeedApply && joinType != goxml.GuildJoinClose {
		return false
	}
	return true
}

type C2LGuildLogListCommand struct {
	base.UserCommand
}

func (c *C2LGuildLogListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildLogListCommand) Error(msg *cl.L2C_GuildLogList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildLogList, msg)
	return false
}

func (c *C2LGuildLogListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildLogList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildLogList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildLogList: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildLogList{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildLogList: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildLogList: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildLogList: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildLogList: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildLogList, c.Msg.UID, &l2c.L2C_GuildLogList{}) {
		l4g.Errorf("[Guild] user:%d C2L_GuildLogList: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildManagerMemberCommand struct {
	base.UserCommand
}

func (c *C2LGuildManagerMemberCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildManagerMemberCommand) Error(msg *cl.L2C_GuildManagerMember, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildManagerMember, msg)
	return false
}

func (c *C2LGuildManagerMemberCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildManagerMember{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildManagerMember Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildManagerMember: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildManagerMember{
		Ret:  uint32(cret.RET_OK),
		Id:   cmsg.Id,
		Type: cmsg.Type,
	}

	if cmsg.Id == 0 {
		l4g.Errorf("user: %d C2L_GuildManagerMember: param id is 0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if cmsg.Type != character.GuildManagerLeader && cmsg.Type != character.GuildManagerDeputy &&
		cmsg.Type != character.GuildManagerKick && cmsg.Type != character.GuildManagerRegiment {
		l4g.Errorf("user: %d C2L_GuildManagerMember: param type illegal, type:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildManagerMember: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if c.User.ID() == cmsg.Id {
		l4g.Errorf("user: %d C2L_GuildManagerMember: can't manage self. requestId:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildManagerMember: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !c.User.UserGuild().IsInGuild() {
		l4g.Errorf("user: %d C2L_GuildManagerMember:  guildUser error. requestId:%d",
			c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildManagerMember: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildManagerMember: not manager.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	myGrade := guildUser.Grade()

	switch cmsg.Type {
	case character.GuildManagerLeader: // 设为会长
		if myGrade != character.GuildGradeLeader {
			l4g.Errorf("user: %d C2L_GuildManagerMember: not leader. guild:%d ",
				c.Msg.UID, c.User.UserGuild().GuildID())
			return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
		}
	case character.GuildManagerDeputy: // 设为副会长or撤销
		if myGrade != character.GuildGradeLeader {
			l4g.Errorf("user: %d C2L_GuildManagerMember: not leader. guild:%d ",
				c.Msg.UID, c.User.UserGuild().GuildID())
			return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
		}
	case character.GuildManagerKick: // 踢出公会
		if goxml.GuildDungeonResetting(goxml.GetData(), time.Now().Unix()) {
			l4g.Errorf("user: %d C2L_GuildManagerMember: guildDungeon resetting .", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING_LIMIT_KICK_MEMBER))
		}
	case character.GuildManagerRegiment: // 设为副团长or撤销
		if myGrade != character.GuildGradeLeader && myGrade != character.GuildGradeDeputy {
			l4g.Errorf("user: %d C2L_GuildManagerMember: not leader. guild:%d ",
				c.Msg.UID, c.User.UserGuild().GuildID())
			return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildManagerMember, c.User.ID(), &l2c.L2C_GuildManagerMember{
		Id:   cmsg.Id,
		Type: cmsg.Type,
	}) {
		l4g.Errorf("[Guild] user:%d C2L_GuildManagerMember: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildModifyInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildModifyInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildModifyInfoCommand) Error(msg *cl.L2C_GuildModifyInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyInfo, msg)
	return false
}

func (c *C2LGuildModifyInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildModifyInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildModifyInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildModifyInfo: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildModifyInfo{
		Ret:         uint32(cret.RET_OK),
		Badge:       cmsg.Badge,
		JoinType:    cmsg.JoinType,
		Declaration: cmsg.Declaration,
		Language:    cmsg.Language,
		LvLimit:     cmsg.LvLimit,
		PowerLimit:  cmsg.PowerLimit,
		Label:       cmsg.Label,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: not manager. guild:%d", c.Msg.UID, c.User.UserGuild().GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}

	if goxml.GetData().GuildLabelInfoM.Index(cmsg.Label) == nil {
		l4g.Errorf("user: %d C2L_GuildModify: label not exist. label:%d", c.Msg.UID, cmsg.Label)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//if retN := checkGuildBadge(cmsg.Badge); cmsg.Badge != guild.GetBadge() && retN != uint32(cret.RET_OK) {
	if cmsg.Badge != 0 {
		if retN := checkGuildBadge(cmsg.Badge); retN != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_GuildModify: badge check error. badge:%d", c.Msg.UID, cmsg.Badge)
			return c.Error(smsg, retN)
		}
	}

	// 检查门槛
	if cmsg.LvLimit > goxml.GetData().PlayerLevelInfoM.MaxLevel() || cmsg.PowerLimit > 1000000000000 {
		l4g.Errorf("user: %d C2L_GuildModify lvLimit powerLimit invalid, lvLimit:%d powerLimit:%d",
			c.Msg.UID, cmsg.LvLimit, cmsg.PowerLimit)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !checkJoinType(cmsg.JoinType) {
		l4g.Errorf("user: %d C2L_GuildModify: joinType error. joinType:%d", c.Msg.UID, cmsg.JoinType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 检查宣言是否符合规范
	retn := CheckString(cmsg.Declaration, c.User.ID(), int(goxml.GetData().GuildConfigInfoM.DeclarationLengthMax))
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: declaration error. guild:%d", c.Msg.UID, c.User.UserGuild().GuildID())
		return c.Error(smsg, retn)
	}

	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		if len(cmsg.Declaration) != 0 {
			var checks []*p2l.SensitiveWordCheckReq
			checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Declaration, c.Srv.ServerID(), character.SensitiveGuildDeclaration, platformConfig.Channel))
			base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LGuildModifyInfoReq{
				Smsg: smsg,
			}, checks, nil)
			return true
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildModifyInfo, c.Msg.UID, &l2c.L2C_GuildModifyInfo{
		Badge:       cmsg.Badge,
		JoinType:    cmsg.JoinType,
		Declaration: cmsg.Declaration,
		Language:    cmsg.Language,
		LvLimit:     cmsg.LvLimit,
		PowerLimit:  cmsg.PowerLimit,
		Label:       cmsg.Label,
	}) {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildSetNameCommand struct {
	base.UserCommand
}

func (c *C2LGuildSetNameCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildSetNameCommand) Error(msg *cl.L2C_GuildSetName, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSetName, msg)
	return false
}

func (c *C2LGuildSetNameCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildSetName{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSetName Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildSetName: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildSetName{
		Ret:  uint32(cret.RET_OK),
		Name: cmsg.Name,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildSetName: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSetName. failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildSetName: cross connect error.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildSetName: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildSetName: guildUserNotExist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if guildUser.Grade() != character.GuildGradeLeader {
		l4g.Errorf("user: %d C2L_GuildSetName: not leader. guild:%d grade:%d",
			c.Msg.UID, guild.ID(), guildUser.Grade())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}

	retn := checkName(cmsg.Name, c.Msg.UID, int(goxml.GetData().GuildConfigInfoM.NameLengthMax))
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildSetName: name error. guild:%d ",
			c.Msg.UID, guild.ID())
		return c.Error(smsg, retn)
	}

	if ret, _ := c.User.CheckResourcesSize(goxml.GetData().GuildConfigInfoM.NameChangeCost); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildSetName: resource not enough.", c.Msg.UID)
		return c.Error(smsg, ret)
	}

	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		var checks []*p2l.SensitiveWordCheckReq
		checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Name, c.Srv.ServerID(), character.SensitiveGuildName, platformConfig.Channel))
		base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LGuildSetNameReq{
			Smsg: smsg,
		}, checks, nil)
		return true
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildSetName, c.Msg.UID, &l2c.L2C_GuildSetName{
		Name: cmsg.Name,
	}) {
		l4g.Errorf("user: %d C2L_GuildSetName: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildModifyNoticeCommand struct {
	base.UserCommand
}

func (c *C2LGuildModifyNoticeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildModifyNoticeCommand) Error(msg *cl.L2C_GuildModifyNotice, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyNotice, msg)
	return false
}

func (c *C2LGuildModifyNoticeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildModifyNotice{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildModifyNotice Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildModifyNotice: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildModifyNotice{
		Ret:    uint32(cret.RET_OK),
		Notice: cmsg.Notice,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: not leader. ", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	retn := CheckString(cmsg.Notice, c.Msg.UID, int(goxml.GetData().GuildConfigInfoM.NoticeLengthMax))
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: notice error.", c.Msg.UID)
		return c.Error(smsg, retn)
	}

	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		var checks []*p2l.SensitiveWordCheckReq
		checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Notice, c.Srv.ServerID(), character.SensitiveGuildDeclaration, platformConfig.Channel))
		base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LGuildModifyNoticeReq{
			Smsg: smsg,
		}, checks, nil)
		return true
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildModifyNotice, c.Msg.UID, &l2c.L2C_GuildModifyNotice{
		Notice: cmsg.Notice,
	}) {
		l4g.Errorf("user: %d C2L_GuildModifyNotice: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildApplyListCommand struct {
	base.UserCommand
}

func (c *C2LGuildApplyListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildApplyListCommand) Error(msg *cl.L2C_GuildApplyList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, msg)
	return false
}

func (c *C2LGuildApplyListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildApplyList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildApplyList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildApplyList: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildApplyList{
		Ret:       uint32(cret.RET_OK),
		ApplyType: cmsg.ApplyType,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildApplyList: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildApplyList: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildApplyList: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildApplyList: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildApplyList: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildApplyList: not manager. guild:%d", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	logic2Cross := &l2c.L2C_GuildApplyList{
		ApplyType: cmsg.ApplyType,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildApplyList, c.Msg.UID, logic2Cross) {
		l4g.Errorf("user: %d C2L_GuildApplyList: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildApplyRatifyCommand struct {
	base.UserCommand
}

func (c *C2LGuildApplyRatifyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildApplyRatifyCommand) Error(msg *cl.L2C_GuildApplyRatify, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyRatify, msg)
	return false
}

func (c *C2LGuildApplyRatifyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildApplyRatify{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildApplyRatify Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildApplyRatify: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildApplyRatify{
		Ret:    uint32(cret.RET_OK),
		Id:     cmsg.Id,
		Accept: cmsg.Accept,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if uint32(len(cmsg.Id)) == 0 || uint32(len(cmsg.Id)) > goxml.GetData().GuildConfigInfoM.ApplyingReceiveMax {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: request ids num error. num:%d", c.Msg.UID, len(cmsg.Id))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: not manager. guild:%d", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildApplyRatify, c.Msg.UID, &l2c.L2C_GuildApplyRatify{
		Id:     cmsg.Id,
		Accept: cmsg.Accept,
	}) {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildUserApplyCommand struct {
	base.UserCommand
}

func (c *C2LGuildUserApplyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildUserApplyCommand) Error(msg *cl.L2C_GuildUserApply, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildUserApply, msg)
	return false
}

func (c *C2LGuildUserApplyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildUserApply{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildUserApply Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildUserApply: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildUserApply{
		Ret:  uint32(cret.RET_OK),
		Id:   cmsg.Id,
		Type: cmsg.Type,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildUserApply: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildUserApply: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if guildM.GetGuildByUser(c.Msg.UID) != nil && cmsg.Type != character.GuildUserQuickTransfer {
		l4g.Errorf("user: %d C2L_GuildUserApply: user has guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_HAS_GUILD))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildUserApply: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		guildUser = guildM.NewGuildUser(c.Msg.UID, c.User.Name())
	}
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildUserApply: guildUser create error.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	//
	//guild := guildM.GetGuildByID(cmsg.Id)
	//if guild == nil && cmsg.Type != character.GuildUserQuickJoin {
	//	l4g.Errorf("user: %d C2L_GuildUserApply: guild not exist. guildId:%d", c.Msg.UID, cmsg.Id)
	//	return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	//}

	oldApplyIds := guildUser.ApplyIds()

	if cmsg.Type != character.GuildUserCancelApply &&
		goxml.GetData().GuildQuitCdInfoM.IsInQuitCd(guildUser.FlushGuildLevelCnt(), cmsg.Id, guildUser.LeaveTm(), time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildUserApply: join in guild CD ...", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_JOIN_CD))
	}

	switch cmsg.Type {
	case character.GuildUserJoinIn: // 加入
	case character.GuildUserApply: // 申请
		if guildUser.ApplyIdsCnt() >= goxml.GetData().GuildConfigInfoM.ApplyingCountMax {
			l4g.Errorf("user: %d C2L_GuildUserApply: user apply list count limit.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		repeat := guildUser.CheckApplyRepeat(cmsg.Id)
		if repeat {
			l4g.Errorf("user: %d C2L_GuildUserApply: user apply repeat. guildId:%d", c.Msg.UID, cmsg.Id)
			return c.Error(smsg, uint32(cret.RET_GUILD_APPLY_REPEAT))
		}
	case character.GuildUserCancelApply: // 取消申请
	case character.GuildUserQuickJoin: // 快速加入
	case character.GuildUserQuickTransfer: // 快速转会
	default:
		l4g.Errorf("user: %d C2L_GuildUserApply: request type error. cmsg.Type:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	msgToCross := &l2c.L2C_GuildUserApply{
		Id:          cmsg.Id,
		Type:        cmsg.Type,
		Name:        c.User.Name(),
		OldApplyIds: oldApplyIds,
		Level:       c.User.Level(),
		Power:       c.User.Power(),
		Language:    c.User.GetLang(),
		LeaveCnt:    guildUser.FlushGuildLevelCnt(),
		LeaveTm:     guildUser.LeaveTm(),
	}
	if c.User.GetCrystalPower() > 0 {
		msgToCross.Power = c.User.GetCrystalPower()
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildUserApply, c.Msg.UID, msgToCross) {
		l4g.Errorf("user: %d C2L_GuildUserApply: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildCombineApplyCommand struct {
	base.UserCommand
}

func (c *C2LGuildCombineApplyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildCombineApplyCommand) Error(msg *cl.L2C_GuildCombineApply, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineApply, msg)
	return false
}

func (c *C2LGuildCombineApplyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildCombineApply{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCombineApply Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildCombineApply: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildCombineApply{
		Ret:  uint32(cret.RET_OK),
		Gid:  cmsg.Gid,
		Type: cmsg.Type,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildCombineApply: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildCombineApply: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildCombineApply: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	// 获取玩家公会
	sourceGuild := guildM.GetGuildByUser(c.Msg.UID)
	if sourceGuild == nil {
		l4g.Errorf("user: %d C2L_GuildCombineApply: user has no guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildCombineApply: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 检查玩家是否是会长
	if !guildUser.IsGuildLeader() {
		l4g.Errorf("user: %d C2L_GuildCombineApply: not leader.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	logic2Cross := &l2c.L2C_GuildCombineApply{
		SourceGid: sourceGuild.ID(),
		TargetGid: cmsg.Gid,
		Type:      cmsg.Type,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildCombineApply, c.Msg.UID, logic2Cross) {
		l4g.Errorf("user: %d C2L_GuildCombineApply: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildCombineCheckCommand struct {
	base.UserCommand
}

func (c *C2LGuildCombineCheckCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildCombineCheckCommand) Error(msg *cl.L2C_GuildCombineCheck, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, msg)
	return false
}

func (c *C2LGuildCombineCheckCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildCombineCheck{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCombineCheck Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildCombineCheck: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildCombineCheck{
		Ret:       uint32(cret.RET_OK),
		SourceGid: cmsg.SourceGid,
		TargetGid: cmsg.TargetGid,
		ApplyType: cmsg.ApplyType,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildCombineCheck: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildCombineCheck: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildCombineCheck: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	// 获取玩家公会
	userGuild := guildM.GetGuildByUser(c.Msg.UID)
	if userGuild == nil {
		l4g.Errorf("user: %d C2L_GuildCombineCheck: user has no guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	if userGuild.ID() != cmsg.SourceGid && userGuild.ID() != cmsg.TargetGid {
		l4g.Errorf("user: %d C2L_GuildCombineCheck: userGuild %d not match, SourceGid %d TargetGid %d", c.Msg.UID, userGuild.ID(), cmsg.SourceGid, cmsg.TargetGid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// // 当前公会为发起方，在logic检查请求是否已过期；当前公会为接收方，在cross检查请求是否已过期
	// if userGuild.ID() == cmsg.SourceGid {
	// 	switch cmsg.ApplyType {
	// 	case uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_REQUEST): // 申请合并
	// 		if !userGuild.IsValidCombineRequestExist(cmsg.TargetGid) {
	// 			l4g.Errorf("user: %d C2L_GuildCombineCheck: no valid request, targetGid", c.Msg.UID, cmsg.TargetGid)
	// 			return c.Error(smsg, uint32(cret.RET_GUILD_COMBINE_APPLY_EXPIRED))
	// 		}
	// 	case uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_INVITE): // 邀请被合并
	// 		if !userGuild.IsValidCombineInviteExist(cmsg.TargetGid) {
	// 			l4g.Errorf("user: %d C2L_GuildCombineCheck: no valid invite, targetGid", c.Msg.UID, cmsg.TargetGid)
	// 			return c.Error(smsg, uint32(cret.RET_GUILD_COMBINE_APPLY_EXPIRED))
	// 		}
	// 	default:
	// 		l4g.Errorf("user: %d C2L_GuildCombineCheck: request type error. cmsg.ApplyType:%d", c.Msg.UID, cmsg.ApplyType)
	// 		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	// 	}
	// }

	logic2Cross := &l2c.L2C_GuildCombineCheck{
		SourceGid: cmsg.SourceGid,
		TargetGid: cmsg.TargetGid,
		ApplyType: cmsg.ApplyType,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildCombineCheck, c.Msg.UID, logic2Cross) {
		l4g.Errorf("user: %d C2L_GuildCombineCheck: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildCombineRatifyCommand struct {
	base.UserCommand
}

func (c *C2LGuildCombineRatifyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildCombineRatifyCommand) Error(msg *cl.L2C_GuildCombineRatify, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineRatify, msg)
	return false
}

func (c *C2LGuildCombineRatifyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildCombineRatify{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCombineRatify Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildCombineRatify: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildCombineRatify{
		Ret:    uint32(cret.RET_OK),
		Gids:   cmsg.Gids,
		Accept: cmsg.Accept,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: not manager. guild:%d", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	if len(cmsg.Gids) == 0 {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: invalid guild num %d", c.Msg.UID, len(cmsg.Gids))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Accept {
		// 检查同意合并的公会数量是否正常
		if len(cmsg.Gids) != 1 {
			l4g.Errorf("user: %d C2L_GuildCombineRatify: accept err, guild num %d", c.Msg.UID, len(cmsg.Gids))
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		// 请求类型需要是申请合并或者邀请被合并
		if cmsg.ApplyType != uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_REQUEST) && cmsg.ApplyType != uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_INVITE) {
			l4g.Errorf("user: %d C2L_GuildCombineRatify: invalid applyType %d", c.Msg.UID, cmsg.ApplyType)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildCombineRatify, c.Msg.UID, &l2c.L2C_GuildCombineRatify{
		Gids:      cmsg.Gids,
		ApplyType: cmsg.ApplyType,
		Accept:    cmsg.Accept,
	}) {
		l4g.Errorf("user: %d C2L_GuildCombineRatify: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildSendMailCommand struct {
	base.UserCommand
}

func (c *C2LGuildSendMailCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildSendMailCommand) Error(msg *cl.L2C_GuildSendMail, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendMail, msg)
	return false
}

func (c *C2LGuildSendMailCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildSendMail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSendMail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildSendMail: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildSendMail{
		Ret:     uint32(cret.RET_OK),
		Title:   cmsg.Title,
		Content: cmsg.Content,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildSendMail: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	retn := CheckString(cmsg.Title, c.User.ID(), int(goxml.GetData().GuildConfigInfoM.MailTitleLengthMax))
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildSendMail: title error. title:%s", c.Msg.UID, cmsg.Title)
		return c.Error(smsg, retn)
	}

	retn = CheckString(cmsg.Content, c.User.ID(), int(goxml.GetData().GuildConfigInfoM.MailContentLengthMax))
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildSendMail: content error. content:%s", c.Msg.UID, cmsg.Content)
		return c.Error(smsg, retn)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSendMail: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildSendMail: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildSendMail: guild is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildSendMail: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildSendMail: not manager. guildId:%d", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}
	if guildUser.SendMailTm()+goxml.GetData().GuildConfigInfoM.MailSendDelay >= time.Now().Unix() {
		l4g.Errorf("user: %d C2L_GuildSendMail: guild send mail CD ...", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_SEND_MAIL_CD))
	}
	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		var checks []*p2l.SensitiveWordCheckReq
		if len(cmsg.Title) != 0 {
			checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Title, c.Srv.ServerID(), character.SensitiveGuildMail, platformConfig.Channel))
		}
		if len(cmsg.Content) != 0 {
			checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Content, c.Srv.ServerID(), character.SensitiveGuildMail, platformConfig.Channel))
		}
		if len(checks) > 0 {
			base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LGuildSendMailReq{
				Smsg: smsg,
			}, checks, nil)
			return true
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildSendMail, c.Msg.UID, &l2c.L2C_GuildSendMail{
		Title:   cmsg.Title,
		Content: cmsg.Content,
		Name:    c.User.Name(),
	}) {
		l4g.Errorf("user: %d C2L_GuildSendMail: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildQuitCommand struct {
	base.UserCommand
}

func (c *C2LGuildQuitCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildQuitCommand) Error(msg *cl.L2C_GuildQuit, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildQuit, msg)
	return false
}

func (c *C2LGuildQuitCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildQuit{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildQuit Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildQuit: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildQuit{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildQuit: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildQuit: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildQuit: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildQuit: failed, guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildQuit: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if guildUser.Grade() == character.GuildGradeLeader { // 会长
		l4g.Errorf("user: %d C2L_GuildQuit: user is leader. guild:%d",
			c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildQuit, c.Msg.UID, &l2c.L2C_GuildQuit{
		Name: c.User.Name(),
	}) {
		l4g.Errorf("user: %d C2L_GuildQuit: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildDisbandCommand struct {
	base.UserCommand
}

func (c *C2LGuildDisbandCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDisbandCommand) Error(msg *cl.L2C_GuildDisband, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDisband, msg)
	return false
}

func (c *C2LGuildDisbandCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDisband{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDisband Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDisband: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDisband{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDisband: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDisband: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDisband: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDisband: failed, guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.Msg.UID)
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDisband: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if guildUser.Grade() != character.GuildGradeLeader {
		l4g.Errorf("user: %d C2L_GuildDisband: not leader. guild:%d",
			c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDisband, c.Msg.UID, &l2c.L2C_GuildDisband{
		Name: c.User.Name(),
	}) {
		l4g.Errorf("user: %d C2L_GuildDisband: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildSearchCommand struct {
	base.UserCommand
}

func (c *C2LGuildSearchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildSearchCommand) Error(msg *cl.L2C_GuildSearch, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSearch, msg)
	return false
}

func (c *C2LGuildSearchCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildSearch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSearch Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildSearch: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildSearch{
		Ret:       uint32(cret.RET_OK),
		Id:        cmsg.Id,
		Name:      cmsg.Name,
		IsCombine: cmsg.IsCombine,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildSearch: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSearch: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildSearch: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if cmsg.Id == 0 && cmsg.Name == "" {
		l4g.Errorf("user: %d C2L_GuildSearch: request param error", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	reqMsg := &l2c.L2C_GuildSearch{
		Id:        cmsg.Id,
		Name:      cmsg.Name,
		IsCombine: cmsg.IsCombine,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildSearch, c.Msg.UID, reqMsg) {
		l4g.Errorf("user: %d C2L_GuildSearch: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetMembersCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetMembersCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetMembersCommand) Error(msg *cl.L2C_GuildGetMembers, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMembers, msg)
	return false
}

func (c *C2LGuildGetMembersCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetMembers{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetMembers Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildGetMembers: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildGetMembers{
		Ret: uint32(cret.RET_OK),
		Id:  cmsg.Id,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildGetMembers: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetMembers: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildGetMembers: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetMembers, c.Msg.UID, &l2c.L2C_GuildGetMembers{
		Id: cmsg.Id,
	}) {
		l4g.Errorf("user: %d C2L_GuildGetMembers: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetDeclarationCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetDeclarationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetDeclarationCommand) Error(msg *cl.L2C_GuildGetDeclaration, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDeclaration, msg)
	return false
}

func (c *C2LGuildGetDeclarationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetDeclaration{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetDeclaration Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildGetDeclaration: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildGetDeclaration{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildGetDeclaration: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetDeclaration: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildGetDeclaration: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByID(cmsg.Id)
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildGetDeclaration: guild not exist. guildId:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetDeclaration, c.Msg.UID, &l2c.L2C_GuildGetDeclaration{
		Id: cmsg.Id,
	}) {
		l4g.Errorf("user: %d C2L_GuildGetDeclaration: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildSendRecruitMsgCommand struct {
	base.UserCommand
}

func (c *C2LGuildSendRecruitMsgCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildSendRecruitMsgCommand) Error(msg *cl.L2C_GuildSendRecruitMsg, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendRecruitMsg, msg)
	return false
}

func (c *C2LGuildSendRecruitMsgCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildSendRecruitMsg{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSendRecruitMsg Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildSendRecruitMsg: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildSendRecruitMsg{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_RECRUIT), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByID(c.User.GuildID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: guild not exist. guildId:%d", c.Msg.UID, c.User.GuildID())
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}
	now := time.Now().Unix()
	if guildUser.GetRecruitCdTime() >= now {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: send recruit msg too frequently.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_RECRUIT_CD_NOT_EXPIRE))
	}
	if guildUser.Grade() != character.GuildGradeLeader && guildUser.Grade() != character.GuildGradeDeputy {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: grade is invalid.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_RECRUIT_GRADE_INVALID))
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildSendRecruitMsg, c.Msg.UID, &l2c.L2C_GuildSendRecruitMsg{
		GuildId: c.User.GuildID(),
	}) {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDonateCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildDonateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDonateCommand) Error(msg *cl.L2C_GuildDonate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDonate, msg)
	return false
}

func (c *C2LGuildDonateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDonate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDonate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDonate: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDonate{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DONATE), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDonate: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDonate: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDonate: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByID(c.User.GuildID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDonate: guild not exist. guildId:%d", c.Msg.UID, c.User.GuildID())
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDonate: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	count := guildUser.GetDonateCount()
	donateMaxCount := goxml.GetData().GuildConfigInfoM.GetGuildDonateCount()
	if count >= donateMaxCount {
		l4g.Errorf("user: %d C2L_GuildDonate: donate count:%d more than max:%d.", c.Msg.UID, count, donateMaxCount)
		return c.Error(smsg, uint32(cret.RET_GUILD_DONATE_COUNT_MAX))
	}

	donateInfo := goxml.GetData().GuildDonateInfoM.Index(cmsg.Id)
	if donateInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDonate: donateInfo not exist. id:%d.", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if len(donateInfo.CostClRes) > 0 {
		if retCode, _ := c.User.CheckResourcesSize(donateInfo.CostClRes); retCode != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_GuildDonate: donate resource not enough. ret:%d", c.Msg.UID, retCode)
			return c.Error(smsg, uint32(cret.RET_GUILD_DONATE_COUNT_MAX))
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDonate, c.Msg.UID, &l2c.L2C_GuildDonate{
		Id: cmsg.Id,
	}) {
		l4g.Errorf("user: %d C2L_GuildDonate: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetDonateAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildGetDonateAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetDonateAwardCommand) Error(msg *cl.L2C_GuildGetDonateAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDonateAward, msg)
	return false
}

func (c *C2LGuildGetDonateAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetDonateAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetDonateAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GetDonateAward: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildGetDonateAward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DONATE), c.Srv) {
		l4g.Errorf("user: %d C2L_GetDonateAward: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GetDonateAward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GetDonateAward: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByID(c.User.GuildID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GetDonateAward: guild not exist. guildId:%d", c.Msg.UID, c.User.GuildID())
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GetDonateAward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	if len(cmsg.Ids) == 0 || len(cmsg.Ids) > goxml.GetData().GuildCollectiveDonateInfoM.Len() {
		l4g.Errorf("user: %d C2L_GetDonateAward: client ids lens err", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	repeatCheck := make(map[uint32]struct{}, len(cmsg.Ids))
	//totalAward := make([]*cl.Resource, 0, len(cmsg.Ids)*2)
	for _, v := range cmsg.Ids {
		_, exist := repeatCheck[v]
		if exist {
			l4g.Errorf("user: %d C2L_GetDonateAward: client id:%d is repeat", c.Msg.UID, v)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatCheck[v] = struct{}{}

		if guildUser.IsDonateAwards(v) {
			l4g.Errorf("user: %d C2L_GetDonateAward: client id:%d info is repeated", c.Msg.UID, v)
			return c.Error(smsg, uint32(cret.RET_GUILD_DONATE_GET_AWARD_REPEATED))
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetDonateAward, c.Msg.UID, &l2c.L2C_GuildGetDonateAward{
		Ids: cmsg.Ids,
	}) {
		l4g.Errorf("user: %d C2L_GetDonateAward: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildDonateLogListCommand struct {
	base.UserCommand
}

func (c *C2LGuildDonateLogListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDonateLogListCommand) Error(msg *cl.L2C_GuildDonateLogList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDonateLogList, msg)
	return false
}

func (c *C2LGuildDonateLogListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDonateLogList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDonateLogList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDonateLogList: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDonateLogList{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDonateLogList: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDonateLogList: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildLogList: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDonateLogList: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDonateLogList, c.Msg.UID, &l2c.L2C_GuildDonateLogList{}) {
		l4g.Errorf("user: %d C2L_GuildDonateLogList: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGuildRankCommand struct {
	base.UserCommand
}

func (c *C2LGuildRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildRankCommand) Error(msg *cl.L2C_GuildRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildRank, msg)
	return false
}

func (c *C2LGuildRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildRank: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildRank{
		Ret:    uint32(cret.RET_OK),
		RankId: cmsg.RankId,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildRank: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildRank failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildRank: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	selfId := uint64(0)

	if cmsg.RankId == goxml.GuildDungeonUserDamageRankId {
		selfId = c.User.ID()
	} else {
		guild := guildM.GetGuildByUser(c.Msg.UID)
		if guild != nil {
			selfId = guild.ID()
		}
	}

	if _, exist := goxml.GuildRank[cmsg.RankId]; !exist {
		l4g.Errorf("user: %d C2L_GuildRank: client ids lens err", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	rankInfo := goxml.GetData().RankingInfoM.Index(cmsg.RankId)
	if rankInfo == nil {
		l4g.Errorf("user: %d C2L_GuildRank: client ids lens err", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if cmsg.RankId == goxml.GuildLevelRankID || cmsg.RankId == goxml.GuildDungeonChapterRankId ||
		cmsg.RankId == goxml.GuildDungeonUserDamageRankId {
		getRankList := &l2c.L2C_RankGetList{
			RankId: cmsg.RankId,
			// ResetTime:
			SelfId:    selfId,
			BeginRank: 1,
			EndRank:   rankInfo.ShowCount,
		}

		if cmsg.RankId == goxml.GuildDungeonChapterRankId || cmsg.RankId == goxml.GuildDungeonUserDamageRankId {
			getRankList.ResetTime = uint64(goxml.GetGuildDungeonWeeklyResetTime(time.Now().Unix())) - util.WeekSecs
		}

		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_RankGetList, c.Msg.UID, getRankList) {
			l4g.Errorf("user: %d C2L_GuildRank: cross maintain", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}
		return true
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildRank, c.Msg.UID, &l2c.L2C_GuildRank{
		RankId: cmsg.RankId,
	}) {
		l4g.Errorf("user: %d C2L_GuildRank: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildListGetDetailCommand struct {
	base.UserCommand
}

func (c *C2LGuildListGetDetailCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildListGetDetailCommand) Error(msg *cl.L2C_GuildListGetDetail, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildListGetDetail, msg)
	return false
}

func (c *C2LGuildListGetDetailCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildListGetDetail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildListGetDetail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildListGetDetail: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildListGetDetail{
		Ret: uint32(cret.RET_OK),
		Gid: cmsg.Gid,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildListGetDetail: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.Gid == 0 {
		l4g.Errorf("user: %d C2L_GuildListGetDetail: gid is 0.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildListGetDetail: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildListGetDetail: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}
	reqMsg := &l2c.L2C_GuildListGetDetail{
		Gid: cmsg.Gid,
	}
	if cmsg.Partition != 0 && c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)) != cmsg.Partition {
		reqMsg.Sid = c.Srv.ServerID()
		reqMsg.Uid = c.Msg.UID
		if !c.Srv.SendCmdToCrossActivityPartition(uint32(l2c.ACTIVITYID_GUILD), cmsg.Partition, uint32(l2c.ID_MSG_L2C_GuildListGetDetail), reqMsg) {
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		return true
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildListGetDetail, c.Msg.UID, reqMsg) {
		l4g.Errorf("user: %d C2L_GuildListGetDetail: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetBadgeListCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetBadgeListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetBadgeListCommand) Error(msg *cl.L2C_GuildGetBadgeList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetBadgeList, msg)
	return false
}

func (c *C2LGuildGetBadgeListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetBadgeList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetBadgeList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildGetBadgeList: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildGetBadgeList{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildGetBadgeList: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetBadgeList: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildGetBadgeList: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildGetBadgeList: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//if !guildUser.IsGuildManager() {
	//	l4g.Errorf("user: %d C2L_GuildGetBadgeList: not manager. guild:%d", c.Msg.UID, c.User.UserGuild().GuildID())
	//	return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	//}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetBadgeList, c.Msg.UID, &l2c.L2C_GuildGetBadgeList{}) {
		l4g.Errorf("user: %d C2L_GuildGetBadgeList: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetDivisionAwardInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetDivisionAwardInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetDivisionAwardInfoCommand) Error(msg *cl.L2C_GuildGetDivisionAwardInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDivisionAwardInfo, msg)
	return false
}

func (c *C2LGuildGetDivisionAwardInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetDivisionAwardInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetDivisionAwardInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildGetDivisionAwardInfo: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildGetDivisionAwardInfo{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildGetDivisionAwardInfo: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetDivisionAwardInfo: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildGetDivisionAwardInfo: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildGetDivisionAwardInfo: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildGetDivisionAwardInfo: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetDivisionAwardInfo, c.Msg.UID, &l2c.L2C_GuildGetDivisionAwardInfo{}) {
		l4g.Errorf("user: %d C2L_GuildGetDivisionAwardInfo: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildGetMedalsCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetMedalsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildGetMedalsCommand) Error(msg *cl.L2C_GuildGetMedals, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMedals, msg)
	return false
}

func (c *C2LGuildGetMedalsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildGetMedals{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetMedals Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildGetMedals: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildGetMedals{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildGetMedals: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetMedals: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildGetMedals: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildGetMedals: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if guild.GetLevel() < goxml.GetData().GuildConfigInfoM.GetGuildMedalOpenLevel() {
		l4g.Errorf("user: %d C2L_GuildGetMedals: level limit. guildLevel:%d", c.Msg.UID, guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GUILD_MEDAL_NOT_OPEN))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildGetMedals: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetMedals, c.Msg.UID, &l2c.L2C_GuildGetMedals{
		Req: cmsg,
	}) {
		l4g.Errorf("user: %d C2L_GuildGetMedals: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildMedalLikeCommand struct {
	base.UserCommand
}

func (c *C2LGuildMedalLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildMedalLikeCommand) Error(msg *cl.L2C_GuildMedalLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMedalLike, msg)
	return false
}

func (c *C2LGuildMedalLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildMedalLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMedalLike Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildMedalLike: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildMedalLike{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildMedalLike: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMedalLike: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildMedalLike: cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildMedalLike: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if guild.GetLevel() < goxml.GetData().GuildConfigInfoM.GetGuildMedalOpenLevel() {
		l4g.Errorf("user: %d C2L_GuildMedalLike: level limit. guildLevel:%d", c.Msg.UID, guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GUILD_MEDAL_NOT_OPEN))
	}

	costs := goxml.GetData().GuildConfigInfoM.GetGuildMedalLikeCosts()
	// 检查激活道具
	ret, _ := c.User.CheckResourcesSize(costs)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildMedalLike: resource not enough. ret:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildMedalLike: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if c.User.GuildMedalLikeRepeat(cmsg.MedalId, cmsg.Uid) {
		l4g.Errorf("user: %d C2L_GuildMedalLike: like repeat. req:%s", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_GUILD_MEDAL_LIKE_REPEAT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildMedalLike, c.Msg.UID, &l2c.L2C_GuildMedalLike{
		Req:  cmsg,
		Name: c.User.Name(),
	}) {
		l4g.Errorf("user: %d C2L_GuildMedalLike: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	c.User.AddGuildMedalLiked(cmsg.MedalId, cmsg.Uid)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LGuildCreateReq struct {
	UserName string
	Smsg     *cl.L2C_GuildCreate
}

func (ar *AsyncC2LGuildCreateReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LSetNameReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildCreate, ar.Smsg)
		return false
	}
	if !srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildCreate, args.UID, &l2c.L2C_GuildCreate{
		Name:        ar.Smsg.Name,
		Badge:       ar.Smsg.Badge,
		Language:    ar.Smsg.Language,
		JoinType:    ar.Smsg.JoinType,
		LvLimit:     ar.Smsg.LvLimit,
		PowerLimit:  ar.Smsg.PowerLimit,
		Label:       ar.Smsg.Label,
		Declaration: ar.Smsg.Declaration,
		NewGuildId:  srv.CreateUniqueID(),
		UserName:    ar.UserName,
	}) {
		l4g.Errorf("[Guild] user:%d cross maintain", args.UID)
		ar.Smsg.Ret = uint32(cret.RET_CROSS_MAINTAIN)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildCreate, ar.Smsg)
		return false
	}
	return true
}

type AsyncC2LGuildSetNameReq struct {
	Smsg *cl.L2C_GuildSetName
}

func (ar *AsyncC2LGuildSetNameReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LGuildSetNameReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildSetName, ar.Smsg)
		return false
	}
	if !srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildSetName, args.UID, &l2c.L2C_GuildSetName{
		Name: ar.Smsg.Name,
	}) {
		l4g.Errorf("[Guild] user:%d cross maintain", args.UID)
		ar.Smsg.Ret = uint32(cret.RET_CROSS_MAINTAIN)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildSetName, ar.Smsg)
		return false
	}
	return true
}

type AsyncC2LGuildModifyNoticeReq struct {
	Smsg *cl.L2C_GuildModifyNotice
}

func (ar *AsyncC2LGuildModifyNoticeReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LGuildModifyNoticeReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyNotice, ar.Smsg)
		return false
	}
	if !srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildModifyNotice, args.UID, &l2c.L2C_GuildModifyNotice{
		Notice: ar.Smsg.Notice,
	}) {
		l4g.Errorf("[Guild] user:%d cross maintain", args.UID)
		ar.Smsg.Ret = uint32(cret.RET_CROSS_MAINTAIN)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyNotice, ar.Smsg)
		return false
	}
	return true
}

type AsyncC2LGuildModifyInfoReq struct {
	Smsg *cl.L2C_GuildModifyInfo
}

func (ar *AsyncC2LGuildModifyInfoReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LGuildModifyInfoReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyInfo, ar.Smsg)
		return false
	}

	if !srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildModifyInfo, args.UID, &l2c.L2C_GuildModifyInfo{
		Badge:       ar.Smsg.Badge,
		JoinType:    ar.Smsg.JoinType,
		Declaration: ar.Smsg.Declaration,
		Language:    ar.Smsg.Language,
		LvLimit:     ar.Smsg.LvLimit,
		PowerLimit:  ar.Smsg.PowerLimit,
		Label:       ar.Smsg.Label,
	}) {
		l4g.Errorf("user: %d C2L_GuildModifyInfo: cross maintain", args.UID)

		ar.Smsg.Ret = uint32(cret.RET_CROSS_MAINTAIN)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyInfo, ar.Smsg)
		return false
	}
	return true
}

type AsyncC2LGuildSendMailReq struct {
	Smsg *cl.L2C_GuildSendMail
}

func (ar *AsyncC2LGuildSendMailReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LGuildSendMailReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendMail, ar.Smsg)
		return false
	}

	if !srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildSendMail, args.UID, &l2c.L2C_GuildSendMail{

		Title:   ar.Smsg.Title,
		Content: ar.Smsg.Content,
		Name:    args.Caller.Name(),
	}) {
		l4g.Errorf("user: %d C2L_GuildSendMail: cross maintain", args.UID)

		ar.Smsg.Ret = uint32(cret.RET_CROSS_MAINTAIN)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendMail, ar.Smsg)
		return false
	}
	return true
}
