package item

import (
	"app/goxml"
	"context"

	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/mail"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GetMails), &C2LGetMailsCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ReadMail), &C2LReadMailCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_DrawMails), &C2LDrawMailsCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_DeleteMails), &C2LDeleteMailsCommand{}, state)
}

type C2LGetMailsCommand struct {
	base.UserCommand
}

func (c *C2LGetMailsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetMailsCommand) Error(msg *cl.L2C_GetMails, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetMails, msg)
	return false
}

func (c *C2LGetMailsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetMails{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetMails Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user %d GetMails: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_GetMails{
		Ret: uint32(cret.RET_OK),
	}

	mb := c.User.MailBox()
	beDeleteIds := make([]uint64, 0, mb.Count())
	maxNum := goxml.GetData().ConfigInfoM.MailNumLimit
	mb.GetMailsGreaterThanID(cmsg.MaxId, maxNum, func(m *cl.Mail) bool {
		if m.Type == mail.TypeGlobal {
			if c.Srv.UserM().MailBox().IsMailBeGmDel(m.GetId()) { // 全服邮件被删除了
				beDeleteIds = append(beDeleteIds, m.GetId())
				return false
			}
		}
		mClone := m.Clone()
		if mClone.MultiLang {
			mClone.Params = c.Srv.MultiLangM().GetLangs(mClone.Id, c.User.GetLang())
		}
		smsg.Mails = append(smsg.Mails, mClone)
		return true
	})
	if len(beDeleteIds) > 0 {
		c.User.MailBox().Delete(beDeleteIds) // 去掉玩家身上已经被删除的全服邮件
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetMails, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LReadMailCommand struct {
	base.UserCommand
}

func (c *C2LReadMailCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LReadMailCommand) Error(msg *cl.L2C_ReadMail, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ReadMail, msg)
	return false
}

func (c *C2LReadMailCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ReadMail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ReadMail Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user %d ReadMail: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ReadMail{
		Id: cmsg.Id,
	}

	if cmsg.Id == 0 {
		l4g.Errorf("user: %d C2L_ReadMail: no mailID", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	mb := c.User.MailBox()
	m := mb.Get(cmsg.Id)

	if m == nil {
		l4g.Errorf("user: %d C2L_ReadMail: mail is nil, id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_MAIL_HAS_DELETE_OR_EXPIRED))
	}

	if !mail.HasRead(m) {
		m.Flag = uint32(common.MAIL_FLAG_READ)
		mb.OpMail(m, mail.MailOpRead, time.Now().Unix())
		mb.Change([]*cl.Mail{m})
	}
	smsg.Ret = uint32(cret.RET_OK)
	smsg.Flag = m.Flag
	//smsg.Flag = uint32(common.MAIL_FLAG_READ)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ReadMail, smsg)
	c.User.LogReadMail(c.Srv, cmsg.Id, c.User.GuildID(), m)
	return c.ResultOK(smsg.Ret)
}

type C2LDrawMailsCommand struct {
	base.UserCommand
}

func (c *C2LDrawMailsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDrawMailsCommand) Error(msg *cl.L2C_DrawMails, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DrawMails, msg)
	return false
}

func (c *C2LDrawMailsCommand) createMailsAndAwards(mb *mail.Box,
	ids []uint64) ([]*cl.Mail, []*cl.Resource, []*cl.Resource, uint32) {
	awards := make([]*cl.Resource, 0, len(ids))
	webLargeRechargeAwards := make([]*cl.Resource, 0, len(ids)) // 官网大额充值的奖励
	ms := make([]*cl.Mail, 0, len(ids))
	uniq := make(map[uint64]struct{})
	now := time.Now().Unix()

	for _, id := range ids {
		if _, exist := uniq[id]; exist {
			return nil, nil, nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
		}
		uniq[id] = struct{}{}
		m := mb.Get(id)
		if m == nil || !mail.HasAwards(m, now) {
			l4g.Errorf("user %d request error, mail id: %d , is nil:%t", c.Msg.UID, id, (m == nil))
			return nil, nil, nil, uint32(cret.RET_MAIL_HAS_DELETE_OR_EXPIRED)
		}
		mailAwardsClone := make([]*cl.Resource, 0)

		createTime := m.CreateTime
		if m.GenTime > 0 && m.GenTime < createTime {
			createTime = m.GenTime
		}
		seasonExpire := goxml.IsSeasonResourceExpire(createTime, now)
		for _, mailAward := range m.Awards {
			if seasonExpire && goxml.IsSeasonResource(mailAward) {
				continue
			}
			mailAwardClone := mailAward.Clone()
			mailAwardClone.GenTime = m.CreateTime
			mailAwardsClone = append(mailAwardsClone, mailAwardClone)
		}
		if m.Reason == uint32(log.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL) {
			webLargeRechargeAwards = append(webLargeRechargeAwards, mailAwardsClone...)
		} else {
			awards = append(awards, mailAwardsClone...)
		}
		ms = append(ms, m)
	}
	return ms, awards, webLargeRechargeAwards, uint32(cret.RET_OK)
}

func (c *C2LDrawMailsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DrawMails{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DrawMails Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user %d DrawMails: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_DrawMails{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}

	if len(cmsg.Ids) == 0 {
		l4g.Errorf("user: %d C2L_DrawMails: no ids", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	mb := c.User.MailBox()
	ms, awards, webLargeRechargeAwards, result := c.createMailsAndAwards(mb, cmsg.Ids)

	// 赛季装备自动分解
	awards = c.User.SeasonJewelryManager().AutoDecompose(c.Srv, awards, goxml.SeasonJewelryRewardTypeRest)

	if len(webLargeRechargeAwards) > 0 { // 有大额充值奖励的情况，提前判断背包空间
		allAwards := make([]*cl.Resource, 0, len(awards)+len(webLargeRechargeAwards))
		allAwards = append(allAwards, awards...)
		allAwards = append(allAwards, webLargeRechargeAwards...)
		smsg.Ret, _ = c.User.CheckBagsSpace(allAwards)
	}

	smsg.Ret = result
	//奖励
	if smsg.Ret == uint32(cret.RET_OK) {

		if len(awards) > 0 {
			smsg.Ret, awards = c.User.Award(c.Srv, awards, character.AwardTagNone,
				uint32(log.RESOURCE_CHANGE_REASON_DRAW_MAILS), 0)
		}

		if smsg.Ret == uint32(cret.RET_OK) {
			if len(webLargeRechargeAwards) > 0 {
				smsg.Ret, webLargeRechargeAwards = c.User.Award(c.Srv, webLargeRechargeAwards, character.AwardTagNone,
					uint32(log.RESOURCE_CHANGE_REASON_DRAW_MAILS), uint64(log.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL))
				awards = append(awards, webLargeRechargeAwards...) // 合一起，用来传给前端显示
			}

			for _, m := range ms {
				m.Flag = uint32(common.MAIL_FLAG_DRAW)
				mb.OpMail(m, mail.MailOpDraw, time.Now().Unix())
			}
			mb.Change(ms)
			smsg.Flag = uint32(common.MAIL_FLAG_DRAW)
			smsg.Awards = awards

			//行为日志
			mailLogMessage := make([]*log.MailMessageForWm, 0, len(cmsg.Ids))
			logAwards := make(map[uint64][]*cl.Resource)
			for _, id := range cmsg.Ids {
				m := mb.Get(id)
				if m == nil {
					continue
				}
				mailLogMessage = append(mailLogMessage, &log.MailMessageForWm{
					Id:       m.Id,
					MailType: m.Type,
					Title:    getMailTitle(m, c.Srv, c.User.GetLang()),
				})
				if len(m.Awards) > 0 {
					logAwards[m.Id] = m.Awards
				}
			}
			c.User.LogDrawMails(c.Srv, cmsg.Ids, mailLogMessage, logAwards)
		}
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DrawMails, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LDeleteMailsCommand struct {
	base.UserCommand
}

func (c *C2LDeleteMailsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDeleteMailsCommand) Error(msg *cl.L2C_DeleteMails, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DeleteMails, msg)
	return false
}

func (c *C2LDeleteMailsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DeleteMails{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DeleteMails Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user %d DeleteMails: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_DeleteMails{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}

	if len(cmsg.Ids) == 0 {
		l4g.Errorf("user: %d C2L_DeleteMails: no ids", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	mb := c.User.MailBox()
	if len(cmsg.Ids) > mb.Count() {
		l4g.Errorf("user: %d C2L_DeleteMails: too many ids", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	uniq := make(map[uint64]struct{}, len(cmsg.Ids))
	for _, id := range cmsg.Ids {
		if _, ok := uniq[id]; ok {
			l4g.Errorf("user: %d C2L_DeleteMails: id repeat, id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[id] = struct{}{}
	}

	now := time.Now().Unix()
	mailLogMessage := make([]*log.MailMessageForWm, 0, len(cmsg.Ids))
	logAwards := make(map[uint64][]*cl.Resource)
	for _, id := range cmsg.Ids {
		m := mb.Get(id)
		if m == nil || mail.HasAwards(m, now) {
			l4g.Errorf("user: %d C2L_DeleteMails: mail can not del, id:%d", c.Msg.UID, id)
			smsg.Ret = uint32(cret.RET_MAIL_HAS_DELETE_OR_EXPIRED)
			break
		}
		mailLogMessage = append(mailLogMessage, &log.MailMessageForWm{
			Id:       m.Id,
			MailType: m.Type,
			Title:    getMailTitle(m, c.Srv, c.User.GetLang()),
		})
		if len(m.Awards) > 0 {
			logAwards[m.Id] = m.Awards
		}
	}
	if smsg.Ret == uint32(cret.RET_OK) {
		mb.Delete(cmsg.Ids)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DeleteMails, smsg)
	c.User.LogDeleteMails(c.Srv, cmsg.Ids, mailLogMessage, logAwards)
	return c.ResultOK(smsg.Ret)
}

func getMailTitle(m *cl.Mail, srv command.Servicer, userLang string) string {
	mailInfo := goxml.GetData().MailInfoM.Index(m.BaseId)
	if mailInfo == nil {
		return ""
	}
	if mailInfo.TitleNum != 1 {
		return ""
	}
	var contents []string
	if m.MultiLang {
		contents = srv.MultiLangM().GetLangs(m.Id, userLang)
	} else {
		contents = m.Params
	}
	titleIndex := int(mailInfo.SenderNum)
	if len(contents) <= titleIndex {
		return ""
	}
	return contents[titleIndex]
}
