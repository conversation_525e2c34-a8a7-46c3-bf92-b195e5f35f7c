package divinedemon

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_DivineDemonGetOpenActivity), &C2LDivineDemonGetOpenActivity{}, state)   // 获取开放的新英雄抽卡的活动
	cmds.Register(uint32(cl.ID_MSG_C2L_DivineDemonSummon), &C2LDivineDemonSummon{}, state)                     // 英雄抽卡
	cmds.Register(uint32(cl.ID_MSG_C2L_DivineDemonReceiveTaskAward), &C2LDivineDemonReceiveTaskAward{}, state) // 领取任务奖励
}

type C2LDivineDemonGetOpenActivity struct {
	base.TimelyResetUserCommand
}

func (c *C2LDivineDemonGetOpenActivity) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDivineDemonGetOpenActivity) Error(msg *cl.L2C_DivineDemonGetOpenActivity, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DivineDemonGetOpenActivity, msg)
	return false
}

func (c *C2LDivineDemonGetOpenActivity) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DivineDemonGetOpenActivity{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DebutGetHeroSummonActivity Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DebutGetHeroSummonActivity: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_DivineDemonGetOpenActivity{
		Ret: uint32(cret.RET_OK),
	}

	divineDemonM := c.User.DivineDemon()
	divineDemonM.CheckReset(c.Srv)
	divineDemonM.FixWishRecord()

	smsg.DivineDemon = divineDemonM.Flush()
	smsg.Activities = divineDemonM.FlushOpenActivity(c.Srv)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DivineDemonGetOpenActivity, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LDivineDemonSummon struct {
	base.TimelyResetUserCommand
}

func (c *C2LDivineDemonSummon) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDivineDemonSummon) Error(msg *cl.L2C_DivineDemonSummon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DivineDemonSummon, msg)
	return false
}

func (c *C2LDivineDemonSummon) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DivineDemonSummon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DivineDemonSummon Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DivineDemonSummon: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_DivineDemonSummon{
		Ret:            uint32(cret.RET_OK),
		Id:             cmsg.Id,
		SummonType:     cmsg.SummonType,
		CurHero:        cmsg.CurHero,
		SummonCostType: cmsg.SummonCostType,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DIVINE_DEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.isValidSummonType(cmsg.SummonType) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: summon type is invalid type:%d", c.Msg.UID, cmsg.SummonType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if !c.isValidSummonCostType(cmsg.SummonCostType, cmsg.Id) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: summon cost type is invalid type:%d activityID:%d", c.Msg.UID, cmsg.SummonCostType, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	divineDemonM := c.User.DivineDemon()
	divineDemonM.CheckReset(c.Srv)

	openActivity := divineDemonM.GetOpenDivineDemon(c.Srv, time.Now().Unix(), false)
	if openActivity == nil {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: activity not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_DIVINE_DEMON_ACTIVITY_NOT_OPEN))
	}
	if openActivity.Id != cmsg.Id {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: activity id invalid.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_DIVINE_DEMON_ACTIVITY_ID_INVALID))
	}
	demonInfo := goxml.GetData().DivineDemonInfoM.Index(openActivity.SysID)
	if demonInfo == nil {
		l4g.Errorf("user:%d C2L_DivineDemonSummon: demonInfo not exist. activityID: %d", c.Msg.UID, openActivity.SysID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	if _, exist := demonInfo.HeroIds[cmsg.CurHero]; !exist {
		l4g.Errorf("user:%d C2L_DivineDemonSummon: cmsg.CurHero is invalid. curHero: %d", c.Msg.UID, cmsg.CurHero)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	divineDemon := divineDemonM.DivineDemon(cmsg.Id, openActivity.SysID)
	ret, awards, costs, v2, summonCount, guaranteeWays, wishHeroes := divineDemon.Summon(cmsg.SummonType, cmsg.SummonCostType, demonInfo, c.Srv.Rand(), cmsg.CurHero)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: summon failed. ret: %d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	smsg.Ret, smsg.Awards = c.transform(awards)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: transform hero failed. ret: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, ret)
	}

	if len(costs) > 0 {
		smsg.Ret, _ = c.User.Trade(c.Srv, costs, smsg.Awards, uint32(log.RESOURCE_CHANGE_REASON_DIVINE_DEMON_SUMMON_AWARD), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_DivineDemonSummon: trade summon award failed. ret: %d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	} else {
		smsg.Ret, _ = c.User.Award(c.Srv, smsg.Awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_DIVINE_DEMON_SUMMON_AWARD), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_DivineDemonSummon: trade summon award failed. ret: %d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	redHeroCount, wishHeroCount := c.sendSystemMsg(awards, cmsg.CurHero)
	divineDemon.SetSummonV2(v2)
	divineDemonM.Save()
	smsg.SummonV2 = divineDemon.FlushSummonV2()
	//c.User.DivineDemonWishRecord().ResetRecord()
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeDivineDemonSummon, uint64(summonCount))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DivineDemonSummon, smsg)
	activityType := c.getActivityType(openActivity.Id, demonInfo)
	c.User.LogDivineDemonSummon(c.Srv, openActivity.SysID, cmsg.SummonType, cmsg.CurHero, activityType, wishHeroCount, redHeroCount, v2, awards, guaranteeWays, wishHeroes, cmsg.SummonCostType)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LDivineDemonSummon) getActivityType(id uint64, info *goxml.DivineDemonInfoExt) uint32 {
	if id < goxml.DivineDemonUniqIDMax {
		return goxml.DivineDemonActivityTypeNewServer
	} else {
		if info.Type == 1 {
			return goxml.DivineDemonActivityTypeCommonServerDebut
		}

		return goxml.DivineDemonActivityTypeCommonServer
	}
}

func (c *C2LDivineDemonSummon) isValidSummonType(tType uint32) bool {
	if tType != uint32(common.DIVINE_DEMON_SUMMON_TYPE_DSN_SINGLE) && tType != uint32(common.DIVINE_DEMON_SUMMON_TYPE_DSN_MANY) {
		return false
	}

	return true
}

func (c *C2LDivineDemonSummon) isValidSummonCostType(cType uint32, _ uint64) bool {
	if cType == uint32(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_NONE) || cType >= uint32(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_MAX) {
		return false
	}

	return true
}

func (c *C2LDivineDemonSummon) transform(awards []*cl.Resource) (uint32, []*cl.Resource) {
	oldNum := c.User.HeroManager().GetCount()
	maxNum := c.User.HeroSlot()
	retAwards := make([]*cl.Resource, 0, len(awards))

	for _, v := range awards { //nolint:varnamelen
		if v.Type != uint32(common.RESOURCE_HERO) {
			retAwards = append(retAwards, v)
			continue
		}
		oldNum++
		if oldNum > maxNum {
			fragment := goxml.GetData().FragmentInfoM.Hero2Fragment(v.Value)
			if fragment == nil {
				l4g.Errorf("user: %d C2L_DivineDemonSummon: hero to fragment error. heroSysID: %d", c.User.ID(), v.Value)
				return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
			}
			retAwards = append(retAwards, fragment)

		} else {
			retAwards = append(retAwards, v)
		}
	}

	return uint32(cret.RET_OK), retAwards
}

func (c *C2LDivineDemonSummon) sendSystemMsg(awards []*cl.Resource, curHero uint32) (uint32, uint32) {
	redCardCount := uint32(0)
	upRedCardCount := uint32(0)
	params := make([]string, 0, len(awards))
	for _, award := range awards {
		if award.Type == uint32(common.RESOURCE_HERO) {
			heroInfo := goxml.GetData().HeroInfoM.Index(award.Value)
			if heroInfo == nil {
				l4g.Errorf("user: %d hero sysID not exist. sysID: %d", c.Msg.UID, award.Value)
				continue
			}
			if heroInfo.Rare >= goxml.HeroRareTrueFive {
				params = append(params, strconv.FormatUint(uint64(award.Value), 10))
				redCardCount++
				if award.Value == curHero {
					upRedCardCount++
				}
			}
		}
	}
	if len(params) == 0 {
		return redCardCount, upRedCardCount
	}

	message := character.NewMsg(character.Id(c.Srv), character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))),
		character.Type(goxml.ChatTypeDivineDemonSummon), character.Params(params...))
	character.ChatSendMsgToPlatform(c.Srv, c.User.ID(), message)

	return redCardCount, upRedCardCount
}

type C2LDivineDemonReceiveTaskAward struct {
	base.TimelyResetUserCommand
}

func (c *C2LDivineDemonReceiveTaskAward) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDivineDemonReceiveTaskAward) Error(msg *cl.L2C_DivineDemonReceiveTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DivineDemonReceiveTaskAward, msg)
	return false
}

func (c *C2LDivineDemonReceiveTaskAward) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DivineDemonReceiveTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DivineDemonReceiveTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_DivineDemonReceiveTaskAward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_DivineDemonReceiveTaskAward{
		Ret:      uint32(cret.RET_OK),
		Id:       cmsg.Id,
		TaskIds:  cmsg.TaskIds,
		TaskType: cmsg.TaskType,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DIVINE_DEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: function not open. type:%d", c.Msg.UID, cmsg.TaskType)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	divineDemonM := c.User.DivineDemon()
	divineDemonM.CheckReset(c.Srv)

	openActivity := divineDemonM.GetOpenDivineDemon(c.Srv, time.Now().Unix(), true)
	if openActivity == nil {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: activity not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_DIVINE_DEMON_ACTIVITY_NOT_OPEN))
	}
	if openActivity.Id != cmsg.Id {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: activity id invalid.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_DIVINE_DEMON_ACTIVITY_ID_INVALID))
	}
	if !isValidTaskType(openActivity.SysID, cmsg.TaskType) {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: task type invalid. type:%d", c.Msg.UID, cmsg.TaskType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	task := divineDemonM.DivineDemon(cmsg.Id, openActivity.SysID).GetTask(cmsg.TaskType)
	if ret := c.checkTaskID(task, openActivity.SysID, cmsg.TaskType, cmsg.TaskIds); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: receive award failed. id: %d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, ret)
	}

	awards := task.GetTaskAwards(openActivity.SysID, cmsg.TaskIds)
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_DIVINE_DEMON_TASK_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: send task award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	task.HandlerReceive(cmsg.TaskIds)
	c.User.DivineDemon().Save()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DivineDemonReceiveTaskAward, smsg)
	c.User.LogDivineDemonReceiveTaskAward(c.Srv, cmsg.Id, openActivity.SysID, cmsg.TaskType, cmsg.TaskIds)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LDivineDemonReceiveTaskAward) checkTaskID(task character.DivineDemonTask, activityID, tType uint32, taskIDs []uint32) uint32 {
	if !c.isValidTaskLen(tType, activityID, taskIDs) {
		l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: task id num too much. num: %d", c.Msg.UID, len(taskIDs))
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}

	repeatMap := make(map[uint32]struct{}, len(taskIDs))
	for _, taskID := range taskIDs {
		if _, exist := repeatMap[taskID]; exist {
			l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: id repeat. id: %d, type: %d", c.Msg.UID, taskID, tType)
			return uint32(cret.RET_DIVINE_DEMON_TASK_ID_REPEAT)
		}
		repeatMap[taskID] = struct{}{}

		if task.IsReceiveAwarded(taskID) {
			l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: award be receive. id: %d, type: %d", c.Msg.UID, taskID, tType)
			return uint32(cret.RET_DIVINE_DEMON_TASK_AWARD_BE_RECEIVE)
		}

		if ret := c.IsFinish(task, activityID, taskID); ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_DivineDemonReceiveTaskAward: task not finish. id: %d, type: %d", c.Msg.UID, taskID, tType)
			return uint32(cret.RET_DIVINE_DEMON_TASK_NOT_FINISH)
		}
	}

	return uint32(cret.RET_OK)
}

func (c *C2LDivineDemonReceiveTaskAward) isValidTaskLen(tType, activityID uint32, taskIDs []uint32) bool {
	var taskNum int
	switch tType {
	case uint32(common.DIVINE_DEMON_TASK_TYPE_DTT_SUMMON):
		taskNum = goxml.GetData().DivineDemonTaskSummonInfoM.GetTaskNum(activityID)
	case uint32(common.DIVINE_DEMON_TASK_TYPE_DTT_ACTIVE):
		taskNum = goxml.GetData().DivineDemonTaskActiveInfoM.GetTaskNum(activityID)
	case uint32(common.DIVINE_DEMON_TASK_TYPE_DTT_UP_STAR):
		taskNum = goxml.GetData().DivineDemonTaskStarInfoM.GetTaskNum(activityID)
	}

	return len(taskIDs) <= taskNum
}

func (c *C2LDivineDemonReceiveTaskAward) IsFinish(task character.DivineDemonTask, activityID, taskID uint32) uint32 {
	typeID, value, progress := task.GetTaskData(activityID, taskID)
	if typeID == 0 || value == 0 {
		l4g.Errorf("user:%d C2L_DivineDemonReceiveTaskAward: task id not exist. id:%d", c.Msg.UID, typeID)
		return uint32(cret.RET_DIVINE_DEMON_TASK_ID_NOT_EXIST)
	}

	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(typeID)
	if taskTypeInfo == nil {
		l4g.Errorf("user:%d C2L_DivineDemonReceiveTaskAward: taskTypeInfo not exist. typeID:%d", c.Msg.UID, typeID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		progress = c.User.CalcTaskProgress(taskTypeInfo)
	}
	if !c.User.CheckTaskFinish(progress, typeID, value) {
		l4g.Errorf("user:%d C2L_DivineDemonReceiveTaskAward: task not finish. taskID:%d", c.Msg.UID, taskID)
		return uint32(cret.RET_DIVINE_DEMON_TASK_NOT_FINISH)
	}

	return uint32(cret.RET_OK)
}

func isValidTaskType(_, taskType uint32) bool {
	if taskType == uint32(common.DIVINE_DEMON_TASK_TYPE_DTT_SUMMON) || taskType == uint32(common.DIVINE_DEMON_TASK_TYPE_DTT_UP_STAR) {
		return true
	}

	return false
}
