package artifactdebut

import (
	"app/goxml"
	"context"
	"strconv"

	"app/logic/character"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutMainInfo), &C2LArtifactDebutMainInfoCommand{}, state)           //获取玩法数据
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutSetWish), &C2LArtifactDebutSetWishCommand{}, state)             //设置心愿神器
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutSummon), &C2LArtifactDebutSummonCommand{}, state)               //抽卡
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutRecvActAward), &C2LArtifactDebutRecvActAwardCommand{}, state)   //领取活动奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutRecvTaskAward), &C2LArtifactDebutRecvTaskAwardCommand{}, state) //领取任务奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutOpenPuzzle), &C2LArtifactDebutOpenPuzzleCommand{}, state)       //拼图游戏掀格子
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutGetActivity), &C2LArtifactDebutGetActivityCommand{}, state)     //获取活动配置
	cmds.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutTestSummon), &C2LArtifactDebutTestSummonCommand{}, state)       //抽卡测试
}

type C2LArtifactDebutMainInfoCommand struct {
	base.UserCommand
}

func (c *C2LArtifactDebutMainInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutMainInfoCommand) Error(msg *cl.L2C_ArtifactDebutMainInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutMainInfo, msg)
	return false
}

func (c *C2LArtifactDebutMainInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutMainInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutMainInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutMainInfo: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutMainInfo{
		Ret: uint32(cret.RET_OK),
	}

	// if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_DEBUT), c.Srv) {
	// 	l4g.Errorf("user: %d C2L_ArtifactDebutMainInfo: function not open", c.Msg.UID)
	// 	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	// }

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInAwardTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutMainInfo: not in award time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_AWARD_TIME))
	}

	smsg.Data = adManager.Flush()
	smsg.GuaranteeLeftCount = adManager.CalcGuaranteeCount()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutMainInfo, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LArtifactDebutSetWishCommand struct {
	base.UserCommand
}

func (c *C2LArtifactDebutSetWishCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutSetWishCommand) Error(msg *cl.L2C_ArtifactDebutSetWish, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutSetWish, msg)
	return false
}

func (c *C2LArtifactDebutSetWishCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutSetWish{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutSetWish Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutSetWish: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutSetWish{
		Ret:     uint32(cret.RET_OK),
		WishAid: cmsg.WishAid,
	}

	if cmsg.WishAid == 0 {
		l4g.Errorf("user: %d C2L_ArtifactDebutSetWish: param err. WishAid=0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_DEBUT), c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSetWish: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	info := goxml.GetData().ArtifactInfoM.Index(cmsg.WishAid)
	if info == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutSetWish: artifactInfo not exist. WishAid:%d", c.Msg.UID, cmsg.WishAid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInActiveTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSetWish: not in activity time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME))
	}

	if !goxml.GetData().ArtifactDebutDrawOptionalInfoM.IsLegalArtifact(adManager.GetActID(), cmsg.WishAid) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSetWish: WishAid illegal. WishAid:%d", c.Msg.UID, cmsg.WishAid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.WishAid == adManager.GetWishArtifactID() {
		l4g.Errorf("user: %d C2L_ArtifactDebutSetWish: repeat set. WishAid:%d", c.Msg.UID, cmsg.WishAid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	adManager.SetWishArtifact(cmsg.WishAid)
	smsg.UniqId = adManager.GetID()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutSetWish, smsg)
	c.User.LogArtifactDebutSetWish(c.Srv, smsg.UniqId, adManager.GetActID(), cmsg.WishAid)
	return c.ResultOK(smsg.Ret)
}

type C2LArtifactDebutSummonCommand struct {
	base.UserCommand
}

func (c *C2LArtifactDebutSummonCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutSummonCommand) Error(msg *cl.L2C_ArtifactDebutSummon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutSummon, msg)
	return false
}

//nolint:funlen
func (c *C2LArtifactDebutSummonCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutSummon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutSummon Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutSummon: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutSummon{
		Ret:        uint32(cret.RET_OK),
		Category:   cmsg.Category,
		IsMultiple: cmsg.IsMultiple,
	}

	// if cmsg.Category != uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_JUNIOR) &&
	// 	cmsg.Category != uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR) {
	// 	l4g.Errorf("user: %d C2L_ArtifactDebutSummon: param err. Category=0", c.Msg.UID, cmsg.Category)
	// 	return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	// }
	if cmsg.Category != uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: param err. Category:%d illegal", c.Msg.UID, cmsg.Category)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_DEBUT), c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInActiveTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: not in activity time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME))
	}

	actId := adManager.GetActID()
	actInfo := goxml.GetData().ArtifactDebutActivityInfoM.GetRecordById(actId)
	if actInfo == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: actInfo not exist. actId:%d", c.Msg.UID, actId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	wishArtifactID := adManager.GetWishArtifactID()
	if wishArtifactID == 0 {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: no wish artifact", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NO_WISH_ARTIFACT))
	}

	//验证抽卡券
	var drawCount uint32 = 1
	if cmsg.IsMultiple {
		drawCount = goxml.GetData().ArtifactDebutConfigInfoM.GetMultipleDraw()
	}
	costs := calcDrawCosts(cmsg.Category, drawCount)
	itemEnough, _ := c.User.CheckResourcesSize(costs)
	if itemEnough != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: cost item not enough, category:%d, isMultiple:%v",
			c.Msg.UID, cmsg.Category, cmsg.IsMultiple)
		return c.Error(smsg, uint32(cret.RET_NOT_ENOUGH_RESOURCES))
	}

	//抽卡
	var flag bool
	var drawRet *character.ArtifactDebutDrawRet
	switch actInfo.Type {
	case uint32(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_WHOLE): //旧版抽卡-能抽到完成神器
		flag, drawRet = adManager.DrawArtifact(c.Srv, cmsg.Category, drawCount)
	case uint32(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_FRAGMENT): //新版抽卡-只能抽到碎片
		flag, drawRet = adManager.DrawArtifactFragments(c.Srv, cmsg.Category, drawCount)
	default:
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: actInfo.Type err. actId:%d, type:%d", c.Msg.UID, actId, actInfo.Type)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !flag || drawRet == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: DrawArtifact failed. flag %v drawRet %+v", c.Msg.UID, flag, drawRet)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	showAward := c.getBeDecomposedArtifacts(drawRet.Awards)

	//合并抽卡资源与积分
	// pointsRes :=  goxml.GetData().ArtifactDebutConfigInfoM.MakeDrawAddPointsRes(cmsg.Category, drawCount)
	// totalAwards := append(drawRet.Awards, pointsRes...)

	//更新资源
	smsg.Ret, smsg.Award = c.User.Trade(c.Srv, costs, drawRet.Awards, uint32(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_SUMMON), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: trade error", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}
	smsg.Award = append(smsg.Award, showAward...)

	//每次抽卡奖励分开展示
	smsg.RawAward = drawRet.Awards

	//更新抽卡相关数据
	adManager.SetSummon(cmsg.Category, drawRet.Summon)
	// if cmsg.Category == uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR) {
	// 	adManager.AddDrawCount(drawCount)
	// }

	//更新保底数据
	if actInfo.Type == uint32(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_WHOLE) {
		adManager.SetGuarantee(drawRet.Guarantee)
		adManager.Save()
	}

	//抽卡跑马灯
	c.pushToAll(drawRet.Awards, goxml.ChatTypeArtifactDebutDraw)

	//积分兑换
	// points := uint32(c.User.GetResourceCount(uint32(common.RESOURCE_TOKEN),
	// 	uint32(common.TOKEN_TYPE_ARTIFACT_DEBUT_DRAW_POINTS)))
	// exchangeCount, exchangeCost :=  goxml.GetData().ArtifactDebutConfigInfoM.GetExchangeCost(points)
	// if exchangeCount > 0 {
	// 	exchangeAward := []*cl.Resource{
	// 		{
	// 			Type:  uint32(common.RESOURCE_ARTIFACT),
	// 			Value: wishArtifactID,
	// 			Count: exchangeCount,
	// 		},
	// 	}

	// 	smsg.Ret, smsg.PointsAward = c.User.Trade(c.Srv, exchangeCost, exchangeAward,
	// 		uint32(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_SUMMON), 0)
	// 	if smsg.Ret != uint32(cret.RET_OK) {
	// 		l4g.Errorf("user: %d C2L_ArtifactDebutSummon: exchange trade error", c.Msg.UID)
	// 		return c.Error(smsg, smsg.Ret)
	// 	}

	// 	//积分兑换跑马灯
	// 	c.pushToAll(exchangeAward, goxml.ChatTypeArtifactDebutPointsExchange)
	// }

	smsg.Count = adManager.GetDrawCount()
	smsg.UniqId = adManager.GetID()
	smsg.GuaranteeLeftCount = adManager.CalcGuaranteeCount()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutSummon, smsg)

	summonLogData := adManager.GetSummon(cmsg.Category)
	logData := &log.ArtifactDebutSummon{
		Category:        cmsg.Category,
		Count:           drawCount,
		SeniorDrawCount: smsg.Count,
		// ExchangeCount:   exchangeCount,
		// LeftPoints:      points - exchangeCount* goxml.GetData().ArtifactDebutConfigInfoM.GetPointExchange(),
		FinishGroups: summonLogData.FinishGroups,
		CurrentGroup: summonLogData.CurrentGroup,
		FinishIds:    summonLogData.CurrentIds,
	}
	c.User.LogArtifactDebutSummon(c.Srv, smsg.UniqId, adManager.GetActID(), logData, drawRet.GuaranteeTypes, drawRet.Awards)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeArtifactDebutSummon, uint64(drawCount))
	return c.ResultOK(smsg.Ret)
}

func calcDrawCosts(category, drawCount uint32) []*cl.Resource {
	itemID := goxml.ArtifactDebutJuniorDrawItemID
	if category == uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR) {
		itemID = goxml.ArtifactDebutSeniorDrawItemID
	}
	return []*cl.Resource{
		{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: itemID,
			Count: drawCount,
		},
	}
}

// 生成奖励展示数据: 只获取转成碎片的神器
func (c *C2LArtifactDebutSummonCommand) getBeDecomposedArtifacts(awards []*cl.Resource) []*cl.Resource {
	showAwards := make([]*cl.Resource, 0, len(awards))
	for _, v := range awards { //nolint:varnamelen
		if v.Type == uint32(common.RESOURCE_ARTIFACT) {
			info := goxml.GetData().ArtifactInfoM.Index(v.Value)
			if info == nil {
				l4g.Errorf("user: %d C2L_ArtifactDebutSummon: makeShowAward no info, id:%d",
					c.User.ID(), v.Value)
				return nil
			}

			if c.User.ArtifactManager().GetArtifact(v.Value) != nil {
				showAwards = append(showAwards, &cl.Resource{
					Type:  uint32(common.RESOURCE_ARTIFACT),
					Value: v.Value,
					Count: 1,
					Attrs: []*cl.Attr{{
						Type:  uint32(common.RESOURCE_ATTR_TYPE_RESAT_AWARD_FLAG),
						Value: int64(common.RESOURCE_REWARD_FLAG_RRF_NEW_DECOMPOSED),
					}},
				})
				continue
			}
		}
	}
	return showAwards
}

// 向全服推送跑马灯消息
// @param []*cl.Resource awards 奖励数据
// @param uint32 msgType 28-抽卡 30-积分兑换
func (c *C2LArtifactDebutSummonCommand) pushToAll(awards []*cl.Resource, msgType uint32) {
	aids := make([]string, 0, 1)
	for _, award := range awards {
		if award.Type != uint32(common.RESOURCE_ARTIFACT) {
			continue
		}

		info := goxml.GetData().ArtifactInfoM.Index(award.Value)
		if info == nil {
			l4g.Errorf("user: %d C2L_ArtifactDebutSummon: pushToAll failed, artifact not exist: %d",
				c.Msg.UID, award.Value)
			continue
		}

		//过滤橙色以下品质的神器
		if info.Rare < goxml.RareOrange {
			continue
		}

		for i := uint32(0); i < award.Count; i++ {
			aid := strconv.FormatUint(uint64(award.Value), 10)
			aids = append(aids, aid)
		}
	}

	if len(aids) > 0 {
		message := character.NewMsg(character.Id(c.Srv), character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))),
			character.Type(msgType), character.Params(aids...))
		character.ChatSendMsgToPlatform(c.Srv, c.User.ID(), message)
	}
}

type C2LArtifactDebutRecvActAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LArtifactDebutRecvActAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutRecvActAwardCommand) Error(msg *cl.L2C_ArtifactDebutRecvActAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutRecvActAward, msg)
	return false
}

func (c *C2LArtifactDebutRecvActAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutRecvActAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutRecvActAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutRecvActAward: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutRecvActAward{
		Ret:  uint32(cret.RET_OK),
		Type: cmsg.Type,
	}

	// if cmsg.Type != uint32(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_LOGIN) &&
	// 	cmsg.Type != uint32(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_SUMMON) &&
	// 	cmsg.Type != uint32(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_PUZZLE) {
	// 	l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: param type err. Type:%d", c.Msg.UID, cmsg.Type)
	// 	return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	// }
	if cmsg.Type != uint32(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_LOGIN) &&
		cmsg.Type != uint32(common.ARTIFACT_DEBUT_ACT_RECV_ADAR_PUZZLE) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: param type err. Type:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	count := len(cmsg.Ids)
	if count == 0 || count > character.ArtifactDebutRecvAwardCountLimit {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: param ids err. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_DEBUT), c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInAwardTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: not in award time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_AWARD_TIME))
	}

	uniq := make(map[uint32]struct{})
	for _, id := range cmsg.Ids {
		if id == 0 {
			l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: param id=0. Type:%d", c.Msg.UID, cmsg.Type)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if _, exist := uniq[id]; exist {
			l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: param repeat, ids:%v",
				c.Msg.UID, cmsg.Ids)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		uniq[id] = struct{}{}
	}

	actHandler := adManager.GetActHandler(cmsg.Type)
	if actHandler == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: actHandler not exist, type:%d",
			c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	actID := adManager.GetActID()
	if !actHandler.CanReceiveAward(actID, cmsg.Ids) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: CanReceiveAward failed, actID:%d, type:%d, ids:%v",
			c.Msg.UID, actID, cmsg.Type, cmsg.Ids)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_CANT_RECV_ACT_AWARD))
	}

	award := actHandler.GetAward(actID, cmsg.Ids)
	smsg.Ret, smsg.Award = c.User.Award(c.Srv, award, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RECV_ACT_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvActAward: award error. retCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.ReceivedIds = actHandler.ReceiveAward(cmsg.Ids)
	adManager.Save()

	smsg.Ids = cmsg.Ids
	smsg.UniqId = adManager.GetID()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutRecvActAward, smsg)
	c.User.LogArtifactDebutRecvActAward(c.Srv, smsg.UniqId, actID, cmsg.Type, smsg.ReceivedIds)
	return c.ResultOK(smsg.Ret)
}

type C2LArtifactDebutRecvTaskAwardCommand struct {
	base.UserCommand
}

func (c *C2LArtifactDebutRecvTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutRecvTaskAwardCommand) Error(msg *cl.L2C_ArtifactDebutRecvTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutRecvTaskAward, msg)
	return false
}

func (c *C2LArtifactDebutRecvTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutRecvTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutRecvTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutRecvTaskAward: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutRecvTaskAward{
		Ret: uint32(cret.RET_OK),
	}

	count := len(cmsg.Ids)
	if count == 0 || count > character.ArtifactDebutRecvAwardCountLimit {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: param ids err. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_DEBUT), c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInAwardTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: not in award time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME))
	}

	actID := adManager.GetActID()
	uniq := make(map[uint32]struct{})
	taskInfos := make([]*goxml.ArtifactDebutTaskInfo, 0, count)
	for _, id := range cmsg.Ids {
		if id == 0 {
			l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: param id=0", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if _, exist := uniq[id]; exist {
			l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: param repeat, ids:%v",
				c.Msg.UID, cmsg.Ids)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		uniq[id] = struct{}{}

		info := goxml.GetData().ArtifactDebutTaskInfoM.Index(actID, id)
		if info == nil {
			l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: task info not exist, actID:%d, id:%d",
				c.Msg.UID, actID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		taskInfos = append(taskInfos, info)

		//是否可领奖
		if !adManager.CanReceiveAward(info, true) {
			l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: cannot receive, actID:%d, id:%d",
				c.Msg.UID, actID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	award := goxml.GetData().ArtifactDebutTaskInfoM.GetAwardByIDs(actID, cmsg.Ids)
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, award, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_RECV_TASK_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArtifactDebutRecvTaskAward: award error. retCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	adManager.ReceiveTasksAward(taskInfos)
	adManager.Save()

	smsg.Ids = cmsg.Ids
	smsg.UniqId = adManager.GetID()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutRecvTaskAward, smsg)
	c.User.LogArtifactDebutRecvTaskAward(c.Srv, smsg.UniqId, actID, cmsg.Ids)
	return c.ResultOK(smsg.Ret)
}

type C2LArtifactDebutOpenPuzzleCommand struct {
	base.UserCommand
}

func (c *C2LArtifactDebutOpenPuzzleCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutOpenPuzzleCommand) Error(msg *cl.L2C_ArtifactDebutOpenPuzzle, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutOpenPuzzle, msg)
	return false
}

func (c *C2LArtifactDebutOpenPuzzleCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutOpenPuzzle{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutOpenPuzzle Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutOpenPuzzle: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutOpenPuzzle{
		Ret: uint32(cret.RET_OK),
	}

	count := len(cmsg.Ids)
	if count == 0 || count > goxml.ArtifactDebutPuzzleMaxAwardID {
		l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: param ids err. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_DEBUT), c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInAwardTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: not in award time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_AWARD_TIME))
	}

	actID := adManager.GetActID()
	uniq := make(map[uint32]struct{})
	for _, id := range cmsg.Ids {
		if id == 0 {
			l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: param id=0", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if _, exist := uniq[id]; exist {
			l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: param repeat, ids:%v",
				c.Msg.UID, cmsg.Ids)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		uniq[id] = struct{}{}

		if !goxml.GetData().ArtifactDebutPuzzleRewardInfoM.IsLegalPosID(int(id)) {
			l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: id illegal, actID:%d, id:%d",
				c.Msg.UID, actID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if adManager.HasOpenedPuzzle(id) {
			l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: has opened, id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	cost := goxml.GetData().ArtifactDebutConfigInfoM.GetOpenPuzzleCost(uint32(count))
	smsg.Ret = c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_ARTIFACT_DEBUT_OPEN_PUZZLE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArtifactDebutOpenPuzzle: consume error", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.OpenedIds = adManager.OpenPuzzle(cmsg.Ids)
	adManager.Save()

	smsg.Ids = cmsg.Ids
	smsg.UniqId = adManager.GetID()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutOpenPuzzle, smsg)
	c.User.LogArtifactDebutOpenPuzzle(c.Srv, smsg.UniqId, actID, smsg.OpenedIds)
	return c.ResultOK(smsg.Ret)
}

type C2LArtifactDebutGetActivityCommand struct {
	base.UserCommand
}

func (c *C2LArtifactDebutGetActivityCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutGetActivityCommand) Error(msg *cl.L2C_ArtifactDebutGetActivity, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutGetActivity, msg)
	return false
}

func (c *C2LArtifactDebutGetActivityCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutGetActivity{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutGetActivity Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutGetActivity: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutGetActivity{
		Ret: uint32(cret.RET_OK),
	}

	adManager := c.User.ArtifactDebutM()
	smsg.Datas = adManager.FlushActivities(c.Srv)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutGetActivity, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LArtifactDebutTestSummonCommand struct {
	base.LimitedCommand
}

func (c *C2LArtifactDebutTestSummonCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArtifactDebutTestSummonCommand) Error(msg *cl.L2C_ArtifactDebutTestSummon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutTestSummon, msg)
	return false
}

func (c *C2LArtifactDebutTestSummonCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArtifactDebutTestSummon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArtifactDebutTestSummon Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArtifactDebutTestSummon: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArtifactDebutTestSummon{
		Ret: uint32(cret.RET_OK),
	}

	category := uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR)

	adManager := c.User.ArtifactDebutM()
	if !adManager.IsInActiveTime(c.Srv) {
		l4g.Errorf("user: %d C2L_ArtifactDebutTestSummon: not in activity time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME))
	}

	actId := adManager.GetActID()
	actInfo := goxml.GetData().ArtifactDebutActivityInfoM.GetRecordById(actId)
	if actInfo == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutTestSummon: actInfo not exist. actId:%d", c.Msg.UID, actId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	wishArtifactID := adManager.GetWishArtifactID()
	if wishArtifactID == 0 {
		l4g.Errorf("user: %d C2L_ArtifactDebutTestSummon: no wish artifact", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARTIFACT_DEBUT_NO_WISH_ARTIFACT))
	}

	artifactInfo := goxml.GetData().ArtifactInfoM.Index(wishArtifactID)
	if artifactInfo == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutTestSummon: artifactInfo not exist. wishArtifactID:%d", c.Msg.UID, wishArtifactID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	//抽卡
	var flag bool
	var drawRet *character.ArtifactDebutDrawRet
	switch actInfo.Type {
	case uint32(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_WHOLE): //旧版抽卡-能抽到完成神器
		flag, drawRet = adManager.DrawArtifact(c.Srv, category, cmsg.Count)
	case uint32(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_FRAGMENT): //新版抽卡-只能抽到碎片
		flag, drawRet = adManager.DrawArtifactFragments(c.Srv, category, cmsg.Count)
	default:
		l4g.Errorf("user: %d C2L_ArtifactDebutTestSummon: actInfo.Type err. actId:%d, type:%d", c.Msg.UID, actId, actInfo.Type)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !flag || drawRet == nil {
		l4g.Errorf("user: %d C2L_ArtifactDebutTestSummon: DrawArtifact failed. flag %v drawRet %+v", c.Msg.UID, flag, drawRet)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//更新抽卡相关数据
	adManager.SetSummon(category, drawRet.Summon)

	//更新保底数据
	if actInfo.Type == uint32(common.ARTIFACT_DEBUT_DRAW_ACT_TYPE_DRAW_WHOLE) {
		adManager.SetGuarantee(drawRet.Guarantee)
		adManager.Save()
	}

	// 红色神器碎片掉落数量 -> 次数
	dropNum2Count := make(map[uint32]uint32)
	for _, award := range drawRet.Awards {
		if award.Value != artifactInfo.CostValue {
			continue
		}
		dropNum2Count[award.Count]++
	}

	//合并抽卡资源
	smsg.DropAwards = c.User.MergeResources(drawRet.Awards)
	smsg.FragmentRecord = dropNum2Count

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutTestSummon, smsg)

	return c.ResultOK(smsg.Ret)
}
