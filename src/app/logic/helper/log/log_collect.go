package log

import (
	"bytes"
	"fmt"
	"sync"

	"gitlab.qdream.com/kit/sea/time"

	jsoniter "github.com/json-iterator/go"
	"gitlab.qdream.com/kit/sea/ctx"

	l4g "github.com/ivanabc/log4go"
	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
)

type LogCollect struct {
	group   *ctx.Group
	pool    *sync.Pool
	ch      chan LogI
	file    *rotatelogs.RotateLogs
	buf     *bytes.Buffer
	encoder *jsoniter.Encoder
}

type LogCollectParams struct {
	ChanSize      int
	LogPath       string
	RotationCount uint
	Pool          *sync.Pool
	Group         *ctx.Group
}

func NewLogCollect(params *LogCollectParams) *LogCollect {
	lc := &LogCollect{
		group: params.Group,
		ch:    make(chan LogI, params.ChanSize),
		buf:   bytes.NewBuffer(make([]byte, 0, maxBufferSize)),
	}
	lc.pool = params.Pool
	file, err := rotatelogs.New(params.LogPath+"-%Y-%m-%d-%H",
		rotatelogs.WithRotationTime(time.Hour),
		rotatelogs.WithLinkName(params.LogPath),
		rotatelogs.WithRotationCount(params.RotationCount))
	if err != nil {
		panic(fmt.Sprintf("[rotatelogs] New Error: %v", err))
	}
	lc.file = file
	lc.encoder = jsoniter.NewEncoder(lc.buf)
	return lc
}

func (l *LogCollect) GetNewPoolLog() interface{} {
	log := l.pool.Get()
	return log
}

// 防止run已经停止了还在写造成阻塞
func (l *LogCollect) Write(log LogI) {
	select {
	case l.ch <- log:
	case <-l.group.Done():
		l4g.Error("Collect write log error ch is finish log:%+v", log)
	}
}

func (l *LogCollect) Run(group *ctx.Group) {
	defer group.Finish()

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case log := <-l.ch:
			err := l.write(log)
			if err != nil {
				l4g.Errorf("(%+v) json marshal failed, error: %s", log, err)
				ctx.Stop()
				return
			}
			l.recycle(log)
		case <-ticker.C:
			err := l.flush()
			if err != nil {
				l4g.Errorf("flush buffer failed, error: %s", err)
				ctx.Stop()
				return
			}
		case <-group.Done():
			return
		}
	}
}

func (l *LogCollect) write(val interface{}) error {
	err := l.encoder.Encode(val)
	if err != nil {
		return err
	}
	if l.buf.Len() >= maxBufferSize {
		_, err = l.buf.WriteTo(l.file)
		return err
	}
	return nil
}

func (l *LogCollect) flush() error {
	_, err := l.buf.WriteTo(l.file)
	return err
}

func (l *LogCollect) Close() {
	l.group.Stop()
	l.group.Wait()

	for more := true; more; {
		select {
		case log := <-l.ch:
			err := l.write(log)
			if err != nil {
				l4g.Errorf("(%+v) json marshal failed, error: %s", log, err)
			}
		default:
			more = false
		}
	}
	err := l.flush()
	if err != nil {
		l4g.Errorf("json flush failed, error: %s", err)
	}
	l.file.Close()

	l.group.Finish()
	l4g.Infof("log collect close...")
}

func (l *LogCollect) recycle(log LogI) {
	log.Reset()
	l.pool.Put(log)
}
