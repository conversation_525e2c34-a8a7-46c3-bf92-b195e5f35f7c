package log

import (
	"app/protos/in/log"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
)

type CollectManger struct {
	group    *ctx.Group
	collects map[log.LOG_TYPE]LogCollectI
}

func NewCollectManager(group *ctx.Group) *CollectManger {
	collectM := &CollectManger{
		group:    group,
		collects: make(map[log.LOG_TYPE]LogCollectI),
	}
	return collectM
}

func (manager *CollectManger) NewGroup() *ctx.Group {
	return manager.group.CreateChild()
}

func (manager *CollectManger) AddNewCollect(logType log.LOG_TYPE, collect LogCollectI) {
	if _, exist := manager.collects[logType]; exist {
		panic(fmt.Sprintf("logType:%d exist", logType))
	}
	manager.collects[logType] = collect
}

func (manager *CollectManger) Run() {
	for _, collect := range manager.collects {
		go collect.Run(manager.NewGroup())
	}
}

func (manager *CollectManger) GetCollect(logType log.LOG_TYPE) LogCollectI {
	return manager.collects[logType]
}

func (manager *CollectManger) GetPoolLog(logType log.LOG_TYPE) interface{} {
	collect := manager.GetCollect(logType)
	if collect != nil {
		return collect.GetNewPoolLog()
	}
	return nil
}

func (manager *CollectManger) WriteLog(logType log.LOG_TYPE, log LogI) {
	collect := manager.GetCollect(logType)
	if collect != nil {
		collect.Write(log)
	}
}

func (manager *CollectManger) Close() {
	manager.group.Stop()
	for _, collect := range manager.collects {
		collect.Close()
	}
	manager.group.Wait()
	manager.group.Finish()
	l4g.Infof("CollectManger closed")
}
