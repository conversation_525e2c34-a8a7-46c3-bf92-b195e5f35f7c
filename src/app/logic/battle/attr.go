package battle

const (
	PsAttrInit                           int = iota
	PsAttrSkillParam                         //技能系数修改zheng
	PsAttrAddBuffRate                        //添加buff的概率修正
	PsAttrCorruption                         //腐蚀的系数
	PsAttrDefHurtedMax                       //防守方受到伤害的上限， 取最小值
	PsAttrDefHurtedHpMax                     //防守方受到伤害的生命的上限， 取最小值
	PsAttrDoKill                             //斩杀值，对方生命低于这个值(百分比)的时候，会直接斩杀
	PsAttrShield                             //护盾系数
	PsAttrHurtParam                          //伤害系数
	PsAttrCureParam                          //治疗系数
	PsAttrHurtedHpPctMax                     //受到最大伤害的血量百分比
	PsAttrNormalHurtRate                     //普攻增伤率
	PsAttrSmallHurtRate                      //小招增伤率
	PsAttrBigHurtRate                        //大招增伤率
	PsAttrOverCureRate                       //溢出治疗率
	PsAttrPsType1AddRate                     //庇佑、无敌(1)类被动触发概率修正
	PsAttrSeasonPhyDamAddRate                //赛季物理增伤
	PsAttrSeasonPhyDamReduceRate             //赛季物理减伤
	PsAttrSeasonMagDamAddRate                //赛季魔法增伤
	PsAttrSeasonMagDamReduceRate             //赛季魔法减伤
	PsAttrSeasonPhyDamAddRatePct             //赛季物理增伤加成
	PsAttrSeasonPhyDamReduceRatePct          //赛季物理减伤加成
	PsAttrSeasonMagDamAddRatePct             //赛季魔法增伤加成
	PsAttrSeasonMagDamReduceRatePct          //赛季魔法减伤加成stune
	PsAttrStuneRate                          //攻击能施加眩晕概率(默认施加buff 20100)
	PsAttrPsType4AddRate                     //(4)类被动触发概率修正
	PsAttrPsType2AddRate                     //大招连击(2)类被动触发概率修正
	PsAttrDefHurtedMaxByAttackAtk            //根据对手攻击限制受伤上限
	PsAttrPsType3FixValue                    // (3)类被动修正公式计算的值
	PsAttrCallDamAddRate                     //被召唤物攻击的增伤
	PsAttrReflect                            //反弹的系数
	PsAttrNotConsume                         //对buff类型317,318有概率生效后不消耗
	PsAttrDeadEventAddTriggerCount           //死亡事件的被动触发次数 + x
	PsAttrCallEventAddTriggerCount           //召唤事件的被动触发次数 + x
	PsAttrCallDeadEventAddTriggerCount       //召唤物死亡事件的被动触发次数 + x
	PsAttrHurtedHpPctMax2                    //受到最大伤害的血量百分比(去除属性47和60的影响)
	PsAttrBuffAttrFix                        //加减属性类buff结果修正
	PsAttrSeasonLinkEnergyFix                //赛季羁绊获取能量结果修正
	PsAttrSeasonLinkNegEnergyFix             //赛季羁绊获取能量(负能量)结果修正
	PsAttrBeShield                           //被加护盾系数
	PsAttrActiveSkillCritDefPunctureRate     //主动技能暴击时无视敌方防御系数
	PsAttrReduceNormalHurtRate               //普攻减伤率
	PsAttrReduceSmallHurtRate                //小招减伤率
	PsAttrReduceBigHurtRate                  //大招减伤率
	PsAttrReduceCritDamRate                  //暴击减伤率
	PsAttrPsType5FixValue                    //(5)类被动修正公式计算的值
	PsAttrHurtReduceByDivided                //伤害降低倍数
	PsAttrAttrLimitFix                       // 加属性时,有限制的属性(被动效果40,50)最大值修正
	PsAttrTotal
)

var gPsAttrAddType = [PsAttrTotal]int{
	PsAttrSkillParam:                     PsAttrAddTypeAdd,
	PsAttrAddBuffRate:                    PsAttrAddTypeAdd,
	PsAttrCorruption:                     PsAttrAddTypeAdd,
	PsAttrDefHurtedMax:                   PsAttrAddTypeMin,
	PsAttrDefHurtedHpMax:                 PsAttrAddTypeMin,
	PsAttrDoKill:                         PsAttrAddTypeMax,
	PsAttrShield:                         PsAttrAddTypeAdd,
	PsAttrHurtParam:                      PsAttrAddTypeAdd,
	PsAttrCureParam:                      PsAttrAddTypeAdd,
	PsAttrHurtedHpPctMax:                 PsAttrAddTypeMin,
	PsAttrNormalHurtRate:                 PsAttrAddTypeAdd,
	PsAttrSmallHurtRate:                  PsAttrAddTypeAdd,
	PsAttrBigHurtRate:                    PsAttrAddTypeAdd,
	PsAttrOverCureRate:                   PsAttrAddTypeMin,
	PsAttrPsType1AddRate:                 PsAttrAddTypeAdd,
	PsAttrSeasonPhyDamAddRate:            PsAttrAddTypeMax,
	PsAttrSeasonPhyDamReduceRate:         PsAttrAddTypeMax,
	PsAttrSeasonMagDamAddRate:            PsAttrAddTypeMax,
	PsAttrSeasonMagDamReduceRate:         PsAttrAddTypeMax,
	PsAttrSeasonPhyDamAddRatePct:         PsAttrAddTypeMax,
	PsAttrSeasonPhyDamReduceRatePct:      PsAttrAddTypeMax,
	PsAttrSeasonMagDamAddRatePct:         PsAttrAddTypeMax,
	PsAttrSeasonMagDamReduceRatePct:      PsAttrAddTypeMax,
	PsAttrStuneRate:                      PsAttrAddTypeAdd,
	PsAttrPsType4AddRate:                 PsAttrAddTypeAdd,
	PsAttrPsType2AddRate:                 PsAttrAddTypeAdd,
	PsAttrPsType3FixValue:                PsAttrAddTypeAdd,
	PsAttrDefHurtedMaxByAttackAtk:        PsAttrAddTypeMin,
	PsAttrCallDamAddRate:                 PsAttrAddTypeAdd,
	PsAttrReflect:                        PsAttrAddTypeAdd,
	PsAttrNotConsume:                     PsAttrAddTypeMax,
	PsAttrDeadEventAddTriggerCount:       PsAttrAddTypeAdd,
	PsAttrCallEventAddTriggerCount:       PsAttrAddTypeAdd,
	PsAttrCallDeadEventAddTriggerCount:   PsAttrAddTypeAdd,
	PsAttrHurtedHpPctMax2:                PsAttrAddTypeMin,
	PsAttrBuffAttrFix:                    PsAttrAddTypeAdd,
	PsAttrSeasonLinkEnergyFix:            PsAttrAddTypeAdd,
	PsAttrSeasonLinkNegEnergyFix:         PsAttrAddTypeAdd,
	PsAttrBeShield:                       PsAttrAddTypeAdd,
	PsAttrActiveSkillCritDefPunctureRate: PsAttrAddTypeAdd,
	PsAttrReduceNormalHurtRate:           PsAttrAddTypeAdd,
	PsAttrReduceSmallHurtRate:            PsAttrAddTypeAdd,
	PsAttrReduceBigHurtRate:              PsAttrAddTypeAdd,
	PsAttrReduceCritDamRate:              PsAttrAddTypeAdd,
	PsAttrPsType5FixValue:                PsAttrAddTypeAdd,
	PsAttrHurtReduceByDivided:            PsAttrAddTypeAdd,
	PsAttrAttrLimitFix:                   PsAttrAddTypeAdd,
}

const (
	PsAttrAddTypeNone = iota //
	PsAttrAddTypeAdd         //累加
	PsAttrAddTypeMin         //取最小值 ,注意这里无法只删除部分，只能全部删除
	PsAttrAddTypeMax         //取最大值 ,注意这里无法只删除部分，只能全部删除
)
