//nolint:mnd
package battle

import (
	"app/goxml"
	"app/protos/out/bt"
	"fmt"
	"sort"

	l4g "github.com/ivanabc/log4go"
)

//nolint:funlen
func (m *Manager) DoPassiveSkillEffect(attack, defense Uniter, pse *PsSkillExe,
	effectData *goxml.PSEffectData, redirectTargets *Uniters) {
	switch effectData.Effect {
	case goxml.EffectHurt:
		//passive_skill_effect表中的Effects默认值是0，同goxml.EffectHurt，通过effectData的配置加以区分
		if effectData.IsDefaultEffect() {
			return
		}
		m.DoPassiveSkillHurt(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectDoKill:
		m.DoPassiveSkillDoKill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectCure:
		m.DoPassiveSkillCure(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddBuff:
		m.DoPassiveSkillAddBuff(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectRemoveBuff:
		m.DoPassiveSkillRemoveBuff(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectNewSkill:
		m.DoPassiveSkillNewSkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectComboSkill:
		m.DoPassiveSkillComboSkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectComboNormalSkill:
		m.DoPassiveSkillComboNormalSkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectBindSkill:
		m.DoPassiveSkillBindSkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectReplaceSkill:
		m.DoPassiveSkillReplaceSkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectResurrection:
		m.DoPassiveSkillResurrection(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeAttr:
		m.DoPassiveSkillChangeAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangePsAttr:
		m.DoPassiveSkillChangePsAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeForevePsAttr:
		m.DoPassiveSkillChangeForevePsAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectNextPsAttr:
		m.DoPassiveSkillChangeNextPsAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectHpLowerAttr:
		m.DoPassiveSkillHpLowerAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectHpHigherAttr:
		m.DoPassiveSkillHpHigerAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeForeveAttr:
		m.DoPassiveSkillChangeForeveAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectNextRoundSkill:
		m.DoPassiveSkillNextActionSkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddArtifactEnergy:
		m.DoPassiveSkillAddArtifactEnergy(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectDecArtifactEnergy:
		m.DoPassiveSkillDecArtifactEnergy(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectHasBuff:
		m.DoPassiveSkillHasBuff(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAliveNum:
		m.DoPassiveSkillAliveNum(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectBuffType:
		m.DoPassiveEffectBuffType(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectBuffLayer:
		m.DoPassiveEffectBuffLayer(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddPS:
		m.DoPassiveEffectAddPS(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectComboNormalUnion:
		m.DoPassiveSkillEffectComboNormalUnion(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectFightBack:
		m.DoPassiveSkillEffectFightBack(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddShield:
		m.DoPassiveEffectAddShield(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectCopyBuff:
		m.DoPassiveEffectCopyBuff(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangePS:
		m.DoPassiveEffectChangePS(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeArtifactCD:
		m.DoPassiveChangeArtifactCD(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectStealShield:
		m.DoPassiveStealShield(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectHurtSelf:
		m.DoPassiveHurtSelf(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectSettleBuff:
		m.DoPassiveSettleBuff(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectFullHurt:
		m.DoPassiveFullHurt(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectCall:
		m.DoPassiveCall(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeHaloAttr:
		m.DoPassiveSkillChangeHaloAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeHaloPsAttr:
		m.DoPassiveSkillChangeHaloPsAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeForeveAttrWithLimit:
		m.DoPassiveSkillChangeForeveAttrWithLimit(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectBless:
		m.DoPassiveBless(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectExplosiveShield:
		m.DoPassiveExplosiveShield(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectSplashHurt:
		m.DoPassiveSplashHurt(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectStealSoul:
		m.DoPassiveStealSoul(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddSeasonLinkEnergy:
		m.DoPassiveSkillAddSeasonLinkEnergy(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectDecSeasonLinkEnergy:
		m.DoPassiveSkillDecSeasonLinkEnergy(attack, defense, pse, effectData, redirectTargets)
	// case goxml.EffectChangeAttrWithLimit:
	//目前effect47作用等同与effect40，故暂弃用。v2.8.0-dev中有加属性逻辑优化后的代码
	// 	m.DoPassiveSkillChangeAttrWithLimit(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectDevour:
		m.DoPassiveSkillDevour(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddBuffByEffTarget:
		m.DoPassiveSkillAddBuffByEffTarget(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeForeveAttrWithLimit2:
		m.DoPassiveSkillChangeForeveAttrWithLimit2(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddShieldByInherit:
		m.DoPassiveEffectAddShieldByInherit(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectSwapPos:
		m.DoPassiveEffectSwapPos(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectCopyBuffWhenSwapPos:
		m.DoPassiveEffectCopyBuffWhenSwapPos(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddSkillAttackCount:
		m.DoPassiveSkillAddSkillAttackCount(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddBuffByBuffType:
		m.DoPassiveSkillAddBuffByBuffType(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectComboBySkill:
		m.DoPassiveSkillEffectComboBySkill(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectCleanBuffAddAttr:
		m.DoPassiveEffectCleanBuffAddAttr(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectHurt2Cure:
		m.DoPassiveEffectHurt2Cure(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectEat:
		m.DoPassiveEffectEat(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectKillNotDead:
		m.DoPassiveEffectKillNotDead(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectTransferBuff:
		m.DoPassiveEffectTransferBuff(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectAddBuffByBuffLayer:
		m.DoPassiveEffectAddBuffByBuffLayer(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectChangeSkillAttackDefenser:
		m.DoPassiveEffectChangeSkillAttackDefenser(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectCallDepartedSpirit:
		m.DoPassiveEffectCallDepartedSpirit(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectDuel:
		m.DoPassiveEffectDuel(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectHurtSelfDamageToTrigger:
		m.DoPassiveEffectHurtSelfDamageToTrigger(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectReverseTargetsActOrder:
		m.DoReverseTargetsActOrder(attack, defense, pse, effectData, redirectTargets)
	case goxml.EffectResetSkillCD:
		m.DoPassiveEffectResetSkillCD(attack, defense, pse, effectData, redirectTargets)
	default:
		panic(fmt.Sprintf("battle get passive effect no fin :%d", effectData.Effect))
	}
	if df() {
		m.report.AddPassiveDebugFormula()
	}
}

func (m *Manager) DoPassiveSkillHurt(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)

	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].Hurted(attack, value, bt.SpecialType_HurtTypePassive, factor, pse)
		}
	} else {
		defense.Hurted(attack, value, bt.SpecialType_HurtTypePassive, factor, pse)
	}
}

// 斩杀新机制，使用被动技能来实现
func (m *Manager) DoPassiveSkillDoKill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if defense.EliteType() > 0 {
		//精英怪不触发斩杀
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(斩杀:%d), 未触发，对精英怪%v", defense.ID(), defense.EliteType()))
		}
		return
	}
	value, _ := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	if value >= defense.GetHp() {
		//触发了斩杀
		value = defense.GetHp()
		defense.Hurted(attack, value, bt.SpecialType_HurtTypeDoKill, bt.FactorType_FactorNone, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(斩杀:%d)", defense.ID()))
		}
	} else {
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(斩杀:%d), 未触发，剩余血量%d大于要求值%d", defense.ID(), defense.GetHp(), value))
		}

	}
}

func (m *Manager) DoPassiveSkillCure(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].Cured(attack, value, bt.SpecialType_SpecialTypeNone, factor, pse)
		}
	} else {
		defense.Cured(attack, value, bt.SpecialType_SpecialTypeNone, factor, pse)
	}
}

func (m *Manager) DoPassiveSkillAddBuff(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	defense.DoAddBuff(attack, eff.EffFormula, uint32(eff.EffParam1), uint32(eff.EffParam2), pse)
}

func (m *Manager) DoPassiveSkillRemoveBuff(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	defense.DoRemoveBuff(attack, eff.EffFormula, uint32(eff.EffParam1), uint32(eff.EffParam2), uint32(eff.EffParam3), uint32(eff.EffParam4), pse)
}

// 再释放一个技能
func (m *Manager) DoPassiveSkillNewSkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := m.AddNextSkill(defense, pse, pse.args, pse.tUniter)
	if skill != nil && de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)在释放一个技能,id:%d",
			defense.ID(), skill.info.Id))
	}
}

// 连击
func (m *Manager) DoPassiveSkillComboSkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	nowSkill := pse.GetNowSkill()
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)连击 技能id:%d, 参数:%d",
			defense.ID(), nowSkill.info.Id, eff.EffParam1))
	}
	m.AddComboSkill(defense, nowSkill, int32(eff.EffParam1), pse.tUniter)
}

// 暴追
func (m *Manager) DoPassiveSkillComboNormalSkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	hasCombo := false
	var nextSkill *NextSkill
	for _, v := range m.nextSkills {
		if v.skillType == NextSkillTypeCombo {
			hasCombo = true
			nextSkill = v
			break
		}
	}
	if hasCombo {
		//已经触发了连击
		nextSkill.param = int32(float64(nextSkill.param) * (1.0 + float64(eff.EffParam1)/BaseFloat))
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)爆追击 重复爆追 修改下一次系数:%d",
				defense.ID(), nextSkill.param))
		}
	} else {
		normalSkill := attack.GetSkillM().GetNormalSkill()
		if normalSkill == nil {
			panic(fmt.Sprintf("battle passive skill effect DoPassiveSkillComboNormalSkill id:%d pse:%+v",
				attack.ID(), pse))
		}
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)爆追 技能id:%d 系数:%d",
				defense.ID(), normalSkill.info.Id, NextSkillNoModify))
		}
		m.AddComboSkill(defense, normalSkill, NextSkillNoModify, pse.tUniter)
	}
}

// 绑定技能, 只有未被替换的时候才需要刷新
func (m *Manager) DoPassiveSkillBindSkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	def := defense.(*Member)
	def.skillM.SetBindSkill(int(eff.EffParam1), uint32(eff.EffParam2), pse.psSkill)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)绑定技能 绑定位置:%d 绑定技能id:%d",
			defense.ID(), eff.EffParam1, eff.EffParam2))
	}
}

// 替换技能, 只有未被替换的时候才需要刷新
func (m *Manager) DoPassiveSkillReplaceSkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if m.nowSkill != nil && m.nowSkill.GetPos() == int(eff.EffParam1) {
		m.ResetNowSkill(uint32(eff.EffParam2))
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)替换当前位置:%d的技能为 :%d",
				defense.ID(), eff.EffParam1, eff.EffParam2))
		}
	}
}

func (m *Manager) DoPassiveSkillChangeAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	addTime := pse.GetChangeAttrTime()
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].ChangeAttr(addTime, BattleAdd, int(eff.TriggerEffect), value)
			pse.LogAttrChange(uniterArray[i])
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改属性 属性id:%d, 属性值%d",
					uniterArray[i].ID(), eff.TriggerEffect, value))
			}
		}
	} else {
		defense.ChangeAttr(addTime, BattleAdd, int(eff.TriggerEffect), value)
		pse.LogAttrChange(defense)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改属性 属性id:%d, 属性值%d",
				defense.ID(), eff.TriggerEffect, value))
		}
	}
}

func (m *Manager) DoPassiveSkillChangePsAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	addTime := pse.GetChangeAttrTime()
	defense.ChangePsAttr(int(addTime), BattleAdd, int(eff.TriggerEffect), value)
	pse.LogPsAttrChange(defense, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改特殊属性 属性id:%d, 属性值%d",
			defense.ID(), eff.TriggerEffect, value))
	}
}

func (m *Manager) DoPassiveSkillResurrection(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if defense.GetStatus() == UniterAlive || defense.HasBuff(BuffTypeNoAlive) {
		return
	}
	if defense.IsCall() && defense.GetMonsterType() == MonsterTypeCallDepartedSpirit { // 召唤物不执行复活
		return
	}
	value, _ := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.DoResurrection(attack, value, pse)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)复活,hp:%d",
			defense.ID(), value))
	}
}

func (m *Manager) DoPassiveSkillChangeNextPsAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.ChangePsAttr(ChangePsAttrTimeNextSkill, BattleAdd, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改下次特殊属性 属性id:%d, 值:%d",
			defense.ID(), eff.TriggerEffect, value))
	}
}

func (m *Manager) DoPassiveSkillHpLowerAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := pse.psSkill.info
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.(*Member).attrM.AddHpAttrEffect(int(eff.TriggerEffect), value, HpAttrCondHpLower, int(skill.Info.TriggerParam11))
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改血量低于xx改变属性 属性id:%d, 属性值:%d, 血量低于:%d 触发",
			defense.ID(), eff.TriggerEffect, value, skill.Info.TriggerParam11))
	}
}

func (m *Manager) DoPassiveSkillHpHigerAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := pse.psSkill.info
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.(*Member).attrM.AddHpAttrEffect(int(eff.TriggerEffect), value, HpAttrCondHpHigher, int(skill.Info.TriggerParam11))
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改血量高于xx改变属性 属性id:%d, 属性值:%d, 血量高于:%d 触发",
			defense.ID(), eff.TriggerEffect, value, skill.Info.TriggerParam11))
	}
}

func (m *Manager) DoPassiveSkillChangeForevePsAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.ChangePsAttr(ChangePsAttrTimeForeve, BattleAdd, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改永久特殊属性 属性id:%d, 值:%d",
			defense.ID(), eff.TriggerEffect, value))
	}
}

func (m *Manager) DoPassiveSkillChangeForeveAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	if pse.psSkill.info.Info.PassiveType == PassiveType3 {
		fixRate := defense.GetPSEffectValue(PsAttrPsType3FixValue, 0)
		if fixRate != 0 {
			value = int64(float64(value) * (1 + float64(fixRate)/BaseFloat))
		}
	} else if pse.psSkill.info.Info.PassiveType == PassiveType5 {
		fixRate := defense.GetPSEffectValue(PsAttrPsType5FixValue, 0)
		if fixRate != 0 {
			value = int64(float64(value) * (1 + float64(fixRate)/BaseFloat))
		}
	}
	addTime := ChangeAttrTimeForeve
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].ChangeAttr(addTime, BattleAdd, int(eff.TriggerEffect), value)
		}
	} else {
		defense.ChangeAttr(addTime, BattleAdd, int(eff.TriggerEffect), value)
	}
}

func (m *Manager) DoPassiveSkillNextActionSkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	info := pse.psSkill.info
	defense.(*Member).skillM.ResetNextActionSkill(int(info.Info.EffParam1))
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改下次回合技能:%d",
			defense.ID(), info.Info.EffParam1))
	}
}

// 神器能量增加
func (m *Manager) DoPassiveSkillAddArtifactEnergy(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	artifact, ok := defense.(*ArtifactManager)
	if ok {
		artifact.AddEnergy(int(value), attack, defense, pse)
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)增加神器能量:%d",
			defense.ID(), value))
	}
}

// 神器能量减少
func (m *Manager) DoPassiveSkillDecArtifactEnergy(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	artifact, ok := defense.(*ArtifactManager)
	if ok {
		artifact.DecEnergy(int(value), attack, defense, pse)
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)减少神器能量:%d",
			defense.ID(), value))
	}
}

func (m *Manager) DoPassiveSkillHasBuff(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := pse.psSkill.info
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.(*Member).attrM.AddHadBuffAttrEffect(skill.Info.Id, skill.Info.TriggerParam11, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)拥有xxBuff类型改变属性 属性id:%d, 属性值:%d, Buff类型:%d 触发",
			defense.ID(), eff.TriggerEffect, value, skill.Info.TriggerParam11))
	}
}

func (m *Manager) DoPassiveSkillAliveNum(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := pse.psSkill.info
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.(*Member).attrM.AddAliveNumAttrEffect(skill.Info.Id, skill.Info.TriggerParam11, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)我方存活人数大于等于X时改变属性 属性id:%d, 属性值:%d, 存货人数:%d 触发",
			defense.ID(), eff.TriggerEffect, value, skill.Info.TriggerParam11))
	}
}

func (m *Manager) DoPassiveEffectBuffType(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	team := m.GetTeam(defense)
	team.BuffTypeEffect(uint32(eff.EffParam2), int(eff.EffParam1), uint32(eff.EffParam3))
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改指定buff_type的参数 类型:%d buff_type:%d, 影响值:%d 触发",
			defense.ID(), eff.EffParam1, eff.EffParam2, eff.EffParam3))
	}
}

func (m *Manager) DoPassiveEffectBuffLayer(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].EffectBuffAttr(attack, eff.EffFormula, uint32(eff.EffParam1), uint32(eff.EffParam2), uint32(eff.EffParam3), pse)
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改指定buff_type的层数 公式:%d 参数1:%d,参数2:%d,参数3:%d 触发",
					defense.ID(), eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3))
			}
		}
	} else {
		defense.EffectBuffAttr(attack, eff.EffFormula, uint32(eff.EffParam1), uint32(eff.EffParam2), uint32(eff.EffParam3), pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改指定buff_type的层数 公式:%d 参数1:%d,参数2:%d,参数3:%d 触发",
				defense.ID(), eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3))
		}
	}
}

func (m *Manager) DoPassiveEffectAddPS(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	defense.AddPsSkill(uint32(eff.EffParam1))
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)添加被动 被动id:%d 触发",
			defense.ID(), eff.EffParam1))
	}
}

// 普攻联动
func (m *Manager) DoPassiveSkillEffectComboNormalUnion(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	normalSkill := attack.GetSkillM().GetNormalSkill()
	if normalSkill == nil {
		panic(fmt.Sprintf("battle passive skill effect DoPassiveSkillEffectComboNormalUnion id:%d pse:%+v",
			attack.ID(), pse))
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(attack:%d)普攻联动 技能id:%d 系数:%d defense:%d",
			attack.ID(), normalSkill.info.Id, eff.EffParam1, defense.ID()))
	}
	m.AddComboNormalUnion(defense, normalSkill, int32(eff.EffParam1), pse.tUniter)
}

// 回击
func (m *Manager) DoPassiveSkillEffectFightBack(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := attack.GetSkillM().GetSkillByType(uint32(eff.EffParam2))
	if skill == nil {
		l4g.Errorf("DoPassiveSkillEffectFightBack: no skill type:%d, id:%d pseInfo:%+v",
			eff.EffParam2, attack.ID(), pse.psSkill.info.Info)
		return
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(attack:%d)回击 技能id:%d 系数:%d defense:%d",
			attack.ID(), skill.info.Id, eff.EffParam1, defense.ID()))
	}
	m.AddFightBack(defense, skill, int32(eff.EffParam1), pse.tUniter)
}

func (m *Manager) DoPassiveEffectAddShield(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, _ := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	value = int64(float64(attack.GetPSEffectValue(PsAttrShield, BaseFloatInt)) / BaseFloat * float64(defense.GetPSEffectValue(PsAttrBeShield, BaseFloatInt)) / BaseFloat * float64(value))
	sysBuff := goxml.GetData().BuffInfoM.Index(eff.TriggerEffect)
	if sysBuff == nil {
		panic(fmt.Sprintf("battle no find buff:%d", eff.TriggerEffect))
	}
	sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
	if sysBuffType == nil {
		panic(fmt.Sprintf("battle no find buff type :%d %d", eff.TriggerEffect, sysBuff.Type))
	}
	buff := m.NewBuff(attack, defense, sysBuff, sysBuffType, pse, value)
	defense.(*Member).GetBuffM().addBuff(buff, true, pse)
}

func (m *Manager) DoPassiveEffectCopyBuff(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {

	check := func(v *Buff) bool {
		if !CheckBuffInUse(v) {
			return false
		}
		switch uint32(eff.EffParam1) {
		case BuffCopyTypeHelp:
			return IsBenefitBuff(v.typeInfo)
		case BuffCopyTypeHarm:
			return IsHarmfulBuff(v.typeInfo)
		case BuffCopyTypeHarmfulAndNoControl:
			return !v.IsControl() && IsHarmfulBuff(v.typeInfo)
		default:
			panic(fmt.Sprintf("do copy buff type ID:%d error", eff.EffParam1))
		}
	}

	var needCopyBuffs []*Buff
	for _, buffs := range defense.GetBuffM().buffs {
		for _, buff := range buffs {
			if !check(buff) {
				continue
			}
			needCopyBuffs = append(needCopyBuffs, buff)
		}
	}

	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.psm.getTarget(pse, uint32(eff.EffParam2), targets, nil)
	totalNum := targets.GetNum()
	uniterArray := targets.GetUniters()
	var uniter Uniter
	for _, buff := range needCopyBuffs {
		for i := 0; i < totalNum; i++ {
			uniter = uniterArray[i]
			if uniter.ID() == defense.ID() {
				continue
			}
			//复制buff给目标
			uniter.GetBuffM().copyBuff(buff, true, pse, attack)
		}
	}
}

func (m *Manager) DoPassiveEffectChangePS(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	changedSkillInfo := goxml.GetData().PassiveSkillInfoM.Index(uint32(eff.EffParam1))
	if changedSkillInfo == nil {
		panic(fmt.Sprintf("%d cant find passive %d", pse.psSkill.info.Info.Id, eff.EffParam1))
	}

	if ePs, exist := defense.GetManager().psm.psSkills[int(changedSkillInfo.TriggerInfo.Event)][defense.ID()]; exist {
		for _, ps := range ePs {
			if ps.info.Info.Id == uint32(changedSkillInfo.Info.Id) {
				ps.setActive(false)
				defense.AddPsSkill(uint32(eff.EffParam2))
				if de() {
					m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改指定被动 原被动id:%d 修正被动id:%d 触发",
						defense.ID(), eff.EffParam1, eff.EffParam2))
				}
				break
			}
		}
	}
}

func (m *Manager) DoPassiveChangeArtifactCD(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	artifactM := defense.(*ArtifactManager)
	artifactM.skillM.ChangeRound(eff.EffParam3, eff.EffFormula, eff.EffParam1, eff.EffParam2, attack, defense, pse)
	if de() {
		m.GetRep().AddDebugEvent("效果:修改神器回合数")
	}
}

func (m *Manager) DoPassiveStealShield(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	clearedShield := defense.GetBuffM().ClearShield(pse, attack)
	if clearedShield == 0 {
		return
	}
	maxShield := int64(float64(attack.GetAttr(goxml.AttrAttack)) * (float64(eff.EffParam1) / BaseFloat))
	shield := clearedShield
	if shield > maxShield {
		shield = maxShield
	}
	sysBuff := goxml.GetData().BuffInfoM.Index(uint32(eff.EffParam2))
	if sysBuff == nil {
		panic(fmt.Sprintf("battle no find buff:%d", eff.EffParam2))
	}
	sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
	if sysBuffType == nil {
		panic(fmt.Sprintf("battle no find buff type :%d %d", eff.EffParam2, sysBuff.Type))
	}

	if rTargets == nil {
		panic(fmt.Sprintf("DoPassiveStealShield: no rTargets, id:%d pse:%+v", attack.ID(), pse))
	}
	redirectNum := rTargets.GetNum()
	uniterArray := rTargets.GetUniters()
	for i := 0; i < redirectNum; i++ {
		newID := m.NewAutoID()
		buff := NewBuff(attack, uniterArray[i], newID, sysBuff, sysBuffType)
		buff.SetValue(shield)
		uniterArray[i].GetBuffM().addBuff(buff, true, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:攻击方(%d)偷取防守方(%d)的护盾值(%d)给目标(%d)加护盾(%d)",
				attack.ID(), defense.ID(), clearedShield, uniterArray[i].ID(), shield))
		}
	}
}

func (m *Manager) DoPassiveHurtSelf(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.Hurted(attack, value, bt.SpecialType_HurtTypeHurtSelf, factor, pse)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("被动效果:%d自残", defense.ID()))
	}
}

func (m *Manager) DoPassiveSettleBuff(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	buff, ok := pse.args.(*Buff)
	if !ok {
		panic(fmt.Sprintf("%s DoPassiveSettleBuff, error pse.args is not Buff", pse.debug()))
	}
	var i int64
	for ; i < eff.EffParam1; i++ {
		m.SettleBuff(buff, pse)
		if buff.IsReset() {
			//这个buff可能整个结算行为中被清除了
			break
		}
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("被动效果:拥有者(%d)结算buff(%d) (%d)次 实际执行(%d)次",
			buff.owner.ID(), buff.ID(), eff.EffParam1, i))
	}
}

func (m *Manager) DoPassiveFullHurt(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.Hurted(attack, value, bt.SpecialType_HurtTypeFull, factor, pse)
}

func (m *Manager) DoPassiveCall(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	m.Call(attack, defense, uint32(eff.EffParam1), uint32(eff.EffParam2), uint32(eff.EffParam3), pse)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("被动效果:攻击方(%d)在位置(%d)召唤(%d), param2:%d, param3:%d",
			attack.ID(), defense.Pos(), eff.EffParam1, eff.EffParam2, eff.EffParam3))
	}

	fixedCount := 1 + int(attack.GetPSEffectValue(PsAttrCallEventAddTriggerCount, 0))
	for i := 0; i < fixedCount; i++ {
		m.TriggerPsSkill(attack, goxml.Event_Call, pse)
	}
}

func (m *Manager) DoPassiveSkillChangeHaloAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	team := m.GetTeam(defense)
	team.tUniter.AddHaloAttr(defense, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改光环属性 属性id:%d, 属性值%d",
			defense.ID(), eff.TriggerEffect, value))
	}
}

func (m *Manager) DoPassiveSkillChangeHaloPsAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	team := m.GetTeam(defense)
	team.tUniter.AddHaloPsAttr(defense, int(eff.TriggerEffect), value)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改特殊光环属性 属性id:%d, 属性值%d",
			defense.ID(), eff.TriggerEffect, value))
	}
}

func (m *Manager) DoPassiveSkillChangeForeveAttrWithLimit(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	attrType := int(eff.TriggerEffect)
	value0 := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, 0, pse)
	limitV := int64(float64(attack.GetAttrM().GetInitAttr(int(eff.EffParam3))) / BaseFloat * float64(eff.EffParam4))
	if pse.psSkill.limitValueForever == nil {
		pse.psSkill.limitValueForever = &BattleLimitValue{
			addAttrValue:    make(map[int]int64),
			reduceAttrValue: make(map[int]int64),
			owner:           pse.GetOwner(),
		}
	}
	value := pse.psSkill.limitValueForever.GetLimitedAttr(attrType, value0, limitV)
	if value == 0 {
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)(被动:%d)修改永久属性 属性id:%d, 值:%d 超过限制(%d)了! eff:%+v",
				attack.ID(), pse.psSkill.info.Info.Id, eff.TriggerEffect, value0, limitV, eff))
		}
		return
	}
	pse.psSkill.limitValueForever.AddAttr(attrType, value)
	addTime := ChangeAttrTimeForeve
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].ChangeAttr(addTime, BattleAdd, attrType, value)
			m.report.NewChangeAttr(attrType, value, attack, uniterArray[i], pse)
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改永久属性 属性id:%d, 值:%d",
					uniterArray[i].ID(), eff.TriggerEffect, value))
			}
		}
	} else {
		defense.ChangeAttr(addTime, BattleAdd, attrType, value)
		m.report.NewChangeAttr(attrType, value, attack, defense, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改永久属性 属性id:%d, 值:%d",
				defense.ID(), eff.TriggerEffect, value))
		}
	}
}

func (m *Manager) DoPassiveBless(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	defense.DoAddBuff(attack, eff.EffFormula, uint32(eff.EffParam1), uint32(eff.EffParam2), pse)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("被动效果:目标(%d)触发庇佑", defense.ID()))
	}
	m.TriggerPsSkill(defense, goxml.Event_Bless, pse)
}

func (m *Manager) DoPassiveExplosiveShield(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	if value <= 0 {
		return
	}
	useShield := int64(float64(value) / BaseFloat * float64(eff.EffParam4))
	attack.GetBuffM().UseShield(useShield, pse, attack)

	if rTargets == nil {
		panic(fmt.Sprintf("DoPassiveExplosiveShield: no rTargets, id:%d pse:%+v", attack.ID(), pse))
	}
	redirectNum := rTargets.GetNum()
	uniterArray := rTargets.GetUniters()
	for i := 0; i < redirectNum; i++ {
		fixedValue, _ := Formula516(m, 516, attack, uniterArray[i], value, eff.EffParam2, eff.EffParam3, pse)
		uniterArray[i].Hurted(attack, fixedValue, bt.SpecialType_HurtTypePassive, bt.FactorType_FactorNone, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)受到(%d)的爆盾伤害,值:%d",
				uniterArray[i].ID(), attack.ID(), fixedValue))
		}
	}
}

func (m *Manager) DoPassiveSplashHurt(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].Hurted(attack, value, bt.SpecialType_HurtTypeTransfer, factor, pse)
		}
	} else {
		defense.Hurted(attack, value, bt.SpecialType_HurtTypeTransfer, factor, pse)
	}
}

func (m *Manager) DoPassiveStealSoul(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if rTargets == nil {
		panic(fmt.Sprintf("DoPassiveStealSoul: no rTargets, id:%d pse:%+v", attack.ID(), pse))
	}

	defenseMem, ok := defense.(*Member)
	if !ok {
		panic(fmt.Sprintf("dDoPassiveStealSoul: defense %d not a member", defense.ID()))
	}
	if defenseMem.HasBuff(BuffTypeImmuneStealAndEat) {
		return
	}

	redirectNum := rTargets.GetNum()
	uniterArray := rTargets.GetUniters()
	for i := 0; i < redirectNum; i++ {
		uniter := uniterArray[i]
		m.StealSoul(attack, defense, uniter, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果44:攻击方(%d)偷取防守方(%d)的灵魂到位置(%d)",
				attack.ID(), defense.ID(), uniter.Pos()))
		}
	}
}

// 赛季羁绊能量(信仰点)增加
// param2 1:正能量 2:负能量
func (m *Manager) DoPassiveSkillAddSeasonLinkEnergy(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	isNegative := isSeasonLinkNegativeEnergy(eff.EffParam2)
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)

	var fixParam float64
	if isNegative {
		fixParam = float64(defense.GetPSEffectValue(PsAttrSeasonLinkNegEnergyFix, BaseFloatInt))
	} else {
		fixParam = float64(defense.GetPSEffectValue(PsAttrSeasonLinkEnergyFix, BaseFloatInt))
	}
	value = int64(float64(value) / BaseFloat * fixParam)
	if value <= 0 {
		return
	}

	team := m.GetTeam(defense)
	if isNegative {
		// 负能量的增加等价于正能量的减少
		team.DecSeasonLinkEnergy(value, attack, defense, pse, isNegative)
	} else {
		team.AddSeasonLinkEnergy(value, attack, defense, pse, isNegative)
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)增加赛季羁绊能量(信仰点):%d, 是负能量(%t)",
			defense.ID(), value, isNegative))
	}
}

// 赛季羁绊能量(信仰点)减少
// param2 1:正能量 2:负能量
func (m *Manager) DoPassiveSkillDecSeasonLinkEnergy(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	isNegative := isSeasonLinkNegativeEnergy(eff.EffParam2)
	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	if value <= 0 {
		return
	}

	team := m.GetTeam(defense)
	if isNegative {
		// 负能量的减少等价于正能量的增加
		team.AddSeasonLinkEnergy(value, attack, defense, pse, isNegative)
	} else {
		team.DecSeasonLinkEnergy(value, attack, defense, pse, isNegative)
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)减少赛季羁绊能量(信仰点):%d, 是负能量(%t)",
			defense.ID(), value, isNegative))
	}
}

// func (m *Manager) DoPassiveSkillChangeAttrWithLimit(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
// 	attrType := int(eff.TriggerEffect)
// 	value := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, 0, pse)
// 	limitV := int64(float64(attack.GetAttrM().GetInitAttr(int(eff.EffParam3))) / BaseFloat * float64(eff.EffParam4))
// 	if pse.psSkill.limitValueOneSkill == nil {
// 		pse.psSkill.limitValueOneSkill = &BattleLimitValue{
// 			addAttrValue:    make(map[int]int64),
// 			reduceAttrValue: make(map[int]int64),
// 		}
// 	}
// 	value = pse.psSkill.limitValueOneSkill.GetLimitedAttr(attrType, value, limitV)
// 	if value == 0 {
// 		if de() {
// 			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)(被动:%d)修改临时属性 属性id:%d, 值:%d 超过限制了!",
// 				attack.ID(), pse.psSkill.info.Info.Id, eff.TriggerEffect, value))
// 		}
// 		return
// 	}
// 	pse.psSkill.limitValueOneSkill.AddAttr(attrType, value)
// 	addTime := ChangeAttrTimeOneSkill
// 	if rTargets != nil {
// 		redirectNum := rTargets.GetNum()
// 		uniterArray := rTargets.GetUniters()
// 		for i := 0; i < redirectNum; i++ {
// 			uniterArray[i].ChangeAttr(addTime, BattleAdd, attrType, value)
// 			m.report.NewChangeAttr(attrType, value, attack, uniterArray[i], pse)
// 			if de() {
// 				m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改临时属性 属性id:%d, 值:%d",
// 					uniterArray[i].ID(), eff.TriggerEffect, value))
// 			}
// 		}
// 	} else {
// 		defense.ChangeAttr(addTime, BattleAdd, attrType, value)
// 		m.report.NewChangeAttr(attrType, value, attack, defense, pse)
// 		if de() {
// 			m.GetRep().AddDebugEvent(fmt.Sprintf("效果:(目标:%d)修改临时属性 属性id:%d, 值:%d",
// 				defense.ID(), eff.TriggerEffect, value))
// 		}
// 	}
// }

// 吞噬
func (m *Manager) DoPassiveSkillDevour(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.psm.getTarget(pse, uint32(eff.EffParam1), targets, nil)
	totalNum := targets.GetNum()
	uniterArray := targets.GetUniters()

	if totalNum == 0 {
		return
	}

	for i := 0; i < totalNum; i++ {
		uniter := uniterArray[i]
		if uniter.GetUniterType() == UniterTypePos {
			// 吃人的首段效果是选择空位或吞噬召唤物。因此有可能目标是空位，此时跳过即可
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果48:(吞噬:%d), 空位", uniter.ID()))
			}
			continue
		}

		//精英怪免疫吞噬
		if uniter.EliteType() > 0 {
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果48:(吞噬:%d), %d 精英怪免疫",
					uniter.ID(), uniter.EliteType()))
			}
			continue
		}

		uniter.Hurted(attack, uniter.GetHp(), bt.SpecialType_HurtTypeDevour, bt.FactorType_FactorNone, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果48:(吞噬:%d)", uniter.ID()))
		}

		m.TriggerPsSkill(attack, goxml.Event_Devour, pse)
	}
}

// 根据触发目标buff数据，添加对应层数的新buff
func (m *Manager) DoPassiveSkillAddBuffByEffTarget(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if rTargets == nil {
		panic(fmt.Sprintf("DoPassiveSkillAddBuffByEffTarget, no rTargets, attackID:%d, defenseID:%d, pse:%+v, eff:%+v",
			attack.ID(), defense.ID(), pse, eff))
	}

	buffID := uint32(eff.EffParam2)
	sysBuff := goxml.GetData().BuffInfoM.Index(buffID)
	if sysBuff == nil {
		panic(fmt.Sprintf("DoPassiveSkillAddBuffByEffTarget, no find buff:%d", buffID))
	}
	sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
	if sysBuffType == nil {
		panic(fmt.Sprintf("DoPassiveSkillAddBuffByEffTarget, no find buff type :%d %d", buffID, sysBuff.Type))
	}

	layer := defense.GetBuffM().GetBuffLayerByType(uint32(eff.EffParam1))
	if layer <= 0 {
		return
	}

	redirectNum := rTargets.GetNum()
	uniterArray := rTargets.GetUniters()
	for i := 0; i < redirectNum; i++ {
		uniter := uniterArray[i]
		uniter.GetBuffM().successAddBuff(defense, uniter, pse, sysBuff, sysBuffType, layer)
	}
}

func (m *Manager) DoPassiveSkillChangeForeveAttrWithLimit2(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	attrType := int(eff.TriggerEffect)
	value0 := m.SimpleZeroFormula(attack, defense, eff.EffFormula, eff.EffParam1, 0, 0, pse)
	limitV := attack.GetAttrM().GetInitAttr(int(eff.EffParam3)) // 参数3有可能不是基础属性，此时limitV为0
	maxV := int64(eff.EffParam4)
	// limitV为0时，上限直接用参数4的值
	if limitV == 0 || limitV > maxV {
		limitV = maxV
	}
	if pse.psSkill.limitValueForever == nil {
		pse.psSkill.limitValueForever = &BattleLimitValue{
			addAttrValue:    make(map[int]int64),
			reduceAttrValue: make(map[int]int64),
			owner:           pse.GetOwner(),
		}
	}
	value := pse.psSkill.limitValueForever.GetLimitedAttr(attrType, value0, limitV)
	if value == 0 {
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果50:(目标:%d)(被动:%d)修改永久属性失败 属性id:%d, 初始值:%d, 限制值:%d, eff:%+v",
				attack.ID(), pse.psSkill.info.Info.Id, eff.TriggerEffect, value0, limitV, eff))
		}
		return
	}
	pse.psSkill.limitValueForever.AddAttr(attrType, value)
	addTime := ChangeAttrTimeForeve
	if rTargets != nil {
		redirectNum := rTargets.GetNum()
		uniterArray := rTargets.GetUniters()
		for i := 0; i < redirectNum; i++ {
			uniterArray[i].ChangeAttr(addTime, BattleAdd, attrType, value)
			m.report.NewChangeAttr(attrType, value, attack, uniterArray[i], pse)
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果50:(目标:%d)修改永久属性 属性id:%d, 值:%d",
					uniterArray[i].ID(), eff.TriggerEffect, value))
			}
		}
	} else {
		defense.ChangeAttr(addTime, BattleAdd, attrType, value)
		m.report.NewChangeAttr(attrType, value, attack, defense, pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果50:(目标:%d)修改永久属性 属性id:%d, 值:%d",
				defense.ID(), eff.TriggerEffect, value))
		}
	}
}

// 继承单次护盾值并施加护盾
func (m *Manager) DoPassiveEffectAddShieldByInherit(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	argsBuffBeAdd, ok := pse.args.(*ArgsBuffBeAdd)
	if !ok {
		panic(fmt.Sprintf("DoPassiveEffectAddShieldByInherit error, pse.args is not ArgsBuffBeAdd. %s", pse.debug()))
	}

	value0 := argsBuffBeAdd.GetValue()
	value := int64(float64(value0) / BaseFloat * float64(eff.EffParam1))
	if value == 0 {
		l4g.Errorf("DoPassiveEffectAddShieldByInherit error, value0:%d, debug:%s", value0, pse.debug())
		return
		// panic(fmt.Sprintf("DoPassiveEffectAddShieldByInherit error, value is 0. %s", pse.debug()))
	}

	buff := m.NewBuff(attack, defense, argsBuffBeAdd.GetBuffInfo(), argsBuffBeAdd.GetBuffTypeInfo(), pse, value)
	defense.(*Member).GetBuffM().addBuff(buff, true, pse)

	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果51:(目标:%d)继承单次护盾值并施加护盾, 继承护盾值:%d, 修正系数:%d, 修正后的值:%d",
			defense.ID(), value0, eff.EffParam1, value))
	}
}

func (m *Manager) DoPassiveEffectSwapPos(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if eff.EffParam2 == 0 {
		panic(fmt.Sprintf("DoPassiveEffectSwapPos error, EffParam2 illegal. %s", pse.debug()))
	}

	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.psm.getTarget(pse, uint32(eff.EffParam2), targets, defense)
	if targets.GetNum() == 0 {
		l4g.Debugf("DoPassiveEffectSwapPos: no target")
		return
	}
	uniterArray := targets.GetUniters()

	target := uniterArray[0]
	if defense.ID() == target.ID() {
		l4g.Debugf("DoPassiveEffectSwapPos: target is self")
		defense.SetLastPos(0) // 未成功换位时，将此值置为0。防止下次目标选择时，重复选择
		return
	}

	srcPos := defense.Pos()
	dstPos := target.Pos()
	defense.SetPos(dstPos)
	defense.SetLastPos(srcPos)
	if target.GetUniterType() == UniterTypeMember {
		target.SetPos(srcPos)
		target.SetLastPos(dstPos)
	}

	var count uint32 = 1
	if uint32(eff.EffParam1) > count {
		count = uint32(eff.EffParam1)
	}

	l4g.Debugf("DoPassiveEffectSwapPos, 效果52:(%d-%d)与(%d-%d)交换位置, 次数效果(%d)",
		defense.ID(), srcPos, target.ID(), dstPos, count)

	m.report.NewSwapPos(pse, srcPos, dstPos, count, defense, target)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果52:(%d-%d)与(%d-%d)交换位置, 次数效果(%d)",
			defense.ID(), srcPos, target.ID(), dstPos, count))
	}

	for i := 0; i < int(count); i++ {
		m.TriggerPsSkill(defense, goxml.Event_SwapPos, pse)
	}
}

func (m *Manager) DoPassiveEffectCopyBuffWhenSwapPos(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	redirectNum := rTargets.GetNum()
	if redirectNum < 1 {
		l4g.Debugf("DoPassiveEffectCopyBuffWhenSwapPos, no rTargets, id:%d", defense.ID())
		return
	}
	uniterArray := rTargets.GetUniters()
	target := uniterArray[0]

	if defense.ID() == target.ID() {
		return
	}

	check := func(v *Buff) bool {
		if !CheckBuffInUse(v) {
			return false
		}
		switch uint32(eff.EffParam1) {
		case BuffCopyTypeHelp:
			return IsBenefitBuff(v.typeInfo)
		case BuffCopyTypeHarm:
			return IsHarmfulBuff(v.typeInfo)
		case BuffCopyTypeHarmfulAndNoControl:
			return !v.IsControl() && IsHarmfulBuff(v.typeInfo)
		default:
			panic(fmt.Sprintf("DoPassiveEffectCopyBuffWhenSwapPos:do copy buff type ID:%d error", eff.EffParam1))
		}
	}

	for _, buffs := range defense.GetBuffM().buffs {
		for _, buff := range buffs {
			if !check(buff) {
				continue
			}
			target.GetBuffM().copyBuff(buff, true, pse, attack)
		}
	}

	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果53:(%d)的buff，复制给了(%d)", defense.ID(), target.ID()))
	}
}

// 增加技能攻击次数
func (m *Manager) DoPassiveSkillAddSkillAttackCount(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	addCount := m.SimpleZeroFormula(attack, defense, eff.EffFormula, int64(eff.EffParam4), eff.EffParam2, eff.EffParam3, pse)
	if addCount < 1 {
		addCount = 1
	}

	skill := attack.GetSkillM().GetSkillByType(uint32(eff.EffParam1))
	if skill == nil {
		panic(fmt.Sprintf("DoPassiveSkillAddSkillAttackCount, skill not found, id:%d, skillType:%d, pseInfo:%+v",
			attack.ID(), eff.EffParam1, pse.psSkill.info.Info))
	}

	oldCount := skill.GetCount()
	if oldCount >= int64(eff.EffParam4) {
		l4g.Debugf("DoPassiveSkillAddSkillAttackCount, count hit max, id:%d, skillId:%d, count:%d, max:%d",
			attack.ID(), skill.info.Id, oldCount, eff.EffParam4)
		return
	}

	newCount := 1 + addCount
	if newCount > int64(eff.EffParam4) {
		newCount = int64(eff.EffParam4)
	}

	skill.SetCount(newCount)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果54:(目标:%d)增加技能攻击次数 技能id:%d, 原次数:%d, 新次数:%d",
			defense.ID(), skill.info.Id, oldCount, newCount))
	}
}

// 根据buff类型, 添加相同层数的buff
//
//nolint:varnamelen
func (m *Manager) DoPassiveSkillAddBuffByBuffType(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	team := m.GetTeam(attack)
	var buff *Buff
	var u Uniter
	for _, mem := range team.members {
		buff = mem.buffM.GetOneBuff(uint32(eff.EffParam1))
		u = mem
		if buff != nil {
			break
		}
	}

	if buff == nil {
		l4g.Debugf("DoPassiveSkillAddBuffByBuffType, no buff: attackID:%d, defenseID:%d, buffID:%d",
			u.ID(), defense.ID(), eff.EffParam1)
		return
	}

	defense.GetBuffM().successAddBuff(u, defense, pse, buff.info, buff.typeInfo, buff.layer)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果55:(目标:%d)加buff, buff类型:%d, buffID:%d",
			defense.ID(), eff.EffParam1, buff.info.Id))
	}
}

// 根据参数选择技能联动
func (m *Manager) DoPassiveSkillEffectComboBySkill(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skillID := uint32(eff.EffParam1)
	skill := attack.AddNewSkill(skillID, goxml.SkillPosOther, nil)
	if skill == nil {
		panic(fmt.Sprintf("battle passive skill effect DoPassiveSkillEffectComboBySkill id:%d pse:%+v",
			attack.ID(), pse))
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果56:(attack:%d)根据参数选择技能联动 技能id:%d 系数:%d defense:%d",
			attack.ID(), eff.EffParam1, eff.EffParam2, defense.ID()))
	}
	m.AddComboNormalUnion(defense, skill, int32(eff.EffParam2), pse.tUniter)
}

func (m *Manager) DoPassiveEffectCleanBuffAddAttr(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	redirectNum := rTargets.GetNum()
	if redirectNum < 1 {
		l4g.Debugf("DoPassiveEffectCleanBuffAddAttr, no rTargets, id:%d", defense.ID())
		return
	}

	check := func(v *Buff) bool {
		if !CheckBuffInUse(v) {
			return false
		}
		switch uint32(eff.EffParam1) {
		case BuffCopyTypeHelp:
			return IsBenefitBuff(v.typeInfo) && v.CanRemove()
		case BuffCopyTypeHarm:
			return IsHarmfulBuff(v.typeInfo) && v.CanRemove()
		default:
			panic(fmt.Sprintf("DoPassiveEffectCleanBuffAddAttr, type ID:%d error", eff.EffParam1))
		}
	}

	layLimit := uint32(eff.EffParam2)
	var addLayer uint32
	for _, buffs := range defense.GetBuffM().buffs {
		for _, buff := range buffs {
			if !check(buff) {
				continue
			}

			var decLayer uint32
			tmp := layLimit - addLayer
			if buff.layer > tmp {
				addLayer += tmp
				decLayer = tmp
			} else {
				addLayer += buff.layer
				decLayer = buff.layer
			}

			defense.GetBuffM().decrLayer(buff, decLayer, pse, attack)
			if addLayer >= layLimit {
				break
			}
		}
	}

	value0 := int64(float64(addLayer) / BaseFloat * float64(eff.EffParam3))
	if value0 <= 0 {
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果57:(目标:%d)(被动:%d)驱散buff, addLayer:%d, eff:%+v",
				attack.ID(), pse.psSkill.info.Info.Id, addLayer, eff))
		}
		return
	}

	if pse.psSkill.limitValueForever == nil {
		pse.psSkill.limitValueForever = &BattleLimitValue{
			addAttrValue:    make(map[int]int64),
			reduceAttrValue: make(map[int]int64),
			owner:           pse.GetOwner(),
		}
	}
	attrType := int(eff.TriggerEffect)
	limitVal := int64(eff.EffParam4)
	value := pse.psSkill.limitValueForever.GetLimitedAttr(attrType, value0, limitVal)
	if value == 0 {
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果57:(目标:%d)(被动:%d)修改永久属性失败 属性id:%d, 初始值:%d, 限制值:%d, eff:%+v",
				attack.ID(), pse.psSkill.info.Info.Id, eff.TriggerEffect, value0, limitVal, eff))
		}
		return
	}
	pse.psSkill.limitValueForever.AddAttr(attrType, value)

	uniterArray := rTargets.GetUniters()
	for i := 0; i < redirectNum; i++ {
		uniterArray[i].ChangeAttr(ChangeAttrTimeForeve, BattleAdd, attrType, value)
		m.report.NewChangeAttr(attrType, value, attack, uniterArray[i], pse)
		if de() {
			m.GetRep().AddDebugEvent(fmt.Sprintf("效果57:(目标:%d)修改永久属性 属性id:%d, 值:%d",
				uniterArray[i].ID(), attrType, value))
		}
	}
}

func (m *Manager) DoPassiveEffectHurt2Cure(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	redirectNum := rTargets.GetNum()
	if redirectNum < 1 {
		l4g.Debugf("DoPassiveEffectHurt2Cure, no rTargets, id:%d", defense.ID())
		return
	}

	argsDecHp, ok := pse.args.(*ArgsDecHp)
	if !ok {
		panic(fmt.Sprintf("DoPassiveEffectHurt2Cure error, pse.args is not ArgsDecHp. %s", pse.debug()))
	}

	loseHp := argsDecHp.GetLoseHp()
	value := int64(float64(loseHp) / BaseFloat * float64(eff.EffParam1))
	if value <= 0 {
		l4g.Debugf("DoPassiveEffectHurt2Cure, value<=0, id:%d, loseHp:%d", defense.ID(), loseHp)
		return
	}

	uniterArray := rTargets.GetUniters()
	for i := 0; i < redirectNum; i++ {
		uniterArray[i].Cured(attack, value, bt.SpecialType_SpecialTypeNone, bt.FactorType_FactorNone, pse)
	}
}

// 吃人效果要继承的属性列表
var EffectEatAddAttrs = []int{
	goxml.AttrAttack,
	goxml.AttrHp,
	goxml.AttrPhyDef,
	goxml.AttrMagDef,
}

func (m *Manager) DoPassiveEffectEat(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	redirectNum := rTargets.GetNum()
	if redirectNum != 1 {
		panic(fmt.Sprintf("DoPassiveEffectEat, no rTargets. %s", pse.debug()))
	}
	uniter := rTargets.GetUniters()[0]

	defenseMem, ok := defense.(*Member)
	if !ok {
		panic(fmt.Sprintf("DoPassiveEffectEat: defense %d not a member", defense.ID()))
	}
	if defenseMem.HasBuff(BuffTypeImmuneStealAndEat) {
		return
	}

	// 继承属性
	if eff.EffParam1 > 0 {
		for _, attrType := range EffectEatAddAttrs {
			value := int64(float64(defense.GetAttr(attrType)) / BaseFloat * float64(eff.EffParam1))
			if value > eff.EffParam3 {
				value = eff.EffParam3
			}
			uniter.ChangeAttr(ChangeAttrTimeForeve, BattleAdd, attrType, value)
			m.report.NewChangeAttr(attrType, value, attack, uniter, pse)
			if de() {
				m.GetRep().AddDebugEvent(fmt.Sprintf("效果59:(目标:%d)修改永久属性 属性id:%d, 上限修正值:%d, 实际添加属性值:%d",
					uniter.ID(), attrType, eff.EffParam3, value))
			}
		}
	}

	// 吃人-加隐藏buff
	buffID := uint32(eff.EffParam2)
	sysBuff := goxml.GetData().BuffInfoM.Index(buffID)
	if sysBuff == nil {
		panic(fmt.Sprintf("DoPassiveEffectEat, battle no find buff:%d", buffID))
	}
	sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
	if sysBuffType == nil {
		panic(fmt.Sprintf("DoPassiveEffectEat, battle no find buff type :%d %d", buffID, sysBuff.Type))
	}
	defense.GetBuffM().successAddBuff(uniter, defense, pse, sysBuff, sysBuffType, 0)

	m.TriggerPsSkill(attack, goxml.Event_EatHero, pse)
}

func (m *Manager) DoPassiveEffectKillNotDead(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	hurtType := bt.SpecialType_HurtTypeKillNotDead
	if eff.EffParam4 == 1 {
		hurtType = bt.SpecialType_HurtTypeKillIgnoreLockBlood
	}
	defense.Hurted(attack, value, hurtType, factor, pse)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果60: %d 击杀, 类型:%d", defense.ID(), hurtType))
	}
}

// 净化转移buff
//
//nolint:varnamelen
func (m *Manager) DoPassiveEffectTransferBuff(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if eff.EffParam1 <= 0 {
		panic(fmt.Sprintf("DoPassiveEffectTransferBuff, limitLayer is 0, eff:%+v", eff))
	}

	isTrans := m.CheckRate(eff.EffParam2) // 是否转移buff
	transNum := rTargets.GetNum()
	uniterArray := rTargets.GetUniters()
	if transNum < 1 {
		isTrans = false
		l4g.Debugf("DoPassiveEffectTransferBuff, no rTargets, id:%d", defense.ID())
	}

	check := func(v *Buff, isControl bool) bool {
		if !CheckBuffInUse(v) {
			return false
		}
		if eff.EffParam3 == 0 && !v.CanRemove() {
			return false
		}
		if eff.EffParam4 > 0 && eff.EffParam4 == v.info.Subtype {
			return false
		}

		if isControl {
			return v.IsControl()
		}
		return IsHarmfulBuff(v.typeInfo)
	}

	limitLayer := uint32(eff.EffParam1)
	var totalRemoveLayer uint32
	//优先控制
	for _, buffs := range defense.GetBuffM().buffs {
		for _, buff := range buffs {
			if !check(buff, true) {
				continue
			}

			var decLayer uint32
			tmp := limitLayer - totalRemoveLayer
			if buff.layer > tmp {
				totalRemoveLayer += tmp
				decLayer = tmp
			} else {
				totalRemoveLayer += buff.layer
				decLayer = buff.layer
			}

			if isTrans {
				for i := 0; i < transNum; i++ {
					uniter := uniterArray[i]
					uniter.GetBuffM().successAddBuff(defense, uniter, pse, buff.info, buff.typeInfo, decLayer)
				}
			}
			defense.GetBuffM().decrLayer(buff, decLayer, pse, attack)
			if totalRemoveLayer >= limitLayer {
				break
			}
		}
	}

	//其他负面buff
	if totalRemoveLayer < limitLayer {
		for _, buffs := range defense.GetBuffM().buffs {
			for _, buff := range buffs {
				if !check(buff, false) {
					continue
				}

				var decLayer uint32
				tmp := limitLayer - totalRemoveLayer
				if buff.layer > tmp {
					totalRemoveLayer += tmp
					decLayer = tmp
				} else {
					totalRemoveLayer += buff.layer
					decLayer = buff.layer
				}

				if isTrans {
					for i := 0; i < transNum; i++ {
						uniter := uniterArray[i]
						uniter.GetBuffM().successAddBuff(defense, uniter, pse, buff.info, buff.typeInfo, decLayer)
					}
				}
				defense.GetBuffM().decrLayer(buff, decLayer, pse, attack)
				if totalRemoveLayer >= limitLayer {
					break
				}
			}
		}
	}

	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果61: 驱散转移buff, defense:%d, 实际驱散buff总层数:%d, 量表参数:%+v, 是否成功转移:%t",
			defense.ID(), totalRemoveLayer, eff, isTrans))
	}
}

// 根据当前添加buff层数添加指定buff
func (m *Manager) DoPassiveEffectAddBuffByBuffLayer(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	argsBuffBeAdd, ok := pse.args.(*ArgsBuffBeAdd)
	if !ok {
		panic(fmt.Sprintf("DoPassiveEffectAddBuffByBuffLayer error, pse.args is not ArgsBuffBeAdd. %s", pse.debug()))
	}

	layer := argsBuffBeAdd.GetBuffLayer()
	if layer == 0 {
		l4g.Errorf("DoPassiveEffectAddBuffByBuffLayer, layer illegal, debug:%s", pse.debug())
		return
	}

	buffID := uint32(eff.EffParam1)
	sysBuff := goxml.GetData().BuffInfoM.Index(buffID)
	if sysBuff == nil {
		panic(fmt.Sprintf("DoPassiveEffectAddBuffByBuffLayer, no find buff:%d", buffID))
	}
	sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
	if sysBuffType == nil {
		panic(fmt.Sprintf("DoPassiveEffectAddBuffByBuffLayer, no find buff type :%d %d", buffID, sysBuff.Type))
	}

	defense.GetBuffM().successAddBuff(attack, defense, pse, sysBuff, sysBuffType, layer)

	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果62:(目标:%d)根据当前添加buff层数添加指定buff, 添加buffID:%d, 添加数量:%d",
			defense.ID(), sysBuff.Id, layer))
	}
}

func (m *Manager) DoPassiveEffectChangeSkillAttackDefenser(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	oneSkillAttack, ok := pse.args.(*OneSkillAttack)
	if !ok {
		panic(fmt.Sprintf("DoPassiveEffectChangeSkillAttackDefenser error, pse.args is not OneSkillAttack. %s", pse.debug()))
	}
	m.report.NewChangeSkillAttackDefenser(pse, oneSkillAttack.GetAttack(), oneSkillAttack.GetDefense(), defense)
	oneSkillAttack.SetDefenser(defense)
}

// 召唤亡灵（继承被动技能，属性累加继承不覆盖）
func (m *Manager) DoPassiveEffectCallDepartedSpirit(attack, callTarget Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if rTargets == nil {
		l4g.Errorf("DoPassiveEffectCallDeadMember: target is nil.")
		return
	}
	num := rTargets.GetNum()
	uniterArray := rTargets.GetUniters()
	var posTarget Uniter
	if num < 1 {
		l4g.Debugf("DoPassiveEffectTransferBuff, no rTargets, id:%d", callTarget.ID())
		return
	}
	posTarget = uniterArray[0]

	monsterId := uint32(eff.EffParam1)
	callMember := callTarget.(*Member)
	member := NewSoulMember(posTarget.Pos(), callMember) // 效果同偷人所以用NewSoulMember
	member.SetMonsterType(MonsterTypeCallDepartedSpirit) // 亡灵召唤物有单独的type
	member.SetTemplateID(monsterId)                      // 前端要用的模版ID
	team := m.GetTeamByIndex(posTarget.Index())
	team.AddMember(member, nil, nil, nil, true, false)
	team.addCallCount()
	member.initSkills(true)
	member.SetCallerID(attack.ID())
	m.report.NewCall(pse, attack, member)

	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果64:攻击方(%d)在位置(%d)召唤(%d), param1:%d, param2:%d",
			attack.ID(), posTarget.Pos(), callMember.ID(), eff.EffParam1, eff.EffParam2))
	}
	fixedCount := 1 + int(attack.GetPSEffectValue(PsAttrCallEventAddTriggerCount, 0))
	for i := 0; i < fixedCount; i++ {
		m.TriggerPsSkill(attack, goxml.Event_Call, pse)
	}
}

// 指定两个目标决斗
func (m *Manager) DoPassiveEffectDuel(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	var firstTarget, secondTarget Uniter
	var bindTarget bool

	if eff.EffParam4 == 1 { // 离间决斗
		if rTargets.GetNum() == 2 {
			firstTarget = rTargets.GetUniters()[0]
			secondTarget = rTargets.GetUniters()[1]
		} else {
			l4g.Debugf("DoPassiveEffectDuel: targets num not 2.")
			return
		}
		bindTarget = true
	} else { // 正常决斗
		firstTarget = defense
		if rTargets.GetNum() == 1 {
			secondTarget = rTargets.GetUniters()[0]
		} else {
			l4g.Errorf("DoPassiveEffectDuel: no target.")
			return
		}
	}
	// firstTarget优先出手
	m.TriggerPsSkill(firstTarget, goxml.Event_BeforeToDuel, pse)
	m.TriggerPsSkill(secondTarget, goxml.Event_BeforeBeDuel, pse)
	m.DoDuelSkill(firstTarget, secondTarget, eff.EffParam1, eff.EffParam2, bindTarget)
	if secondTarget.GetStatus() == UniterAlive {
		m.DoDuelSkill(secondTarget, firstTarget, eff.EffParam1, eff.EffParam3, bindTarget)
	}
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果65:(目标：%d)优先出手，和(目标：%d)使用技能（技能类型：%d）进行决斗, param2:%d, param3:%d",
			firstTarget.ID(), secondTarget.ID(), eff.EffParam1, eff.EffParam2, eff.EffParam3))
	}
}

// 执行决斗的技能
func (m *Manager) DoDuelSkill(attack, defense Uniter, skillType, effParam int64, isBindTarget bool) {
	skill := attack.GetSkillM().GetSkillByType(uint32(skillType))
	if skill == nil {
		l4g.Errorf("DoDuelSkill: no skill type:%d, id:%d skills:%v",
			skillType, attack.ID(), attack.GetSkillM().GetOwnerSkills())
		return
	}
	if isBindTarget {
		skill.setBindTarget(defense)
	}
	oneSkill := NewOneSkillByPassiveSkill(m, skill, int32(effParam), NextSkillTypeNone, nil)
	defer gPool.FreeOneSkill(oneSkill)
	oneSkill.DoRun()
}

// 特殊自残，伤害计算到被动触发者身上
func (m *Manager) DoPassiveEffectHurtSelfDamageToTrigger(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	value, factor := m.CalFormula(attack, defense, eff.EffFormula, eff.EffParam1, eff.EffParam2, eff.EffParam3, pse)
	defense.Hurted(attack, value, bt.SpecialType_HurtTypeHurtSelfDamageToTrigger, factor, pse)
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果66:%d特殊自残（伤害算触发者身上）", defense.ID()))
	}
}

// 目标的执行顺序倒序
func (m *Manager) DoReverseTargetsActOrder(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	if rTargets.GetNum() <= 1 {
		return
	}

	var memberHeap MemberHeap // 为了排序统一，使用MemberHeap结构，但是不使用堆特性
	for i := 0; i < rTargets.GetNum(); i++ {
		mem, ok := rTargets.GetUniters()[i].(*Member)
		if !ok {
			continue
		}
		memberHeap = append(memberHeap, mem)
	}
	sort.Slice(memberHeap, memberHeap.Less)

	for i := 0; i < memberHeap.Len()/2; i++ {
		replaceIndex := memberHeap.Len() - 1 - i
		m.setReplaceActMember(memberHeap[i].ID(), memberHeap[replaceIndex].ID())
		m.setReplaceActMember(memberHeap[replaceIndex].ID(), memberHeap[i].ID())
	}

	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果67:目标的执行顺序倒序。 倒序前ID列表:%+v", memberHeap))
	}
}

func (m *Manager) DoPassiveEffectResetSkillCD(attack, defense Uniter, pse *PsSkillExe, eff *goxml.PSEffectData, rTargets *Uniters) {
	skill := defense.GetSkillM().GetSkillByType(uint32(eff.EffParam1))
	if skill == nil {
		l4g.Errorf("DoPassiveEffectResetSkillCD: no skill type:%d, id:%d pseInfo:%+v",
			eff.EffParam1, defense.ID(), pse.psSkill.info.Info)
		return
	}
	skill.resetCD()
	if de() {
		m.GetRep().AddDebugEvent(fmt.Sprintf("效果68:%d重置%d的技能%d CD", attack.ID(), defense.ID(), skill.info.Id))
	}
}
