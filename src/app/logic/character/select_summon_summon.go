package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

type SelectSummonLog struct {
	GuaranteeWays  []uint32
	WishHeroes     []uint32
	SummonCount    uint32
	UpRedHeroCount uint32
	RedHeroCount   uint32
}

func (s *SelectSummon) Summon(summonType uint32, summonCostType uint32, info *goxml.SelectSummonInfoExt, rd *rand.Rand, heroID uint32) (uint32, []*cl.Resource, []*cl.Resource, *cl.SelectSummonSummon, *SelectSummonLog) {
	v2Summon := s.SelectSummon.Summon
	if v2Summon == nil {
		s.initSelectSummonSummon()
		v2Summon = s.SelectSummon.Summon
	}
	v2Clone := v2Summon.Clone()

	log := &SelectSummonLog{}

	var summonCount int
	var costs []*cl.Resource
	if summonType == uint32(common.SELECT_SUMMON_TYPE_SST_SINGLE) {
		log.SummonCount = 1
		if summonCostType == uint32(common.SELECT_SUMMON_COST_TYPE_SSCT_FREE) {
			if v2Clone.FreeTime+log.SummonCount > goxml.GetData().SelectSummonConfigInfoM.FreeTimes {
				l4g.Errorf("user:%d select summon free time:%d not enough", s.owner.ID(), v2Clone.FreeTime)
				return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil, nil, nil
			}
		} else if summonCostType == uint32(common.SELECT_SUMMON_COST_TYPE_SSCT_ITEM) {
			costs = info.CostClRes
		} else {
			l4g.Errorf("user:%d select summon costType:%d error", s.owner.ID(), summonCostType)
			return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil, nil, nil
		}
	} else {
		log.SummonCount = goxml.GetData().DivineDemonConfigInfoM.SummonPlural
		if summonCostType == uint32(common.SELECT_SUMMON_COST_TYPE_SSCT_FREE) {
			if v2Clone.FreeTime+log.SummonCount > goxml.GetData().SelectSummonConfigInfoM.FreeTimes {
				l4g.Errorf("user:%d select summon free time:%d not enough", s.owner.ID(), v2Clone.FreeTime)
				return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil, nil, nil
			}
		} else if summonCostType == uint32(common.SELECT_SUMMON_COST_TYPE_SSCT_ITEM) {
			costs = info.PluralCosts
		} else {
			l4g.Errorf("user:%d select summon costType:%d error", s.owner.ID(), summonCostType)
			return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil, nil, nil
		}
	}

	ret := uint32(cret.RET_OK)
	if len(costs) > 0 {
		ret, _ = s.owner.CheckResourcesSize(costs)
	}

	if ret != uint32(cret.RET_OK) {
		return ret, nil, nil, nil, nil
	}
	summonCount = int(log.SummonCount)

	log.GuaranteeWays = make([]uint32, 0, summonCount)

	v2Clone.TotalSummonCount += uint32(summonCount)
	var awards []*cl.Resource
	log.WishHeroes = make([]uint32, 0, summonCount)
	for i := 0; i < int(log.SummonCount); i++ {
		var guaranteeWay SummonWay
		var summonResource []*cl.Resource
		var wishHero bool
		for index := range v2Clone.ColorGuarantee {
			v2Clone.ColorGuarantee[index]++
		}
		groupId, guarantee, red, gdGroupClass := goxml.GetData().SelectSummonGroupInfoM.RandomGroup(rd, v2Clone.ColorGuarantee, info.Id)
		if groupId == 0 {
			l4g.Errorf("user:%d divine demon Summon v2 failed", s.owner.ID())
			return uint32(cret.RET_SELECT_SUMMON_RAND_GROUP_FAILED), nil, nil, nil, nil
		}

		if guarantee {
			guaranteeWay = SummonColorfulGuarantee
		} else {
			guaranteeWay = SummonNormal
		}

		if red {
			v2Clone.TotalRedCard++
			log.RedHeroCount++
		}

		v2Clone.NotUpHeroCount, summonResource, wishHero = goxml.GetData().SummonGroupInfoM.RandomClassSelectSummon(rd, groupId, gdGroupClass, v2Clone.NotUpHeroCount, red, heroID, v2Clone.TotalRedCard)
		if summonResource == nil {
			l4g.Errorf("user:%d select summon summon  failed", s.owner.ID())
			return uint32(cret.RET_SELECT_SUMMON_RAND_CLASS_FAILED), nil, nil, nil, nil
		}

		log.GuaranteeWays = append(log.GuaranteeWays, uint32(guaranteeWay))
		if wishHero {
			log.WishHeroes = append(log.WishHeroes, 1)
			log.UpRedHeroCount++
		} else {
			log.WishHeroes = append(log.WishHeroes, 0)
		}

		addSelectSummonGuaranteeAfterSummon(v2Clone.ColorGuarantee, summonResource)
		awards = append(awards, summonResource...)
	}
	if summonCostType == uint32(common.SELECT_SUMMON_COST_TYPE_SSCT_FREE) {
		v2Clone.FreeTime += log.SummonCount
	}

	return uint32(cret.RET_OK), awards, costs, v2Clone, log
}

func addSelectSummonGuaranteeAfterSummon(copyGuarantee []uint32, resources []*cl.Resource) {
	for index, resource := range resources {
		if resource == nil {
			l4g.Errorf("addSelectSummonGuaranteeAfterSummon index:%d is nil", index)
			continue
		}
		if resource.Type == uint32(common.RESOURCE_HERO) {
			heroInfo := goxml.GetData().HeroInfoM.Index(resource.Value)
			if heroInfo == nil {
				l4g.Errorf("addSelectSummonGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
				continue
			}
			switch heroInfo.Rare {
			case uint32(common.QUALITY_RED):
				copyGuarantee[goxml.SelectSummonRedGuaranteeIndex] = 0
			case uint32(common.QUALITY_ORANGE):
				copyGuarantee[goxml.SelectSummonOrangeGuaranteeIndex] = 0
			case uint32(common.QUALITY_PURPLE):
				copyGuarantee[goxml.SelectSummonPurpleGuaranteeIndex] = 0
			}
		} else if resource.Type == uint32(common.RESOURCE_FRAGMENT) {
			fragmentInfo := goxml.GetData().FragmentInfoM.Index(resource.Value)
			if fragmentInfo == nil {
				l4g.Errorf("addSelectSummonGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
				continue
			}
			switch fragmentInfo.Rare {
			case uint32(common.QUALITY_RED):
				copyGuarantee[goxml.SelectSummonRedFragmentGuaranteeIndex] = 0
			}
		}
	}
}

func (s *SelectSummon) TestSummon(summonCount uint32, info *goxml.SelectSummonInfoExt, rd *rand.Rand, heroID uint32, smsg *cl.L2C_SelectSummonTestSummon) (uint32, *cl.SelectSummonSummon, *SelectSummonLog) {
	v2Summon := s.SelectSummon.Summon
	if v2Summon == nil {
		s.initSelectSummonSummon()
		v2Summon = s.SelectSummon.Summon
	}
	v2Clone := v2Summon.Clone()
	log := &SelectSummonLog{}
	log.GuaranteeWays = make([]uint32, 0, summonCount)

	v2Clone.TotalSummonCount += uint32(summonCount)
	log.WishHeroes = make([]uint32, 0, summonCount)
	smsg.RedSummonCount = make(map[uint32]uint32)
	smsg.RedCard = make(map[uint32]uint32)
	smsg.OrangeCard = make(map[uint32]uint32)
	smsg.PurpleCard = make(map[uint32]uint32)
	smsg.RedFragment = make(map[uint32]uint32)
	smsg.OthersResource = make(map[uint32]uint32)
	for i := 0; i < int(summonCount); i++ {
		var guaranteeWay SummonWay
		var summonResource []*cl.Resource
		var wishHero bool
		for index := range v2Clone.ColorGuarantee {
			v2Clone.ColorGuarantee[index]++
		}
		groupId, guarantee, red, gdGroupClass := goxml.GetData().SelectSummonGroupInfoM.RandomGroup(rd, v2Clone.ColorGuarantee, info.Id)
		if groupId == 0 {
			l4g.Errorf("user:%d divine demon Summon v2 failed", s.owner.ID())
			return uint32(cret.RET_SELECT_SUMMON_RAND_GROUP_FAILED), nil, nil
		}

		if guarantee {
			guaranteeWay = SummonColorfulGuarantee
		} else {
			guaranteeWay = SummonNormal
		}

		if red {
			v2Clone.TotalRedCard++
			log.RedHeroCount++
		}

		v2Clone.NotUpHeroCount, summonResource, wishHero = goxml.GetData().SummonGroupInfoM.RandomClassSelectSummon(rd, groupId, gdGroupClass, v2Clone.NotUpHeroCount, red, heroID, v2Clone.TotalRedCard)
		if summonResource == nil {
			l4g.Errorf("user:%d select summon test summon  failed", s.owner.ID())
			return uint32(cret.RET_SELECT_SUMMON_RAND_CLASS_FAILED), nil, nil
		}
		for index, resource := range summonResource {
			if resource == nil {
				l4g.Errorf("addSelectSummonGuaranteeAfterSummon index:%d is nil", index)
				continue
			}
			if resource.Type == uint32(common.RESOURCE_HERO) {
				heroInfo := goxml.GetData().HeroInfoM.Index(resource.Value)
				if heroInfo == nil {
					l4g.Errorf("addSelectSummonGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
					continue
				}
				switch heroInfo.Rare {
				case uint32(common.QUALITY_RED):
					smsg.RedSummonCount[v2Clone.ColorGuarantee[goxml.SelectSummonRedGuaranteeIndex]]++
					smsg.RedCard[heroInfo.Id]++
					smsg.TotalRedCount++
				case uint32(common.QUALITY_ORANGE):
					smsg.OrangeCard[heroInfo.Id]++
				case uint32(common.QUALITY_PURPLE):
					smsg.PurpleCard[heroInfo.Id]++
				}
			} else if resource.Type == uint32(common.RESOURCE_FRAGMENT) {
				fragmentInfo := goxml.GetData().FragmentInfoM.Index(resource.Value)
				if fragmentInfo == nil {
					l4g.Errorf("addSelectSummonGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
					continue
				}
				switch fragmentInfo.Rare {
				case uint32(common.QUALITY_RED):
					smsg.RedFragment[fragmentInfo.Id]++
				default:
					smsg.OthersResource[fragmentInfo.Id]++
				}
			} else {
				smsg.OthersResource[resource.Value]++
			}
		}

		log.GuaranteeWays = append(log.GuaranteeWays, uint32(guaranteeWay))
		if wishHero {
			log.WishHeroes = append(log.WishHeroes, 1)
			log.UpRedHeroCount++
		} else {
			log.WishHeroes = append(log.WishHeroes, 0)
		}

		addSelectSummonGuaranteeAfterSummon(v2Clone.ColorGuarantee, summonResource)
	}

	return uint32(cret.RET_OK), v2Clone, log
}
