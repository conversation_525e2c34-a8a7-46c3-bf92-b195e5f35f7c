package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/in/l2m"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
)

const (
	UserGroup   = 2 << 4
	userGroupOP = UserGroup - 1
)

const (
	GameYearsPerDay         = 100   // 现实1天对应游戏里的100年
	BattleReportSegmentSize = 64500 //比logic最大包64k小一点
)

const (
	MaxItemUseCount = 999 //使用道具的最大数量限制
)

// 杂项int类型
const (
	MaxBodyPoolNum            = 5     //淬体池数量
	BodyTrainNumOne           = 1     //淬体1次
	BodyTrainNumTen           = 10    //淬体10次
	PracticeTickResetNum      = 10    //修炼每次创建Ticks的数量
	PracticeTickInterval      = 5     //修炼每次结算时间5s
	LuckyChanceMaxNum         = 50    //拥有机缘上限
	PracticeTickOfflineMaxNum = 5760  //离线最多计算的tick数 8个小时 28800/5
	PracticeBuffAdd           = 1     //修炼增益Buff
	PracticeBuffDec           = 2     //修炼减益Buff
	WudaoResetGold            = 50    //悟道重置消耗元宝
	NpcDailyTalkNum           = 3     //道友每日交谈次数
	NpcWedAffinity            = 100   //道友成亲亲密度
	NpcRoomMaxTeamID          = 10    //后端限制最大队伍id 策划扩展后再调整
	NpcRoomMaxTeamEffectCnt   = 100   //限制最大派遣队伍效果次数
	HalfHourSecs              = 1800  //半小时(秒)
	EightHourSecs             = 28800 //8小时(秒)
	EquipmentUpgradeStone     = 6     //法宝升级石
	FacContestAppointMaxNum   = 6     //门派大比约定选项
	FriendRequestsMaxNum      = 20    //好友请求列表存储数量
	//RecommendFriendNum         = 5     //好友推荐数量
	RecommendedFriendInterval  = 15 //已经推荐好友屏蔽间隔
	RecommendedFriendResetTime = 60 //重置推荐
	//UserNameLength            = 14    //名字长度, utf-8 rune长度, 用config配置
	ChatContentLength  = 30 //聊天内容长度
	UserNameMaxLength  = 35 //角色名长度 压测用的
	UserSnapshotReqMax = 20 //一次最多拉取20个快照数据
	GiftCodeMaxLength  = 20 //礼包码最大长度
)

// 杂项uint32类型
const (
	LevelupInviteMaxProp uint32 = 10 //境界要求好友加成上限
	EquipmentMaxStage    uint32 = 6  //法宝最大阶数
)

// 修炼加成类型
const (
	PracticeAddPercentExp          = 1 //经验比加成
	PracticeAddPercentEnergy       = 2 //元气比加成
	PracticeAddPercentExpAndEnergy = 3 //经验、元气同时加成
)

const (
	BodyPoolValueStay = 0 //淬体池Value不变
	BodyPoolValueAdd  = 1 //淬体池Value增加
	BodyPoolValueDec  = 2 //淬体池Value减少
)

const (
	BodyPoolID1 = 1 //淬体1号池
	BodyPoolID2 = 2 //淬体2号池
	BodyPoolID3 = 3 //淬体3号池
	BodyPoolID4 = 4 //淬体4号池
	BodyPoolID5 = 5 //淬体5号池
)

const (
	IndexOne   = 1 //选择1
	IndexTwo   = 2 //选择2
	IndexThree = 3 //选择3
	IndexFour  = 4 //选择4
)

// 机缘类型
const (
	LuckyChanceShop          = 1 //水府商人
	LuckyChanceBeton         = 2 //投机修士
	LuckyChanceBattleSelf    = 3 //挑战自己
	LuckyChanceBattleMonster = 4 //挑战怪物
	LuckyChanceSelect        = 5 //选择
)

// 功能开启
const (
	FuncLevelConOr  = 0 //level或者vip达成
	FuncLevelConAnd = 1 //level和vip同时达成
)

// knight类型，1主角，2灵宠
const (
	KnightMainRole = 1
	KnightPet      = 2
)

// 灵宠升星的 消耗类型
const (
	PetUpStarCostID      = 1
	PetUpStarCostQuality = 2
)

// PassiveSkill的Target的类型
const (
	PassiveSkillTargetSelf      = 1
	PassiveSkillTargetAll       = 2
	PassiveSkillTargetCareerHun = 3
	PassiveSkillTargetCareerShu = 4
	PassiveSkillTargetCareerTi  = 5
)

// 功法书 主动类 被动类
const (
	SkillBookTypeActive  = 1
	SkillBookTypePassive = 2
)

const (
	AwardTagNone   = 0x0
	AwardTagMerged = 0x1 //资源已进行过唯一性合并，不需要再进行合并处理
	AwardTagMail   = 0x2 //如果背包已满，则通过邮件发送
)

// 修炼资源获取加成index
const (
	PracticeAdditionExp    = iota //修为
	PracticeAdditionEnergy        //元气
	PracticeAdditionMax
)

// 修炼随机范围值
const (
	PracticeBoundMinExp    = iota //修为min
	PracticeBoundMaxExp           //修为max
	PracticeBoundMinEnergy        //元气min
	PracticeBoundMaxEnergy        //元气max
	PracticeBoundMax
)

// 阵容槽位限制
const (
	FormatPetMinSlot       = 1 //灵宠最小槽位
	FormatPetMaxSlot       = 3 //灵宠最大槽位
	FormatEquipmentMinSlot = 1 //法宝最小槽位
	FormatEquipmentMaxSlot = 6 //法宝最大槽位
)

// 邮件ID
const (
	MailIDLevelup                             uint32 = 1   //升级
	MailIDBagFull                             uint32 = 2   //背包已满，奖励邮件发送
	MailIDGM                                  uint32 = 3   //GM后台奖励
	MailIDArenaDaily                          uint32 = 4   //竞技场每日段位排行奖励
	MailIDArenaSeason                         uint32 = 6   //竞技场赛季段位排行奖励
	MailIDGuildSendMail                       uint32 = 8   //公会发送邮件
	MailIDGuildBeApprove                      uint32 = 9   //通过入会审核
	MailIDGuildBeKick                         uint32 = 10  //被踢出公会
	MailIDGuildLeaderTransferForNewLeader     uint32 = 11  //被委任为会长
	MailIDGuildLeaderAutoTransferForNewLeader uint32 = 12  //会长被自动转让(发给新会长)
	MailIDGuildLeaderAutoTransferForOldLeader uint32 = 13  //会长被自动转让(发给老会长)
	MailIDGuildLeaderTransfer                 uint32 = 14  //会长被转让
	MailIDGuildLeaderAutoTransfer             uint32 = 15  //自动转让会长(发给除新老会长外的其它成员)
	MailIDArenaAvatarFrame                    uint32 = 20  //竞技场赛季头像框奖励
	MailIDBagFullDungeon                      uint32 = 101 //背包已满，奖励邮件发送 - 主线领取挂机奖励/加速挂机
	MailIDBagFullForest                       uint32 = 102 //背包已满，奖励邮件发送 - 密林领奖/密林掠夺和复仇战斗结算
	MailIDBagFullMirage                       uint32 = 103 //背包已满，奖励邮件发送 - 个人BOSS战斗结算
	MailIDMonthlyCardGrowUpAward              uint32 = 104 //成长月卡 - 补发每日奖励邮件
	MailIDMonthlyCardGrowUpSoonExpire         uint32 = 105 //成长月卡 - 过期倒计时邮件
	MailIDMonthlyCardGrowUpExpired            uint32 = 106 //成长月卡 - 过期邮件
	MailIDMonthlyCardSupplyAward              uint32 = 107 //补给月卡 - 补发每日奖励邮件
	MailIDMonthlyCardSupplySoonExpire         uint32 = 108 //补给月卡 - 过期倒计时邮件
	MailIDMonthlyCardSupplyExpired            uint32 = 109 //补给月卡 - 过期邮件
	MailIDPassDailyActive                     uint32 = 110 //战令 - 日常活跃周期结束补发奖励邮件
	MailIDPassMazeActive                      uint32 = 111 //战令 - 迷宫活跃周期结束补发奖励邮件
	MailIDDivineDemonTask                     uint32 = 112 //神魔抽卡 - 补发任务奖励
	MailIDWrestleDaily                        uint32 = 21  // 神树争霸 - 每日结算
	MailIDWrestleSeason                       uint32 = 22  // 神树争霸 - 赛季结算
	MailIDWrestleSeasonRank                   uint32 = 23  // 神树争霸 - 赛季排名
	MailIDWrestleLevelAwardsReissue           uint32 = 24  // 神树争霸 - 赛场等级奖励补发（只有赛季最后一天）
	MailIDGuildDungeonWeeklyRank              uint32 = 25  // 公会副本 - 周结算
	MailIDGuildDungeonMonthlyRank             uint32 = 26  // 公会副本 - 月结算
	MailIDGuildDungeonTaskCompensateAwards    uint32 = 27  // 公会副本 - 任务补偿奖励
	MailIDGuildDungeonBossBoxCompensateAwards uint32 = 28  // 公会副本 - boss宝箱补偿奖励
	MailIDFlowerOccupyAwards                  uint32 = 29  // 密林 - 据点模式奖励
	MailIDDailyWishCloseActivity              uint32 = 117 // 每日许愿 - 活动结束
	MailIDPassDiamondActive                   uint32 = 115 // 战令 - 钻石活跃周期结束补发奖励邮件
	MailIDPassProphetActive                   uint32 = 116 // 战令 - 先知活跃战令周期结束补发奖励邮件
	MailIDArtifactDebut                       uint32 = 118 // 神器首发 - 补发奖励
	MailIDGuildDonateAward                    uint32 = 119 // 公会捐赠 - 补发奖励
	MailIDTowerSeason                         uint32 = 127 // 百塔 - 奖励补发
	//MailIDGodPresent                          uint32 = 128 // 777抽活动 - 开启邮件
	MailIDWeeklyDivisionAward    uint32 = 129 // 公会副本 - 每周段位奖励
	MailIDSeasonDivisionAward    uint32 = 130 // 公会副本 - 每赛季段位奖励
	MailIDSeasonTopDivision      uint32 = 131 // 公会副本 - 赛季最高段位
	MailIDSeasonDivisionRank     uint32 = 132 // 公会副本 - 赛季段位排行
	MailIDWorldBossRoomRank      uint32 = 133 //世界boss - 房间榜结算邮件
	MailIDWorldBossPartitionRank uint32 = 134 //世界boss - 战区榜结算邮件
	MailIDTowerSeasonRank        uint32 = 135 // 百塔 - 结算邮件
	//MailIDGodPresentSecond                    uint32 = 136 // 777抽活动 - 第二期开启邮件
	MailIDActivityStory              uint32 = 137 // 故事互动 - 更换活动
	MailIDPeakGuess                  uint32 = 140 // 巅峰竞技场 - 竞猜奖励
	MailIDPeakPhase                  uint32 = 141 // 巅峰竞技场 - 小周期排名奖励
	MailIDPeakSeason                 uint32 = 142 // 巅峰竞技场 - 赛季排名奖励
	MailIDWebRechargeCoupon          uint32 = 145 // 充值 - 官网充值代金券奖励
	MailIDRechargeCouponExtra        uint32 = 146 // 充值 - 代金券充值返利
	MailIDGodPresentThird            uint32 = 147 // 777抽活动 - 第三期开启邮件
	MailIDRiteRecycle                uint32 = 150 // 阵法回收 - 奖励补发
	MailIDGstLeader                  uint32 = 151 // 公会战 - 领导奖励
	MailIDGstGroupRank               uint32 = 152 // 公会战 - 排名奖励
	MailIDSeasonLinkRecycle          uint32 = 153 // 阵法回收 - 奖励补发
	MailIDGstFirstOccupy             uint32 = 156 // 公会战 - 首次占领奖励
	MailIDSADivisionAward            uint32 = 172 // 赛季竞技场 - 段位奖励
	MailIDSARankAward                uint32 = 173 // 赛季竞技场 - 排行奖励
	MailIDSADivisionAwardCompensate  uint32 = 174 // 赛季竞技场 - 段位补偿奖励
	MailIDDailyAttendance            uint32 = 175 // 累计登录优化 - 奖励补发
	MailIDGstDragonWin               uint32 = 187 // 公会战 - 龙战胜利
	MailIDGstDragonLose              uint32 = 188 // 公会战 - 龙战失败
	MailIDGstDragonTask              uint32 = 189 // 公会战 - 任务奖励补发
	MailIDTalentTreeRecycle          uint32 = 195 // 天赋树 - 回馈奖励
	MailIDSociety                    uint32 = 201 // 主线邮件
	MailIDActivityTowerSeasonRank    uint32 = 203 // 百塔冲榜结算邮件
	MailIDGSTChallengeTotalScoreRank uint32 = 205 // 公会战新擂台赛-积分排行奖励邮件
	MailIDSeasonJewelryRecycle       uint32 = 207 // 赛季装备 - 回收奖励
	MailIDSeasonDoorRest             uint32 = 209 // 赛季开门玩法 - 挂机优化补偿
	MailIDGuildMobTaskBeCancel       uint32 = 220 // 公会任务被取消
	MailIDTowerPokemonRank           uint32 = 240 // 宠物爬塔 - 结算邮件

	MailIDSeasonComplianceTotalRankReward     uint32 = goxml.MailIDSeasonComplianceTotalRankReward     // 赛季冲榜总排行排名奖励
	MailIDSeasonComplianceStageID1RankReward  uint32 = goxml.MailIDSeasonComplianceStageID1RankReward  // 赛季冲榜阶段1排行奖励
	MailIDSeasonComplianceStageID1ScoreReward uint32 = goxml.MailIDSeasonComplianceStageID1ScoreReward // 赛季冲榜阶段1积分奖励
	MailIDSeasonComplianceStageID2RankReward  uint32 = goxml.MailIDSeasonComplianceStageID2RankReward  // 赛季冲榜阶段2排行奖励
	MailIDSeasonComplianceStageID2ScoreReward uint32 = goxml.MailIDSeasonComplianceStageID2ScoreReward // 赛季冲榜阶段2积分奖励
	MailIDSeasonComplianceStageID3RankReward  uint32 = goxml.MailIDSeasonComplianceStageID3RankReward  // 赛季冲榜阶段3排行奖励
	MailIDSeasonComplianceStageID3ScoreReward uint32 = goxml.MailIDSeasonComplianceStageID3ScoreReward // 赛季冲榜阶段3积分奖励
	MailIDSeasonComplianceStageID4RankReward  uint32 = goxml.MailIDSeasonComplianceStageID4RankReward  // 赛季冲榜阶段4排行奖励
	MailIDSeasonComplianceStageID4ScoreReward uint32 = goxml.MailIDSeasonComplianceStageID4ScoreReward // 赛季冲榜阶段4排行奖励

	MailIDQuestionnaire                uint32 = 10001 // 问卷邮件
	MailIDActivityComplianceReparation uint32 = 10003 // 开服冲榜活动补偿邮件
	MailIDActivityComplianceRank       uint32 = 10004 // 开服冲榜结算邮件
	MailIDActivityTowerRank            uint32 = 10005 // 地宫冲榜结算邮件
	MailIDActivityMirageRank           uint32 = 10008 // 幻境冲榜结算邮件
	MailIDRebase                       uint32 = 10009 // 开服返利邮件
	MailIDActivityWeb                  uint32 = 10010 // 国服官网充值
	MailIDTowerCompliance              uint32 = 10011 // 爬塔冲榜
	MailIDMirageCompliance             uint32 = 10012 // 幻境冲榜
)

// 根据资源来源，获取邮件的系统ID
func GetMailIDByResourceReason(reason uint32) uint32 {
	switch reason {
	case uint32(log.RESOURCE_CHANGE_REASON_ONHOOK), uint32(log.RESOURCE_CHANGE_REASON_SPEED_ONHOOK):
		return MailIDBagFullDungeon
	case uint32(log.RESOURCE_CHANGE_REASON_MIRAGE_FIGHT):
		return MailIDBagFullMirage
	}
	return MailIDBagFull
}

// 有背包限制的资源类型
const BagSpaceLimitTypeNum = 4 //英雄，宝石，纹章，装备

// 商店价格是否随购买次数变化
const (
	ShopBuySizeFix        = 0
	ShopBuySizeDailyReset = 1
)

const MaxFragmentCompose = 99
const MaxShopBuySize = 99
const MaxSkillFormat = 5 //主角功法孔最大个数

var SkillHolesPos = [...]uint32{201, 202, 301, 302, 303} // 主角功法孔的pos id
var ActiveSkillHoles = SkillHolesPos[:2]
var PassiveSkillHoles = SkillHolesPos[2:]

const (
	ChangeTagFlushPower uint64 = 0x01 //战力更新
	ChangeTagFlushPill  uint64 = 0x02 //丹药更新
)

const (
	RecoverTypePetReborn          = 1 //灵宠重生
	RecoverTypePetDestroy         = 2 //灵宠献祭
	RecoverTypePetFragmentDestory = 3 //灵宠碎片献祭
	RecoverTypeEquipRebirth       = 4 //法宝重生
	RecoverTypeEquipMelting       = 5 //法宝熔炼
	RecoverTypePillRecycle        = 6 //丹药凝练
	RecoverTypeHerbsRecycle       = 7 //草药凝练
	RecoverTypeThroneRecycle      = 8 //神器重生
)

const (
	EquipmentSuitTwo   uint32 = 2 //法宝两件套
	EquipmentSuitFour  uint32 = 4 //法宝四件套
	EquipmentSuitExtra uint32 = 8 //法宝额外两件套
)

// 道友性格
const (
	NpcCharacterInitAffinity        = 1  //初始亲密度为x点
	NpcCharacterDailyDecAffinity    = 2  //每天0点减少亲密度x点（最少到0）
	NpcCharacterTalkAddAffinity     = 3  //每次交谈额外增加x点亲密度
	NpcCharacterTalkDecAffinity     = 4  //每次交谈增加的亲密度减少x点（最少到0）
	NpcCharacterPresentAddAffinity  = 5  //每次送礼额外增加x点亲密度
	NpcCharacterPresentDecAffinity  = 6  //每次送礼增加的亲密度减少x点（最少到0）
	NpcCharacterWedDecProp          = 7  //成亲/结义成功率下降x%
	NpcCharacterWedAddProp          = 8  //成亲/结义成功率上升x%
	NpcCharacterPresentXAddAffinity = 9  //送礼时赠送x类道具亲密度额外增加10点（x对应礼物在item表里的道具类型6道具，其value1）
	NpcCharacterPresentXDecAffinity = 10 //送礼时赠送x类道具亲密度额外减少10点（x对应礼物在item表里的道具类型6道具，其value1）
)

const TowerBeginID = 1

const (
	FairyDivineSkillMax   = 3 //灵宠幻境占卜格技能数
	FairylandFirstStage   = 1 //灵宠幻境初始地图
	FairySpringHealAll    = 1 //全体恢复血量30%
	FairySpringHealSingle = 2 //随机一个血量低于50%的单位恢复满血
	FairySpringRebirth    = 3 //随机复活一个已阵亡灵宠
)

const (
	WorldBeginChapter             = 1
	WorldBeginChapterRow          = 0
	WorldBeginChapterCol          = 0
	WorldDisturbMonsterPriceGroup = 2
	WorldMessageMaxNum            = 20 //留言板上留言数量上限
	WorldMessageMaxLength         = 30 //单条留言长度上限
	WorldMessageLoadTimeout       = 3  //留言板加载超时时间
)

const (
	DataLoadNone uint32 = 0 //数据未加载
	DataLoading  uint32 = 1 //数据加载中
	DataLoaded   uint32 = 2 //数据加载完成
)

var WorldDisturbMonsterBuffs = [...]uint32{1001, 1002, 1003}

const (
	NpcAddBusinessByID    = 1 //直接添加商人
	NpcAddBusinessByGroup = 2 //根据group随机
)

const (
	FacContestFloorAppoint = 1 //门派大比约定
	FacContestFloorBattle  = 2 //门派大比对决
)

// 门派大比胜利约定
const (
	FacContestWinRoundX     = 1 //x回合获胜
	FacContestWinLiveX      = 2 //己方x人不死
	FacContestWinLeftHpXp   = 3 //我方总血量大于x%
	FacContestWinLiveRoundX = 4 //坚持x回合
)

const (
	HallOfFameShow     = 50 //名人堂显示前50名
	HallOfFameTopThree = 3  //前三名可以被点赞
)

// 道友任务类型
const (
	NpcTaskBattle = 1 //战斗
	NpcTaskCost   = 2 //消耗道具
)

// 道友任务lock条件
const (
	NpcTaskLockGoodness       = 1 //善值达到x
	NpcTaskLockEvil           = 2 //恶值达到x
	NpcTaskLockGoodnessOrEvil = 3 //善恶值达到x
	NpcTaskLockAffinity       = 4 //好感度达到x
	NpcTaskLockWed            = 5 //需要结义
)

var IllegalNameRune = [13]rune{
	0x00,   //\0
	0x09,   //\t
	0x2c,   //,
	0x22,   //"
	0x60,   //`
	0x1a,   //ctrl+z
	0x0a,   //\n
	0x0d,   //\r
	0x27,   //'
	0x25,   //%
	0x5c,   //\
	0x2d,   //-
	0x3000, //全角空格的unicode
}

var HasIllegalNameRune = func(c rune) bool {
	/*国际化考虑先不限制这个
	if len(string(c)) >= 4 { //检查emoji
		return true
	}*/
	for _, v := range IllegalNameRune {
		if v == c {
			return true
		}
	}
	return false
}

const (
	DailyPlayTimeMax int64 = 5400 //每日游戏时常
)

const (
	FormationMaxPos      uint32 = 5 //布阵的最大位置
	FormationArtifMaxPos uint32 = 3 //神器布阵的最大位置
)

// 战役
const (
	OnhookMaxTime   uint32 = 43200 //挂机时间上限 - 12小时
	OnhookSpeedTime uint32 = 7200  //加速挂机时间
	FightTimeLimit  uint32 = 3     //两次战斗最小间隔时间 - 秒
)

// 资源更新
const (
	OptimizeAwards int = 1
	OptimizeCosts  int = 2
)

// 英雄自身技能数量
const (
	SkillNumOthers uint32 = 3
	SkillNumLegend uint32 = 4
)

// HeroBackLegalStar 英雄回退合法星级
var HeroBackLegalStar = map[uint32]bool{
	5: true,
}

// HeroChangeLegalRace 英雄转换合法种族
var HeroChangeLegalRace = map[uint32]bool{
	goxml.RaceEmpire: true,
	goxml.RaceForest: true,
	goxml.RaceMoon:   true,
}

// HeroChangeLegalStar 英雄转换合法星级
var HeroChangeLegalStar = map[uint32]bool{
	4: true,
	5: true,
}

// HeroChange2SummonGroupID 英雄转换对应的召唤组ID
// map[种族]map[星级]summon_group_info表中的group_id
//
//nolint:mnd
var HeroChange2SummonGroupID = map[uint32]map[uint32]uint32{
	goxml.RaceEmpire: {
		5: 304,
	},
	goxml.RaceForest: {
		5: 305,
	},
	goxml.RaceMoon: {
		5: 306,
	},
}

// 商店
const (
	ShopRefreshTypeNone uint32 = 0 //随机商店刷新类型 - 不存在的类型，用于判断
	ShopRefreshTypeFree uint32 = 1 //随机商店刷新类型 - 免费
	ShopRefreshTypePaid uint32 = 2 //随机商店刷新类型 - 付费
)

// 先知抽卡类型
var AssocSummon = []uint32{
	goxml.AssocSummonEmpire,
	goxml.AssocSummonWoodland,
	goxml.AssocSummonEclipse,
	goxml.AssocSummonDemon,
}

// 碎片
const (
	CompExpectType uint32 = 1 //碎片合成类型   定向合成
	CompRandomType uint32 = 2 //               随机合成
)

// 装备-接口用到的常量
const (
	EquipWear         uint32 = 1  //装备操作类型  - 穿
	EquipRemove       uint32 = 2  //                脱
	EquipOneKeyWear   uint32 = 3  //            一键穿
	EquipOneKeyRemove uint32 = 4  //          一键脱
	EquipQuickLimit   uint32 = 10 //  装备的快速强化、快速附魔的等级限制
	EquipWearMaxNum   uint32 = 4  // 英雄可穿装备上限
)

// 装备-初始养成等级
const (
	EquipInitialStrengthLevel  uint32 = 1 // 装备初始强化等级
	EquipInitialRefineExp      uint32 = 0 // 装备初始精炼经验
	EquipInitialRefineLevel    uint32 = 0 // 装备初始精炼等级
	EquipInitialEnchantLevel   uint32 = 0 // 装备初始附魔等级
	EquipInitialEvolutionLevel uint32 = 0 // 装备初始进阶等级
	EquipInitialEnchantSeed    uint32 = 0 // 装备初始附魔种子
)

// 装备-槽位
const (
	EquipWeapon  uint32 = 1 //装备-武器
	EquipClothes uint32 = 2 //装备-衣服
	EquipHelmet  uint32 = 3 //装备-头盔
	EquipShoes   uint32 = 4 //装备-鞋子
)

// 更新阵容战力类型
const (
	FormationPowerUpdateByHero       uint32 = 1 // 英雄变化，改变阵容战力
	FormationPowerUpdateByGlobalAttr uint32 = 2 // 全局属性变化，改变阵容战力 注:只要是养成里涉及全局属性的，全部走此type
	FormationPowerUpdateByIds        uint32 = 3 //
)

const (
	UserCreateBaseID uint32 = 1 //创角的默认头像1
)

// 悬赏任务
const (
	DispatchFormulaParam float64 = 600 // 加速任务消耗钻石公式的参数
)

// 材料本
const (
	TrialInitialLevel = 1 // 材料本初始level
	TrialOneStar      = 1 //  材料本一星
	TrialTwoStar      = 2
	TrialThreeStar    = 3
)

// 材料本战斗目标，达成目标，获得星星
const (
	TrialTargetNone  = 0 // 无条件(获胜就达成)
	TrialTargetOne   = 1 // 击杀需要击杀的敌人数量大于等于【参数】
	TrialTargetTwo   = 2 // 回合数小于等于【参数】
	TrialTargetThree = 3 // 我方阵亡人数小于等于【参数】
	TrialTargetFour  = 4 // 我方所有存活单位总血量百分比大于等于【参数】
	TrialTargetFive  = 5 // 存活人数大于等于【参数】
	TrialTargetSix   = 6 // 单回合全队最高累计伤害（包括溢出）大于等于【参数】
)

const (
	TrialOnHookDuration = 3600
)

const MaxCharactUtf8Len int = 4 //字符的最大utf8编码的字节数

// 纹章
const (
	EmblemSlotNone          = 0 //无效的槽位
	EmblemSlotOne           = 1 // 纹章槽位1
	EmblemSlotTwo           = 2 // 纹章槽位2
	EmblemWearMaxNum uint32 = 2 // 纹章槽位总数
)

// 纹章-初始养成等级
const (
	EmblemInitialStrengthLevel uint32 = 1 // 纹章初始强化等级
	EmblemInitialBlessingLevel uint32 = 0 // 纹章初始祝福等级
)

// 限制操作的频率，比如客户端消息请求 id->毫秒
const (
	OIViewUser           = iota //查看玩家信息
	OIViewFormation             //查看玩家阵容
	OIChatFrequencyTime         //世界聊天 - 废弃
	OIRecommendFriend           //推荐好友
	OIBattle                    //战斗
	OIDungeon                   //主线战斗
	OITower                     //爬塔战斗
	OITrial                     //材料本战斗
	OIDuel                      //切磋
	OIClientInfo                //记录客户端信息
	OIChatPrivate               //私聊
	OIChatGuild                 //公会聊天
	OIChatWorld                 //世界聊天
	OIRevive                    //重生（英雄、装备、符文）
	OITowerstar                 //条件爬塔战斗
	OIWrestle                   //神树争霸
	OIFlowerbedRecommend        //密林推荐花坛
	OIMirage                    //幻境挑战
	OITowerSeason               //百塔战斗
	OIWorldBoss                 //世界BOSS
	OIActivityStory             //夏日活动
	OISeasonDungeon             //赛季主线
	OISeasonArenaRefresh        //赛季竞技场刷新
	OISeasonArenaFight          //赛季竞技场战斗
	OIFlowerOccupyAssist        //密林小助手占矿推荐列表
	OIViewUserDefPower
	OISeasonDoor
	OISeasonJewelryWear //赛季装备一键穿脱
	OIMax
)

// OperateIntervals 操作间隔
//
//nolint:mnd
var OperateIntervals = [OIMax]int64{ //后端自己做的限制，可以直接加这里
	OIViewUser:           500,
	OIViewFormation:      300,
	OIChatFrequencyTime:  1000,
	OIRecommendFriend:    1000, //前端限制了1s，后端就小一点 //1s刷新一次推荐好友
	OIBattle:             500,  //战斗
	OIDungeon:            500,  //主线战斗
	OITower:              500,  //爬塔战斗
	OITrial:              500,  //材料本战斗
	OIDuel:               500,  //切磋
	OIClientInfo:         1000, //最多1s一次
	OIRevive:             2000, //最多2s一次
	OITowerstar:          500,
	OIWrestle:            500, //神树争霸战斗
	OIFlowerbedRecommend: 200, //密林推荐花坛
	OIMirage:             500,
	OIWorldBoss:          500,
	OITowerSeason:        500, //百塔战斗
	OIActivityStory:      500,
	OISeasonDungeon:      500,
	OISeasonArenaRefresh: 1000, //赛季竞技场刷新
	OISeasonArenaFight:   500,  //赛季竞技场
	OIFlowerOccupyAssist: 200,  //密林小助手占矿推荐列表
	OIViewUserDefPower:   500,
	OISeasonDoor:         1000,
	OISeasonJewelryWear:  200,
}

const (
	DaySecs = 86400
)

// 个人boss
const (
	MiragePlayerCount        uint32  = 10000 //玩家人数 - 用于计算过关梯队的计算
	MirageDefaultMinPassRate float64 = 6000  //默认最小过关超越率60%
)

const (
	RivalFormation      = iota // 对手信息
	RivalBattleSnapshot        // 对手固定阵容的快照(单独保存)
)

// 公会 - 职位
const (
	GuildGradeLeader   = 1 // 会长
	GuildGradeDeputy   = 2 // 副会长
	GuildGradeMember   = 3 // 普通成员
	GuildGradeRegiment = 4 // 团长
)

// 公会 - command的type
const (
	GuildManagerLeader     = 0 // 设为会长
	GuildManagerDeputy     = 1 // 管理副会长(任命or撤销)
	GuildManagerKick       = 2 // 踢人
	GuildManagerRegiment   = 3 // 管理团长(任命or取消)
	GuildUserJoinIn        = 0 // 加入公会
	GuildUserApply         = 1 // 申请公会
	GuildUserCancelApply   = 2 // 取消申请
	GuildUserQuickJoin     = 3 // 快速加入
	GuildUserQuickTransfer = 4 // 快速转会
	GuildBadgeBackground   = 1 // 公会徽章的背景
	GuildBadgeIcon         = 2 // 公会徽章的图案
)

// 操作次数统计
const (
	OpWorldChat   = iota //世界聊天
	OpResourceAdd        //资源产出
	OpResourceDec        //资源消耗
	OpMax
)

const (
	CommandMaxParamLen = 100 //
)

// 迷宫
const (
	MazeLockTm = 2 // 迷宫锁的时间 2秒：用于异步操作加锁
)

// 公会副本
const (
	GuildDungeonAwardReceiveAllThroughChapter = 0 // 领取已通过章节的所有奖励
	GuildDungeonAwardReceiveOneChapter        = 1 // 章节通关后领取玩家当前章节的所有宝箱
	GuildDungeonAwardReceiveBoxes             = 2 // 领取玩家当前章节的可领宝箱(未通关)
	GuildDungeonBoxCount                      = 4 // 章节宝箱的个数
	GuildDungeonMaxRound                      = 8 // 公会副本战斗的最大回合数
)

// 神器
const (
	ArtifactOneKeyStrengthLimit = 10 // 神器一键强化等级限制
	ArtifactOneKeyForgeLimit    = 10 // 神器一键铸造等级限制
	ArtifactRarePurple          = 40 // 神器紫色品质
	ArtifactRareRed             = 60 // 神器红色品质
	ArtifactSkill2OpenStar      = 2  // 紫色及以上品质神器拥有的技能2的解锁星级
	ArtifactSkill3OpenStar      = 4  // 红色及以上品质神器拥有的技能3的解锁星级
)

// 红点
const (
	RedPointFlushNumLimit = 100 // 红点请求的数量上限
)

// 宝石系统相关
const (
	GemLeftSlot   uint32 = 1 // 宝石左槽位
	GemRightSlot  uint32 = 2 // 宝石右槽位
	GemWearMaxNum uint32 = 2 // 宝石槽位总数

	GemTypeWear   uint32 = 1 // 宝石穿戴
	GemTypeRemove uint32 = 2 // 宝石卸下
	GemNoRebuild  uint32 = 0 // 宝石不可置换属性
	GemSlotNum    int    = 2 // 宝石槽位数量
)

const (
	UserInitFromRealUser   bool = true
	UserInitFromBattleUser bool = false
)

// 成长大师部件数量
const (
	EquipNum  int = 4 // 装备大师开启需要的部件数量
	EmblemNum int = 2 // 纹章大师开启需要的部件数量
)

// 嘉年华
const (
	CarnivalSeven    = 1
	CarnivalFourteen = 2
)

const (
	ItemHeroExp = 10001 // 英雄经验value
)

// RedPointID2RankID 红点id与排行类型id的对应关系
var RedPointID2RankID = map[common.RED_POINT]uint32{
	common.RED_POINT_RANK_ACHIEVE_POWER:          goxml.PowerRankId,
	common.RED_POINT_RANK_ACHIEVE_DUNGEON:        goxml.DungeonRankId,
	common.RED_POINT_RANK_ACHIEVE_TOWER:          goxml.TowerRankId,
	common.RED_POINT_RANK_ACHIEVE_ARENA:          goxml.ArenaRankId,
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_EMPIRE:  goxml.MirageEmpireRankId,
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_FOREST:  goxml.MirageForestRankId,
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_MOON:    goxml.MirageMoonRankId,
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_PROTOSS: goxml.MirageProtossRankId,
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_DEMON:   goxml.MirageDemonRankId,
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_SIX:     goxml.MirageSixRankId,
}

// RedPointID2FuncID 红点id与玩法id的对应关系
var RedPointID2FuncID = map[common.RED_POINT]uint32{
	common.RED_POINT_RANK_ACHIEVE_POWER:          uint32(common.FUNCID_MODULE_RANK_ACHIEVE_POWER),
	common.RED_POINT_RANK_ACHIEVE_DUNGEON:        uint32(common.FUNCID_MODULE_RANK_ACHIEVE_DUNGEON),
	common.RED_POINT_RANK_ACHIEVE_TOWER:          uint32(common.FUNCID_MODULE_RANK_ACHIEVE_TOWER),
	common.RED_POINT_RANK_ACHIEVE_ARENA:          uint32(common.FUNCID_MODULE_RANK_ACHIEVE_ARENA),
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_EMPIRE:  uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_EMPIRE),
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_FOREST:  uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_FOREST),
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_MOON:    uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_MOON),
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_PROTOSS: uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_PROTOSS),
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_DEMON:   uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_DEMON),
	common.RED_POINT_RANK_ACHIEVE_MIRAGE_SIX:     uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_SIX),
}

// AwardRedPointID2MirageCopyID 红点id与个人boss副本类型的对应关系
var AwardRedPointID2MirageCopyID = map[common.RED_POINT]uint32{
	common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_EMPIRE:  goxml.MirageRaceEmpire,
	common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_FOREST:  goxml.MirageRaceForest,
	common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_MOON:    goxml.MirageRaceMoon,
	common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_DEMON:   goxml.MirageRaceDemon,
	common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_PROTOSS: goxml.MirageRaceProtoss,
	common.RED_POINT_MIRAGE_UNCLAIMED_AWARD_SIX:     goxml.MirageRaceSix,
}

// FightRedPointID2MirageCopyID 红点id与个人boss副本类型的对应关系
var FightRedPointID2MirageCopyID = map[common.RED_POINT]uint32{
	common.RED_POINT_MIRAGE_CAN_FIGHT_EMPIRE:  goxml.MirageRaceEmpire,
	common.RED_POINT_MIRAGE_CAN_FIGHT_FOREST:  goxml.MirageRaceForest,
	common.RED_POINT_MIRAGE_CAN_FIGHT_MOON:    goxml.MirageRaceMoon,
	common.RED_POINT_MIRAGE_CAN_FIGHT_DEMON:   goxml.MirageRaceDemon,
	common.RED_POINT_MIRAGE_CAN_FIGHT_PROTOSS: goxml.MirageRaceProtoss,
	common.RED_POINT_MIRAGE_CAN_FIGHT_SIX:     goxml.MirageRaceSix,
}

// handbook
const (
	HeroHandbookAwardStar  = 5 // 英雄图鉴有奖励的英雄初始星级
	HeroHandbookActiveAuto = 0 // 英雄图鉴自动激活类型
)

const (
	WmOsAndroid     = "2" // 安卓
	WmOsOfficialIos = "3" // ios官方
)

// 机器人养成程度类型 - 依靠策划配置的数据
const (
	RobotTypeOne      uint32 = 1   // 第一天账号养成程度
	RobotTypeThird    uint32 = 3   // 第三天账号养成程度
	RobotTypeSeventh  uint32 = 7   // 第七天账号养成程度
	RobotTypeFourteen uint32 = 14  // 第十四天账号养成程度
	RobotTypeThiry    uint32 = 30  // 第三十天账号养成程度
	RobotTypeNinety   uint32 = 90  // 第九十天账号养成程度
	RobotType360      uint32 = 360 // 第360账号养成程度
)

// 账号流派 - 依靠策划配置的数据
const (
	AccountTypeBlitz     = 1 // 暴击
	AccountTypeRebound   = 2 // 反弹
	AccountTypeCorrosion = 3 // 腐蚀
	AccountTypeResurrect = 4 // 复活
	AccountTypeAssault   = 5 // 猛攻
	AccountTypeNoControl = 6 // 免控
	AccountTypeFullLevel = 7 // 满级号
)

// 共享养成相关常量
const (
	RequireHeroCountForContract int    = 5  //完成缔约的英雄数量要求
	FirstCrystalSlotID          uint32 = 1  //首个槽位id
	DefaultUnlockSlotCount      uint32 = 1  //默认解锁槽位数量 - 谨慎修改，若修改，需修复数据
	BattleListRequireHeroRare   uint32 = 40 // 进入战斗列表的英雄品质限制
)

// 英雄相关操作类型
const (
	HeroOpTypeLevelUp    uint32 = 1 //升级
	HeroOpTypeStageUp    uint32 = 2 //突破
	HeroOpTypeStarUp     uint32 = 3 //升星
	HeroOpTypeDelete     uint32 = 4 //分解
	HeroOpTypeBack       uint32 = 5 //回退
	HeroOpTypeRevive     uint32 = 6 //重生
	HeroOpTypeAddNew     uint32 = 7 //添加新英雄
	HeroOpTypeGemLevelUp uint32 = 8 //英雄的宝石升级
)

// 英雄升星耗材数量初始cap
const HeroStarUpCostInitCap = 5

const TrialTaskType = 1035

const (
	ProcessOrderRetSuccess = 1 //订单正常处理完成
	ProcessOrderRetFailure = 2 //订单处理失败
)

var (
	StarLimitMaxStarLv          uint32 = 15 //限制星级数据中，最大的
	StarLimitDiffNum            uint32 = 2  //需求星级与目标星级的差值
	StarLimitStarLvRequireCount uint32 = 5  //限制星级数据中，需要的数量
)

// 获取最小需求星级
// @return uint32
func GetMinRequireStar() uint32 {
	return goxml.StarLimitMinStarLv - StarLimitDiffNum
}

// 根据目标星级，获取需求星级
// @param star uint32 目标星级
// @return uint32
// 调用方验证，star需在StarLimitMinStarLv与StarLimitMaxStarLv区间内
func GetStarLimitRequireStar(star uint32) uint32 {
	return star - StarLimitDiffNum
}

// 根据限制星级，获取满足其等级需求的最小星级
// @return uint32
func GetStarLimitMinRequireStarByStar(star uint32) uint32 {
	if star < goxml.StarLimitMinStarLv {
		return GetStarLimitRequireStar(goxml.StarLimitMinStarLv)
	}
	return GetStarLimitRequireStar(star)
}

const TowerstarFullStar = 0b0111

var ForbiddenFunction = map[uint32]struct{}{
	uint32(common.FUNCID_MODULE_TALES_ELITE): {},
}

const (
	RateContentLength = 140 // 评分 - 评论内容长度
	RateMaxScore      = 5   // 评分 - 最大分数
)

// 战斗类型 - 日志用
const (
	BattleOpTypeFight     uint32 = 1 //战斗
	BattleOpTypeSweep     uint32 = 2 //扫荡
	BattleOpTypeCrush     uint32 = 3 //碾压/跳关
	BattleOpTypeAutoSweep uint32 = 4 //一键扫荡
)

// 心愿单格位
const (
	WishSlotLeft  uint32 = 1
	WishSlotRight uint32 = 2
)

const Top5HeroNum int = 5

// accountTag 账号标识
const (
	AccountTagNone    = iota // 普通账户
	AccountTagWelfare        // 福利账号
)

const MaxClientDataLen = 1024

const (
	ResetLinkSummonGuarantee = 0
	LinkSummonGuaranteeLen   = 3
)

// 神器首发
const (
	ArtifactDebutSummonCap           = 2  //抽卡 - 数据所用cap
	ArtifactDebutRecvAwardCountLimit = 50 //单次领奖数量上限
	ArtifactDebutActivityGetLimit    = 5  //获取活跃活动数量上限

	ArtifactDebutGuaranteeTypeNone    uint32 = 0 //保底类型 - 无
	ArtifactDebutGuaranteeTypeRegular uint32 = 1 //保底类型 - 常规
	ArtifactDebutGuaranteeTypeRound   uint32 = 2 //保底类型 - 轮次
)

// 新服轮次活动
const (
	RoundActivityRecvAwardCountLimit        = 10 //任务单次领奖数量上限
	RoundActivityRecvAwardFromUser   uint64 = 0  //任务领奖类型 - 玩家领奖
	RoundActivityRecvAwardFromSys    uint64 = 1  //任务领奖类型 - 系统补发
)

const (
	RecvAwardFromUser uint64 = 0
	RecvAwardFromSys  uint64 = 1
)

type SummonWay uint32 // 抽卡方式

const (
	SummonNormal                    SummonWay = 1 // 普通抽
	SummonFixedGuarantee            SummonWay = 2 // 固定保底
	SummonChangeGuarantee           SummonWay = 3 // 可变保底
	SummonWishGuarantee             SummonWay = 4 // 心愿单保底
	SummonArtifactFragmentGuarantee SummonWay = 5 // 神器碎片保底
	SummonRoundActivityGuarantee    SummonWay = 6 // 活动保底
	SummonColorfulGuarantee         SummonWay = 7 // 品质保底
)

const WebLargeRechargeGmMailType uint32 = 4

// 皮肤回收最大资源数量限制
func fixSkinRecoveryLimit(typ, count uint32) uint32 {
	limitCount := goxml.GetData().ConfigInfoM.GetSkinDecomposeLimit(typ)
	if count > limitCount {
		return limitCount
	}
	return count
}

var formationWithCognition = map[common.FORMATION_ID]l2m.CognitionType{
	common.FORMATION_ID_FI_DUNGEON:                l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_TRIAL_GOLD:             l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_TRIAL_EXP:              l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_TRIAL_EQUIP:            l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_TRIAL_HERO:             l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_TOWER_SEASON:           l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_TOWER:                  l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_MIRAGE_RACE_EMPIRE:     l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_MIRAGE_RACE_FOREST:     l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_MIRAGE_RACE_MOON:       l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_MIRAGE_RACE_PROTOSS:    l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_MIRAGE_RACE_DEMON:      l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_MIRAGE_RACE_SIX:        l2m.CognitionType_CT_Normal,
	common.FORMATION_ID_FI_WORLD_BOSS:             l2m.CognitionType_CT_RoundPass,
	common.FORMATION_ID_FI_SEASON_DUNGEON:         l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_A: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_B: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_C: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_D: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_E: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_F: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_G: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_H: l2m.CognitionType_CT_SeasonPass,
	common.FORMATION_ID_FI_GUILD_DUNGEON_1:        l2m.CognitionType_CT_SeasonRoundPass,
	common.FORMATION_ID_FI_GUILD_DUNGEON_2:        l2m.CognitionType_CT_SeasonRoundPass,
	common.FORMATION_ID_FI_GUILD_DUNGEON_3:        l2m.CognitionType_CT_SeasonRoundPass,
	common.FORMATION_ID_FI_GUILD_DUNGEON_4:        l2m.CognitionType_CT_SeasonRoundPass,
	common.FORMATION_ID_FI_GUILD_DUNGEON_5:        l2m.CognitionType_CT_SeasonRoundPass,
	common.FORMATION_ID_FI_GUILD_DUNGEON_6:        l2m.CognitionType_CT_SeasonRoundPass,
	common.FORMATION_ID_FI_GST_BOSS_1:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_2:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_3:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_4:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_5:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_6:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_7:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_BOSS_8:             l2m.CognitionType_CT_GstBossRank,
	common.FORMATION_ID_FI_GST_DRAGON_1:           l2m.CognitionType_CT_GstDragonRank,
	common.FORMATION_ID_FI_GST_DRAGON_2:           l2m.CognitionType_CT_GstDragonRank,
	common.FORMATION_ID_FI_GST_DRAGON_3:           l2m.CognitionType_CT_GstDragonRank,
	common.FORMATION_ID_FI_GST_DRAGON_4:           l2m.CognitionType_CT_GstDragonRank,
	common.FORMATION_ID_FI_GST_DRAGON_5:           l2m.CognitionType_CT_GstDragonRank,
	common.FORMATION_ID_FI_BOSS_RUSH_1:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_2:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_3:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_4:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_5:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_6:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_7:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_BOSS_RUSH_8:            l2m.CognitionType_CT_BossRush,
	common.FORMATION_ID_FI_GST_ORE_1:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_2:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_3:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_4:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_5:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_6:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_7:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_GST_ORE_8:              l2m.CognitionType_CT_GstOreRank,
	common.FORMATION_ID_FI_SEASON_DOOR_1:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_2:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_3:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_4:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_5:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_6:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_7:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_DOOR_8:          l2m.CognitionType_CT_SeasonDoor,
	common.FORMATION_ID_FI_SEASON_MAP_1:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_2:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_3:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_4:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_5:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_6:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_7:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_8:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_9:           l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_10:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_11:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_12:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_13:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_14:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_15:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_16:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_SEASON_MAP_17:          l2m.CognitionType_CT_SeasonMap,
	common.FORMATION_ID_FI_TOWER_POKEMON:          l2m.CognitionType_CT_SeasonPass,
}

// 代金券参数
const (
	CouponAwardTypeNormal                        = 1
	CouponAwardTypeExtra                         = 2
	WebRechargeCouponActivityWebGiftId           = 10007
	WebRechargeCouponInternalId                  = 20000
	WebRechargeCouponCustomRechargeNumMin        = 10000  //官网代金券自定义充值下限
	WebRechargeCouponCustomRechargeNumMax        = 100000 //官网代金券自定义充值上限
	WebRechargeCouponSdkPid               string = "coupon_7_999"
	rechargeMailTxtLen                           = 2
	WebRechargeOpIdTW                            = 18
)

// 剧情回忆录参数
const (
	StoryTypeActivity = 2
	StoryTypeSeason   = 3
)

const WrongUserLimitTime int64 = 10 * 60

// 战斗类型
const (
	BattlePVE uint32 = iota // 打机器人
	BattlePVP               // 打真人
	BattleEVE               // 用机器人打机器人
)

const (
	FlowerOccupyAssistLimitCount = 30 // 密林占矿小助手检索数量上限
)

// 战报压缩标准
const BattleReportCompressBytes = 50 * 1024 // 50KB

const (
	GSTBossMaxRound   = 8  // 公会boss战斗的最大回合数
	GSTDragonMaxRound = 15 // 公会战龙战战斗的最大回合数
)

const (
	SensitiveGuildName        = "12"
	SensitiveUserName         = "13"
	SensitiveGuildDeclaration = "14"
	SensitiveGuildMail        = "15"
	SensitiveGstMessage       = "16"
	SensitiveTalentTree       = "17"
)

// 自定义战报版本号
const ReportVersion uint32 = 1

// 战斗胜利标准
type BattleWinType uint32

const (
	BattleWinTypeAllWin   BattleWinType = iota // 全胜
	BattleWinTypeHalfWin                       // 胜场过半
	BattleWinTypeForceWin                      // 强制获胜
)

const (
	SeasonDoorTypeEquip = 1
	SeasonDoorTypeMat   = 2
)

func MirageComplianceOpenCheck(user *User, srv Servicer, now int64) bool {
	activityRankInfo := goxml.GetData().RankingInfoM.Index(goxml.ActivityMirageRankID)
	if activityRankInfo != nil && user.Level() >= activityRankInfo.Level && goxml.GetData().ConfigInfoM.ActivityMirageOpen(srv.ServerOpenDay(now), user.Level()) {
		return true
	}
	return false
}

func MirageComplianceShowCheck(user *User, srv Servicer, now int64) bool {
	if goxml.GetData().ConfigInfoM.ActivityMirageShow(srv.ServerOpenDay(now), user.Level()) {
		return true
	}
	return false
}

func MirageComplianceEndCheck(user *User, srv Servicer, now int64) bool {
	if goxml.GetData().ConfigInfoM.ActivityMirageEnd(srv.ServerOpenDay(now)) {
		return true
	}
	return false
}

func TowerComplianceOpenCheck(user *User, srv Servicer, now int64) bool {
	activityRankInfo := goxml.GetData().RankingInfoM.Index(goxml.ActivityTowerRankID)
	if activityRankInfo != nil && user.Level() >= activityRankInfo.Level && goxml.GetData().ConfigInfoM.ActivityTowerOpen(srv.ServerOpenDay(now), user.Level()) {
		return true
	}
	return false
}

func TowerComplianceShowCheck(user *User, srv Servicer, now int64) bool {
	if goxml.GetData().ConfigInfoM.ActivityTowerShow(srv.ServerOpenDay(now), user.Level()) {
		return true
	}
	return false
}

func TowerComplianceEndCheck(user *User, srv Servicer, now int64) bool {
	if goxml.GetData().ConfigInfoM.ActivityTowerEnd(srv.ServerOpenDay(now)) {
		return true
	}
	return false
}

var ComplianceTasksEventCheckFunc = map[uint32]func(user *User, srv Servicer, now int64) bool{
	event.AeMirageTotalFloor: MirageComplianceOpenCheck,
	event.AeTowerFloorToX:    TowerComplianceOpenCheck,
}

var ModuleComplianceCheck = map[uint32]*ComplianceCheck{
	uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE): {
		OpenCheck: MirageComplianceOpenCheck,
		ShowCheck: MirageComplianceShowCheck,
		EndCheck:  MirageComplianceEndCheck,
		RankID:    goxml.ActivityMirageRankID,
		Mail:      ActivityMirageCompliance,
	},
	uint32(common.FUNCID_MODULE_ACTIVITY_TOWER): {
		OpenCheck: TowerComplianceOpenCheck,
		ShowCheck: TowerComplianceShowCheck,
		EndCheck:  TowerComplianceEndCheck,
		RankID:    goxml.ActivityTowerRankID,
		Mail:      ActivityTowerCompliance,
	},
}

type ComplianceCheck struct {
	OpenCheck func(user *User, srv Servicer, now int64) bool
	ShowCheck func(user *User, srv Servicer, now int64) bool
	EndCheck  func(user *User, srv Servicer, now int64) bool
	RankID    uint32
	Mail      func(srv servicer, user *User, awards []*cl.Resource)
}

const (
	SeasonMapBuffBuyPrice    = 1
	SeasonMapBuffBuyStamina  = 2
	SeasonMapBuffFightDamage = 3
)

const (
	SeasonMapTradeTimeLimit int64 = 60
)

const (
	PokemonBallTypeSeasonPVP uint32 = 4
	PokemonBallPos1          uint32 = 1
	PokemonBallPos2          uint32 = 2
	PokemonBallPos3          uint32 = 3
)
