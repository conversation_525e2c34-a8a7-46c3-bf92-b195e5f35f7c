package character

import (
	"app/logic/mail"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

// 原则：根据数据库的数据，构建自身数据结构（可重入，不产生副作用）
func (u *User) LoadLoginUser(recv *r2l.R2L_Login, srv servicer) {
	u.loadDBUser(recv.User, srv)

	// 加载相关单独存储
	u.FormationManager().Load(recv.Formations)
	u.EquipManager().Load(recv.Equips)       //装备需要先于英雄加载
	u.ArtifactManager().load(recv.Artifacts) // 神器,先于英雄加载
	u.GemManager().Load(recv.Gems)           // 宝石，先于英雄加载
	u.EmblemManager().Load(recv.Emblems)
	u.SeasonJewelryManager().Load(recv.SeasonJewelry) // 赛季装备，先于英雄加载
	u.PokemonManager().load(recv.Pokemons)            // 宠物，先于英雄加载
	u.HeroManager().Load(recv.Heroes, false)
	u.MirageManager().Load(recv.Mirages)
	u.MailBox().Load(recv.Mails)
	u.MazeM().Load(recv.MazePlayer)
	u.Orders().load(recv.Orders, recv.WaitRefundOrders, recv.WaitProcessOrders, recv.RefundedOrders)
	u.loadCrystal(recv.User.Crystal, true) //需要在英雄后加载
	u.OperateActivityM().load(recv.Activities)
	u.Questionnaires().load(recv.Questionnaires)
	u.HeroStarUpCostsManager().Load(recv.HeroesStarUpCosts)
	u.SkinManager().Load(recv.Skins, true)
	u.RiteManager().Load(recv.Rites)
	u.SeasonLink().LoadActivation(recv.SeasonlinkActivation)
	u.SeasonLink().LoadMonument(recv.SeasonlinkMonuments)
	u.RemainM().load(recv.Remains)
	u.ActivitySumM().load(recv.ActivitySum)
	u.Duel().Load(recv.Duel)
	u.TitleManager().Load(recv.Titles)
	u.SetSeasonAddPower(u.CalcSeasonAddPower())
}

func (u *User) loadCreateUser(dbUser *db.User, srv servicer) {
	u.loadDBUser(dbUser, srv)
	u.loadCrystal(dbUser.Crystal, true)
}

func (u *User) loadDBUser(dbUser *db.User, srv servicer) {
	u.dbUser = dbUser
	u.loadDBBase(dbUser.Base)

	u.loadResourceData()

	u.mails = mail.NewBox(dbUser.Id)
	u.loadDBModule(dbUser.Module)
	if dbUser.Module1 == nil {
		dbUser.Module1 = &db.ModuleAttr1{}
	}
	u.loadDBModule1(dbUser.Module1)
	if dbUser.Module2 == nil {
		dbUser.Module2 = &db.ModuleAttr2{}
	}
	u.loadDBModule2(dbUser.Module2)
	if dbUser.Module3 == nil {
		dbUser.Module3 = &db.ModuleAttr3{}
	}
	u.loadDBModule3(dbUser.Module3, srv)

	if dbUser.Module4 == nil {
		dbUser.Module4 = &db.ModuleAttr4{}
	}
	u.loadDBModule4(dbUser.Module4)

	if dbUser.Module5 == nil {
		dbUser.Module5 = &db.ModuleAttr5{}
	}
	u.loadDBModule5(dbUser.Module5)

	if dbUser.Disorderland == nil {
		dbUser.Disorderland = &db.ModuleAttrDisorderland{}
	}
	u.loadDBModuleDisorderLand(dbUser.Disorderland)

	if dbUser.ModuleGlobalAttr == nil {
		dbUser.ModuleGlobalAttr = &db.ModuleGlobalAttr{}
	}
	u.loadDBModuleGlobalAttr(dbUser.ModuleGlobalAttr)
	u.loadClientInfo(dbUser.ClientInfo)
	u.AchievementsShowcase().load(dbUser.AchievementsShowcase)

	if dbUser.Module6 == nil {
		dbUser.Module6 = &db.ModuleAttr6{}
	}
	u.loadDBModule6(dbUser.Module6)

	if dbUser.Module7 == nil {
		dbUser.Module7 = &db.ModuleAttr7{}
	}
	u.loadDBModule7(dbUser.Module7)
	if dbUser.Module8 == nil {
		dbUser.Module8 = &db.ModuleAttr8{}
	}
	u.loadDBModule8(dbUser.Module8)
}

func (u *User) loadResourceData() {
	if u.dbUser.Resource == nil {
		u.dbUser.Resource = &db.ResourceAttr{
			Data: make([]uint64, db.RESOURCE_MAX),
		}
	} else if len(u.dbUser.Resource.Data) < int(db.RESOURCE_MAX) {
		//版本增加新资源
		ress := make([]uint64, db.RESOURCE_MAX)
		copy(ress, u.dbUser.Resource.Data)
		u.dbUser.Resource.Data = ress
	}
}

// 生成BattleData的时候特殊处理的
func (u *User) loadDbUserForBattleData(dbUser *db.User) {
	u.dbUser = dbUser
	u.loadDBBase(dbUser.Base)
	if dbUser.ModuleGlobalAttr != nil {
		u.loadDBModuleGlobalAttr(dbUser.ModuleGlobalAttr)
	}
	u.AchievementsShowcase().load(dbUser.AchievementsShowcase)
}

func (u *User) loadDBBase(base *db.BaseAttr) {
}

func (u *User) loadDBModule(module *db.ModuleAttr) {
	// u.Shop().load(module.ShopPermanentCnt)
	u.Achieve().load(module.Achieve)
	if module.Daily == nil {
		//这里不初始化，下面的dailytask会报错
		module.Daily = &db.DailyInfo{}
	}
	if module.Weekly == nil {
		module.Weekly = &db.WeeklyInfo{}
	}
	if module.Summon == nil {
		module.Summon = make(map[uint32]*db.SummonInfo)
	}
	if module.Shops == nil {
		module.Shops = make(map[uint32]*cl.Shop)
	}
	if module.Towers == nil {
		module.Towers = make(map[uint32]*cl.Tower)
	}
	if module.Avatars == nil {
		module.Avatars = make(map[uint32]*cl.Avatar)
	}
	if module.Trials == nil {
		module.Trials = make(map[uint32]*cl.TrialInfo)
	}
	if module.NumInfo == nil {
		module.NumInfo = make(map[uint32]*cl.NumInfo)
	}
	u.DailyTask().load(module.Daily.DailyTask)
	u.WeeklyTask().load(module.Weekly.WeeklyTask)
	u.Dungeon().load(module.Dungeon)
	u.Summon().load(module.Summon)
	u.ShopM().load(module.Shops)
	u.Towers().load(module.Towers)
	u.ArenaOpponent().load(module.ArenaOpponents)
	u.Avatar().load(module.Avatars)
	u.Trials().load(module.Trials)
	u.Dispatch().load(module.Dispatch)
}

func (u *User) loadDBModule1(module *db.ModuleAttr1) {
	u.GoldBuy().load(module.GoldBuy)

	u.RankAchieveAward().load(module.RankAchieveAwards)
	//	u.UserGuild().load(module.GuildInfo)
	u.Tales().load(module.Tales)
	u.Drop().load(module.DropInfo)
	u.Guidance().load(module.Guidance)
	u.Carnival().load(module.Carnival)
	u.SevenDayLogin().load(module.SevenDayLogin)
	u.Medal().load(module.Medal)
}

func (u *User) loadDBModule2(module *db.ModuleAttr2) {
	u.Forecast().load(module.Forecast)
	u.Recharge().load(module.Recharge)
	u.VipManager().load(module.VipInfo)
	u.ActivityRecharge().load(module.ActivityRecharge)
	u.Towerstar().load(module.Towerstar)
	u.MonthlyCard().load(module.MonthlyCard)
	u.Pass().load(module.Passes)
	u.PushGift().load(module.PushGiftInfo)
	u.Rate().load(module.Rate)
}

func (u *User) loadDBModule3(module *db.ModuleAttr3, srv servicer) {
	u.Link().load(module.LinkInfo)
	u.Flower().load(module.FlowerOccupyNum)
	u.GoddessContract().loadGoddess(module.GoddessContractInfo)
	u.MonthTasks().load(module.MonthTasks)
	u.DailyWishM().Load(module.DailyWish)
	u.LinkSummon().load(module.LinkHeroSummon)
	u.ArtifactDebutM().load(module.ArtifactDebut)
	u.DivineDemon().load(module.DivineDemon)
	u.RoundActivityM().load(module.RoundActivity, srv)
}

func (u *User) loadDBModule4(module *db.ModuleAttr4) {
	if module.MonthlyInfo == nil {
		module.MonthlyInfo = &db.MonthlyInfo{}
	}
	u.TowerSeason().load(module.TowerSeason)
	u.GodPresentM().load(module.GodPresentsNew)
	u.ChatGroupTag().load(module.ChatGroupTag)
	u.DropActivityM().load(module.DropActivity)
	u.DailyAttendance().load(module.DailyAttendance)
	u.DailySpecial().load(module.DailySpecial)
	u.UserGuildChestItem().load(module.UserGuildChestItem)
	u.MonthlyTask().load(module.MonthlyInfo.MonthlyTask)
	u.WorldBoss().load(module.WorldBoss)
}

func (u *User) loadDBModule5(module *db.ModuleAttr5) {
	u.ActivityStory().load(module.ActivityStory)
	u.DivineDemonWishRecord().load(module.DivineDemonWishRecord)
	u.AssistanceActivity().load(module.AssistanceActivity)
	u.SeasonLevel().load(module.SeasonLevel)
	u.SeasonDungeon().Load(module.SeasonDungeon)
	u.ActivityReturn().load(module.ActivityReturn)
	u.PreSeason().load(module.PreSeason)
	u.SeasonReturn().load(module.SeasonReturn)
	u.StoryReview().load(module.StoryReview)
	u.NewYearActivity().load(module.NewYearActivity)
}

func (u *User) loadDBModuleDisorderLand(module *db.ModuleAttrDisorderland) {
	u.DisorderLand().load(module.DisorderlandData)
}

func (u *User) loadDBModuleGlobalAttr(module *db.ModuleGlobalAttr) {
	u.Memory().load(module.Memory)
	u.GuildTalent().load(module.GuildTalent)
	u.HandbookManager().load(module.HandbooksData)
	u.GoddessContract().load(module.GoddessContractExp, module.GoddessSuits)
	u.RemainM().loadRemainBook(module.RemainBook)
	u.TalentTree().loadLv(module.TalentTreeCul)
	u.ShareGrowth().load(module.ShareGrowth)
}

func (u *User) loadClientInfo(data *db.ClientInfo) {
	u.ClientInfo().load(data)
}

func (u *User) loadCrystal(data *db.Crystal, isSelf bool) {
	// u.Crystal().load(data, isSelf)
}

func (u *User) loadFinished() bool {
	//l4g.Debugf("[User] %s,%d load data: attrs:%+v", u.Name(), u.ID(), u.dbUser.Base.BattleFormat)

	//初始化包裹
	if u.dbUser.Bag == nil {
		u.dbUser.Bag = &db.Bags{
			Items: make(map[uint32]uint32),
		}
	}
	l4g.Debugf("user %d, show bag, items: %+v", u.ID(), u.dbUser.Bag.Items)
	if u.dbUser.Bag.Items == nil {
		u.dbUser.Bag.Items = make(map[uint32]uint32)
	}

	if u.dbUser.Bag.Fragments == nil {
		u.dbUser.Bag.Fragments = make(map[uint32]uint32)
	}

	if u.dbUser.Bag.Tokens == nil {
		u.dbUser.Bag.Tokens = make(map[uint32]uint64)
	}

	if u.dbUser.Bag.ArtifactFragments == nil {
		u.dbUser.Bag.ArtifactFragments = make(map[uint32]uint32)
	}

	if u.dbUser.Bag.EmblemFragments == nil {
		u.dbUser.Bag.EmblemFragments = make(map[uint32]uint32)
	}

	if u.dbUser.Bag.RemainFragments == nil {
		u.dbUser.Bag.RemainFragments = make(map[uint32]uint32)
	}

	if u.dbUser.Bag.ExpiredItem == nil {
		u.dbUser.Bag.ExpiredItem = make(map[uint32]*cl.ExpiredItems)
	}

	if u.dbUser.Bag.SeasonMapBag == nil {
		u.dbUser.Bag.SeasonMapBag = &db.SeasonMapBag{}
	}

	if u.dbUser.Bag.SeasonMapBag.Goods == nil {
		u.dbUser.Bag.SeasonMapBag.Goods = make(map[uint32]uint32)
	}

	if u.dbUser.Bag.PokemonFragments == nil {
		u.dbUser.Bag.PokemonFragments = make(map[uint32]uint32)
	}

	if u.powerTm() == 0 {
		u.SetPowerTm(u.CreateTime())
	}
	return true
}

func (u *User) loadDBModule6(module *db.ModuleAttr6) {
	u.Pyramid().load(module.Pyramid)
	u.RemainM().loadItems(module.RemainItems)
	u.SeasonArenaTask().Load(module.SeasonArenaTask)
	//u.GST().load(module.BossUser)
	u.LineTask().load(module.LineTask)
	u.ActivityTurnTable().load(module.ActivityTurnTable)
	u.TalentTree().loadBase(module.TalentTree)
}

func (u *User) loadDBModule7(module *db.ModuleAttr7) {
	u.ActivityLifelongGifts().load(module.ActivityLifelongGifts)
	u.ActivityCompliance().load(module.ActivityCompliance)
	u.BossRush().load(module.BossRush, module.BossRushInit)
	u.SelectSummon().load(module.SelectSummon)
	u.SeasonComplianceM().load(module.SeasonCompliances)
	u.SeasonDoor().Load(module.SeasonDoor)
	u.SeasonJewelryManager().loadConfig(module.SeasonJewelryConfig)
	u.ActivityWeb().load(module.ActivityWebDatas)
	u.SeasonShop().load(module.SeasonShops)
}

func (u *User) loadDBModule8(module *db.ModuleAttr8) {
	u.ComplianceTasks().load(module.ComplianceTasks)
	u.ActivityCoupon().load(module.ActivityCoupon)
	u.DailyAttendanceHero().load(module.DailyAttendanceHero)
	u.SeasonMap().load(module.SeasonMap)
	u.PokemonManager().loadGlobal(module.Pokemon)
	u.TowerPokemon().load(module.TowerPokemon)
	u.PokemonSummon().load(module.PokemonSummon)
}
