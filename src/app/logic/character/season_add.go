package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

func (u *User) GetSeasonAddData() []*cl.SeasonAddInfo {
	seasonID := u.GetSeasonID()
	if seasonID == 0 {
		return nil
	}
	oneSeasonAddInfo := goxml.GetData().SeasonAddInfoM.GetOneSeasonAddInfo(seasonID)
	if oneSeasonAddInfo == nil {
		return nil
	}

	seasonAddInfos := make([]*cl.SeasonAddInfo, 0, len(oneSeasonAddInfo.SysIdData))

	for handbookType, sysIds := range oneSeasonAddInfo.SysIdData {
		for _, sysId := range sysIds {
			addInfo := u.generateSeasonAddInfo(handbookType, sysId)
			if addInfo == nil {
				continue
			}
			seasonAddInfos = append(seasonAddInfos, addInfo)
		}
	}
	return seasonAddInfos
}

func (u *User) generateSeasonAddInfo(handbookType, sysId uint32) *cl.SeasonAddInfo {
	star, emblemExclusiveLv := u.seasonAddGetMaxParams(handbookType, sysId)
	switch handbookType {
	case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar, goxml.SeasonAddHandbookPokemonStar:
		if star == 0 {
			return nil
		}
		return &cl.SeasonAddInfo{Type: handbookType, SysId: sysId, Star: star}
	case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
		if emblemExclusiveLv == 0 {
			return nil
		}
		return &cl.SeasonAddInfo{Type: handbookType, SysId: sysId, EmblemExclusiveLv: emblemExclusiveLv}
	default:
		return nil
	}
}

// CalcSeasonAdd
// @Description: 计算赛季加成
func (u *User) CalcSeasonAdd(battleParams *battle.ManagerParams, fromAttack bool) {
	seasonID := u.GetSeasonID()
	if seasonID == 0 {
		return
	}

	var formationID uint32
	if fromAttack {
		formationID = battleParams.GetAttackFID()
	} else {
		formationID = battleParams.GetDefenseFID()
	}

	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d CalcSeasonAdd: no formationInfo, formationID:%d", u.ID(), formationID)
		return
	}

	now := time.Now().Unix()
	if !goxml.IsSeasonFunctionOpen(goxml.GetData(), now, formationInfo.FunctionId) {
		return
	}

	groupIds := goxml.GetData().SeasonAddGroupInfoM.GetGroupIdsByFuncId(formationInfo.FunctionId)
	if len(groupIds) == 0 {
		return
	}
	oneSeasonAddInfo := goxml.GetData().SeasonAddInfoM.GetOneSeasonAddInfo(seasonID)
	if oneSeasonAddInfo == nil {
		return
	}

	raisePSMap := make(map[uint32][]uint64)
	for addType, infos := range oneSeasonAddInfo.OneTypeData {
		if addType != goxml.SeasonAddTypeFirst && addType != goxml.SeasonAddTypeSecond {
			continue
		}
		for handbookType, infosByHandbookType := range infos.Data {
			for sysId, addInfos := range infosByHandbookType {
				maxStar, emblemExclusiveLv := u.seasonAddGetMaxParams(handbookType, sysId)
				if maxStar == 0 && emblemExclusiveLv == 0 {
					continue
				}
				for _, info := range addInfos {
					switch handbookType {
					case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar, goxml.SeasonAddHandbookPokemonStar:
						if info.Star > maxStar {
							continue
						}
					case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
						if info.EmblemLevel > emblemExclusiveLv {
							continue
						}
					default:
						break
					}
					if !util.InUint32s(groupIds, info.GroupId) {
						continue
					}
					rpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(info.Value)
					if rpsInfo == nil {
						continue
					}
					raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], rpsInfo.ID)
				}
			}
		}
	}

	l4g.Debugf("user:%d CalcSeasonAdd: seasonAdd raisePSMap: %+v", u.ID(), raisePSMap)

	if fromAttack {
		battleParams.AltRaisePS.AltAttack(raisePSMap)
	} else {
		battleParams.AltRaisePS.AltDefense(raisePSMap)
	}
}

func (u *User) GetSeasonAddPower() int64 {
	return u.seasonAddPower
}

func (u *User) SetSeasonAddPower(power int64) {
	u.seasonAddPower = power
}

func (u *User) CalcSeasonAddPower() int64 {
	seasonID := u.GetSeasonID()
	if seasonID == 0 {
		return 0
	}
	oneSeasonAddInfo := goxml.GetData().SeasonAddInfoM.GetOneSeasonAddInfo(seasonID)
	if oneSeasonAddInfo == nil {
		return 0
	}
	power := int64(0)
	for _, infos := range oneSeasonAddInfo.OneTypeData {
		for handbookType, infosByHandbookType := range infos.Data {
			for sysId, addInfos := range infosByHandbookType {
				maxStar, emblemExclusiveLv := u.seasonAddGetMaxParams(handbookType, sysId)
				if maxStar == 0 && emblemExclusiveLv == 0 {
					continue
				}
				for _, info := range addInfos {
					needAddPower := false
					switch handbookType {
					case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar:
						if info.Star <= maxStar {
							needAddPower = true
						}
					case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
						if info.EmblemLevel <= emblemExclusiveLv {
							needAddPower = true
						}
					default:
						break
					}
					if needAddPower {
						power += int64(info.Power)
					}
				}
			}
		}
	}
	return power
}

// HandleHeroSeasonSkill
// @Description: 处理英雄的赛季技能
func (u *User) HandleHeroSeasonSkill(formationID uint32, teamIndex int, altAttr *battle.AltAttr, fromAttack bool) {
	if u.GetSeasonID() == 0 {
		// 因为涉及离线玩家以及其他服的玩家调用该方法，所以这里用赛季ID是否为0来判断玩家是否解锁赛季
		return
	}

	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d HandleHeroSeasonSkill: no formationInfo, formationID:%d", u.ID(), formationID)
		return
	}

	now := time.Now().Unix()
	if !goxml.IsSeasonFunctionOpen(goxml.GetData(), now, formationInfo.FunctionId) {
		return
	}

	currentSeasonId := goxml.GetCurrentSeasonID(goxml.GetData(), now)

	formationTeam := u.GetFormationTeam(formationID, teamIndex)
	if formationTeam == nil {
		l4g.Errorf("user %d HandleHeroSeasonSkill: get formation team failed, formationID:%d teamIndex:%d", u.ID(), formationID, teamIndex)
		return
	}

	attrMap := make(map[uint32]map[int]int64)

	for _, hero := range formationTeam.Info {
		heroData := u.HeroManager().Get(hero.Hid)
		if heroData == nil {
			l4g.Errorf("user %d HandleHeroSeasonSkill: user hero not found, heroId %d", u.ID(), hero.Hid)
			continue
		}

		info := goxml.GetData().HeroInfoM.Index(heroData.GetHeroSysID())
		if info == nil {
			l4g.Errorf("user %d HandleHeroSeasonSkill: user hero not found, heroId %d", u.ID(), hero.Hid)
			continue
		}
		if currentSeasonId != info.SeasonIdForSkill {
			continue
		}

		if info.SeasonSkill1 == 0 {
			continue
		}

		levelInfo := goxml.GetData().SkillLevelInfoM.Index(info.SkillLevelID, heroData.GetStar())
		if levelInfo == nil {
			l4g.Errorf("user %d HandleHeroSeasonSkill:: SkillLevelInfo not exist, sysID:%d, SkillLevelID:%d, star:%d",
				u.ID(), heroData.GetHeroSysID(), info.SkillLevelID, heroData.GetStar())
			return
		}

		skill := goxml.GetData().SkillInfoM.GroupLevel(info.SeasonSkill1, levelInfo.SeasonSkill1Level)
		if skill == nil {
			continue
		}
		if attrMap[hero.GetPos()] == nil {
			attrMap[hero.GetPos()] = make(map[int]int64)
		}
		for _, attr := range skill.Attrs {
			l4g.Debugf("user %d HandleHeroSeasonSkill: hid:%d sysID:%d attr:%+v",
				u.ID(), heroData.GetHid(), heroData.GetHeroSysID(), attr)
			attrMap[hero.GetPos()][int(attr.Type)] += attr.Value
		}
	}

	l4g.Debugf("user:%d HandleHeroSeasonSkill: heroSeasonSkill attrMap: %+v", u.ID(), attrMap)

	if fromAttack {
		altAttr.SetAttack(attrMap)
	} else {
		altAttr.SetDefense(attrMap)
	}
}

func (u *User) calcSeasonAddGstFightTimes() uint32 {
	seasonId := u.GetSeasonID()
	if seasonId == 0 {
		return 0
	}

	seasonAddOneTypeInfo := goxml.GetData().SeasonAddInfoM.GetSeasonAddOneTypeInfo(seasonId, goxml.SeasonAddTypeGvgFightTimes)
	if seasonAddOneTypeInfo == nil {
		return 0
	}

	addTimes := uint32(0)

	for handbookType, infosBySysId := range seasonAddOneTypeInfo {
		for sysId, infos := range infosBySysId {
			maxStar, emblemExclusiveLv := u.seasonAddGetMaxParams(handbookType, sysId)
			if maxStar == 0 && emblemExclusiveLv == 0 {
				continue
			}
			for _, info := range infos {
				switch handbookType {
				case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar, goxml.SeasonAddHandbookPokemonStar:
					if maxStar >= info.Star {
						addTimes += info.Value
					}
				case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
					if emblemExclusiveLv >= info.EmblemLevel {
						addTimes += info.Value
					}
				default:
					break
				}
			}
		}
	}
	return addTimes
}

// HandleSeasonAddChange
// @Description:  处理赛季加成变化
func (u *User) HandleSeasonAddChange(srv Servicer, handbookType, sysId, param uint32) {
	if u.GetSeasonID() == 0 {
		return
	}

	seasonAddInfo := goxml.GetData().SeasonAddInfoM.GetOneSeasonAddInfo(u.GetSeasonID())
	if seasonAddInfo == nil {
		l4g.Errorf("user:%d HandleSeasonAddChange: seasonAddInfo is nil. seasonID:%d", u.ID(), u.GetSeasonID())
		return
	}

	var addChange bool
Change:
	for _, oneTypeAddInfo := range seasonAddInfo.OneTypeData {
		if oneTypeAddInfo.Data[handbookType] == nil {
			continue
		}
		for _, info := range oneTypeAddInfo.Data[handbookType][sysId] {
			switch handbookType {
			case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookArtifactStar, goxml.SeasonAddHandbookPokemonStar:
				if param >= info.Star {
					addChange = true
					break Change
				}
			case goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
				if param >= info.EmblemLevel {
					addChange = true
					break Change
				}
			default:
				l4g.Errorf("user:%d HandleSeasonAddChange: handbookType:%d error.", u.ID(), handbookType)
				break Change
			}
		}
	}
	if addChange {
		u.SetSeasonAddPower(u.CalcSeasonAddPower())
		u.UpdateAllPower(srv, PowerUpdateBySeasonRaise)
	}
}

func (u *User) seasonAddGetMaxParams(handbookType, sysId uint32) (uint32, uint32) {
	switch handbookType {
	case goxml.SeasonAddHandbookHeroStar, goxml.SeasonAddHandbookHeroEmblemExclusiveLv:
		heroHandbookM := u.HandbookManager().GetHeroHandbookM()
		heroHandbook := heroHandbookM.GetOne(sysId)
		if heroHandbook == nil {
			l4g.Debugf("user:%d SeasonAddGetStar: have no this heroHandbook. sysId:%d", u.ID(), sysId)
			return 0, 0
		}
		linkBookTaskInfo := goxml.GetData().LinkBookTaskInfoM.GetRecordById(heroHandbook.Link_1StarAttr)
		if linkBookTaskInfo == nil {
			l4g.Debugf("user:%d SeasonAddGetStar: linkBookTaskInfo not exist. id:%d", u.ID(), heroHandbook.Link_1StarAttr)
			return 0, 0
		}
		return linkBookTaskInfo.Value, heroHandbook.EmblemExclusiveLv
	case goxml.SeasonAddHandbookArtifactStar:
		artifactM := u.ArtifactManager()
		artifact := artifactM.GetArtifact(sysId)
		if artifact == nil {
			l4g.Debugf("user:%d SeasonAddGetStar: artifact not exist. id:%d", u.ID(), sysId)
			return 0, 0
		}
		return artifact.GetStar(), 0
	case goxml.SeasonAddHandbookPokemonStar:
		pokemon := u.PokemonManager().GetPokemon(sysId)
		if pokemon == nil {
			l4g.Debugf("user:%d SeasonAddGetStar: pokemon not exist. id:%d", u.ID(), sysId)
			return 0, 0
		}
		return pokemon.GetStar(), 0
	default:
		l4g.Errorf("user:%d SeasonAddGetStar: handbookType error. handbookType:%d", u.ID(), handbookType)
	}
	return 0, 0
}

// 是否属于赛季加成英雄
func (u *User) IsSeasonAddHero(formationID, heroSysID uint32) bool {
	if u.GetSeasonID() == 0 {
		return false
	}

	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d IsSeasonAddHero: no formationInfo, formationID:%d", u.ID(), formationID)
		return false
	}

	now := time.Now().Unix()
	if !goxml.IsSeasonFunctionOpen(goxml.GetData(), now, formationInfo.FunctionId) {
		return false
	}

	currentSeasonId := goxml.GetCurrentSeasonID(goxml.GetData(), now)
	info := goxml.GetData().HeroInfoM.Index(heroSysID)
	if info == nil {
		l4g.Errorf("user %d IsSeasonAddHero: no heroInfo, heroSysID %d", u.ID(), heroSysID)
		return false
	}
	return currentSeasonId == info.SeasonIdForSkill
}

// 是否属于赛季加成英雄
func IsSeasonAddHero(seasonID, formationID, heroSysID uint32) bool {
	if seasonID == 0 {
		return false
	}

	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("IsSeasonAddHero: no formationInfo, formationID:%d", formationID)
		return false
	}

	if !goxml.IsFuncInSeason(goxml.GetData(), seasonID, formationInfo.FunctionId) {
		return false
	}

	info := goxml.GetData().HeroInfoM.Index(heroSysID)
	if info == nil {
		l4g.Errorf("IsSeasonAddHero: no heroInfo, heroSysID %d", heroSysID)
		return false
	}
	return seasonID == info.SeasonIdForSkill
}
