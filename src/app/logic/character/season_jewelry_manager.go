package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"cmp"
	"fmt"
	"math"
	"slices"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SeasonJewelryM struct {
	u     *User
	datas map[uint64]*SeasonJewelry // 赛季装备

	heroJewelryList map[uint64]map[uint32]*SeasonJewelry // 英雄对应的赛季装备列表 cache

	//创建，修改，删除装备使用，定时保存使用
	changes map[uint64]*cl.SeasonJewelry
	deletes map[uint64]struct{}

	config *db.SeasonJewelryConfig // 赛季装备配置
}

func NewSeasonJewelryM(user *User) *SeasonJewelryM {
	data := &SeasonJewelryM{
		u:               user,
		datas:           make(map[uint64]*SeasonJewelry),
		heroJewelryList: make(map[uint64]map[uint32]*SeasonJewelry),
		changes:         make(map[uint64]*cl.SeasonJewelry),
		deletes:         make(map[uint64]struct{}),
	}
	if data.config == nil {
		data.config = &db.SeasonJewelryConfig{
			LockConfig: make(map[uint64]bool),
		}
	}
	if data.config.LockConfig == nil {
		data.config.LockConfig = make(map[uint64]bool)
	}
	return data
}

func (sjm *SeasonJewelryM) Load(dbSeasonJewelrys map[uint64]*cl.SeasonJewelry) {
	for id, data := range dbSeasonJewelrys {
		if data == nil {
			continue
		}

		seasonJewelry := new(SeasonJewelry).Load(data)
		if seasonJewelry == nil {
			continue
		}
		sjm.datas[id] = seasonJewelry

		info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(data.SysId)
		if info == nil {
			continue
		}
		heroId := seasonJewelry.GetHid()
		if heroId == 0 {
			continue
		}
		if _, exist := sjm.heroJewelryList[heroId]; !exist {
			sjm.heroJewelryList[heroId] = make(map[uint32]*SeasonJewelry)
		}
		if info.Pos < goxml.SeasonJewelryPosMin || info.Pos > goxml.SeasonJewelryPosMax {
			continue
		}
		sjm.heroJewelryList[heroId][info.Pos] = seasonJewelry
	}
}

func (sjm *SeasonJewelryM) loadConfig(config *db.SeasonJewelryConfig) {
	sjm.config = config
	if sjm.config == nil {
		sjm.config = &db.SeasonJewelryConfig{
			LockConfig: make(map[uint64]bool),
		}
	}
	if sjm.config.LockConfig == nil {
		sjm.config.LockConfig = make(map[uint64]bool)
	}
}

func (sjm *SeasonJewelryM) FlushAll() []*cl.SeasonJewelry {
	ret := make([]*cl.SeasonJewelry, 0, len(sjm.datas))
	for _, sj := range sjm.datas {
		ret = append(ret, sj.Data.Clone())
	}
	return ret
}

func (sjm *SeasonJewelryM) FlushSeasonJewelryListWithOrder() {
	packNum := goxml.SeasonJewelryMaxSendNumPerPack
	jewelryNum := len(sjm.datas)
	if jewelryNum == 0 {
		// 发送空包
		smsg := &cl.L2C_SeasonJewelryGetData{
			Ret:                 uint32(cret.RET_OK),
			Total:               1,
			Order:               0,
			JewelryList:         []*cl.SeasonJewelry{},
			AutoDecomposeConfig: sjm.GetDecomposeConfig(),
			LockHeroes:          sjm.FlushLockConfig(),
		}
		sjm.u.SendCmdToGateway(cl.ID_MSG_L2C_SeasonJewelryGetData, smsg)
		return
	}

	// 总分包数
	total := int(math.Ceil(float64(jewelryNum) / float64(packNum)))
	slc := make([]*cl.SeasonJewelry, 0, jewelryNum)
	for _, sj := range sjm.datas {
		slc = append(slc, sj.Data.Clone())
	}

	for order := 0; order < total; order++ {
		smsg := &cl.L2C_SeasonJewelryGetData{
			Ret:   uint32(cret.RET_OK),
			Total: uint32(total),
			Order: uint32(order),
		}
		start := order * packNum
		var end int
		if order == total-1 {
			end = jewelryNum // 最后一包取到末尾
			smsg.AutoDecomposeConfig = sjm.GetDecomposeConfig()
			smsg.LockHeroes = sjm.FlushLockConfig()
		} else {
			end = (order + 1) * packNum
		}
		smsg.JewelryList = slc[start:end]
		sjm.u.SendCmdToGateway(cl.ID_MSG_L2C_SeasonJewelryGetData, smsg)
	}
}

func (sjm *SeasonJewelryM) FlushLockConfig() []uint64 {
	if sjm.config == nil || sjm.config.LockConfig == nil {
		return []uint64{}
	}

	lockConfig := make([]uint64, 0, len(sjm.config.LockConfig))
	for hid := range sjm.config.LockConfig {
		lockConfig = append(lockConfig, hid)
	}

	return lockConfig
}

func (sjm *SeasonJewelryM) Get(id uint64) *SeasonJewelry {
	return sjm.datas[id]
}

func (sjm *SeasonJewelryM) GetLockConfig() map[uint64]bool {
	if sjm.config == nil {
		return nil
	}

	return sjm.config.LockConfig
}

func (sjm *SeasonJewelryM) GetDecomposeConfig() *cl.SeasonJewelryDecomposeCfg {
	if sjm.config == nil || sjm.config.DecomposeConfig == nil {
		return nil
	}
	return sjm.config.DecomposeConfig.Clone()
}

func (sjm *SeasonJewelryM) IsDecomposeConfigValid() bool {
	if sjm.config == nil || sjm.config.DecomposeConfig == nil {
		return false
	}
	config := sjm.config.DecomposeConfig
	return len(config.AutoRare) > 0 && config.AutoLevel > 0
}

func (sjm *SeasonJewelryM) Add(srv servicer, id uint64, sysID uint32, seasonJewelryInfo *goxml.SeasonJewelryInfo) uint32 {
	newJewelry := NewSeasonJewelry(id, sysID)
	if newJewelry == nil {
		l4g.Errorf("Add season jewelry -> NewSeasonJewelry failed. uid:%d id:%d sysID:%d", sjm.u.ID(), id, sysID)
		return uint32(cret.RET_ERROR)
	}

	gRet := newJewelry.GenerateSkills(srv.Rand(), seasonJewelryInfo)
	if gRet != uint32(cret.RET_OK) {
		l4g.Errorf("Add season jewelry -> GenerateSkills failed. uid:%d id:%d sysID:%d", sjm.u.ID(), id, sysID)
		return gRet
	}

	sjm.datas[id] = newJewelry
	sjm.changes[id] = newJewelry.Data

	// 存在随机词条，需要更新装备数据给前端
	sjm.u.SendSeasonJewelryToClient([]*cl.SeasonJewelry{newJewelry.Flush()})
	sjm.u.LogSeasonJewelryGet(srv, newJewelry.Convert2LogData(sjm.u))
	return uint32(cret.RET_OK)
}

// AddSeasonJewelry ：添加赛季装备
// @return isChange bool 是否变化
// @return increaseJewelry *cl.Resource 新增的赛季装备
// @return showJewelry []*cl.Resource 用于客户端显示的分解情况（需要增加资源标记）
// @return decomposedResults []*cl.Resource 分解产物
func (sjm *SeasonJewelryM) AddSeasonJewelry(srv servicer, jid uint64, sysID uint32) (*cl.Resource, []*cl.Resource, []*cl.Resource) {
	seasonJewelryInfo := goxml.GetData().SeasonJewelryInfoM.GetRecordById(sysID)
	if seasonJewelryInfo == nil {
		l4g.Errorf("AddSeasonJewelry: config not exist. uid:%d id:%d sysID:%d", sjm.u.ID(), jid, sysID)
		return nil, nil, nil
	}

	showAwards := make([]*cl.Resource, 0, 2)
	newResource := &cl.Resource{
		Id:    jid,
		Type:  uint32(common.RESOURCE_SEASON_JEWELRY),
		Value: seasonJewelryInfo.Id,
		Count: 1,
		Attrs: make([]*cl.Attr, 0, 1),
	}

	// 根据自动分解设置判断是否需要分解
	needDecompose := sjm.canAutoDecompose(seasonJewelryInfo)

	// 1.无须分解
	if !needDecompose {
		aRet := sjm.Add(srv, jid, sysID, seasonJewelryInfo)
		if aRet != uint32(cret.RET_OK) {
			return nil, nil, nil
		}
		showAwards = append(showAwards, newResource)
		return newResource, showAwards, nil
	}

	// 2.分解新获得的装备
	decomposedResource := sjm.getDecomposedResource(jid, seasonJewelryInfo.Id, int64(common.RESOURCE_REWARD_FLAG_RRF_NEW_DECOMPOSED)) //奖励标志设置为新资源被分解
	showAwards = append(showAwards, decomposedResource)
	decomposeResults := sjm.getDecomposeResults(seasonJewelryInfo, jid)
	if sj := NewSeasonJewelry(jid, seasonJewelryInfo.Id); sj != nil {
		sjm.u.LogSeasonJewelryDecompose(srv, sj.Convert2LogData(sjm.u), goxml.SeasonJewelryDecomposeTypePassive, decomposeResults, nil)
	}

	return nil, showAwards, decomposeResults
}

func (sjm *SeasonJewelryM) getDecomposedResource(jid uint64, sysId uint32, rewardFlag int64) *cl.Resource {
	decomposedResource := &cl.Resource{
		Id:    jid,
		Type:  uint32(common.RESOURCE_SEASON_JEWELRY),
		Value: sysId,
		Count: 1,
		Attrs: make([]*cl.Attr, 0, 1),
	}
	decomposedResource.Attrs = append(decomposedResource.Attrs, &cl.Attr{
		Type:  uint32(common.RESOURCE_ATTR_TYPE_RESAT_AWARD_FLAG),
		Value: rewardFlag,
	})
	return decomposedResource
}

func (sjm *SeasonJewelryM) getDecomposeResults(jewelryInfo *goxml.SeasonJewelryInfo, jid uint64) []*cl.Resource {
	if jewelryInfo == nil {
		return nil
	}
	decomposeResults := make([]*cl.Resource, 0, len(jewelryInfo.DecomposeRewards))
	for _, res := range jewelryInfo.DecomposeRewards {
		decomposeResult := res.Clone()
		//奖励标志设置为分解产物
		decomposeResult.Attrs = append(decomposeResult.Attrs, &cl.Attr{
			Type:  uint32(common.RESOURCE_ATTR_TYPE_RESAT_AWARD_FLAG),
			Value: int64(common.RESOURCE_REWARD_FLAG_RRF_DECOMPOSE_RESULT),
		})
		//设置产物来源
		decomposeResult.Attrs = append(decomposeResult.Attrs, &cl.Attr{
			Type:  uint32(common.RESOURCE_ATTR_TYPE_RESAT_FROM_ID),
			Value: int64(jid),
		})
		decomposeResults = append(decomposeResults, decomposeResult)
	}
	return decomposeResults
}

func (sjm *SeasonJewelryM) Delete(id uint64) {
	delete(sjm.datas, id)
	sjm.deletes[id] = struct{}{}
	delete(sjm.changes, id)
}

func (sjm *SeasonJewelryM) SetChange(id uint64) {
	sjm.changes[id] = sjm.datas[id].Data
	delete(sjm.deletes, id)
}

func (sjm *SeasonJewelryM) Save(msg *r2l.OpSeasonJewelry) bool {
	success := false
	changes := len(sjm.changes)
	if changes > 0 {
		msg.Changes = make([]*cl.SeasonJewelry, 0, changes)
		for id, k := range sjm.changes {
			msg.Changes = append(msg.Changes, k.Clone())
			delete(sjm.changes, id)
		}
		success = true
	}
	deletes := len(sjm.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint64, 0, deletes)
		for id := range sjm.deletes {
			msg.Deletes = append(msg.Deletes, id)
			delete(sjm.deletes, id)
		}
		success = true
	}
	return success
}

func (sjm *SeasonJewelryM) SaveConfig() {
	sjm.u.dbUser.Module7.SeasonJewelryConfig = sjm.config
	sjm.u.setSaveTag(saveTagModule7)
}

func (sjm *SeasonJewelryM) GetCount() uint32 {
	return uint32(len(sjm.datas))
}

func (sjm *SeasonJewelryM) CheckSeasonJewelryExist(id uint64) bool {
	if _, ok := sjm.datas[id]; ok {
		return true
	}
	return false
}

func (sjm *SeasonJewelryM) GetHeroIdsWithSeasonJewelry() []uint64 {
	var changeHeroIds []uint64
	for _, j := range sjm.datas {
		if j.Data == nil {
			continue
		}
		if j.Data.HeroId > 0 {
			changeHeroIds = append(changeHeroIds, j.Data.HeroId)
		}
	}
	return changeHeroIds
}

func (sjm *SeasonJewelryM) RemoveHeroID(hids []uint64) {
	var seasonJewelryLists []*cl.SeasonJewelry
	for _, hid := range hids {
		for _, seasonJewelry := range sjm.GetJewelryListByHeroId(hid) {
			if seasonJewelry == nil {
				continue
			}
			// 先处理英雄数据
			sjm.SeasonJewelryTakeOffByHero(seasonJewelry.GetHid(), seasonJewelry.GetSysId()) // 清英雄穿戴数据缓存
			// 再处理赛季装备数据
			sjm.SeasonJewelryTakeOff(seasonJewelry)

			seasonJewelryLists = append(seasonJewelryLists, seasonJewelry.Data.Clone())
		}

		// 清除锁定数据
		sjm.UnlockHero(hid)
	}
	sjm.u.SendSeasonJewelryToClient(seasonJewelryLists)
}

func (sjm *SeasonJewelryM) ClearSeasonJewelry() {
	for id := range sjm.datas {
		sjm.deletes[id] = struct{}{}
	}
	sjm.changes = make(map[uint64]*cl.SeasonJewelry)
	sjm.datas = make(map[uint64]*SeasonJewelry)
	sjm.heroJewelryList = make(map[uint64]map[uint32]*SeasonJewelry) // cache
}

func (sjm *SeasonJewelryM) ClearSeasonJewelryConfig() {
	sjm.config = &db.SeasonJewelryConfig{
		LockConfig:      make(map[uint64]bool),
		DecomposeConfig: &cl.SeasonJewelryDecomposeCfg{},
	}
	sjm.SaveConfig()
}

func (sjm *SeasonJewelryM) IsHeroLocked(heroId uint64) bool {
	if sjm.config == nil || sjm.config.LockConfig == nil {
		return false
	}

	_, exist := sjm.config.LockConfig[heroId]
	return exist
}

func (sjm *SeasonJewelryM) LockHero(heroId uint64) {
	if sjm.config == nil {
		sjm.config = &db.SeasonJewelryConfig{
			LockConfig: make(map[uint64]bool),
		}
	}
	if sjm.config.LockConfig == nil {
		sjm.config.LockConfig = make(map[uint64]bool)
	}
	sjm.config.LockConfig[heroId] = true
	sjm.SaveConfig()
}

func (sjm *SeasonJewelryM) UnlockHero(heroId uint64) {
	if sjm.config == nil || sjm.config.LockConfig == nil {
		return
	}
	_, exist := sjm.config.LockConfig[heroId]
	if !exist {
		return
	}
	delete(sjm.config.LockConfig, heroId)
	sjm.SaveConfig()
}

func (sjm *SeasonJewelryM) SetDecomposeConfig(config *cl.SeasonJewelryDecomposeCfg) {
	if sjm.config == nil {
		sjm.config = &db.SeasonJewelryConfig{
			LockConfig: make(map[uint64]bool),
		}
	}
	sjm.config.DecomposeConfig = config
	sjm.SaveConfig()
}

func (sjm *SeasonJewelryM) canAutoDecompose(info *goxml.SeasonJewelryInfo) bool {
	// 获取自动分解配置
	config := sjm.GetDecomposeConfig()
	if config == nil {
		return false
	}

	// 判断配置是否有效
	if !sjm.IsDecomposeConfigValid() {
		return false
	}

	// 同时满足稀有度和等级条件
	rareOk := util.InUint32s(config.AutoRare, info.Rare)
	levelOk := info.Level <= config.AutoLevel

	return rareOk && levelOk
}

func (sjm *SeasonJewelryM) CheckWearObjects(operateType uint32, objs []*cl.SeasonJewelryWearObject) uint32 {
	heroVisited, jewelryVisited := make(map[uint64]struct{}), make(map[uint64]struct{})
	for _, obj := range objs {
		// 检查英雄重复
		if _, exist := heroVisited[obj.Hid]; exist {
			l4g.Errorf("user: %d CheckWearObjects: hero is duplicate. hid:%d", sjm.u.ID(), obj.Hid)
			return uint32(cret.RET_CLIENT_REQUEST_ERROR)
		}
		heroVisited[obj.Hid] = struct{}{}

		// 获取英雄数据
		hero := sjm.u.HeroManager().Get(obj.Hid)
		if hero == nil {
			l4g.Errorf("user: %d CheckWearObjects: hero not exist. hid:%d", sjm.u.ID(), obj.Hid)
			return uint32(cret.RET_HERO_NOT_EXIST)
		}

		// 检查是否存在被锁定的英雄（type = 2(多人一键脱)和 5(多人一键穿)时锁定状态才生效）
		if operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_TAKE_OFF) || operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR_ALL) {
			if sjm.IsHeroLocked(obj.Hid) {
				l4g.Errorf("user: %d CheckWearObjects: hero is locked. hid:%d", sjm.u.ID(), obj.Hid)
				return uint32(cret.RET_HERO_LOCKED)
			}
		}

		heroInfo := goxml.GetData().HeroInfoM.GetRecordById(hero.GetHeroSysID())
		if heroInfo == nil {
			l4g.Errorf("user: %d CheckWearObjects: heroInfo not exist. hid:%d", sjm.u.ID(), obj.Hid)
			return uint32(cret.RET_SYSTEM_DATA_ERROR)
		}

		// 穿戴检查1
		if operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_WEAR) || operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR) ||
			operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR_ALL) {
			if wRet := hero.CanWearSeasonJewelry(heroInfo.Rare); wRet != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d CheckWearObjects: hero can not wear season jewelry. hid:%d", sjm.u.ID(), obj.Hid)
				return wRet
			}
		}

		for pos, jid := range obj.PosJewelry {
			if _, exist := jewelryVisited[jid]; exist {
				l4g.Errorf("user: %d CheckWearObjects: jewelry is duplicate. jid:%d", sjm.u.ID(), jid)
				return uint32(cret.RET_CLIENT_REQUEST_ERROR)
			}
			jewelryVisited[jid] = struct{}{}

			jewelry := sjm.Get(jid)
			if jewelry == nil {
				l4g.Errorf("user: %d CheckWearObjects: jewelry not exist. jid:%d", sjm.u.ID(), jid)
				return uint32(cret.RET_SEASON_JEWELRY_NOT_EXIST)
			}

			jewelryInfo := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelry.GetSysId())
			if jewelryInfo == nil {
				l4g.Errorf("user: %d CheckWearObjects: jewelryInfo not exist. jid:%d", sjm.u.ID(), jid)
				return uint32(cret.RET_SYSTEM_DATA_ERROR)
			}

			// 穿戴检查2
			if operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_WEAR) || operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR) ||
				operateType == uint32(common.SEASON_JEWELRY_OPERATE_TYPE_JOT_AUTO_WEAR_ALL) {
				if jewelryInfo.Pos != pos {
					l4g.Errorf("user: %d CheckWearObjects: jewelry pos not match. jid:%d pos:%d", sjm.u.ID(), jid, pos)
					return uint32(cret.RET_CLIENT_REQUEST_ERROR)
				}
				// 获取添加可变羁绊后的英雄羁绊
				changedLinks := hero.GetActiveLinkIdsWithChangeLink(heroInfo, sjm.u)
				if len(changedLinks) == 0 {
					l4g.Errorf("user: %d CheckWearObjects: hero active links is empty. hid:%d", sjm.u.ID(), obj.Hid)
					return uint32(cret.RET_SYSTEM_DATA_ERROR)
				}
				if !util.InUint32s(changedLinks, jewelryInfo.Link) {
					l4g.Errorf("user: %d CheckWearObjects: jewelry link %d not in hero active links %+v", sjm.u.ID(), jewelryInfo.Link, changedLinks)
					return uint32(cret.RET_CLIENT_REQUEST_ERROR)
				}
			}
		}
	}
	return uint32(cret.RET_OK)
}

func (sjm *SeasonJewelryM) GetTaskOffAndWearList(objs []*cl.SeasonJewelryWearObject) ([]uint64, map[uint64]map[uint32]uint64, map[uint64]struct{}) {
	// 所有受影响的英雄
	affectHids := make(map[uint64]struct{})
	// 获取所有脱和穿的装备
	var takeOffJewelryIds []uint64
	wearJewelryIds := make(map[uint64]map[uint32]uint64) // heroId => pos => jewelryId
	for _, obj := range objs {
		affectHids[obj.Hid] = struct{}{}
		for pos := goxml.SeasonJewelryPosMin; pos <= goxml.SeasonJewelryPosMax; pos++ {
			var oldJewelryId uint64
			if oldJewelry := sjm.GetHeroSeasonJewelryByPos(obj.Hid, pos); oldJewelry != nil {
				oldJewelryId = oldJewelry.GetId()
			}
			newJewelryId := obj.PosJewelry[pos]

			if oldJewelryId == newJewelryId { // 装备保持不变
				continue
			}
			if oldJewelryId != 0 && newJewelryId != 0 { // 1.旧装备替换为新装备
				// 脱自己的旧装备
				takeOffJewelryIds = append(takeOffJewelryIds, oldJewelryId)

				// 穿新装备
				_, exist := wearJewelryIds[obj.Hid]
				if !exist {
					wearJewelryIds[obj.Hid] = make(map[uint32]uint64)
				}
				wearJewelryIds[obj.Hid][pos] = newJewelryId

				// 脱别人的旧装备
				newJewelry := sjm.Get(newJewelryId)
				if newJewelry == nil {
					continue
				}
				if otherHid := newJewelry.GetHid(); otherHid != obj.Hid {
					takeOffJewelryIds = append(takeOffJewelryIds, newJewelryId)
				}
			} else if oldJewelryId == 0 { // 2. 无旧装备，穿新装备
				_, exist := wearJewelryIds[obj.Hid]
				if !exist {
					wearJewelryIds[obj.Hid] = make(map[uint32]uint64)
				}
				wearJewelryIds[obj.Hid][pos] = newJewelryId

				newJewelry := sjm.Get(newJewelryId)
				if newJewelry == nil {
					continue
				}
				if otherHid := newJewelry.GetHid(); otherHid != obj.Hid {
					takeOffJewelryIds = append(takeOffJewelryIds, newJewelryId)
				}
			} else { // 3. 脱下旧装备，不穿新装备
				takeOffJewelryIds = append(takeOffJewelryIds, oldJewelryId)
			}
		}
	}

	return takeOffJewelryIds, wearJewelryIds, affectHids
}

func (sjm *SeasonJewelryM) CheckWear(wearJewelryIds map[uint64]map[uint32]uint64) uint32 {
	for hid, v := range wearJewelryIds {
		for pos, jid := range v {
			// 1.英雄的这个位置是否无装备
			oldJewelry := sjm.GetHeroSeasonJewelryByPos(hid, pos)
			if oldJewelry != nil {
				l4g.Errorf("user: %d CheckWear: hero pos %d has jewelry %d", sjm.u.ID(), pos, oldJewelry.GetId())
				return uint32(cret.RET_ERROR)
			}

			// 2.要穿的装备是否无主
			newJewelry := sjm.Get(jid)
			if newJewelry.GetHid() != 0 {
				l4g.Errorf("user: %d CheckWear: jewelry has hero. jid %d hid %d", sjm.u.ID(), jid, newJewelry.GetHid())
				return uint32(cret.RET_ERROR)
			}
		}
	}

	return uint32(cret.RET_OK)
}

func (sjm *SeasonJewelryM) FlushWearChange(changeHids, changeJids []uint64) ([]*cl.Hero, []*cl.SeasonJewelry) {
	// 去重
	heroes := make(map[uint64]struct{})
	for _, hid := range changeHids {
		heroes[hid] = struct{}{}
	}

	jewelrys := make(map[uint64]struct{})
	for _, jid := range changeJids {
		jewelrys[jid] = struct{}{}
	}

	// 生成返回数据
	heroList := make([]*cl.Hero, 0, len(heroes))
	for hid := range heroes {
		hero := sjm.u.HeroManager().Get(hid)
		if hero == nil {
			continue
		}
		heroList = append(heroList, hero.Flush())
	}

	jewelryList := make([]*cl.SeasonJewelry, 0, len(jewelrys))
	for jid := range jewelrys {
		jewelry := sjm.Get(jid)
		if jewelry == nil {
			continue
		}
		jewelryList = append(jewelryList, jewelry.Flush())
	}

	return heroList, jewelryList
}

/*****************HeroCache*****************/

func (sjm *SeasonJewelryM) GetJewelryListByHeroId(hid uint64) map[uint32]*SeasonJewelry {
	if sjm.heroJewelryList == nil {
		return nil
	}
	return sjm.heroJewelryList[hid]
}

func (sjm *SeasonJewelryM) GetHeroSeasonJewelryByPos(hid uint64, pos uint32) *SeasonJewelry {
	if sjm.heroJewelryList == nil {
		return nil
	}
	if _, exist := sjm.heroJewelryList[hid]; !exist {
		return nil
	}
	return sjm.heroJewelryList[hid][pos]
}

func (sjm *SeasonJewelryM) SeasonJewelryWear(hid uint64, jewelry *SeasonJewelry) {
	jewelry.SetHid(hid)
	sjm.SetChange(jewelry.GetId())
}

func (sjm *SeasonJewelryM) SeasonJewelryWearByHero(hero *Hero, jid uint64, pos uint32) {
	hid := hero.GetHid()
	// 处理缓存数据
	if _, exist := sjm.heroJewelryList[hid]; !exist {
		sjm.heroJewelryList[hid] = make(map[uint32]*SeasonJewelry)
	}
	sjm.heroJewelryList[hid][pos] = sjm.Get(jid)

	// 处理英雄数据
	hero.WearSeasonJewelry(jid, pos)
	sjm.u.HeroManager().SetChange(hid)
}

func (sjm *SeasonJewelryM) SeasonJewelryTakeOff(jewelry *SeasonJewelry) {
	jewelry.SetHid(0)
	sjm.SetChange(jewelry.GetId())
}

func (sjm *SeasonJewelryM) SeasonJewelryTakeOffByHero(hid uint64, jewelrySysId uint32) {
	if hid == 0 {
		return
	}
	heroM := sjm.u.HeroManager()
	hero := heroM.Get(hid)
	if hero == nil {
		l4g.Errorf("[SeasonJewelryM] SeasonJewelryTakeOff: hero is nil. hid %d", hid)
		return
	}

	info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelrySysId)
	if info == nil {
		l4g.Errorf("[SeasonJewelryM] SeasonJewelryTakeOff: info is nil. jewelrySysId %d", jewelrySysId)
		return
	}
	pos := info.Pos

	// 处理英雄数据
	hero.RemoveSeasonJewelry([]uint32{pos})
	heroM.SetChange(hid)

	// 处理缓存数据
	if _, exist := sjm.heroJewelryList[hid]; !exist {
		return
	}
	delete(sjm.heroJewelryList[hid], pos)
}

/*****************Season*****************/

func (sjm *SeasonJewelryM) OnSeasonEnd(srv servicer, needSettleAward bool) {
	seasonId := sjm.u.GetSeasonID()
	if seasonId == 0 {
		return
	}

	// 1.赛季装备结算发奖
	if needSettleAward {
		sjm.SettleAward(srv, seasonId)
	}

	// 2.清除赛季装备数据
	changeHeroIds := sjm.GetHeroIdsWithSeasonJewelry()
	for _, heroId := range changeHeroIds {
		hero := sjm.u.HeroManager().Get(heroId)
		if hero == nil {
			l4g.Errorf("SeasonJewelryM.OnSeasonEnd hero is nil. heroId:%d", heroId)
			continue
		}
		hero.RemoveAllSeasonJewelry()
		sjm.u.HeroManager().SetChange(heroId)
	}
	sjm.ClearSeasonJewelry()
	sjm.u.SendSeasonJewelryToClient(nil)

	// 3. 清除当前赛季设置
	sjm.ClearSeasonJewelryConfig()

	// 4.删除赛季资源（道具和代币）
	sjm.DelSeasonResources(srv)
}

// 删除过期的赛季资源（包括道具和代币）
func (sjm *SeasonJewelryM) DelSeasonResources(srv servicer) {
	delConfigs := goxml.GetData().SeasonJewelryConfigInfoM.GetDelResources()
	delRes := make([]*cl.Resource, 0, len(delConfigs))
	for _, delConfig := range delConfigs {
		delCount := uint32(0)
		if delConfig.Type == uint32(common.RESOURCE_ITEM) { // 道具类型
			delCount = sjm.u.GetItemCount(delConfig.Value)
		} else if delConfig.Type == uint32(common.RESOURCE_TOKEN) { // 代币类型
			delCount = uint32(sjm.u.GetTokenCount(delConfig.Value))
		}
		if delCount > 0 {
			delRes = append(delRes, goxml.GenSimpleResource(delConfig.Type, delConfig.Value, delCount))
		}
	}

	if len(delRes) == 0 {
		return
	}

	retCode := sjm.u.Consume(srv, delRes, uint32(log.RESOURCE_CHANGE_REASON_SEASON_JEWELRY_SEASON_RESET), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d seasonJewelry.OnSeasonEnd: Consume error. ret:%d", sjm.u.ID(), retCode)
	}
}

func (sjm *SeasonJewelryM) SettleAward(srv servicer, seasonId uint32) {
	recyclePoint := sjm.getRecyclePoint()
	if recyclePoint == 0 {
		return
	}
	rewardInfo := goxml.GetData().SeasonJewelryRecycleInfoM.GetRecordBySeasonIdPointMaxLe(seasonId, recyclePoint)
	if rewardInfo == nil || len(rewardInfo.Rewards) == 0 {
		l4g.Errorf("SeasonJewelryM.SettleAward rewardInfo is nil. recyclePoint:%d", recyclePoint)
		return
	}
	SeasonJewelryRecycleMail(srv, sjm.u.ID(), rewardInfo.Rewards, MailIDSeasonJewelryRecycle)
	sjm.u.LogSeasonJewelryRecycle(srv, recyclePoint, rewardInfo.Rewards)
	l4g.Infof("user %d SeasonJewelryM.SettleAward: success, recyclePoint:%d rewards:%v", sjm.u.ID(), recyclePoint, rewardInfo.Rewards)
}

func (sjm *SeasonJewelryM) getRecyclePoint() uint32 {
	var recyclePoint uint32

	// 无赛季装备
	if len(sjm.datas) == 0 {
		l4g.Debugf("user %d SeasonJewelryM.getRecyclePoint: no season jewelry", sjm.u.ID())
		return recyclePoint
	}

	// 获取回收配置
	recycleConfig := goxml.GetData().SeasonJewelryConfigInfoM.GetRecordByKey("REWARD_POINT_MAX")
	if recycleConfig == nil {
		l4g.Errorf("user %d SeasonJewelryM.getRecyclePoint: recycleConfig is nil", sjm.u.ID())
		return recyclePoint
	}

	// 筛选满足条件的装备
	recycleList := sjm.filterRecycleJewelryList(recycleConfig.Count)
	if len(recycleList) == 0 { // 有赛季装备但获取回收列表失败
		l4g.Errorf("user %d SeasonJewelryM.getRecyclePoint: recycleList is empty", sjm.u.ID())
		return recyclePoint
	}

	// 计算回收积分
	for _, v := range recycleList {
		recyclePoint += v.point
	}

	return recyclePoint
}

func (sjm *SeasonJewelryM) filterRecycleJewelryList(recycleNum uint32) []*SeasonJewelryRecycleSortUnit {
	units := make([]*SeasonJewelryRecycleSortUnit, 0, len(sjm.datas))
	for _, jewelry := range sjm.datas {
		if jewelry == nil {
			continue
		}
		rare := jewelry.GetRare()
		units = append(units, &SeasonJewelryRecycleSortUnit{
			sysId: jewelry.GetSysId(),
			rare:  rare,
			point: goxml.GetData().SeasonJewelryConfigInfoM.GetRecyclePointsByRare(rare),
		})
	}

	// 回收规则：按品质排序，回收前N个
	slices.SortFunc(units, func(a, b *SeasonJewelryRecycleSortUnit) int {
		return cmp.Compare(b.rare, a.rare)
	})

	if uint32(len(units)) <= recycleNum {
		return units
	}
	return units[:recycleNum]
}

type SeasonJewelryRecycleSortUnit struct {
	sysId uint32
	rare  uint32
	point uint32
}

/*****************Battle*****************/

func (sjm *SeasonJewelryM) RaisePSs(formationId uint32, teamIndex int) map[uint32][]uint64 {
	pss := make(map[uint32][]uint64)

	formationTeam := sjm.u.GetFormationTeam(formationId, teamIndex)
	if formationTeam == nil || len(formationTeam.Info) == 0 {
		return nil
	}

	for _, heroInfo := range formationTeam.Info {
		if heroInfo == nil {
			continue
		}
		suitNum := make(map[uint32]uint32) // suitId => num
		passiveSkills := make([]uint32, 0)
		for _, jewelry := range sjm.GetJewelryListByHeroId(heroInfo.Hid) { // 获取英雄身上的赛季装备
			jewelryInfo := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelry.GetSysId())
			if jewelryInfo == nil {
				continue
			}
			suitNum[jewelryInfo.Suit]++

			// 词条技能
			for _, skill := range jewelry.GetSkills() {
				if skill == nil {
					continue
				}
				info := goxml.GetData().SeasonJewelrySkillInfoM.GetRecordBySkillId(skill.SkillId)
				if info == nil {
					continue
				}
				levelInfo := goxml.GetData().SeasonJewelrySkillLvInfoM.GetRecordBySkillTypeLevel(info.SkillType, skill.SkillLevel)
				if levelInfo == nil {
					continue
				}
				passiveSkills = append(passiveSkills, levelInfo.PassiveSkill)
			}
		}

		// 套装技能
		passiveSkills = append(passiveSkills, sjm.getTriggeredSuitSkills(suitNum)...)

		for _, ps := range passiveSkills {
			raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(ps)
			if raisePassiveSkillInfo == nil {
				l4g.Errorf("user:%d SeasonJewelry.RaisePSs: raisePassiveSkillInfo is nil. id: %d", sjm.u.ID(), ps)
				continue
			}
			pss[heroInfo.Pos] = append(pss[heroInfo.Pos], raisePassiveSkillInfo.ID)
		}
	}

	return pss
}

func (sjm *SeasonJewelryM) getTriggeredSuitSkills(suitIdNum map[uint32]uint32) []uint32 {
	suitSkills := make([]uint32, 0)
	for suitType, data := range sjm.getSuitUnits(suitIdNum) {
		suitInfo := goxml.GetData().SeasonJewelrySuitInfoM.GetRecordBySuitTypeSuitLevel(suitType, data.minLevel)
		if suitInfo == nil {
			continue
		}

		if data.num < suitInfo.SuitNum1 {
			continue
		}

		suitSkills = append(suitSkills, suitInfo.SuitSkill1)
	}
	return suitSkills
}

type SuitUnit struct {
	suitType uint32
	num      uint32
	minLevel uint32
}

func (sjm *SeasonJewelryM) getSuitUnits(suitIdNum map[uint32]uint32) map[uint32]*SuitUnit {
	suitUnits := make(map[uint32]*SuitUnit)
	for suitId, num := range suitIdNum {
		suitInfo := goxml.GetData().SeasonJewelrySuitInfoM.GetRecordBySuitId(suitId)
		if suitInfo == nil {
			continue
		}
		_, exist := suitUnits[suitInfo.SuitType]
		if !exist {
			suitUnits[suitInfo.SuitType] = &SuitUnit{
				suitType: suitInfo.SuitType,
				num:      num,
				minLevel: suitInfo.SuitLevel,
			}
		} else {
			suitUnits[suitInfo.SuitType].num += num
		}

		if suitInfo.SuitLevel < suitUnits[suitInfo.SuitType].minLevel {
			suitUnits[suitInfo.SuitType].minLevel = suitInfo.SuitLevel
		}
	}
	return suitUnits
}

func (sjm *SeasonJewelryM) CalcPower(hero *Hero) uint32 {
	if hero == nil || hero.data == nil {
		return 0
	}

	var power uint32
	for _, jewelry := range sjm.GetJewelryListByHeroId(hero.GetHid()) {
		if jewelry == nil {
			continue
		}
		info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelry.GetSysId())
		if info == nil {
			continue
		}
		power += info.Power
	}

	return power
}

func (sjm *SeasonJewelryM) TriggerEvent(srv servicer, sysID, count uint32) {
	seasonJewelryInfo := goxml.GetData().SeasonJewelryInfoM.GetRecordById(sysID)
	if seasonJewelryInfo == nil {
		return
	}
	// 赛季装备过期不触发，分解依然会触发
	sjm.u.FireCommonEvent(srv.EventM(), event.IeSeasonJewelryGet, uint64(count), seasonJewelryInfo.Level) // 获得X等级以上的赛季装备Y件
}

type rareSortUnit struct {
	rare   uint32
	reward *cl.Resource
}

func (sjm *SeasonJewelryM) AutoDecompose(srv servicer, rewards []*cl.Resource, rewardType uint32) []*cl.Resource {
	realRewards := make([]*cl.Resource, 0, len(rewards))

	// 获取同级同羁绊同位置装备的保留数量
	var reserveNum uint32
	if rewardType == goxml.SeasonJewelryRewardTypeRest {
		reserveNum = goxml.GetData().SeasonDoorConfigInfoM.AwardAutoDecomposeNum
	} else if rewardType == goxml.SeasonJewelryRewardTypeMail {
		reserveNum = goxml.GetData().SeasonDoorConfigInfoM.MailAutoDecomposeNum
	} else {
		return rewards
	}
	autoDecomposeMaxLevel := goxml.GetData().SeasonDoorConfigInfoM.AutoDecomposeMaxLevel

	jewelryStr2JewelrySlice := make(map[string][]*rareSortUnit) // level_position_link => []*rareSortUnit
	generateStringKey := func(level, position, link uint32) string {
		return fmt.Sprintf("%d_%d_%d", level, position, link)
	}

	for _, reward := range rewards {
		// 只有赛季装备资源才会被分解
		if reward.Type != uint32(common.RESOURCE_SEASON_JEWELRY) {
			realRewards = append(realRewards, reward)
			continue
		}

		info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(reward.Value)
		if info == nil {
			continue
		}

		// level > X 不会被分解
		if info.Level > autoDecomposeMaxLevel {
			realRewards = append(realRewards, reward)
			continue
		}

		key := generateStringKey(info.Level, info.Pos, info.Link) // 使用字符串拼接的方式来构造key:level_pos_link
		_, exist := jewelryStr2JewelrySlice[key]
		if !exist {
			jewelryStr2JewelrySlice[key] = make([]*rareSortUnit, 0)
		}

		jewelryStr2JewelrySlice[key] = append(jewelryStr2JewelrySlice[key], &rareSortUnit{
			rare:   info.Rare,
			reward: reward,
		})
	}

	// 同level同pos同link的饰品选取rare前reserveNum的
	for _, units := range jewelryStr2JewelrySlice {
		// 将units按照rare从大到小排序
		slices.SortFunc(units, func(a, b *rareSortUnit) int {
			return cmp.Compare(b.rare, a.rare)
		})

		// units中的reward.count保留前reservrNum个
		needNum := reserveNum
		for _, unit := range units {
			if unit == nil || unit.reward == nil {
				continue
			}
			unitNum := unit.reward.Count

			// needNum == 0说明已经结束，后面的都被分解
			if needNum == 0 {
				decomposeRewards := sjm.calcDecomposeRewards([]*rareSortUnit{unit}, srv)
				realRewards = append(realRewards, decomposeRewards...)
				continue
			}

			if needNum >= unitNum {
				realRewards = append(realRewards, unit.reward)
				needNum -= unitNum
			} else { // needNum < unitNum 临界点
				// 需要的部分
				decomposeNum := unitNum - needNum
				if needNum > 0 {
					realRewards = append(realRewards, goxml.GenSimpleResource(unit.reward.Type, unit.reward.Value, needNum))
				}

				// 剩余的部分分解
				unitClone := &rareSortUnit{
					rare:   unit.rare,
					reward: goxml.GenSimpleResource(unit.reward.Type, unit.reward.Value, decomposeNum),
				}
				decomposeRewards := sjm.calcDecomposeRewards([]*rareSortUnit{unitClone}, srv)
				realRewards = append(realRewards, decomposeRewards...)

				needNum = 0
			}
		}
	}
	realRewards = MergeResources(realRewards)
	return realRewards
}

func (sjm *SeasonJewelryM) calcDecomposeRewards(decomposeUnits []*rareSortUnit, srv servicer) []*cl.Resource {
	decomposeRewards := make([]*cl.Resource, 0)
	for _, decomposeUnit := range decomposeUnits {
		if decomposeUnit == nil || decomposeUnit.reward == nil {
			continue
		}
		info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(decomposeUnit.reward.Value)
		if info == nil {
			continue
		}
		decomposeNum := decomposeUnit.reward.Count
		sjm.TriggerEvent(srv, decomposeUnit.reward.Value, decomposeNum)

		for _, v := range info.DecomposeRewards {
			decomposeRewards = append(decomposeRewards, goxml.GenSimpleResource(v.Type, v.Value, v.Count*decomposeNum))
		}
	}
	return decomposeRewards
}
