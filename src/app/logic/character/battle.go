package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/in/l2c"
	"app/protos/in/p2l"
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"fmt"

	"github.com/gogo/protobuf/proto"
	"github.com/golang/snappy"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

// PVP通用战斗参数
type commonBattleParamWithUser struct {
	sendReport  bool                  // 是否发送战报
	attackFID   common.FORMATION_ID   // 进攻阵容id
	defenseFID  common.FORMATION_ID   // 防守阵容id
	clientData  []byte                // 前端透传数据
	battleParam *battle.ManagerParams // 战斗参数
	toUser      *User                 // 防守方
	fixFunc     fixBattleParamFunc    // 修正函数
}

func newCommonBattleParamWithUser(attackFID, defenseFID common.FORMATION_ID, clientData []byte,
	altAttr *battle.AltAttr, toUser *User, fixFunc fixBattleParamFunc) *commonBattleParamWithUser {
	return &commonBattleParamWithUser{
		sendReport:  NeedSendReport,
		attackFID:   attackFID,
		defenseFID:  defenseFID,
		clientData:  clientData,
		battleParam: battle.NewManagerParams(attackFID, defenseFID, 0, 0, altAttr, 0),
		toUser:      toUser,
		fixFunc:     fixFunc,
	}
}

func (u *User) commonProcessBattleWithUser(srv servicer, cbp *commonBattleParamWithUser) (*bt.BattleSummary, ret.RET) {
	battleReport := u.NewBattleReport(cbp.clientData, cbp.attackFID)
	if cbp.fixFunc != nil {
		cbp.fixFunc(cbp.battleParam, 0)
	}
	bat := u.NewBattleWithUser(srv, cbp.toUser, cbp.battleParam)
	if bat == nil {
		l4g.Errorf("user:%d commonProcessBattleWithUser init battle failed, toUserID:%d", u.ID(), cbp.toUser.ID())
		return nil, ret.RET_ERROR
	}
	defer bat.Free()
	isWin, retCode := DoBattleResult(srv, u, bat, battleReport)
	if retCode != ret.RET_OK {
		l4g.Errorf("user: %d commonProcessBattleWithUser do battle failed, toUserID: %d errorID: %d altAttr: %+v altRaisePS: %+v",
			u.ID(), cbp.toUser.ID(), retCode, cbp.battleParam.AltAttr, cbp.battleParam.AltRaisePS)
		return nil, ret.RET_ERROR
	}
	u.ProcessBattleReport(srv, cbp.sendReport, isWin, battleReport, []*battle.Manager{bat})
	return makeBattleSummary(battleReport), ret.RET_OK
}

// PVE通用战斗参数
type commonBattleParamWithMonster struct {
	sendReport    bool                // 是否发送战报
	attackFID     common.FORMATION_ID // 玩法进攻阵容id
	clientData    []byte              // 前端透传数据
	monsterGroups []uint32            // 怪物id列表
	winType       BattleWinType       // 战斗胜利标准
	fixFunc       fixBattleParamFunc  // 修正函数
	altAttr       *battle.AltAttr     // 修正属性
}

type fixBattleParamFunc func(*battle.ManagerParams, int)

func newCommonBattleParamWithMonster(attackFID common.FORMATION_ID, clientData []byte, monsterGroups []uint32, sendReport bool,
	fixFunc fixBattleParamFunc, altAttr *battle.AltAttr) *commonBattleParamWithMonster {
	return &commonBattleParamWithMonster{
		sendReport:    sendReport,
		attackFID:     attackFID,
		clientData:    clientData,
		monsterGroups: monsterGroups,
		winType:       BattleWinTypeAllWin,
		fixFunc:       fixFunc,
		altAttr:       altAttr,
	}
}

// 机器人展示信息
type botShowData struct {
	name     string // 昵称
	seasonLv uint32 // 赛季等级
}

func (u *User) commonProcessBattleWithMonster(srv servicer, cbp *commonBattleParamWithMonster, botShow *botShowData,
	extraData ...uint32) (*bt.BattleSummary, ret.RET, uint32) {
	battleReport := u.NewBattleReport(cbp.clientData, cbp.attackFID)
	win := true
	bats := make([]*battle.Manager, 0, len(cbp.monsterGroups))
	var defeatTeam uint32
	var loseCount, winCount int
	halfCount := len(cbp.monsterGroups) / 2 //nolint:mnd
	for index, monsterGroup := range cbp.monsterGroups {
		battleParam := battle.NewManagerParams(cbp.attackFID, 0, index, index, cbp.altAttr, monsterGroup)
		if cbp.fixFunc != nil {
			cbp.fixFunc(battleParam, index)
		}
		bat := u.NewBattleWithMonster(srv, battleParam, extraData...)
		if bat == nil {
			l4g.Errorf("user:%d commonProcessBattleWithMonster init battle failed, monsterGroup: %d",
				u.ID(), monsterGroup)
			return nil, ret.RET_ERROR, defeatTeam
		}
		defer bat.Free()
		if botShow != nil {
			bat.SetDefenseName(botShow.name)
			bat.SetSeasonLevel(botShow.seasonLv)
		}
		bats = append(bats, bat)

		isWin, retno := DoBattleResult(srv, u, bat, battleReport)
		if retno != ret.RET_OK {
			l4g.Errorf("user:%d commonProcessBattleWithMonster do battle failed, monsterGroup:%d, errCode:%v",
				u.ID(), monsterGroup, retno)
			return nil, retno, defeatTeam
		}

		if cbp.winType == BattleWinTypeAllWin {
			if !isWin {
				win = false
				defeatTeam = uint32(index + 1)
				break
			}
		} else if cbp.winType == BattleWinTypeHalfWin {
			if isWin {
				winCount++
			} else {
				loseCount++
			}
			if winCount > halfCount || loseCount > halfCount {
				if loseCount > halfCount {
					win = false
				}
				break
			}
		}
	}

	u.ProcessBattleReport(srv, cbp.sendReport, win, battleReport, bats)
	u.buildCognitionLogLinkInfo(srv, battleReport, extraData...)
	return makeBattleSummary(battleReport), ret.RET_OK, defeatTeam
}

// 特殊PVE通用战斗参数
type commonBattleParamWithMonsterAndAssists struct {
	sendReport     bool                  // 是否发送战报
	attackFID      common.FORMATION_ID   // 进攻阵容id
	clientData     []byte                // 前端透传数据
	battleParam    *battle.ManagerParams // 战斗参数
	assistMonsters []uint32
}

func newCommonBattleParamWithMonsterAndAssists(monsterGroup uint32, attackFID common.FORMATION_ID,
	clientData []byte, altAttr *battle.AltAttr, assistMonsters []uint32) *commonBattleParamWithMonsterAndAssists {
	return &commonBattleParamWithMonsterAndAssists{
		sendReport:     NeedSendReport,
		attackFID:      attackFID,
		clientData:     clientData,
		battleParam:    battle.NewManagerParams(attackFID, 0, 0, 0, altAttr, monsterGroup),
		assistMonsters: assistMonsters,
	}
}

func (u *User) commonProcessBattleWithMonsterAndAssists(srv servicer, cbp *commonBattleParamWithMonsterAndAssists) (*bt.MultipleTeamsReport, ret.RET) {
	battleReport := u.NewBattleReport(cbp.clientData, cbp.attackFID)
	bat := u.NewBattleWithMonsterAndAssists(srv, cbp.assistMonsters, cbp.battleParam)
	if bat == nil {
		l4g.Errorf("user:%d commonProcessBattleWithMonsterAndAssists init battle failed", u.ID())
		return nil, ret.RET_ERROR
	}
	defer bat.Free()
	isWin, retCode := DoBattleResult(srv, u, bat, battleReport)
	if retCode != ret.RET_OK {
		l4g.Errorf("user: %d commonProcessBattleWithMonsterAndAssists do battle failed, errorID: %d altAttr: %+v altRaisePS: %+v",
			u.ID(), retCode, cbp.battleParam.AltAttr, cbp.battleParam.AltRaisePS)
		return nil, ret.RET_ERROR
	}
	u.ProcessBattleReport(srv, NeedSendReport, isWin, battleReport, []*battle.Manager{bat})
	return battleReport, ret.RET_OK
}

/********************每个玩法添加自己的战斗协议***********************/
func (u *User) AttackDungeon(srv servicer, monsterGroups []uint32, assistMonster uint32,
	clientData []byte, quick bool) (*bt.BattleSummary, ret.RET, uint32) {
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_DUNGEON, clientData, monsterGroups, !quick, nil, nil)
	battleReport, retCode, defeatTeam := u.commonProcessBattleWithMonster(srv, cbp, nil, assistMonster)
	return battleReport, retCode, defeatTeam
}

func (u *User) AttackTower(srv servicer, towerInfo *goxml.TowerInfoExt, clientData []byte,
	quick bool) (*bt.BattleSummary, ret.RET, uint32) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAltRaisePS(u.getLinkExtraSkills(towerInfo, index))
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_TOWER, clientData, towerInfo.MonsterGroups, !quick, fixFunc, nil)
	battleReport, retCode, defeatTeam := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleReport, retCode, defeatTeam
}

func (u *User) getLinkExtraSkills(towerInfo *goxml.TowerInfoExt, index int) *battle.AltRaisePS {
	formationLinkInfo := u.FormationManager().GetFormationLinkInfo(uint32(common.FORMATION_ID_FI_TOWER), index)
	if formationLinkInfo == nil {
		return nil
	}
	activeLinks := formationLinkInfo.ActivedLinks
	towerActivedLinkSkills := make(map[uint32]uint32)
	for id, condition := range towerInfo.TowerBuffs {
		for _, activedLink := range activeLinks {
			if activedLink.Num >= condition {
				towerActivedLinkSkills[id]++
			}
		}
	}
	altRaisePS := battle.NewAltRaisePS()
	raisePSMap := make(map[uint32][]uint64)

	for id, layer := range towerActivedLinkSkills {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(id, layer)
		if raisePSInfo == nil {
			l4g.Errorf("%d RaisePassiveSkillInfoM GetPassiveSkill error id: %d layer: %d", u.ID(), id, layer)
			continue
		}
		raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePSInfo.ID)
	}
	altRaisePS.AltAttack(raisePSMap)
	return altRaisePS
}

// 竞技场攻打真人
func (u *User) AttackArenaUser(srv servicer, toUser *User, attackFormationId, defenseFormationId common.FORMATION_ID, clientData []byte) (*bt.BattleSummary, ret.RET) {
	cbp := newCommonBattleParamWithUser(attackFormationId, defenseFormationId, clientData, nil, toUser, nil)
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	return battleSummary, retCode
}

// 竞技场攻打机器人
func (u *User) AttackArenaRobot(srv servicer, id uint32, name string, clientData []byte) (bool, string, ret.RET) {
	altAttr := battle.NewAltAttr()
	monsterAltAttr := u.calcCommonMonsterAltAttr(id)
	if len(monsterAltAttr) > 0 {
		altAttr.SetDefense(monsterAltAttr)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_ARENA_ATTACK, clientData, []uint32{id}, NeedSendReport, nil, altAttr)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, &botShowData{name: name})
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

/*
preventWin 是否正常的输赢判断 如果不是 就设置战斗不结束 只有回合到了才会结束
*/
func (u *User) AttackTrial(srv servicer, info *goxml.TrialInfoExt, formationID common.FORMATION_ID,
	altRaisePS *battle.AltRaisePS, clientData []byte) (string, uint32, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetNeedMoreResult()
		if info.MaxRound > 0 {
			battleParam.SetMaxRound(info.MaxRound)
		}
		//不正常的胜利判断 就设置为不结束回合
		if info.IsPreventWin {
			battleParam.SetNotFinish(true)
		}
		//target_type3为1且target_value3为击杀人数
		if len(info.Targets) > 4 && info.Targets[4] == TrialTargetOne {
			battleParam.SetMaxKill(info.Targets[5])
		}
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(formationID, clientData, []uint32{info.MonsterGroup}, NeedSendReport, fixFunc, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackTrial failed, err: %d", u.ID(), retCode)
		return "", 0, retCode
	}
	star := calStarNum(battleSummary, info)
	return battleSummary.GetId(), star, retCode
}

func (u *User) AttackActivityStory(srv servicer, info *goxml.ActivityStoryDungeonInfo,
	altRaisePS *battle.AltRaisePS, clientData []byte) (*bt.BattleSummary, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		if info.MaxRound > 0 {
			battleParam.SetMaxRound(info.MaxRound)
		}
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID(info.Formation), clientData,
		[]uint32{info.MonsterGroup}, NeedSendReport, fixFunc, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary, retCode
}

// AttackMaze : 迷宫中的pve战斗
// param@ id: monsterGroup id; 怪物组id
// param@ altAttr: 修正的属性
// param@ altPassiveSkill: 修正的被动技能
func (u *User) AttackMaze(srv servicer, id uint32, altAttr *battle.AltAttr,
	maxRound uint32, altRaisePS *battle.AltRaisePS, clientData []byte) (*bt.BattleSummary, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		if maxRound > 0 {
			battleParam.SetMaxRound(maxRound)
		}
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_MAZE, clientData, []uint32{id}, NeedSendReport, fixFunc, altAttr)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackMaze failed, err: %d", u.ID(), retCode)
		return nil, retCode
	}
	return battleSummary, retCode
}

// AttackMazeUser : 迷宫中的pvp战斗
// param@ toUser: 匹配主线战力榜上的玩家
// param@ altAttr: 修正的属性
// param@ altPassiveSkill: 修正的被动技能
func (u *User) AttackMazeUser(srv servicer, toUser *User, altAttr *battle.AltAttr,
	altRaisePS *battle.AltRaisePS, clientData []byte) (*bt.BattleSummary, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithUser(common.FORMATION_ID_FI_MAZE,
		common.FORMATION_ID_FI_DUNGEON, clientData, altAttr, toUser, fixFunc)
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackMazeUser %d failed, err: %d", u.ID(), toUser.ID(), retCode)
		return nil, retCode
	}
	return battleSummary, retCode
}

// 个人boss战斗
func (u *User) AttackMirage(srv servicer, id uint32, formationID common.FORMATION_ID, hurdleInfo *goxml.MirageHurdleInfo, altAttr *battle.AltAttr,
	altRaisePS *battle.AltRaisePS, clientData []byte) (bool, string, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(formationID, clientData, []uint32{id}, NeedSendReport, fixFunc, altAttr)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// buildCognitionLogLinkInfo
// @Description: 补充通关记录所需的战报内的内容
// @receiver u
// @param cLog
// @param battleReport
func (u *User) buildCognitionLogLinkInfo(srv servicer, battleReport *bt.MultipleTeamsReport, params ...uint32) {
	if _, exist := formationWithCognition[common.FORMATION_ID(battleReport.GetFormationId())]; !exist {
		return
	}

	if !u.IsFunctionOpen(uint32(common.FUNCID_MODULE_COGNITION), srv) {
		return
	}

	// 队伍中有人数不满的 就不记录通关记录
	for _, report := range battleReport.Reports {
		if report == nil {
			continue
		}
		if uint32(len(report.Attackers)) < FormationMaxPos {
			return
		}
	}

	fid := battleReport.GetFormationId()
	cLog := u.CognitionManager().NewCognitionLog(srv, fid, params...)
	cLog.ReportId = battleReport.GetId()
	cLog.AttackLinkInfo = make([]*cl.CognitionLogLinkInfo, len(battleReport.Reports))
	for i, report := range battleReport.Reports {
		if report == nil {
			continue
		}
		newCognitionLinkInfo := &cl.CognitionLogLinkInfo{}
		newCognitionLinkInfo.AttackLinkInfo = report.AttackLinkInfo
		newCognitionLinkInfo.AttackArtifactLinkInfo = report.AttackArtifactLinkInfo
		cLog.AttackLinkInfo[i] = newCognitionLinkInfo
	}

	formationInfo := goxml.GetData().FormationInfoM.Index(fid)
	if formationInfo != nil {
		ballType := goxml.GetData().PokemonBallInfoM.GetBallTypeByFunction(formationInfo.FunctionId)
		if ballType > 0 {
			cLog.Pokemons = u.PokemonManager().GetBallPokemonsByType(ballType)
		}
	}

	u.CognitionManager().SetCognitionLog(cLog)
}

// 列传中的pve战斗
func (u *User) AttackTales(srv servicer, monsterGroup uint32, assistMonsters []uint32, clientData []byte) (bool, string, ret.RET) {
	cbp := newCommonBattleParamWithMonsterAndAssists(monsterGroup, common.FORMATION_ID_FI_TALES, clientData, nil, assistMonsters)
	battleReport, retCode := u.commonProcessBattleWithMonsterAndAssists(srv, cbp)
	return battleReport.GetWin(), battleReport.GetId(), retCode
}

func (u *User) AttackTalesElite(srv servicer, monsterGroup uint32, clientData []byte) (bool, string, ret.RET) {
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_TALES, clientData, []uint32{monsterGroup}, NeedSendReport, nil, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// 密林-占矿战斗
func (u *User) AttackFlower(srv servicer, toUser *User, clientData []byte) (bool, string, ret.RET) {
	cbp := newCommonBattleParamWithUser(common.FORMATION_ID_FI_FLOWER_ATTACK,
		common.FORMATION_ID_FI_FLOWER_DEFENSE, clientData, nil, toUser, nil)
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// 密林-抢花战斗
func (u *User) AttackFlowerSnatch(srv servicer, toUser *User, clientData []byte, formationId uint32) (bool, string, ret.RET) {
	cbp := newCommonBattleParamWithUser(common.FORMATION_ID_FI_FLOWER_ATTACK,
		common.FORMATION_ID(formationId), clientData, nil, toUser, nil)
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// AttackFlowerRobot : 密林种花模式下的pve战斗
// param@ id: 怪物组id
func (u *User) AttackFlowerRobot(srv servicer, id uint32, clientData []byte) (bool, string, ret.RET) {
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_FLOWER_ATTACK, clientData, []uint32{id}, NeedSendReport, nil, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// 女武神的pve战斗
func (u *User) AttackGoddess(srv servicer, monsterGroup uint32, assistMonsters []uint32, clientData []byte) (bool, string, ret.RET) {
	cbp := newCommonBattleParamWithMonsterAndAssists(monsterGroup, common.FORMATION_ID_FI_GODDESS_CONTRACT, clientData, nil, assistMonsters)
	battleReport, retCode := u.commonProcessBattleWithMonsterAndAssists(srv, cbp)
	return battleReport.GetWin(), battleReport.GetId(), retCode
}

// 百塔
func (u *User) AttackTowerSeason(srv servicer, info *goxml.TowerSeasonDungeonInfoExt, altAttr *battle.AltAttr,
	slcAltRaisePS []*battle.AltRaisePS, pokemonSkillIds []uint32, clientData []byte, quick bool) (*bt.BattleSummary, ret.RET, uint32) {
	// 修正队伍数量不超过怪物组数量
	formationID := common.FORMATION_ID_FI_TOWER_SEASON
	if formation := u.GetFormation(uint32(formationID)); formation != nil && len(formation.Teams) > len(info.MonsterGroups) {
		newFormation := formation.Clone()
		newFormation.Teams = newFormation.Teams[:len(info.MonsterGroups)] // 考虑到后续拓展到更多队
		u.FormationManager().NewFormation(srv, uint32(formationID), newFormation)
	}

	if len(info.MonsterGroups) != len(slcAltRaisePS) {
		l4g.Errorf("%d Attack TowerSeason error: len(MonsterGroups) %d != len(slcAltRaisePS) %d",
			u.ID(), len(info.MonsterGroups), len(slcAltRaisePS))
		return nil, ret.RET_ERROR, 0
	}

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		if info.MaxRound > 0 {
			battleParam.SetMaxRound(info.MaxRound)
		}
		battleParam.SetAltRaisePS(slcAltRaisePS[index])
		battleParam.SetPokemonSkillIds(pokemonSkillIds)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_TOWER_SEASON, clientData, info.MonsterGroups, !quick, fixFunc, altAttr)
	battleSummary, retCode, defeatTeam := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary, retCode, defeatTeam
}

// 计算怪物组通用修正属性
// 根据玩家最高主线战力，对怪物属性进行修正
// @param uint32 monsterGroupID 怪物组id
// @return map[uint32]map[int]int64 怪物修正后的属性
func (u *User) calcCommonMonsterAltAttr(monsterGroupID uint32) map[uint32]map[int]int64 {
	gInfo := goxml.GetData().MonsterGroupInfoM.Index(monsterGroupID)
	if gInfo == nil {
		l4g.Errorf("user: %d calcCommonMonsterAltAttr monsterGroup is nil. monsterGroup id: %d", u.ID(), monsterGroupID)
		return nil
	}

	ratio := u.MaxPower() * battle.BaseFloatInt / gInfo.Power
	if ratio < goxml.RobotLowestRatio {
		ratio = goxml.RobotLowestRatio
	}
	ratio -= battle.BaseFloatInt
	altAttr := make(map[uint32]map[int]int64)
	for pos, monsterID := range gInfo.Monsters {
		mInfo := goxml.GetData().MonsterInfoM.Index(monsterID)
		if mInfo == nil {
			l4g.Errorf("user: %d calcCommonMonsterAltAttr monsterInfo nil. monsterID: %d", u.ID(), monsterID)
			continue
		}

		monsterAttr := mInfo.GetAttr(goxml.GetData())
		if len(monsterAttr) == 0 {
			l4g.Errorf("user: %d calcCommonMonsterAltAttr monster attr nil. monsterID: %d", u.ID(), monsterID)
			continue
		}

		//修正攻防血
		attr := make(map[int]int64)
		attr[goxml.AttrHp] = monsterAttr[goxml.AttrHp] * ratio / battle.BaseFloatInt
		attr[goxml.AttrAttack] = monsterAttr[goxml.AttrAttack] * ratio / battle.BaseFloatInt
		attr[goxml.AttrPhyDef] = monsterAttr[goxml.AttrPhyDef] * ratio / battle.BaseFloatInt
		attr[goxml.AttrMagDef] = monsterAttr[goxml.AttrMagDef] * ratio / battle.BaseFloatInt
		altAttr[pos] = attr
	}

	return altAttr
}

func (u *User) AttackGuildDungeon(srv servicer, id uint32, altRaisePS *battle.AltRaisePS,
	formationID uint32, clientData []byte) (*bt.BattleSummary, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetMaxRound(GuildDungeonMaxRound)
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID(formationID), clientData, []uint32{id}, NeedSendReport, fixFunc, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackGuildDungeon failed, err: %d", u.ID(), retCode)
		return nil, retCode
	}
	return battleSummary, retCode
}

// 公会boss战
// func (u *User) AttackGSTBoss(srv servicer, id uint32, formationID common.FORMATION_ID, clientData []byte) (*bt.BattleSummary, ret.RET) {
// fixFunc := func(battleParam *battle.ManagerParams, index int) {
// 	battleParam.SetMaxRound(GSTBossMaxRound)
// }
// 	cbp := newCommonBattleParamWithMonster(formationID, clientData, []uint32{id}, NeedSendReport, fixFunc, nil)
// 	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
// if retCode != ret.RET_OK {
// 	l4g.Errorf("%d AttackGSTBoss failed, err: %d", u.ID(), retCode)
// 	return nil, retCode
// }
// 	return battleSummary, retCode
// }

// 切磋
func (u *User) AttackDuel(srv servicer, toUser *User, clientData []byte) (bool, string, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetPvp()
	}
	cbp := newCommonBattleParamWithUser(common.FORMATION_ID_FI_DUNGEON,
		common.FORMATION_ID_FI_DUNGEON, clientData, nil, toUser, fixFunc)
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// 神树争霸战斗
func (u *User) AttackWrestleOpponent(srv servicer, toUser *User, attackFormationId, defenseFormationId common.FORMATION_ID, teamNum int,
	clientData []byte, seasonBuffs []*goxml.WrestleBuff) (bool, string, ret.RET) {
	battleReport := u.NewBattleReport(clientData, attackFormationId)

	var winCount int
	var aLoseTeamIndex int //进攻方战败队伍的index
	var dLoseTeamIndex int //防守方战败队伍的index
	var bats []*battle.Manager
	for index := 0; index < teamNum; index++ {
		altRaisePS := battle.NewAltRaisePS()
		u.calcWrestleExtraSkills(altRaisePS, seasonBuffs, index, true)
		toUser.calcWrestleExtraSkills(altRaisePS, seasonBuffs, index, false)
		battleParams := battle.NewManagerParams(attackFormationId, defenseFormationId, index, index, nil, 0)
		battleParams.SetAltRaisePS(altRaisePS)
		bat := u.NewBattleWithUser(srv, toUser, battleParams)
		if bat == nil {
			l4g.Errorf("user: %d AttackWrestleOpponent defense:%d error", u.ID(), toUser.ID())
			return false, "", ret.RET_ERROR
		}
		defer bat.Free()
		bats = append(bats, bat)
		isWin, retno := DoBattleResult(srv, u, bat, battleReport)
		if retno != ret.RET_OK {
			l4g.Errorf("user: %d AttackWrestleOpponent defense:%d error", u.ID(), toUser.ID())
			return false, "", ret.RET_ERROR
		}

		if isWin {
			winCount++
			dLoseTeamIndex = index
		} else {
			aLoseTeamIndex = index
		}
	}

	var win bool
	if winCount == teamNum {
		win = true
	} else if winCount == 0 {
		win = false
	} else {
		altRaisePS := battle.NewAltRaisePS()
		u.calcWrestleExtraSkills(altRaisePS, seasonBuffs, aLoseTeamIndex, true)
		toUser.calcWrestleExtraSkills(altRaisePS, seasonBuffs, dLoseTeamIndex, false)
		battleParams := battle.NewManagerParams(attackFormationId, defenseFormationId, aLoseTeamIndex, dLoseTeamIndex, nil, 0)
		battleParams.SetAltRaisePS(altRaisePS)
		bat := u.NewBattleWithUser(srv, toUser, battleParams)
		if bat == nil {
			l4g.Errorf("user: %d AttackWrestleOpponent defense:%d error", u.ID(), toUser.ID())
			return false, "", ret.RET_ERROR
		}
		defer bat.Free()
		bats = append(bats, bat)
		isWin, retno := DoBattleResult(srv, u, bat, battleReport)
		if retno != ret.RET_OK {
			l4g.Errorf("user: %d AttackWrestleOpponent defense:%d error", u.ID(), toUser.ID())
			return false, "", ret.RET_ERROR
		}
		win = isWin
	}

	u.ProcessBattleReport(srv, NeedSendReport, win, battleReport, bats)
	return battleReport.GetWin(), battleReport.GetId(), ret.RET_OK
}

// 计算神树争霸羁绊加成技能
// @param *battle.AltRaisePS altRaisePS
// @param []*goxml.WrestleBuff seasonBuffs 量表加成信息
// @param int teamIndex 队伍序号
// @param bool fromAttacker 是否来自于进攻方
func (u *User) calcWrestleExtraSkills(altRaisePS *battle.AltRaisePS, seasonBuffs []*goxml.WrestleBuff,
	teamIndex int, fromAttacker bool) {
	if len(seasonBuffs) == 0 {
		return
	}
	fid := uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)
	if fromAttacker {
		fid = uint32(common.FORMATION_ID_FI_CROSS_ARENA_ATTACK)
	}

	link := u.FormationManager().GetFormationLinkInfo(fid, teamIndex)
	if link == nil {
		l4g.Errorf("user: %d calcWrestleExtraSkills: link is nil. fid:%d, teamIndex:%d",
			u.ID(), fid, teamIndex)
		return
	}

	raisePSMap := make(map[uint32][]uint64)
	for pos, data := range link.HeroLinkInfo {
		if len(data) == 0 {
			continue
		}

		for _, seasonBuff := range seasonBuffs {
			if _, exist := data[seasonBuff.Link]; exist {
				raisePSMap[pos] = append(raisePSMap[pos], seasonBuff.Buff)
			}
		}
	}

	if fromAttacker {
		altRaisePS.AltAttack(raisePSMap)
	} else {
		altRaisePS.AltDefense(raisePSMap)
	}
}

// 神树争霸战斗攻打机器人
func (u *User) AttackWrestleRobot(srv servicer, monsterGroups []uint32, name string,
	clientData []byte, seasonBuffs []*goxml.WrestleBuff) (bool, string, ret.RET) {
	formationID := common.FORMATION_ID_FI_CROSS_ARENA_ATTACK
	battleReport := u.NewBattleReport(clientData, formationID)

	var winCount int
	var aLoseTeamIndex int //进攻方战败队伍的index
	var dLoseTeamIndex int //防守方战败队伍的index
	var bats []*battle.Manager
	for index, id := range monsterGroups {
		altRaisePS := battle.NewAltRaisePS()
		u.calcWrestleExtraSkills(altRaisePS, seasonBuffs, index, true)
		battleParams := battle.NewManagerParams(formationID, 0, index, index, nil, id)
		battleParams.SetAltRaisePS(altRaisePS)
		bat := u.NewBattleWithMonster(srv, battleParams)
		if bat == nil {
			l4g.Errorf("user: %d AttackWrestleRobot error id: %d", u.ID(), id)
			return false, "", ret.RET_ERROR
		}
		defer bat.Free()
		bats = append(bats, bat)
		bat.SetDefenseName(name)
		isWin, retno := DoBattleResult(srv, u, bat, battleReport)
		if retno != ret.RET_OK {
			l4g.Errorf("user: %d AttackWrestleRobot error  monsterGroup:%d, errCode:%v", u.ID(), id, retno)
			return false, "", retno
		}

		if isWin {
			winCount++
			dLoseTeamIndex = index
		} else {
			aLoseTeamIndex = index
		}
	}

	var win bool
	if winCount == len(monsterGroups) {
		win = true
	} else if winCount == 0 {
		win = false
	} else {
		id := monsterGroups[dLoseTeamIndex]
		altRaisePS := battle.NewAltRaisePS()
		u.calcWrestleExtraSkills(altRaisePS, seasonBuffs, aLoseTeamIndex, true)
		battleParams := battle.NewManagerParams(formationID, 0, aLoseTeamIndex, dLoseTeamIndex, nil, id)
		battleParams.SetAltRaisePS(altRaisePS)
		bat := u.NewBattleWithMonster(srv, battleParams)
		if bat == nil {
			l4g.Errorf("user: %d AttackWrestleRobot error id: %d", u.ID(), id)
			return false, "", ret.RET_ERROR
		}
		defer bat.Free()
		bats = append(bats, bat)
		bat.SetDefenseName(name)
		isWin, retno := DoBattleResult(srv, u, bat, battleReport)
		if retno != ret.RET_OK {
			l4g.Errorf("user: %d AttackWrestleRobot error  monsterGroup:%d, errCode:%v", u.ID(), id, retno)
			return false, "", retno
		}
		win = isWin
	}

	u.ProcessBattleReport(srv, NeedSendReport, win, battleReport, bats)
	return battleReport.GetWin(), battleReport.GetId(), ret.RET_OK
}

// 赛季竞技场战斗
func (u *User) SeasonArenaOpponent(srv servicer, toUser *User, teamNum int,
	clientData []byte, attackFormationID, defFormationID, formationMode, showTeam uint32) (bool, string, ret.RET) {
	battleReport := u.NewBattleReport(clientData, common.FORMATION_ID(attackFormationID))

	var winCount int
	var loseCount int
	var bats []*battle.Manager

	cloneIndex := make([]int, 0, teamNum)
	for i := 0; i < teamNum; i++ {
		cloneIndex = append(cloneIndex, i)
	}
	if formationMode == 1 {
		srv.Rand().Shuffle(len(cloneIndex)-int(showTeam), func(i, j int) {
			cloneIndex[i+int(showTeam)], cloneIndex[j+int(showTeam)] = cloneIndex[j+int(showTeam)], cloneIndex[i+int(showTeam)]
		})
	}

	for index := 0; index < len(cloneIndex); index++ {
		battleSwitch := u.seasonArenaOpponentTeamCheck(attackFormationID, defFormationID, toUser, index, cloneIndex)
		var isWin bool
		var retno ret.RET
		switch battleSwitch {
		case 0:
			battleParams := battle.NewManagerParams(common.FORMATION_ID(attackFormationID), common.FORMATION_ID(defFormationID), index, cloneIndex[index], nil, 0)
			bat := u.NewBattleWithUser(srv, toUser, battleParams)
			if bat == nil {
				l4g.Errorf("user: %d AttackWrestleOpponent defense:%d error", u.ID(), toUser.ID())
				return false, "", ret.RET_ERROR
			}
			defer bat.Free()
			bats = append(bats, bat)
			isWin, retno = DoBattleResult(srv, u, bat, battleReport)
			if retno != ret.RET_OK {
				l4g.Errorf("user: %d AttackWrestleOpponent defense:%d error", u.ID(), toUser.ID())
				return false, "", ret.RET_ERROR
			}
		case 1:
			isWin = true
		}

		if isWin {
			winCount++
		} else {
			loseCount++
		}
		if winCount >= teamNum/2+1 || loseCount >= teamNum/2+1 {
			break
		}
	}

	var win bool
	if winCount >= teamNum/2+1 {
		win = true
	} else {
		win = false
	}

	u.ProcessBattleReport(srv, NeedSendReport, win, battleReport, bats)
	return battleReport.GetWin(), battleReport.GetId(), ret.RET_OK
}

func (u *User) seasonArenaOpponentTeamCheck(attackFormationID, defFormationID uint32, toUser *User, index int, cloneIndex []int) uint32 {
	attackExist := u.IsTeamExist(attackFormationID, index)
	defExist := toUser.IsTeamExist(defFormationID, int(cloneIndex[index]))
	if attackExist && defExist {
		return 0
	} else if attackExist && !defExist {
		return 1
	} else if !attackExist && defExist {
		return 2 //nolint:mnd
	} else {
		return 3 //nolint:mnd
	}
}

// 赛季竞技场攻打机器人
func (u *User) SeasonArenaRobot(srv servicer, monsterGroups []uint32, name string,
	clientData []byte, formationID uint32, formationMode, botShowLv, showTeam uint32) (bool, string, ret.RET) {
	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user: %d AttackSeasonArenaRobot formationInfo is nil. formationID: %d", u.ID(), formationID)
		return false, "", ret.RET_SYSTEM_DATA_ERROR
	}

	cloneMonsterGroups := make([]uint32, 0, int(formationInfo.TeamNum))
	for i := 0; i < int(formationInfo.TeamNum); i++ {
		cloneMonsterGroups = append(cloneMonsterGroups, monsterGroups[i])
	}

	if formationMode == 1 {
		srv.Rand().Shuffle(len(cloneMonsterGroups)-int(showTeam), func(i, j int) {
			cloneMonsterGroups[i+int(showTeam)], cloneMonsterGroups[j+int(showTeam)] = cloneMonsterGroups[j+int(showTeam)], cloneMonsterGroups[i+int(showTeam)]
		})
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID(formationID), clientData, cloneMonsterGroups, NeedSendReport, nil, nil)
	cbp.winType = BattleWinTypeHalfWin
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, &botShowData{
		name:     name,
		seasonLv: botShowLv,
	})
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

// PVP战斗测试
func (u *User) AttackPVPTest(srv servicer, toUser *User) (bool, string, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetPvp()
	}
	cbp := newCommonBattleParamWithUser(common.FORMATION_ID_FI_DUNGEON, common.FORMATION_ID_FI_DUNGEON, nil, nil, toUser, fixFunc)
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	return battleSummary.GetWin(), battleSummary.GetId(), retCode
}

func (u *User) AttackTowerstar(srv servicer, info *goxml.TowerstarDungeonInfo,
	clientData []byte, quick bool, formationId common.FORMATION_ID) (bool, string, uint32, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetNeedMoreResult()
	}
	cbp := newCommonBattleParamWithMonster(formationId, clientData, []uint32{info.MonsterGroup}, !quick, fixFunc, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackTowerstar failed, err: %d", u.ID(), retCode)
		return false, "", 0, retCode
	}
	starInfo := calTowerstarStarInfo(battleSummary.Reports[0], info)
	return battleSummary.GetWin(), battleSummary.GetId(), starInfo, retCode
}

func calTowerstarStarInfo(report *bt.BattleSummaryReport, towerstarDungeonInfo *goxml.TowerstarDungeonInfo) uint32 {
	if !report.Win {
		return 0
	}
	var starInfo uint32 = 0
	towerstarConditionInfo := goxml.GetData().TowerstarConditionInfoM.Index(towerstarDungeonInfo.StarUnlock1)
	if TowerstarCheckStarFuncs[towerstarConditionInfo.Type](report, towerstarConditionInfo) {
		starInfo |= (1 << 0)
	}
	towerstarConditionInfo = goxml.GetData().TowerstarConditionInfoM.Index(towerstarDungeonInfo.StarUnlock2)
	if TowerstarCheckStarFuncs[towerstarConditionInfo.Type](report, towerstarConditionInfo) {
		starInfo |= (1 << 1)
	}
	towerstarConditionInfo = goxml.GetData().TowerstarConditionInfoM.Index(towerstarDungeonInfo.StarUnlock3)
	if TowerstarCheckStarFuncs[towerstarConditionInfo.Type](report, towerstarConditionInfo) {
		starInfo |= (1 << 2) //nolint:mnd
	}

	return starInfo
}

type TowestarCheckStarFunc func(*bt.BattleSummaryReport, *goxml.TowerstarConditionInfo) bool

var TowerstarCheckStarFuncs = map[uint32]TowestarCheckStarFunc{
	1:    towerstarWin,
	2:    towerstarRoundLessEqualX,
	3:    towerstarAliveMoreEqualXAndHpPctMoreEqualX,
	4:    towerstarAliveMoreEqualX,
	1001: towerstarXRaceMoreEqualX,
	1002: towerstarXRaceLessEqualX,
	1003: towerstarXJobMoreEqualX,
	1004: towerstarXJobLessEqualX,
	2001: towerstarLinksMoreEqualX,
	2002: towerstarXLinkNumMoreEqualX,
	2003: towerstarLinksLessEqualX,
	3001: towerstarLink1orLink2MoreEqualX,
}

func towerstarWin(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	return report.Win
}
func towerstarRoundLessEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	return report.Round <= conditonInfo.Value1
}
func towerstarAliveMoreEqualXAndHpPctMoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	if len(report.AttackersFinal) < int(conditonInfo.Value1) {
		return false
	}
	for _, mem := range report.AttackersFinal {
		if float64(mem.NowHp)/float64(mem.MaxHp)*goxml.BaseFloat < float64(conditonInfo.Value2) {
			return false
		}
	}
	return true
}
func towerstarAliveMoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	return len(report.AttackersFinal) >= int(conditonInfo.Value1)
}
func towerstarXRaceMoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	value := 0
	conditonSet := make(map[uint32]struct{})
	if conditonInfo.Value1 == 6 { //nolint:mnd
		conditonSet[goxml.RaceProtoss] = struct{}{}
		conditonSet[goxml.RaceDemon] = struct{}{}
	} else {
		conditonSet[conditonInfo.Value1] = struct{}{}
	}
	for _, mem := range report.Attackers {
		heroInfo := goxml.GetData().HeroInfoM.Index(mem.SysId)
		if _, exist := conditonSet[heroInfo.Race]; exist {
			value++
			if value >= int(conditonInfo.Value2) {
				return true
			}
		}
	}
	return false
}
func towerstarXRaceLessEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	value := 0
	conditonSet := make(map[uint32]struct{})
	if conditonInfo.Value1 == 6 { //nolint:mnd
		conditonSet[goxml.RaceProtoss] = struct{}{}
		conditonSet[goxml.RaceDemon] = struct{}{}
	} else {
		conditonSet[conditonInfo.Value1] = struct{}{}
	}
	for _, mem := range report.Attackers {
		heroInfo := goxml.GetData().HeroInfoM.Index(mem.SysId)
		if _, exist := conditonSet[heroInfo.Race]; exist {
			value++
			if value > int(conditonInfo.Value2) {
				return false
			}
		}
	}
	return true
}
func towerstarXJobMoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	value := 0
	for _, mem := range report.Attackers {
		heroInfo := goxml.GetData().HeroInfoM.Index(mem.SysId)
		if heroInfo.Job == conditonInfo.Value1 {
			value++
			if value >= int(conditonInfo.Value2) {
				return true
			}
		}
	}
	return false
}
func towerstarXJobLessEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	value := 0
	for _, mem := range report.Attackers {
		heroInfo := goxml.GetData().HeroInfoM.Index(mem.SysId)
		if heroInfo.Job == conditonInfo.Value1 {
			value++
			if value > int(conditonInfo.Value2) {
				return false
			}
		}
	}
	return true
}
func towerstarLinksMoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	return report.ExtraResult.ActiveLinkNum >= conditonInfo.Value1
}
func towerstarXLinkNumMoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	for linkId, num := range report.ExtraResult.LinkInfo {
		if linkId == conditonInfo.Value1 && num >= conditonInfo.Value2 {
			return true
		}
	}
	return false
}
func towerstarLinksLessEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	return report.ExtraResult.ActiveLinkNum <= conditonInfo.Value1
}
func towerstarLink1orLink2MoreEqualX(report *bt.BattleSummaryReport, conditonInfo *goxml.TowerstarConditionInfo) bool {
	var totalNum uint32
	for linkId, num := range report.ExtraResult.LinkInfo {
		if linkId == conditonInfo.Value1 || linkId == conditonInfo.Value2 {
			totalNum += num
		}
	}
	return totalNum >= conditonInfo.Value3
}

func (u *User) AttackWorldBoss(srv servicer, id uint32, formationID uint32,
	altRaisePS *battle.AltRaisePS, clientData []byte) (*bt.BattleSummary, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID(formationID), clientData, []uint32{id}, NeedSendReport, fixFunc, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil, u.WorldBoss().GetLevel())
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackWorldBoss failed, err: %d", u.ID(), retCode)
		return nil, retCode
	}
	return battleSummary, retCode
}

// AttackDisorderLand 失序空间的战斗
func (u *User) AttackDisorderLand(srv servicer, id uint32, formationID common.FORMATION_ID, altAttr *battle.AltAttr,
	altRaisePS *battle.AltRaisePS, clientData []byte) (bool, string, uint32) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(formationID, clientData, []uint32{id}, NeedSendReport, fixFunc, altAttr)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary.GetWin(), battleSummary.GetId(), uint32(retCode)
}

func (u *User) NewBattleWithUser(srv servicer, toUser *User, battleParams *battle.ManagerParams) *battle.Manager {
	CheckBattleDebug(srv)
	//处理阵法相关战斗参数
	riteFlag := u.SetRiteBattleParams(BattlePVP, battleParams, toUser)
	if !riteFlag {
		l4g.Errorf("%d NewBattleWithUser.SetRiteBattleParams failed %d", u.ID(), battleParams.GetDefenseFID())
		return nil
	}

	attack, attackArtifactPSMap := u.NewUserTeam(battleParams.GetAttackFID(), battleParams.GetAttackTeamIndex())
	if attack == nil {
		l4g.Errorf("%d pvp attack team error", u.ID())
		return nil
	}

	defense, defenseArtifactPSMap := toUser.NewUserTeam(battleParams.GetDefenseFID(), battleParams.GetDefenseTeamIndex())
	if defense == nil {
		l4g.Errorf("%d pvp defense team error", toUser.ID())
		return nil
	}

	// 添加被动技能和属性
	u.AddRaisePSAndAttr(battleParams, true, attackArtifactPSMap, false)
	toUser.AddRaisePSAndAttr(battleParams, false, defenseArtifactPSMap, false)

	return battle.NewManager(attack, defense, battleParams)
}

// 添加被动技能和属性
// @param battleParams *battle.ManagerParams 战斗参数
// @param fromAttack bool 是否来自于进攻方
// @param artifactPSMap map[uint32][]uint64 神器被动技能
// @param fromBattleCmd bool 是否来自跑测工具
func (u *User) AddRaisePSAndAttr(battleParams *battle.ManagerParams, fromAttack bool,
	artifactPSMap map[uint32][]uint64, fromBattleCmd bool) {
	if fromBattleCmd {
		u.FormationManager().u = u
	}

	// 添加神器被动技能
	u.artifactAdd(artifactPSMap, battleParams.AltRaisePS, fromAttack)
	// 添加全局养成被动技能
	u.globalAdd(battleParams.AltRaisePS, fromAttack)
	// 添加阵容带来的加成
	u.riteAdd(battleParams, fromAttack)
	// 处理赛季养成相关战斗数据
	u.seasonTrain(battleParams, fromAttack)
	// 遗物的战斗加成
	u.remainRaisePS(battleParams, fromAttack)
	// 处理赛季加成
	u.CalcSeasonAdd(battleParams, fromAttack)
	// 天赋树
	u.talentTreeRaisePSAndAttr(battleParams, fromAttack)
	// 赛季装备
	u.seasonJewelryRaisePS(battleParams, fromAttack)
	// 宠物
	u.pokemonRaisePS(battleParams, fromAttack)
}

func (u *User) NewBattleWithMonster(srv servicer, battleParams *battle.ManagerParams, extra ...uint32) *battle.Manager {
	CheckBattleDebug(srv)
	//处理阵法相关战斗参数
	riteFlag := u.SetRiteBattleParams(BattlePVE, battleParams, nil)
	if !riteFlag {
		l4g.Errorf("%d NewBattleWithMonster.SetRiteBattleParams failed %d", u.ID(), battleParams.MonsterGroupId)
		return nil
	}

	attack, attackArtifactPSMap := u.NewUserTeam(battleParams.GetAttackFID(), battleParams.GetAttackTeamIndex(), extra...)
	if attack == nil {
		l4g.Errorf("%d pve error attack team error %d", u.ID(), battleParams.MonsterGroupId)
		return nil
	}

	defenseRiteRestrictState := battle.GetOpponentRiteRestrictState(battleParams.GetRiteRestrictState())
	defense, defenseRaisePSMap := battle.NewMonsterGroupTeam(battleParams.MonsterGroupId,
		defenseRiteRestrictState, battleParams.GetDefenseTeamIndex())
	if defense == nil {
		l4g.Errorf("%d pve error defense team error %d", u.ID(), battleParams.MonsterGroupId)
		return nil
	}
	setAltPassiveSkill(battleParams.AltRaisePS, defenseRaisePSMap, false)

	// 添加被动技能和属性
	u.AddRaisePSAndAttr(battleParams, true, attackArtifactPSMap, false)
	return battle.NewManager(attack, defense, battleParams)
}

// 这个攻击方使用的也是monster,外层没有处理联结技能
func (u *User) NewBattleWithMonsterAndAssists(srv servicer, assistMonsters []uint32, battleParams *battle.ManagerParams) *battle.Manager {
	CheckBattleDebug(srv)
	//处理阵法相关战斗参数
	riteFlag := u.SetRiteBattleParams(BattleEVE, battleParams, nil)
	if !riteFlag {
		l4g.Errorf("%d NewBattleWithMonsterAndAssists.SetRiteBattleParams failed %d", u.ID(), battleParams.MonsterGroupId)
		return nil
	}

	attack := u.NewTeamByAssistMonsters(assistMonsters, battleParams.GetAttackTeamIndex())
	if attack == nil {
		l4g.Errorf("%d pve error attack team error %d", u.ID(), battleParams.MonsterGroupId)
		return nil
	}

	defenseRiteRestrictState := battle.GetOpponentRiteRestrictState(battleParams.GetRiteRestrictState())
	defense, defenseRaisePSMap := battle.NewMonsterGroupTeam(battleParams.MonsterGroupId,
		defenseRiteRestrictState, battleParams.GetDefenseTeamIndex())
	if defense == nil {
		l4g.Errorf("%d pve error defense team error %d", u.ID(), battleParams.MonsterGroupId)
		return nil
	}
	setAltPassiveSkill(battleParams.AltRaisePS, defenseRaisePSMap, false)

	// 添加被动技能和属性
	u.AddRaisePSAndAttr(battleParams, true, nil, false)
	return battle.NewManager(attack, defense, battleParams)
}

// 处理阵法相关战斗参数
// @param typ uint32 战斗类型
// @param params *battle.ManagerParams 战斗参数
// @param toUser *User 防守方用户
// @return bool 是否正常执行
func (u *User) SetRiteBattleParams(typ uint32, params *battle.ManagerParams, toUser *User) bool {
	if typ != BattlePVE && typ != BattlePVP && typ != BattleEVE {
		l4g.Errorf("SetRiteBattleParams, uid:%d type err: %d", u.ID(), typ)
		return false
	}

	if typ == BattlePVP {
		if params.GetDefenseFID() == 0 || toUser == nil {
			l4g.Errorf("SetRiteBattleParams, uid:%d BattlePVP no defenseFID or toUser is nil:%t", u.ID(), toUser == nil)
			return false
		}
	} else {
		if params.MonsterGroupId == 0 {
			l4g.Errorf("SetRiteBattleParams, uid:%d BattlePVE or BattleEVE no monsterGroup", u.ID())
			return false
		}
	}

	var (
		attackRiteId, attackRiteRare, defenseRiteId, defenseRiteRare uint32
		attackRiteGrids, defenseRiteGrids                            []*bt.RiteGrid
	)

	// 进攻方
	if typ == BattlePVE || typ == BattlePVP {
		attackRiteId, attackRiteRare, attackRiteGrids = u.GetRiteForBattle(params.GetAttackFID(), params.GetAttackTeamIndex())
	}
	// 防守方
	if typ == BattlePVP {
		defenseRiteId, defenseRiteRare, defenseRiteGrids = toUser.GetRiteForBattle(params.GetDefenseFID(), params.GetDefenseTeamIndex())
	} else {
		defenseRiteId, defenseRiteRare, defenseRiteGrids = goxml.GetData().RiteMonsterGroupInfoM.GetRiteForBattle(params.MonsterGroupId)
	}

	riteRestrictState := battle.GetRiteRestrictState(attackRiteId, defenseRiteId, attackRiteRare, defenseRiteRare)
	params.SetAttackRite(attackRiteId, attackRiteRare, attackRiteGrids)
	params.SetDefenseRite(defenseRiteId, defenseRiteRare, defenseRiteGrids)
	params.SetRiteRestrictState(riteRestrictState)
	return true
}

// 添加额外的被动技能
// @param altRaisePS *battle.AltRaisePS
// @param raisePSMap map[uint32][]uint64
// @param fromAttack bool 是否来自于攻击方
func setAltPassiveSkill(altRaisePS *battle.AltRaisePS, raisePSMap map[uint32][]uint64, fromAttack bool) {
	if len(raisePSMap) == 0 {
		return
	}

	if fromAttack {
		altRaisePS.AltAttack(raisePSMap)
	} else {
		altRaisePS.AltDefense(raisePSMap)
	}
}

// 添加额外的属性
// @param altAttr *battle.AltAttr
// @param attrMap map[uint32]map[int]int64
// @param fromAttack bool 是否来自于攻击方
func setAltAttr(altAttr *battle.AltAttr, attrMap map[uint32]map[int]int64, fromAttack bool) {
	if len(attrMap) == 0 {
		return
	}

	if fromAttack {
		altAttr.SetAttack(attrMap)
	} else {
		altAttr.SetDefense(attrMap)
	}
}

// 根据战斗参数获取队伍数据
// @param battleParams *battle.ManagerParams
// @param fromAttack bool 是否来自于攻击方
// @return formationID uint32 阵容id
// @return teamIndex int 队伍序号
func getTeamByBattleParams(battleParams *battle.ManagerParams, fromAttack bool) (formationID uint32, teamIndex int) {
	if fromAttack {
		formationID = battleParams.GetAttackFID()
		teamIndex = battleParams.GetAttackTeamIndex()
	} else {
		formationID = battleParams.GetDefenseFID()
		teamIndex = battleParams.GetDefenseTeamIndex()
	}
	return
}

// 添加神器被动技能
func (u *User) artifactAdd(raisePSMap map[uint32][]uint64, altRaisePS *battle.AltRaisePS, fromAttack bool) {
	setAltPassiveSkill(altRaisePS, raisePSMap, fromAttack)
}

// 添加全局养成被动技能
func (u *User) globalAdd(altRaisePS *battle.AltRaisePS, fromAttack bool) {
	raisePSMap := make(map[uint32][]uint64)
	raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], u.GetGlobalRaisePs()...)
	setAltPassiveSkill(altRaisePS, raisePSMap, fromAttack)
}

// 添加阵容带来的加成
func (u *User) riteAdd(battleParams *battle.ManagerParams, fromAttack bool) {
	formationID, teamIndex := getTeamByBattleParams(battleParams, fromAttack)
	formationTeam := u.GetFormationTeam(formationID, teamIndex)
	if formationTeam == nil || len(formationTeam.Info) == 0 {
		l4g.Errorf("%d riteAdd error %d:%d", u.ID(), formationID, teamIndex)
		return
	}

	riteRestrictState := battleParams.GetRiteRestrictState()
	if !fromAttack {
		riteRestrictState = battle.GetOpponentRiteRestrictState(riteRestrictState)
	}

	riteRaisePSMap, riteAttrs := u.GetBattleRiteRaisePSsAndAttrsMap(formationTeam, riteRestrictState)
	setAltPassiveSkill(battleParams.AltRaisePS, riteRaisePSMap, fromAttack)
	setAltAttr(battleParams.AltAttr, riteAttrs, fromAttack)
}

// 处理赛季养成相关战斗加成
// @param battleParams *battle.ManagerParams
// @param fromAttack bool 是否来自于攻击方
func (u *User) seasonTrain(battleParams *battle.ManagerParams, fromAttack bool) {
	/*
		// 根据战斗参数获取队伍数据
		//
			formationID, teamIndex := getTeamByBattleParams(battleParams, fromAttack)
			seasonTrain := u.GetMonumentForBattle(formationID, teamIndex)
			if seasonTrain != nil {
				if fromAttack {
					battleParams.SetAttackSeasonTrain(seasonTrain.MonumentID, seasonTrain.MonumentLevel)
				} else {
					battleParams.SetDefenseSeasonTrain(seasonTrain.MonumentID, seasonTrain.MonumentLevel)
				}
				setAltPassiveSkill(battleParams.AltRaisePS, seasonTrain.RaisePSMap, fromAttack)
				setAltAttr(battleParams.AltAttr, seasonTrain.Attrs, fromAttack)
			}
	*/

	activedHeroes := u.SeasonLink().FlushActivedHeros()
	if len(activedHeroes) > 0 {
		if fromAttack {
			battleParams.SetAttackSeasonLinkActivedHeroes(activedHeroes)
		} else {
			battleParams.SetDefenseSeasonLinkActivedHeroes(activedHeroes)
		}
	}
}

// 遗物战斗加成
// @param battleParams *battle.ManagerParams
// @param fromAttack bool 是否来自于攻击方
func (u *User) remainRaisePS(battleParams *battle.ManagerParams, fromAttack bool) {
	// 根据战斗参数获取队伍数据
	formationID, teamIndex := getTeamByBattleParams(battleParams, fromAttack)
	remainBookRaisePSsMap := u.RemainM().RemainBookRaisePSs()
	setAltPassiveSkill(battleParams.AltRaisePS, remainBookRaisePSsMap, fromAttack)

	formationTeam := u.GetFormationTeam(formationID, teamIndex)
	if formationTeam == nil || len(formationTeam.RemainInfo) == 0 {
		return
	}
	remainRaisePSsMap, remainStars := u.RemainM().RemainRaisePSs(formationID, formationTeam.RemainInfo)
	setAltPassiveSkill(battleParams.AltRaisePS, remainRaisePSsMap, fromAttack)

	if fromAttack {
		battleParams.SetAttackRemain(remainStars)
	} else {
		battleParams.SetDefenseRemain(remainStars)
	}
}

func (u *User) talentTreeRaisePSAndAttr(battleParams *battle.ManagerParams, fromAttack bool) {
	// 根据战斗参数获取队伍数据
	formationID, teamIndex := getTeamByBattleParams(battleParams, fromAttack)
	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d talentTreeRaisePSAndAttr: no formationInfo, formationID:%d", u.ID(), formationID)
		return
	}
	now := time.Now().Unix()
	if !goxml.IsSeasonFunctionOpen(goxml.GetData(), now, formationInfo.FunctionId) {
		return
	}

	formationLinkInfo := u.FormationManager().GetFormationLinkInfo(formationID, teamIndex)
	if formationLinkInfo == nil {
		l4g.Errorf("user %d talentTreeRaisePSAndAttr: no formationLinkInfo, formationID:%d teamIndex:%d", u.ID(), formationID, teamIndex)
		return
	}

	talentTreePSsMap := u.TalentTree().RaisePSs(formationInfo.FunctionId, formationLinkInfo.ActivedLinks)
	setAltPassiveSkill(battleParams.AltRaisePS, talentTreePSsMap, fromAttack)

	talentTreeAttrMap := u.TalentTree().AddAttr()
	setAltAttr(battleParams.AltAttr, talentTreeAttrMap, fromAttack)

	cultivate := u.TalentTree().FlushCultivate()
	if fromAttack {
		battleParams.SetAttackTalentTree(cultivate.Levels)
	} else {
		battleParams.SetDefenseTalentTree(cultivate.Levels)
	}
}

// 赛季装备被动技能
func (u *User) seasonJewelryRaisePS(battleParams *battle.ManagerParams, fromAttack bool) {
	// 根据战斗参数获取队伍数据
	formationID, teamIndex := getTeamByBattleParams(battleParams, fromAttack)
	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d seasonJewelryRaisePS: no formationInfo, formationID:%d", u.ID(), formationID)
		return
	}

	// 是否生效赛季加成
	if !goxml.IsSeasonFunctionOpen(goxml.GetData(), time.Now().Unix(), formationInfo.FunctionId) {
		return
	}

	seasonJewelryPSsMap := u.SeasonJewelryManager().RaisePSs(formationID, teamIndex)
	setAltPassiveSkill(battleParams.AltRaisePS, seasonJewelryPSsMap, fromAttack)
}

func (u *User) GetRiteForBattle(formationID uint32, teamIndex int) (uint32, uint32, []*bt.RiteGrid) {
	formationTeam := u.GetFormationTeam(formationID, teamIndex)
	if formationTeam == nil {
		l4g.Errorf("%d get formation team failed. formationID:%d teamIndex:%d", u.ID(), formationID, teamIndex)
		return 0, 0, nil
	}
	if formationTeam.RiteInfo == nil || formationTeam.RiteInfo.RiteId == 0 {
		return 0, 0, nil
	}
	rite := u.RiteManager().GetRite(formationTeam.RiteInfo.RiteId)
	if rite == nil {
		l4g.Errorf("%d get formation team rite failed. formationID:%d teamIndex:%d riteId:%d",
			u.ID(), formationID, teamIndex, formationTeam.RiteInfo.RiteId)
		return 0, 0, nil
	}
	riteGrids := rite.GetGrids()
	btRiteGrids := make([]*bt.RiteGrid, 0, len(riteGrids))
	for _, riteGrid := range riteGrids {
		if riteGrid == nil {
			continue
		}
		var powerId uint32
		for _, ritePower := range formationTeam.RiteInfo.RitePowers {
			if ritePower != nil && ritePower.Pos == riteGrid.Pos {
				powerId = ritePower.PowerId
				break
			}
		}
		btRiteGrids = append(btRiteGrids, &bt.RiteGrid{
			Pos:     riteGrid.Pos,
			MarkId:  riteGrid.MarkId,
			PowerId: powerId,
		})
	}
	return formationTeam.RiteInfo.RiteId, rite.GetRare(), btRiteGrids
}

func (u *User) pokemonRaisePS(battleParams *battle.ManagerParams, fromAttack bool) {
	// 根据战斗参数获取队伍数据
	formationID, _ := getTeamByBattleParams(battleParams, fromAttack)
	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d pokemonRaisePS: no formationInfo, formationID:%d", u.ID(), formationID)
		return
	}
	raisePSMap := u.PokemonManager().RaisePSs(battleParams)
	setAltPassiveSkill(battleParams.AltRaisePS, raisePSMap, fromAttack)
}

func CheckBattleDebug(srv servicer) {
	if srv.BattleDebug() {
		battle.OpenDebugAttr()
		battle.OpenBattleDebug()
		//battle.SetStrictPool(true)
	}
}

func DoBattleResult(srv servicer, user *User, b *battle.Manager, battleReport *bt.MultipleTeamsReport) (bool, ret.RET) {
	b.Run()
	if b.GetRet() != ret.RET_OK {
		return false, b.GetRet()
	}
	battleReport.Reports = append(battleReport.Reports, b.GetReport())
	return b.GetReport().ReportInfo.Win, b.GetRet()
}

func (u *User) MarshalBattleReport(report *bt.MultipleTeamsReport) []byte {
	if report == nil {
		l4g.Errorf("MarshalBattleReport nil error %d", u.ID())
		return nil
	}
	data, err := proto.Marshal(report)
	if err != nil {
		l4g.Errorf("MarshalBattleReport error %d %s %v", u.ID(), report.Id, err)
		return nil
	}
	return data
}

func (u *User) SendBattleReport(id string, data []byte) {
	// 检查压缩
	var compress bool
	compress, data = checkCompress(data)

	total := (len(data) + BattleReportSegmentSize - 1) / BattleReportSegmentSize
	for i := 0; i < total; i++ {
		start := i * BattleReportSegmentSize
		end := start + BattleReportSegmentSize
		if end > len(data) {
			end = len(data)
		}
		msg := &cl.L2C_BattleReport{
			Ret:      uint32(ret.RET_OK),
			Id:       id,
			Total:    uint32(total),
			Order:    uint32(i),
			Report:   data[start:end],
			Compress: compress,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_BattleReport, msg)
	}
}

// 检查压缩战报
// @param buf []byte 序列化战报数据
// @return bool 是否压缩
// @return []byte 处理后的数据
func checkCompress(buf []byte) (bool, []byte) {
	if len(buf) < BattleReportCompressBytes {
		l4g.Debugf("checkCompress, not compress, len %d", len(buf))
		return false, buf
	}

	wbuf := make([]byte, snappy.MaxEncodedLen(len(buf)))
	ret := snappy.Encode(wbuf, buf)
	l4g.Debugf("checkCompress, compress, before:%d, after:%d", len(buf), len(ret))
	return true, wbuf[:len(ret)]
}

// func (u *User)
func calStarNum(battleReport *bt.BattleSummary, levelInfo *goxml.TrialInfoExt) uint32 {
	report := battleReport.Reports[0]
	star := uint32(0)
	//nolint:mnd
	for i := 0; i+1 < len(levelInfo.Targets); i = i + 2 { // 下面要读i+1，所以判断i+1
		switch levelInfo.Targets[i] {
		case TrialTargetNone:
			if battleReport.Win {
				star++
			}
		case TrialTargetOne:
			if report.ExtraResult.KillNum >= levelInfo.Targets[i+1] {
				star++
			}
		case TrialTargetTwo:
			if battleReport.Win && report.ExtraResult.Round <= levelInfo.Targets[i+1] {
				star++
			}
		case TrialTargetThree:
			if report.ExtraResult.DeadNum <= levelInfo.Targets[i+1] {
				star++
			}
		case TrialTargetFour:
			if report.ExtraResult.LeftHpPer >= levelInfo.Targets[i+1] {
				star++
			}
		case TrialTargetFive:
			if report.ExtraResult.AliveNum >= levelInfo.Targets[i+1] {
				star++
			}
		case TrialTargetSix:
			if report.ExtraResult.MaxRoundHurt >= uint64(levelInfo.Targets[i+1]) {
				star++
			}
		}
	}

	return star
}

// 构造战斗结果简要信息
func makeBattleSummary(battleReport *bt.MultipleTeamsReport) *bt.BattleSummary {
	battleSummary := &bt.BattleSummary{
		Id:            battleReport.Id,
		Win:           battleReport.Win,
		ReportVersion: battleReport.ReportVersion,
	}

	cloneMember := func(oldMembers []*bt.Member, newMembers []*bt.BattleSummaryMember) {
		for index, member := range oldMembers {
			newMembers[index] = &bt.BattleSummaryMember{
				UniterId:  member.Id,
				UniqId:    member.Info.UniqId,
				SysId:     member.Info.SysId,
				Pos:       member.Info.Pos,
				NowHp:     member.NowHp,
				MaxHp:     member.MaxHp,
				ShowMaxHp: member.ShowMaxHp,
				Star:      member.Info.Star,
				Level:     member.Info.Level,
			}
		}
	}

	for _, teamReport := range battleReport.Reports {
		newTeamReport := &bt.BattleSummaryReport{
			Win:          teamReport.ReportInfo.Win,
			Round:        uint32(len(teamReport.Rounds) - 1),
			TotalHurt:    teamReport.ReportInfo.TotalHurtShow,
			ExtraResult:  teamReport.ReportInfo.ExtraResult.Clone(),
			AttackPower:  teamReport.ReportInfo.AttackPower,
			DefensePower: teamReport.ReportInfo.DefensePower,
		}
		newTeamReport.Attackers = make([]*bt.BattleSummaryMember, len(teamReport.Attackers))
		cloneMember(teamReport.Attackers, newTeamReport.Attackers)
		newTeamReport.AttackersFinal = make([]*bt.BattleSummaryMember, len(teamReport.AttackersFinal))
		cloneMember(teamReport.AttackersFinal, newTeamReport.AttackersFinal)

		newTeamReport.Defensers = make([]*bt.BattleSummaryMember, len(teamReport.Defensers))
		cloneMember(teamReport.Defensers, newTeamReport.Defensers)
		newTeamReport.DefensersFinal = make([]*bt.BattleSummaryMember, len(teamReport.DefensersFinal))
		cloneMember(teamReport.DefensersFinal, newTeamReport.DefensersFinal)

		battleSummary.Reports = append(battleSummary.Reports, newTeamReport)
	}
	return battleSummary
}

const (
	NeedSendReport   bool = true
	NoNeedSendReport bool = false
)

func (u *User) NewBattleReport(clientData []byte, formationID common.FORMATION_ID) *bt.MultipleTeamsReport {
	report := &bt.MultipleTeamsReport{
		ClientData:    clientData,
		Pvp:           goxml.GetData().FormationInfoM.IsPVPMode(uint32(formationID)),
		FormationId:   uint32(formationID),
		ReportVersion: ReportVersion,
	}
	return report
}

func (u *User) ProcessBattleReport(srv servicer, send, win bool, report *bt.MultipleTeamsReport, bats []*battle.Manager) []byte {
	report.Win = win
	report.Id = u.genBattleKey(srv, report)
	report.Version = goxml.GetData().ServerInfoM.Version
	for index, bat := range bats {
		u.UpdateFightEvent(srv, bat, report)
		u.LogBattleReportDetails(srv, index, bat, report)
	}
	reportByte := u.MarshalBattleReport(report)
	if send {
		u.SendBattleReport(report.Id, reportByte)
	}
	u.SendBattleReportToOss(srv, report, reportByte)
	return reportByte
}

type ReportLogInfo struct {
	Win    bool   //是否赢
	FuncID uint32 //玩法id
	ID     string //战报id
}

func (u *User) SendBattleReportToOss(srv servicer, report *bt.MultipleTeamsReport, data []byte) {
	if u.Level() < goxml.GetData().ConfigInfoM.OssOpenLv {
		return
	}
	msg := &p2l.L2O_CreateBattleReport{
		ObjectKey: report.Id,
		Data:      data,
	}
	var funcID common.FUNCID
	formationConfigInfo := goxml.GetData().FormationInfoM.Index(report.FormationId)
	if formationConfigInfo == nil {
		l4g.Errorf("FormationInfoM cant find formationId: %d", report.FormationId)
		funcID = common.FUNCID_MODULE_DUNGEON
	} else {
		funcID = common.FUNCID(formationConfigInfo.FunctionId)
	}
	u.LogBattleReport(srv, &ReportLogInfo{
		Win:    report.Win,
		ID:     report.Id,
		FuncID: uint32(funcID),
	})
	srv.SendCmdToOSS(uint32(p2l.ID_MSG_L2O_CreateBattleReport), u.ID(), msg, funcID)
}

func (u *User) genBattleKey(srv servicer, report *bt.MultipleTeamsReport) string {
	uniqueID := srv.CreateUniqueID()
	formationConfigInfo := goxml.GetData().FormationInfoM.Index(report.FormationId)
	if formationConfigInfo == nil {
		panic(fmt.Sprintf("cant find formationID %d", report.FormationId))
	}
	return srv.OSSM().GetOssKey(uniqueID, common.FUNCID(formationConfigInfo.FunctionId))
}

func (u *User) AttackSeasonDungeon(srv servicer, info *goxml.SeasonDungeonInfoExt,
	clientData []byte) (*bt.BattleSummary, ret.RET) {
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		cret, altRaisePS := u.seasonDungeonAltRaisePS(info, index)
		if cret != uint32(ret.RET_OK) {
			l4g.Errorf("AttackSeasonDungeon %d add AtlRaise Ps error id: %d", u.ID(), cret)
			return
		}
		if info.MaxRound > 0 {
			battleParam.SetMaxRound(info.MaxRound)
		}
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_SEASON_DUNGEON, clientData, info.MonsterGroups, NeedSendReport, fixFunc, nil)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary, retCode
}

func (u *User) seasonDungeonAltRaisePS(dungeonInfo *goxml.SeasonDungeonInfoExt, index int) (uint32, *battle.AltRaisePS) {
	team := u.GetFormationTeam(uint32(common.FORMATION_ID_FI_SEASON_DUNGEON), index)
	if team == nil {
		l4g.Errorf("user:%d AltRaisePS: formation is nil.", u.ID())
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	raisePSMap := make(map[uint32][]uint64)
	var heroTotalStar uint32
	var suitAdd uint32
	skinM := u.SkinManager()
	for _, info := range team.Info {
		hero := u.HeroManager().Get(info.Hid)
		if hero == nil {
			continue
		}

		layerInfo := goxml.GetData().SeasonDungeonLayerInfoM.GetAddHero(u.GetSeasonID(), hero.data.SysId)
		if layerInfo != nil {
			heroTotalStar += hero.GetStar()
		}

		skin := skinM.GetSkinByHeroIDByBattle(hero.data.SysId)
		if skin != nil {
			suitLayerInfo := goxml.GetData().SeasonDungeonLayerInfoM.GetAddSuit(u.GetSeasonID(), skin.GetID())
			if suitLayerInfo != nil {
				suitAdd++
			}
		}
	}
	// num == 0，表示上阵的英雄不符合增加增益buff
	if heroTotalStar == 0 && suitAdd == 0 {
		return uint32(ret.RET_OK), nil
	}
	// 通过英雄总星级查找对应BUFF
	if heroTotalStar > 0 {
		u.seasonDungeonHeroTotalStar(heroTotalStar, raisePSMap)
	}

	// 皮肤BUFF层数上限
	if suitAdd > dungeonInfo.SkinRaiseMax {
		suitAdd = dungeonInfo.SkinRaiseMax
	}
	if suitAdd > 0 {
		u.seasonDungeonSuit(suitAdd, dungeonInfo.RaisePassiveSkinId, raisePSMap)
	}

	altRaisePS := battle.NewAltRaisePS()
	altRaisePS.AltAttack(raisePSMap)

	return uint32(ret.RET_OK), altRaisePS
}

func (u *User) seasonDungeonHeroTotalStar(heroTotalStar uint32, raisePSMap map[uint32][]uint64) {
	layer := uint32(1)
	skillID := goxml.GetData().SeasonHeroAddInfoM.GetPassSkill(heroTotalStar)
	if skillID == 0 {
		l4g.Info("user:%d SeasonHeroAddInfoM GetPassSkill: total Hero Star:%d get passSkill failed", u.ID(), heroTotalStar)
		return
	}
	raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skillID, layer)
	if raisePSInfo == nil {
		l4g.Info("user:%d RaisePassiveSkillInfoM get hero total star RaisePassiveSkill failed skillId:%d layer:%d", u.ID(), skillID, layer)
		return
	}

	raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePSInfo.ID)
}

func (u *User) seasonDungeonSuit(suitAdd, RaisePassiveSkinId uint32, raisePSMap map[uint32][]uint64) {
	raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(RaisePassiveSkinId, suitAdd)
	if raisePassiveSkillInfo == nil {
		l4g.Info("user:%d SeasonDungeonAltRaisePS AltRaisePS: raisePassiveSkillInfo is nil. suit passiveLayerID: %d layer:%d", u.ID(), RaisePassiveSkinId, suitAdd)
		return
	}
	raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
}

// 巅峰竞技场
// @param servicer srv
// @param *User leftUser 左侧玩家
// @param *User rightUser 右侧玩家
// @param uint32 leftPos 左侧玩家占位id
// @param uint32 rightPos 右侧玩家占位id
// @param uint32 fid 阵容id
// @param *battle.AltRaisePS
// @return bool 左侧玩家是否获胜
// @return string 战报id
// @return ret.RET 错误码
// @return uint32 左侧玩家获胜队伍数量
// @return uint32 右侧玩家获胜队伍数量
func PeakFight(srv servicer, leftUser, rightUser *User, leftPos, rightPos, fid uint32,
	altRaisePS *battle.AltRaisePS) (bool, string, ret.RET, uint32, uint32) {
	battleReport := leftUser.NewBattleReport(nil, common.FORMATION_ID(fid))
	teamNum := goxml.GetPeakTeamNum(fid)
	var leftWinCount, rightWinCount uint32
	var bats []*battle.Manager
	lTeam := &peakTeam{}
	rTeam := &peakTeam{}
	altAttr := &battle.AltAttr{}
	for {
		leftHasTeam := true
		if !leftUser.IsTeamExist(fid, lTeam.index) {
			l4g.Debugf("PeakFight: leftUser no team, uid:%d, index:%d", leftUser.ID(), lTeam.index)
			leftHasTeam = false
		}

		rightHasTeam := true
		if !rightUser.IsTeamExist(fid, rTeam.index) {
			l4g.Debugf("PeakFight: rightUser no team, uid:%d, index:%d", rightUser.ID(), rTeam.index)
			rightHasTeam = false
		}

		var leftWin bool
		var report *bt.Report
		if !leftHasTeam || !rightHasTeam {
			leftWin = peakCalcResultWhenLack(leftHasTeam, rightHasTeam, leftPos, rightPos)
		} else {
			cloneAltRaisePS := altRaisePS.Clone()
			l4g.Debugf("PeakFight: lID:%d lTeam:%+v rID:%d rTeam:%+v altAttr:%+v altRaisePS:%+v",
				leftUser.ID(), lTeam, rightUser.ID(), rTeam, altAttr, cloneAltRaisePS)
			battleParams := battle.NewManagerParams(common.FORMATION_ID(fid), common.FORMATION_ID(fid),
				lTeam.index, rTeam.index, altAttr, 0)
			battleParams.SetAltRaisePS(cloneAltRaisePS)
			bat := leftUser.NewBattleWithUser(srv, rightUser, battleParams)
			if bat == nil {
				l4g.Errorf("PeakFight: bat is nil, left:%d, right:%d", leftUser.ID(), rightUser.ID())
				return false, "", ret.RET_ERROR, 0, 0
			}
			defer bat.Free()
			bats = append(bats, bat)
			var retno ret.RET
			leftWin, retno = DoBattleResult(srv, leftUser, bat, battleReport)
			if retno != ret.RET_OK {
				l4g.Errorf("PeakFight: ret not ok, left:%d, right:%d", leftUser.ID(), rightUser.ID())
				return false, "", ret.RET_ERROR, 0, 0
			}
			report = bat.GetReport()
		}

		if leftWin {
			leftWinCount++
		} else {
			rightWinCount++
		}
		if leftWinCount >= uint32(teamNum) || rightWinCount >= uint32(teamNum) {
			break
		}

		// 更新战斗数据
		// 根据布阵逻辑，不会存在中间队伍为空的情况。如果有空的，它后面的队伍一定都是空的
		// 因此，仅当正常战斗有战报时才更新，即可
		if report != nil {
			peakUpdateFightData(lTeam, rTeam, report, altAttr, altRaisePS)
		}
	}

	isWin := leftWinCount >= uint32(teamNum)
	l4g.Debugf("PeakFight: leftWinCount:%d rightWinCount:%d leftWin:%t", leftWinCount, rightWinCount, isWin)
	leftUser.ProcessBattleReport(srv, NoNeedSendReport, isWin, battleReport, bats)
	return battleReport.GetWin(), battleReport.GetId(), ret.RET_OK, leftWinCount, rightWinCount
}

// 巅峰出战队伍信息
type peakTeam struct {
	winCount uint32 // 队伍连胜次数
	index    int    // 队伍序号，从0开始
}

// 巅峰 - 队伍缺失情况下，统计胜负
// @return bool 左侧队伍胜利
func peakCalcResultWhenLack(leftHasTeam, rightHasTeam bool, leftPos, rightPos uint32) bool {
	var leftWin bool
	if !leftHasTeam && !rightHasTeam {
		if leftPos < rightPos {
			leftWin = true
		}
	} else if leftHasTeam && !rightHasTeam {
		leftWin = true
	}
	return leftWin
}

// 巅峰 - 战斗数据更新入口
// @param *peakTeam leftTeam 左侧队伍(进攻方)
// @param *peakTeam rightTeam 右侧队伍(防守方)
// @param *bt.Report report 单场战斗数据
func peakUpdateFightData(leftTeam, rightTeam *peakTeam, report *bt.Report,
	altAttr *battle.AltAttr, altRaisePS *battle.AltRaisePS) {
	// 更新后续出战队伍数据
	leftOldWinCount := leftTeam.winCount
	rightOldWinCount := rightTeam.winCount
	if report.ReportInfo.Win {
		peakUpdateTeam(leftTeam, rightTeam)
	} else {
		peakUpdateTeam(rightTeam, leftTeam)
	}

	// 更新连胜虚弱buff效果
	peakUpdateWeakBuff(leftOldWinCount, leftTeam.winCount, true, altRaisePS)
	peakUpdateWeakBuff(rightOldWinCount, rightTeam.winCount, false, altRaisePS)

	// 更新剩余血量
	peakUpdateLeftHP(leftTeam.winCount, true, report, altAttr)
	peakUpdateLeftHP(rightTeam.winCount, false, report, altAttr)
	altAttr.SetFixMaxHP()
}

// 巅峰 - 更新后续出战队伍数据
func peakUpdateTeam(winTeam, loseTeam *peakTeam) {
	winTeam.winCount++
	if winTeam.winCount >= goxml.GetData().PeakConfigInfoM.GetTeamMaxWinLimit() {
		winTeam.index++
		winTeam.winCount = 0
	}

	loseTeam.winCount = 0
	loseTeam.index++
}

// 巅峰 - 更新连胜虚弱buff效果
func peakUpdateWeakBuff(oldWinCount, newWinCount uint32, isLeft bool, altRaisePS *battle.AltRaisePS) {
	// 清除旧效果
	if oldWinCount > 0 {
		rmRaisePS := goxml.GetData().PeakConfigInfoM.GetWeakBuffRaisePSID(oldWinCount)
		if isLeft {
			altRaisePS.AltAttackRm(0, rmRaisePS)
		} else {
			altRaisePS.AltDefenseRm(0, rmRaisePS)
		}
	}

	// 添加新效果
	if newWinCount > 0 {
		addRaisePS := goxml.GetData().PeakConfigInfoM.GetWeakBuffRaisePSID(newWinCount)
		raisePSMap := map[uint32][]uint64{
			0: {addRaisePS},
		}
		if isLeft {
			altRaisePS.AltAttack(raisePSMap)
		} else {
			altRaisePS.AltDefense(raisePSMap)
		}
	}
}

// 巅峰 - 更新修正血量
func peakUpdateLeftHP(winCount uint32, isLeft bool, report *bt.Report, altAttr *battle.AltAttr) {
	if winCount == 0 {
		// 清除存血修正
		if isLeft {
			altAttr.SetAttackIDHpPct(nil)
		} else {
			altAttr.SetDefenseIDHpPct(nil)
		}
	} else {
		// 更新存血修正
		if isLeft {
			hpPct := calcLeftHpPct(report.Attackers, report.AttackersFinal, altAttr.GetAttackIDHpPct())
			if len(hpPct) > 0 {
				altAttr.SetAttackIDHpPct(hpPct)
			}
		} else {
			hpPct := calcLeftHpPct(report.Defensers, report.DefensersFinal, altAttr.GetDefenseIDHpPct())
			if len(hpPct) > 0 {
				altAttr.SetDefenseIDHpPct(hpPct)
			}
		}
	}
}

// 计算英雄残血数据
// 仅记录死亡和残血英雄
// @param initMems 战斗前的英雄数据
// @param finalMems 战斗后的英雄数据
// @param map[uint64]int64 hpMap 上一轮的血量万分比
// @return map[uint64]int64 英雄唯一id => 血量万分比
func calcLeftHpPct(initMems, finalMems []*bt.Member, hpMap map[uint64]int64) map[uint64]int64 {
	finalMap := make(map[uint64]*bt.Member)
	for _, mem := range finalMems {
		finalMap[mem.Info.UniqId] = mem
	}

	if len(hpMap) == 0 {
		hpMap = make(map[uint64]int64)
	}
	for _, mem := range initMems {
		if _, exist := finalMap[mem.Info.UniqId]; !exist {
			// 死亡了，血量比为0
			hpMap[mem.Info.UniqId] = 0
		} else {
			pct := float64(finalMap[mem.Info.UniqId].NowHp) / float64(finalMap[mem.Info.UniqId].ShowMaxHp) * goxml.BaseFloat
			if pct < 1 {
				pct = 1
			}
			// 残血英雄
			if pct < goxml.BaseFloat {
				hpMap[mem.Info.UniqId] = int64(pct)
			}
		}
	}
	return hpMap
}

func GSTPVEFight(srv servicer, attack *User, fight *cl.GSTFightResult) ret.RET {
	monsterInfo := goxml.GetData().GuildSandTableMonsterGroupInfoM.Index(fight.Defense.MonsterId)
	if monsterInfo == nil {
		l4g.Errorf("GSTPVEFight cant find monsterID %d", fight.Defense.MonsterId)
		return ret.RET_ERROR
	}

	attackIndex := int(fight.Attack.TeamIndex)
	altAttr, altRaisePS := GSTFightFix(attack, nil, attackIndex, 0, fight)

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAttackTeamIndex(attackIndex)
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_GST, nil, []uint32{monsterInfo.MonsterGroup}, NoNeedSendReport, fixFunc, altAttr)
	battleSummary, retCode, _ := attack.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d GSTPVEFight failed, err: %d", attack.ID(), retCode)
		return retCode
	}
	GSTFightUpdate(fight, battleSummary)
	return retCode
}

func GSTFightFix(attack, defense *User, attackIndex, defenseIndex int, fight *cl.GSTFightResult) (*battle.AltAttr, *battle.AltRaisePS) {
	//修正血量
	altAttr := battle.NewAltAttr()
	altAttr.SetAttackPosHpPct(fight.Attack.HpPct)
	altAttr.SetDefensePosHpPct(fight.Defense.HpPct)
	var altPs *battle.AltRaisePS
	if fight.ArenaRound != 0 {
		altPs = GSTArenaFix(attack, defense, attackIndex, defenseIndex, fight)
	} else {
		altPs = GSTNormalFix(attack, defense, attackIndex, defenseIndex, fight)
	}
	if len(fight.Attack.AltPassives) != 0 {
		if altPs == nil {
			altPs = &battle.AltRaisePS{}
		}
		attackPSMap := make(map[uint32][]uint64)
		for pos, passives := range fight.Attack.AltPassives {
			attackPSMap[pos] = passives.Passives
		}
		altPs.AltAttack(attackPSMap)
	}
	if len(fight.Defense.AltPassives) != 0 {
		if altPs == nil {
			altPs = &battle.AltRaisePS{}
		}
		DefensePSMap := make(map[uint32][]uint64)
		for pos, passives := range fight.Defense.AltPassives {
			DefensePSMap[pos] = passives.Passives
		}
		altPs.AltDefense(DefensePSMap)
	}
	return altAttr, altPs
}

func GSTArenaFix(attack, defense *User, attackIndex, defenseIndex int, fight *cl.GSTFightResult) *battle.AltRaisePS {
	altPs := &battle.AltRaisePS{}
	mapInfo := goxml.GetData().GuildSandTableMapGroupInfoM.Index(fight.MapConfigId)
	if mapInfo == nil {
		l4g.Errorf("cant find map:%d", fight.MapConfigId)
		return altPs
	}
	arenaInfo := goxml.GetData().GuildSandTableArenaInfoM.GetRecordByArenaGroupMatchIndexRank(mapInfo.ArenaGroup, fight.ArenaRound, 1)
	if arenaInfo == nil {
		l4g.Errorf("cant find arenaInfo:%d %d", mapInfo.ArenaGroup, fight.ArenaRound)
		return altPs
	}
	attackRaisePSMap := make(map[uint32][]uint64)
	defenseRaisePSMap := make(map[uint32][]uint64)
	if arenaInfo.ArenaLink1 != 0 {
		rpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(arenaInfo.ArenaLinkBuff1)
		if rpsInfo == nil {
			l4g.Errorf("cant find RaisePassiveSkillInfo:%d %d", arenaInfo.ArenaLink1, arenaInfo.ArenaLinkBuff1)
			return altPs
		}
		attackLinkInfo := attack.FormationManager().GetFormationLinkInfo(uint32(common.FORMATION_ID_FI_GST), attackIndex)
		if attackLinkInfo != nil {
			for pos, links := range attackLinkInfo.HeroLinkInfo {
				for link := range links {
					if link == arenaInfo.ArenaLink1 {
						attackRaisePSMap[pos] = append(attackRaisePSMap[pos], rpsInfo.ID)
						break
					}
				}
			}
		}
		defenseLinkInfo := defense.FormationManager().GetFormationLinkInfo(uint32(common.FORMATION_ID_FI_GST), defenseIndex)
		if defenseLinkInfo != nil {
			for pos, links := range defenseLinkInfo.HeroLinkInfo {
				for link := range links {
					if link == arenaInfo.ArenaLink1 {
						defenseRaisePSMap[pos] = append(defenseRaisePSMap[pos], rpsInfo.ID)
						break
					}
				}
			}
		}
	}
	attackPopularInfo := goxml.GetData().GuildSandTablePopularityInfoM.GetRecordByPopularityRangeMaxLe(fight.Attack.PopularValue)
	if attackPopularInfo == nil {
		l4g.Errorf("cant find GuildSandTablePopularityInfo:%d", fight.Attack.PopularValue)
		return altPs
	}
	attackPopularRpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(attackPopularInfo.RaisePassiveSkillId)
	if attackPopularRpsInfo == nil {
		l4g.Errorf("cant find RaisePassiveSkillInfo:%d", attackPopularInfo.RaisePassiveSkillId)
		return altPs
	}
	attackRaisePSMap[battle.TeamUniterBattlePos] = append(attackRaisePSMap[battle.TeamUniterBattlePos], attackPopularRpsInfo.ID)
	defensePopularInfo := goxml.GetData().GuildSandTablePopularityInfoM.GetRecordByPopularityRangeMaxLe(fight.Defense.PopularValue)
	if defensePopularInfo == nil {
		l4g.Errorf("cant find GuildSandTablePopularityInfo:%d", fight.Defense.PopularValue)
		return altPs
	}
	defensePopularRpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(defensePopularInfo.RaisePassiveSkillId)
	if defensePopularRpsInfo == nil {
		l4g.Errorf("cant find RaisePassiveSkillInfo:%d", defensePopularInfo.RaisePassiveSkillId)
		return altPs
	}
	defenseRaisePSMap[battle.TeamUniterBattlePos] = append(defenseRaisePSMap[battle.TeamUniterBattlePos], defensePopularRpsInfo.ID)
	altPs.AltAttack(attackRaisePSMap)
	altPs.AltDefense(defenseRaisePSMap)
	return altPs
}

func GSTNormalFix(attack, defense *User, attackIndex, defenseIndex int, fight *cl.GSTFightResult) *battle.AltRaisePS {
	altPs := &battle.AltRaisePS{}
	groundInfo := goxml.GetData().GuildSandTableMapInfoM.GetMapGroundInfo(fight.GroundId)
	if groundInfo == nil {
		l4g.Errorf("GuildSandTableMapInfoM cant find. id:%d", fight.GroundId)
		return altPs
	}
	var seasonLinkInfo *goxml.GuildSandTableLinkInfoExt
	if groundInfo.LinkAddId != 0 {
		seasonLinkInfo = goxml.GetData().GuildSandTableLinkInfoM.Index(groundInfo.LinkAddId)
	}
	if attack != nil {
		raisePSMap := make(map[uint32][]uint64)
		if fight.Attack.FatigueValue != 0 {
			fatigueInfo := goxml.GetData().GuildSandTableFatigueInfoM.GetByFatigue(fight.Attack.FatigueValue)
			if fatigueInfo != nil {
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], fatigueInfo.RaisePSID)
			}
		}
		if seasonLinkInfo != nil {
			num := attack.FormationManager().GetLinkActiveNum(uint32(common.FORMATION_ID_FI_GST), groundInfo.LinkAddId, attackIndex)
			if num >= seasonLinkInfo.NeedNum {
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], seasonLinkInfo.RaisePSID)
			}
		}
		buffInfo := goxml.GetData().GuildSandTableMoraleBuffInfoM.GetRecordByIdMaxLe(fight.Attack.Morale)
		if buffInfo != nil {
			rpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(buffInfo.RaisePassiveSkillId)
			if rpsInfo != nil {
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], rpsInfo.ID)
			}
		}
		altPs.AltAttack(raisePSMap)
	}
	if defense != nil {
		raisePSMap := make(map[uint32][]uint64)
		if fight.Defense.FatigueValue != 0 {
			fatigueInfo := goxml.GetData().GuildSandTableFatigueInfoM.GetByFatigue(fight.Defense.FatigueValue)
			if fatigueInfo != nil {
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], fatigueInfo.RaisePSID)
			}
		}
		if seasonLinkInfo != nil {
			num := defense.FormationManager().GetLinkActiveNum(uint32(common.FORMATION_ID_FI_GST), groundInfo.LinkAddId, defenseIndex)
			if num >= seasonLinkInfo.NeedNum {
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], seasonLinkInfo.RaisePSID)
			}
		}
		buffInfo := goxml.GetData().GuildSandTableMoraleBuffInfoM.GetRecordByIdMaxLe(fight.Defense.Morale)
		if buffInfo != nil {
			rpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(buffInfo.RaisePassiveSkillId)
			if rpsInfo != nil {
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], rpsInfo.ID)
			}
		}
		altPs.AltDefense(raisePSMap)
	}
	return altPs
}

func GSTPVPFight(srv servicer, attack, defense *User, fight *cl.GSTFightResult) ret.RET {
	fid := common.FORMATION_ID_FI_GST
	attackIndex, defenseIndex := int(fight.Attack.TeamIndex), int(fight.Defense.TeamIndex)

	attackFormation := attack.GetFormationTeam(uint32(fid), attackIndex)
	if attackFormation == nil || len(attackFormation.Info) == 0 {
		l4g.Errorf("GSTFight: attack no team, uid:%d, index:%d", attack.ID(), attackIndex)
		return ret.RET_OK
	}
	defenseFormation := defense.GetFormationTeam(uint32(fid), defenseIndex)
	if defenseFormation == nil || len(defenseFormation.Info) == 0 {
		l4g.Errorf("GSTFight: defense no team, uid:%d, index:%d", attack.ID(), defenseIndex)
		fight.IsWin = true
		return ret.RET_OK
	}

	altAttr, altPs := GSTFightFix(attack, defense, attackIndex, defenseIndex, fight)

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAttackTeamIndex(attackIndex)
		battleParam.SetDefenseTeamIndex(defenseIndex)
		battleParam.SetAltRaisePS(altPs)
	}
	cbp := newCommonBattleParamWithUser(common.FORMATION_ID_FI_GST, fid, nil, altAttr, defense, fixFunc)
	cbp.sendReport = NoNeedSendReport
	battleSummary, retCode := attack.commonProcessBattleWithUser(srv, cbp)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d GSTPVPFight %d failed, err: %d", attack.ID(), defense.ID(), retCode)
		return retCode
	}
	GSTFightUpdate(fight, battleSummary)
	return retCode
}

func GSTFightUpdate(fight *cl.GSTFightResult, battleSummary *bt.BattleSummary) {
	report := battleSummary.Reports[0]
	fight.ReportId = battleSummary.Id
	fight.IsWin = report.Win
	fight.Attack.Power = report.AttackPower
	fight.Defense.Power = report.DefensePower
	//更新剩余血量(只能低不能高)
	GSTFightUpdateHpPct(fight.Attack, report.AttackersFinal, report.Attackers)
	GSTFightUpdateHpPct(fight.Defense, report.DefensersFinal, report.Defensers)
}

func GSTFightUpdateHpPct(info *cl.GSTFightTeam, finnalMems, initMems []*bt.BattleSummaryMember) {
	if info.HpPct == nil {
		info.HpPct = make(map[uint32]int64)
		for _, initMem := range initMems {
			info.HpPct[initMem.Pos] = battle.BaseFloatInt
		}
	}
	for _, initMem := range initMems {
		find := false
		for _, finalMem := range finnalMems {
			if finalMem.UniterId == initMem.UniterId {
				find = true
				newPct := battle.GetMemHpPct(finalMem)
				if newPct < info.HpPct[initMem.Pos] {
					info.HpPct[initMem.Pos] = newPct
				}
				break
			}
		}
		if !find {
			info.HpPct[initMem.Pos] = 0
		}
	}
}

// 针对计算剩余血量时比上一次要低的情况
func CalcLeftHpPct(finnalMems, initMems []*bt.Member, oldPosHpMap map[uint32]int64) map[uint32]int64 {
	if oldPosHpMap == nil {
		oldPosHpMap = make(map[uint32]int64)
		for _, initMem := range initMems {
			oldPosHpMap[initMem.Info.Pos] = battle.BaseFloatInt
		}
	}
	newPosHpMap := make(map[uint32]int64)
	for _, initMem := range initMems {
		find := false
		for _, finalMem := range finnalMems {
			if finalMem.Id == initMem.Id {
				find = true
				newPct := battle.GetMemPosHpPct(finalMem)
				newPosHpMap[initMem.Info.Pos] = newPct
				if newPct > oldPosHpMap[initMem.Info.Pos] {
					newPosHpMap[initMem.Info.Pos] = oldPosHpMap[initMem.Info.Pos]
				}
				break
			}
		}
		if !find {
			newPosHpMap[initMem.Info.Pos] = 0
		}
	}
	return newPosHpMap
}

func (u *User) GSTChallengePVEFight(srv servicer, msg *l2c.CS2L_GSTChallengeFightStart, clientData []byte) (ret.RET, string) {
	match := msg.Match
	challenge := msg.User
	botInfo := goxml.GetData().GuildSandTableChallengeBotInfoM.GetRecordById(uint32(match.Bot.Id))
	if botInfo == nil {
		l4g.Errorf("GSTChallengePVEFight cant find monsterID %d", match.Bot.Id)
		return ret.RET_ERROR, ""
	}

	attackIndex := int(match.Index)
	altAttr, altRaisePS := GSTChallengeFightFix(match, challenge.Buff)

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAttackTeamIndex(attackIndex)
		battleParam.SetAltRaisePS(altRaisePS)
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_GST_CHALLENGE, clientData, []uint32{botInfo.MonsterGroup}, NoNeedSendReport, fixFunc, altAttr)
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, &botShowData{name: match.Bot.GetName()})
	if retCode != ret.RET_OK {
		l4g.Errorf("%d GSTChallengePVEFight failed, err: %d", u.ID(), retCode)
		// 擂台赛战斗异常，按照胜利处理
		u.GSTChallengeBattleError(msg)
		return ret.RET_OK, ""
	}
	u.GSTChallengeUpdateHpPct(match, battleSummary)
	return retCode, battleSummary.Id
}

func (u *User) GSTChallengeBattleError(msg *l2c.CS2L_GSTChallengeFightStart) {
	l4g.Errorf("user:%d GSTChallengeBattleError failed, match:%+v", u.ID(), msg.Match)
	// 战斗出错一律算赢
	msg.Match.Status = goxml.GSTChallengeTeamWin
	msg.Match.WinTimes += 1
	msg.Match.TotalWins += 1
	msg.Match.OpHpPct = make(map[uint32]int64)
	for _, hero := range msg.Defender.Heroes {
		msg.Match.OpHpPct[hero.Pos] = 0
	}
	u.GSTChallengeWinHpPctFix(msg.Match.HpPct)
}

func (u *User) GSTChallengeUpdateHpPct(match *cl.GSTChallengeMatchInfo, battleSummary *bt.BattleSummary) {
	report := battleSummary.Reports[0]
	var winBattleMembers, winFinal []*bt.BattleSummaryMember
	var loseBattleMembers []*bt.BattleSummaryMember
	var winHpPctMap, loseHpPctMap map[uint32]int64
	if match.HpPct == nil {
		match.HpPct = make(map[uint32]int64)
	}
	match.OpHpPct = make(map[uint32]int64)
	if report.Win {
		match.Status = goxml.GSTChallengeTeamWin
		match.WinTimes += 1
		match.TotalWins += 1
		winBattleMembers, winFinal = report.Attackers, report.GetAttackersFinal()
		loseBattleMembers = report.Defensers
		winHpPctMap = match.HpPct
		loseHpPctMap = match.OpHpPct
	} else {
		match.Status = goxml.GSTChallengeTeamLose
		winHpPctMap = match.OpHpPct
		loseHpPctMap = match.HpPct
		winBattleMembers, winFinal = report.Defensers, report.GetDefensersFinal()
		loseBattleMembers = report.Attackers
	}

	// 失败方的血量都设为0
	for _, member := range loseBattleMembers {
		loseHpPctMap[member.Pos] = 0
	}

	//更新剩余血量(只能低不能高)
	for _, initMem := range winBattleMembers {
		if _, exist := winHpPctMap[initMem.Pos]; !exist {
			winHpPctMap[initMem.Pos] = goxml.BaseInt64
		}
		find := false
		for _, finalMem := range winFinal {
			if finalMem.UniterId == initMem.UniterId {
				find = true
				newPct := battle.GetMemHpPct(finalMem)
				if newPct < winHpPctMap[initMem.Pos] {
					winHpPctMap[initMem.Pos] = newPct
				}
				break
			}
		}
		if !find {
			winHpPctMap[initMem.Pos] = 0
		}
	}

	// 特殊处理。如果赢了但是血量都为0.修改其中一个人的为1
	if report.Win {
		u.GSTChallengeWinHpPctFix(winHpPctMap)
	}
	l4g.Debugf("wjdebug match:%+v  hpPctMap:%+v", match, winHpPctMap)
}

// 特殊处理。如果赢了但是血量都为0.修改其中一个人的为1
func (u *User) GSTChallengeWinHpPctFix(winHpPctMap map[uint32]int64) {
	needFixHpPct := true
	for _, pct := range winHpPctMap {
		if pct != 0 {
			needFixHpPct = false
			break
		}
	}
	if needFixHpPct {
		for pos := range winHpPctMap {
			winHpPctMap[pos] = 1
			break
		}
	}
}

func (u *User) GenerateGSTChallengeTeam(teamIndex uint32) *cl.GSTChallengeTeamInfo {
	team := &cl.GSTChallengeTeamInfo{}
	team.TeamIndex = teamIndex
	baseInfo := u.CreateGstUserBaseInfo()
	if baseInfo != nil {
		team.UserInfo = baseInfo.GstUser.Clone()
	}

	formation := u.FormationManager().Get(uint32(common.FORMATION_ID_FI_GST_CHALLENGE))
	if formation == nil {
		l4g.Errorf("GenerateGSTChallengeTeam: formation is nil. id:%d", u.ID())
		return team
	}

	if teamIndex >= uint32(len(formation.Teams)) {
		return team
	}

	for _, hero := range formation.Teams[teamIndex].Info {
		heroData := u.HeroManager().Get(hero.Hid)
		if heroData == nil {
			l4g.Errorf("GenerateGSTChallengeTeam: hero is nil. uid:%d hid:%d", u.ID(), hero.Hid)
			return nil
		}
		gstHero := &cl.GSTTeamHeroInfo{
			Pos:   hero.Pos,
			Star:  heroData.GetStar(),
			SysId: heroData.GetHeroSysID(),
		}
		team.Heroes = append(team.Heroes, gstHero)
	}
	return team
}

func (u *User) GSTChallengePVPFight(srv servicer, msg *l2c.CS2L_GSTChallengeFightStart, defense *User, clientData []byte) (ret.RET, string) {
	match := msg.Match
	challenge := msg.User
	attackFid := common.FORMATION_ID_FI_GST_CHALLENGE
	defenseFid := common.FORMATION_ID_FI_GST
	attackIndex, defenseIndex := int(match.Index), int(match.OpTeamIndex)

	attackFormation := u.GetFormationTeam(uint32(attackFid), attackIndex)
	if attackFormation == nil || len(attackFormation.Info) == 0 {
		l4g.Errorf("GSTChallengePVPFight: attack no team, uid:%d, index:%d", u.ID(), attackIndex)
		return ret.RET_OK, ""
	}
	defenseFormation := defense.GetFormationTeam(uint32(defenseFid), defenseIndex)
	if defenseFormation == nil || len(defenseFormation.Info) == 0 {
		l4g.Errorf("GSTChallengePVPFight: defense no team, uid:%d, index:%d", u.ID(), defenseIndex)
		// 擂台赛战斗异常，按照胜利处理
		u.GSTChallengeBattleError(msg)
		return ret.RET_OK, ""
	}

	altAttr, altPs := GSTChallengeFightFix(match, challenge.Buff)

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAttackTeamIndex(attackIndex)
		battleParam.SetDefenseTeamIndex(defenseIndex)
		battleParam.SetAltRaisePS(altPs)
	}
	cbp := newCommonBattleParamWithUser(attackFid, defenseFid, clientData, altAttr, defense, fixFunc)
	cbp.sendReport = NoNeedSendReport
	battleSummary, retCode := u.commonProcessBattleWithUser(srv, cbp)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d GSTChallengePVPFight %d failed, err: %d", u.ID(), defense.ID(), retCode)
		// 擂台赛战斗异常，按照胜利处理
		u.GSTChallengeBattleError(msg)
		return ret.RET_OK, ""
	}
	u.GSTChallengeUpdateHpPct(match, battleSummary)
	return retCode, battleSummary.Id
}

func GSTChallengeFightFix(match *cl.GSTChallengeMatchInfo, buff []uint32) (*battle.AltAttr, *battle.AltRaisePS) {
	//修正血量
	altAttr := battle.NewAltAttr()
	altAttr.SetAttackPosHpPct(match.HpPct)
	// altAttr.SetDefensePosHpPct(match.OpHpPct)
	altPs := &battle.AltRaisePS{}

	raisePSMap := make(map[uint32][]uint64)
	for _, buffID := range buff {
		info := goxml.GetData().GuildSandTableChallengeBuffInfoM.GetRecordById(buffID)
		if info == nil {
			continue
		}
		rpsInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(info.Value)
		if rpsInfo != nil {
			raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], rpsInfo.ID)
		}
	}
	if len(raisePSMap) > 0 {
		altPs.AltAttack(raisePSMap)
	}
	return altAttr, altPs
}

// SeasonLinkBattleData 赛季羁绊相关战斗数据
type SeasonLinkBattleData struct {
	MonumentID    uint32                   // 生效丰碑的id，生效条件为赛季羁绊生效，一个队伍只会存在一个生效丰碑，没有生效的丰碑则返回0
	MonumentLevel uint32                   // 生效丰碑的玩法等级
	RaisePSMap    map[uint32][]uint64      // 生效的被动技能，仅处理丰碑技能，羁绊技能在hero上处理
	Attrs         map[uint32]map[int]int64 // 丰碑符石属性，与丰碑是否生效无关
}

// 获取赛季羁绊相关战斗数据
// @param formationID uint32 阵容id
// @param teamIndex int 队伍序号
// @return *SeasonLinkBattleData
func (u *User) GetMonumentForBattle(formationID uint32, teamIndex int) *SeasonLinkBattleData {
	seasonID := u.GetSeasonID()
	if seasonID == 0 {
		return nil
	}

	// 检查玩法是否开启
	formationInfo := goxml.GetData().FormationInfoM.Index(formationID)
	if formationInfo == nil {
		l4g.Errorf("user %d GetMonumentForBattle: no formationInfo, formationID:%d", u.ID(), formationID)
		return nil
	}
	now := time.Now().Unix()
	if !goxml.IsSeasonFunctionOpen(goxml.GetData(), now, formationInfo.FunctionId) {
		return nil
	}

	l := u.SeasonLink()
	battleData := &SeasonLinkBattleData{
		RaisePSMap: make(map[uint32][]uint64),            // pos => passiveSkills
		Attrs:      l.GetBattleSeasonLinkAttrs(seasonID), // 符石属性，只要存在符石就会生效
	}

	// 生效丰碑技能
	formationLinkInfo := u.FormationManager().GetFormationLinkInfo(formationID, teamIndex)
	if formationLinkInfo == nil {
		l4g.Errorf("user %d GetMonumentForBattle: no formationLinkInfo, formationID:%d teamIndex:%d", u.ID(), formationID, teamIndex)
		return battleData
	}

	return battleData
}

// 龙战战斗
func (u *User) AttackGSTDragon(srv servicer, monsterGroups []uint32, formationID common.FORMATION_ID, clientData []byte, isSweep bool) (*bt.BattleSummary, ret.RET, []*cl.FormationTeamInfo) {
	var formationTeams []*cl.FormationTeamInfo
	// 修正队伍数量不超过怪物组数量
	formation := u.GetFormation(uint32(formationID))
	if formation != nil {
		if len(formation.Teams) > len(monsterGroups) {
			newFormation := formation.Clone()
			newFormation.Teams = newFormation.Teams[:len(monsterGroups)]
			u.FormationManager().NewFormation(srv, uint32(formationID), newFormation)
			formationTeams = newFormation.Teams
		} else {
			formationTeams = formation.Teams
		}
	}

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetMaxRound(GSTDragonMaxRound)
	}
	cbp := newCommonBattleParamWithMonster(formationID, clientData, monsterGroups, !isSweep, fixFunc, nil)
	cbp.winType = BattleWinTypeForceWin
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackGSTDragon failed, err: %d", u.ID(), retCode)
		return nil, retCode, nil
	}
	return battleSummary, retCode, formationTeams
}

// Boss挑战战斗
func (u *User) AttackBossRush(srv servicer, monsterGroup uint32, formationID common.FORMATION_ID, clientData []byte, isSweep bool) (*bt.BattleSummary, ret.RET, []*cl.FormationTeamInfo) {
	var formationTeams []*cl.FormationTeamInfo
	success, teamNum := u.GetTeamNum(uint32(formationID))
	if !success {
		l4g.Errorf("user: %d AttackBossRush: GetTeamNum failed. ", u.ID())
		return nil, ret.RET_ERROR, formationTeams
	}
	monsterGroups := make([]uint32, 0, teamNum)
	for i := 0; i < teamNum; i++ {
		monsterGroups = append(monsterGroups, monsterGroup)
	}
	// 修正队伍数量不超过怪物组数量
	formation := u.GetFormation(uint32(formationID))
	if formation != nil {
		if len(formation.Teams) > len(monsterGroups) {
			newFormation := formation.Clone()
			newFormation.Teams = newFormation.Teams[:len(monsterGroups)]
			u.FormationManager().NewFormation(srv, uint32(formationID), newFormation)
			formationTeams = newFormation.Teams
		} else {
			formationTeams = formation.Teams
		}
	}

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetMaxRound(goxml.GetData().BossRushConfigInfoM.RoundMax)
	}
	cbp := newCommonBattleParamWithMonster(formationID, clientData, monsterGroups, !isSweep, fixFunc, nil)
	cbp.winType = BattleWinTypeForceWin
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackBossRush failed, err: %d", u.ID(), retCode)
		return nil, retCode, nil
	}
	return battleSummary, retCode, formationTeams
}

// func (u *User) AttackGSTOreProcess(srv servicer, cMsg *l2c.CS2L_GSTOreFightStart, csMsg *l2c.L2CS_GSTOreFightFinish, clientData []byte) ret.RET {
// 	oreID := cMsg.Req.OreId
// 	oreInfo := goxml.GetData().GuildSandTableOreLevelInfoM.Index(oreID)
// 	if oreInfo == nil {
// 		l4g.Errorf("GuildSandTableOreLevelInfoM cant find %d", oreID)
// 		return ret.RET_ERROR
// 	}
// 	monsterInfo := goxml.GetData().GuildSandTableOreMonsterInfoM.Index(oreInfo.OreMonster)
// 	if monsterInfo == nil {
// 		l4g.Errorf("GuildSandTableOreMonsterInfoM cant find %d", oreInfo.OreMonster)
// 		return ret.RET_ERROR
// 	}
// 	var altPs *battle.AltRaisePS
// 	if len(cMsg.AltPassives) != 0 {
// 		altPs = &battle.AltRaisePS{}
// 		attackPSMap := make(map[uint32][]uint64)
// 		for pos, passives := range cMsg.AltPassives {
// 			attackPSMap[pos] = passives.Passives
// 		}
// 		altPs.AltAttack(attackPSMap)
// 	}

// 	battleReport, retCode := u.AttackGSTOre(srv, []uint32{monsterInfo.MonsterGroup}, clientData, common.FORMATION_ID(monsterInfo.FormationId), altPs)
// 	if retCode != ret.RET_OK {
// 		l4g.Errorf("%d AttackGSTOre failed, err: %d", u.ID(), retCode)
// 		return retCode
// 	}
// 	damage := battleReport.Reports[0].TotalHurt
// 	hpInfo := goxml.GetData().GuildSandTableOreBossHpInfoM.Index(oreInfo.OreMonster)
// 	if hpInfo == nil {
// 		l4g.Errorf("GuildSandTableOreBossHpInfoM cant find %d", oreInfo.OreMonster)
// 		return ret.RET_ERROR
// 	}
// 	csMsg.ReqData.AddProgress = helper.CalcReduceHpByDamage(damage, hpInfo.DamageRange, hpInfo.HpRange)
// 	csMsg.ReqData.ReportId = battleReport.Id
// 	csMsg.ReqData.Damage = damage
// 	return retCode
// }

// func (u *User) AttackGSTOre(srv servicer, monsterGroups []uint32, clientData []byte, attackFID common.FORMATION_ID, altPs *battle.AltRaisePS) (*bt.BattleSummary, ret.RET) {
// 	fixFunc := func(battleParam *battle.ManagerParams, index int) {
// 		battleParam.SetAltRaisePS(altPs)
// 	}
// 	cbp := newCommonBattleParamWithMonster(attackFID, clientData, monsterGroups, true, fixFunc, nil)
// 	cbp.winType = BattleWinTypeForceWin
// 	battleReport, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
// 	return battleReport, retCode
// }

// AttackDuelFight 切磋
func (u *User) AttackDuelFight(srv servicer, toUser *User, fid common.FORMATION_ID, teamNum int, clientData []byte) (bool, string, ret.RET) {
	if fid == common.FORMATION_ID_FI_DUEL_1 {
		battleSummary, aRet := u.AttackArenaUser(srv, toUser, fid, fid, clientData)
		return battleSummary.GetWin(), battleSummary.GetId(), aRet
	} else if fid == common.FORMATION_ID_FI_DUEL_2 { // []*goxml.WrestleBuff{} : 不要祝福系统
		return u.AttackWrestleOpponent(srv, toUser, fid, fid, teamNum, clientData, nil)
	} else if fid == common.FORMATION_ID_FI_DUEL_3 { // formationMode = 0 : 队伍顺序固定
		return u.SeasonArenaOpponent(srv, toUser, teamNum, clientData, uint32(fid), uint32(fid), 0, 0)
	} else {
		l4g.Errorf("%d AttackDuel: invalid formationId. %d", u.ID(), fid)
		return false, "", ret.RET_CLIENT_REQUEST_ERROR
	}
}

func (u *User) AttackSeasonDoor(srv servicer,
	doorInfo *goxml.SeasonDoorInfoExt, clientData []byte) (*bt.BattleSummary, ret.RET) {
	formationId := common.FORMATION_ID(doorInfo.Formation)
	battleReport := u.NewBattleReport(clientData, formationId)
	win := true
	bats := make([]*battle.Manager, 0, len(doorInfo.MonsterGroups))
	var posHpMap map[uint32]int64
	altAttr := battle.NewAltAttr()
	skillIds, _ := u.PokemonManager().GetValidPokemonSkillIds(uint32(formationId), doorInfo.Level)
	for _, monsterGroup := range doorInfo.MonsterGroups {
		if posHpMap != nil {
			altAttr.SetAttackPosHpPct(posHpMap)
		}
		battleParam := battle.NewManagerParams(formationId, 0, 0, 0, altAttr, monsterGroup)
		altRaisePS := u.SeasonDoor().altRaisePS(doorInfo)
		battleParam.SetAltRaisePS(altRaisePS)
		battleParam.SetPokemonSkillIds(skillIds)

		bat := u.NewBattleWithMonster(srv, battleParam)
		if bat == nil {
			l4g.Errorf("user:%d attackSeasonDoor init battle failed, monsterGroup: %d",
				u.ID(), monsterGroup)
			return nil, ret.RET_ERROR
		}
		defer bat.Free()
		bats = append(bats, bat)
		isWin, retno := DoBattleResult(srv, u, bat, battleReport)
		if retno != ret.RET_OK {
			l4g.Errorf("user:%d attackSeasonDoor do battle failed, monsterGroup:%d, errCode:%v",
				u.ID(), monsterGroup, retno)
			return nil, retno
		}

		if !isWin {
			win = false
			break
		}
		report := bat.GetReport()
		posHpMap = CalcLeftHpPct(report.AttackersFinal, report.Attackers, posHpMap)
	}

	u.ProcessBattleReport(srv, NoNeedSendReport, win, battleReport, bats)
	u.buildCognitionLogLinkInfo(srv, battleReport)
	return makeBattleSummary(battleReport), ret.RET_OK
}

// 赛季地图战斗
func (u *User) AttackSeasonMap(srv servicer, monsterGroup uint32, formationID common.FORMATION_ID, terrain, monsterLevel uint32, clientData []byte) (*bt.BattleSummary, ret.RET) {
	formation := u.GetFormation(uint32(formationID))
	if formation == nil || len(formation.Teams) == 0 {
		l4g.Errorf("user: %d AttackSeasonMap: GetFormation failed. ", u.ID())
		return nil, ret.RET_ERROR
	}
	monsterGroups := make([]uint32, 0, len(formation.Teams))
	for i := 0; i < len(formation.Teams); i++ {
		monsterGroups = append(monsterGroups, monsterGroup)
	}
	altRaisePS := u.TalentTree().GetSeasonMapFightAltPs(terrain)
	skillIds, _ := u.PokemonManager().GetValidPokemonSkillIds(uint32(formationID), monsterLevel)
	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		battleParam.SetAltRaisePS(altRaisePS)
		battleParam.SetPokemonSkillIds(skillIds)
	}
	cbp := newCommonBattleParamWithMonster(formationID, clientData, monsterGroups, NeedSendReport, fixFunc, nil)
	cbp.winType = BattleWinTypeForceWin
	battleSummary, retCode, _ := u.commonProcessBattleWithMonster(srv, cbp, nil)
	if retCode != ret.RET_OK {
		l4g.Errorf("%d AttackSeasonMap failed, err: %d", u.ID(), retCode)
		return nil, retCode
	}
	return battleSummary, retCode
}

func (u *User) AttackTowerPokemon(srv servicer, info *goxml.TowerPokemonDungeonInfoExt, altAttr *battle.AltAttr,
	slcAltRaisePS []*battle.AltRaisePS, clientData []byte) (*bt.BattleSummary, ret.RET, uint32) {
	if len(info.MonsterGroups) != len(slcAltRaisePS) {
		l4g.Errorf("%d Attack TowerPokemon error: len(MonsterGroups) %d != len(slcAltRaisePS) %d",
			u.ID(), len(info.MonsterGroups), len(slcAltRaisePS))
		return nil, ret.RET_ERROR, 0
	}

	fixFunc := func(battleParam *battle.ManagerParams, index int) {
		if info.Data.MaxRound > 0 {
			battleParam.SetMaxRound(info.Data.MaxRound)
		}
		battleParam.SetAltRaisePS(slcAltRaisePS[index])
	}
	cbp := newCommonBattleParamWithMonster(common.FORMATION_ID_FI_TOWER_POKEMON, clientData, info.MonsterGroups, NeedSendReport, fixFunc, altAttr)
	battleSummary, retCode, defeatTeam := u.commonProcessBattleWithMonster(srv, cbp, nil)
	return battleSummary, retCode, defeatTeam
}
