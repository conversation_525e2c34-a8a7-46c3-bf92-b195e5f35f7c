package character

import (
	"app/goxml"
	"app/logic/event"
	"app/logic/helper"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"time"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func (u *User) initSeasonBase(srv servicer, currentSeasonID uint32) {
	level := goxml.GetData().SeasonLevelInfoM.GetInitLevel(currentSeasonID)
	if level == 0 {
		l4g.Errorf("user:%d User.InitSeason: init level is 0. seasonID:%d", u.ID(), currentSeasonID)
		return
	}
	u.SetSeasonID(currentSeasonID)
	u.SetSeasonLevel(level)
	u.SetSeasonEnter(false)
	u.SetSeasonEnd(false)
	u.SetSeasonTopPower(0)
	u.SetSeasonEnterTime(time.Now().Unix())
	u.SetSeasonAddPower(u.CalcSeasonAddPower())
	u.UpdateAllPower(srv, PowerUpdateBySeasonRaise)
	u.setSaveTag(saveTagBase)
}

func (u *User) SetSeasonID(seasonID uint32) {
	u.dbUser.Base.SeasonId = seasonID
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonID() uint32 {
	return u.dbUser.Base.SeasonId
}

func (u *User) SetSeasonBeforeOpen(seasonID uint32) {
	u.dbUser.Base.SeasonBeforeOpen = seasonID
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonBeforeOpen() uint32 {
	return u.dbUser.Base.SeasonBeforeOpen
}

func (u *User) SetSeasonPower(power int64) {
	u.dbUser.Base.SeasonPower = power
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonPower() int64 {
	return u.dbUser.Base.SeasonPower
}

func (u *User) SetSeasonTopPower(power int64) {
	u.dbUser.Base.SeasonTopPower = power
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonTopPower() int64 {
	return u.dbUser.Base.SeasonTopPower
}

func (u *User) SetSeasonLevel(level uint32) {
	u.dbUser.Base.SeasonLv = level
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonLevel() uint32 {
	return u.dbUser.Base.SeasonLv
}

func (u *User) SetSeasonEnter(enter bool) {
	u.dbUser.Base.SeasonEnter = enter
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonEnter() bool {
	return u.dbUser.Base.SeasonEnter
}

func (u *User) SetSeasonEnd(end bool) {
	u.dbUser.Base.SeasonEnd = end
	u.setSaveTag(saveTagBase)
}

func (u *User) GetSeasonEnd() bool {
	return u.dbUser.Base.SeasonEnd
}

func (u *User) GetHasRebase() bool {
	return u.dbUser.Base.HasRebase
}

func (u *User) SetHasRebase(flag bool) {
	u.dbUser.Base.HasRebase = flag
	u.setSaveTag(saveTagBase)
}

func (u *User) clearSeasonLevelItem(srv servicer) {
	tokens := u.tokensBag()
	if tokens[goxml.SeasonLevelUpToken] > 0 {
		delResource := &cl.Resource{
			Type:  uint32(common.RESOURCE_TOKEN),
			Value: goxml.SeasonLevelUpToken,
			Count: uint32(tokens[goxml.SeasonLevelUpToken]),
		}
		u.Consume(srv, []*cl.Resource{delResource}, uint32(log.RESOURCE_CHANGE_REASON_SEASON_RESET_DEL_LEVEL_TOKEN), 0)
	}
}

func (u *User) UpdateSeasonPower(srv servicer) {
	if !u.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON), srv) {
		return
	}
	power := u.HeroManager().CalcSeasonBattleListPower(srv)
	if power != u.GetSeasonPower() {
		u.SetSeasonPower(power)
		if power > u.GetSeasonTopPower() {
			u.SetSeasonTopPower(power)
			u.updateSeasonTopPowerRank(srv)
		}
		u.FireCommonEvent(srv.EventM(), event.IeSeasonPowerChange, uint64(power))
		u.SendSelfToClient()
	}
}

// SeasonOpenDay
// @Description: 获取是当前赛季第几天
// @param now
// @return uint32
func (u *User) SeasonOpenDay(now int64) uint32 {
	seasonID := goxml.GetCurrentSeasonID(goxml.GetData(), now)
	info := goxml.GetData().SeasonInfoM.Index(seasonID)
	if info == nil {
		return 0
	}
	if now < info.StartTm {
		return 0
	}
	return helper.DaysBetweenTimes(now, info.StartTm) + 1
}

func (u *User) checkSeasonEnd(currentSeasonID uint32, isEnd bool, unlockSeason bool) (bool, bool) {
	userSeasonID := u.GetSeasonID()
	oldSeasonBeforeOpen := u.GetSeasonBeforeOpen()
	var needSeasonEnd, needSettleAward bool
	if userSeasonID == 0 {
		if !unlockSeason {
			if oldSeasonBeforeOpen != currentSeasonID {
				needSeasonEnd = true
				u.SetSeasonBeforeOpen(currentSeasonID)
			}
		} else {
			if oldSeasonBeforeOpen != 0 && oldSeasonBeforeOpen != currentSeasonID {
				needSeasonEnd = true
				u.SetSeasonBeforeOpen(0)
			}
		}
	} else {
		if !u.GetSeasonEnd() {
			if userSeasonID != currentSeasonID {
				needSeasonEnd = true
			} else if isEnd {
				needSeasonEnd = true
			}
			needSettleAward = true
		}
	}
	return needSeasonEnd, needSettleAward
}

// ResetSeason
// @Description: 赛季数据重置入口，注意是赛季变化后，玩家的下一次请求才会重置
// @receiver u
func (u *User) ResetSeason(srv servicer) {
	now := time.Now().Unix()
	currentSeasonID, isEnd := goxml.GetData().SeasonInfoM.GetCurrentSeasonIDAndState(now)
	unlockSeason := u.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON), srv)
	needSeasonEnd, needSettleAward := u.checkSeasonEnd(currentSeasonID, isEnd, unlockSeason)
	if needSeasonEnd {
		u.OnSeasonEnd(srv, needSettleAward)
		u.SetSeasonEnd(true)
	}
	if currentSeasonID == 0 || isEnd || !unlockSeason || currentSeasonID == u.GetSeasonID() {
		return
	}
	u.OnSeasonInit(srv, currentSeasonID)
}

// @param needSettleAward 是否需要执行发奖逻辑(这里有只清理物品和清理物品并发奖2种情况)
func (u *User) OnSeasonEnd(srv servicer, needSettleAward bool) {
	u.onSeasonEnd(srv, needSettleAward)
	// 阵法回收
	u.RiteManager().OnSeasonEnd(srv, needSettleAward)
	// 羁绊丰碑回收
	u.SeasonLink().OnSeasonEnd(srv, needSettleAward)
	// 遗物系统
	u.RemainM().OnSeasonEnd(srv, needSettleAward)
	// 天赋树
	u.TalentTree().OnSeasonEnd(srv, needSettleAward)
	// 失序空间
	u.DisorderLand().OnSeasonEnd(srv, needSettleAward)
	// 公会战
	u.GST().OnSeasonEnd(srv, needSettleAward)
	// 赛季嘉年华
	u.Carnival().OnSeasonEnd()
	// 赛季装备
	u.SeasonJewelryManager().OnSeasonEnd(srv, needSettleAward)
	// 赛季开门
	u.SeasonDoor().OnSeasonEnd(srv, needSettleAward)
	// 赛季商店
	u.SeasonShop().OnSeasonEnd()
	// 赛季地图
	u.SeasonMap().OnSeasonEnd(srv, needSettleAward)
	u.TowerPokemon().OnSeasonEnd(srv, needSettleAward)
}

func (u *User) onSeasonEnd(srv servicer, _ bool) {
	// 检查添加赛季回顾Id列表
	u.checkAddSeasonFlashBackId()
	u.clearSeasonLevelItem(srv)
}

func (u *User) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	// 初始化基本数据
	u.onSeasonInit(srv, currentSeasonID)
	// 赛季等级数据重置
	u.SeasonLevel().OnSeasonInit(srv, currentSeasonID)
	// 赛季主线
	u.SeasonDungeon().OnSeasonInit(srv, currentSeasonID)
	// 失序空间
	u.DisorderLand().OnSeasonInit(srv, currentSeasonID)
	// 赛季回流
	u.SeasonReturn().OnSeasonInit(srv, currentSeasonID)
	// 天赋树赛季
	u.TalentTree().OnSeasonInit(srv, currentSeasonID)
	// Boss挑战
	u.BossRush().OnSeasonInit(srv, currentSeasonID)
	// 公会战
	u.GST().OnSeasonInit(srv, currentSeasonID)
	// 赛季开门
	u.SeasonDoor().OnSeasonInit(srv, currentSeasonID)
	// 赛季地图
	u.SeasonMap().OnSeasonInit(srv, currentSeasonID)
	u.TowerPokemon().OnSeasonInit(srv, currentSeasonID)
}

func (u *User) onSeasonInit(srv servicer, currentSeasonID uint32) {
	u.initSeasonBase(srv, currentSeasonID)
}

func (u *User) updateSeasonTopPowerRank(srv servicer) {
	now := time.Now().Unix()
	powerRank := &cr.RankSeasonTopPower{
		Uid:            u.ID(),
		Sid:            u.ServerID(),
		SeasonTopPower: u.GetSeasonTopPower(),
		Tm:             now,
		Snapshot:       u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(powerRank)

	if err != nil {
		l4g.Errorf("user:%d updateSeasonTopPowerRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    goxml.SeasonPowerRankID,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}

func (u *User) UpdateSeasonLinkRank(srv servicer, rankId uint32, point int64) {
	now := time.Now().Unix()
	rankingInfo := goxml.GetData().RankingInfoM.Index(rankId)
	if rankingInfo == nil {
		l4g.Errorf("user:%d updateSeasonLinkRank input rank id:%d is error", u.ID(), rankId)
		return
	}
	powerRank := &cr.RankSeasonLink{
		Uid:          u.ID(),
		Sid:          u.ServerID(),
		RecyclePoint: point,
		Tm:           now,
		Snapshot:     u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(powerRank)

	if err != nil {
		l4g.Errorf("user:%d updateSeasonLinkRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    rankId,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}

func (u *User) UpdateSeasonBossRushRank(srv servicer, rankId, bossId, bossLevel, totalProgress uint32) {
	l4g.Debugf("user:%d UpdateSeasonBossRushRank rankId %d bossId %d bossLevel %d totalProgress %d", u.ID(), rankId, bossId, bossLevel, totalProgress)
	now := time.Now().Unix()
	rankingInfo := goxml.GetData().RankingInfoM.Index(rankId)
	if rankingInfo == nil {
		l4g.Errorf("user:%d UpdateSeasonBossRushRank input rank id %d invalid. ", u.ID(), rankId)
		return
	}
	powerRank := &cr.RankBossRush{
		Uid:           u.ID(),
		Sid:           u.ServerID(),
		BossId:        bossId,
		BossLevel:     bossLevel,
		TotalProgress: totalProgress,
		Tm:            now,
		Snapshot:      u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(powerRank)

	if err != nil {
		l4g.Errorf("user:%d UpdateSeasonBossRushRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		l4g.Debugf("user:%d UpdateSeasonBossRushRank lastResetTime == 0. ", u.ID())
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    rankId,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}

func (u *User) UpdateTalentTreeRootLevelRank(srv servicer, level uint32) {
	rankId := goxml.TalentTreeRootLevelRank
	l4g.Debugf("user:%d UpdateTalentTreeRootLevelRank rankId %d level %d", u.ID(), rankId, level)
	now := time.Now().Unix()
	rankingInfo := goxml.GetData().RankingInfoM.Index(rankId)
	if rankingInfo == nil {
		l4g.Errorf("user:%d UpdateTalentTreeRootLevelRank input rank id %d invalid. ", u.ID(), rankId)
		return
	}
	rankData := &cr.RankTalentTree{
		Uid:      u.ID(),
		Sid:      u.ServerID(),
		Level:    level,
		Tm:       now,
		Snapshot: u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(rankData)

	if err != nil {
		l4g.Errorf("user:%d UpdateTalentTreeRootLevelRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		l4g.Debugf("user:%d UpdateSeasonBossRushRank lastResetTime == 0. ", u.ID())
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, u.ID(), &l2c.L2C_RankUpdate{
		RankId:    rankId,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}

func (u *User) UpdateAllSeasonRank(srv servicer) {
	if !u.dbUser.Module5.SeasonRankSync {
		if u.GetSeasonTopPower() == 0 {
			u.SetSeasonTopPower(u.GetSeasonPower())
		}
		if u.GetSeasonTopPower() > 0 {
			u.updateSeasonTopPowerRank(srv)
		}

		monumentInfos := goxml.GetData().SeasonLinkMonumentInfoM.SeasonLinkMonumentInfoM.GetRecordsBySeasonId(u.GetSeasonID())
		seasonLinkM := u.SeasonLink()
		for _, info := range monumentInfos {
			if info == nil {
				continue
			}
			monument := seasonLinkM.GetMonumentById(info.Id)
			if monument == nil {
				continue
			}

			newPoint := monument.GetRecyclePoint()
			u.UpdateSeasonLinkRank(srv, info.Ranking, int64(newPoint))
		}
	}
	u.dbUser.Module5.SeasonRankSync = true
	u.setSaveTag(saveTagModule5)
}

func (u *User) checkAddSeasonFlashBackId() {
	configInfoM := goxml.GetData().SeasonFlashBackConfigInfoM
	if u.GetSeasonLevel() >= configInfoM.GetUnlockSeasonLevel() {
		// maxRare, _ := u.SeasonLink().getMaxRareAndNum()
		// if maxRare >= configInfoM.GetUnlockSeasonLinkRare() {
		u.AddSeasonFlashBackId(u.GetSeasonID())
		// }
	}
}

func (u *User) UpdateSeasonRemainBookRank(srv servicer, totalExp int64) {
	now := time.Now().Unix()
	powerRank := &cr.RankSeasonRemainBookExp{
		Uid:           u.ID(),
		Sid:           u.ServerID(),
		RemainBookExp: totalExp,
		Tm:            now,
		Snapshot:      u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(powerRank)

	if err != nil {
		l4g.Errorf("user:%d updateSeasonRemainBookRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    goxml.SeasonRemainBookExpRankID,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}

func (u *User) UpdateTowerSeasonActivityRank(srv servicer, now int64, rankData []byte) {
	l4g.Debugf("user:%d UpdateTowerSeasonActivityRank. rankData %+v", u.ID(), rankData)

	if seasonOpenDay := u.SeasonOpenDay(now); !goxml.GetData().ConfigInfoM.IsRankActivityTowerSeasonActive(seasonOpenDay) {
		l4g.Debugf("user:%d UpdateTowerSeasonActivityRank activity not active. seasonOpenDay %d", u.ID(), seasonOpenDay)
		return
	}

	rankId := goxml.ActivityTowerSeasonRankID
	rankingInfo := goxml.GetData().RankingInfoM.Index(rankId)
	if rankingInfo == nil {
		l4g.Errorf("user:%d UpdateTowerSeasonActivityRank input rank id %d invalid. ", u.ID(), rankId)
		return
	}

	lastResetTime := goxml.GetTowerSeasonRoundLastBeginResetTime(now)
	if lastResetTime == 0 {
		l4g.Debugf("user:%d UpdateTowerSeasonActivityRank lastResetTime == 0. ", u.ID())
		return
	}

	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    rankId,
		ResetTime: uint64(lastResetTime),
		Data:      rankData,
	})
}

func (u *User) UpdateSeasonMapRank(srv servicer, rankId uint32, rankValue uint32) {
	now := time.Now().Unix()
	rankingInfo := goxml.GetData().RankingInfoM.Index(rankId)
	if rankingInfo == nil {
		l4g.Errorf("user:%d UpdateSeasonMapRank input rank id:%d is error", u.ID(), rankId)
		return
	}
	rankData := &cr.RankSeasonMap{
		Uid:       u.ID(),
		Sid:       u.ServerID(),
		RankValue: rankValue,
		Tm:        now,
		Snapshot:  u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(rankData)
	if err != nil {
		l4g.Errorf("user:%d UpdateSeasonMapRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    rankId,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}

func (u *User) UpdateTowerPokemonRank(srv servicer, DungeonId uint32, now int64) {
	Rank := &cr.RankTowerPokemon{
		Uid:       u.ID(),
		Sid:       u.ServerID(),
		DungeonId: DungeonId,
		Tm:        now,
		Snapshot:  u.NewUserSnapshot(0),
	}

	data, err := proto.Marshal(Rank)

	if err != nil {
		l4g.Errorf("user:%d UpdateTowerPokemonRank marshal power err:%s", u.ID(), err)
		return
	}
	lastResetTime := goxml.GetSeasonLastBeginResetTime(now)
	if lastResetTime == 0 {
		return
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, srv.ServerID(), &l2c.L2C_RankUpdate{
		RankId:    goxml.TowerPokemonRank,
		ResetTime: uint64(lastResetTime),
		Data:      data,
	})
}
