package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/event"
	"app/logic/helper"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"math"
	"slices"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type SeasonDoor struct {
	owner *User
	data  *db.SeasonDoor //db-save-check
}

func newSeasonDoor(user *User) *SeasonDoor {
	return &SeasonDoor{
		owner: user,
	}
}

func (s *SeasonDoor) Owner() *User {
	return s.owner
}

func (s *SeasonDoor) Load(data *db.SeasonDoor) {
	s.data = data
	s.customInit()
}

func (s *SeasonDoor) customInit() {
	if s.data == nil {
		s.data = &db.SeasonDoor{}
	}
	if s.data.SeasonDoor == nil {
		s.data.SeasonDoor = &cl.SeasonDoor{}
	}
	if s.data.SeasonDoor.TaskProgress == nil {
		s.data.SeasonDoor.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if s.data.SeasonDoor.ReceiveAwarded == nil {
		s.data.SeasonDoor.ReceiveAwarded = make(map[uint32]bool)
	}
	if s.data.SeasonDoor.PassedDoors == nil {
		s.data.SeasonDoor.PassedDoors = make(map[uint32]uint32)
	}
	if s.data.SeasonDoor.PreFightResult == nil {
		s.data.SeasonDoor.PreFightResult = make(map[uint32]bool)
	}
}

func (s *SeasonDoor) Save() {
	s.owner.dbUser.Module7.SeasonDoor = s.data
	s.owner.setSaveTag(saveTagModule7)
}

func (s *SeasonDoor) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	s.resetData()
	s.customInit()
}

func (s *SeasonDoor) resetData() {
	if s.data == nil {
		return
	}
	if s.data.SeasonDoor != nil {
		s.data.SeasonDoor = nil
	}
	if s.data.Logs != nil {
		s.data.Logs = nil
	}
	s.Save()
}

func (s *SeasonDoor) OnSeasonEnd(srv servicer, needSettleAward bool) {
	s.clearSeasonItem(srv)
	s.resetData()
	s.customInit()
}

func (s *SeasonDoor) clearSeasonItem(srv servicer) {
	clearItems := make([]*cl.Resource, 0)
	for _, item := range goxml.GetData().SeasonDoorConfigInfoM.SeasonEndClearItems {
		count := s.owner.GetResourceCount(item.Type, item.Value)
		if count != 0 {
			clearItems = append(clearItems, &cl.Resource{
				Type:  item.Type,
				Value: item.Value,
				Count: uint32(count),
			})
		}
	}
	if len(clearItems) > 0 {
		s.owner.Consume(srv, clearItems, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_SEASON_RESET), 0)
	}
}

func (s *SeasonDoor) Flush() *cl.SeasonDoor {
	return s.data.SeasonDoor.Clone()
}

func (s *SeasonDoor) IsRecvAward(taskId uint32) bool {
	return s.data.SeasonDoor.ReceiveAwarded[taskId]
}

func (s *SeasonDoor) SetRecvAward(taskIds []uint32) {
	for _, taskId := range taskIds {
		s.data.SeasonDoor.ReceiveAwarded[taskId] = true
	}
	s.Save()
}

func (s *SeasonDoor) OnSeasonDoorEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	//功能未开启返回
	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_DOOR), srv) {
		return
	}

	progressMap, change := s.owner.TaskTypeOnEvent(s.data.SeasonDoor.TaskProgress, event, progress, values)
	if change {
		s.Save()
		s.sendTaskProgress(progressMap)
	}
}

func (s *SeasonDoor) sendTaskProgress(progressMap map[uint32]*cl.TaskTypeProgress) {
	s.owner.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDoorTaskUpdate, &cl.L2C_SeasonDoorTaskUpdate{
		Ret:              uint32(ret.RET_OK),
		TaskTypeProgress: progressMap,
	})
}

func (s *SeasonDoor) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return s.owner.CalcTaskProgress(taskTypeInfo)
	}
	return s.data.SeasonDoor.TaskProgress[taskTypeInfo.Id]
}

func (s *SeasonDoor) FightLine(srv servicer, req *cl.C2L_SeasonDoorFightLine, rsp *cl.L2C_SeasonDoorFightLine) uint32 {
	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_DOOR), srv) {
		l4g.Errorf("user: %d FightLine: function not open", s.owner.ID())
		return uint32(ret.RET_FUNCTION_NOT_OPEN)
	}

	fRet := s.checkFightLine(req)
	if fRet != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d FightLine: checkFightLine error. %d", s.owner.ID(), fRet)
		return fRet
	}

	s.addFightLine(req)
	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) isLineEnough() bool {
	fightingLineNum := len(s.data.SeasonDoor.FightLines)
	hadLineNum := goxml.GetData().SeasonDoorConfigInfoM.InitQueueNum + s.owner.TalentTree().GetSeasonDoorLineNum()
	return hadLineNum > fightingLineNum
}

func (s *SeasonDoor) checkFightLine(req *cl.C2L_SeasonDoorFightLine) uint32 {
	targetInfo := goxml.GetData().SeasonDoorInfoM.Index(req.Id)
	if targetInfo == nil {
		l4g.Errorf("user: %d checkFightLine: SeasonDoorInfo is nil,id: %d", s.owner.ID(), req.Id)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	if !s.isLineEnough() {
		l4g.Errorf("user: %d checkFightLine: line not enough", s.owner.ID())
		return uint32(ret.RET_SEASON_DOOR_LINE_CANT_FIGHT)
	}

	formation := s.owner.GetFormation(targetInfo.Formation)
	if formation == nil {
		l4g.Errorf("user: %d checkFightLine: formation %d is nil", s.owner.ID(), targetInfo.Formation)
		return uint32(ret.RET_SEASON_DOOR_LINE_CANT_FIGHT)
	}

	if !s.CheckFormation(targetInfo.Formation, formation.Teams) {
		l4g.Errorf("user: %d checkFightLine: checkFormation error. id: %d", s.owner.ID(), targetInfo.Id)
		return uint32(ret.RET_SEASON_DOOR_LINE_CANT_FIGHT)
	}

	if !s.isDoorUnlock(targetInfo) {
		l4g.Errorf("user: %d checkFightLine: door not unlock,id: %d", s.owner.ID(), targetInfo.Id)
		return uint32(ret.RET_SEASON_DOOR_LINE_CANT_FIGHT)
	}

	if oRet := s.canDoorUseOil(req); oRet != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d checkFightLine: invalid oil. doorId %d oilId %d", s.owner.ID(), req.Id, req.OilId)
		return oRet
	}

	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) addFightLine(req *cl.C2L_SeasonDoorFightLine) {
	s.data.SeasonDoor.FightLines = append(s.data.SeasonDoor.FightLines, &cl.SeasonDoorFightLine{
		Id:           req.Id,
		FightTimes:   goxml.GetData().SeasonDoorConfigInfoM.FightModelNum,
		OilOpType:    req.OilOpType,
		OilId:        req.OilId,
		OccupyOilNum: req.OilNum,
	})
	s.Save()
}

func (s *SeasonDoor) CheckFormation(targetFormation uint32, teams []*cl.FormationTeamInfo) bool {
	if s.IsDoorFighting(targetFormation) {
		l4g.Errorf("user: %d checkFormation: door is fighting", s.owner.ID())
		return false
	}
	formationHeroes := make(map[uint64]struct{})
	for _, team := range teams {
		for _, heroInfo := range team.Info {
			formationHeroes[heroInfo.Hid] = struct{}{}
		}
	}
	// 与其他正在战斗中的队列 阵容互斥
	for i := uint32(common.FORMATION_ID_FI_SEASON_DOOR_1); i < uint32(common.FORMATION_ID_FI_SEASON_DOOR_8); i++ {
		if i == targetFormation || !s.IsDoorFighting(i) {
			continue
		}
		formation := s.owner.GetFormation(i)
		if formation == nil {
			continue
		}
		for _, team := range formation.Teams {
			for _, heroInfo := range team.Info {
				if _, exist := formationHeroes[heroInfo.Hid]; exist {
					l4g.Errorf("user: %d checkSeasonDoorFormation: hero exist. funcId:%d Hid:%d", s.owner.ID(), i, heroInfo.Hid)
					return false
				}
			}
		}
	}

	return true
}

func (s *SeasonDoor) IsDoorFighting(targetFormation uint32) bool {
	for _, fight := range s.data.SeasonDoor.FightLines {
		doorInfo := goxml.GetData().SeasonDoorInfoM.Index(fight.Id)
		if doorInfo == nil {
			l4g.Errorf("user: %d isDoorFighting: SeasonDoorInfo is nil,id: %d", s.owner.ID(), fight.Id)
			return true
		}
		if doorInfo.Formation == targetFormation {
			return true
		}
	}
	return false
}

func (s *SeasonDoor) isDoorUnlock(targetInfo *goxml.SeasonDoorInfoExt) bool {
	if targetInfo.DoorType == SeasonDoorTypeEquip {
		if targetInfo.Level == 1 {
			return true
		}
		passLevel := s.data.SeasonDoor.PassedDoors[targetInfo.DoorNum]
		if passLevel+1 >= targetInfo.Level {
			return true
		}
	} else if targetInfo.DoorType == SeasonDoorTypeMat {
		if targetInfo.Level == 1 {
			for doorNum, passLevel := range s.data.SeasonDoor.PassedDoors {
				doorType := goxml.GetData().SeasonDoorInfoM.GetDoorType(doorNum)
				if doorType == SeasonDoorTypeEquip && passLevel >= goxml.GetData().SeasonDoorConfigInfoM.MatUnlockLevel {
					return true
				}
			}
		} else {
			passLevel := s.data.SeasonDoor.PassedDoors[targetInfo.DoorNum]
			if passLevel+1 >= targetInfo.Level {
				return true
			}
		}
	}

	return false
}

func (s *SeasonDoor) isDoorPassed(targetInfo *goxml.SeasonDoorInfoExt) bool {
	passLevel := s.data.SeasonDoor.PassedDoors[targetInfo.DoorNum]
	return passLevel >= targetInfo.Level
}

func (s *SeasonDoor) canDoorUseOil(req *cl.C2L_SeasonDoorFightLine) uint32 {
	if req.OilId == 0 {
		return uint32(ret.RET_OK)
	}

	if dropGroup := goxml.GetData().SeasonDoorInfoM.GetOilDropGroup(req.Id, req.OilId); dropGroup == 0 {
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	// v2.38优化：oil可以被不同的门使用
	// if s.isOilUsed(oilId) {
	// 	return uint32(ret.RET_SEASON_DOOR_LINE_OIL_USED)
	// }

	// 使用oil的目标数量是否足够
	if req.OilOpType == uint32(common.SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_LIMITED) && req.OilNum > 0 {
		var availableOilNum uint32
		realOilNum := uint32(s.owner.GetItemCount(req.OilId))
		totalOccupiedNum := s.calcTotalOccupiedOilNum(req.OilId)
		if realOilNum > totalOccupiedNum {
			availableOilNum = realOilNum - totalOccupiedNum
		}
		if availableOilNum < req.OilNum {
			return uint32(ret.RET_SEASON_DOOR_LINE_OIL_NOT_ENOUGH)
		}
	}

	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) isOilUsed(oilId uint32) bool {
	for _, fightLine := range s.data.SeasonDoor.FightLines {
		if fightLine.OilId == oilId {
			return true
		}
	}
	return false
}

func (s *SeasonDoor) getAvailableOilNum(fightLine *cl.SeasonDoorFightLine) uint32 {
	var availableOilNum uint32

	oilId := fightLine.OilId
	if oilId == 0 { // 不使用油
		return 0
	}

	realOilNum := s.owner.GetItemCount(oilId)
	if realOilNum == 0 { // 没有油
		return 0
	}

	occupyOilNum := fightLine.OccupyOilNum
	if fightLine.OilOpType == uint32(common.SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_LIMITED) { // 限制使用油的数量，读取自己占有的部分
		availableOilNum = min(realOilNum, occupyOilNum)
	} else if fightLine.OilOpType == uint32(common.SEASON_DOOR_OIL_OPERATION_TYPE_SDOOT_UNLIMITED) { // 不限使用油数量，获取其他占有完剩下的
		totalOccupiedNum := s.calcTotalOccupiedOilNum(oilId)
		if realOilNum <= totalOccupiedNum {
			return 0
		}
		availableOilNum = realOilNum - totalOccupiedNum
	}

	return availableOilNum
}

func (s *SeasonDoor) calcTotalOccupiedOilNum(oilId uint32) uint32 {
	var totalNum uint32
	for _, fightLine := range s.data.SeasonDoor.FightLines {
		if fightLine.OilId != oilId {
			continue
		}
		totalNum += fightLine.OccupyOilNum
	}
	return totalNum
}

func (s *SeasonDoor) TaskReward(srv servicer, req *cl.C2L_SeasonDoorTaskReward, rsp *cl.L2C_SeasonDoorTaskReward) uint32 {
	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_DOOR), srv) {
		l4g.Errorf("user: %d TaskReward: function not open", s.owner.ID())
		return uint32(ret.RET_FUNCTION_NOT_OPEN)
	}
	dataMaxLens := goxml.GetData().SeasonDoorTaskInfoM.GetTaskMaxLen()
	taskLens := len(req.TaskIds)
	if taskLens == 0 || taskLens > dataMaxLens {
		l4g.Errorf("user: %d TaskReward taskIds len is error", s.owner.ID())
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}
	repeatedCheck := make(map[uint32]struct{}, taskLens)
	totalAward := make([]*cl.Resource, 0, taskLens)
	totalPoint := uint64(0)
	for _, taskId := range req.TaskIds {
		_, exist := repeatedCheck[taskId]
		if exist {
			l4g.Errorf("user: %d TaskReward task ID:%d is repeated", s.owner.ID(), taskId)
			return uint32(ret.RET_REPEATED_RECEIVE_AWARD)
		}
		repeatedCheck[taskId] = struct{}{}

		if s.IsRecvAward(taskId) {
			l4g.Errorf("user: %d TaskReward recv award is recv:%d", s.owner.ID(), taskId)
			return uint32(ret.RET_REPEATED_RECEIVE_AWARD)
		}

		taskInfo := goxml.GetData().SeasonDoorTaskInfoM.Index(taskId)
		if taskInfo == nil {
			l4g.Errorf("user: %d TaskReward task:%d Info is nil", s.owner.ID(), taskId)
			return uint32(ret.RET_SYSTEM_DATA_ERROR)
		}

		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d TaskReward: taskTypeInfo not exist. typeID:%d", s.owner.ID(), taskInfo.TypeId)
			return uint32(ret.RET_SYSTEM_DATA_ERROR)
		}

		progress := s.GetTaskProgress(taskTypeInfo)
		if !s.owner.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
			l4g.Errorf("user:%d TaskReward: task:%d is not finish.", s.owner.ID(), taskId)
			return uint32(ret.RET_SEASON_DUNGEON_TASK_PROGRESS_NOT_ENOUGH)
		}
		totalAward = append(totalAward, taskInfo.ClRes...)
		totalPoint += uint64(taskInfo.GetPoints)
	}
	result, res := s.owner.Award(srv, totalAward, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_TASK_REWARD), 0)
	if result != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d TaskReward: send award failed. retCode: %d", s.owner.ID(), result)
		return result
	}
	if totalPoint > 0 {
		s.owner.FireCommonEvent(srv.EventM(), event.AeSeasonDoorTaskPoints, totalPoint)
	}
	rsp.Rewards = res
	rsp.TaskIds = req.TaskIds
	s.SetRecvAward(req.TaskIds)
	logData := &log.LogSeasonDoorTaskReward{
		TaskId: req.TaskIds,
	}
	s.owner.LogSeasonDoorTaskReward(srv, logData)
	s.Save()
	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) getAllFightLines() []*cl.SeasonDoorFightLine {
	return s.data.SeasonDoor.FightLines
}

func (s *SeasonDoor) getFightLine(id uint32) *cl.SeasonDoorFightLine {
	for _, fight := range s.data.SeasonDoor.FightLines {
		if fight.Id == id {
			return fight
		}
	}
	return nil
}

func (s *SeasonDoor) delFightLine(id uint32) {
	s.data.SeasonDoor.FightLines = slices.DeleteFunc(s.data.SeasonDoor.FightLines, func(fight *cl.SeasonDoorFightLine) bool {
		return fight.Id == id
	})
	s.Save()
}

func (s *SeasonDoor) Log(srv servicer, req *cl.C2L_SeasonDoorLog, rsp *cl.L2C_SeasonDoorLog) uint32 {
	var pageNum uint32 = 10
	if req.Page == 0 {
		req.Page = 1
		pageNum = 50
	}
	rsp.Page = req.Page
	rsp.Total = uint32(len(s.data.Logs))
	start := (req.Page - 1) * pageNum
	if start >= uint32(rsp.Total) {
		return uint32(ret.RET_OK)
	}
	start = rsp.Total - start
	var end uint32
	if start > pageNum {
		end = start - pageNum
	} else {
		end = 0
	}

	for i := start; i > end; i-- {
		rsp.Logs = append(rsp.Logs, s.data.Logs[i-1].Clone())
	}
	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) FightLineReward(srv servicer, req *cl.C2L_SeasonDoorFightLineReward, rsp *cl.L2C_SeasonDoorFightLineReward) uint32 {
	fightLine := s.getFightLine(req.Id)
	if fightLine == nil {
		l4g.Errorf("user: %d FightLineReward: fightLine is nil,id: %d", s.owner.ID(), req.Id)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	now := time.Now().Unix()
	if fightLine.EndTime == 0 || now < fightLine.EndTime {
		l4g.Errorf("user: %d FightLineReward: fightLine is not finish,id: %d now:%d end:%d ", s.owner.ID(), req.Id, now, fightLine.EndTime)
		return uint32(ret.RET_SEASON_DOOR_LINE_FIGHT_NOT_FINISH)
	}

	doorInfo := goxml.GetData().SeasonDoorInfoM.Index(fightLine.Id)
	if doorInfo == nil {
		l4g.Errorf("user: %d FightLineReward: SeasonDoorInfo is nil,id: %d", s.owner.ID(), fightLine.Id)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	rRet := s.restSettle(srv, fightLine, now)
	if rRet != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d FightLineReward: restSettle err. %d", s.owner.ID(), rRet)
		return rRet
	}

	awards := make([]*cl.Resource, 0, len(fightLine.Rewards)+len(fightLine.OilRewards))
	awards = append(awards, fightLine.Rewards...)
	awards = append(awards, fightLine.OilRewards...)
	awards = MergeResources(awards)
	awards = s.owner.SeasonJewelryManager().AutoDecompose(srv, awards, goxml.SeasonJewelryRewardTypeRest)

	// 存在没有生成奖励就直接取消的情况
	if len(awards) > 0 {
		retCode, res := s.owner.Award(srv, awards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_LINE_REWARD), uint64(common.SEASON_DOOR_REWARD_TYPE_DRP_REST))
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d FightLineReward: send award failed. retCode: %d", s.owner.ID(), retCode)
			return retCode
		}
		rsp.Rewards = res
	}

	// v2.36优化：删除涂油展示
	// if len(fightLine.OilRewards) > 0 {
	// 	retCode, oRes := s.owner.Award(srv, fightLine.OilRewards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_LINE_REWARD), uint64(common.SEASON_DOOR_REWARD_TYPE_DRP_OIL))
	// 	if retCode != uint32(ret.RET_OK) {
	// 		l4g.Errorf("user: %d FightLineReward: send oilRewards failed. retCode: %d", s.owner.ID(), retCode)
	// 		return retCode
	// 	}
	// 	rsp.OilRewards = oRes
	// }

	// 领奖完成后清除未领取奖励
	fightLine.Rewards = nil
	fightLine.OilRewards = nil
	s.Save()

	// 结束挂机
	if req.FinishRest {
		s.finishFightLine(srv, doorInfo, fightLine)
		rsp.FinishRest = true
	}
	rsp.RestSettleNum = fightLine.RestSettleNum

	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) restSettle(srv servicer, fightLine *cl.SeasonDoorFightLine, now int64) uint32 {
	hasSettleNum := fightLine.RestSettleNum // 挂机已结算次数
	rewards := fightLine.Rewards            // 剩余可领取挂机奖励
	oilRewards := fightLine.OilRewards      // 剩余可领取涂油奖励
	totalRewards := fightLine.TotalRewards  // 累计挂机奖励

	// 战斗结果
	results := s.getValidFightResults(fightLine)
	resultsNum := uint32(len(results))
	if resultsNum == 0 {
		l4g.Errorf("restSettle: no fightLine Results. fightLine %+v", fightLine)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	// 挂机最大结算次数
	maxSettleNum := s.calcMaxSettleNum(results)
	if maxSettleNum == 0 {
		l4g.Errorf("restSettle: maxSettleNum = 0. results %+v", results)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	// 上次挂机结算结束时间
	lastSettleTime := fightLine.LastSettleTime
	if now <= fightLine.LastSettleTime { // 根据上次结算数据进行发奖
		l4g.Errorf("restSettle: now %+v <= lastSettleTime %+v", now, lastSettleTime)
		return uint32(ret.RET_ERROR)
	}

	var oilNum, oldOilNum, oilDropGroup uint32
	if fightLine.OilId > 0 {
		oilNum = s.getAvailableOilNum(fightLine.Clone())
		oldOilNum = oilNum
		oilDropGroup = goxml.GetData().SeasonDoorInfoM.GetOilDropGroup(fightLine.Id, fightLine.OilId)
	}

	interval := now - lastSettleTime       // 上次结算到现在的间隔
	var totalRestTime, addSettleTime int64 // 总有效挂机时间, 新增的结算时间
	for j := hasSettleNum; j < maxSettleNum; j++ {
		result := results[j%resultsNum]
		if result == nil {
			continue
		}
		addSettleTime = totalRestTime
		totalRestTime += result.RestTime
		if interval < totalRestTime { // 中止结算
			break
		}
		dropGroup := result.DropGroup
		if result.Win && oilNum > 0 && oilDropGroup > 0 { // 全胜可以涂油
			dropAwards, _ := s.owner.Drop().DoDrop(srv.Rand(), oilDropGroup)
			oilRewards = append(oilRewards, dropAwards...)
			totalRewards = append(totalRewards, dropAwards...)
			oilNum--
		} else if dropGroup > 0 {
			dropAwards, _ := s.owner.Drop().DoDrop(srv.Rand(), dropGroup)
			rewards = append(rewards, dropAwards...)
			totalRewards = append(totalRewards, dropAwards...)
		}
		if j+1 > fightLine.RestSettleNum {
			fightLine.RestSettleNum = j + 1
		}
	}

	fightLine.LastSettleTime += addSettleTime
	fightLine.Rewards = MergeResources(rewards)
	fightLine.OilRewards = MergeResources(oilRewards)
	fightLine.TotalRewards = MergeResources(totalRewards)

	if oldOilNum > oilNum {
		consumeOilNum := oldOilNum - oilNum
		if fightLine.OccupyOilNum < consumeOilNum {
			fightLine.OccupyOilNum = 0
		} else {
			fightLine.OccupyOilNum -= consumeOilNum
		}

		oilCosts := goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), fightLine.OilId, consumeOilNum)
		retCode := s.owner.Consume(srv, []*cl.Resource{oilCosts}, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_USE_OIL), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d FightLineGetData: consume error", s.owner.ID())
			return retCode
		}
	}

	s.Save()

	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) calcMaxSettleNum(results []*cl.SeasonDoorFightResult) uint32 {
	maxRestTime := goxml.GetData().SeasonDoorConfigInfoM.GetMaxRestTime()
	var loopTime int64
	for _, result := range results {
		loopTime += result.RestTime
	}
	if loopTime == 0 {
		return 0
	}
	maxSettleNum := uint32(maxRestTime / loopTime)

	extraTime := maxRestTime % loopTime
	var totalRestTime int64
	for _, result := range results {
		totalRestTime += result.RestTime
		if extraTime < totalRestTime {
			break
		}
		maxSettleNum++
	}

	return maxSettleNum
}

func (s *SeasonDoor) calcWinSettleNum(fightLine *cl.SeasonDoorFightLine, settleNum uint32) uint32 {
	var winSettleNum uint32 // 战斗全胜的挂机结算场次

	// 获取战斗结果
	results := s.getValidFightResults(fightLine)
	resultsNum := uint32(len(results))
	if resultsNum == 0 {
		l4g.Errorf("calcWinSettleNum: no fightLine Results. fightLine %+v", fightLine)
		return 0
	}

	var loopWinCount uint32
	for _, result := range results {
		if !result.Win {
			continue
		}
		loopWinCount++
	}
	loopNum := settleNum / resultsNum
	winSettleNum += loopNum * loopWinCount

	extraNum := settleNum % resultsNum
	for i := uint32(0); i < extraNum; i++ {
		if i >= uint32(len(results)) {
			continue
		}
		if !results[i].Win {
			continue
		}
		winSettleNum++
	}

	return winSettleNum
}

func (s *SeasonDoor) finishFightLine(srv servicer, doorInfo *goxml.SeasonDoorInfoExt, fightLine *cl.SeasonDoorFightLine) {
	defer s.delFightLine(fightLine.Id)

	// 任务：挂机领奖X次
	s.owner.FireCommonEvent(srv.EventM(), event.AeSeasonDoorFightX, uint64(fightLine.RestSettleNum))

	// 任务：全胜且挂机领奖X次
	winSettleNum := s.calcWinSettleNum(fightLine, fightLine.RestSettleNum)
	if winSettleNum > 0 {
		s.owner.FireCommonEvent(srv.EventM(), event.AeSeasonDoorWinX, uint64(winSettleNum))
	}

	// 日志
	batchFightResult, _ := s.batchFightResult(fightLine, doorInfo, fightLine.RestSettleNum, winSettleNum)
	s.addFightLineLog(doorInfo, fightLine, batchFightResult)
}

// 批量处理战斗结果，用于生成日志
func (s *SeasonDoor) batchFightResult(fightLine *cl.SeasonDoorFightLine, doorInfo *goxml.SeasonDoorInfoExt, settleNum, winSettleNum uint32) (*cl.SeasonDoorBatchFightResult, uint32) {
	var totalWaveRound, winTimes, loseSettleNum uint32
	if settleNum > winSettleNum {
		loseSettleNum = settleNum - winSettleNum
	}
	batchFightResult := &cl.SeasonDoorBatchFightResult{
		WinTimes:  winSettleNum,
		LoseTimes: loseSettleNum,
	}

	// 获取战斗结果
	results := s.getValidFightResults(fightLine)
	resultsNum := uint32(len(results))
	if resultsNum == 0 {
		l4g.Errorf("batchFightResult: no fightLine Results. fightLine %+v", fightLine)
		return batchFightResult, winTimes
	}
	for _, result := range results {
		if result == nil {
			continue
		}
		for _, waveResult := range result.Results {
			if waveResult.Win {
				totalWaveRound += waveResult.Round
			} else {
				totalWaveRound += battle.MaxRoundNum
			}
		}
		if uint32(len(result.Results)) < doorInfo.WaveNum {
			totalWaveRound += battle.MaxRoundNum * (doorInfo.WaveNum - uint32(len(result.Results)))
		}
		batchFightResult.FinnalReportId = result.ReportId
		if result.Win {
			winTimes += 1
		}
	}

	totalWaveTimes := doorInfo.WaveNum * fightLine.FightTimes
	if totalWaveTimes > 0 {
		batchFightResult.AvgRound = float32(math.Round(float64(totalWaveRound)/float64(totalWaveTimes)*10)) / 10
	}

	return batchFightResult, winTimes
}

func (s *SeasonDoor) getValidFightResults(fightLine *cl.SeasonDoorFightLine) []*cl.SeasonDoorFightResult {
	resultsNum := uint32(len(fightLine.Results))
	maxResultsNum := goxml.GetData().SeasonDoorConfigInfoM.FightModelNum
	if resultsNum > maxResultsNum {
		resultsNum = maxResultsNum
	}
	return fightLine.Results[:resultsNum]
}

func (s *SeasonDoor) passDoor(doorInfo *goxml.SeasonDoorInfoExt) {
	s.data.SeasonDoor.PreFightResult[doorInfo.Id] = true
	doorPassLevel := s.data.SeasonDoor.PassedDoors[doorInfo.DoorNum]
	if doorPassLevel < doorInfo.Level {
		s.data.SeasonDoor.PassedDoors[doorInfo.DoorNum] = doorInfo.Level
	}
	s.Save()
}

func (s *SeasonDoor) notPassDoor(doorInfo *goxml.SeasonDoorInfoExt) {
	s.data.SeasonDoor.PreFightResult[doorInfo.Id] = false
	s.Save()
}

func (s *SeasonDoor) addFightLineLog(doorInfo *goxml.SeasonDoorInfoExt,
	fightLine *cl.SeasonDoorFightLine, batchFightResult *cl.SeasonDoorBatchFightResult) {
	fightTeam := s.owner.GetFormationTeam(doorInfo.Formation, 0)
	if fightTeam == nil {
		l4g.Errorf("user: %d addFightLineLog: fightTeam %d is nil", s.owner.ID(), doorInfo.DoorNum)
		return
	}
	// 减少日志数据量
	fightLine.Results = nil
	log := &cl.SeasonDoorFightLineLog{
		Result:      fightLine,
		BatchResult: batchFightResult,
	}
	heroM := s.owner.HeroManager()
	for _, info := range fightTeam.Info {
		hero := heroM.Get(info.Hid)
		if hero == nil {
			continue
		}
		log.HeroSysIds = append(log.HeroSysIds, hero.GetHeroSysID())
	}
	logNum := len(s.data.Logs)
	if logNum >= goxml.GetData().SeasonDoorConfigInfoM.LogMaxNum {
		s.data.Logs = slices.Delete(s.data.Logs, 0, 1)
	}
	s.data.Logs = append(s.data.Logs, log)
	s.Save()
}

func (s *SeasonDoor) IsShopGoodOpen(info *goxml.ShopRegularGoodsInfoExt, _ Servicer) bool {
	for doorNum, passLevel := range s.data.SeasonDoor.PassedDoors {
		doorType := goxml.GetData().SeasonDoorInfoM.GetDoorType(doorNum)
		if info.ExtType == goxml.ShopGoodsSeasonDoorPassLevelLimit1 && doorType == SeasonDoorTypeEquip && passLevel >= info.MinExt {
			return true
		} else if info.ExtType == goxml.ShopGoodsSeasonDoorPassLevelLimit2 && doorType == SeasonDoorTypeMat && passLevel >= info.MinExt {
			return true
		}
	}
	return false
}

func (s *SeasonDoor) Fight(srv servicer, req *cl.C2L_SeasonDoorFight, rsp *cl.L2C_SeasonDoorFight) uint32 {
	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_DOOR), srv) {
		l4g.Errorf("user: %d Fight: function not open", s.owner.ID())
		return uint32(ret.RET_FUNCTION_NOT_OPEN)
	}

	if !helper.CheckBytesLen(req.ClientData, MaxClientDataLen) {
		l4g.Errorf("user: %d Fight: ClientData too long", s.owner.ID())
		return uint32(ret.RET_ERROR)
	}

	//防刷验证
	if !s.owner.CheckAndSetOperateInterval(srv, OISeasonDoor) {
		l4g.Errorf("user: %d Fight: operate too frequently", s.owner.ID())
		return uint32(ret.RET_OPERATE_TOO_OFTEN)
	}

	fightLine := s.getFightLine(req.Id)
	if fightLine == nil {
		l4g.Errorf("user: %d Fight: fightLine is nil,id: %d", s.owner.ID(), req.Id)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	if fightLine.EndTime > 0 {
		l4g.Errorf("user: %d Fight: fightLine is finish,id: %d", s.owner.ID(), req.Id)
		return uint32(ret.RET_SEASON_DOOR_LINE_FIGHT_HAD_FINISH)
	}

	doorInfo := goxml.GetData().SeasonDoorInfoM.Index(req.Id)
	if doorInfo == nil {
		l4g.Errorf("user: %d Fight: SeasonDoorInfo is nil,id: %d", s.owner.ID(), req.Id)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}
	battleReport, result := s.owner.AttackSeasonDoor(srv, doorInfo, req.ClientData)
	if result != ret.RET_OK {
		l4g.Errorf("user: %d Fight: error %d", s.owner.ID(), result)
		return uint32(result)
	}
	rsp.Report = battleReport.Clone()

	if fightLine.RealFightTimes == goxml.GetData().SeasonDoorConfigInfoM.FightModelNum-1 { // 最后一场战斗
		rsp.LastMultiFight = true
	}

	// 生成战斗结果
	fightResult := s.buildFightResult(doorInfo, battleReport, rsp.LastMultiFight, false)
	rsp.Result = fightResult.Clone()

	return s.processFinishFight(srv, doorInfo, fightLine, fightResult, rsp)
}

func (s *SeasonDoor) buildFightResult(doorInfo *goxml.SeasonDoorInfoExt, battleReport *bt.BattleSummary, lastFight, passFight bool) *cl.SeasonDoorFightResult {
	fightResult := &cl.SeasonDoorFightResult{
		ReportId: battleReport.Id,
		Win:      battleReport.Win,
	}
	for _, waveReport := range battleReport.Reports {
		fightResult.Results = append(fightResult.Results, &cl.SeasonDoorWaveFightResult{
			Win:   waveReport.Win,
			Round: waveReport.Round,
		})
	}

	// 挂机战斗才计算挂机时间和掉落组
	if !passFight {
		var restTime, dropGroup uint32
		if fightResult.Win {
			restTime = doorInfo.Rest
		} else {
			restTime = doorInfo.FailRest
		}
		dropGroupIndex := 0
		for _, waveResult := range fightResult.Results {
			if !waveResult.Win {
				continue
			}
			restTime += goxml.GetData().SeasonDoorRestInfoM.GetRestTime(doorInfo.DoorType, doorInfo.Level, waveResult.Round)
			dropGroupIndex++
		}
		if dropGroupIndex > 0 && dropGroupIndex <= len(doorInfo.DropGroups) {
			dropGroup = doorInfo.DropGroups[dropGroupIndex-1]
		}

		if restTime > 0 {
			fightResult.RestTime = int64(restTime)
		}
		if dropGroup != 0 {
			fightResult.DropGroup = dropGroup
		}
	}
	return fightResult
}

func (s *SeasonDoor) isFirst() bool {
	return s.data.First
}

func (s *SeasonDoor) setFirst() {
	s.data.First = true
	s.Save()
}

// 处理每一场战斗结果
func (s *SeasonDoor) processFightResult(srv servicer, doorInfo *goxml.SeasonDoorInfoExt, fightResult *cl.SeasonDoorFightResult) {
	if fightResult.Win {
		s.owner.CognitionManager().Send(srv, doorInfo.Formation, doorInfo.Id, 0, 0)
	} else {
		s.owner.CognitionManager().Del(doorInfo.Formation)
	}

	logData := &log.LogSeasonDoorFight{
		MonsterId:      doorInfo.MonsterGroups,
		BattleReportId: fightResult.ReportId,
		DoorType:       doorInfo.DoorType,
		DoorLevel:      doorInfo.Level,
		DoorNum:        doorInfo.DoorNum,
		Win:            fightResult.Win,
		FightTime:      uint32(fightResult.RestTime),
	}
	s.owner.LogSeasonDoorFight(srv, logData)
}

// 常规战斗
func (s *SeasonDoor) processFinishFight(srv servicer, doorInfo *goxml.SeasonDoorInfoExt, fightLine *cl.SeasonDoorFightLine, fightResult *cl.SeasonDoorFightResult, rsp *cl.L2C_SeasonDoorFight) uint32 {
	// 处理fightLine
	if len(fightLine.Results) < int(goxml.GetData().SeasonDoorConfigInfoM.FightModelNum) {
		fightLine.Results = append(fightLine.Results, fightResult)
	}
	if !rsp.LastMultiFight {
		fightLine.RealFightTimes++
	} else { // 最后一场战斗
		fightLine.RealFightTimes = fightLine.FightTimes
		fightLine.EndTime = time.Now().Unix()
		fightLine.LastSettleTime = time.Now().Unix()

		// 通关逻辑
		isPass := s.processPassDoor(doorInfo, fightLine)
		if isPass {
			rsp.Pass = true
		}

		// 未通关 或 无挂机时间，直接完成挂机
		if !isPass || !s.isRestTimeValid(fightLine) {
			s.delFightLine(fightLine.Id)
			batchFightResult, _ := s.batchFightResult(fightLine, doorInfo, 0, 0)
			s.addFightLineLog(doorInfo, fightLine, batchFightResult)
		}

		// 引导战斗有战斗奖励
		if !s.isFirst() {
			dropGroup := goxml.GetData().SeasonDoorConfigInfoM.FirstDrop
			dropAwards, _ := s.owner.Drop().DoDrop(srv.Rand(), dropGroup)
			retCode, res := s.owner.Award(srv, dropAwards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_REWARD), 0)
			if retCode != uint32(ret.RET_OK) {
				l4g.Errorf("user: %d processFinishFight: send award failed. retCode: %d", s.owner.ID(), retCode)
				return retCode
			}
			s.setFirst()
			rsp.Rewards = res
		}
	}

	// 处理FightResult
	s.processFightResult(srv, doorInfo, fightResult)

	s.Save()

	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) processPassDoor(doorInfo *goxml.SeasonDoorInfoExt, fightLine *cl.SeasonDoorFightLine) bool {
	// 只要有1场战斗胜利，则设置通关状态
	var loseNum uint32
	for _, result := range fightLine.Results {
		if !result.Win {
			loseNum++
		}
	}
	if loseNum == uint32(len(fightLine.Results)) { // 全败
		s.notPassDoor(doorInfo)
		return false
	}

	s.passDoor(doorInfo)
	return true
}

func (s *SeasonDoor) isRestTimeValid(fightLine *cl.SeasonDoorFightLine) bool {
	var totalRestTime int64
	for _, result := range fightLine.Results {
		totalRestTime += result.RestTime
	}
	return totalRestTime > 0
}

func (s *SeasonDoor) altRaisePS(doorInfo *goxml.SeasonDoorInfoExt) *battle.AltRaisePS {
	altRaisePS := &battle.AltRaisePS{}
	formationTeam := s.owner.GetFormationTeam(doorInfo.Formation, 0)
	if formationTeam == nil || len(formationTeam.Info) == 0 {
		return altRaisePS
	}
	buffInfo := goxml.GetData().SeasonDoorBuffInfoM.Index(doorInfo.Id)
	if buffInfo == nil {
		return altRaisePS
	}
	raisePSs := make(map[uint32][]uint64)
	jewelryM := s.owner.SeasonJewelryManager()
	for _, heroV := range formationTeam.Info {
		var minJewelryLv uint32 = math.MaxUint32
		var jewelryNum uint32 = 0
		for _, jewelry := range jewelryM.GetJewelryListByHeroId(heroV.Hid) {
			jewelryInfo := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelry.GetSysId())
			if jewelryInfo == nil {
				continue
			}
			if buffInfo.Link != 0 && jewelryInfo.Link != buffInfo.Link {
				continue
			}
			jewelryNum++
			if jewelryInfo.Level < minJewelryLv {
				minJewelryLv = jewelryInfo.Level
			}
		}
		if minJewelryLv == math.MaxUint32 || minJewelryLv < buffInfo.EquipRequireLv {
			continue
		}
		var activeBuff *goxml.SeasonDoorBuffInfoLimit
		for _, buff := range buffInfo.Limits {
			if buff.EquipRequireNum > jewelryNum {
				break
			}
			activeBuff = buff
		}
		if activeBuff == nil {
			continue
		}
		raisePSs[heroV.Pos] = append(raisePSs[heroV.Pos], activeBuff.RaisePassiveSkillId)
	}
	altRaisePS.AltAttack(raisePSs)

	seasonOpenDay := s.owner.SeasonOpenDay(time.Now().Unix())
	bossWeakInfo := goxml.GetData().SeasonDoorBossWeakInfoM.GetRecordByIdSeasonDayMaxLe(doorInfo.Id, seasonOpenDay)
	if bossWeakInfo != nil {
		bossRaisePSs := make(map[uint32][]uint64)
		info := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(bossWeakInfo.Skill, 1)
		if info != nil {
			bossRaisePSs[battle.TeamUniterBattlePos] = append(bossRaisePSs[battle.TeamUniterBattlePos], info.ID)
			altRaisePS.AltDefense(bossRaisePSs)
		}
	}
	return altRaisePS
}

func (s *SeasonDoor) FightLineViewReward(srv servicer, req *cl.C2L_SeasonDoorFightLineViewReward, rsp *cl.L2C_SeasonDoorFightLineViewReward) uint32 {
	fightLine := s.getFightLine(req.Id)
	if fightLine == nil {
		l4g.Errorf("user: %d FightLineViewReward: fightLine is nil,id: %d", s.owner.ID(), req.Id)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	now := time.Now().Unix()
	if fightLine.EndTime == 0 || now < fightLine.EndTime {
		l4g.Errorf("user: %d FightLineViewReward: fightLine is not finish,id: %d now:%d end:%d ", s.owner.ID(), req.Id, now, fightLine.EndTime)
		return uint32(ret.RET_SEASON_DOOR_LINE_FIGHT_NOT_FINISH)
	}

	s.restSettle(srv, fightLine, now)
	rsp.Rewards = fightLine.Rewards
	rsp.OilRewards = fightLine.OilRewards
	rsp.RestSettleNum = fightLine.RestSettleNum
	rsp.OilOpType = fightLine.OilOpType
	rsp.OccupyOilNum = fightLine.OccupyOilNum

	return uint32(ret.RET_OK)
}

func (s *SeasonDoor) FightLineGetData(srv servicer, rsp *cl.L2C_SeasonDoorGetData) {
	for _, fightLine := range s.getAllFightLines() {
		if fightLine == nil {
			l4g.Errorf("user: %d FightLineGetData: fightLine is nil. ", s.owner.ID())
			continue
		}

		now := time.Now().Unix()
		if fightLine.EndTime == 0 || now < fightLine.EndTime {
			l4g.Errorf("user: %d FightLineGetData: fightLine is not finish,id: %d now:%d end:%d ", s.owner.ID(), fightLine.Id, now, fightLine.EndTime)
			continue
		}

		s.restSettle(srv, fightLine, now)
	}
	rsp.Data = s.Flush()
}

//func (s *SeasonDoor) PassFight(srv servicer, req *cl.C2L_SeasonDoorPassFight, rsp *cl.L2C_SeasonDoorPassFight) uint32 {
//	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_DOOR), srv) {
//		l4g.Errorf("user: %d PassFight: function not open", s.owner.ID())
//		return uint32(ret.RET_FUNCTION_NOT_OPEN)
//	}
//
//	if !helper.CheckBytesLen(req.ClientData, MaxClientDataLen) {
//		l4g.Errorf("user: %d PassFight: ClientData too long", s.owner.ID())
//		return uint32(ret.RET_ERROR)
//	}
//
//	//防刷验证
//	if !s.owner.CheckAndSetOperateInterval(srv, OISeasonDoor) {
//		l4g.Errorf("user: %d PassFight: operate too frequently", s.owner.ID())
//		return uint32(ret.RET_OPERATE_TOO_OFTEN)
//	}
//
//	doorInfo := goxml.GetData().SeasonDoorInfoM.Index(req.Id)
//	if doorInfo == nil {
//		l4g.Errorf("user: %d PassFight: SeasonDoorInfo is nil,id: %d", s.owner.ID(), req.Id)
//		return uint32(ret.RET_SYSTEM_DATA_ERROR)
//	}
//
//	if s.isDoorPassed(doorInfo) {
//		l4g.Errorf("user: %d PassFight: door is passed,id: %d", s.owner.ID(), req.Id)
//		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
//	}
//
//	battleReport, result := s.owner.AttackSeasonDoor(srv, doorInfo, req.ClientData)
//	if result != ret.RET_OK {
//		l4g.Errorf("user: %d PassFight: error %d", s.owner.ID(), result)
//		return uint32(result)
//	}
//	rsp.Report = battleReport.Clone()
//
//	// 生成战斗结果
//	fightResult := s.buildFightResult(doorInfo, battleReport, false, true)
//	rsp.Result = fightResult.Clone()
//
//	// 引导战斗有战斗奖励
//	if !s.isFirst() {
//		dropGroup := goxml.GetData().SeasonDoorConfigInfoM.FirstDrop
//		dropAwards, _ := s.owner.Drop().DoDrop(srv.Rand(), dropGroup)
//		retCode, res := s.owner.Award(srv, dropAwards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_DOOR_FIGHT_REWARD), 0)
//		if retCode != uint32(ret.RET_OK) {
//			l4g.Errorf("user: %d PassFight: send award failed. retCode: %d", s.owner.ID(), retCode)
//			return retCode
//		}
//		s.setFirst()
//		rsp.Rewards = res
//	}
//
//	// 战斗胜利就算通关
//	if fightResult.Win {
//		s.passDoor(doorInfo)
//		rsp.Pass = true
//	} else {
//		s.notPassDoor(doorInfo)
//	}
//
//	return uint32(ret.RET_OK)
//}
