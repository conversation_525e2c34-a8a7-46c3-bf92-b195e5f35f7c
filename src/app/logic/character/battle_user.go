package character

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	"time"

	l4g "github.com/ivanabc/log4go"
)

//nolint:varnamelen
func GetBattleDataFromUser(user *User, formationID uint32) *db.UserBattleData {
	ub := &db.UserBattleData{}
	ub.User = user.GetBattleUser()
	ub.Heroes = make(map[uint64]*cl.HeroBody)
	ub.Rites = make(map[uint32]*cl.Rite)
	ub.Remains = make(map[uint32]*cl.Remain)
	if formationID == 0 {
		for _, heroID := range ub.User.Base.Top5Heros {
			if heroID == 0 {
				break
			}
			hero := user.HeroManager().Get(heroID)
			if hero != nil {
				heroData := hero.GetData()
				ub.Heroes[heroData.Id] = heroData.Clone()
			}
		}
	} else {
		formation := user.GetFormation(formationID)
		if formation == nil {
			l4g.Errorf("%d GetBattleDataFromUser error %d", user.ID(), formationID)
			return nil
		}
		ub.FormationId = formationID
		formations := make(map[uint32]*cl.Formation)
		formations[formationID] = formation.Clone()
		ub.Formations = formations
		for _, t := range formation.Teams {
			if t == nil {
				continue
			}
			for _, v := range t.Info {
				hero := user.HeroManager().Get(v.Hid)
				if hero == nil {
					continue
				}
				heroData := hero.GetData()
				ub.Heroes[heroData.Id] = heroData.Clone()
			}
			if t.RiteInfo != nil && t.RiteInfo.RiteId > 0 {
				rite := user.RiteManager().GetRite(t.RiteInfo.RiteId)
				if rite != nil {
					ub.Rites[t.RiteInfo.RiteId] = rite.GetData().Clone()
				}
			}
			for _, v := range t.RemainInfo {
				if v == nil {
					continue
				}
				remain := user.RemainM().GetRemain(v.GetId())
				if remain != nil {
					ub.Remains[remain.GetData().Id] = remain.GetData().Clone()
				}
			}
		}
	}
	ub.Equips = make(map[uint64]*cl.Equipment)
	ub.Artifacts = make(map[uint32]*cl.Artifact)
	ub.Emblems = make(map[uint64]*cl.EmblemInfo)
	ub.GuildMsg = &db.GuildMsgForBattle{
		Id:   user.UserGuild().GuildID(),
		Name: user.UserGuild().GuildName(),
	}
	ub.Skins = make(map[uint32]*cl.Skin)
	ub.SeasonJewelry = make(map[uint64]*cl.SeasonJewelry)
	for _, hero := range ub.Heroes {
		for _, eid := range hero.Equipment { // 装备
			equip := user.EquipManager().Get(eid)
			if equip != nil {
				ub.Equips[equip.Data.Id] = equip.Data.Clone()
			}
		}

		for _, v := range hero.Emblem { // 纹章
			emblem := user.EmblemManager().Get(v)
			if emblem != nil {
				ub.Emblems[emblem.Data.Id] = emblem.Data.Clone()
			}
		}

		//皮肤
		skin := user.SkinManager().GetSkinByHeroIDByBattle(hero.GetSysId())
		if skin != nil {
			ub.Skins[skin.GetID()] = skin.Flush()
		}

		//赛季装备
		if goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix()) == user.GetSeasonID() {
			for _, jewelry := range user.SeasonJewelryManager().GetJewelryListByHeroId(hero.GetId()) {
				if jewelry == nil {
					continue
				}
				ub.SeasonJewelry[jewelry.GetId()] = jewelry.Clone().Data
			}
		}
	}
	for _, v := range user.ArtifactManager().FlushAll() {
		ub.Artifacts[v.SysId] = v
	}
	ub.User.AchievementsShowcase = user.AchievementsShowcase().getData().Clone()
	// 赛季羁绊(确保加载的是本赛季的数据)
	if goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix()) == user.GetSeasonID() {
		ub.SeasonLinkActivation = user.SeasonLink().GetActivatedHeroes()
		ub.Monuments = user.SeasonLink().FlushMonuments()
	}
	ub.AreaId = user.GetArea()
	ub.Duel = user.Duel().Flush()
	ub.Titles = user.TitleManager().FlushData()
	return ub
}

func GetUserFromUserBattleData(srv servicer, ub *db.UserBattleData) *User {
	if ub == nil {
		return nil
	}
	RepairBattleData(srv, ub)
	user := &User{}
	user.init(ub.User.Uuid, ub.User.ServerId, UserInitFromBattleUser)
	if ub.GuildMsg != nil {
		user.UserGuild().Update(ub.GuildMsg.Id, ub.GuildMsg.Name) // 更新userGuild中缓存的信息
	}

	dbUser := NewDbUserFromDbBattleUser(ub.User)
	user.loadDbUserForBattleData(dbUser)
	user.FormationManager().Load(ub.Formations) //先加载阵容
	user.EquipManager().Load(ub.Equips)         //装备需要先于英雄加载
	user.ArtifactManager().load(ub.Artifacts)
	user.EmblemManager().Load(ub.Emblems)
	user.SeasonJewelryManager().Load(ub.SeasonJewelry) //赛季装备需要先于英雄加载
	user.PokemonManager().load(ub.Pokemons)            // 宠物需要先于英雄加载
	user.HeroManager().Load(ub.Heroes, true)
	user.loadCrystal(dbUser.Crystal, false) //需要在英雄后加载
	user.SkinManager().Load(ub.Skins, false)
	user.RiteManager().Load(ub.Rites)
	user.areaId = ub.AreaId
	user.Duel().Load(ub.Duel)
	user.TitleManager().Load(ub.Titles)

	// TODO: 赛季相关养成的处理。因为玩家的赛季是需要玩家主动触发重置的，所以离线玩家的数据可能是上赛季的。
	// 新赛季还未进行重置时，不加载玩家的上赛季羁绊激活数据和丰碑数据
	if goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix()) == user.GetSeasonID() {
		user.SeasonLink().LoadMonument(ub.Monuments)
		user.SeasonLink().LoadActivation(ub.SeasonLinkActivation)
		user.RemainM().load(ub.Remains)
		user.SetSeasonAddPower(user.CalcSeasonAddPower())
	}

	return user
}

func (u *User) GetOfflineUser() *db.OfflineUser {
	return &db.OfflineUser{
		Uuid:       u.dbUser.Uuid,
		ServerId:   u.dbUser.ServerId,
		Id:         u.dbUser.Id,
		Name:       u.dbUser.Name,
		Base:       u.dbUser.Base.Clone(),
		BaseOp:     u.dbUser.BaseOp,
		Resource:   u.dbUser.Resource.Clone(),
		ResourceOp: u.dbUser.ResourceOp,
	}
}

func (u *User) GetBattleUser() *db.BattleUser {
	return &db.BattleUser{
		Uuid:               u.dbUser.Uuid,
		ServerId:           u.dbUser.ServerId,
		Id:                 u.dbUser.Id,
		Name:               u.dbUser.Name,
		Base:               u.dbUser.Base.Clone(),
		BaseOp:             u.dbUser.BaseOp,
		Resource:           u.dbUser.Resource.Clone(),
		ResourceOp:         u.dbUser.ResourceOp,
		ModuleGlobalAttr:   u.dbUser.ModuleGlobalAttr.Clone(),
		ModuleGlobalAttrOp: u.dbUser.ModuleGlobalAttrOp,
		Crystal:            u.dbUser.Crystal.Clone(),
		CrystalOp:          u.dbUser.CrystalOp,
	}
}

//nolint:funlen
func (u *User) FlushBattleData(srv servicer, formationID uint32, isSimple bool) *cl.UserBattleData {
	ub := &cl.UserBattleData{}
	ub.User = u.NewUserSnapshot(formationID)
	if isSimple {
		ub.Heroes = make([]*cl.Hero, 0, FormationMaxPos)
		ub.Equips = make([]*cl.Equipment, 0, FormationMaxPos*4)                                        //nolint:mnd
		ub.Emblems = make([]*cl.EmblemInfo, 0, FormationMaxPos*4)                                      //nolint:mnd
		ub.SeasonJewelryList = make([]*cl.SeasonJewelry, 0, FormationMaxPos*goxml.SeasonJewelryPosMax) //nolint:mnd
		for _, heroID := range ub.User.Top5Heros {
			if heroID == 0 {
				break
			}
			hero := u.HeroManager().Get(heroID)
			if hero == nil {
				l4g.Errorf("user: %d FlushBattleData: hero not exist. hid:%d", u.ID(), heroID)
				return nil
			}
			ub.Heroes = append(ub.Heroes, hero.Flush())
			for _, eid := range hero.data.Equipment {
				equip := u.EquipManager().Get(eid)
				if equip == nil {
					continue
				}
				ub.Equips = append(ub.Equips, equip.Flush())
			}
			for _, eid := range hero.data.Emblem {
				emblem := u.EmblemManager().Get(eid)
				if emblem == nil {
					continue
				}
				ub.Emblems = append(ub.Emblems, emblem.Data.Clone())
			}
			for _, jewelry := range u.SeasonJewelryManager().GetJewelryListByHeroId(heroID) {
				ub.SeasonJewelryList = append(ub.SeasonJewelryList, jewelry.Flush())
			}
		}
		ub.Artifacts = u.ArtifactManager().GetTopThree()
	} else {
		formation := u.GetFormation(formationID)
		if formation == nil {
			l4g.Errorf("user: %d FlushBattleData error %d", u.ID(), formationID)
			return nil
		}
		ub.FormationId = formationID
		ub.Formation = formation.Clone()
		ub.Heroes = make([]*cl.Hero, 0, len(ub.Formation.Teams)*int(FormationMaxPos))
		ub.Equips = make([]*cl.Equipment, 0, len(ub.Formation.Teams)*int(FormationMaxPos)*4)   //nolint:mnd
		ub.Emblems = make([]*cl.EmblemInfo, 0, len(ub.Formation.Teams)*int(FormationMaxPos)*4) //nolint:mnd
		ub.Artifacts = make([]*cl.Artifact, 0, 3)                                              //nolint:mnd
		ub.Rites = make([]*cl.Rite, 0, len(ub.Formation.Teams))
		ub.Remains = make([]*cl.Remain, 0, len(ub.Formation.Teams)*3)                                                                    //nolint:mnd
		ub.SeasonJewelryList = make([]*cl.SeasonJewelry, 0, len(ub.Formation.Teams)*int(FormationMaxPos)*int(goxml.SeasonJewelryPosMax)) //nolint:mnd

		for _, team := range ub.Formation.Teams {
			if team == nil {
				continue
			}
			for _, v := range team.Info {
				hero := u.HeroManager().Get(v.Hid)
				if hero == nil {
					continue
				}
				ub.Heroes = append(ub.Heroes, hero.Flush())
				for _, eid := range hero.data.Equipment {
					equip := u.EquipManager().Get(eid)
					if equip == nil {
						continue
					}
					ub.Equips = append(ub.Equips, equip.Flush())
				}
				for _, eid := range hero.data.Emblem {
					emblem := u.EmblemManager().Get(eid)
					if emblem == nil {
						continue
					}
					ub.Emblems = append(ub.Emblems, emblem.Data.Clone())
				}
				for _, jid := range hero.data.SeasonJewelry {
					jewelry := u.SeasonJewelryManager().Get(jid)
					if jewelry == nil {
						continue
					}
					ub.SeasonJewelryList = append(ub.SeasonJewelryList, jewelry.Flush())
				}
			}
			for _, v := range team.Artifacts {
				artifact := u.ArtifactManager().GetArtifact(v.Aid)
				if artifact == nil {
					continue
				}
				ub.Artifacts = append(ub.Artifacts, artifact.GetData().Clone())
			}
			if team.RiteInfo != nil && team.RiteInfo.RiteId > 0 {
				rite := u.RiteManager().GetRite(team.RiteInfo.RiteId)
				if rite != nil {
					ub.Rites = append(ub.Rites, rite.GetData().Clone())
				}
			}
			for _, v := range team.RemainInfo {
				remain := u.RemainM().GetRemain(v.Id)
				if remain == nil {
					continue
				}
				ub.Remains = append(ub.Remains, remain.GetData().Clone())
			}
		}
	}
	if ub.GlobalAttr == nil {
		ub.GlobalAttr = &cl.GlobalAttr{}
	}
	ub.GlobalAttr.MemoryAttr = u.Memory().FlushMemoryGlobalAttr()
	ub.GlobalAttr.ArtifactAttr = u.ArtifactManager().FlushArtifactGlobalAttr()
	ub.GlobalAttr.GuildAttr = u.GuildTalent().FlushGuildTalentGlobalAttr()
	ub.GlobalAttr.HandbookAttr = u.HandbookManager().FlushHandbookGlobalAttr()
	// ub.GlobalAttr.CrystalBlessingAttr = u.Crystal().FlushCrystalGlobalAttrs()
	ub.GlobalAttr.GoddessContractAttr = u.GoddessContract().FlushGoddessGlobalAttr()

	ub.ArtifactNum = u.ArtifactManager().GetArtifactNum()
	// ub.CrystalAttr = u.Crystal().FlushShareAttr()
	// ub.ResonanceHeroes = u.Crystal().FlushResonanceHids()
	ub.ShareGrowth = u.ShareGrowth().Flush()

	// 获取展示成就
	ub.Achieves = u.AchievementsShowcase().FlushAchieve(srv)

	ub.Skins = u.SkinManager().FlushAll()
	ub.SeasonLink = u.SeasonLink().Flush()
	ub.SeasonAdd = u.GetSeasonAddData()
	if u.RemainM() != nil && u.RemainM().remainBook != nil {
		ub.RemainBookLevel = u.RemainM().remainBook.Level()
	}
	ub.TalentTreeCul = u.TalentTree().FlushCultivate()
	ub.Duel = u.Duel().Flush()
	return ub
}

func NewDbUserFromDbBattleUser(data *db.BattleUser) *db.User {
	return &db.User{
		Uuid:                   data.Uuid,
		ServerId:               data.ServerId,
		Id:                     data.Id,
		Name:                   data.Name,
		Base:                   data.Base.Clone(),
		BaseOp:                 data.BaseOp,
		Resource:               data.Resource.Clone(),
		ResourceOp:             data.ResourceOp,
		ModuleGlobalAttr:       data.ModuleGlobalAttr.Clone(),
		ModuleGlobalAttrOp:     data.ModuleGlobalAttrOp,
		Crystal:                data.Crystal.Clone(),
		CrystalOp:              data.CrystalOp,
		AchievementsShowcase:   data.AchievementsShowcase.Clone(),
		AchievementsShowcaseOp: data.AchievementsShowcaseOp,
	}
}

func NewUBDOnlyPowerFromUser(u *User, formationID uint32) *db.UserBattleData {
	if u == nil {
		return nil
	}
	defPower := u.CalFormationPower(formationID)
	bu := newUBDOnlyPower(u.ServerID(), u.ID(), formationID, defPower)
	return bu
}

func NewUBDOnlyPowerFromUBD(srv servicer, bu *db.UserBattleData, formationID uint32) *db.UserBattleData {
	if bu == nil {
		return nil
	}
	newUser := GetUserFromUserBattleData(srv, bu)
	if newUser != nil {
		defPower := newUser.CalFormationPower(formationID)
		u := newUBDOnlyPower(newUser.ServerID(), newUser.ID(), formationID, defPower)
		return u
	} else {
		return nil
	}
}

func newUBDOnlyPower(sid, uid uint64, formationID uint32, power int64) *db.UserBattleData {
	return &db.UserBattleData{
		User: &db.BattleUser{
			ServerId: sid,
			Id:       uid,
		},
		FormationId: formationID,
		Power:       power,
	}
}
