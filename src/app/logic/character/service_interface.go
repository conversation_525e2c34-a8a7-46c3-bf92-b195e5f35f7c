package character

import (
	"app/hotfix"
	"app/logic/account"
	"app/logic/cross"
	"app/logic/etcdcache"
	"app/logic/helper/log"
	"app/logic/mongo"
	"app/logic/multilang"
	"app/logic/oss"
	"app/logic/rank"
	"app/logic/rankachieve"
	"app/logic/request"
	"app/logic/session"
	"app/protos/in/config"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"

	"github.com/gogo/protobuf/proto"

	plog "app/protos/in/log"

	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/platform/proto/da"
)

// 对外
//
//nolint:iface
type Servicer interface {
	servicer
}

// character中使用到的LogicService接口
//
//nolint:iface
type servicer interface {
	//管理器
	Rand() *rand.Rand
	GateM() *session.Manager
	EventM() *event.Manager
	UserM() *UserManager
	GetGlobalRank(rank.ID) *rank.Global
	SmallRankM() *rank.SmallRankManager
	CommonRankM() *rank.CommonRankManager
	RankAchieveM() *rankachieve.Manager
	MultiLangM() *multilang.Manager
	ReqTimeM() *request.TimeOutManager
	BattleDebug() bool
	AccountM() *account.Manager
	CrossM() *cross.Manager
	CrossMasterM() *cross.CrossMasterManager
	GetTimeZoneAndOffest() string
	MongoM() *mongo.Manager
	OSSM() *oss.OSSManager
	ActivityMixM() *etcdcache.ActivityMixManager
	LinkSetting() *etcdcache.LinkSettingManager
	CouponCfgM() *etcdcache.CouponCfgManager
	RefundMailTemplateM() *etcdcache.RefundMailTemplateManager
	FlowerPushFrequencyLimitDelete(uint64)
	GetHotfixManager() *hotfix.HotfixManager

	//方法
	SendCmdToCrossActivityPartition(uint32, uint32, uint32, interface{}) bool
	SendCmdToDB(uint32, uint64, interface{})
	SendCmdToCross(l2c.ID, uint64, interface{}) bool
	SendRemoteLogic(uint64, l2c.REMOTE_LOGIC, interface{}) bool
	BroadcastCmdToClient(cl.ID, interface{})
	SendCmdToPlatform(uint32, uint64, interface{})
	BroadcastCmdByOpIDAndChannel(cl.ID, interface{}, []uint32, []uint32)
	BroadCastCmdByUids([]uint64, cl.ID, interface{})
	CreateUniqueID() uint64
	CreateLogID() uint64
	CreateUserLogID() uint64
	CreateAsyncMessage(interface{}, int64) uint64 //传递一个消息和超时时间
	NewLogMessage() *da.Log
	WriteLogMessage(msg *da.Log)
	DeleteTimerID(uint32) //删除一个定时器
	SendCmdToOSS(uint32, uint64, interface{}, common.FUNCID)
	AddAchievementsShowcase(uint64, *cl.AchievementsShowcase)
	AddTargetCmdMes(uint32, proto.Message)
	SimulationSendCmdToCross(l2c.ID, l2c.ID, uint64) bool
	FixActivityTime(now, startTm, endTm int64, protectDay uint32) (bool, int64)
	GetUserGuildLevel(uint64) uint32
	GetGuildID(uid uint64) uint64
	GetGuildMedal(uid uint64) *db.LogicGuildMedal
	GetGuildDungeonChatRoomID(uid uint64) uint64
	GetGstChatRoomID(uid uint64) string
	GetCrossArea(uint32) uint32
	GetNormalPartSids() []uint64
	DelDropActivityFlowerOccupy(uid uint64, itemRes map[uint32]map[uint32]*cl.Resource) []*cl.Resource
	SyncGstFormation(user *User)
	SyncGstUserInfo(user *User)
	SyncGstBuildDispatch(user *User, hid uint64)
	GetLogicGuild(uid uint64) *db.LogicGuild
	GetPgpIdsByUser(uid uint64) []uint32
	SyncSeasonArenaSnapshot(user *User)
	GetSeasonComplianceCurStage(user *User) *cl.SeasonComplianceStage
	NewLog(plog.LOG_TYPE) interface{}
	WriteLog(plog.LOG_TYPE, log.LogI)

	//配置
	AppID() uint64
	ServerID() uint64
	CheckLocalServer(uint64) bool
	OpGroup() uint64
	StartServiceTm() int64
	ServerDay(now int64) uint32
	ServerOpenDay(now int64) uint32 // 新增获取开服天数的方法，解决ServerDay 没有判断是否开服的问题
	GetUserSaveInterval() int64
	EnableTraceLog(uid uint64) bool
	GetUserCacheInterval() int64
	ServerName() string
	GetFunctionStatus(uint32) bool
	ServiceStatus() int32
	LogTopicDC() string
	LogTopicResource() string
	ServerType() string
	PlatformConfig() *config.Platform
	GetActivityScheduleConfig() *cl.ActivityScheduleDatas
}
