package character

import (
	"app/goxml"
	"app/protos/out/cl"
	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type PassData cl.PassData

func (p *PassData) newActivePassData() {
	p.ActivePass = &cl.PassActive{}
	p.ActivePass.ActiveTime = time.Now().Unix()
	p.ActivePass.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	p.ActivePass.ReceivePoint = make(map[uint32]bool)
}

// 自动处理积分相关逻辑
func (p *PassData) autoHandlePoint(srv servicer, user *User) {
	for typeID := range p.ActivePass.TaskProgress {
		for _, task := range goxml.GetData().PassCycleTaskInfoM.GetTasksByTypeID(p.SysId, typeID) {
			// 是否已领取过积分
			if p.ActivePass.ReceivePoint[task.Id] {
				continue
			}
			// 检查任务是否完成
			if !p.checkTaskFinish(user, task.TypeId, task.Value) {
				continue
			}
			p.updatePoint(srv, user, task.Id, task.Point)
		}
	}
}

func (p *PassData) checkTaskFinish(user *User, typeID, value uint32) bool {
	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(typeID)
	if taskTypeInfo == nil {
		return false
	}

	var taskProgress *cl.TaskTypeProgress
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		taskProgress = user.CalcTaskProgress(taskTypeInfo)
	} else {
		taskProgress = p.ActivePass.TaskProgress[taskTypeInfo.Id]
	}

	if !user.CheckTaskFinish(taskProgress, typeID, uint64(value)) {
		return false
	}

	return true
}

func (p *PassData) updatePoint(srv servicer, user *User, id, point uint32) {
	p.ActivePass.ReceivePoint[id] = true
	// 触发增加积分的任务
	typeID, exist := goxml.GetData().PassTaskInfoM.GetPassEventTypeID(p.SysId)
	if !exist {
		l4g.Errorf("user:%d update point GetPassEventTypeID failed passID:%d", user.ID(), p.SysId)
		return
	}
	progress, exist := p.TaskProgress[typeID]
	if !exist {
		p.TaskProgress[typeID] = &cl.TaskTypeProgress{
			TaskTypeId: typeID,
		}
		progress = p.TaskProgress[typeID]
	}
	progress.Progress += uint64(point)
}

//// 每天的零点都需要重置周期任务的进度及积分领取状态
//// 周期任务重置：分为每日重置和每周一重置
//func (p *PassData) initCycleTask() {
//	// 每周一重置
//	if helper.Weekday(time.Now().Unix()) == 1 {
//		p.ActivePass.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
//		p.ActivePass.ReceivePoint = make(map[uint32]bool)
//	} else {
//		// 每日重置
//		for taskID := range p.ActivePass.ReceivePoint {
//			task :=  goxml.GetData().PassCycleTaskInfoM.Index(taskID)
//			if task == nil {
//				continue
//			}
//			if task.ResetType == goxml.PassCycleTaskResetDaily {
//				delete(p.ActivePass.TaskProgress, task.TypeId)
//				delete(p.ActivePass.ReceivePoint, task.Id)
//			}
//		}
//	}
//}

func (p *PassData) ResetCycleTask(resetType uint32) {
	for taskID := range p.ActivePass.ReceivePoint {
		taskInfo := goxml.GetData().PassCycleTaskInfoM.Index(taskID)
		if taskInfo == nil {
			continue
		}
		if taskInfo.PassId != p.SysId {
			continue
		}
		if taskInfo.ResetType == resetType {
			delete(p.ActivePass.TaskProgress, taskInfo.TypeId)
			delete(p.ActivePass.ReceivePoint, taskInfo.Id)
		}
	}

}

//// 检查是否周期结束
//func (p *PassData) checkCycleFinish(srv servicer, userID uint64) bool {
//	passInfo :=  goxml.GetData().PassInfoM.Index(p.SysId)
//	if passInfo == nil {
//		l4g.Errorf("user: %d checkCycleFinish failed, passInfo is nil. passID: %d", userID, p.SysId)
//		return false
//	}
//	now := time.Now().Unix()
//	openDay := srv.ServerDay(now)
//	//开服天数 <= 解锁天数
//	if openDay <= passInfo.UnlockDay {
//		return false
//	}
//	// 开服天数 - 解锁天数 < 周期天数，未到重置时间
//	pastDay := openDay - passInfo.UnlockDay
//	if pastDay < passInfo.Cycle {
//		return false
//	}
//	// 周期天数的整倍数，到了重置时间
//	if pastDay%passInfo.Cycle == 0 {
//		return true
//	}
//
//	// 离线玩家错过重置时间，再次上线检查是否需要重置
//	return p.checkPassRound(srv, passInfo)
//}
//
//// 检查玩家身上的轮次和当前轮次，如果不一样，重置
//func (p *PassData) checkPassRound(srv servicer, passInfo *goxml.PassInfo) bool {
//	playerRound := p.calcPassRound(srv, passInfo, p.ActivePass.ActiveTime)
//	curRound := p.calcPassRound(srv, passInfo, time.Now().Unix())
//	return playerRound != curRound
//}
//
//func (p *PassData) calcPassRound(srv servicer, passInfo *goxml.PassInfo, realTime int64) uint32 {
//	realDay := srv.ServerDay(realTime) - passInfo.UnlockDay
//	return realDay / passInfo.Cycle
//}

// 计算补发的奖励
func (p *PassData) calcReissueAward() []*cl.Resource {
	awards := make([]*cl.Resource, 0, 10)
	for _, progress := range p.TaskProgress {
		if progress.Progress == 0 {
			continue
		}

		for _, task := range goxml.GetData().PassTaskInfoM.GetTaskInfos(p.SysId, progress.TaskTypeId) {
			// 任务进度没有完成
			if uint64(task.Value) > progress.Progress {
				continue
			}

			freeAward, chargeAward := p.GetTaskAward(task)
			awards = append(awards, freeAward...)
			awards = append(awards, chargeAward...)
		}
	}

	return awards
}

func (p *PassData) GetTaskAward(taskInfo *goxml.PassTaskInfo) ([]*cl.Resource, []*cl.Resource) {
	var freeAward, chargeAward []*cl.Resource
	receiveState, ok := p.Receive[taskInfo.Id]
	if !ok {
		receiveState = &cl.PassReceiveSate{Id: taskInfo.Id}
	}

	if !receiveState.Free {
		freeAward = taskInfo.FreeClRes
	}
	if !receiveState.Charge && p.Buy {
		chargeAward = append(chargeAward, taskInfo.AwardClRes...)
	}
	if !receiveState.Charge2 && p.Buy2 {
		chargeAward = append(chargeAward, taskInfo.Award2ClRes...)
	}

	return freeAward, chargeAward
}

func (p *PassData) UpdateTaskProgress(pointMap map[uint32]uint64) map[uint32]*cl.TaskTypeProgress {
	retProgress := make(map[uint32]*cl.TaskTypeProgress, len(pointMap))
	for taskID, point := range pointMap {
		if progress, exist := p.TaskProgress[taskID]; exist {
			progress.Progress += point
			retProgress[taskID] = progress.Clone()
		} else {
			progress = &cl.TaskTypeProgress{
				TaskTypeId: taskID,
				Progress:   point,
			}
			p.TaskProgress[taskID] = progress
			retProgress[taskID] = progress.Clone()
		}
	}

	return retProgress
}

func (p *PassData) SetReceiveState(ids []uint32) []*cl.PassReceiveSate {
	ret := make([]*cl.PassReceiveSate, 0, len(ids))
	for _, id := range ids {
		ReceiveSate, ok := p.Receive[id]
		if !ok {
			ReceiveSate = &cl.PassReceiveSate{Id: id}
			p.Receive[id] = ReceiveSate
		}
		ReceiveSate.Free = true
		if p.Buy {
			ReceiveSate.Charge = true
		}
		if p.Buy2 {
			ReceiveSate.Charge2 = true
		}
		ret = append(ret, ReceiveSate.Clone())
	}

	return ret
}

// IsReceivedAward : 是否已领取过奖励
func (p *PassData) CanReceivedAward(taskID uint32) bool {
	receiveState := p.Receive[taskID]
	if receiveState == nil {
		return true
	}
	freeState := !receiveState.Free
	charge1State := p.canRecvChargeAward(p.Buy, receiveState.Charge)
	charge2State := p.canRecvChargeAward(p.Buy2, receiveState.Charge2)

	return freeState || charge1State || charge2State
}

func (p *PassData) canRecvChargeAward(charge bool, recv bool) bool {
	if !charge {
		return false
	}
	return charge && !recv
}

func (p *PassData) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo, user *User) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return user.CalcTaskProgress(taskTypeInfo)
	}
	return p.TaskProgress[taskTypeInfo.Id]
}
