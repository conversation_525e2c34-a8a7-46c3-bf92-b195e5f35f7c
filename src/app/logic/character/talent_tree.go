package character

import (
	"app/goxml"
	"app/logic/battle"
	aevent "app/logic/event"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"maps"
	"slices"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type TalentTree struct {
	owner *User

	cultivate *cl.TalentTreeCultivate
	base      *cl.TalentTreeBase
}

func newTalentTree(user *User) *TalentTree {
	t := &TalentTree{
		owner: user,
	}
	t.cultivate = &cl.TalentTreeCultivate{
		Levels: make(map[uint32]uint32),
	}
	t.base = &cl.TalentTreeBase{
		TaskTypeProgress: make(map[uint32]*cl.TaskTypeProgress),
		Awarded:          make(map[uint32]bool),
	}
	return t
}

// level数据涉及战斗时的被动技能，放在GlobalAttr中，方便离线获取，所以单独加载
func (t *TalentTree) loadLv(cultivate *cl.TalentTreeCultivate) {
	t.cultivate = cultivate
	if t.cultivate == nil {
		t.cultivate = &cl.TalentTreeCultivate{}
	}
	if t.cultivate.Levels == nil {
		t.cultivate.Levels = make(map[uint32]uint32)
	}
}

// 基本数据
func (t *TalentTree) loadBase(base *cl.TalentTreeBase) {
	t.base = base
	if t.base == nil {
		t.base = &cl.TalentTreeBase{}
	}
	if t.base.TaskTypeProgress == nil {
		t.base.TaskTypeProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if t.base.Awarded == nil {
		t.base.Awarded = make(map[uint32]bool)
	}
}

func (t *TalentTree) Owner() *User {
	return t.owner
}

func (t *TalentTree) save() {
	t.saveBase()
	t.Owner().dbUser.ModuleGlobalAttr.TalentTreeCul = t.cultivate
	t.Owner().setSaveTag(saveTagModuleGlobalAttr)
}

func (t *TalentTree) saveBase() {
	t.Owner().dbUser.Module6.TalentTree = t.base
	t.Owner().setSaveTag(saveTagModule6)
}

func (t *TalentTree) Flush() (*cl.TalentTreeBase, *cl.TalentTreeCultivate) {
	return t.base.Clone(), t.cultivate.Clone()
}

func (t *TalentTree) FlushCultivate() *cl.TalentTreeCultivate {
	return t.cultivate.Clone()
}

func (t *TalentTree) GetNodeNum() int {
	if t.cultivate == nil || t.cultivate.Levels == nil {
		return 0
	}
	return len(t.cultivate.Levels)
}

func (t *TalentTree) GetLevel(id uint32) uint32 {
	return t.cultivate.Levels[id]
}

func (t *TalentTree) CheckLevelUp(targetLevelInfo *goxml.SeasonTalentTreeLevelInfo, targetBaseInfo *goxml.SeasonTalentTreeBaseInfo,
	levelUpedNode map[uint32]uint32) bool {
	seasonID := t.owner.GetSeasonID()

	// sectCheck & costCheck
	sectCheck := false
	if targetBaseInfo.LinkSkillSect > 0 {
		sectCheck = true
	}

	costCheck := false
	if targetLevelInfo.PreconditionCostLimit > 0 {
		costCheck = true
	}

	if !sectCheck && !costCheck {
		return true
	}
	levels := make(map[uint32]uint32)
	maps.Copy(levels, t.cultivate.Levels)
	maps.Copy(levels, levelUpedNode)
	nodeType, totalCost := targetBaseInfo.NodeType, uint32(0)
	// 遍历所有已养成节点
	for cultivateID, cultivateLevel := range levels {
		cultivateInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(seasonID, cultivateID)
		if cultivateInfo == nil {
			continue
		}

		if nodeType != cultivateInfo.NodeType {
			continue
		}

		if cultivateInfo.BelongPage != targetBaseInfo.BelongPage {
			continue
		}

		// sectCheck:同一类型节点，同一页签，只能选择一侧分区进行养成
		if sectCheck {
			if nodeType == goxml.TalentTreeEggBlessing {
				if cultivateInfo.LinkSkillSect != 0 && cultivateID != targetBaseInfo.Id &&
					cultivateInfo.LinkSkillSect == targetBaseInfo.LinkSkillSect {
					l4g.Errorf("user:%d CanUpdateCultivate: sectCheck failed. targetId:%d cultivateId:%d", t.owner.ID(), targetBaseInfo.Id, cultivateInfo.Id)
					return false
				}
			} else {
				if cultivateInfo.LinkSkillSect != 0 && cultivateInfo.LinkSkillSect != targetBaseInfo.LinkSkillSect {
					l4g.Errorf("user:%d CanUpdateCultivate: sectCheck failed. targetId:%d cultivateId:%d", t.owner.ID(), targetBaseInfo.Id, cultivateInfo.Id)
					return false
				}
			}
		}

		if costCheck {
			treeLevelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(cultivateID, cultivateLevel)
			if treeLevelInfo == nil {
				continue
			}
			totalCost += treeLevelInfo.CostCountSum
		}
	}

	// costCheck:同一类型节点，同一页签，需要前置消耗多少点数，才能养成目标节点
	if costCheck && totalCost < targetLevelInfo.PreconditionCostLimit {
		l4g.Errorf("user:%d CanUpdateCultivate: costCheck failed. totalCost %d < limit %d, targetId %d", t.owner.ID(), totalCost, targetLevelInfo.PreconditionCostLimit, targetLevelInfo.Id)
		return false
	}

	return true
}

func (t *TalentTree) SetLevel(id, level uint32) {
	t.cultivate.Levels[id] = level
	t.save()
}

func (t *TalentTree) getLevelPower() uint32 {
	rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(t.Owner().GetSeasonID())
	lv := t.GetRootLevel()
	info := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(rootId, lv)
	if info == nil {
		return 0
	}
	return info.Power
}

func (t *TalentTree) GetResetTimes() uint32 {
	return t.base.GetResetTimes()
}

func (t *TalentTree) CalcResetReturnAwards(resetNodeIds []uint32) []*cl.Resource {
	totalAwards := make([]*cl.Resource, 0, 1000) //nolint:mnd
	for _, id := range resetNodeIds {
		level := t.GetLevel(id)
		for i := uint32(1); i <= level; i++ {
			info := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, i)
			if info == nil {
				l4g.Errorf("user:%d CalcResetReturnAwards: level info not exist. id:%d i:%d", t.Owner().ID(), id, i)
				return nil
			}
			totalAwards = append(totalAwards, info.Cost...)
		}
	}
	return totalAwards
}

func (t *TalentTree) ResetCultivationByNodeIds(resetNodeIds []uint32) {
	for _, id := range resetNodeIds {
		delete(t.cultivate.Levels, id)
	}
	t.save()
}

// GetRootLevel
// @Description: 获取树根等级
// @receiver t
// @return uint32
func (t *TalentTree) GetRootLevel() uint32 {
	rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(t.owner.GetSeasonID())
	return t.GetLevel(rootId)
}

func (t *TalentTree) OnSeasonTalentTreeEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	if !t.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_TALENT_TREE), srv) { // 是否解锁赛季
		return
	}

	p, change := t.owner.TaskTypeOnEvent(t.base.TaskTypeProgress, event, progress, values)
	if change {
		t.update(p)
		t.save()
	}
}

func (t *TalentTree) update(progress map[uint32]*cl.TaskTypeProgress) {
	smsg := &cl.L2C_TalentTreeTaskUpdate{
		Ret:      uint32(ret.RET_OK),
		Progress: progress,
	}
	t.owner.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeTaskUpdate, smsg)
}

func (t *TalentTree) GetOneTypeProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return t.owner.CalcTaskProgress(taskTypeInfo)
	}
	return t.base.TaskTypeProgress[taskTypeInfo.Id]
}

func (t *TalentTree) IsAwarded(id uint32) bool {
	if _, exist := t.base.Awarded[id]; exist {
		return true
	}
	return false
}

func (t *TalentTree) ReceiveAward(ids []uint32) {
	for _, id := range ids {
		t.base.Awarded[id] = true
	}
	t.save()
}

func (t *TalentTree) CheckSeasonLinkValid(nodeInfo *goxml.SeasonTalentTreeBaseInfo, activatedLinks []*goxml.LinkSkillInfoExt) bool {
	equalFunc := func(linkSkillInfo *goxml.LinkSkillInfoExt) bool {
		if linkSkillInfo.LinkId == nodeInfo.SeasonLink {
			if nodeInfo.LinkNumLimt > 0 {
				if linkSkillInfo.Num >= nodeInfo.LinkNumLimt {
					return true
				}
			} else {
				return true
			}
		}
		return false
	}
	return slices.ContainsFunc(activatedLinks, equalFunc)
}

func (t *TalentTree) RaisePSs(funcID uint32, activatedLinks []*goxml.LinkSkillInfoExt) map[uint32][]uint64 {
	pss := make(map[uint32][]uint64)
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), goxml.TalentTreeNodeLevelAdditionPassive)
	for _, id := range ids {
		level := t.GetLevel(id)
		if level == 0 {
			continue
		}
		nodeInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(t.owner.GetSeasonID(), id)
		if nodeInfo == nil {
			l4g.Errorf("user:%d talentTree.RaisePSs: nodeInfo not exist. id:%d", t.Owner().ID(), id)
			continue
		}
		if nodeInfo.PassiveType != goxml.TalentTreeNodeLevelAdditionPassive {
			continue
		}
		levelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if levelInfo == nil {
			l4g.Errorf("user:%d talentTree.RaisePSs: levelInfo not exist. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if len(levelInfo.PassiveParams) == 0 {
			continue
		}
		passiveParamsOk := false
		switch nodeInfo.NodeType {
		case goxml.TalentTreeRoot:
			passiveParamsOk = true
		case goxml.TalentTreeSeasonTeam:
			if nodeInfo.TeamPostion > 0 {
				passiveParamsOk = true
			} else if nodeInfo.SeasonLink > 0 {
				passiveParamsOk = t.CheckSeasonLinkValid(nodeInfo, activatedLinks)
			}
		case goxml.TalentTreeFunc:
			if funcID != 0 && funcID == nodeInfo.SeasonFunction {
				passiveParamsOk = true
			}
		case goxml.TalentTreeEggBlessing:
			passiveParamsOk = true
		case goxml.TalentTreeSeasonMap:
			if funcID != 0 && funcID == nodeInfo.SeasonFunction {
				passiveParamsOk = true
			}
		}
		if passiveParamsOk {
			for _, param := range levelInfo.PassiveParams {
				raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(param)
				if raisePassiveSkillInfo == nil {
					l4g.Errorf("user:%d talentTree.RaisePSs: raisePassiveSkillInfo is nil. id: %d", t.Owner().ID(), param)
					continue
				}
				pss[battle.TeamUniterBattlePos] = append(pss[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
			}
		}
	}
	return pss
}

func (t *TalentTree) AddAttr() map[uint32]map[int]int64 {
	attrs := make(map[uint32]map[int]int64)
	attrs[0] = make(map[int]int64)
	level := t.GetRootLevel()
	if level == 0 {
		return attrs
	}
	rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(t.Owner().GetSeasonID())
	info := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(rootId, level)
	if info == nil {
		l4g.Errorf("user:%d talentTree.AddAttr: levelInfo not exist. id:%d level:%d", t.Owner().ID(), rootId, level)
		return attrs
	}
	for _, attr := range info.Attr {
		attrs[0][int(attr.Type)] = int64(attr.Value)
	}
	return attrs
}

func (t *TalentTree) OnSeasonEnd(srv servicer, needSettleAward bool) {
	itemIds := goxml.GetData().SeasonTalentTreeConfigInfoM.GetClearResetItem()
	reduce := make([]*cl.Resource, 0, len(itemIds))
	for _, itemId := range itemIds {
		if count := t.Owner().GetItemCount(itemId); count > 0 {
			reduce = append(reduce, goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), itemId, count))
		}
	}
	if len(reduce) > 0 {
		retCode := t.Owner().Consume(srv, reduce, uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_SEASON_RESET), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user:%d talentTree.seasonInit: Consume error. ret:%d", t.Owner().ID(), retCode)
		}
	}
	if !needSettleAward {
		return
	}
	// 发送赛季回馈奖励
	seasonId := t.Owner().GetSeasonID()
	if seasonId == 0 {
		return
	}
	recycleAwardInfo := goxml.GetData().SeasonTalentTreeRecyleAwardInfoM.GetRecordBySeasonIdLevelMaxLe(seasonId, t.GetRootLevel())
	if recycleAwardInfo == nil {
		l4g.Errorf("user:%d talentTree.seasonEnd: recycleAwardInfo not exist. seasonId:%d level:%d",
			t.Owner().ID(), seasonId, t.GetRootLevel())
		return
	}
	TalentTreeRecycle(srv, t.Owner(), recycleAwardInfo.Reward, MailIDTalentTreeRecycle)
}

func (t *TalentTree) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	t.base = &cl.TalentTreeBase{
		TaskTypeProgress: make(map[uint32]*cl.TaskTypeProgress),
		Awarded:          make(map[uint32]bool),
	}
	t.cultivate = &cl.TalentTreeCultivate{
		Levels: make(map[uint32]uint32),
	}
	t.base.LastAddTimesTm = time.Now().Unix()
	t.save()

	awards := goxml.GetData().SeasonTalentTreeConfigInfoM.GetInitialResource()
	retCode, _ := t.Owner().Award(srv, awards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_SEASON_RESET), 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("user:%d talentTree.seasonInit: Award error. ret:%d", t.Owner().ID(), retCode)
	}
}

func (t *TalentTree) CheckAddResetTimes(srv servicer) {
	now := time.Now().Unix()
	if t.base.LastAddTimesTm >= now {
		return
	}
	interval := now - t.base.LastAddTimesTm
	addTime := int64(goxml.GetData().SeasonTalentTreeConfigInfoM.GetResetAddTime())
	if addTime == 0 {
		return
	}
	num := interval / addTime
	if num == 0 {
		return
	}
	awards := goxml.GetData().SeasonTalentTreeConfigInfoM.AwardTalentReset(uint32(num))
	t.owner.Award(srv, awards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_TALENT_AWARD_RESET_ITEM), 0)
	t.base.LastAddTimesTm += num * addTime
	t.save()
}

func (t *TalentTree) GetTeamAddition(additionType uint32) int {
	if additionType != goxml.TalentTreeNodeLevelAdditionGstTeamNum && additionType != goxml.TalentTreeNodeLevelAdditionGstDragonTeamNum {
		l4g.Errorf("user:%d talentTree.GetGstTeamAddition: additionType:%d error.", t.Owner().ID(), additionType)
		return 0
	}

	num := 0
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), additionType)
	for _, id := range ids {
		level := t.GetLevel(id)
		levelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if levelInfo == nil {
			l4g.Errorf("user:%d talentTree.GetGstTeamAddition: levelInfo not exist. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if len(levelInfo.PassiveParams) == 0 || levelInfo.PassiveParams[0] == 0 {
			continue
		}
		num += int(levelInfo.PassiveParams[0])
	}
	return num
}

func (t *TalentTree) GmSetLevel(level uint32) map[uint32][]uint32 {
	changeIds := make(map[uint32][]uint32)
	if level == 0 {
		for id := range goxml.GetData().SeasonTalentTreeBaseInfoM.GetIdRecordMapBySeasonId(t.Owner().GetSeasonID()) {
			maxLevel := uint32(0)
			for level := range goxml.GetData().SeasonTalentTreeLevelInfoM.GetLevelRecordMapById(id) {
				if level > maxLevel {
					maxLevel = level
				}
			}
			if maxLevel > 0 {
				oldLevel := t.GetLevel(id)
				t.SetLevel(id, maxLevel)
				changeIds[id] = []uint32{oldLevel, maxLevel}
			}
		}
	} else {
		// 指定等级的话，只修改根节点等级
		rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(t.Owner().GetSeasonID())
		if goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(rootId, level) == nil {
			l4g.Errorf("user:%d talentTree.GmSetLevel: levelInfo not exist. id:%d level:%d", t.Owner().ID(), rootId, level)
			return nil
		}
		t.SetLevel(rootId, level)
	}
	t.save()
	return changeIds
}

// getActiveNumReduceLinkIds
// @Description: 获取羁绊激活数量减少的羁绊ID列表
// @receiver t
// @return []uint32
func (t *TalentTree) getLinkActiveNumReduceLinkIds() []uint32 {
	nodeIds := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), goxml.TalentTreeNodeLevelAdditionLinkHeroNumReduce)
	if len(nodeIds) == 0 {
		return nil
	}
	linkIds := make([]uint32, 0, len(nodeIds))
	for _, id := range nodeIds {
		baseInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(t.Owner().GetSeasonID(), id)
		if baseInfo.SeasonLink == 0 {
			continue
		}
		level := t.GetLevel(id)
		lvInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if lvInfo == nil {
			continue
		}
		if len(lvInfo.PassiveParams) > 0 && lvInfo.PassiveParams[0] == 1 {
			linkIds = append(linkIds, baseInfo.SeasonLink)
		}
	}
	return linkIds
}

func (t *TalentTree) GetBossRushTalentTreeCultivation(seasonId, formationId uint32) *cl.TalentTreeCultivate {
	res := &cl.TalentTreeCultivate{
		Levels: make(map[uint32]uint32),
	}

	bossRushFids := goxml.GetData().BossRushInfoM.GetFormationIdsBySeason(seasonId)
	if len(bossRushFids) == 0 {
		return res
	}

	if _, exist := bossRushFids[formationId]; !exist {
		return res
	}

	bossRushInfo := goxml.GetData().BossRushInfoM.GetFirstRecordBySeasonIdFormationId(seasonId, formationId)
	if bossRushInfo == nil {
		l4g.Errorf("user:%d talentTree.GetBossRushTalentTreeCultivation: no bossRushInfo. seasonId %d formationId %d", t.Owner().ID(), seasonId, formationId)
		return res
	}
	res.Levels[bossRushInfo.TalentSkill] = t.GetLevel(bossRushInfo.TalentSkill)

	return res
}

// IsResetFree 判断是否免费重置
func (t *TalentTree) IsResetFree() bool {
	seasonEnterTime := t.Owner().GetSeasonEnterTime()
	return time.Now().Unix() <= seasonEnterTime+int64(goxml.GetData().SeasonTalentTreeConfigInfoM.GetTalentResetFreeTime())*util.DaySecs
}

func (t *TalentTree) IsPlanNumEnough() bool {
	return uint32(len(t.base.Plans)) < goxml.GetData().SeasonTalentTreeConfigInfoM.PlanNumMax
}

func (t *TalentTree) PlanSave(plan *cl.TalentTreePlan) {
	t.base.Plans = append([]*cl.TalentTreePlan{plan.Clone()}, t.base.Plans...)
	t.saveBase()
}

func (t *TalentTree) PlanDelete(index int) ret.RET {
	if len(t.base.Plans) <= index {
		l4g.Errorf("PlanDelete plan index:%d over", index)
		return ret.RET_CLIENT_REQUEST_ERROR
	}
	t.base.Plans = slices.Delete(t.base.Plans, index, index+1)
	t.saveBase()
	return ret.RET_OK
}

func (t *TalentTree) PlanUse(index int) ret.RET {
	return ret.RET_OK
}

func (t *TalentTree) GetSeasonDoorLineNum() int {
	num := 0
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), goxml.TalentTreeNodeLevelAdditionSeasonDoorLineNum)
	for _, id := range ids {
		level := t.GetLevel(id)
		levelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if levelInfo == nil {
			l4g.Errorf("user:%d talentTree.GetGstTeamAddition: levelInfo not exist. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if len(levelInfo.PassiveParams) == 0 {
			continue
		}
		num += int(levelInfo.PassiveParams[0])
	}
	return num
}

func (t *TalentTree) GetSeasonJewelryAdd(pos uint32) int64 {
	var add int64
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), goxml.TalentTreeNodeLevelAdditionSeasonJewelry)
	for _, id := range ids {
		level := t.GetLevel(id)
		if level == 0 {
			continue
		}
		levelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if levelInfo == nil {
			l4g.Errorf("user:%d talentTree.GetSeasonJewelryAdd: levelInfo not exist. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if len(levelInfo.PassiveParams) < 2 || levelInfo.PassiveParams[1] == 0 {
			continue
		}
		if levelInfo.PassiveParams[0] != pos {
			continue
		}
		add += int64(levelInfo.PassiveParams[1])
	}
	return add
}

// NodesLevelUp
// @Description:
// @receiver t
// @param srv
// @param nodes
// @param seasonMap 是否通过seasonMap升级
// @return uint32
// @return []*cl.Resource
//
//nolint:funlen
func (t *TalentTree) NodesLevelUp(srv Servicer, nodes []*cl.TalentTreeNode, seasonMap bool) (uint32, []*cl.Resource) {
	if len(nodes) > len(goxml.GetData().SeasonTalentTreeBaseInfoM.GetIdRecordMapBySeasonId(t.owner.GetSeasonID()))*3 {
		l4g.Errorf("user:%d NodeLevelUp: nodes num:%d error. failed", t.owner.ID(), len(nodes))
		return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
	}
	totalCost := make([]*cl.Resource, 0)
	totalAwards := make([]*cl.Resource, 0)
	oldLevelMap := make(map[uint32]uint32)
	rootId := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(t.owner.GetSeasonID())
	rootLv := t.GetLevel(rootId)
	levelUpedNode := make(map[uint32]uint32)
	for _, node := range nodes {
		currentLv := t.GetLevel(node.GetId())
		if targetLv, exist := levelUpedNode[node.GetId()]; exist {
			currentLv = targetLv
		} else {
			oldLevelMap[node.GetId()] = currentLv
		}
		if node.GetTargetLevel() == 0 || node.GetTargetLevel() <= currentLv {
			l4g.Errorf("user: %d NodeLevelUp: targetLv error.id:%d targetLv:%d currentLv:%d",
				t.owner.ID(), node.GetId(), node.GetTargetLevel(), currentLv)
			return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
		}

		baseInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(t.owner.GetSeasonID(), node.GetId())
		if baseInfo == nil {
			l4g.Errorf("user: %d NodeLevelUp: baseInfo error.seasonId:%d id:%d",
				t.owner.ID(), t.owner.GetSeasonID(), node.GetId())
			return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
		}
		if baseInfo.NodeType == goxml.TalentTreeSeasonMap && !seasonMap { // 赛季功能节点只能通过seasonMap加点
			l4g.Errorf("user: %d NodeLevelUp: request id error. id:%d", t.owner.ID(), node.GetId())
			return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
		}

		targetInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(node.GetId(), node.GetTargetLevel())
		if targetInfo == nil {
			l4g.Errorf("user: %d NodeLevelUp: levelInfo not exist. id:%d targetLv:%d", t.owner.ID(), node.GetId(), node.GetTargetLevel())
			return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
		}
		if !t.CheckLevelUp(targetInfo, baseInfo, levelUpedNode) {
			l4g.Errorf("user:%d NodeLevelUp: CheckLevelUp failed", t.owner.ID())
			return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
		}

		for i := currentLv + 1; i <= node.GetTargetLevel(); i++ {
			lvInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(node.GetId(), i)
			if lvInfo == nil {
				l4g.Errorf("user: %d NodeLevelUp: levelInfo not exist. id:%d targetLv:%d", t.owner.ID(), node.GetId(), i)
				return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
			}
			totalCost = append(totalCost, lvInfo.Cost...)
			if lvInfo.TreeLevelLimit != 0 && rootLv < lvInfo.TreeLevelLimit {
				l4g.Errorf("user: %d NodeLevelUp: treeLevel not ok. treeLv:%d limitLv:%d",
					t.owner.ID(), rootLv, lvInfo.TreeLevelLimit)
				return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
			}
			if lvInfo.PreconditionId != 0 && t.GetLevel(lvInfo.PreconditionId) < lvInfo.PreconditionLevel &&
				levelUpedNode[lvInfo.PreconditionId] < lvInfo.PreconditionLevel {
				l4g.Errorf("user: %d NodeLevelUp: preconditionId not ok. preconditionIdLv:%d, limitLv:%d",
					t.owner.ID(), t.GetLevel(lvInfo.PreconditionId), lvInfo.PreconditionLevel)
				return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
			}
			if lvInfo.PreconditionId2 != 0 && t.GetLevel(lvInfo.PreconditionId2) < lvInfo.PreconditionLevel2 &&
				levelUpedNode[lvInfo.PreconditionId2] < lvInfo.PreconditionLevel2 {
				l4g.Errorf("user: %d NodeLevelUp: preconditionId not ok. preconditionIdLv:%d, limitLv:%d",
					t.owner.ID(), t.GetLevel(lvInfo.PreconditionId2), lvInfo.PreconditionLevel2)
				return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil
			}
			if len(lvInfo.Awards) > 0 {
				totalAwards = append(totalAwards, lvInfo.Awards...)
			}
		}
		levelUpedNode[node.GetId()] = node.GetTargetLevel()
		if node.GetId() == rootId {
			rootLv = node.GetTargetLevel()
		}
	}

	retCode := uint32(ret.RET_OK)
	if len(totalCost) > 0 && len(totalAwards) > 0 {
		retCode, totalAwards = t.owner.Trade(srv, totalCost, totalAwards, uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_LEVEL_UP), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_TalentTreeLevelUp: trade error. ret:%d", t.owner.ID(), retCode)
			return retCode, nil
		}
	} else if len(totalCost) > 0 {
		retCode = t.owner.Consume(srv, totalCost, uint32(log.RESOURCE_CHANGE_REASON_TALENT_TREE_LEVEL_UP), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_TalentTreeLevelUp: consume error. ret:%d", t.owner.ID(), retCode)
			return retCode, nil
		}
	}

	for id, targetLv := range levelUpedNode {
		t.handleByAdditionTypeBeforeLevelUp(id, srv)
		oldLevel := oldLevelMap[id]
		t.owner.FireCommonEvent(srv.EventM(), uint32(aevent.IeTalentTreeLevelUp), uint64(targetLv), oldLevel, id)
		t.SetLevel(id, targetLv)
		t.owner.LogTalentTreeLevelUp(srv, id, oldLevel, targetLv)
	}

	t.owner.UpdateAllPower(srv, PowerUpdateBySeasonRaise)
	t.owner.SendSelfToClient()
	if seasonMap {
		// 最新数据推送
		updateClient := &cl.L2C_TalentTreeCultivateUpdate{
			Ret:       uint32(ret.RET_OK),
			Cultivate: t.FlushCultivate(),
		}
		t.owner.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeCultivateUpdate, updateClient)
	}
	return uint32(ret.RET_OK), totalAwards
}

// 升级前不同加成需要做的处理：比如结算
func (t *TalentTree) handleByAdditionTypeBeforeLevelUp(nodeId uint32, srv Servicer) {
	baseInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(t.owner.GetSeasonID(), nodeId)
	if baseInfo == nil {
		l4g.Errorf("user:%d HandleByAdditionTypeBeforeLevelUp: baseInfo not exist. id:%d", t.owner.ID(), nodeId)
		return
	}
	switch baseInfo.PassiveType {
	case goxml.TalentTreeNodeLevelAdditionSeasonMapStaminaMax:
		t.owner.SeasonMap().UpdateGoodsStaminaMax(srv)
	case goxml.TalentTreeNodeLevelAdditionSeasonMapStaminaRecover:
		t.owner.SeasonMap().RecoverGoodsStamina(srv)
	}
}

func (t *TalentTree) GetSeasonMapAddition(addType uint32) uint32 {
	num := uint32(0)
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), addType)
	for _, id := range ids {
		level := t.GetLevel(id)
		if level == 0 {
			continue
		}
		levelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if levelInfo == nil {
			l4g.Errorf("user:%d talentTree.GetSeasonMapAddition: levelInfo not exist. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if len(levelInfo.PassiveParams) == 0 || levelInfo.PassiveParams[0] == 0 {
			continue
		}
		num += levelInfo.PassiveParams[0]
	}
	return num
}

func (t *TalentTree) GetSeasonMapFightAltPs(terrain uint32) *battle.AltRaisePS {
	raisePSMap := make(map[uint32][]uint64)
	ids := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.Owner().GetSeasonID(), goxml.TalentTreeNodeLevelAdditionSeasonMapFight)
	for _, id := range ids {
		level := t.GetLevel(id)
		if level == 0 {
			continue
		}
		nodeInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(t.owner.GetSeasonID(), id)
		if nodeInfo == nil {
			l4g.Errorf("user:%d talentTree.RaisePSs: nodeInfo not exist. id:%d", t.Owner().ID(), id)
			continue
		}
		levelInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if levelInfo == nil {
			l4g.Errorf("user:%d talentTree.RaisePSs: levelInfo not exist. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if len(levelInfo.PassiveParams) == 0 {
			continue
		}
		totalParams := len(levelInfo.PassiveParams)
		if totalParams < 2 {
			l4g.Errorf("user:%d talentTree.RaisePSs: levelInfo.PassiveParams error. id:%d level:%d", t.Owner().ID(), id, level)
			continue
		}
		if levelInfo.PassiveParams[0] == terrain {
			raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(levelInfo.PassiveParams[1])
			if raisePassiveSkillInfo == nil {
				l4g.Errorf("user:%d talentTree.RaisePSs: raisePassiveSkillInfo is nil. id: %d", t.Owner().ID(), levelInfo.PassiveParams[1])
				continue
			}
			raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)

		}

		for i := 2; i < totalParams; i++ {
			raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRecordByRaisePassiveSkill(levelInfo.PassiveParams[i])
			if raisePassiveSkillInfo == nil {
				l4g.Errorf("user:%d talentTree.RaisePSs: raisePassiveSkillInfo is nil. id: %d", t.Owner().ID(), levelInfo.PassiveParams[i])
				continue
			}
			raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
		}
	}
	altPs := &battle.AltRaisePS{}
	if len(raisePSMap) > 0 {
		altPs.AltAttack(raisePSMap)
	}
	return altPs
}
