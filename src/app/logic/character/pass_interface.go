package character

import (
	"app/goxml"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type IsOpen func(srv servicer, user *User, passInfo *goxml.PassInfoExt, passData *PassData) bool

func onePassIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	// 检查活跃战令解锁天数
	if srv.ServerDay(time.Now().Unix()) < goxml.GetData().PassInfoM.GetUnlockDay(passInfo.Id) {
		return false
	}

	return true
}

func checkCyclePassOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	if srv.ServerDay(time.Now().Unix()) < goxml.GetData().PassInfoM.GetUnlockDay(passInfo.Id) {
		return false
	}
	return true
}

func cyclePassIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, passData *PassData) bool {
	if !checkCyclePassOpen(srv, user, passInfo, passData) {
		return false
	}
	pass := user.Pass().GetPass(passInfo.Id)
	return pass != nil
}

func activityStoryPassIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	now := time.Now().Unix()

	if passInfo.CloseServerDay > 0 && passInfo.CloseServerDay <= srv.StartServiceTm() {
		return false
	}

	if passInfo.CloseServerDay2 > 0 && passInfo.CloseServerDay2 > srv.StartServiceTm() {
		return false
	}

	if now >= passInfo.OpenDay && now < passInfo.EndDay {
		return true
	}

	return false
}

func skinPassIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	if srv.ServerDay(time.Now().Unix()) < goxml.GetData().PassInfoM.GetUnlockDay(passInfo.Id) {
		return false
	}

	now := time.Now().Unix()
	if now >= passInfo.OpenDay && now < passInfo.EndDay {
		return true
	}

	return false
}

func seasonPassIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	now := time.Now().Unix()
	if now >= passInfo.OpenDay && now < passInfo.EndDay {
		return true
	}

	return false
}

func createUserDayIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	now := time.Now().Unix()
	createDay := user.CreateDay(now)

	if createDay >= passInfo.UnlockDay && createDay < passInfo.CloseDay {
		return true
	}

	return false
}

func openServerDayIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, passData *PassData) bool {
	if passInfo == nil {
		return false
	}

	if passData != nil {
		return true
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	now := time.Now().Unix()
	openDay := srv.ServerOpenDay(now)

	if openDay >= passInfo.UnlockDay && openDay < passInfo.CloseDay {
		return true
	}

	return false
}

func seasonWeekIsOpen(srv servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	if !user.IsFunctionOpen(passInfo.ModuleFunctionId, srv) {
		return false
	}

	now := time.Now().Unix()
	if now >= passInfo.OpenDay && now < passInfo.EndDay {
		return true
	}

	return false
}

var passOpenCheck = map[uint32]IsOpen{
	goxml.PassTypeOnce:       onePassIsOpen,
	goxml.PassTypeCycle:      cyclePassIsOpen,
	goxml.PassTypeActivity:   activityStoryPassIsOpen,
	goxml.PassTypeSeason:     seasonPassIsOpen,
	goxml.PassTypeDungeon:    onePassIsOpen, // 与一次性战令一致
	goxml.PassTypeTower:      onePassIsOpen,
	goxml.PassTypeOpenServer: openServerDayIsOpen,
	goxml.PassTypeCreateU:    createUserDayIsOpen,
	goxml.PassTypeUserLevel:  onePassIsOpen,
	goxml.PassTypeSkin:       skinPassIsOpen,
	goxml.PassTypeSeasonWeek: seasonWeekIsOpen,
}

type PassI interface {
	IsEnd(srv Servicer, user *User, passInfo *goxml.PassInfoExt, passData *PassData) bool
}

type oncePass PassData

func (op *oncePass) IsEnd(_ Servicer, _ *User, _ *goxml.PassInfoExt, _ *PassData) bool {
	return false
}

type cyclePass PassData

func (cp *cyclePass) IsEnd(srv Servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		l4g.Errorf("user: %d checkCycleFinish failed, passInfo is` nil. passID: %d", user.ID(), cp.SysId)
		return false
	}
	now := time.Now().Unix()
	openDay := srv.ServerDay(now)
	if openDay < passInfo.UnlockDay {
		return true
	}
	if cp.ActivePass == nil {
		return true
	}
	round := cp.calcPassRound(srv, passInfo, cp.ActivePass.ActiveTime)
	endDay := passInfo.UnlockDay + round*passInfo.Cycle
	return openDay >= endDay
}

func (cp *cyclePass) calcPassRound(srv servicer, passInfo *goxml.PassInfoExt, realTime int64) uint32 {
	realDay := srv.ServerDay(realTime) - passInfo.UnlockDay
	return realDay/passInfo.Cycle + 1
}

type actStoryPass PassData

func (ap *actStoryPass) IsEnd(_ Servicer, _ *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()
	return now >= passInfo.EndDay
}

type seasonPass PassData

func (ap *seasonPass) IsEnd(_ Servicer, _ *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()
	return now >= passInfo.EndDay
}

// 主线战令
type dungeonPass PassData

func (op *dungeonPass) IsEnd(_ Servicer, _ *User, _ *goxml.PassInfoExt, _ *PassData) bool {
	return false
}

// 地宫战令
type towerPass PassData

func (op *towerPass) IsEnd(_ Servicer, _ *User, _ *goxml.PassInfoExt, _ *PassData) bool {
	return false
}

type createUserPass PassData

func (op *createUserPass) IsEnd(_ Servicer, user *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}
	now := time.Now().Unix()
	createDay := user.CreateDay(now)

	return createDay >= passInfo.CloseDay
}

type openServer PassData

func (op *openServer) IsEnd(_ Servicer, _ *User, passInfo *goxml.PassInfoExt, passData *PassData) bool {
	if passData != nil && passData.ActivePass != nil {
		actTime := passData.ActivePass.ActiveTime
		now := time.Now().Unix()
		if util.DaysBetweenTimes(now, actTime) >= passInfo.ActiveCloseDay {
			return true
		}
	}
	return false
}

// 玩家等级
type userLevelPass PassData

func (op *userLevelPass) IsEnd(_ Servicer, _ *User, _ *goxml.PassInfoExt, _ *PassData) bool {
	return false
}

type seasonWeekPass PassData

func (ap *seasonWeekPass) IsEnd(_ Servicer, _ *User, passInfo *goxml.PassInfoExt, _ *PassData) bool {
	if passInfo == nil {
		return false
	}

	now := time.Now().Unix()
	return now >= passInfo.EndDay
}
