package character

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type Pass struct {
	owner *User
	data  map[uint32]*PassData
}

func newPass(user *User) *Pass {
	return &Pass{
		owner: user,
		data:  make(map[uint32]*PassData),
	}
}

func newPassData(sysID uint32) *PassData {
	data := &PassData{
		SysId:        sysID,
		Receive:      make(map[uint32]*cl.PassReceiveSate),
		TaskProgress: make(map[uint32]*cl.TaskTypeProgress),
	}

	passInfo := goxml.GetData().PassInfoM.Index(sysID)
	if passInfo == nil {
		return data
	}

	if goxml.IsCyclePass(passInfo) {
		data.newActivePassData()
	}

	return data
}

func (p *Pass) load(dbData map[uint32]*cl.PassData) {
	for id, pd := range dbData {
		pass := (*PassData)(pd)
		p.data[id] = pass
		if pass.Receive == nil {
			pass.Receive = make(map[uint32]*cl.PassReceiveSate)
		}
		if pass.TaskProgress == nil {
			pass.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
		}
		// 活跃战令独有结构
		if pass.ActivePass != nil {
			if pass.ActivePass.TaskProgress == nil {
				pass.ActivePass.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
			}
			if pass.ActivePass.ReceivePoint == nil {
				pass.ActivePass.ReceivePoint = make(map[uint32]bool)
			}
		}
	}
}

func (p *Pass) Flush(sysID uint32) *cl.PassData {
	return (*cl.PassData)(p.data[sysID]).Clone()
}

func (p *Pass) Save(passID uint32) {
	if p.owner.dbUser.Module2.Passes == nil {
		p.owner.dbUser.Module2.Passes = make(map[uint32]*cl.PassData)
	}
	p.owner.dbUser.Module2.Passes[passID] = (*cl.PassData)(p.data[passID])
	p.owner.setSaveTag(saveTagModule2)
}

func (p *Pass) GetPass(SysID uint32) *PassData {
	return p.data[SysID]
}

func (p *Pass) InitPass(sysID uint32) *PassData {
	data := newPassData(sysID)
	p.data[sysID] = data
	p.Save(sysID)
	return data
}

func (p *Pass) CheckAndInitLoginPass(passID uint32, srv servicer) {
	dailyPoint := goxml.GetData().PassCycleTaskInfoM.IsDailyLoginPass(passID)
	if dailyPoint == 0 {
		return
	}

	passInfo := goxml.GetData().PassInfoM.Index(passID)
	if passInfo == nil {
		return
	}

	if !p.IsOpen(srv, passID) {
		return
	}

	typeID, exist := goxml.GetData().PassTaskInfoM.GetPassEventTypeID(passID)
	if !exist {
		return
	}

	pass := p.GetPass(passID)
	if pass == nil {
		pass = p.InitPass(passID)
		pass.TaskProgress[typeID] = &cl.TaskTypeProgress{
			TaskTypeId: typeID,
			Progress:   uint64(dailyPoint),
		}
		p.Save(passID)
	}

}

func (p *Pass) DelPass(sysID uint32) {
	delete(p.data, sysID)
	delete(p.owner.dbUser.Module2.Passes, sysID)
	p.owner.setSaveTag(saveTagModule2)
}

// 处理战令主任务
func (p *Pass) OnPassMainEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	ids := goxml.GetData().PassTaskInfoM.GetPassIDsByEvent(event)
	for _, id := range ids {
		p.updateMainEvent(id, event, progress, values, srv)
	}
}

// 更新战令主任务
func (p *Pass) updateMainEvent(passID, event uint32, progress uint64, values []uint32, srv servicer) {
	passInfo := goxml.GetData().PassInfoM.Index(passID)
	if passInfo == nil {
		return
	}
	// 任玩家等级，主线和地宫战令更新进度不需要判断是否开启
	if passInfo.PassType != goxml.PassTypeDungeon && passInfo.PassType != goxml.PassTypeTower && passInfo.PassType != goxml.PassTypeUserLevel {
		if !p.IsOpen(srv, passID) {
			return
		}
	}

	pass := p.GetPass(passID)
	if pass == nil {
		pass = p.InitPass(passID)
	}

	progressMap, change := p.owner.TaskTypeOnEvent(pass.TaskProgress, event, progress, values)
	if change {
		p.sendPassTaskUpdateToClient(passID, progressMap)
		p.Save(passID)
	}
}

// 处理战令循环任务
func (p *Pass) OnPassCycleEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	p.handleCycleTask(event, progress, values, srv)
}

func (p *Pass) process(srv servicer, order *db.Order) bool {
	passInfo := goxml.GetData().PassInfoM.Index(order.Custom.Id)
	if passInfo == nil {
		l4g.Errorf("user: %d account tag:%d  process: pass not exist. SysID :%d", p.owner.ID(), p.owner.GetAccountTag(), order.Custom.Id)
		return false
	}

	if !p.IsOpen(srv, passInfo.Id) {
		l4g.Errorf("user: %d account tag:%d buy pass:%d pass is not open", p.owner.ID(), p.owner.GetAccountTag(), passInfo.Id)
		return false
	}

	buy1 := passInfo.InternalId == order.Custom.InternalId
	buy2 := passInfo.InternalId2 == order.Custom.InternalId
	if !(buy1 || buy2) {
		l4g.Errorf("user: %d account tag:%d  process: internalID:%d or internalID2:%d not match. internalID: %d",
			p.owner.ID(), p.owner.GetAccountTag(), passInfo.InternalId, passInfo.InternalId2, order.Custom.InternalId)
		return false
	}

	var buyType uint32
	if buy1 {
		buyType = 1
	}
	if buy2 {
		buyType = 2
	}

	pass := p.GetPass(passInfo.Id)
	if pass != nil && pass.Buy && buy1 {
		l4g.Errorf("user: %d account tag:%d  process: internalID %d has been purchased", p.owner.ID(), p.owner.GetAccountTag(), order.Custom.InternalId)
		return false
	}
	if pass != nil && pass.Buy2 && buy2 {
		l4g.Errorf("user: %d account tag:%d  process: internalID %d has been purchased", p.owner.ID(), p.owner.GetAccountTag(), order.Custom.InternalId)
		return false
	}
	var vipClRes []*cl.Resource
	if buy1 {
		vipClRes = passInfo.VipClRes
	}
	if buy2 {
		vipClRes = passInfo.VipClRes2
	}

	var awards []*cl.Resource
	var retCode uint32
	if len(vipClRes) > 0 {
		retCode, awards = p.owner.Award(srv, vipClRes, AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_PASS_RECHARGE), goxml.AddResourcesForRecharge)
		if retCode != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d account tag:%d  process: send award failed. retCode:%d", p.owner.ID(), p.owner.GetAccountTag(), retCode)
			return false
		}
	}

	p.setBuy(pass, passInfo.Id, buyType)

	//附赠活跃点数
	if passInfo.BounsPoint > 0 {
		eventType := goxml.GetData().PassTaskInfoM.GetEventsByPassID(passInfo.Id)
		p.updateMainEvent(passInfo.Id, eventType, uint64(passInfo.BounsPoint), nil, srv)
	}

	p.owner.LogPassRecharge(srv, passInfo.Id)
	p.owner.SendCmdToGateway(cl.ID_MSG_L2C_PassBuyNotify, &cl.L2C_PassBuyNotify{
		Ret:     uint32(cret.RET_OK),
		Awards:  awards,
		SysId:   passInfo.Id,
		BuyType: buyType,
	})

	return true
}

func (p *Pass) sendPassTaskUpdateToClient(id uint32, progress map[uint32]*cl.TaskTypeProgress) {
	smsg := &cl.L2C_PassUpdate{
		Ret:          uint32(cret.RET_OK),
		PassId:       id,
		TaskProgress: progress,
		PassData:     p.Flush(id),
	}
	p.owner.SendCmdToGateway(cl.ID_MSG_L2C_PassUpdate, smsg)
}

func (p *Pass) setBuy(pass *PassData, passID uint32, buyType uint32) {
	if pass == nil {
		pass = p.InitPass(passID)
	}
	switch buyType {
	case 1:
		pass.Buy = true
	case 2: //nolint:mnd
		pass.Buy2 = true
	}
	p.Save(passID)
}

// 活跃战令的周期任务，每天零点重置：任务进度、积分领取状态都重置
// 活跃战令在满足解锁条件的前提下，记录任务进度，当任务完成时自动触发加积分的任务
// 该周期任务不向玩家显示，无需玩家操作，仅作为积分自动发放途径
func (p *Pass) handleCycleTask(event uint32, progress uint64, values []uint32, srv servicer) {
	for passID := range goxml.GetData().PassCycleTaskInfoM.GetPassIDByEvent(event) {
		if !p.IsOpen(srv, passID) {
			continue
		}
		passInfo := goxml.GetData().PassInfoM.Index(passID)
		if passInfo == nil {
			l4g.Errorf("user:%d handleCycleTask get pass:%d info is nil", p.owner.ID(), passID)
			continue
		}
		pass, exist := p.data[passID]
		if !exist {
			if passInfo.PassType == goxml.PassTypeCycle {
				l4g.Errorf("user%d handleCycleTask cant find pass %d", p.owner.ID(), passID)
				continue
			} else {
				pass = p.InitPass(passID)
			}
		}
		_, change := p.owner.TaskTypeOnEvent(pass.ActivePass.TaskProgress, event, progress, values)
		if change {
			// 检查任务是否完成，完成，自动触发增加积分的任务
			pass.autoHandlePoint(srv, p.owner)
			p.sendPassTaskUpdateToClient(passID, pass.TaskProgress)
			p.Save(passID)
		}
	}
}

func (p *Pass) IsOpen(srv servicer, passID uint32) bool {
	passInfo := goxml.GetData().PassInfoM.Index(passID)
	if passInfo == nil {
		return false
	}

	if !passInfo.SeasonCheck(p.owner.GetSeasonID()) {
		return false
	}

	checkFunc, exist := passOpenCheck[passInfo.PassType]
	if !exist {
		return false
	}

	return checkFunc(srv, p.owner, passInfo, p.data[passID])
}

// 遍历检查过期 每种战令只配置一种类型的循环任务故直接进行重置
func (p *Pass) ResetDaily(srv servicer) {
	dailyReset := goxml.GetData().PassCycleTaskInfoM.GetDailyReset()
	for _, pass := range p.data {
		if pass == nil {
			continue
		}
		_, exist := dailyReset[pass.SysId]
		if exist && pass.ActivePass != nil {
			pass.ResetCycleTask(goxml.PassCycleTaskResetDaily)
			p.Save(pass.SysId)
		}
		passI, passInfo := p.getPassI(pass)
		if passI == nil || passInfo == nil {
			continue
		}
		isEnd := passI.IsEnd(srv, p.owner, passInfo, pass)
		if isEnd {
			if (passInfo.SeasonID > 0) && (pass.ActivePass != nil && pass.ActivePass.ActiveTime >= passInfo.EndDay) {
				continue
			}
			if passInfo.MailId != 0 {
				mailGenTime := int64(0)
				if passInfo.PassType == goxml.PassTypeSeason || passInfo.PassType == goxml.PassTypeSeasonWeek {
					mailGenTime = passInfo.OpenDay
				}
				p.sendReissueAwardMail(srv, pass, p.owner.ID(), passInfo.MailId, mailGenTime)
			}
			p.DelPass(pass.SysId)
			p.owner.SendCmdToGateway(cl.ID_MSG_L2C_PassUpdate, &cl.L2C_PassUpdate{
				Ret:       uint32(cret.RET_OK),
				PassId:    pass.SysId,
				FromReset: true,
			})
		}
	}
	p.CheckCyclePassOpen(srv)
}

// 只做周重置，过期检查放在每日做
func (p *Pass) ResetWeekly() {
	for id := range goxml.GetData().PassCycleTaskInfoM.GetWeeklyReset() {
		pass := p.GetPass(id)
		if pass != nil && pass.ActivePass != nil {
			pass.ResetCycleTask(goxml.PassCycleTaskResetEveryMonday)
			p.Save(id)
		}
	}
}

//// 每天零点重置：重置任务进度、积分领取状态
//func (p *Pass) resetActivePass(srv servicer, passID, mailID uint32) {
//	if pass, exist := p.data[passID]; exist {
//		if pass.ActivePass == nil {
//			return
//		}
//		pass.initCycleTask()
//		if pass.checkCycleFinish(srv, p.owner.ID()) {
//			p.sendReissueAwardMail(srv, pass, p.owner.ID(), mailID)
//			pass.initPass()
//			p.owner.SendCmdToGateway(cl.ID_MSG_L2C_PassUpdate, &cl.L2C_PassUpdate{
//				Ret:       uint32(cret.RET_OK),
//				PassId:    pass.SysId,
//				PassData:  (*cl.PassData)(pass).Clone(),
//				FromReset: true,
//			})
//		}
//		p.Save(passID)
//	}
//}

// 发送补发奖励邮件
func (p *Pass) sendReissueAwardMail(srv servicer, pass *PassData, _ uint64, mailID uint32, genTime int64) {
	awards := pass.calcReissueAward()
	if len(awards) > 0 {
		awards = p.owner.MergeResources(awards)
		PassActiveMail(srv, mailID, awards, p.owner, genTime)
	}
}

func (p *Pass) getPassI(pass *PassData) (PassI, *goxml.PassInfoExt) {
	if pass == nil {
		return nil, nil
	}
	passInfo := goxml.GetData().PassInfoM.Index(pass.SysId)
	if passInfo == nil {
		return nil, nil
	}
	switch passInfo.PassType {
	case uint32(goxml.PassTypeOnce):
		return (*oncePass)(pass), passInfo
	case uint32(goxml.PassTypeCycle):
		return (*cyclePass)(pass), passInfo
	case uint32(goxml.PassTypeActivity):
		return (*actStoryPass)(pass), passInfo
	case uint32(goxml.PassTypeSeason):
		return (*seasonPass)(pass), passInfo
	case uint32(goxml.PassTypeDungeon):
		return (*dungeonPass)(pass), passInfo
	case uint32(goxml.PassTypeTower):
		return (*towerPass)(pass), passInfo
	case uint32(goxml.PassTypeCreateU):
		return (*createUserPass)(pass), passInfo
	case uint32(goxml.PassTypeOpenServer):
		return (*openServer)(pass), passInfo
	case uint32(goxml.PassTypeUserLevel):
		return (*userLevelPass)(pass), passInfo
	case uint32(goxml.PassTypeSkin):
		return (*actStoryPass)(pass), passInfo
	case uint32(goxml.PassTypeSeasonWeek):
		return (*seasonWeekPass)(pass), passInfo
	default:
		return nil, nil
	}
}

// 检查前置战令是否结束
func (p *Pass) GetPreviousPassFinished(preId uint32) (bool, uint32) {
	// 不存在前置战令
	if preId == 0 {
		return true, 0
	}

	// 判断前置战令是否结束
	// 1.购买高级奖励 -> 根据领取完高级奖励判断
	// 2.已领取完基础奖励 + 高级奖励
	pass := p.GetPass(preId)
	if pass == nil {
		l4g.Errorf("GetPreviousPassFinished: no pass, id %d", preId)
		return false, preId
	}

	if len(pass.Receive) == 0 {
		return false, preId
	}

	taskDatas := goxml.GetData().PassTaskInfoM.GetTaskDatasByPassID(preId)
	for _, taskData := range taskDatas {
		state, exist := pass.Receive[taskData.Id]
		if !exist {
			return false, preId
		}

		if len(taskData.FreeClRes) > 0 && !state.Free {
			return false, preId
		}

		if len(taskData.AwardClRes) > 0 && !state.Charge {
			return false, preId
		}

		if len(taskData.Award2ClRes) > 0 && !state.Charge2 {
			return false, preId
		}
	}

	return true, preId
}

// 检查循环类战令的开启
func (p *Pass) CheckCyclePassOpen(srv servicer) {
	for _, cyclePassInfo := range goxml.GetData().PassInfoM.GetAllCyclePass() {
		pass := p.GetPass(cyclePassInfo.Id)
		if pass != nil {
			continue
		}
		if !checkCyclePassOpen(srv, p.owner, cyclePassInfo, pass) {
			continue
		}
		if cyclePassInfo.Replace != 0 {
			replaceData := p.GetPass(cyclePassInfo.Replace)
			if replaceData != nil {
				// 已经开了要等待完成
				continue
			}
		} else {
			beReplace := goxml.GetData().PassInfoM.GetCycleBeReplace(cyclePassInfo.Id)
			if beReplace != 0 {
				beReplaceData := p.GetPass(cyclePassInfo.Id)
				if beReplaceData != nil {
					// 已经开了优先级更高的
					continue
				}
				beReplaceInfo := goxml.GetData().PassInfoM.Index(beReplace)
				if beReplaceInfo != nil && checkCyclePassOpen(srv, p.owner, beReplaceInfo, pass) {
					// 需要开放优先级更高的
					continue
				}
			}
		}

		pass = p.InitPass(cyclePassInfo.Id)
		p.owner.SendCmdToGateway(cl.ID_MSG_L2C_PassUpdate, &cl.L2C_PassUpdate{
			Ret:      uint32(cret.RET_OK),
			PassId:   pass.SysId,
			PassData: p.Flush(pass.SysId),
		})
	}
}
