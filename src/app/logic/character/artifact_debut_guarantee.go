package character

import (
	"app/goxml"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

func (a *ArtifactDebutManager) newGuarantee() *cl.ArtifactDebutGuarantee {
	return &cl.ArtifactDebutGuarantee{}
}

func (a *ArtifactDebutManager) getGuarantee() *cl.ArtifactDebutGuarantee {
	if a.data.Guarantee == nil {
		a.data.Guarantee = a.newGuarantee()
	}
	return a.data.Guarantee
}

func (a *ArtifactDebutManager) resetGuarantee() {
	if a.data.Guarantee != nil {
		a.data.Guarantee.RoundCount = 0
		a.data.Guarantee.RoundGroup = 0
	}
}

func (a *ArtifactDebutManager) SetGuarantee(guarantee *cl.ArtifactDebutGuarantee) {
	a.data.Guarantee = guarantee
}

// 计算保底情况
// @param *cl.ArtifactDebutGuarantee data 临时数据 - 保底数据
// @return ArtifactDebutGuaranteeType 保底类型
func (a *ArtifactDebutManager) calcGuarantee(data *cl.ArtifactDebutGuarantee) uint32 {
	//常规保底
	if goxml.GetData().ArtifactDebutConfigInfoM.IsHitGuarantee(data.RegularCount) {
		return ArtifactDebutGuaranteeTypeRegular
	}

	//轮次保底
	if goxml.GetData().ArtifactDebutGuaranteeInfoM.IsHitGuarantee(data.RoundGroup, data.RoundCount) {
		return ArtifactDebutGuaranteeTypeRound
	}

	return ArtifactDebutGuaranteeTypeNone
}

// 计算剩余保底次数
func (a *ArtifactDebutManager) CalcGuaranteeCount() uint32 {
	guaranteeLeftCount := goxml.GetData().ArtifactDebutConfigInfoM.GetRegularGuarantee()
	if a == nil || a.data == nil || a.data.Guarantee == nil {
		return guaranteeLeftCount
	}
	guaranteeData := a.data.Guarantee
	guaranteeLeftCount = goxml.GetData().ArtifactDebutConfigInfoM.LeftGuarantee(guaranteeData.RegularCount)

	//更新轮次保底
	curInfo := goxml.GetData().ArtifactDebutGuaranteeInfoM.Index(guaranteeData.RoundGroup)
	if curInfo != nil {
		if guaranteeData.RoundCount < curInfo.GuaranteeCount {
			roundLeftCount := curInfo.GuaranteeCount - guaranteeData.RoundCount
			if roundLeftCount < guaranteeLeftCount {
				guaranteeLeftCount = roundLeftCount
			}
		}
	}

	return guaranteeLeftCount
}

// 更新保底数据
// @param bool winWishCard 是否抽中心愿卡
// @param *cl.ArtifactDebutGuarantee data 临时数据 - 保底数据
func (a *ArtifactDebutManager) updateGuarantee(winWishCard bool, data *cl.ArtifactDebutGuarantee) {
	//更新常规保底
	if winWishCard {
		data.RegularCount = 0
	} else {
		data.RegularCount++
	}

	//更新轮次保底
	curInfo := goxml.GetData().ArtifactDebutGuaranteeInfoM.Index(data.RoundGroup)
	if curInfo == nil {
		l4g.Errorf("user: %d ArtifactDebutManager.updateGuarantee: guarantee info not exist, groud:%d",
			a.owner.ID(), data.RoundGroup)
		return
	}
	if winWishCard {
		nextInfo := goxml.GetData().ArtifactDebutGuaranteeInfoM.Index(data.RoundGroup + 1)
		if nextInfo != nil {
			data.RoundCount = 0
			data.RoundGroup = nextInfo.Group
		} else {
			data.RoundCount = curInfo.GuaranteeCount
		}
	} else {
		if data.RoundCount < curInfo.GuaranteeCount {
			data.RoundCount++
		}
	}
}
