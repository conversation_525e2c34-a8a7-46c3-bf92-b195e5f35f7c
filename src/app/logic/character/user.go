package character

import (
	"app/gmxml"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/p2l"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"app/goxml"
	"app/logic/battle"
	"app/logic/event"
	"app/logic/helper"
	"app/logic/helper/monitor"
	"app/logic/helper/sync"
	"app/logic/mail"
	"app/logic/session"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/bt"
	"app/protos/out/cg"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	// "gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	//"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

const (
	syncClients = 5
	syncServers = 20
)

const (
	StateNone = iota
	StateLogin
	StateCreate
	StateOnline
	StateCache
	StateOffline
	StateException
)

const (
	saveTagBase                 = 0x1
	saveTagResource             = 0x2
	saveTagModule               = 0x4
	saveTagBag                  = 0x8
	saveTagModule1              = 0x10
	saveTagModuleGlobalAttr     = 0x20
	saveTagClientInfo           = 0x40
	saveTagWmExtra              = 0x80
	saveTagModule2              = 0x100
	saveTagCrystal              = 0x200
	saveTagModule3              = 0x400
	saveTagAchievementsShowcase = 0x800
	saveTagModule4              = 0x1000
	saveTagModule5              = 0x2000
	saveTagDisorderland         = 0x4000
	saveTagModule6              = 0x8000
	saveTagModule7              = 0x10000
	saveTagModule8              = 0x20000
	saveTagAll                  = 0xFFFFFFFF
)

type User struct {
	gate             *session.Client
	gateID           uint64 //网关服务器ID
	gateAddr         uint64 //网关地址
	cookie           uint64 //客服端所在网关服务器对应的唯一ID
	netAddr          string //客户端地址
	deviceID         string //设备ID
	contextID        uint64 //日志组ID
	age              uint32 //玩家年龄
	roleType         uint32 //玩家类型0:未实名1:已实名
	opID             uint32 //渠道ID
	channel          uint32 //二级渠道
	sign             string //uuid:serverID
	pid              uint32 //37pid
	gid              uint32 //37gid
	cacheExpiredTime int64

	state int

	saveLock   *util.TimeSpinLock
	tenSecLock *util.TimeSpinLock

	//消息同步
	msgSync    *sync.MessageSync
	cseq, sseq uint32

	dbUser            *db.User
	formations        *FormationM
	mails             *mail.Box
	heroes            *HeroM
	equips            *EquipM
	artifacts         *ArtifactM
	gems              *GemM
	emblems           *EmblemM
	mirages           *MirageM
	rites             *RiteM
	operateActivity   *OperateActivityM
	heroesStarUpCosts *HeroStarUpCostsM
	skins             *SkinM
	remains           *RemainM
	activitySumM      *ActivitySumManager
	seasonJewelry     *SeasonJewelryM
	titles            *TitleM
	pokemonM          *PokemonM

	//操作太频繁限制
	operateTimes []int64
	//协议请求间隔限制
	commandTimes map[uint32]int64
	//点赞请求间隔限制
	likeTimes map[uint64]int64

	//客户端与服务器时间偏移 单位ms
	keepAliveTimeOffset     int64
	showKeepAliveTimeOffset bool

	setNameLock bool //改名字锁

	//推荐好友
	recommendedNum uint32 //已推荐次数
	recommendedTm  int64
	recommendedF   map[uint64]int64 //已推荐好友 id=>timestamp

	modules map[uint32]UserModuler //功能模块 功能id=>moduler

	//监控：协议顺序数组
	cmdArray *monitor.Cmds
	//监控：操作次数统计
	opArray []uint32

	adapterExtra string // adapter处理日志需要的信息

	wmos string //完美的os

	hasAutoLogin bool //是否触发过自动登录

	attrCache []int64

	areaId uint32

	cloneHeroAttr []int64

	clientData map[common.FUNCID][]byte

	seasonAddPower int64 // 临时在user加的缓存，后续可优化到对应的结构体
}

func NewUser(gate *session.Client, cookie uint64, uuid string, serverID uint64, na string) *User {
	user := new(User)
	user.setNetInfo(gate, cookie, na)
	user.init(uuid, serverID, UserInitFromRealUser)
	return user
}

// @param fromRealUser 是否是真实用户init.true 真实用户。false battleUser
func (u *User) init(uuid string, serverID uint64, fromRealUser bool) {
	u.SetState(StateNone)
	u.dbUser = &db.User{
		Uuid:     uuid,
		ServerId: serverID,
	}
	u.sign = fmt.Sprintf("%s:%d", uuid, serverID)

	u.formations = NewFormationM(u)
	u.heroes = newHeroM(u)
	u.equips = NewEquipM(u)
	u.artifacts = newArtifactM(u)
	u.gems = newGemM(u)
	u.emblems = newEmblemM(u)
	u.skins = NewSkinM(u)
	u.modules = make(map[uint32]UserModuler)
	u.initModule(fromRealUser)
	if fromRealUser {
		u.mirages = newMirageM(u)
		u.ClearRecommendedFriend()
		u.operateActivity = newOperateActivityM(u)
		u.heroesStarUpCosts = newHeroStarUpCostsM(u)
	}
	u.rites = newRiteM(u)
	u.remains = newRemainM(u)
	u.activitySumM = newActivitySumManager(u)
	u.seasonJewelry = NewSeasonJewelryM(u)
	u.titles = NewTitleM(u)
	u.pokemonM = newPokemonM(u)
}

// 1. Create消息CSeq必须为1，SSeq为0
// 2. Ping/Pong消息不需要改变CSeq, SSeq
// 3. 切换账号，Login消息CSeq重置为1, SSeq为0
func (u *User) InitMessageSync(cookie uint64) {
	u.msgSync = sync.NewMessageSync(cookie, syncClients, syncServers)
}

func (u *User) GateID() uint64  { return u.gateID }
func (u *User) Cookie() uint64  { return u.cookie }
func (u *User) NetAddr() string { return u.netAddr }

func (u *User) SetState(state int) { u.state = state }
func (u *User) State() int         { return u.state }
func (u *User) LockSetName()       { u.setNameLock = true }
func (u *User) UnlockSetName()     { u.setNameLock = false }

func (u *User) UUID() string          { return u.dbUser.Uuid }
func (u *User) ServerID() uint64      { return u.dbUser.ServerId }
func (u *User) ID() uint64            { return u.dbUser.Id }
func (u *User) DCID() uint32          { return u.dbUser.Base.DcId }
func (u *User) Name() string          { return u.dbUser.Name }
func (u *User) Level() uint32         { return u.dbUser.Base.Level }
func (u *User) SetLevel(level uint32) { u.dbUser.Base.Level = level }
func (u *User) setLevelTm(now int64)  { u.dbUser.Base.LevelTm = now }
func (u *User) LevelTm() int64        { return u.dbUser.Base.LevelTm }
func (u *User) SetPower(power int64) {
	u.dbUser.Base.Power = power
	u.dbUser.Base.PowerTm = time.Now().Unix()
	u.setSaveTag(saveTagBase)
}
func (u *User) SetPowerTm(now int64) { u.dbUser.Base.PowerTm = now }
func (u *User) powerTm() int64       { return u.dbUser.Base.PowerTm }
func (u *User) Career() uint32       { return u.dbUser.Base.Career }
func (u *User) BaseID() uint32 {
	return u.dbUser.Base.BaseId
} //主角BaseID

func (u *User) ExpireTime() []int64 {
	return u.dbUser.Base.ExpiredTime
}

func (u *User) SetAreaId(areaId uint32) {
	u.areaId = areaId
}
func (u *User) GetArea() uint32 { return u.areaId }

func (u *User) CanMute() bool { return u.dbUser.Base.CanMute > 0 }

func (u *User) GetMute() uint32 { return u.dbUser.Base.CanMute }

func (u *User) SetLang(lang string) {
	u.dbUser.Base.Lang = lang
	u.setSaveTag(saveTagBase)
}

func (u *User) SetPush(id uint32, isPush bool) {
	if u.dbUser.Base.PushSetting == nil {
		u.dbUser.Base.PushSetting = make(map[uint32]bool)
	}
	u.dbUser.Base.PushSetting[id] = isPush
	u.setSaveTag(saveTagBase)
}

func (u *User) FlushPushSetting() map[uint32]bool {
	retPush := make(map[uint32]bool, len(u.dbUser.Base.PushSetting))
	for k, v := range u.dbUser.Base.PushSetting {
		retPush[k] = v
	}

	return retPush
}

// GetPushSetting : true: 不推送，false：推送
func (u *User) GetPushSetting(id uint32) bool {
	return u.dbUser.Base.PushSetting[id]
}

func (u *User) GetLang() string { return u.dbUser.Base.Lang }

// 历史最高战力
func (u *User) SetMaxPower(power int64) {
	u.dbUser.Base.MaxPower = power
	u.setSaveTag(saveTagBase)
}

func (u *User) GetIcon() uint32 {
	return u.dbUser.Base.BaseId & 0xFFFF //nolint:mnd
}

func (u *User) GetFrame() uint32 {
	frame := u.dbUser.Base.BaseId & 0xFFFF0000 //nolint:mnd
	frame = frame >> 16                        //nolint:mnd
	return frame
}

func (u *User) GetImage() uint32 {
	return u.dbUser.Base.Image
}
func (u *User) GetShowHero() uint32 {
	return u.dbUser.Base.ShowHero
}

func (u *User) GetChatBubbles() uint32 {
	return u.dbUser.Base.ChatBubbles
}

func (u *User) GetTitle() uint32 {
	return u.dbUser.Base.Title
}

func (u *User) SetIcon(id uint32, expired int64) {
	u.dbUser.Base.BaseId = (u.dbUser.Base.BaseId & 0xFFFF0000) | (id & 0xFFFF) //nolint:mnd
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_ICON_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetFrame(id uint32, expired int64) {
	u.dbUser.Base.BaseId = (u.dbUser.Base.BaseId & 0xFFFF) | (id << 16) //nolint:mnd
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_FARM_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetImage(id uint32, expired int64) {
	u.dbUser.Base.Image = id
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_IMAGE_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetChatBubble(id uint32, expired int64) {
	u.dbUser.Base.ChatBubbles = id
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_CHAT_BUBBLES_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetTitle(id uint32, expired int64) {
	u.dbUser.Base.Title = id
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_TITLE_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetIconExpire(expired int64) {
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_ICON_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetFrameExpire(expired int64) {
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_FARM_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetImageExpire(expired int64) {
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_IMAGE_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetChatBubbleExpire(expired int64) {
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_CHAT_BUBBLES_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) SetTitleExpire(expired int64) {
	if len(u.dbUser.Base.ExpiredTime) < int(common.DB_BASE_USER_MAX_EXPIRED) {
		temp := make([]int64, common.DB_BASE_USER_MAX_EXPIRED)
		copy(temp, u.dbUser.Base.ExpiredTime)
		u.dbUser.Base.ExpiredTime = temp
	}
	u.dbUser.Base.ExpiredTime[common.DB_BASE_USER_TITLE_EXPIRED] = expired
	u.setSaveTag(saveTagBase)
}

func (u *User) UpQuality(baseID uint32) {
	u.dbUser.Base.BaseId = baseID
	u.setSaveTag(saveTagBase)
}
func (u *User) CreateTime() int64             { return u.dbUser.Base.CreateTime }
func (u *User) OnlineTime() int64             { return u.dbUser.Base.OnlineTime }
func (u *User) OfflineTime() int64            { return u.dbUser.Base.OfflineTime }
func (u *User) LastOnlineTime() int64         { return u.dbUser.Base.LastOnlineTime }
func (u *User) EquipmentEnchantCount() uint32 { return u.dbUser.Base.EquipEnchantCount }
func (u *User) AddEquipmentEnchantCount() {
	u.dbUser.Base.EquipEnchantCount++
	u.setSaveTag(saveTagBase)
}

func (u *User) SeasonArenaSkipFormation() bool {
	return u.dbUser.Base.SeasonArenaSkipFormation
}

func (u *User) SetSeasonArenaSkipFormation(flag bool) {
	u.dbUser.Base.SeasonArenaSkipFormation = flag
	u.setSaveTag(saveTagBase)
}

func (u *User) TrialGuide() bool { return u.dbUser.Base.TrialGuide }
func (u *User) SetTrailGuideFinish() {
	u.dbUser.Base.TrialGuide = true
	u.setSaveTag(saveTagBase)
}

func (u *User) SetArenaPower(power int64) {
	u.dbUser.Base.ArenaPower = power
	u.setSaveTag(saveTagBase)
}

func (u *User) GetDefensePower(formationID uint32) int64 {
	if u.dbUser.Base.DefensePower == nil {
		return int64(0)
	}
	return u.dbUser.Base.DefensePower[formationID]
}

func (u *User) SetDefensePower(formationID uint32, power int64) {
	if u.dbUser.Base.DefensePower == nil {
		u.dbUser.Base.DefensePower = make(map[uint32]int64)
	}
	u.dbUser.Base.DefensePower[formationID] = power
	u.setSaveTag(saveTagBase)
}

func (u *User) IsOnline() bool { return u.state == StateOnline }
func (u *User) LogNameID() string {
	if u != nil {
		return u.Name() + "," + strconv.FormatUint(u.ID(), 10)
	}
	return ""
}
func (u *User) Sign() string { return u.sign }

// 创角第几天
func (u *User) CreateDay(now int64) uint32 {
	return helper.DaysBetweenTimes(now, u.CreateTime()) + 1
}

func (u *User) DeviceID() string             { return u.deviceID }
func (u *User) SetDeviceID(deviceID string)  { u.deviceID = deviceID }
func (u *User) OpID() uint32                 { return u.opID }
func (u *User) SetOpID(id uint32)            { u.opID = id }
func (u *User) Pid() uint32                  { return u.pid }
func (u *User) SetPid(id uint32)             { u.pid = id }
func (u *User) Gid() uint32                  { return u.gid }
func (u *User) SetGid(id uint32)             { u.gid = id }
func (u *User) Age() uint32                  { return u.age }
func (u *User) RoleType() uint32             { return u.roleType }
func (u *User) SetAge(a uint32)              { u.age = a }
func (u *User) SetRoleType(r uint32)         { u.roleType = r }
func (u *User) IsUnderAge() bool             { return u.age < 18 } //nolint:mnd
func (u *User) Channel() uint32              { return u.channel }
func (u *User) SetChannel(channel uint32)    { u.channel = channel }
func (u *User) AdapterExtra() string         { return u.adapterExtra }
func (u *User) SetAdapterExtra(extra string) { u.adapterExtra = extra }
func (u *User) GetAccountTag() uint32 {
	return u.dbUser.Base.AccountTag
}

func (u *User) SetAccountTag(tag uint32) {
	u.dbUser.Base.AccountTag = tag
	u.setSaveTag(saveTagBase)
}

func (u *User) SetMute(tag uint32) {
	u.dbUser.Base.CanMute = tag
	u.setSaveTag(saveTagBase)
}

func (u *User) SetDBUser(user *db.User) {
	u.dbUser = user
}

func (u *User) GetDBUser() *db.User {
	return u.dbUser
}

// 完美的os
func (u *User) WmOs() string {
	return u.wmos
}

func (u *User) GetHotfixVersion() int64 {
	return u.dbUser.Base.HotfixVersion
}

func (u *User) SetHotfixVersion(v int64) {
	u.dbUser.Base.HotfixVersion = v
	u.setSaveTag(saveTagBase)
}

//nolint:varnamelen
func (u *User) SetGameData(g *helper.GameData) {
	u.age = g.Age
	u.opID = g.OpID
	u.channel = g.Channel
	u.deviceID = g.DeviceID
	u.roleType = g.RoleType
	u.wmos = g.WmOs
	if g.SqExtra != nil {
		//37特殊处理
		u.pid = uint32(g.SqExtra.Pid)
		u.gid = uint32(g.SqExtra.Gid)
		u.channel = uint32(g.SqExtra.Gid) //37的情况用户的channel就等于gid
	}
}

func (u *User) Star() uint32 { return u.dbUser.Base.Star }
func (u *User) SetStar(star uint32) {
	u.dbUser.Base.Star = star
	u.setSaveTag(saveTagBase)
}
func (u *User) Vip() uint32 { return u.dbUser.Base.Vip }

func (u *User) VipExp() uint32 {
	return u.dbUser.Base.VipExp
}

func (u *User) Power() int64 { return u.dbUser.Base.Power }

// 获取历史最高战力
func (u *User) MaxPower() int64 { return u.dbUser.Base.MaxPower }

// 竞技场防守战力
func (u *User) ArenaDefensePower() int64 { return u.dbUser.Base.ArenaPower }

func (u *User) HeroSlot() uint32 {
	return u.dbUser.Module.HeroSlot + goxml.GetData().ConfigInfoM.HeroSlotMinNum
}
func (u *User) SetHeroSlot(num uint32) {
	if num > goxml.GetData().ConfigInfoM.HeroSlotMinNum {
		u.dbUser.Module.HeroSlot = num - goxml.GetData().ConfigInfoM.HeroSlotMinNum
		u.setSaveTag(saveTagModule)
	}
}
func (u *User) GetHeroSlot() uint32 { return u.dbUser.Module.HeroSlot }

func (u *User) HeroStarLimit() uint32 {
	if u.dbUser.Module2.HeroStarLimit == 0 {
		return goxml.StarLimitMinStarLv - 1
	}
	return u.dbUser.Module2.HeroStarLimit
}
func (u *User) SetHeroStarLimit(star uint32) {
	u.dbUser.Module2.HeroStarLimit = star
	u.setSaveTag(saveTagModule2)
}

func (u *User) GuildID() uint64 {
	g := u.UserGuild()
	if g != nil {
		return g.GuildID()
	}
	return uint64(0)
}

func (u *User) GuildName() string { return u.UserGuild().GuildName() }

func (u *User) SetCSeq(cseq uint32)        { u.cseq = cseq }
func (u *User) SetSSeq(sseq uint32)        { u.sseq = sseq }
func (u *User) MsgSync() *sync.MessageSync { return u.msgSync }
func (u *User) SetModuleDirty()            { u.dbUser.ModuleOp = db.Op_Dirty }
func (u *User) SetName(name string) {
	u.dbUser.Name = name
}

func (u *User) ContextID() uint64      { return u.contextID }
func (u *User) SetContextID(id uint64) { u.contextID = id }

func (u *User) setCacheExpiredTime(tm int64) { u.cacheExpiredTime = tm }
func (u *User) getCacheExpiredTime() int64   { return u.cacheExpiredTime }

func (u *User) lastRechargeTime() int64 { return u.dbUser.Base.LastRechargeTime }
func (u *User) setLastRechargeTime(tm int64) {
	u.dbUser.Base.LastRechargeTime = tm
	u.setSaveTag(saveTagBase)
}

func (u *User) itemsBag() map[uint32]uint32 {
	return u.dbUser.Bag.Items
}

func (u *User) expiredItemsBag() map[uint32]*cl.ExpiredItems {
	return u.dbUser.Bag.ExpiredItem
}

func (u *User) fragmentsBag() map[uint32]uint32 {
	return u.dbUser.Bag.Fragments
}

func (u *User) tokensBag() map[uint32]uint64 {
	return u.dbUser.Bag.Tokens
}

func (u *User) artifactFragmentsBag() map[uint32]uint32 {
	return u.dbUser.Bag.ArtifactFragments
}

func (u *User) emblemFragmentsBag() map[uint32]uint32 {
	return u.dbUser.Bag.EmblemFragments
}

func (u *User) remainFragmentsBag() map[uint32]uint32 {
	return u.dbUser.Bag.RemainFragments
}

func (u *User) pokemonFragmentsBag() map[uint32]uint32 {
	return u.dbUser.Bag.PokemonFragments
}

func (u *User) MailBox() *mail.Box {
	return u.mails
}

func (u *User) HeroManager() *HeroM {
	return u.heroes
}

func (u *User) EquipManager() *EquipM {
	return u.equips
}

func (u *User) FormationManager() *FormationM {
	return u.formations
}

func (u *User) ArtifactManager() *ArtifactM {
	return u.artifacts
}

func (u *User) GemManager() *GemM {
	return u.gems
}

func (u *User) EmblemManager() *EmblemM {
	return u.emblems
}

func (u *User) MirageManager() *MirageM {
	return u.mirages
}

func (u *User) RiteManager() *RiteM {
	return u.rites
}

func (u *User) RemainM() *RemainM {
	return u.remains
}

func (u *User) ActivitySumM() *ActivitySumManager {
	return u.activitySumM
}

func (u *User) SkinManager() *SkinM {
	return u.skins
}

func (u *User) TitleManager() *TitleM {
	return u.titles
}

func (u *User) SeasonJewelryManager() *SeasonJewelryM {
	return u.seasonJewelry
}

func (u *User) PokemonManager() *PokemonM {
	return u.pokemonM
}

func (u *User) CheckSetNameLock() bool {
	return u.setNameLock
}

func (u *User) CmdArray() *monitor.Cmds {
	return u.cmdArray
}

func (u *User) OperateActivityM() *OperateActivityM {
	return u.operateActivity
}

func (u *User) HeroStarUpCostsManager() *HeroStarUpCostsM {
	return u.heroesStarUpCosts
}

func (u *User) SetFirstMazePower() {
	u.dbUser.Base.FirstMazePower = u.MaxPower()
	u.setSaveTag(saveTagBase)
}

func (u *User) FirstMazePower() int64 {
	return u.dbUser.Base.FirstMazePower
}

// 上线处理
func (u *User) online(info interface{}, srv servicer) uint32 {
	switch recv := info.(type) {
	case *r2l.R2L_Login:
		u.LoadLoginUser(recv, srv)
	case *r2l.R2L_Create:
		u.loadCreateUser(recv.User, srv)
	default:
		return uint32(cret.RET_ERROR)
	}

	if !u.loadFinished() {
		u.SetState(StateException)
		return uint32(cret.RET_ERROR)
	}
	return uint32(cret.RET_OK)
}

func (u *User) SendCmdToGateway(id cl.ID, msg interface{}) {
	ph := &parse.PackHead{
		Cmd: uint32(id),
		UID: u.cookie,
	}
	switch u.state {
	case StateOnline:
		u.pushMsg(ph, msg)
		u.gate.Write(ph, msg)
	case StateCache:
		u.pushMsg(ph, msg)
	default:
		if u.gate != nil {
			u.gate.Write(ph, msg)
		} else {
			l4g.Errorf("[user] %d send msg failed: %+v %s", u.ID(), ph, msg)
		}
	}
}

func (u *User) SendLoginCmdToGateway(flags uint32, msg interface{}) {
	ph := &parse.PackHead{
		Cmd:   uint32(cg.ID_MSG_G2C_Login),
		UID:   u.cookie,
		Flags: flags,
	}
	if u.gate != nil {
		u.gate.Write(ph, msg)
	} else {
		l4g.Errorf("[user] %d send msg failed: %+v %s", u.ID(), ph, msg)
	}
}

// 消息增量
func (u *User) pushMsg(ph *parse.PackHead, msg interface{}) {
	u.sseq++
	if u.cseq == 0 {
		//将登陆创角协议之后的推送消息内容保存
		u.SetCSeq(1)
	}
	ph.CSeq = u.cseq
	ph.SSeq = u.sseq
	if err := u.msgSync.Push(u.cseq, u.sseq, &message{*ph, msg}); err != nil {
		l4g.Errorf("user %d sync msg failed, head(%+v), %s", u.ID(), ph, err)
	}

	l4g.Debugf("[user] %d push msg head: %+v", u.ID(), ph)
}

// 针对消息处理结果在异步回调函数中处理，保证连续性
func (u *User) PushNilMsg(cmd uint32, ret bool) {
	if u.cseq == u.msgSync.MaxClientSeq()+1 &&
		u.sseq == u.msgSync.MaxServerSeq() {
		u.pushMsg(new(parse.PackHead), nil)
		l4g.Debugf("[user] %d push nil msg, cmd: %d cseq: %d sseq: %d result: %v",
			u.ID(), cmd, u.cseq, u.sseq+1, ret)
	}
}

func (u *User) onlineFromCache(gate *session.Client, cookie uint64, na string) {
	u.setNetInfo(gate, cookie, na)
}

func (u *User) setNetInfo(gate *session.Client, cookie uint64, na string) {
	u.gate = gate
	u.gateID = gate.ID()
	u.gateAddr = gate.NetAddr()
	u.cookie = cookie
	u.netAddr = na
}

func (u *User) clearNetInfo() {
	u.gate = nil
	u.gateID = 0
	u.gateAddr = 0
	u.cookie = 0
	u.netAddr = ""
}

type message struct {
	ph   parse.PackHead
	data interface{}
}

func (u *User) Sync(msgs []interface{}) {
	for _, tmp := range msgs {
		msg := tmp.(*message)
		ph := &(msg.ph)
		if ph.Cmd != 0 {
			ph.UID = u.cookie
			u.sseq = ph.SSeq
			u.gate.Write(ph, msg.data)
		}
		l4g.Debugf("sync: %d pack head(%+v) seq(%d %d)", u.ID(), ph, u.cseq, u.sseq)
	}
}

func (u *User) offline(srv servicer, reason uint32) {
	if u.State() == StateOnline {
		u.dbUser.Base.OfflineTime = time.Now().Unix()
		u.setSaveTag(saveTagBase)
		u.ClearRecommendedFriend()
		u.AddDailyPlayTime()
		u.LogLogout(srv, reason)
		u.CmdArray().Add(uint32(cg.ID_MSG_C2G_Offline), true)
		u.LogMonitor(srv)
		u.cmdArray.Clear()
		u.PutRoleToAccount(srv, uint32(log.SUB_TYPE_ID_ACCOUNT_OFFLINE))
	}
	u.SetState(StateOffline)
	u.SendCmdToGateway(cl.ID(cg.ID_MSG_G2C_Offline), &cg.G2C_Offline{})
}

func (u *User) SendSelfToClient() {
	msg := &cl.L2C_GetUser{
		User: &cl.User{
			Id:                u.ID(),
			Name:              u.Name(),
			Level:             u.Level(),
			Career:            u.Career(),
			BaseId:            u.BaseID(),
			Diamond:           u.Diamond(),
			Gold:              u.Gold(),
			Exp:               u.Exp(),
			CreateTime:        u.CreateTime(),
			Energy:            u.GetEnergy(),
			DailyPlayLimit:    u.DailyPlayLimit(),
			HeroSlot:          u.HeroSlot(),
			NumInfo:           u.CloneNumData(),
			Power:             u.Power(),
			GuildId:           u.GuildID(),
			GuildName:         u.GuildName(),
			Vip:               u.Vip(),
			VipExp:            u.VipExp(),
			HeroStarLimit:     u.HeroStarLimit(),
			Lang:              u.GetLang(),
			RechargeAmount:    u.GetRecharge(),
			Top5Heros:         u.CloneTop5(),
			MaxPower:          u.MaxPower(),
			ExpireTime:        u.ExpireTime(),
			CrystalPower:      u.GetCrystalPower(),
			AreaId:            u.GetArea(),
			OfflineTime:       u.OfflineTime(),
			Image:             u.GetImage(),
			EmblemSlot:        u.GetEmblemSlotCount(),
			SeasonLv:          u.GetSeasonLevel(),
			SeasonPower:       u.GetSeasonPower(),
			SeasonId:          u.GetSeasonID(),
			SeasonEnter:       u.GetSeasonEnter(),
			Coupon:            u.Coupon(),
			SeasonTopPower:    u.GetSeasonTopPower(),
			AssistantUnlock:   u.AssistantUnlockMark(),
			ChatBubbles:       u.GetChatBubbles(),
			MaxBlessedHeroCnt: u.GetMaxBlessedHeroCnt(),
			Duel:              u.Duel().Flush(),
			SeasonEnterTime:   u.GetSeasonEnterTime(),
			Title:             u.GetTitle(),
			CanMute:           u.GetMute(),
		},
	}
	//msg.User.Face = make([]uint32, goxml.MaxFacePart)
	//copy(msg.User.Face, u.dbUser.Base.Face)
	u.SendCmdToGateway(cl.ID_MSG_L2C_GetUser, msg)
}

func (u *User) SendHeroesToClient() {
	msg := &cl.L2C_HeroList{
		Ret:    uint32(cret.RET_OK),
		Heroes: u.heroes.FlushAll(),
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_HeroList, msg)
}

func (u *User) SendEquipsToClient(equipInfos []*cl.Equipment) {
	if equipInfos == nil {
		msg := &cl.L2C_EquipGet{
			Ret:      uint32(cret.RET_OK),
			Equips:   u.equips.FlushAll(),
			AutoRare: u.GetEquipAutoDecomposeRare(),
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_EquipGet, msg)
	} else {
		equipMsg := &cl.L2C_EquipUpdate{
			Ret:    uint32(cret.RET_OK),
			Equips: equipInfos,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_EquipUpdate, equipMsg)
	}

}

func (u *User) SendArtifactsToClient() {
	msg := &cl.L2C_ArtifactList{
		Ret:       uint32(cret.RET_OK),
		Artifacts: u.artifacts.FlushAll(),
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactList, msg)
}

func (u *User) SendGemsToClient(gemInfos []*cl.GemInfo) {
	if gemInfos == nil {
		msg := &cl.L2C_GetGems{
			Ret:  uint32(cret.RET_OK),
			Gems: u.gems.FlushAll(),
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_GetGems, msg)
	} else {
		gemMsg := &cl.L2C_UpdateGems{
			Ret:  uint32(cret.RET_OK),
			Gems: gemInfos,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_UpdateGems, gemMsg)
	}
}

// 获取所有的被动技能id
func (u *User) GetGlobalRaisePs() []uint64 {
	raisePSs := make([]uint64, 0, 1)
	raisePSs = append(raisePSs, u.GuildTalent().GetRaisePSs()...)
	raisePSs = append(raisePSs, u.TitleManager().GetRaisePSs()...)
	return raisePSs
}

func (u *User) SendGlobalAttrToClient() {
	globalAttr := &cl.GlobalAttr{
		ArtifactAttr: u.ArtifactManager().FlushArtifactGlobalAttr(),
		MemoryAttr:   u.Memory().FlushMemoryGlobalAttr(),
		GuildAttr:    u.GuildTalent().FlushGuildTalentGlobalAttr(),
		HandbookAttr: u.HandbookManager().FlushHandbookGlobalAttr(),
		// CrystalBlessingAttr: u.Crystal().FlushCrystalGlobalAttrs(),
		GoddessContractAttr: u.GoddessContract().FlushGoddessGlobalAttr(),
		ShareGrowth:         u.ShareGrowth().Flush(),
	}
	msg := &cl.L2C_GlobalAttrGet{
		Ret:        uint32(cret.RET_OK),
		GlobalAttr: globalAttr,
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_GlobalAttrGet, msg)
}

func (u *User) SendEmblemsToClient(emblemInfos []*cl.EmblemInfo) {
	if emblemInfos == nil {
		msg := &cl.L2C_EmblemGet{
			Ret:      uint32(cret.RET_OK),
			Emblems:  u.emblems.FlushAll(),
			AutoRare: u.GetEmblemAutoDecomposeRare(),
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_EmblemGet, msg)
	} else {
		emblemMsg := &cl.L2C_EmblemUpdate{
			Ret:     uint32(cret.RET_OK),
			Emblems: emblemInfos,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_EmblemUpdate, emblemMsg)
	}
}

func (u *User) SendTasksToClient(taskType uint32) {
	smsg := &cl.L2C_TaskGetInfo{
		Ret:  uint32(cret.RET_OK),
		Type: taskType,
	}
	if taskType == 0 {
		smsg.Achieve = u.Achieve().Flush()
		smsg.DailyTask = u.DailyTask().Flush()
		smsg.WeeklyTask = u.WeeklyTask().Flush()
		smsg.MonthlyTask = u.MonthlyTask().Flush()
		smsg.LineTask = u.LineTask().Flush()
	} else {
		if taskType == uint32(common.TASK_RESET_TYPE_RET_ACHIEVE) {
			smsg.Achieve = u.Achieve().Flush()
		}
		if taskType == uint32(common.TASK_RESET_TYPE_RET_DAILY) {
			smsg.DailyTask = u.DailyTask().Flush()
		}
		if taskType == uint32(common.TASK_RESET_TYPE_RET_WEEKLY) {
			smsg.WeeklyTask = u.WeeklyTask().Flush()
		}
		if taskType == uint32(common.TASK_RESET_TYPE_RET_MONTHLY) {
			smsg.MonthlyTask = u.MonthlyTask().Flush()
		}
		if taskType == uint32(common.TASK_RESET_TYPE_RET_LINE) {
			smsg.LineTask = u.LineTask().Flush()
		}
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_TaskGetInfo, smsg)
}

func (u *User) SendNewMailTipToClient(isBagFull bool) {
	msg := &cl.L2C_NewMailTip{
		Ret:     uint32(cret.RET_OK),
		BagFull: isBagFull,
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_NewMailTip, msg)
}

func (u *User) SendCrystalToClient() {
	/*
		crystal := u.Crystal()
		if crystal.IsCrystalOpened() {
			msg := &cl.L2C_CrystalGetAllData{
				Ret:  uint32(cret.RET_OK),
				Data: crystal.Flush(),
			}
			u.SendCmdToGateway(cl.ID_MSG_L2C_CrystalGetAllData, msg)
		}
	*/
}

func (u *User) SendPushGiftToClient() {
	smsg := &cl.L2C_PushGiftGetData{
		Ret: uint32(cret.RET_OK),
	}

	smsg.PushGifts = u.PushGift().Flush()
	u.SendCmdToGateway(cl.ID_MSG_L2C_PushGiftGetData, smsg)
}

func (u *User) SendRitesToClient() {
	msg := &cl.L2C_RiteGetData{
		Ret:   uint32(cret.RET_OK),
		Rites: u.rites.FlushAll(),
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_RiteGetData, msg)
}

func (u *User) SendSkinsToClient() {
	msg := &cl.L2C_SkinList{
		Ret:   uint32(cret.RET_OK),
		Skins: u.SkinManager().FlushAll(),
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_SkinList, msg)
}

func (u *User) SendPreSeasonToClient(srv servicer) {
	smsg := &cl.L2C_PreSeasonGetData{
		Ret: uint32(cret.RET_OK),
	}
	if u.PreSeason().IsOpen(srv) {
		smsg.PreSeason = u.PreSeason().Flush2Client()
	} else {
		smsg.PreSeason = u.PreSeason().Flush2Client()
		if smsg.PreSeason == nil {
			smsg.PreSeason = &cl.PreSeason{}
		}
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_PreSeasonGetData, smsg)
}

func (u *User) SendSeasonJewelryToClient(datas []*cl.SeasonJewelry) {
	if datas == nil {
		u.SeasonJewelryManager().FlushSeasonJewelryListWithOrder()
	} else {
		jMsg := &cl.L2C_SeasonJewelryUpdate{
			Ret:         uint32(cret.RET_OK),
			JewelryList: datas,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_SeasonJewelryUpdate, jMsg)
	}
}

// clone玩家数据
//
//nolint:funlen,exhaustive
func (u *User) clone() *db.User {
	dbUser := *u.dbUser

	switch dbUser.BaseOp {
	case db.Op_None:
		dbUser.Base = nil
	case db.Op_Dirty:
		u.dbUser.BaseOp = db.Op_None
		dbUser.Base = u.dbUser.Base.Clone()
	}

	switch dbUser.ResourceOp {
	case db.Op_None:
		dbUser.Resource = nil
	case db.Op_Dirty:
		u.dbUser.ResourceOp = db.Op_None
		dbUser.Resource = u.dbUser.Resource.Clone()
	}

	switch dbUser.ModuleOp {
	case db.Op_None:
		dbUser.Module = nil
	case db.Op_Dirty:
		u.dbUser.ModuleOp = db.Op_None
		dbUser.Module = u.dbUser.Module.Clone()
	}

	switch dbUser.BagOp {
	case db.Op_None:
		dbUser.Bag = nil
	case db.Op_Dirty:
		u.dbUser.BagOp = db.Op_None
		dbUser.Bag = u.dbUser.Bag.Clone()
	}

	switch dbUser.Module1Op {
	case db.Op_None:
		dbUser.Module1 = nil
	case db.Op_Dirty:
		u.dbUser.Module1Op = db.Op_None
		dbUser.Module1 = u.dbUser.Module1.Clone()
	}

	switch dbUser.ModuleGlobalAttrOp {
	case db.Op_None:
		dbUser.ModuleGlobalAttr = nil
	case db.Op_Dirty:
		u.dbUser.ModuleGlobalAttrOp = db.Op_None
		dbUser.ModuleGlobalAttr = u.dbUser.ModuleGlobalAttr.Clone()
	}

	switch dbUser.ClientInfoOp {
	case db.Op_None:
		dbUser.ClientInfo = nil
	case db.Op_Dirty:
		u.dbUser.ClientInfoOp = db.Op_None
		dbUser.ClientInfo = u.dbUser.ClientInfo.Clone()
	}

	switch dbUser.Module2Op {
	case db.Op_None:
		dbUser.Module2 = nil
	case db.Op_Dirty:
		u.dbUser.Module2Op = db.Op_None
		dbUser.Module2 = u.dbUser.Module2.Clone()
	}

	switch dbUser.CrystalOp {
	case db.Op_None:
		dbUser.Crystal = nil
	case db.Op_Dirty:
		u.dbUser.CrystalOp = db.Op_None
		dbUser.Crystal = u.dbUser.Crystal.Clone()
	}

	switch dbUser.Module3Op {
	case db.Op_None:
		dbUser.Module3 = nil
	case db.Op_Dirty:
		u.dbUser.Module3Op = db.Op_None
		dbUser.Module3 = u.dbUser.Module3.Clone()
	}

	switch dbUser.AchievementsShowcaseOp {
	case db.Op_None:
		dbUser.AchievementsShowcase = nil
	case db.Op_Dirty:
		dbUser.AchievementsShowcaseOp = db.Op_None
		dbUser.AchievementsShowcase = u.dbUser.AchievementsShowcase.Clone()
	}

	switch dbUser.Module4Op {
	case db.Op_None:
		dbUser.Module4 = nil
	case db.Op_Dirty:
		u.dbUser.Module4Op = db.Op_None
		dbUser.Module4 = u.dbUser.Module4.Clone()
	}

	switch dbUser.Module5Op {
	case db.Op_None:
		dbUser.Module5 = nil
	case db.Op_Dirty:
		u.dbUser.Module5Op = db.Op_None
		dbUser.Module5 = u.dbUser.Module5.Clone()
	}

	switch dbUser.DisorderlandOp {
	case db.Op_None:
		dbUser.Disorderland = nil
	case db.Op_Dirty:
		u.dbUser.DisorderlandOp = db.Op_None
		dbUser.Disorderland = u.dbUser.Disorderland.Clone()
	}

	switch dbUser.Module6Op {
	case db.Op_None:
		dbUser.Module6 = nil
	case db.Op_Dirty:
		u.dbUser.Module6Op = db.Op_None
		dbUser.Module6 = u.dbUser.Module6.Clone()
	}

	switch dbUser.Module7Op {
	case db.Op_None:
		dbUser.Module7 = nil
	case db.Op_Dirty:
		u.dbUser.Module7Op = db.Op_None
		dbUser.Module7 = u.dbUser.Module7.Clone()
	}

	switch dbUser.Module8Op {
	case db.Op_None:
		dbUser.Module8 = nil
	case db.Op_Dirty:
		u.dbUser.Module8Op = db.Op_None
		dbUser.Module8 = u.dbUser.Module8.Clone()
	}

	return &dbUser
}

// 仅设置存储标识，不真正落地，由定时器落地
func (u *User) setSaveTag(tag uint32) {
	if tag&saveTagBase != 0 {
		u.dbUser.BaseOp = db.Op_Dirty
	}
	if tag&saveTagResource != 0 {
		u.dbUser.ResourceOp = db.Op_Dirty
	}
	if tag&saveTagModule != 0 {
		u.dbUser.ModuleOp = db.Op_Dirty
	}
	if tag&saveTagBag != 0 {
		u.dbUser.BagOp = db.Op_Dirty
	}
	if tag&saveTagModule1 != 0 {
		u.dbUser.Module1Op = db.Op_Dirty
	}
	if tag&saveTagModuleGlobalAttr != 0 {
		u.dbUser.ModuleGlobalAttrOp = db.Op_Dirty
	}
	if tag&saveTagClientInfo != 0 {
		u.dbUser.ClientInfoOp = db.Op_Dirty
	}
	if tag&saveTagModule2 != 0 {
		u.dbUser.Module2Op = db.Op_Dirty
	}
	if tag&saveTagCrystal != 0 {
		u.dbUser.CrystalOp = db.Op_Dirty
	}
	if tag&saveTagModule3 != 0 {
		u.dbUser.Module3Op = db.Op_Dirty
	}
	if tag&saveTagAchievementsShowcase != 0 {
		u.dbUser.AchievementsShowcaseOp = db.Op_Dirty
	}
	if tag&saveTagModule4 != 0 {
		u.dbUser.Module4Op = db.Op_Dirty
	}
	if tag&saveTagModule5 != 0 {
		u.dbUser.Module5Op = db.Op_Dirty
	}
	if tag&saveTagDisorderland != 0 {
		u.dbUser.DisorderlandOp = db.Op_Dirty
	}
	if tag&saveTagModule6 != 0 {
		u.dbUser.Module6Op = db.Op_Dirty
	}
	if tag&saveTagModule7 != 0 {
		u.dbUser.Module7Op = db.Op_Dirty
	}
	if tag&saveTagModule8 != 0 {
		u.dbUser.Module8Op = db.Op_Dirty
	}
}

// 检查功能是否开放
func (u *User) IsFunctionOpen(id uint32, srv servicer) bool {
	// 功能开关
	if info, exist := gmxml.GmConfigInfoM.Datas[gmxml.GmConfigID(id)]; exist {
		if info.Status == gmxml.GmConfigClose {
			return false
		}
	} else {
		if !srv.GetFunctionStatus(id) {
			return false
		}
	}

	if id == uint32(common.FUNCID_MODULE_GST) && goxml.GetData().ConfigInfoM.GstShieldTime != 0 &&
		time.Now().Unix() <= goxml.GetData().ConfigInfoM.GstShieldTime {
		return false
	}

	info := goxml.GetData().FunctionInfoM.Index(id)
	if info == nil {
		l4g.Debugf("%s,%d function id error:%d", u.Name(), u.ID(), id)
		return false
	}

	if _, exist := ForbiddenFunction[id]; exist {
		l4g.Errorf("%s,%d function id forbidden:%d", u.Name(), u.ID(), id)
		return false
	}

	if goxml.GetData().SeasonInfoM.IsSeasonFunction(id) { // 判断是不是赛季类的玩法
		// 判断赛季限制条件
		ok := u.functionOpenCheckSeasonLimit(goxml.GetData(), id, srv, info)
		if !ok {
			// l4g.Errorf("user:%d IsFunctionOpen: functionOpenCheckSeasonLimit error. funcId:%d", u.ID(), id)
			return false
		}
	}

	if id == uint32(common.FUNCID_MODULE_TOWER_SEASON) {
		if u.isFunctionOpen(srv, goxml.GetData().FunctionInfoM.Index(uint32(common.FUNCID_MODULE_SEASON))) {
			// 策划要求，百塔特殊处理，赛季解锁，百塔按照解锁处理
			return true
		}
	}

	return u.isFunctionOpen(srv, info)
}

func (u *User) isFunctionOpen(srv servicer, info *goxml.FunctionInfo) bool {
	now := time.Now().Unix()
	if info == nil {
		l4g.Errorf("user:%d isFunctionOpen: functionInfo is nil.", u.ID())
		return false
	}
	id := info.Id
	if info.ServerDay > 0 { // 有开服天数限制
		if srv.ServerOpenDay(now) < info.ServerDay {
			// todo： 下面的if else逻辑有S1上线的特殊处理，后续可以只保留else的内容
			if id == uint32(common.FUNCID_MODULE_SEASON) || goxml.GetData().SeasonInfoM.IsSeasonFunction(id) {
				if !goxml.GetData().ConfigInfoM.IsServerSeasonSpecialOpen(srv.StartServiceTm()) {
					//l4g.Errorf("user:%d isFunctionOpen: seasonNotSpecialOpen. funcId:%d startServiceTm:%d",
					//	u.ID(), id, srv.StartServiceTm())
					return false
				}
			} else {
				// l4g.Errorf("user:%d isFunctionOpen: serverOpenDay error. serverOpenDay:%d ", u.ID(), srv.ServerOpenDay(now))
				return false
			}
		}
	}

	if info.PlayerDay > 0 { // 有创角天数限制
		if u.CreateDay(now) < info.PlayerDay {
			l4g.Errorf("user:%d isFunctionOpen: userCreateDay error. userCreateDay:%d infoDay:%d",
				u.ID(), u.CreateDay(now), info.PlayerDay)
			return false
		}
	}

	//vip判断前置，info.vip == 0 的话vip不作为限制条件
	if info.Vip > 0 && u.Vip() >= info.Vip {
		return true
	}

	if u.Level() >= info.Lv {
		if u.Dungeon() == nil {
			return false
		}
		if u.Dungeon().GetDungeonID() >= info.Dungeon {
			return true
		}
	}

	//l4g.Errorf("user:%d isFunctionOpen: level or dungeon error. userLv:%d userDungeon:%d funcID:%d",
	//	u.ID(), u.Level(), u.Dungeon().GetDungeonID(), id)
	return false
}

// functionOpenCheckSeasonLimit
// @Description:判断当前赛季，功能是否开启
// @receiver u
// @param now
// @return bool
func (u *User) functionOpenCheckSeasonLimit(gXmlData *goxml.XmlData, funcID uint32, srv servicer, info *goxml.FunctionInfo) bool {
	now := time.Now().Unix()
	seasonID := goxml.GetCurrentSeasonID(gXmlData, now)

	seasonOpen := goxml.IsSeasonOpen(gXmlData, srv.ServerOpenDay(now), seasonID, now) // 赛季是否开启

	if !seasonOpen { // 这个判断是S1上线的特殊处理，后续可删除
		if !goxml.IsServerUnlockSeason(gXmlData, srv.ServerOpenDay(now)) &&
			goxml.GetData().ConfigInfoM.IsServerSeasonSpecialOpen(srv.StartServiceTm()) {
			seasonOpen = true
		}
	}

	userUnlockSeason := u.isFunctionOpen(srv, goxml.GetData().FunctionInfoM.Index(uint32(common.FUNCID_MODULE_SEASON))) // 玩家是否解锁赛季
	if !seasonOpen || !userUnlockSeason {
		if funcID == uint32(common.FUNCID_MODULE_GUILD_DUNGEON) || funcID == uint32(common.FUNCID_MODULE_TOWER_SEASON) ||
			funcID == uint32(common.FUNCID_MODULE_GST) || funcID == uint32(common.FUNCID_MODULE_GST_BOSS) || funcID == uint32(common.FUNCID_MODULE_GST_DRAGON) ||
			funcID == uint32(common.FUNCID_MODULE_GST_CHALLENGE) {
			// 玩家未解锁赛季也可以玩
			return true
		}
		//l4g.Errorf("user:%d functionOpenCheckSeasonLimit: season not unlock. seasonOpen:%t userUnlockSeason:%t",
		//	u.ID(), seasonOpen, userUnlockSeason)
		return false
	}

	if !goxml.IsFuncInSeason(gXmlData, seasonID, funcID) {
		// 本赛季没有该功能，返回false
		l4g.Errorf("user:%d functionOpenCheckSeasonLimit: function not in this season. seasonId:%d functionID:%d",
			u.ID(), seasonID, funcID)
		return false
	}

	// 判断赛季等级
	if u.GetSeasonLevel() < info.SeasonLv {
		l4g.Errorf("user:%d functionOpenCheckSeasonLimit: seasonLv not ok. userSeasonLv:%d infoSeasonLv:%d",
			u.ID(), u.GetSeasonLevel(), info.SeasonLv)
		return false
	}

	if info.SeasonDungeon != 0 { // 有赛季主线，判断赛季主线
		if u.SeasonDungeon().GetDungeon() < info.SeasonDungeon {
			// l4g.Errorf("user:%d functionOpenCheckSeasonLimit: seasonDungeon not ok. userDungeon:%d infoDungeon:%d",
			// u.ID(), u.SeasonDungeon().GetDungeon(), info.SeasonDungeon)
			return false
		}
	}

	if info.SeasonDay != 0 {
		seasonInfo := goxml.GetData().SeasonInfoM.Index(seasonID)
		if seasonInfo == nil {
			l4g.Errorf("seasonInfo not exist. seasonID:%d", seasonID)
			return false
		}
		if util.DaysBetweenTimes(now, seasonInfo.StartTm)+1 < info.SeasonDay {
			return false
		}
	}

	return true
}

func (u *User) Save(srv servicer) {
	msg := &r2l.L2R_SaveUser{}

	msg.User = u.clone()
	msg.OpMails = &r2l.OpMails{}
	u.MailBox().Save(msg.OpMails)
	msg.RankData = u.newRankData()
	msg.OpHeroes = &r2l.OpHeroes{}
	u.HeroManager().Save(msg.OpHeroes)
	msg.OpEquips = &r2l.OpEquips{}
	u.EquipManager().Save(msg.OpEquips)
	msg.OpFormations = &r2l.OpFormations{}
	u.FormationManager().Save(msg.OpFormations)
	msg.OpArtifacts = &r2l.OpArtifacts{}
	u.ArtifactManager().save(msg.OpArtifacts)
	msg.OpGems = &r2l.OpGems{}
	u.GemManager().Save(msg.OpGems)
	msg.OpEmblems = &r2l.OpEmblems{}
	u.EmblemManager().Save(msg.OpEmblems)
	msg.OpMirages = &r2l.OpMirages{}
	u.MirageManager().Save(msg.OpMirages)
	msg.OpMazePlayer = &r2l.OpMazePlayers{}
	u.MazeM().Save(msg.OpMazePlayer)
	msg.OpActivities = &r2l.OpOperateActivity{}
	u.OperateActivityM().save(msg.OpActivities)
	msg.OpHeroesStarUpCosts = &r2l.OpHeroesStarUpCosts{}
	u.HeroStarUpCostsManager().Save(msg.OpHeroesStarUpCosts)
	msg.OpRites = &r2l.OpRites{}
	u.RiteManager().Save(msg.OpRites)
	msg.OpSkins = &r2l.OpSkins{}
	u.SkinManager().Save(msg.OpSkins)
	msg.OpSeasonLinkMonuments = &r2l.OpSeasonLinkMonuments{}
	u.SeasonLink().Save(msg.OpSeasonLinkMonuments)
	msg.OpRemains = &r2l.OpRemains{}
	u.RemainM().save(msg.OpRemains)
	msg.OpActivitySum = &r2l.OpActivitySum{}
	u.ActivitySumM().Save(msg.OpActivitySum)
	msg.OpDuel = &r2l.OpDuel{}
	u.Duel().save(msg.OpDuel)
	msg.OpSeasonJewelry = &r2l.OpSeasonJewelry{}
	u.SeasonJewelryManager().Save(msg.OpSeasonJewelry)
	msg.OpTitles = &r2l.OpTitles{}
	u.TitleManager().Save(msg.OpTitles)
	msg.OpPokemons = &r2l.OpPokemon{}
	u.PokemonManager().Save(msg.OpPokemons)
	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveUser), u.ID(), msg)
}

func (u *User) ResetDaily(refreshTime uint32, srv servicer) {
	if refreshTime == 0 {
		now := time.Now().Unix()
		refreshTime = helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now)
	}
	if u.dbUser.Module.Daily == nil || u.dbUser.Module.Daily.DailyZero != refreshTime {
		// 重新更新 specialNumberTypePool 池子中 numberType 的NumMax和LeftNum
		numInfos := u.resetNumInfo(u.getRefreshTm(refreshTime))
		u.dbUser.Module.Daily = &db.DailyInfo{
			DailyZero:      refreshTime,
			Level:          u.Level(),
			MazeMatchPower: u.MaxPower(),
			NumInfo:        numInfos,
		}
		u.Pyramid().CheckReset(srv) // 金字塔活动，检查重置
		u.FireCommonEvent(srv.EventM(), event.AeContinueLoginToX, 1)
		u.DailyTask().Reset()                             //里面重新走一次load的过程
		u.ShopM().ResetBuyLimit(goxml.ShopLimitTypeDaily) //重置商店中，日限购的商品购买信息
		u.SevenDayLogin().OnDailyReset()                  //7日活动加天数
		u.ActivityRecharge().ResetDaily()                 //限时礼包
		u.MonthlyCard().ResetDaily(srv)                   //月卡
		u.Pass().ResetDaily(srv)                          //战令
		u.OperateActivityM().ResetDaily(srv)              //配置活动
		u.DailyWishM().ResetDaily(srv)                    //每日许愿
		u.SaveDaily()
		u.ArtifactDebutM().AddLoginCount(srv)  //神器首发，累登计数
		u.ArtifactDebutM().ResetDailyTask(srv) //神器首发，重置每日任务
		u.RoundActivityM().CheckReset(srv)     //新服轮次活动，检查重置
		u.TowerSeason().ResetDaily(srv)
		u.DailyAttendance().AddLoginCount() // 累登计数
		u.addLoginDay()
		u.FireCommonEvent(srv.EventM(), event.AeDailyLogin, 1)
		u.WorldBoss().DailyReset(srv)
		u.GodPresentM().IsSendMail(srv) // 777抽卡 - 开启通知邮件
		u.ActivityStory().ResetDaily(srv, int64(refreshTime))
		u.SeasonLevel().resetDaily()
		u.ActivityReturn().ResetDaily(srv)
		u.NewYearActivity().ResetDaily(srv)
		u.DisorderLand().ResetDaily(srv)
		u.Recharge().OnDailyFirstGiftReset()
		u.DivineDemon().ResetDaily()
		u.ActivityTurnTable().ResetDaily(srv, int64(refreshTime))
		u.ActivitySumM().ResetDaily(srv)
		u.ActivityCompliance().ResetDaily(srv)
		u.SeasonComplianceM().ResetDaily(srv)
		u.ComplianceTasks().RestDaily(srv)
		u.ActivityCoupon().ResetDaily()
		u.DailyAttendanceHero().ResetDaily(srv)
		u.SeasonMap().ResetDaily(srv)
		u.PokemonSummon().RestDaily()
	}
}

func (u *User) DailyInfo() *db.DailyInfo {
	return u.dbUser.Module.Daily
}

func (u *User) SaveDaily() {
	u.setSaveTag(saveTagModule)
}

func (u *User) ResetWeekly(refreshTime uint32) {
	if refreshTime == 0 {
		now := time.Now().Unix()
		refreshTime = helper.GetResetTime(uint32(common.RESET_TYPE_WEEKLY), now)
	}
	if u.dbUser.Module.Weekly == nil || u.dbUser.Module.Weekly.WeeklyZero != refreshTime {
		u.dbUser.Module.Weekly = &db.WeeklyInfo{
			WeeklyZero: refreshTime,
			Level:      u.Level(),
		}
		u.WeeklyTask().Reset() //把新版的周常任务一起重置了

		u.ActivityRecharge().ResetWeekly() //限时礼包
		u.Pass().ResetWeekly()
		u.SaveWeekly()
	}
}

func (u *User) WeeklyInfo() *db.WeeklyInfo {
	return u.dbUser.Module.Weekly
}

func (u *User) SaveWeekly() {
	u.setSaveTag(saveTagModule)
}

func (u *User) ResetMonthly(refreshTime uint32) {
	if refreshTime == 0 {
		now := time.Now().Unix()
		refreshTime = helper.GetResetTime(uint32(common.RESET_TYPE_MONTHLY), now)
	}
	if u.dbUser.Module4.MonthlyInfo == nil || u.dbUser.Module4.MonthlyInfo.MonthlyZero != refreshTime {
		u.dbUser.Module4.MonthlyInfo = &db.MonthlyInfo{
			MonthlyZero: refreshTime,
		}
		u.MonthlyTask().Reset()                           //把新版的周常任务一起重置了
		u.ShopM().ResetBuyLimit(goxml.ShopLimitTypeMonth) // 重置商店月限购的商品购买信息
		u.SaveMonthly()
	}
}

func (u *User) MonthlyInfo() *db.MonthlyInfo {
	return u.dbUser.Module4.MonthlyInfo
}

func (u *User) SaveMonthly() {
	u.setSaveTag(saveTagModule4)
}

func (u *User) ResetWeeklyFriday(refreshTime uint32) {
	if refreshTime == 0 {
		now := time.Now().Unix()
		refreshTime = helper.GetResetTime(uint32(common.RESET_TYPE_WEEKLY_FRIDAY), now)
	}
	if u.dbUser.Module5.WeeklyFriday == nil || u.dbUser.Module5.WeeklyFriday.WeeklyFridayZero != refreshTime {
		u.dbUser.Module5.WeeklyFriday = &db.WeeklyFridayInfo{
			WeeklyFridayZero: refreshTime,
		}
		u.ShopM().ResetBuyLimit(goxml.ShopLimitTypeWeekly) // 重置商店日，周限购的商品购买信息
		u.SaveWeeklyFriday()
	}
}

func (u *User) WeeklyFridayInfo() *db.WeeklyFridayInfo {
	return u.dbUser.Module5.WeeklyFriday
}

func (u *User) SaveWeeklyFriday() {
	u.setSaveTag(saveTagModule5)
}

func (u *User) ResetShops(srv servicer) {
	u.ShopM().ResetShop(srv)
}

func (u *User) ShopsInfo() map[uint32]*cl.Shop {
	return u.dbUser.Module.Shops
}

func (u *User) Clone() *r2l.R2L_Login {
	return &r2l.R2L_Login{
		Ret:   uint32(cret.RET_OK),
		User:  u.dbUser.Clone(),
		Mails: u.MailBox().Clone(),
	}
}

// 注意这个必须是本服的db返回的offlineUser 跨服传递只用已经处理好了的UserSnapshot
func NewUserSnapshot(data *db.OfflineUser, formationID, areaId uint32) *cl.UserSnapshot {
	ret := &cl.UserSnapshot{
		Id:          data.Id,
		Name:        data.Name,
		Level:       data.Base.Level,
		Career:      data.Base.Career,
		BaseId:      data.Base.BaseId,
		Star:        data.Base.Star,
		Power:       data.Base.Power,
		ArenaPower:  data.Base.ArenaPower,
		OfflineTime: data.Base.OfflineTime,
		Online:      false,
		CreateTime:  data.Base.CreateTime,
		MaxPower:    data.Base.MaxPower,
		// ForestPower:  data.Base.ForestPower,
		Vip:          data.Base.Vip,
		Sid:          data.ServerId,
		ShowHero:     data.Base.ShowHero,
		ExpireTime:   data.Base.ExpiredTime,
		CrystalPower: data.Base.CrystalPower,
		AreaId:       areaId,
		Image:        data.Base.Image,
		SeasonLv:     data.Base.GetSeasonLv(),
		SeasonPower:  data.GetBase().GetSeasonPower(),
		SeasonId:     data.Base.GetSeasonId(),
		Title:        data.Base.Title,
	}
	if formationID != 0 {
		if data.Base.DefensePower != nil {
			ret.DefensePower = data.Base.DefensePower[formationID]
		}
	}
	l4g.Debugf("NewUserSnapshot u:%d, base: %+v", data.Id, data.Base)
	return ret
}

// @param formationID：用来获取defensePower，传0不获取
func (u *User) NewUserSnapshot(formationID uint32) *cl.UserSnapshot {
	data := u.dbUser
	ret := &cl.UserSnapshot{
		Id:          data.Id,
		Name:        data.Name,
		Level:       data.Base.Level,
		Career:      data.Base.Career,
		BaseId:      u.BaseID(),
		Star:        data.Base.Star,
		Power:       data.Base.Power,
		ArenaPower:  data.Base.ArenaPower,
		OfflineTime: data.Base.OfflineTime,
		Online:      u.IsOnline(),
		CreateTime:  u.CreateTime(),
		GuildId:     u.UserGuild().GuildID(),
		GuildName:   u.UserGuild().GuildName(),
		MaxPower:    data.Base.MaxPower,
		// ForestPower:  data.Base.ForestPower,
		Vip:                     data.Base.Vip,
		Top5Heros:               u.CloneTop5(),
		Sid:                     u.ServerID(),
		ShowHero:                data.Base.ShowHero,
		ExpireTime:              data.Base.ExpiredTime,
		CrystalPower:            data.Base.CrystalPower,
		AreaId:                  u.GetArea(),
		Image:                   data.Base.Image,
		SeasonLv:                data.Base.GetSeasonLv(),
		SeasonPower:             data.GetBase().GetSeasonPower(),
		SeasonId:                data.Base.GetSeasonId(),
		SeasonTopPower:          data.Base.GetSeasonTopPower(),
		SeasonCrystalTop35Power: data.Base.GetSeasonCrystalTop35Power(),
		Title:                   data.Base.Title,
	}
	if ret.ShowHero == 0 {
		if len(ret.Top5Heros) >= 1 && ret.Top5Heros[0] != 0 {
			hm := u.HeroManager()
			hero := hm.Get(ret.Top5Heros[0])
			if hero != nil && hero.GetData() != nil {
				ret.ShowHero = hero.GetData().SysId
			}
		}
	}
	if formationID != 0 {
		if data.Base.DefensePower != nil {
			ret.DefensePower = data.Base.DefensePower[formationID]
		}
	}
	l4g.Debugf("(u)NewUserSnapshot u:%d, base: %+v", data.Id, data.Base)
	return ret
}

// 注意这个必须是本服的db返回的offlineUser 跨服传递只用已经处理好了的UserSnapshot
func NewMiniUserSnapshot(data *db.OfflineUser) *cl.MiniUserSnapshot {
	ret := &cl.MiniUserSnapshot{
		Id:         data.Id,
		Name:       data.Name,
		Level:      data.Base.Level,
		BaseId:     data.Base.BaseId,
		Power:      data.Base.Power,
		ExpireTime: data.Base.ExpiredTime,
		Sid:        data.ServerId,
		Title:      data.Base.Title,
	}
	l4g.Debugf("NewMiniUserSnapshot u:%d, base: %+v", data.Id, data.Base)
	return ret
}

func (u *User) NewMiniUserSnapshot() *cl.MiniUserSnapshot {
	data := u.dbUser
	ret := &cl.MiniUserSnapshot{
		Id:         data.Id,
		Name:       data.Name,
		Level:      data.Base.Level,
		BaseId:     u.BaseID(),
		Power:      data.Base.Power,
		ExpireTime: data.Base.ExpiredTime,
		Sid:        data.ServerId,
		Vip:        data.Base.Vip,
		Title:      data.Base.Title,
	}
	l4g.Debugf("(u)NewMiniUserSnapshot u:%d, base: %+v", data.Id, data.Base)
	return ret
}

func (u *User) NewUserChatHead() *cl.UserChatHead {
	data := u.dbUser
	return &cl.UserChatHead{
		Id:         data.Id,
		Name:       data.Name,
		BaseId:     u.BaseID(),
		Level:      data.Base.Level,
		Power:      data.Base.Power,
		ExpireTime: data.Base.ExpiredTime,
		Vip:        data.Base.Vip,
		ServerId:   data.ServerId,
	}
}

func (u *User) NewChatUserHead(guild *db.LogicGuild) *cl.ChatUserHead {
	data := u.dbUser
	userHead := &cl.ChatUserHead{
		Id:          strconv.FormatUint(data.Id, 10),
		Name:        data.Name,
		BaseId:      u.BaseID(),
		Level:       data.Base.Level,
		Power:       strconv.FormatUint(uint64(data.Base.Power), 10),
		Vip:         data.Base.Vip,
		ServerId:    strconv.FormatUint(data.ServerId, 10),
		Uid:         data.Uuid,
		ChatBubbles: data.Base.ChatBubbles,
		Title:       data.Base.Title,
	}
	if guild != nil {
		userHead.GuildId = guild.Id
		userHead.GuildName = guild.Name
	}
	return userHead
}

func (u *User) newRankData() *db.GlobalRank {
	return &db.GlobalRank{
		Id:      u.ID(),
		Level:   u.Level(),
		LevelTm: u.LevelTm(),
		Power:   u.Power(),
		PowerTm: u.powerTm(),
	}
}

// 创角成功之后触发
func (u *User) CreateSuccess(srv servicer) {
	u.dbUser.Base.IsCreated = true
	u.setSaveTag(saveTagBase)

	//先内部事件触发 修改好玩家状态 然后再是任务事件
	u.FireCommonEvent(srv.EventM(), event.IeOnline, 0) //一定是先Online在create。因为有些数据是在online里面初始化的
	u.FireCommonEvent(srv.EventM(), event.IeCreate, 0)
	u.FireCommonEvent(srv.EventM(), event.IeLevelToX, 0)

	u.FireCommonEvent(srv.EventM(), event.AeLevelToX, uint64(u.Level()), 0)
	if u.Power() > 0 {
		//任务和成就类的战力，需要大于0才执行
		u.FireCommonEvent(srv.EventM(), event.AePowerToX, uint64(u.Power()))
	}

	u.LogRegister(srv)
}

// 上线成功之后触发
func (u *User) OnlineSuccess(srv servicer) {
	if !u.dbUser.Base.IsCreated {
		//创角过程中，玩家下线
		u.CreateSuccess(srv)
	} else {
		//注意两个事件的顺序
		u.FireCommonEvent(srv.EventM(), event.IeOnline, 0)
		//u.FireCommonEvent(srv.EventM(), aevent.OnlineCnt, 0)
	}
}

// 防沉迷限制时间戳
// @return int64 - 1.未成年玩家返回游戏限制时间戳 2.已成年玩家返回0
func (u *User) DailyPlayLimit() int64 {
	if u.IsUnderAge() {
		now := time.Now().Unix()
		playTime := u.DailyPlayTime()
		if now > u.OnlineTime() {
			playTime += now - u.OnlineTime()
		}
		if playTime < DailyPlayTimeMax {
			return now + DailyPlayTimeMax - playTime
		}
		return now
	}
	return 0
}

type AsyncLoadMessage struct {
	UID   uint64
	Param interface{}
	CB    func(interface{}, interface{}, uint64, uint32) bool
}

func GetOperateFrequencyTime(oi int) int64 {
	return OperateIntervals[oi]
}

func (u *User) CheckAndSetOperateInterval(srv servicer, oi int) bool {
	if oi < OIMax {
		now := time.AccurateNowMsec()
		interval := GetOperateFrequencyTime(oi)
		if interval != 0 {
			if now-u.operateTimes[oi] < interval {
				return false
			}
			u.operateTimes[oi] = now
		}
		return true
	}
	return false
}

func (u *User) CheckAndSetCmdInterval(cmdID uint32) bool {
	if interval, exist := goxml.GetData().ServerInfoM.CmdLimitMap[cmdID]; exist {
		now := time.AccurateNowMsec()
		if now-u.commandTimes[cmdID] < interval {
			return false
		}
		u.commandTimes[cmdID] = now
	}
	return true
}

func (u *User) CheckAndSetLikeInterval(msgID uint64) bool {
	now := time.AccurateNowMsec()
	interval := goxml.GetData().ChatConfigInfoM.ChatLikeCD
	if interval != 0 {
		if now-u.likeTimes[msgID] < interval {
			return false
		}
		u.likeTimes[msgID] = now
	}

	return true
}

func (u *User) resetCache() {
	u.operateTimes = make([]int64, OIMax)
	u.commandTimes = make(map[uint32]int64)
	u.likeTimes = make(map[uint64]int64)
	u.InitAttrCache()
}

// 当前时间(1:时间戳2:开服第X天3:创角第X天)
func (u *User) Now(srv servicer) []uint32 {
	ts := time.Now().Unix()
	now := make([]uint32, common.TIME_TYPE_MAX_SIZE)
	now[int(common.TIME_TYPE_USER_CREATE)] = u.CreateDay(ts)
	now[int(common.TIME_TYPE_SERVER_OPEN)] = srv.ServerDay(ts)
	now[int(common.TIME_TYPE_NORMAL_DATE)] = uint32(ts)
	return now
}

const (
	maxKeepAliveTimeOffset         = 10000  //10s
	maxKeepAliveTimeOffsetNegative = -10000 //10s
)

func (u *User) UpdateKeepAliveTimeOffset(offset int64) {
	lastOffset := u.keepAliveTimeOffset
	u.keepAliveTimeOffset = offset
	if !u.showKeepAliveTimeOffset &&
		(lastOffset < maxKeepAliveTimeOffsetNegative || lastOffset > maxKeepAliveTimeOffset) &&
		(offset < maxKeepAliveTimeOffsetNegative || offset > maxKeepAliveTimeOffset) {
		l4g.Errorf("KeepAlive user:%d sync time exception, offset: %d lastOffset: %d",
			u.ID(), offset, lastOffset)
		u.showKeepAliveTimeOffset = true
	}
}

func (u *User) resetOpArray()        { u.opArray = make([]uint32, OpMax) }
func (u *User) getOpArray() []uint32 { return u.opArray }
func (u *User) IncOpArray(op int) {
	if len(u.opArray) == 0 {
		u.resetOpArray()
	}
	u.opArray[op]++
}

// 0-1点记录自动记录在线玩家
func (u *User) LogLoginAuto(srv servicer) {
	now := time.Now()
	hour := now.Hour()
	if hour >= 0 && hour <= 1 {
		if !u.hasAutoLogin && !util.InSameDay(now.Unix(), u.OnlineTime()) {
			u.hasAutoLogin = true
			u.LogLogin(srv, 1)
		}
	}
}

// 获取累计充值
func (u *User) GetRecharge() uint32 { return u.dbUser.Base.RechargeAmount }

// 增加所有充值方式的总充值金额
func (u *User) addRecharge(amount uint32) {
	u.dbUser.Base.RechargeAmount += amount
	u.setSaveTag(saveTagBase)
}

// 更新共鸣等级变化之后的纹章状态
//
//nolint:funlen
func (u *User) UpdateEmblemByCrystalAttrDown(hids []uint64, old, new *cl.ShareHero) {
	if old == nil {
		return
	}
	var funcInfo *goxml.FunctionInfo
	var pos1, pos2, pos3, pos4 bool

	funcInfo = goxml.GetData().FunctionInfoM.Index(uint32(common.EMBLEM_CONFIG_SLOT_ONE_UNLOCK))
	if funcInfo == nil {
		l4g.Errorf("user:%d UpdateEmblemByCrystalAttrDown no found func info, id:%d",
			u.ID(), common.EMBLEM_CONFIG_SLOT_ONE_UNLOCK)
		return
	}
	if old.Level >= funcInfo.HeroLv && old.Stage >= funcInfo.HeroStage &&
		(new == nil || (new.Level < funcInfo.HeroLv || new.Stage < funcInfo.HeroStage)) {
		pos1 = true
	}

	funcInfo = goxml.GetData().FunctionInfoM.Index(uint32(common.EMBLEM_CONFIG_SLOT_TWO_UNLOCK))
	if funcInfo == nil {
		l4g.Errorf("user:%d UpdateEmblemByCrystalAttrDown no found func info, id:%d",
			u.ID(), uint32(common.EMBLEM_CONFIG_SLOT_TWO_UNLOCK))
		return
	}
	if old.Level >= funcInfo.HeroLv && old.Stage >= funcInfo.HeroStage &&
		(new == nil || (new.Level < funcInfo.HeroLv || new.Stage < funcInfo.HeroStage)) {
		pos2 = true
	}

	funcInfo = goxml.GetData().FunctionInfoM.Index(uint32(common.EMBLEM_CONFIG_SLOT_THREE_UNLOCK))
	if funcInfo == nil {
		l4g.Errorf("user:%d UpdateEmblemByCrystalAttrDown no found func info, id:%d",
			u.ID(), uint32(common.EMBLEM_CONFIG_SLOT_THREE_UNLOCK))
		return
	}
	if old.Level >= funcInfo.HeroLv && old.Stage >= funcInfo.HeroStage &&
		(new == nil || (new.Level < funcInfo.HeroLv || new.Stage < funcInfo.HeroStage)) {
		pos3 = true
	}

	funcInfo = goxml.GetData().FunctionInfoM.Index(uint32(common.EMBLEM_CONFIG_SLOT_FOUR_UNLOCK))
	if funcInfo == nil {
		l4g.Errorf("user:%d UpdateEmblemByCrystalAttrDown no found func info, id:%d",
			u.ID(), uint32(common.EMBLEM_CONFIG_SLOT_FOUR_UNLOCK))
		return
	}
	if old.Level >= funcInfo.HeroLv && old.Stage >= funcInfo.HeroStage &&
		(new == nil || (new.Level < funcInfo.HeroLv || new.Stage < funcInfo.HeroStage)) {
		pos4 = true
	}

	if !pos1 && !pos2 && !pos3 && !pos4 {
		return
	}

	var changeHeroes []*cl.Hero
	var changeEmblems []*cl.EmblemInfo
	var emblemsLen int
	em := u.EmblemManager()
	hm := u.HeroManager()
	for _, hid := range hids {
		hero := hm.Get(hid)
		if hero == nil {
			l4g.Errorf("user:%d UpdateEmblemByCrystalAttrDown no found hero, id:%d", u.ID(), hid)
			continue
		}
		if pos1 {
			emblem := em.DisboardEmblem(hero, goxml.EmblemPos1)
			if emblem != nil {
				changeEmblems = append(changeEmblems, emblem)
			}
		}
		if pos2 {
			emblem := em.DisboardEmblem(hero, goxml.EmblemPos2)
			if emblem != nil {
				changeEmblems = append(changeEmblems, emblem)
			}
		}
		if pos3 {
			emblem := em.DisboardEmblem(hero, goxml.EmblemPos3)
			if emblem != nil {
				changeEmblems = append(changeEmblems, emblem)
			}
		}
		if pos4 {
			emblem := em.DisboardEmblem(hero, goxml.EmblemPos4)
			if emblem != nil {
				changeEmblems = append(changeEmblems, emblem)
			}
		}
		if len(changeEmblems) > emblemsLen {
			emblemsLen = len(changeEmblems)
			changeHeroes = append(changeHeroes, hero.Flush())
		}
	}
	if len(changeEmblems) > 0 {
		u.SendEmblemsToClient(changeEmblems)
	}
	if len(changeHeroes) > 0 {
		heroMsg := &cl.L2C_HeroesUpdate{
			Ret:    uint32(cret.RET_OK),
			Heroes: changeHeroes,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_HeroesUpdate, heroMsg)
	}
}

func (u *User) UpdateGemByCrystalAttrDown(hids []uint64, old, new *cl.ShareHero) {
	if old == nil {
		return
	}
	var funcInfo *goxml.FunctionInfo
	var leftDisboard, rightDisboard bool
	funcInfo = goxml.GetData().FunctionInfoM.Index(goxml.GemLeftSlotUnlockID)
	if funcInfo == nil {
		l4g.Errorf("user:%d UpdateGemByCrystalAttrDown no found func info, id:%d",
			u.ID(), goxml.GemLeftSlotUnlockID)
		return
	}
	if old.Level >= funcInfo.HeroLv && old.Stage >= funcInfo.HeroStage &&
		(new == nil || (new.Level < funcInfo.HeroLv || new.Stage < funcInfo.HeroStage)) {
		leftDisboard = true
	}

	funcInfo = goxml.GetData().FunctionInfoM.Index(goxml.GemRightSlotUnlockID)
	if funcInfo == nil {
		l4g.Errorf("user:%d UpdateGemByCrystalAttrDown no found func info, id:%d",
			u.ID(), goxml.GemRightSlotUnlockID)
		return
	}
	if old.Level >= funcInfo.HeroLv && old.Stage >= funcInfo.HeroStage &&
		(new == nil || (new.Level < funcInfo.HeroLv || new.Stage < funcInfo.HeroStage)) {
		rightDisboard = true
	}
	if !leftDisboard && !rightDisboard {
		return
	}

	hm := u.HeroManager()
	gemM := u.GemManager()
	changedGems := make([]*cl.GemInfo, 0)
	var changeHeroes []*cl.Hero
	var gemsLen int
	for _, hid := range hids {
		hero := hm.Get(hid)
		if hero == nil {
			l4g.Errorf("user:%d UpdateGemByCrystalAttrDown no found hero, id:%d", u.ID(), hid)
			continue
		}
		if leftDisboard {
			gemInfo := gemM.DisboardGem(hero, GemLeftSlot)
			if gemInfo != nil {
				changedGems = append(changedGems, gemInfo)
			}
		}
		if rightDisboard {
			gemInfo := gemM.DisboardGem(hero, GemRightSlot)
			if gemInfo != nil {
				changedGems = append(changedGems, gemInfo)
			}
		}
		if len(changedGems) > gemsLen {
			gemsLen = len(changedGems)
			changeHeroes = append(changeHeroes, hero.Flush())
		}
	}

	if len(changedGems) > 0 {
		u.SendGemsToClient(changedGems)
	}
	if len(changeHeroes) > 0 {
		heroMsg := &cl.L2C_HeroesUpdate{
			Ret:    uint32(cret.RET_OK),
			Heroes: changeHeroes,
		}
		u.SendCmdToGateway(cl.ID_MSG_L2C_HeroesUpdate, heroMsg)
	}
}

// 处理订单发货统一入口
// 根据类型进行相应订单处理
// @order *db.Order 订单信息
// @return uint32 - 1:订单处理成功 2:订单处理失败
func (u *User) OrderShipped(srv servicer, order *db.Order) uint32 {
	info := goxml.GetData().RechargeProductInfoM.GetProductInfoByCustomData(srv.ServerType(), order.Custom)
	if info == nil {
		l4g.Errorf("user: %d User.OrderShipped: get product info error. order: %+v", u.ID(), order)
		return ProcessOrderRetFailure
	}
	if !u.checkOrder(srv, info, order) {
		l4g.Errorf("user: %d User.OrderShipped: check order error. order: %+v", u.ID(), order)
		return ProcessOrderRetFailure
	}

	if !u.handleOrderByRechargeType(srv, order) {
		l4g.Errorf("user: %d User.OrderShipped: failure. order:%+v", u.ID(), order)
		return ProcessOrderRetFailure
	}

	return ProcessOrderRetSuccess
}

func (u *User) checkOrder(srv servicer, productInfo *goxml.RechargeProductInfo, order *db.Order) bool {

	// 福利订单，玩家必须是福利账号
	if order.Type == uint32(common.ORDER_TYPE_OT_WELFARE) && u.GetAccountTag() != AccountTagWelfare {
		l4g.Errorf("user: %d User.checkOrder: welfareOrder user is not welfareAccount. orderId:%s orderType:%d userAccountTag:%d",
			u.ID(), order.OrderId, order.Type, u.GetAccountTag())
		return false
	}

	if productInfo.InternalId != order.Custom.InternalId || productInfo.ProductId != order.ProductId {
		l4g.Errorf("user: %d User.checkOrder: productId not same. orderId:%s internalId:%d orderInternalId:%d productId:%s orderProductId:%s",
			u.ID(), order.OrderId, order.Custom.InternalId, productInfo.InternalId, productInfo.ProductId, order.ProductId)
		return false
	}

	if srv.ServerType() == goxml.SERVER_TYPE_CN {
		if u.UUID() != order.Uuid {
			l4g.Errorf("[FATAL] user: %d User.checkOrder: uuid is not same. orderId:%s uuid:%s order uuid:%s",
				u.ID(), order.OrderId, u.UUID(), order.Uuid)
			return false
		}
	}

	// 金额检查
	if productInfo.InternalId == WebRechargeCouponInternalId && order.SdkPid == WebRechargeCouponSdkPid { //代金券自定义充值存在金额上限
		if order.Amount < WebRechargeCouponCustomRechargeNumMin || order.Amount > WebRechargeCouponCustomRechargeNumMax {
			l4g.Errorf("user: %d User.checkOrder: invalid order amount. orderId:%s order.Amount:%d ",
				u.ID(), order.OrderId, order.Amount)
			return false
		}
	} else { //其他充值需要检查订单金额是否一致
		if productInfo.Amount != order.Amount {
			l4g.Errorf("user: %d User.checkOrder: amount not same. orderId:%s productInfo.Amount:%d order.Amount:%d ",
				u.ID(), order.OrderId, productInfo.Amount, order.Amount)
			return false
		}
	}

	return true
}

// 根据充值类型处理订单
func (u *User) handleOrderByRechargeType(srv servicer, order *db.Order) (ok bool) {
	switch common.RECHARGE_TYPE(order.Custom.Type) {
	case common.RECHARGE_TYPE_RT_NORMAL:
		ok = u.Recharge().normalRechargeProcess(srv, order)
	case common.RECHARGE_TYPE_RT_MONTHLY_CARD:
		ok = u.MonthlyCard().process(srv, order)
	case common.RECHARGE_TYPE_RT_FIRST_GIFT:
		ok = u.Recharge().firstGiftProcess(srv, order)
	case common.RECHARGE_TYPE_RT_ACTIVITY_RECHARGE:
		ok = u.ActivityRecharge().Process(srv, order)
	case common.RECHARGE_TYPE_RT_PASS:
		ok = u.Pass().process(srv, order)
	case common.RECHARGE_TYPE_RT_PUSH_GIFT:
		ok = u.PushGift().process(srv, order)
	case common.RECHARGE_TYPE_RT_GIFT:
		ok = u.OperateActivityM().processGift(srv, order)
	case common.RECHARGE_TYPE_RT_VIP_GIFT:
		ok = u.VipManager().processGift(srv, order)
	case common.RECHARGE_TYPE_RT_WEB_DIAMOND:
		ok = u.Recharge().webDiamondProcess(srv, order)
	case common.RECHARGE_TYPE_RT_WEB_GIFT:
		ok = u.Recharge().webGiftProcess(srv, order)
	case common.RECHARGE_TYPE_RT_COUPON:
		ok = u.Recharge().couponProcess(srv, order)
	case common.RECHARGE_TYPE_RT_WEB_COUPON:
		ok = u.Recharge().webCouponProcess(srv, order)
	case common.RECHARGE_TYPE_RT_PROMOTION:
		ok = u.OperateActivityM().promotionGiftProcess(srv, order)
	case common.RECHARGE_TYPE_RT_LIFELONG_GIFT:
		ok = u.ActivityLifelongGifts().process(srv, order)
	case common.RECHARGE_TYPE_RT_ACTIVITY_COUPON:
		ok = u.ActivityCoupon().process(srv, order)
	default:
		l4g.Errorf("user: %d User.OrderShipped: recharge type error. order: %+v", u.ID(), order)
	}

	return ok
}

// OrderRefunded 处理订单退款统一入口
// @order *db.Order 订单信息
// @return bool - 订单退款成功
func (u *User) OrderRefund(srv servicer, order *db.Order) bool {
	if order.Amount == 0 {
		l4g.Errorf("user: %d OrderRefund: invalid order amount %d", u.ID(), order.Amount)
		return false
	}
	//根据订单金额扣除代金券
	costCoupon := goxml.GenSimpleResource(uint32(common.RESOURCE_COUPON), 0, order.Amount)
	if costCoupon == nil {
		l4g.Errorf("user: %d OrderRefund: costCoupon nil", u.ID())
		return false
	}

	//退款不检查资源直接扣除
	cRet := u.RefundCoupon(srv, costCoupon, uint32(log.RESOURCE_CHANGE_REASON_RECHARGE_REFUND), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d OrderRefund: RefundCoupon err", u.ID())
		return false
	}

	return true
}

func (u *User) GetFinishQuestionnaireIds() []uint32 {
	return u.dbUser.Module2.QuestionnaireFinishedIds
}

func (u *User) SetFinishQuestionnaireIds(newFinishIDs []uint32) {
	u.dbUser.Module2.QuestionnaireFinishedIds = newFinishIDs
	u.setSaveTag(saveTagModule2)
}

func (u *User) GetSeasonFlashBackIds() []uint32 {
	return u.dbUser.Module5.SeasonFlashBackIds
}

func (u *User) AddSeasonFlashBackId(newId uint32) {
	for _, id := range u.dbUser.Module5.SeasonFlashBackIds {
		if id == newId {
			return
		}
	}
	u.dbUser.Module5.SeasonFlashBackIds = append(u.dbUser.Module5.SeasonFlashBackIds, newId)
	u.setSaveTag(saveTagModule5)
}

func (u *User) GetTop5() []uint64 {
	return u.dbUser.Base.Top5Heros
}

func (u *User) SetTop5(top5Heros []uint64) {
	u.dbUser.Base.Top5Heros = top5Heros
	u.setSaveTag(saveTagBase)
}

func (u *User) CloneTop5() []uint64 {
	clone := make([]uint64, len(u.dbUser.Base.Top5Heros))
	copy(clone, u.dbUser.Base.Top5Heros)
	return clone
}

func (u *User) UpdateFightEvent(srv servicer, battleM *battle.Manager, battleReport *bt.MultipleTeamsReport) {
	u.updateFightLinkEvent(srv, battleM, battleReport)
}

func (u *User) updateFightLinkEvent(srv servicer, battleM *battle.Manager, battleReport *bt.MultipleTeamsReport) {
	//联结事件
	team := battleM.GetTeamByIndex(battle.AttackTeam)
	activedLinks := team.GetActivedLinks()
	if len(activedLinks) == 0 {
		return
	}
	linkNumMap := make(map[uint32]uint64) // linkNum => times
	var maxActiveNum uint32
	for _, activedLink := range activedLinks {
		if maxActiveNum < activedLink.Num {
			maxActiveNum = activedLink.Num
		}
		linkNum := len(goxml.GetData().HeroInfoM.GetLinkHeros(activedLink.LinkId))
		linkNumMap[uint32(linkNum)]++
		u.FireCommonEvent(srv.EventM(), uint32(event.AeActivedLink), 1, activedLink.LinkId)

		//同时上阵2个指定羁绊英雄，参加任意战斗x次
		if activedLink.Num >= 2 {
			u.FireCommonEvent(srv.EventM(), uint32(event.Ae2LinkHeroFightToX), 1, activedLink.LinkId)
		}
	}
	u.FireCommonEvent(srv.EventM(), uint32(event.AeActivedLinkToX), uint64(len(activedLinks)))
	for linkNum, times := range linkNumMap {
		u.FireCommonEvent(srv.EventM(), uint32(event.AeActivedLinkNumXToX), uint64(times), linkNum)
	}

	//通关处理的事件
	if battleReport.Win {
		if battleReport.GetFormationId() == uint32(common.FORMATION_ID_FI_DUNGEON) {
			for _, activedLink := range activedLinks {
				u.FireCommonEvent(srv.EventM(), uint32(event.AeLinkPassDungeonToX), 1, activedLink.LinkId)

			}
			if maxActiveNum != 0 {
				u.FireCommonEvent(srv.EventM(), uint32(event.AeLinkNumPassDungeonOrTowerToX), 1, maxActiveNum)
			}
		}
		if battleReport.GetFormationId() == uint32(common.FORMATION_ID_FI_TOWER) {
			for _, activedLink := range activedLinks {
				u.FireCommonEvent(srv.EventM(), uint32(event.AeLinkPassTowerToX), 1, activedLink.LinkId)
			}
			if maxActiveNum != 0 {
				u.FireCommonEvent(srv.EventM(), uint32(event.AeLinkNumPassDungeonOrTowerToX), 1, maxActiveNum)
			}
		}
	}
}

func (u *User) setShowHero(sysID uint32) {
	u.dbUser.Base.ShowHero = sysID
	u.setSaveTag(saveTagBase)
}

func (u *User) InitAttrCache() {
	u.attrCache = make([]int64, goxml.AttrMaxNum)
}

func (u *User) GetAttrCache() []int64 {
	return u.attrCache
}

func (u *User) ClearAttrCache() {
	for index := range u.attrCache {
		u.attrCache[index] = 0
	}
}

func (u *User) CalcCultivateScore() int64 {
	var score int64
	for attrId, attrCount := range u.attrCache {
		_, attrScore := goxml.GetData().AttributeInfoM.CalcScore(uint32(attrId), attrCount)
		score += attrScore
	}
	return score
}

func (u *User) CalcMultipleScore() int64 {
	_, multipleScore := goxml.GetData().ConfigInfoM.CalcRatingMultipleScore(u.CalcCultivateScore())
	return multipleScore
}

// 设置战斗英雄列表总战力
func (u *User) SetCrystalPower(power int64) {
	u.dbUser.Base.CrystalPower = power
	u.setSaveTag(saveTagBase)
}

// 注：此方法获取战斗英雄列表战力只能通过dbUser！！！
// 注：此方法获取战斗英雄列表战力只能通过dbUser！！！
// 注：此方法获取战斗英雄列表战力只能通过dbUser！！！
func (u *User) GetCrystalPower() int64 {
	return u.dbUser.Base.CrystalPower
}

// 根据阵容id，获取队伍数量
// @param uint32 formationID 阵容id
// @return bool 是否正常返回
// @return int 队伍数量
func (u *User) GetTeamNum(formationID uint32) (bool, int) {
	flag, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(formationID)
	if !flag {
		l4g.Errorf("user: %d GetTeamNum err. formation info not exist:%d ", u.ID(), formationID)
		return false, 0
	}

	if teamNum > 0 {
		return true, teamNum
	}

	//teamNum为0时，表示阵容的队伍数量是可变的
	//目前仅有主线
	//如有变化，请在此处扩展
	switch formationID {
	case uint32(common.FORMATION_ID_FI_DUNGEON):
		return true, u.Dungeon().TeamNum()
	case uint32(common.FORMATION_ID_FI_TOWER):
		return true, u.Towers().TeamNum()
	case uint32(common.FORMATION_ID_FI_SEASON_DUNGEON):
		return true, u.SeasonDungeon().TeamNum()
	case uint32(common.FORMATION_ID_FI_GST), uint32(common.FORMATION_ID_FI_GST_CHALLENGE):
		return true, u.GSTTeamNum()
	case uint32(common.FORMATION_ID_FI_GST_DRAGON_1), uint32(common.FORMATION_ID_FI_GST_DRAGON_2), uint32(common.FORMATION_ID_FI_GST_DRAGON_3), uint32(common.FORMATION_ID_FI_GST_DRAGON_4), uint32(common.FORMATION_ID_FI_GST_DRAGON_5):
		return true, u.GSTDragonTeamNum()
	case uint32(common.FORMATION_ID_FI_TOWER_SEASON):
		return true, u.TowerSeason().TeamNum()
	case uint32(common.FORMATION_ID_FI_BOSS_RUSH_1), uint32(common.FORMATION_ID_FI_BOSS_RUSH_2), uint32(common.FORMATION_ID_FI_BOSS_RUSH_3), uint32(common.FORMATION_ID_FI_BOSS_RUSH_4), uint32(common.FORMATION_ID_FI_BOSS_RUSH_5), uint32(common.FORMATION_ID_FI_BOSS_RUSH_6), uint32(common.FORMATION_ID_FI_BOSS_RUSH_7), uint32(common.FORMATION_ID_FI_BOSS_RUSH_8):
		return true, u.BossRush().TeamNum(formationID)
	default:
		l4g.Errorf("user: %d GetTeamNum err. formationID:%d ", u.ID(), formationID)
		return false, 0
	}
}

func (u *User) GSTTeamNum() int {
	defaultNum := 2
	commonScore := uint32(0)

	// 增加天赋树加成
	defaultNum += u.TalentTree().GetTeamAddition(goxml.TalentTreeNodeLevelAdditionGstTeamNum)

	linkIDs := goxml.GetData().LinkBookInfoM.GetLinkIDs(goxml.LinkTypeCommon)
	for _, id := range linkIDs {
		commonScore += u.HandbookManager().GetHeroHandbookM().GetLinkScore(id)
	}
	if commonScore < goxml.GetData().GuildSandTableConfigInfoM.TeamUnlockRequirePoints3 {
		return defaultNum
	}
	defaultNum++
	if commonScore < goxml.GetData().GuildSandTableConfigInfoM.TeamUnlockRequirePoints4 {
		return defaultNum
	}
	defaultNum++
	if commonScore < goxml.GetData().GuildSandTableConfigInfoM.TeamUnlockRequirePoints5 {
		return defaultNum
	}
	defaultNum++
	if commonScore < goxml.GetData().GuildSandTableConfigInfoM.TeamUnlockRequirePoints6 {
		return defaultNum
	}
	defaultNum++
	return defaultNum
}

func (u *User) GSTDragonTeamNum() int {
	defaultNum := goxml.GetData().GuildSandTableConfigInfoM.DragonDefaultTeamNum

	// 天赋树加成
	defaultNum += u.TalentTree().GetTeamAddition(goxml.TalentTreeNodeLevelAdditionGstDragonTeamNum)

	// 羁绊加成
	linkIDs, commonScore := goxml.GetData().LinkBookInfoM.GetLinkIDs(goxml.LinkTypeCommon), uint32(0)
	for _, id := range linkIDs {
		commonScore += u.HandbookManager().GetHeroHandbookM().GetLinkScore(id)
	}
	if commonScore < goxml.GetData().GuildSandTableConfigInfoM.DragonTeamUnlockRequirePoints3 {
		return defaultNum
	}
	defaultNum++
	if commonScore < goxml.GetData().GuildSandTableConfigInfoM.DragonTeamUnlockRequirePoints4 {
		return defaultNum
	}
	defaultNum++

	return defaultNum
}

func (u *User) HasReceivedH5DesktopReward() bool { return u.dbUser.Module4.ReceivedH5Reward }
func (u *User) RecvH5DesktopReward() {
	u.dbUser.Module4.ReceivedH5Reward = true
	u.setSaveTag(saveTagModule4)
}

func (u *User) LoginDay() uint32 { return u.dbUser.Base.LoginDay }

func (u *User) addLoginDay() {
	u.dbUser.Base.LoginDay += 1
	u.setSaveTag(saveTagBase)
}

// IsFirstGetHero
// @Description: 是否第一次获得该英雄，通过图鉴判断
// @receiver u
// @return bool
func (u *User) IsFirstGetHero(sysID uint32) bool {
	return u.HandbookManager().GetHeroHandbookM().GetOne(sysID) == nil
}

// 符文背包栏位数量
func (u *User) GetEmblemSlotCount() uint32 {
	return u.dbUser.Module1.EmblemSlot + goxml.GetData().ConfigInfoM.GetEmblemSlotBaseCount()
}

// 设置符文背包格位购买数量
func (u *User) SetEmblemSlot(num uint32) {
	if num > goxml.GetData().ConfigInfoM.GetEmblemSlotBaseCount() {
		u.dbUser.Module1.EmblemSlot = num - goxml.GetData().ConfigInfoM.GetEmblemSlotBaseCount()
		u.setSaveTag(saveTagModule1)
	}
}

// 符文背包格位已购买数量
func (u *User) GetEmblemBuySlotCount() uint32 { return u.dbUser.Module1.EmblemSlot }

func (u *User) DungeonID() uint32 {
	var dungeonID uint32
	if u.Dungeon() != nil {
		dungeonID = u.Dungeon().GetDungeonID()
	}
	return dungeonID
}

/*
func (u *User) clearToken(tokenID uint32) {
	token := u.tokensBag()
	delete(token, tokenID)
	u.setSaveTag(saveTagBag)
}
*/

func (u *User) GetDailyNumInfo(numType uint32) *cl.NumInfo {
	return u.dbUser.Module.Daily.NumInfo[numType]
}

func (u *User) CreateGstUserBaseInfo() *l2c.L2CS_GSTSYNCGuildUserInfo {
	var remainBooKLevel uint32
	if u.RemainM() != nil && u.RemainM().remainBook != nil {
		remainBooKLevel = u.RemainM().remainBook.Level()
	}
	crossMsg := &l2c.L2CS_GSTSYNCGuildUserInfo{
		GstUser: &cl.GSTGuildUserBase{
			Id:              u.ID(),
			Name:            u.Name(),
			SeasonLv:        u.GetSeasonLevel(),
			BaseId:          u.BaseID(),
			ExpireTime:      u.ExpireTime(),
			SeasonLinkPower: u.SeasonLink().GetBattleSeasonLinkPower(),
			Level:           u.Level(),
			RemainBookLevel: remainBooKLevel,
			TalentTreeLv:    u.TalentTree().GetRootLevel(),
			Image:           u.GetImage(),
			ShowHero:        u.GetShowHero(),
			Title:           u.GetTitle(),
			SeasonAdd:       u.GetSeasonAddData(),
		},
		SeasonlinkActived: make(map[uint32]bool),
		AddFightTimes:     u.GetGstAddFightTimes(),
	}
	for heroId := range u.SeasonLink().GetActivedHeros() {
		crossMsg.SeasonlinkActived[heroId] = true
	}
	return crossMsg
}

func (u *User) DelMailsForQuitGuild() {
	delQuitGuild := func(mail *cl.Mail) uint32 {
		if mail.BaseId != goxml.GetData().GuildSandTableConfigInfoM.QuitDelMailId {
			return uint32(common.DEL_MAIL_TYPE_MAIL_NO_CHANGE)
		}
		length := len(mail.Awards)
		var isChange bool
		for i := 0; i < length; i++ {
			switch mail.Awards[i].Type {
			case uint32(common.RESOURCE_ITEM):
				_, exist := goxml.GetData().GuildSandTableConfigInfoM.QuitDelItemId[mail.Awards[i].Value]
				if exist {
					mail.Awards = append(mail.Awards[:i], mail.Awards[i+1:]...)
					length--
					i--
					isChange = true
				}
			case uint32(common.RESOURCE_TOKEN):
				_, exist := goxml.GetData().GuildSandTableBuildInfoM.GetAllToken()[mail.Awards[i].Value]
				if exist {
					mail.Awards = append(mail.Awards[:i], mail.Awards[i+1:]...)
					length--
					i--
					isChange = true
				}
			default:
				continue
			}
		}
		if isChange {
			if len(mail.Awards) == 0 {
				return uint32(common.DEL_MAIL_TYPE_MAIL_DELETE)
			} else {
				return uint32(common.DEL_MAIL_TYPE_MAIL_CHANGE)
			}
		}
		return uint32(common.DEL_MAIL_TYPE_MAIL_NO_CHANGE)
	}
	u.MailBox().DelMailAward(delQuitGuild)
}

func (u *User) AssistantUnlockMark() bool { return u.dbUser.Module6.AssistantUnlockMark > 0 }

func (u *User) GetGstAddFightTimes() uint32 {
	return u.calcSeasonAddGstFightTimes()
}

func (u *User) OnGstAddFightTimesChange(srv Servicer) {
	srv.SyncGstUserInfo(u)
}

func (u *User) GetCheckSensitiveWordReq(content string, sid uint64, checkType string, channel string) *p2l.SensitiveWordCheckReq {
	data := &p2l.SensitiveWordCheckReq{
		Time:              time.Now().Unix(),
		Uid:               u.UUID(),
		Gid:               "1020857",
		Pid:               strconv.FormatUint(uint64(u.pid), 10),
		Dsid:              strconv.FormatUint(sid, 10),
		LexiconID:         "omniheros",
		ActorName:         u.Name(),
		ActorId:           strconv.FormatUint(u.ID(), 10),
		ActorLevel:        strconv.FormatUint(uint64(u.Level()), 10),
		ActorRechargeGold: strconv.FormatUint(uint64(u.GetRecharge()), 10),
		Content:           content,
		Type:              checkType,
		Channel:           channel,
	}
	return data
}

func (u *User) BuildHotRankUserFormation(formationID, progress uint32) *db.LogicHotRankUserFormation {
	formation := u.FormationManager().Get(formationID)
	if formation == nil || len(formation.Teams) == 0 {
		l4g.Errorf("user:%d BuildHotRankUserFormation formation ID:%d failed ", u.ID(), formationID)
		return nil
	}
	ret := &db.LogicHotRankUserFormation{}
	ret.FormationId = formationID
	ret.Uid = u.ID()
	ret.UpdateTime = time.Now().Unix()
	ret.Progress = progress
	for teamIndex, team := range formation.Teams {
		if team == nil {
			continue
		}
		sortHero := make([]uint32, 0, FormationMaxPos)
		sortArtifact := make([]uint32, 0, FormationArtifMaxPos)
		sortHeroString := ""
		sortArtifactString := ""
		hotFormationBase := &db.HotFormationBase{
			RedEmblemSkills:    make([]uint32, 0, FormationMaxPos),
			OrangeEmblemSkills: make([]uint32, 0, FormationMaxPos),
		}
		for _, hero := range team.Info {
			heroData := u.HeroManager().Get(hero.Hid)
			if heroData == nil || heroData.data == nil {
				l4g.Errorf("user:%d BuildHotRankUserFormation formation ID:%d teamIndex:%d heroID:%d is nil", u.ID(), formationID, teamIndex, hero.Hid)
				continue
			}

			heroInfo := goxml.GetData().HeroInfoM.Index(heroData.GetHeroSysID())
			if heroInfo == nil {
				l4g.Errorf("user:%d BuildHotRankUserFormation formation ID:%d teamIndex:%d heroID:%d heroSysID:%d xml is nil", u.ID(), formationID, teamIndex, hero.Hid, heroData.GetHeroSysID())
				continue
			}

			logTag := heroData.GetLogTag()
			if util.BitAndUint32(logTag, goxml.EmblemExclusiveSkill1) {
				hotFormationBase.OrangeEmblemSkills = append(hotFormationBase.OrangeEmblemSkills, heroInfo.EmblemExclusive1)
			}
			//策划需求2直接算3
			if util.BitAndUint32(logTag, goxml.EmblemExclusiveSkill2) {
				hotFormationBase.RedEmblemSkills = append(hotFormationBase.RedEmblemSkills, heroInfo.EmblemExclusive3)
			}
			if util.BitAndUint32(logTag, goxml.EmblemExclusiveSkill3) {
				hotFormationBase.RedEmblemSkills = append(hotFormationBase.RedEmblemSkills, heroInfo.EmblemExclusive3)
			}
			sortHero = append(sortHero, heroData.GetHeroSysID())
			slices.Sort(sortHero)
			stringSlice := goxml.IntSlice2StringSlice(sortHero)
			sortHeroString = strings.Join(stringSlice, ",")
		}

		for artifactIndex, artifact := range team.Artifacts {
			if artifact == nil {
				l4g.Errorf("user:%d BuildHotRankUserFormation formation ID:%d teamIndex:%d artifactIndex:%d is nil ", u.ID(), formationID, teamIndex, artifactIndex)
				continue
			}
			sortArtifact = append(sortArtifact, artifact.Aid)
			slices.Sort(sortArtifact)
			stringSlice := goxml.IntSlice2StringSlice(sortArtifact)
			sortArtifactString = strings.Join(stringSlice, ",")
		}

		hotFormationBase.HotFormation = sortHeroString + ";" + sortArtifactString
		ret.HotFormationBase = append(ret.HotFormationBase, hotFormationBase)
	}
	return ret
}

// 做一个缓存
func (u *User) GetCloneHeroAttr() []int64 {
	if len(u.cloneHeroAttr) == 0 {
		u.cloneHeroAttr = make([]int64, goxml.AttrMaxNum)
	}
	return u.cloneHeroAttr
}

// 检查更新赛季战力以及排名
// 2.16.5-cn水晶重构后，登录后需要同步赛季战力以及排名
func (u *User) SyncSeasonPower(srv servicer) {
	if u.dbUser.Base.UpdateSeasonPower {
		return
	}

	var needSync bool
	version := goxml.GetData().ServerInfoM.Version
	if srv.ServerType() == goxml.SERVER_TYPE_CN {
		needSync = version >= 2016005 //nolint:mnd
	} else {
		needSync = version >= 2022000 //nolint:mnd
	}

	if needSync {
		u.UpdateSeasonPower(srv)
		u.dbUser.Base.UpdateSeasonPower = true
		u.setSaveTag(saveTagBase)
	}
}

// 获取拥有赐福英雄数量最大值
func (u *User) GetMaxBlessedHeroCnt() uint32 {
	return u.dbUser.Base.MaxBlessedHeroCnt
}

// 设置拥有赐福英雄数量最大值
func (u *User) SetMaxBlessedHeroCnt(cnt uint32) {
	u.dbUser.Base.MaxBlessedHeroCnt = cnt
	u.setSaveTag(saveTagBase)
}

func (u *User) CacheClientData(id common.FUNCID, clientData []byte) {
	if u.clientData == nil {
		u.clientData = make(map[common.FUNCID][]byte)
	}
	u.clientData[id] = clientData
}

func (u *User) GetClientData(id common.FUNCID) []byte {
	data := u.clientData[id]
	delete(u.clientData, id)
	return data
}

func (u *User) GetSeasonEnterTime() int64 {
	return u.dbUser.Module7.SeasonEnterTime
}

func (u *User) SetSeasonEnterTime(enterTime int64) {
	u.dbUser.Module7.SeasonEnterTime = enterTime
	u.setSaveTag(saveTagModule7)
}

func (u *User) RebaseCheck(now int64, srv servicer) bool {
	return goxml.GetData().ConfigInfoM.CanReqRebase(now, u.DungeonID()) && !u.GetHasRebase() && srv.ServerType() == goxml.SERVER_TYPE_CN
}

func (u *User) SeasonMapBag() *db.SeasonMapBag {
	return u.dbUser.Bag.SeasonMapBag
}

func (u *User) RepairData(srv servicer) {
	//
	m := srv.GetHotfixManager()
	if u.GetHotfixVersion() < m.GetMaxVersion() {
		for _, v := range m.GetInfos() {
			if u.GetHotfixVersion() < v.GetVersion() {
				f := v.GetF()
				fr := f.Interface().(func(*User, Servicer))
				fr(u, srv)
			}
		}
	}
}

func RepairBattleData(srv servicer, ub *db.UserBattleData) {
	m := srv.GetHotfixManager()
	if ub != nil && ub.User != nil && ub.User.Base != nil && ub.User.Base.HotfixVersion < m.GetMaxBFVersion() {
		for _, v := range m.GetInfos() {
			if v.GetNeedBF() && ub.User.Base.HotfixVersion < v.GetVersion() {
				f := v.GetBF()
				fr := f.Interface().(func(*db.UserBattleData, Servicer))
				fr(ub, srv)
			}
		}
	}
}
