package character

import (
	"app/gmxml"
	"app/goxml"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"slices"
)

type ActivityCoupon struct {
	owner *User

	data *cl.ActivityCoupon //db-save-check
}

func NewActivityCoupon(owner *User) *ActivityCoupon {
	return &ActivityCoupon{
		owner: owner,
	}
}

func (a *ActivityCoupon) load(data *cl.ActivityCoupon) {
	a.data = data
	if a.data == nil {
		a.data = &cl.ActivityCoupon{}
	}
}

func (a *ActivityCoupon) Save() {
	a.owner.dbUser.Module8.ActivityCoupon = a.data
	a.owner.setSaveTag(saveTagModule8)
}

func (a *ActivityCoupon) ResetDaily() {
	a.CheckAndReset(gmxml.ActivityCouponInfoM.GetCurrentActivity())
}

func (a *ActivityCoupon) CheckAndReset(xml *cl.ActivityCouponXml) {
	if xml == nil {
		if a.data == nil || a.data.ActId != 0 {
			a.data = &cl.ActivityCoupon{}
			a.Save()
		}
	} else {
		if a.data == nil || a.data.ActId != xml.ActId {
			a.data = &cl.ActivityCoupon{
				ActId:  xml.ActId,
				RecvId: make([]uint32, len(xml.Res)),
			}
			a.Save()
		}
	}
}

func (a *ActivityCoupon) Get() *cl.ActivityCoupon {
	return a.data
}

func (a *ActivityCoupon) IsBuy(openDay int, rewardType uint32) bool {
	if openDay > len(a.data.RecvId) {
		return false
	}
	mask := uint32(1 << rewardType)
	return a.data.RecvId[openDay-1]&mask != uint32(0)
}

func (a *ActivityCoupon) SetBuy(openDay int, rewardType uint32) {
	if openDay > len(a.data.RecvId) {
		tmp := make([]uint32, openDay)
		copy(tmp, a.data.RecvId)
		a.data.RecvId = tmp
	}
	mask := uint32(1 << rewardType)
	a.data.RecvId[openDay-1] |= mask
	a.Save()
}

func (p *ActivityCoupon) process(srv servicer, order *db.Order) bool {
	now := time.Now().Unix()
	p.CheckAndReset(gmxml.ActivityCouponInfoM.GetCurrentActivity())

	couponInfo := gmxml.ActivityCouponInfoM.Get(p.data.ActId)
	if couponInfo == nil {
		l4g.Errorf("user:%d ActivityCoupon process get xml failed", p.owner.ID())
		return false
	}

	openDay := int(util.DaysBetweenTimes(couponInfo.OpenDay, now) + 1)
	if p.IsBuy(openDay, order.Custom.Id) {
		l4g.Errorf("user:%d ActivityCoupon process openday:%d rewardtype:%d is buy", p.owner.ID(), openDay, order.Custom.Id)
		return false
	}

	if order.Custom.Id != goxml.ActivityCouponRecharge {
		l4g.Errorf("user:%d ActivityCoupon process order custom id:%d is error", p.owner.ID(), order.Custom.Id)
		return false
	}

	index := slices.IndexFunc(couponInfo.Res, func(res *cl.ActivityCouponRes) bool {
		if res.Day == uint32(openDay) && res.AwardType == order.Custom.Id {
			return true
		}
		return false
	})

	if index == -1 {
		l4g.Errorf("user:%d ActivityCoupon process get awardinfo failed", p.owner.ID())
		return false
	}

	awardInfo := couponInfo.Res[index]
	if awardInfo == nil || awardInfo.InternalId == 0 || awardInfo.InternalId != order.Custom.InternalId {
		l4g.Errorf("user:%d ActivityCoupon process get awardinfo failed", p.owner.ID())
		return false
	}

	smsg := &cl.L2C_ActivityCouponBuy{
		Ret:        uint32(cret.RET_OK),
		SysId:      p.data.ActId,
		RewardType: order.Custom.Id,
	}

	smsg.Ret, smsg.Award = p.owner.Award(srv, awardInfo.Award, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_COUPON_RECV), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d ActivityCoupon process award failed ret:%d", p.owner.ID(), smsg.Ret)
		return false
	}

	p.SetBuy(openDay, order.Custom.Id)

	p.owner.SendCmdToGateway(cl.ID_MSG_L2C_ActivityCouponBuy, smsg)
	return true
}
