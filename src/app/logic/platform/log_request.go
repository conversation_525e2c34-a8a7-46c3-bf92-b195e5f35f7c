package platform

import (
	"app/protos/in/log"
	"bytes"
	"encoding/json"
	"strings"

	l4g "github.com/ivanabc/log4go"
)

func (h *HTTPActor) GetEsLogData() {
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"title": "golang",
			},
		},
		"size": 10,
		"from": 0,
	}
	queryJSON, err := json.Marshal(query)
	if err != nil {
		l4g.<PERSON><PERSON><PERSON>("Error marshaling query: %s", err)
		return
	}
	req := &log.LogQueryReq{
		Type: log.LOG_QUERY_TYPE_LOG_QUERY_TYPE_ES,
		EsQuery: &log.EsQueryReq{
			Index: strings.ToLower(log.ES_LOG_INDEX_ES_LOG_INDEX_USER_INFO.String()),
			Query: queryJSON,
		},
	}
	reqJSON, err := json.Marshal(req)
	if err != nil {
		l4g.Errorf("Error marshaling query: %s", err)
		return
	}
	c := h.m.httpClient()
	c.<PERSON>("test", "application/json", bytes.NewReader(reqJSON))
}
