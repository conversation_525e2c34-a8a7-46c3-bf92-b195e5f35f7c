package service

import (
	"app/gmxml"
	"app/goxml"
	"app/hotfix"
	"app/logic/account"
	"app/logic/activity"
	"app/logic/character"
	cbase "app/logic/command/base"
	"app/logic/cross"
	"app/logic/db"
	"app/logic/etcdcache"
	ae "app/logic/event"
	"app/logic/evententity"
	"app/logic/helper"
	"app/logic/helper/log"
	"app/logic/mongo"
	"app/logic/multilang"
	"app/logic/oss"
	"app/logic/platform"
	"app/logic/rank"
	"app/logic/rankachieve"
	"app/logic/request"
	"app/logic/session"
	"app/protos/in/config"
	pdb "app/protos/in/db"
	"app/protos/in/g2l"
	"app/protos/in/l2c"
	"app/protos/in/l2cm"
	"app/protos/in/p2l"
	"app/protos/in/r2l"
	"app/protos/out/cg"
	"app/protos/out/cl"
	"app/protos/out/ret"
	appsrv "app/service"
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync/atomic"

	plog "app/protos/in/log"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/discovery"
	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/id"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/skiplist"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/timer"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"
	"gitlab.qdream.com/platform/proto/da"
)

type LogicService struct {
	cfg         *goxml.LogicServiceConfig
	cfgFromEtcd *config.Logic

	frame        uint64
	idFactory    *id.Factory
	logIDFactory *id.Factory //日志使用

	resourceLogIDFactory *id.Factory //资源快照日志使用
	userLogIDFactory     *id.Factory //聊天，玩法内日志等用的ID

	eventM *event.Manager
	timerM *timer.Manager

	reqTimeM *request.TimeOutManager

	//about gateway
	gateM       *session.Manager
	gatewayCmds *parse.CommandM
	//about redis
	redisActor *db.RedisActor
	redisCmds  *parse.CommandM

	//about platform
	httpM    *platform.HTTPManager
	httpCmds *parse.CommandM

	//about account
	accountM *account.Manager
	//about cross
	crossM          *cross.Manager
	crossCmds       *parse.CommandM
	crossMasterM    *cross.CrossMasterManager
	crossMasterCmds *parse.CommandM

	userM *character.UserManager

	//消息数量统计
	gateMsgsCount         uint64
	dbMsgsCount           uint64
	platformHttpMsgsCount uint64
	//ossHttpMsgsCount      uint64
	//redisDBMsgsCount uint64
	crossMsgsCount uint64

	rd    *rand.Rand
	queue *skiplist.Set //排队机制

	asyncMessages map[uint64]interface{} //缓存异步消息

	//活动
	acts          []activity.Activity
	actsTickCount uint32 //tick累计计数器
	actsGroupSize uint32 //根据tick周期，每秒需要处理的活动数量

	globalRanks []*rank.Global
	smallRankM  *rank.SmallRankManager
	commonRankM *rank.CommonRankManager

	startTime time.Time //开始启动时间

	etcdNode string
	etcdCfgC chan *config.Logic

	logCollect         *log.Collect
	resourceLogCollect *log.Collect
	resourceLogger     *db.ResourceLogCollect

	keepalive chan struct{}

	//排行成就
	rankAchieveM *rankachieve.Manager

	//多语言
	multiLangM *multilang.Manager

	logTraceUsers map[uint64]util.None

	timeZone   string
	zoneOffset int

	//oss
	ossM *oss.OSSManager

	// simulationSendCmdToCross targetCmdToTargetMessage
	simulationTarget map[uint32]proto.Message // key: targetCmd  value: targetMes

	//about mongo
	mongoM *mongo.Manager

	logicHotRankCollectionLogC chan map[uint32]*pdb.LogicHotRankCollectionLog

	etcdRawData chan *etcdcache.EtcdMessage
	etcdCacheM  *etcdcache.EtcdCacheManager

	hotfixM           *hotfix.HotfixManager
	logCollectManager *log.CollectManger
}

func NewService() *LogicService {
	zone, offset := time.Now().Zone()
	logicService := &LogicService{
		frame:                      0,
		eventM:                     event.NewManager(),
		timerM:                     timer.NewManager(1024),
		reqTimeM:                   request.NewTimeOutManager(1024),
		gatewayCmds:                parse.NewCommandM(uint32(g2l.ID_MSG_MIN), uint32(g2l.ID_MSG_MAX), commandMaxExecuteTime),
		redisCmds:                  parse.NewCommandM(uint32(r2l.ID_MSG_MIN), uint32(r2l.ID_MSG_MAX), commandMaxExecuteTime),
		httpCmds:                   parse.NewCommandM(uint32(p2l.ID_MSG_MIN), uint32(p2l.ID_MSG_MAX), commandMaxExecuteTime),
		crossCmds:                  parse.NewCommandM(uint32(l2c.ID_MSG_MIN), uint32(l2c.ID_MSG_MAX), commandMaxExecuteTime),
		crossMasterCmds:            parse.NewCommandM(uint32(l2cm.ID_MSG_MIN), uint32(l2cm.ID_MSG_MAX), commandMaxExecuteTime),
		rd:                         rand.New(time.Now().UnixNano()),
		queue:                      skiplist.NewSet(&queueCmp{}),
		asyncMessages:              make(map[uint64]interface{}),
		acts:                       make([]activity.Activity, activity.MAX),
		globalRanks:                make([]*rank.Global, rank.MAX),
		smallRankM:                 rank.NewSmallRankManager(),
		commonRankM:                rank.NewCommonRankManager(),
		etcdCfgC:                   make(chan *config.Logic, 10),
		startTime:                  time.AccurateNow(),
		keepalive:                  make(chan struct{}),
		rankAchieveM:               rankachieve.NewManager(),
		multiLangM:                 multilang.NewManager(),
		logTraceUsers:              make(map[uint64]util.None),
		timeZone:                   zone,
		zoneOffset:                 offset / 3600,
		logicHotRankCollectionLogC: make(chan map[uint32]*pdb.LogicHotRankCollectionLog, 10),
		etcdRawData:                make(chan *etcdcache.EtcdMessage, 10),
		etcdCacheM:                 etcdcache.NewEtcdCacheManager(),
		hotfixM:                    hotfix.NewHotfixManager(),
	}
	logicService.etcdCacheM.LoadService(logicService)
	return logicService
}

func (l *LogicService) Frame() uint64                           { return l.frame }
func (l *LogicService) LogicLoop()                              { l.frame++ }
func (l *LogicService) EventM() *event.Manager                  { return l.eventM }
func (l *LogicService) GateM() *session.Manager                 { return l.gateM }
func (l *LogicService) CrossM() *cross.Manager                  { return l.crossM }
func (l *LogicService) CrossMasterM() *cross.CrossMasterManager { return l.crossMasterM }
func (l *LogicService) RedisActor() *db.RedisActor              { return l.redisActor }
func (l *LogicService) HTTPM() *platform.HTTPManager            { return l.httpM }
func (l *LogicService) AccountM() *account.Manager              { return l.accountM }
func (l *LogicService) UserM() *character.UserManager           { return l.userM }
func (l *LogicService) MongoM() *mongo.Manager                  { return l.mongoM }
func (l *LogicService) GateMsgsCount() uint64                   { return l.gateMsgsCount }
func (l *LogicService) GateMsgsLeftCount() int                  { return len(l.gateM.ReadQ) }
func (l *LogicService) DBMsgsCount() uint64                     { return l.dbMsgsCount }
func (l *LogicService) RedisDBMsgsCount() uint64                { return atomic.LoadUint64(&l.redisActor.DBMsgsCount) }
func (l *LogicService) PlatformHttpMsgsCount() uint64           { return l.platformHttpMsgsCount }
func (l *LogicService) CrossMsgsCount() uint64                  { return l.crossMsgsCount }
func (l *LogicService) DBMsgsLeftCount() int                    { return len(l.redisActor.MsgQ) }
func (l *LogicService) RedisDBMsgsLeftCount() int               { return int(l.redisActor.MsgQSize()) }
func (l *LogicService) PlatformHttpMsgsLeftCount() int          { return len(l.httpM.MsgQ) }
func (l *LogicService) CrossMsgsLeftCount() int                 { return len(l.crossM.ReadQ) }
func (l *LogicService) Rand() *rand.Rand                        { return l.rd }
func (l *LogicService) Queue() *skiplist.Set                    { return l.queue }
func (l *LogicService) SmallRankM() *rank.SmallRankManager      { return l.smallRankM }
func (l *LogicService) CommonRankM() *rank.CommonRankManager    { return l.commonRankM }
func (l *LogicService) ReqTimeM() *request.TimeOutManager       { return l.reqTimeM }
func (l *LogicService) RankAchieveM() *rankachieve.Manager      { return l.rankAchieveM }
func (l *LogicService) MultiLangM() *multilang.Manager          { return l.multiLangM }
func (l *LogicService) GetTimeZoneAndOffest() string {
	return l.timeZone + "|" + strconv.FormatInt(int64(l.zoneOffset), 10)
}

/***********etcd config******************************/
func (l *LogicService) ServerID() uint64   { return l.cfgFromEtcd.ServerId }
func (l *LogicService) NodeID() uint64     { return l.cfgFromEtcd.NodeId }
func (l *LogicService) AppID() uint64      { return l.cfgFromEtcd.AppId }
func (l *LogicService) OpGroup() uint64    { return l.cfgFromEtcd.OpGroup }
func (l *LogicService) ServerName() string { return l.cfgFromEtcd.ServerName }
func (l *LogicService) AheadCfg() *config.Ahead {
	return l.cfgFromEtcd.Logic.AheadConfig
}

// 开服时间
func (l *LogicService) StartTime() int64      { return l.cfgFromEtcd.StartTime }
func (l *LogicService) StartServiceTm() int64 { return l.StartTime() }

// 服务器白名单开启
func (l *LogicService) EnableLoginLimit() bool { return l.cfgFromEtcd.EnableLoginLimit }
func (l *LogicService) ServiceStatus() int32   { return int32(l.cfgFromEtcd.Status) }

// 协议加密
func (l *LogicService) EnableCrypto() bool          { return l.cfgFromEtcd.Logic.EnableCrypto }
func (l *LogicService) GetUserSaveInterval() int64  { return l.cfgFromEtcd.Logic.UserSaveInterval }
func (l *LogicService) GetUserCacheInterval() int64 { return l.cfgFromEtcd.Logic.UserCacheInterval }
func (l *LogicService) GiftCodeURL() string         { return l.cfgFromEtcd.Platform.GiftCodeUrl }
func (l *LogicService) SeasonFlashBackUrl() string  { return l.cfgFromEtcd.Platform.SeasonFlashBackUrl }
func (l *LogicService) PayRebateURL() string        { return l.cfgFromEtcd.Platform.PayRebateUrl }
func (l *LogicService) PlatformKey() string         { return l.cfgFromEtcd.Platform.Secret }
func (l *LogicService) BattleDebug() bool           { return l.cfgFromEtcd.Logic.BattleDebug }
func (l *LogicService) WebProxyUrl() string         { return l.cfgFromEtcd.Platform.WebProxyUrl }
func (l *LogicService) ChatAddr() string {
	aheadCfg := l.AheadConfig()
	chatCfg := l.cfgFromEtcd.Platform.Chat
	if aheadCfg == nil || aheadCfg.ChatAddr == "" {
		return chatCfg.CurrentAddr
	}

	return aheadCfg.ChatAddr
}

func (l *LogicService) SdkPushAddr() string {
	return l.cfgFromEtcd.Platform.SdkPushAddr
}

func (l *LogicService) SdkAppID() uint64 {
	appID, _ := strconv.Atoi(l.cfgFromEtcd.Platform.SdkAppId)
	return uint64(appID)
}

func (l *LogicService) SdkSecretKey() string {
	return l.cfgFromEtcd.Platform.SdkSecretKey
}

// 先行服相关配置
func (l *LogicService) AheadConfig() *config.Ahead { return l.cfgFromEtcd.Logic.AheadConfig }

// 日志topic
func (l *LogicService) LogTopicDC() string {
	defaultTopic := "log-ngame"
	cfg := l.AheadConfig()
	if cfg == nil || cfg.LogTopicDc == "" {
		return defaultTopic
	}
	return cfg.LogTopicDc
}
func (l *LogicService) LogTopicResource() string {
	defaultTopic := "resource-ngame"
	cfg := l.AheadConfig()
	if cfg == nil || cfg.LogTopicResource == "" {
		return defaultTopic
	}
	return cfg.LogTopicResource
}

/***********etcd config******************************/

func (l *LogicService) EnableTraceLog(uid uint64) bool {
	if l.cfgFromEtcd.Logic.EnableTraceLog {
		return true
	}
	_, exist := l.logTraceUsers[uid]
	return exist
}

// ServerDay
// @Description: 用来获取开服第几天（旧方法），注意：此方法没有判断当前时间大于开服时间！！！
// @receiver l
// @param now
// @return uint32
func (l *LogicService) ServerDay(now int64) uint32 {
	return helper.DaysBetweenTimes(now, l.StartTime()) + 1
}

func (l *LogicService) GetHotfixManager() *hotfix.HotfixManager {
	return l.hotfixM
}

// ServerOpenDay
// @Description: 用来获取开服第几天，开服当天算第一天，未开服返回 0
// @receiver l
// @param now
// @return uint32
func (l *LogicService) ServerOpenDay(now int64) uint32 {
	if now < l.StartTime() {
		return 0
	}
	return helper.DaysBetweenTimes(now, l.StartTime()) + 1
}

func (l *LogicService) ServerMonth(now int64) uint32 {
	return helper.MonthsBetweenTimes(now, l.StartTime()) + 1
}

func (l *LogicService) NewLogMessage() *da.Log {
	return l.logCollect.NewMessage()
}
func (l *LogicService) WriteLogMessage(msg *da.Log) {
	l.logCollect.Write(msg)
	//l4g.Debugf("[LogCollect] write log message: %+v", msg)
}

func (l *LogicService) NewResourceLogMessage() *da.Log {
	return l.resourceLogCollect.NewMessage()
}
func (l *LogicService) WriteResourceLogMessage(msg *da.Log) {
	l.resourceLogCollect.Write(msg)
	//l4g.Debugf("[ResourceLogCollect] write resource log message: %+v", msg)
}

// 合服情况的兼容
func (l *LogicService) CheckLocalServer(sid uint64) bool {
	return l.ServerID() == sid
}

func (l *LogicService) GetAsyncMessage(id uint64) (interface{}, bool) {
	ret, exist := l.asyncMessages[id]
	return ret, exist
}

type CrossMsgTimeOut struct {
	AsyncMsgID uint64
	Srv        *LogicService
}

func (cm *CrossMsgTimeOut) TimeOut(now int64) {
	if msg, ok := cm.Srv.GetAsyncMessage(cm.AsyncMsgID); ok {
		cm.Srv.DeleteAsyncMessage(cm.AsyncMsgID)
		if amsg, ok := msg.(cbase.AsyncMessager); ok {
			amsg.Work(uint32(ret.RET_CROSS_REQ_TIMEOUT), nil)
		}
	}
}

func (l *LogicService) CreateAsyncMessage(msg interface{}, timeout int64) uint64 {
	id := l.CreateUniqueID()
	l.asyncMessages[id] = msg
	if timeout > 0 {
		if amsg, ok := msg.(cbase.AsyncMessager); ok {
			tid := l.timerM.AddTimer(&CrossMsgTimeOut{id, l}, time.Now().Unix()+timeout, 0)
			amsg.AddTimeoutID(tid)
		}
	}
	return id
}

func (l *LogicService) DeleteAsyncMessage(id uint64) {
	delete(l.asyncMessages, id)
}

func (l *LogicService) DeleteTimerID(id uint32) {
	//这个性能不一定很高，要遍历所有的
	l.timerM.RemoveTimer(id)
}

func (l *LogicService) LoadConfig() bool {

	l.cfg = new(goxml.LogicServiceConfig)
	l.cfg.Load(*appsrv.ServiceConfig, *appsrv.Show)
	goxml.Load(*appsrv.DataPath, *appsrv.Show, false)
	gmxml.Load(*appsrv.GMDataPath, *appsrv.Show)
	return true

}

//nolint:funlen
func (l *LogicService) Init(serviceID string) bool {
	etcdClientCfg := &discovery.Config{
		Servers:     strings.Split(l.cfg.Etcd.Servers, ","),
		DialTimeout: l.cfg.Etcd.DialTimeout,
	}
	etcdClient, err := discovery.GetClient(etcdClientCfg)
	if err != nil {
		l4g.Errorf("get etcd client failed: %s", err)
		return false
	}
	l.etcdCacheM.LoadClient(etcdClient)
	defer etcdClient.Close()
	cfg, err := getConfigFromEtcd(etcdClient, l.cfg.Etcd, serviceID)
	if err != nil {
		l4g.Errorf("get config from etcd failed: %s", err)
		return false
	}
	if cfg.GatewayCluster != nil {
		cfgCluster := *cfg.GatewayCluster
		err = l.etcdCacheM.InitEtcdSubCache(cfgCluster)
		if err != nil {
			l4g.Errorf("InitEtcdSubCache failed: %s", err)
			return false
		}
	}

	if cfg.Platform != nil {
		cfgPlatform := *cfg.Platform
		err = l.etcdCacheM.InitEtcdSubCache(cfgPlatform)
		if err != nil {
			l4g.Errorf("InitEtcdSubCache failed: %s", err)
			return false
		}
	}

	l.etcdNode = l.cfg.Etcd.Logic + serviceID

	l4g.Infof("etcd config: %+v", cfg)
	l.cfgFromEtcd = cfg

	//跨服增加运营组后缀
	suffixOpGroup := fmt.Sprintf("%d/", cfg.OpGroup)
	l.cfg.Etcd.CrossActivity += suffixOpGroup

	l.openCmdRecoverPanic()
	l.registerCmds()
	l.userM = character.NewUserManager(int(cfg.Logic.MaxCacheEntries), l.GetUserCacheInterval())
	l.openEventRecoverPanic()
	l.openTimerRecoverPanic()
	l.initEventWatcher()
	l.registerActivities()
	l.registerGlobalRanks()
	l.registerCommonRank()
	return true
}

func (l *LogicService) openEventRecoverPanic() {
	l.EventM().OpenRecoverPanic()
}

func (l *LogicService) openTimerRecoverPanic() {
	l.timerM.OpenRecoverPanic()
}

func (l *LogicService) initEventWatcher() {
	evententity.InitEventWatcher(l.EventM())
	l.EventM().Watch(ae.IeServiceOneTicker, &doOneTickerEvent)
}

func (l *LogicService) ReloadConfig() {
	show := true
	go goxml.Load(*appsrv.DataPath, show, false)
	l4g.Infof("reload config success...")
}

func (l *LogicService) CreateUniqueID() uint64 {
	id := createID(l.idFactory)
	return id
}

func (l *LogicService) CreateLogID() uint64 {
	return createID(l.logIDFactory)
}

func (l *LogicService) CreateUserLogID() uint64 {
	return createID(l.userLogIDFactory)
}

func (l *LogicService) CreateResourceLogID() uint64 {
	return createID(l.resourceLogIDFactory)
}

func createID(factory *id.Factory) uint64 {
	var id uint64
	for {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Millisecond)
		defer cancel()
		id = factory.Create(ctx)
		if id != 0 {
			break
		} else {
			l4g.Errorf("wait %s backup id too long...", factory.Name())
		}
	}
	return id
}

func (l *LogicService) cleanGatewayCommand() {
	srvCtx := appsrv.CreateServiceContext(l)
	for more := true; more; {
		select {
		case msg := <-l.gateM.ReadQ:
			switch msg.Cmd {
			case session.CloseSessionCmd:
				l4g.Infof("gateway session close: %+v", msg)
			default:
				if err, _ := l.gatewayCmds.Dispatcher(srvCtx, msg); err != nil {
					l4g.Errorf("process gateway command(%+v) error: %s", msg, err)
				}
				l.gateMsgsCount++
			}
		case msg := <-l.crossM.ReadQ:
			switch msg.Cmd {
			case session.CloseSessionCmd:
				l4g.Infof("cross session close: %+v", msg)
			default:
				l4g.Debugf("cross message: %+v", msg.PackHead)
				if err, _ := l.crossCmds.Dispatcher(srvCtx, msg); err != nil {
					l4g.Errorf("process cross (%d-%d) command(%+v)  error: %s",
						msg.SessionID, msg.SessionNetAddr, msg.PackHead, err)
				}
				l.crossMsgsCount++
			}
		case msg := <-l.redisActor.MsgQ:
			l4g.Debugf("redis message: %+v", msg)
			l.dbMsgsCount++
		case msg := <-l.httpM.MsgQ:
			l4g.Debugf("http message: %+v", msg)
			l.platformHttpMsgsCount++
		// case <-l.ossM.GetMsgQ():
		// 	l4g.Info("discard oss message")
		// 	l.ossHttpMsgsCount++
		default:
			more = false
		}
		l.EventM().Process(l)
	}
}

func (l *LogicService) close() {
	l.saveAllActivities()
	l.userM.Close(l)
	l.smallRankM.Save(l)
	l.commonRankM.Save(l)

	//最后一条消息
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_Finish), 0, &r2l.L2R_Finish{})
	//todo 这里可能卡死。。 Finish消息一直不返回。
	/*
		for msg := range l.redisActor.MsgQ {
			l4g.Debugf("redis message: %+v", msg)
			l.dbMsgsCount++
			if msg.Cmd == uint32(r2l.ID_MSG_R2L_Finish) {
				break
			}
		}
	*/

	recvMsg := false
	ticker := time.NewTicker(5 * time.Minute) //nolint:mnd
	defer ticker.Stop()
FINISH:
	for {
		select {
		case msg := <-l.redisActor.MsgQ:
			l4g.Debugf("redis message: %+v", msg)
			recvMsg = true
			l.dbMsgsCount++
			if msg.Cmd == uint32(r2l.ID_MSG_R2L_Finish) {
				break FINISH
			}
		case <-ticker.C:
			if !recvMsg {
				l4g.Errorf("wait redis finish message timeout")
				break FINISH
			}
			recvMsg = false
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second) //nolint:mnd
	if err := l.idFactory.Close(ctx); err != nil {
		l4g.Errorf("id factory close error: %s", err)
	}
	cancel()

	ctx, cancel = context.WithTimeout(context.Background(), 15*time.Second) //nolint:mnd
	if err := l.logIDFactory.Close(ctx); err != nil {
		l4g.Errorf("log id factory close error: %s", err)
	}
	cancel()

	ctx, cancel = context.WithTimeout(context.Background(), 15*time.Second) //nolint:mnd
	if err := l.resourceLogIDFactory.Close(ctx); err != nil {
		l4g.Errorf("resource log id factory close error: %s", err)
	}
	cancel()

	l.redisActor.Close()
	l.resourceLogger.Close()
	l.logCollect.Close()
	l.resourceLogCollect.Close()
	l.accountM.Close()
	l.mongoM.Close()
	l.httpM.Close()
	l.ossM.Close()
	l.logCollectManager.Close()
	l4g.Infof("[service] logic %d quit...", l.ServerID())
}

func (l *LogicService) Close() {
	l.cleanGatewayCommand()
	l.close()
	l4g.Infof("[service] logic %d quit...", l.ServerID())
}

func (l *LogicService) broadcastCmdToClient(id cl.ID, msg interface{}, ids []uint64) {
	bytes, err := proto.Marshal(msg.(proto.Message))
	if err != nil {
		l4g.Errorf("broadcast cmd error:%v", err)
		return
	}
	smsg := &cg.G2C_Broadcast{
		Cmd:  uint32(id),
		Info: bytes,
	}
	if len(ids) > 0 {
		smsg.Ids = ids
	}
	l.GateM().BroadcastMessage(&parse.PackHead{Cmd: uint32(cg.ID_MSG_G2C_Broadcast)}, smsg)
	l4g.Debugf("broadcast cmd:%d %s %v", id, msg, ids)
}

// 全服广播消息
func (l *LogicService) BroadcastCmdToClient(id cl.ID, msg interface{}) {
	l.broadcastCmdToClient(id, msg, nil)
}

// 直接按运营商和渠道ID广播
func (l *LogicService) broadcastCmdByOpIDAndChannel(id cl.ID, msg interface{}, ops, channels []uint32) {
	var cookies []uint64
	l.UserM().Range(func(u *character.User) {
		if helper.CheckOpAndChannel(u.OpID(), u.Channel(), ops, channels) {
			cookies = append(cookies, u.Cookie())
		}
	})
	if cookies != nil {
		l.broadcastCmdToClient(id, msg, cookies)
	}
}

// 按运营商+渠道ID广播消息
func (l *LogicService) BroadcastCmdByOpIDAndChannel(id cl.ID, msg interface{}, ops, channels []uint32) {
	opSize := len(ops)
	if opSize == 0 {
		// 全服广播
		l.broadcastCmdToClient(id, msg, nil)
	} else {
		// 按照OpID和Channel广播
		l.broadcastCmdByOpIDAndChannel(id, msg, ops, channels)
	}
}

// 给部分玩家广播消息
func (l *LogicService) BroadCastCmdByUids(uids []uint64, id cl.ID, msg interface{}) {
	cookies := make([]uint64, 0, len(uids))
	for _, uid := range uids {
		if u := l.UserM().GetUser(uid); u != nil {
			cookies = append(cookies, u.Cookie())
		}
	}
	if len(cookies) > 0 {
		l.broadcastCmdToClient(id, msg, cookies)
	}
}

func (l *LogicService) LoginCheck(ip, deviceId, uuid string) bool {
	m := (l.etcdCacheM.GetSubEtcdCache(etcdcache.WhiteList)).(*etcdcache.Manager)
	if m == nil {
		return false
	}
	return m.CheckLimit(ip, deviceId, uuid)
}

func (l *LogicService) GetFunctionStatus(funcID uint32) bool {
	m := (l.etcdCacheM.GetSubEtcdCache(etcdcache.FunctionStatus)).(*etcdcache.FunctionStatusManager)
	if m == nil {
		return true
	}
	return m.GetFunctionStatus(funcID)
}

func (l *LogicService) QuestionnaireFlush(checkMsg *etcdcache.CheckQuestionnaireMsg, lang string) []*cl.Questionnaire {
	m := l.QuestionnaireM()
	if m == nil {
		return nil
	}
	return m.QuestionnairesFlush(checkMsg, lang)
}

func (l *LogicService) QuestionnaireM() *etcdcache.QuestionnaireManager {
	return (l.etcdCacheM.GetSubEtcdCache(etcdcache.Questionnaire)).(*etcdcache.QuestionnaireManager)
}

func (l *LogicService) GetGiftCodeAwards(awardID uint32) []*cl.Resource {
	m := (l.etcdCacheM.GetSubEtcdCache(etcdcache.GiftCode)).(*etcdcache.GiftCodeAwardsManager)
	if m == nil {
		return nil
	}
	return m.GetAwards(awardID)
}

func (l *LogicService) GetLinkSummonId() uint32 {
	m := (l.etcdCacheM.GetSubEtcdCache(etcdcache.LinkSummon)).(*etcdcache.LinkSummonTimeManager)
	if m == nil {
		return 0
	}
	return m.ActiveID()
}

func (l *LogicService) GetLinkSummonTimes() []*cl.LinkSummonTime {
	m := (l.etcdCacheM.GetSubEtcdCache(etcdcache.LinkSummon)).(*etcdcache.LinkSummonTimeManager)
	if m == nil {
		return nil
	}
	return m.GetActivityTimes()
}

func (l *LogicService) OSSM() *oss.OSSManager { return l.ossM }

func (l *LogicService) OssMsgsLeftCount() uint32 {
	var totalMsgLeft uint32
	for _, actors := range l.ossM.GetActors() {
		for _, actor := range actors.Actors {
			totalMsgLeft += actor.MsgQSize()
		}
	}
	return totalMsgLeft
}

func (l *LogicService) GetCrossMasterAddr() string {
	logicCrossMasterAddr := l.cfgFromEtcd.Logic.SelfCrossMaster
	if logicCrossMasterAddr != "" && strings.Contains(logicCrossMasterAddr, ":") {
		return logicCrossMasterAddr
	} else {
		return l.cfgFromEtcd.Platform.CrossMasterAddr
	}
}

// 获取游戏服的分区。如果为0.代表分区没有配置。或者还没有连上跨服
func (l *LogicService) GetCrossArea(arenaType uint32) uint32 {
	return l.crossM.GetCrossArea(arenaType)
}

func (l *LogicService) GetNormalPartSids() []uint64 {
	return l.crossM.GetNormalPartSids()
}

func (l *LogicService) ActivityMixM() *etcdcache.ActivityMixManager {
	return (l.etcdCacheM.GetSubEtcdCache(etcdcache.ActivityMix)).(*etcdcache.ActivityMixManager)
}

func (l *LogicService) LinkSetting() *etcdcache.LinkSettingManager {
	return (l.etcdCacheM.GetSubEtcdCache(etcdcache.LinkSetting)).(*etcdcache.LinkSettingManager)
}

func (l *LogicService) CouponCfgM() *etcdcache.CouponCfgManager {
	return l.etcdCacheM.GetSubEtcdCache(etcdcache.CouponCfg).(*etcdcache.CouponCfgManager)
}

func (l *LogicService) RefundMailTemplateM() *etcdcache.RefundMailTemplateManager {
	return l.etcdCacheM.GetSubEtcdCache(etcdcache.RefundEmail).(*etcdcache.RefundMailTemplateManager)
}

func (l *LogicService) GetOssConfig() *etcdcache.OssConfig {
	return l.etcdCacheM.GetSubEtcdCache(etcdcache.OssList).(*etcdcache.OssConfig)
}

func (l *LogicService) FlowerPushFrequencyLimitDelete(uid uint64) {
	flowerM := l.FlowerM()
	if flowerM == nil {
		return
	}
	limitM := flowerM.GetPushFrequencyLimit()
	if limitM != nil {
		limitM.Delete(uid)
	}
}

func (l *LogicService) ServerType() string {
	return l.cfgFromEtcd.Platform.ServerType
}

func (l *LogicService) PlatformConfig() *config.Platform {
	return l.cfgFromEtcd.Platform
}

func (l *LogicService) SendHotRankLog(data map[uint32]*pdb.LogicHotRankCollectionLog) {
	l.logicHotRankCollectionLogC <- data
}

func (l *LogicService) GetActivityScheduleConfig() *cl.ActivityScheduleDatas {
	return l.etcdCacheM.GetSubEtcdCache("ActivityScheduleTarget").(*etcdcache.ActivityScheduleManager).Datas
}

func (l *LogicService) LogicServiceConfig() *goxml.LogicServiceConfig {
	return l.cfg
}

func (l *LogicService) GetEtcdMessage() chan *etcdcache.EtcdMessage {
	return l.etcdRawData
}

func (l *LogicService) NewLog(logType plog.LOG_TYPE) interface{} {
	return l.logCollectManager.GetPoolLog(logType)
}

func (l *LogicService) WriteLog(logType plog.LOG_TYPE, log log.LogI) {
	l.logCollectManager.WriteLog(logType, log)
}
