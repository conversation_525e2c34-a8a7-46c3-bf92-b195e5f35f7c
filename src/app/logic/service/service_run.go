package service

import (
	"app/csession"
	"app/goxml"
	"app/logic/account"
	"app/logic/activity"
	"app/logic/activity/hotrank"
	"app/logic/character"
	cdb "app/logic/command/db"
	coss "app/logic/command/oss"
	cplatform "app/logic/command/platform"
	"app/logic/cross"
	"app/logic/db"
	ae "app/logic/event"
	"app/logic/helper"
	"app/logic/helper/log"
	"app/logic/mail"
	"app/logic/mongo"
	"app/logic/oss"
	"app/logic/platform"
	"app/logic/session"
	"app/protos/in/config"
	"app/protos/in/l2c"
	"app/protos/in/l2cm"
	plog "app/protos/in/log"
	"app/protos/in/p2l"
	"app/protos/in/r2l"
	"app/protos/out/cg"
	"app/protos/out/common"
	"app/protos/out/ret"
	appsrv "app/service"
	"app/symbols"
	"context"
	"errors"
	"flag"
	"fmt"
	"path/filepath"
	"strings"
	"sync"

	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/discovery"
	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/id"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

var dcLogPath = flag.String("dc", "../log/", "data center log path")

func (l *LogicService) getServiceDataFromDB() {
	//加载全局ID， 每秒钟可生成10w个ID
	maxID := helper.MaxIDOfService(l.NodeID())
	l4g.Infof("service max id: %d", maxID)
	l.idFactory = id.NewFactory("logic", 10, 15, 100000, maxID, &idStore{l}) //nolint:mnd
	l.idFactory.Init()
	l4g.Infof("logic server unique id: %d, surplus: %d", l.idFactory.Current(), maxID-l.idFactory.Current())

	l.logIDFactory = id.NewFactory("log", 10, 15, 100000, maxID, &idStore{l}) //nolint:mnd
	l.logIDFactory.Init()
	l4g.Infof("logic server log unique id: %d, surplus: %d", l.logIDFactory.Current(), maxID-l.logIDFactory.Current())

	l.resourceLogIDFactory = id.NewFactory("resourcelog", 10, 15, 100000, maxID, &idStore{l}) //nolint:mnd
	l.resourceLogIDFactory.Init()
	l4g.Infof("logic server resource log unique id: %d, surplus: %d",
		l.resourceLogIDFactory.Current(), maxID-l.resourceLogIDFactory.Current())

	l.userLogIDFactory = id.NewFactory("userlog", 10, 15, 100000, maxID, &idStore{l}) //nolint:mnd
	l.userLogIDFactory.Init()
	l4g.Infof("logic server user log unique id: %d, surplus: %d", l.userLogIDFactory.Current(), maxID-l.userLogIDFactory.Current())

	//加载服务器数据
	l.redisActor.AddMessage(uint32(r2l.ID_MSG_L2R_Load), l.NodeID(), &r2l.L2R_Load{
		Sid:     l.NodeID(),
		RankIds: l.CommonRankM().GetLoadIds(),
	})
	msg := <-l.redisActor.MsgQ
	if msg.Cmd != uint32(r2l.ID_MSG_R2L_Load) {
		panic(fmt.Sprintf("load redis data error: %d", msg.Cmd))
	}
	loadData := msg.Data.(*r2l.R2L_Load)
	if loadData.Ret != uint32(ret.RET_OK) {
		panic(fmt.Sprintf("load redis data error: %d", loadData.Ret))
	}
	//全服邮件
	mailBox := mail.NewBox(l.NodeID())
	mailBox.Load(loadData.Mails)
	l.userM.SetMailBox(mailBox)
	//加载活动数据
	l.loadActivities(loadData)
	l.initActivities()
	//加载全局排行榜
	l.loadGlobalRanks(loadData)
	//小排行榜
	l.smallRankM.Load(loadData.SmallRank)
	//通用排行榜
	l.commonRankM.Load(loadData.CommonRank)
	//加载排行成就数据
	l.rankAchieveM.Load(loadData.RankAchieves)
	//加载多语言
	l.multiLangM.Load(loadData)
	//加载离线缓存资源
	l.userM.LoadOfflineResource(loadData.OfflineResource)
	//加载热修复代码
	l.hotfixM.Load(loadData.HotfixCodes, symbols.Symbols)
}

const maxEventProcessNum = 10

//nolint:funlen
func (l *LogicService) Run(group *ctx.Group) {
	parse.NewReadBufferPool(128)

	srvCtx := appsrv.CreateServiceContext(l)

	l.logCollect = log.NewCollect(group.CreateChild(), 2048, filepath.Join(*dcLogPath, "dc.log"), uint(*appsrv.RotationCount))
	l.resourceLogCollect = log.NewCollect(group.CreateChild(), 2048, filepath.Join(*dcLogPath, "resource.log"), uint(*appsrv.RotationCount))
	l.logCollectManager = log.NewCollectManager(group.CreateChild())
	kafkaHandlerLog := log.NewLogCollect(&log.LogCollectParams{
		ChanSize:      2048,
		LogPath:       filepath.Join(*dcLogPath, "kafka_handler.log"),
		RotationCount: uint(*appsrv.RotationCount),
		Pool: &sync.Pool{
			New: func() interface{} {
				return &plog.KafkaLogHandlerData{}
			},
		},
		Group: l.logCollectManager.NewGroup(),
	})
	l.logCollectManager.AddNewCollect(plog.LOG_TYPE_KAFKA_LOG, kafkaHandlerLog)
	l.logCollectManager.Run()
	//启动redis actor，使用顶层context保证关闭时机
	//guardGroup保证在group关闭时能将redis数据安全落地后再关闭
	guardCtx := ctx.NewGroup(context.Background())
	var redisCfg *actor.Config = l.cfg.GetActorConfig(actorKindRedis)
	var redisLimitCfg *db.LimitConfig = &db.LimitConfig{
		MaxAccounts:        int(l.cfgFromEtcd.Logic.AccountSize),
		IPAccounts:         int(l.cfgFromEtcd.Logic.IpAccounts),
		DeviceIDAccounts:   int(l.cfgFromEtcd.Logic.DeviceIdAccounts),
		IPAccountsHW:       int(l.cfgFromEtcd.Logic.IpAccountsHw),
		DeviceIDAccountsHW: int(l.cfgFromEtcd.Logic.DeviceIdAccountsHw),
		BanRegister:        l.cfgFromEtcd.Logic.BanRegister,
		Status:             int(l.cfgFromEtcd.Status),
	}

	l.redisActor = db.NewRedisActor(srvCtx, guardCtx.CreateChild(),
		l.cfgFromEtcd.Redis, goxml.GetData().ServerInfoM.RedisPassword, l.cfgFromEtcd.RedisIndex, redisCfg,
		redisLimitCfg)
	cdb.Init(l.redisActor.GetCommandM(), true)
	go l.redisActor.Run(l.redisActor)
	l.getServiceDataFromDB()

	var accCfg *actor.Config = l.cfg.GetActorConfig(actorKindAccount)
	l.accountM = account.New(srvCtx, guardCtx.CreateChild(), accCfg,
		int(l.cfgFromEtcd.AccParallel), l.cfgFromEtcd.AccAddress, l.cfgFromEtcd.ServerId)

	var httpCfg *actor.Config = l.cfg.GetActorConfig(actorKindHTTP)
	l.httpM = platform.NewHTTPManager(group.CreateChild(),
		httpCfg, int(l.cfgFromEtcd.Platform.Parallel), l.cfgFromEtcd)
	for _, actor := range l.httpM.Actors() {
		cplatform.Init(actor.GetCommandM(), true)
	}

	l.resourceLogger = db.NewResourceLogCollect(guardCtx.CreateChild(), l,
		l.cfgFromEtcd.Redis, goxml.GetData().ServerInfoM.RedisPassword, l.cfgFromEtcd.RedisIndex, redisCfg,
		goxml.GetData().ServerInfoM.ResourceLogTime)
	go l.resourceLogger.Run()

	l.ossM = oss.NewOSSManager(group.CreateChild(), l.cfg.GetActorConfig(actorKindOss),
		l.GetOssConfig(), int(l.cfgFromEtcd.OssParallel))
	for _, actors := range l.ossM.GetActors() {
		for _, actor := range actors.Actors {
			coss.Init(actor.GetCommandM(), true)
		}
	}
	l.ossM.Init()

	var mongoCfg *actor.Config = l.cfg.GetActorConfig(actorKindMongo)
	mongoAddr := l.cfgFromEtcd.MongoAddress
	if l.cfgFromEtcd.Logic.AheadConfig != nil && l.cfgFromEtcd.Logic.AheadConfig.MongoAddr != "" {
		mongoAddr = l.cfgFromEtcd.Logic.AheadConfig.MongoAddr
	}
	l.mongoM = mongo.New(srvCtx, guardCtx.CreateChild(), mongoCfg, int(l.cfgFromEtcd.MongoParallel), mongoAddr)

	//watch service node
	etcdcfg := l.cfg.GetDiscoveryConfig(false)
	etcdClient, err := discovery.GetClient(etcdcfg)
	if err != nil {
		l4g.Errorf("get etcd client failed: %s", err)
		group.Stop()
		return
	}
	defer etcdClient.Close()

	go func() {
		cfgW := &ConfigWatcher{
			C: l.etcdCfgC,
		}
		cfg := l.cfg.GetDiscoveryConfig(false)
		cfg.Target = l.etcdNode
		discovery.WatchByClient(group.CreateChild(), etcdClient, cfg, cfgW)
		group.Stop()
		l4g.Infof("service discovery goruntine closed")
	}()

	l.etcdCacheM.ServiceRun(group, etcdClient, l)
	defer l.etcdCacheM.Close()

	//连接跨服集群
	crossCfg := &session.Config{
		Name:              "Cross",
		ReadQSize:         10000,
		ReconnectInterval: 10,
		Zebra:             l.cfg.GetZebraConfig("cross"),
		Actor:             l.cfg.GetActorConfig(actorKindCross),
	}
	if goxml.GetData().ServerInfoM.IsGM() {
		crossCfg.Zebra.ReadTimeOut = goxml.LongTimeout
		crossCfg.Zebra.WriteTimeOut = goxml.LongTimeout
	}
	l.crossM = cross.New(l, group.CreateChild(), crossCfg)
	//l.crossM.Run()
	defer l.crossM.Close()

	crossMasterCfg := &csession.Config{
		Name:              "CrossMaster",
		ReadQSize:         10000,
		ReconnectInterval: 10,
		Zebra:             l.cfg.GetZebraConfig("crossmaster"),
		Actor:             l.cfg.GetActorConfig(actorKindCrossMaster),
	}
	if goxml.GetData().ServerInfoM.IsGM() {
		crossMasterCfg.Zebra.ReadTimeOut = goxml.LongTimeout
		crossMasterCfg.Zebra.WriteTimeOut = goxml.LongTimeout
	}
	l.crossMasterM = cross.NewCrossMasterManager(group.CreateChild(), crossMasterCfg, l.GetCrossMasterAddr())
	l.crossMasterM.Run()
	defer l.crossMasterM.Close()

	//连接网关集群
	gatewayCfg := &session.Config{
		Name:              "Gateway",
		ReadQSize:         10000,
		ReconnectInterval: 10,
		Discovery:         l.cfg.GetDiscoveryConfig(true),
		Zebra:             l.cfg.GetZebraConfig("gateway"),
		Actor:             l.cfg.GetActorConfig(actorKindGateway),
		Guard:             l.cfgFromEtcd.GatewayCluster.Guard,
		PortOffset:        1,
	}
	if goxml.GetData().ServerInfoM.IsGM() {
		gatewayCfg.Zebra.ReadTimeOut = goxml.LongTimeout
		gatewayCfg.Zebra.WriteTimeOut = goxml.LongTimeout
	}
	gatewayCfg.Discovery.Servers = strings.Split(l.cfgFromEtcd.GatewayCluster.EtcdServers, ",")
	gatewayCfg.Discovery.Target = l.cfgFromEtcd.GatewayCluster.Target

	l.gateM = session.NewManager(group.CreateChild(), gatewayCfg)
	l.gateM.Run()
	defer l.gateM.Close()

	//设置定时任务
	l.InitTimer()

	//计算ticker
	d := 1000 / character.UserGroup
	if d > 100 {
		d = 100
	}
	l.initActivitiesTick(d)
	ticker := time.NewTicker(time.Duration(d) * time.Millisecond)
	defer ticker.Stop()
	l4g.Infof("[service] logic %d run...", l.ServerID())

	l4g.Infof("[service] logic %d running, consume %v", l.ServerID(), time.Since(l.startTime))
	go l.infiniteLoopCheck()
	for {
		select {
		//增加消息优先级概念
		case <-group.Done():
			l4g.Infof("[service] goruntines manager close...")
			return
		case <-l.gateM.Done():
			l4g.Infof("[service] gateway manager close...")
			return
		case <-l.crossM.Done():
			l4g.Infof("[service] cross manager close...")
			return
		case handler := <-appsrv.Ctrls():
			handler(srvCtx)
		case cfg := <-l.etcdCfgC:
			l.changeEtcdConfig(cfg)
		case etcdMsg := <-l.etcdRawData:
			l4g.Infof("etcdMsg :%v", etcdMsg)
			subCache := l.etcdCacheM.GetSubEtcdCache(etcdMsg.ProtoKey)
			if subCache == nil {
				continue
			}
			err, send2Client := subCache.UpdateByRaw(etcdMsg.WatchKey, etcdMsg.Data, etcdMsg.MessageType)
			if err != nil {
				l4g.Errorf("<-l.etcdRawData err: %s", err.Error())
				continue
			}
			if send2Client {
				subCache.SendToClient(l)
			}
		case now := <-ticker.C:
			//保证ticker的优化级
			l.oneTicker(srvCtx, now.Unix())
		case <-l.keepalive:
			l4g.Infof("infinite loop check success")
		default:
			select {
			case now := <-ticker.C:
				//保证ticker的优化级
				l.oneTicker(srvCtx, now.Unix())
			case msg := <-l.gateM.ReadQ:
				l4g.Debugf("into gate message")
				switch msg.Cmd {
				case session.CloseSessionCmd:
					l4g.Infof("gateway session close: %+v", msg)
					l.userM.DeleteUsersByGate(msg.SessionID, l)
				case uint32(cg.ID_MSG_C2G_Login):
					if !l.prepareLogin(msg) {
						break
					}
					fallthrough
				default:
					l4g.Debugf("gateway message: %+v", msg.PackHead)
					if err, _ := l.gatewayCmds.Dispatcher(srvCtx, msg); err != nil {
						l4g.Errorf("process gateway (%d-%d) command(%+v)  error: %s",
							msg.SessionID, msg.SessionNetAddr, msg.PackHead, err)
						if errors.Is(err, parse.CommandPanicError) {
							l.UserM().AddWrongUser(msg.UID, l)
						}
					}
					l.gateMsgsCount++
					srvMetrics.msgsCount.WithLabelValues("gate").Inc()
				}
			case msg := <-l.crossM.ReadQ:
				switch msg.Cmd {
				case session.CloseSessionCmd:
					l4g.Infof("cross session close: %+v", msg)
					l.CrossSessionChange(msg.SessionID, msg.SessionNetAddr, session.SessionClosed)
				default:
					l4g.Debugf("cross message: %+v", msg.PackHead)
					if err, _ := l.crossCmds.Dispatcher(srvCtx, msg); err != nil {
						l4g.Errorf("process cross (%d-%d) command(%+v)  error: %s",
							msg.SessionID, msg.SessionNetAddr, msg.PackHead, err)
						if errors.Is(err, parse.CommandPanicError) {
							l.UserM().AddWrongUser(msg.UID, l)
						}
					}
					srvMetrics.msgsCount.WithLabelValues("cross").Inc()
					l.crossMsgsCount++
				}
			case msg := <-l.crossMasterM.ReadQ:
				switch msg.Cmd {
				case csession.CloseSessionCmd:
					l4g.Infof("cross master session close: %+v", msg)
					l.CrossMasterSessionChange(msg.SessionID, msg.SessionNetAddr, csession.SessionClosed)
				default:
					l4g.Debugf("cross master message: %+v", msg.PackHead)
					if err, _ := l.crossMasterCmds.Dispatcher(srvCtx, msg); err != nil {
						l4g.Errorf("process cross (%d-%d) command(%+v)  error: %s",
							msg.SessionID, msg.SessionNetAddr, msg.PackHead, err)
						if errors.Is(err, parse.CommandPanicError) {
							l.UserM().AddWrongUser(msg.UID, l)
						}
					}
				}

			case msg := <-l.redisActor.MsgQ:
				l4g.Debugf("redis message: %+v", msg)
				if err, _ := l.redisCmds.Dispatcher(srvCtx, msg); err != nil {
					l4g.Errorf("process redis command(%+v) error: %s", msg.PackHead, err)
					if errors.Is(err, parse.CommandPanicError) {
						l.UserM().AddWrongUser(msg.UID, l)
					}
				}
				l.dbMsgsCount++
				srvMetrics.msgsCount.WithLabelValues("redis").Inc()
			case msg := <-l.httpM.MsgQ:
				l4g.Debugf("http message: %+v", msg)
				if err, _ := l.httpCmds.Dispatcher(srvCtx, msg); err != nil {
					l4g.Errorf("process http command(%+v) error: %s", msg.PackHead, err)
					if errors.Is(err, parse.CommandPanicError) {
						l.UserM().AddWrongUser(msg.UID, l)
					}
				}
				l.platformHttpMsgsCount++
				srvMetrics.msgsCount.WithLabelValues("platform").Inc()
			case msg := <-l.mongoM.MsgQ:
				l4g.Debugf("mongo grpc message: %+v", msg)
				OnMongoMessage(srvCtx, msg)
			case hotRankLogic := <-l.logicHotRankCollectionLogC:
				hotRankM, ok := l.GetActivity(activity.HotRank).(*hotrank.Manager)
				if !ok {
					l4g.Error("get hot rank manager failed")
				} else {
					hotRankM.SetNewLogicRankLog(hotRankLogic)
				}
			}
		}
		//单次指令处理完之后，清理工作
		start := time.AccurateNow()
		events, process := l.EventM().Process(l)
		if process > maxEventProcessNum {
			l4g.Infof("event process pre loop: %d -> %d", events, process)
		}
		delay := time.Since(start)
		if int64(delay) > commandMaxExecuteTime {
			l4g.Infof("total event:%d %d execute time:%v", events, process, delay)
		}
	}

}

func (l *LogicService) oneTicker(srvCtx context.Context, _ int64) {
	//修正这里的时间为当前时间,防止定时器太多造成的延迟？？？
	now := time.Now().Unix()
	l.timerM.Run(now, 0)
	l.reqTimeM.Run(now, 0)
	l.UserM().Foreach(l, now)
	l.processQueue(srvCtx)
	l.onActivitiesTick(now)

	//finally
	l.GateM().ForceWrite()
	l.CrossM().ForceWrite()
	l.CrossMasterM().ForceWrite()
	l.EventM().Add(ae.CommonEvent{
		Event: ae.IeServiceOneTicker,
		Value: uint64(now),
	})
}

func (l *LogicService) SendCmdToDB(cmdID uint32, uid uint64, msg interface{}) {
	l.redisActor.AddMessage(cmdID, uid, msg)
}

func (l *LogicService) SendCmdToClient(gateID uint64, cmdID uint32, uid uint64, msg interface{}) {
	gate := l.GateM().GetClientByID(gateID)
	if gate == nil {
		l4g.Errorf("user %d send cmd %d, not find gate: %d", uid, cmdID, gateID)
		return
	}
	ph := &parse.PackHead{
		UID: uid,
		Cmd: cmdID,
	}
	gate.Write(ph, msg)
}

func (l *LogicService) SendCmdToPlatform(cmdID uint32, uid uint64, msg interface{}) {
	l.httpM.AddMessage(cmdID, uid, msg)
}

func (l *LogicService) SendCmdToCross(cmd l2c.ID, uid uint64, msg interface{}) bool {
	ph := &parse.PackHead{
		UID: uid,
		Cmd: uint32(cmd),
	}
	l4g.Debugf("SendToCross: uid:%d, cmd:%d, msg:%+v", uid, ph.Cmd, msg)
	return l.crossM.Write(ph, msg)
}

func (l *LogicService) SendCmdToCrossMaster(cmd l2cm.ID, uid uint64, msg interface{}) bool {
	ph := &parse.PackHead{
		UID: uid,
		Cmd: uint32(cmd),
	}
	l4g.Debugf("SendToCrossMaster: uid:%d, cmd:%d, msg:%+v", uid, ph.Cmd, msg)
	return l.crossMasterM.Write(ph, msg)
}

func (l *LogicService) SendRemoteLogic(
	targetSid uint64, remoteLogic l2c.REMOTE_LOGIC, msg interface{}) bool {
	pbMsg, ok := msg.(proto.Message)
	if !ok {
		l4g.Errorf("SendRemoteLogic: %d targetSid %d not proto message %+v",
			remoteLogic, targetSid, msg)
		return false
	}
	blob, err := proto.Marshal(pbMsg)
	if err != nil {
		l4g.Errorf("SendRemoteLogic: %d targetSid %d marshal proto message err:%v",
			remoteLogic, targetSid, err)
		return false
	}
	l4g.Debugf("SendRemoteLogic to server %d remote logic type %d data %+v",
		targetSid, remoteLogic, msg)
	return l.SendCmdToCross(l2c.ID_MSG_L2C_SendRemoteLogic, 0, &l2c.L2C_SendRemoteLogic{
		TargetSid:   targetSid,
		RemoteLogic: uint32(remoteLogic),
		Data:        blob,
	})
}

func (l *LogicService) prepareLogin(msg *session.Message) bool {
	if l.UserM().CookiesSize() < int(l.cfgFromEtcd.Logic.OnlineSize) {
		return true
	}
	if l.Queue().Length() < l.cfgFromEtcd.Logic.QueueSize {
		//进入排队系统
		score := l.CreateUniqueID()
		l.queue.Insert(newQueueValue(msg, score))

		l4g.Infof("user insert queue: %+v gate: %d score: %d sum: %d",
			msg.PackHead, msg.SessionID, score, l.Queue().Length())
		l.SendCmdToClient(msg.SessionID, uint32(cg.ID_MSG_G2C_Login), msg.UID, &cg.G2C_Login{
			Ret: uint32(cg.RET_QUEUING),
		})
	} else {
		l4g.Infof("user login sreve busy: queue:%d: queue size %+v", l.Queue().Length(), l.cfgFromEtcd.Logic)
		//服务器在线人数超过承载上限
		l.SendCmdToClient(msg.SessionID, uint32(cg.ID_MSG_G2C_Login), msg.UID, &cg.G2C_Login{
			Ret: uint32(ret.RET_SERVER_BUSY),
		})
	}
	return false
}

func (l *LogicService) processQueue(srvCtx context.Context) {
	for i := 0; i < int(l.cfgFromEtcd.Logic.QueuePopSize); i++ {
		if l.UserM().CookiesSize() < int(l.cfgFromEtcd.Logic.OnlineSize) && l.queue.Length() > 0 {
			//排队机制: 正式登陆
			node := l.queue.First()
			queueV := node.Value().(*queueValue)
			msg := queueV.msg
			if err, _ := l.gatewayCmds.Dispatcher(srvCtx, msg); err != nil {
				l4g.Errorf("process gateway command(%+v) error: %s", msg, err)
			}
			l.gateMsgsCount++
			srvMetrics.msgsCount.WithLabelValues("gate").Inc()
			l.queue.Delete(queueV)
			l4g.Infof("user leave queue: %+v, sum: %d", msg.PackHead, l.Queue().Length())
		} else {
			break
		}
	}
}

func (l *LogicService) changeEtcdConfig(cfg *config.Logic) {
	l4g.Infof("change etcd config:%+v", cfg)
	l.cfgFromEtcd = cfg
	l.etcdCacheM.CheckEtcdKeyChange(*cfg.GatewayCluster, l)
	l.etcdCacheM.CheckEtcdKeyChange(*cfg.Platform, l)

	l.userM.Cache().SetMaxEntries(int(cfg.Logic.MaxCacheEntries))
	l.redisActor.AddMessage(uint32(r2l.ID_MSG_L2R_ChangeConfig), l.NodeID(), &r2l.L2R_ChangeConfig{
		AccountSize:        cfg.Logic.AccountSize,
		IpAccounts:         cfg.Logic.IpAccounts,
		DeviceIdAccounts:   cfg.Logic.DeviceIdAccounts,
		IpAccountsHw:       cfg.Logic.IpAccountsHw,
		DeviceIdAccountsHw: cfg.Logic.DeviceIdAccountsHw,
		BanRegister:        cfg.Logic.BanRegister,
		Status:             int32(cfg.Status),
	})
	l.httpM.UpdateEtcdConfig(uint32(p2l.ID_MSG_L2P_ChangeEtcdConfig), l.NodeID(), &p2l.L2P_ChangeEtcdConfig{
		EtcdConfig: cfg, // cfg 是新 new 出来的，不需要 clone
	})
}

var doOneTickerEvent = func(evt event.Eventer, param interface{}) {
	e := evt.(ae.CommonEvent)
	srv := param.(*LogicService)
	srv.UserM().Cache().Evict(srv, int64(e.Value), 10)
}

func (l *LogicService) CrossSessionChange(id uint64, netAddr uint64, t session.SessionChangeType) {
	actIDs := l.CrossM().GetActivityByNetAddr(netAddr)
	l4g.Info("CrossSessionChange net:%d ids:%+v", netAddr, actIDs)
	for _, aid := range actIDs {
		logicActID := GetLogicActIDByCrossActID(aid)
		if logicActID >= 0 {
			logicAct := l.GetActivity(logicActID)
			if m, ok := logicAct.(activity.CrossActivity); ok {
				if t == session.SessionClosed {
					m.CrossClosed(l)
				} else if t == session.SessionConnected {
					l4g.Info("CrossSessionChange by NewCrossActAddOnConnectedNode actID:%d", logicActID)
					m.CrossConnected(l)
				}
			}
		}
	}
}

// 在已经连接的节点上触发了
func (l *LogicService) NewCrossActAddOnConnectedNode(aid uint32) {
	logicActID := GetLogicActIDByCrossActID(aid)
	if logicActID >= 0 {
		logicAct := l.GetActivity(logicActID)
		if m, ok := logicAct.(activity.CrossActivity); ok {
			l4g.Info("CrossSessionChange by NewCrossActAddOnConnectedNode actID:%d", logicActID)
			m.CrossConnected(l)
		}
	}

}

func (l *LogicService) NewCrossActAddOnUnconnectedNode(aid uint32) {
	logicActID := GetLogicActIDByCrossActID(aid)
	if logicActID >= 0 {
		logicAct := l.GetActivity(logicActID)
		if m, ok := logicAct.(activity.CrossActivity); ok {
			l4g.Info("CrossSessionChange by NewCrossActAddOnUnconnectedNode actID:%d", logicActID)
			m.CrossClosed(l)
		}
	}

}

func (l *LogicService) CrossMasterSessionChange(id uint64, netAddr uint64, t csession.SessionChangeType) {
	l4g.Info("CrossMasterSessionChange id:%d, addr:%d, t:%d", t, netAddr, t)
	if t == csession.SessionConnected {
		cmd := l2cm.ID_MSG_L2CM_GetServerPartition
		msg := &l2cm.L2CM_GetServerPartition{
			Sid: l.ServerID(),
		}
		l.SendCmdToCrossMaster(cmd, 0, msg)
	}
	/*
		actIDs := l.CrossM().GetActivityByNetAddr(netAddr)
		for _, id := range actIDs {
			logicActID := GetLogicActIDByCrossActID(id)
			if logicActID >= 0 {
				logicAct := l.GetActivity(logicActID)
				if m, ok := logicAct.(activity.CrossActivity); ok {
					if t == session.SessionClosed {
						m.CrossClosed(l)
					} else if t == session.SessionConnected {
						m.CrossConnected(l)
					}
				}
			}
		}
	*/
}

func GetLogicActIDByCrossActID(id uint32) activity.ID {
	switch id {
	case uint32(l2c.ACTIVITYID_WRESTLE):
		return activity.Wrestle
	case uint32(l2c.ACTIVITYID_GUILD):
		return activity.Guild
	case uint32(l2c.ACTIVITYID_WORLDBOSS):
		return activity.WorldBoss
	case uint32(l2c.ACTIVITYID_PEAK):
		return activity.Peak
	case uint32(l2c.ACTIVITYID_GST):
		return activity.GST
	case uint32(l2c.ACTIVITYID_SEASON_ARENA):
		return activity.SeasonArena
	case uint32(l2c.ACTIVITYID_HOT_RANK):
		return activity.HotRank
	case uint32(l2c.ACTIVITYID_SEASON_COMPLIANCE):
		return activity.SeasonCompliance
	case uint32(l2c.ACTIVITYID_SEASON_MAP):
		return activity.SeasonMap
	default:
		return activity.ID(-1)
	}
}

func (l *LogicService) SendCmdToOSS(cmd uint32, uid uint64, msg interface{}, funcID common.FUNCID) {
	addSuccess := l.ossM.AddMessage(cmd, uid, msg, funcID)
	if !addSuccess {
		l4g.Errorf("%d发送玩法%d消息到oss错误", uid, funcID)
		//l.SendCmdToPlatform(cmd, uid, msg)
	}
}

// IsAhead
// @Description: 是否先行服
// @receiver l
// @return bool
func (l *LogicService) IsAhead() bool {
	return l.cfgFromEtcd.IsAhead == 1
}

func (l *LogicService) SendCmdToCrossActivityPartition(
	actID uint32, targetPartition uint32, protoID uint32, msg interface{}) bool {
	pbMsg, ok := msg.(proto.Message)
	if !ok {
		l4g.Errorf("SendCmdToCrossActivityPartition:  not proto message %+v", msg)
		return false
	}
	blob, err := proto.Marshal(pbMsg)
	if err != nil {
		l4g.Errorf("SendCmdToCrossActivityPartition: err:%v", err)
		return false
	}
	l4g.Debugf("SendCmdToCrossActivityPartition to actID %d targetPartition %d protoID %d data %+v",
		actID, targetPartition, protoID, msg)
	return l.SendCmdToCross(l2c.ID_MSG_L2C_RemoteCrossActivityPartition, 0, &l2c.L2C_RemoteCrossActivityPartition{
		ActId:           actID,
		TargetPartition: targetPartition,
		ProtoId:         protoID,
		Data:            blob,
	})
}
