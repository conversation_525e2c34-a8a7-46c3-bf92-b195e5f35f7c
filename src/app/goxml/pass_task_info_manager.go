package goxml

import (
	"errors"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type PassTaskInfoManager struct {
	xmlData      *XmlData
	Datas        map[uint32]*PassTaskInfo
	allEvent     map[uint32]struct{}
	event        map[uint32]map[uint32]struct{}        //TypeID 战令ID
	pass         map[uint32]map[uint32][]*PassTaskInfo //战令ID TypeID
	taskNum      map[uint32]uint32                     // key: 战令ID value: num
	passIDEvents map[uint32]uint32                     // passID=>eventType
	passIDTypeID map[uint32]uint32                     // passID=>typeID
	passIDDatas  map[uint32][]*PassTaskInfo            //战令ID=>所有任务
}

func newPassTaskInfoManager(xmlData *XmlData) *PassTaskInfoManager {
	m := &PassTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *PassTaskInfoManager) name() string {
	return "PassTaskInfo"
}

func (m *PassTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *PassTaskInfoManager) checkData() error {
	for passID := range m.xmlData.PassCycleTaskInfoM.tasks {
		event, exist := m.passIDTypeID[passID]
		if !exist || event == 0 {
			return errors.New(fmt.Sprintf("check data error %s pass id:%d task event is nil", m.name(), passID))
		}

		/* 加分方式更改检查放开
		eventPassID, exist := TypeIDs[event]
		if !exist {
			TypeIDs[event] = passID
		} else {
			return errors.New(fmt.Sprintf("check data error %s pass id:%d and pass id:%d TypeID:%d is repeated",
				m.name(), passID, eventPassID, event))
		}*/

	}
	return nil
}

func (m *PassTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &PassTaskInfos{}
	fileName := filepath.Join(dir, "pass_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*PassTaskInfo, len(tmp.Datas))
	m.event = make(map[uint32]map[uint32]struct{})
	m.allEvent = make(map[uint32]struct{})
	m.pass = make(map[uint32]map[uint32][]*PassTaskInfo)
	m.taskNum = make(map[uint32]uint32)
	m.passIDEvents = make(map[uint32]uint32)
	m.passIDTypeID = make(map[uint32]uint32)
	m.passIDDatas = make(map[uint32][]*PassTaskInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		/*if data.Value == 0 {
			panic(fmt.Sprintf("check data error %s %d. value is zero", fileName, data.Id))
		}*/

		taskType := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskType == nil {
			panic(fmt.Sprintf("check data error %s %d. taskType not exist", fileName, data.Id))
		}
		m.allEvent[taskType.Type] = struct{}{}

		pass := m.xmlData.PassInfoM.Index(data.PassId)
		if pass == nil {
			panic(fmt.Sprintf("check data error %s %d. passinfo not exist", fileName, data.Id))
		}

		if pass.PassType == PassTypeSeason {
			if len(data.AwardClRes) == 0 || len(data.Award2ClRes) == 0 {
				panic(fmt.Sprintf("Task info id:%d must have charge awrad or chrage2 awrd", data.Id))
			}
		} else {
			if len(data.FreeClRes) == 0 || len(data.AwardClRes) == 0 {
				panic(fmt.Sprintf("Task info id:%d must have free awrad or chrage awrd", data.Id))
			}
		}

		if _, exist := m.event[taskType.Type]; !exist {
			m.event[taskType.Type] = make(map[uint32]struct{})
		}
		m.event[taskType.Type][data.PassId] = struct{}{}

		if _, exist := m.pass[data.PassId]; !exist {
			m.pass[data.PassId] = make(map[uint32][]*PassTaskInfo)
		}
		if _, exist := m.pass[data.PassId][data.TypeId]; exist {
			PassTaskS := m.pass[data.PassId][data.TypeId]
			if data.Value < PassTaskS[len(PassTaskS)-1].Value {
				panic(fmt.Sprintf("check data id:%d error.passinfo same PassId:%d and same task type:%d progress must be increase",
					data.Id, data.PassId, data.TypeId))
			}
		}
		m.pass[data.PassId][data.TypeId] = append(m.pass[data.PassId][data.TypeId], data)
		m.taskNum[data.PassId]++

		//一个战令id仅对应一个任务类型
		if t, exist := m.passIDEvents[data.PassId]; exist {
			if taskType.Type != t {
				panic(fmt.Sprintf("check data error %s %d. passIDEvents illegal", fileName, data.Id))
			}
		} else {
			m.passIDEvents[data.PassId] = taskType.Type
		}

		if t, exist := m.passIDTypeID[data.PassId]; exist {
			if data.TypeId != t {
				panic(fmt.Sprintf("check data error %s %d. passIDTypeID illegal", fileName, data.Id))
			}
		} else {
			m.passIDTypeID[data.PassId] = data.TypeId
		}

		m.passIDDatas[data.PassId] = append(m.passIDDatas[data.PassId], data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *PassTaskInfoManager) GetRecordById(id uint32) *PassTaskInfo {
	return m.Datas[id]
}

func (m *PassTaskInfoManager) Index(key uint32) *PassTaskInfo {
	return m.Datas[key]
}

func (m *PassTaskInfoManager) PassEvents() map[uint32]struct{} {
	return m.allEvent
}

func (m *PassTaskInfoManager) GetPassIDsByEvent(id uint32) []uint32 {
	if m.event[id] == nil {
		return nil
	}

	ids := make([]uint32, 0, len(m.event[id]))
	for passId := range m.event[id] {
		ids = append(ids, passId)
	}

	return ids
}

func (m *PassTaskInfoManager) GetTaskInfos(passID, eType uint32) []*PassTaskInfo {
	if m.pass[passID] == nil {
		return nil
	}

	return m.pass[passID][eType]
}

func (m *PassTaskInfoManager) GetTaskNum(passID uint32) uint32 {
	return m.taskNum[passID]
}

func (m *PassTaskInfoManager) GetEventsByPassID(passID uint32) uint32 {
	return m.passIDEvents[passID]
}

func (m *PassTaskInfoManager) GetTaskDatasByPassID(passID uint32) []*PassTaskInfo {
	return m.passIDDatas[passID]
}

func (m *PassTaskInfoManager) GetPassEventTypeID(passID uint32) (uint32, bool) {
	typeID, exist := m.passIDTypeID[passID]
	return typeID, exist
}
