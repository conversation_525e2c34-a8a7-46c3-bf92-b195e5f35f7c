package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ArtifactDebutConfigInfoManager struct {
	xmlData *XmlData
	Datas   map[string]*ArtifactDebutConfigInfo

	seniorDrawAddPoints uint32         //高级抽每抽积分
	juniorDrawAddPoints uint32         //初级抽每抽积分
	puzzleCost          []*cl.Resource //拼图游戏每次消耗
	pointExchange       uint32         //满XX分送一把神器
	coinExchange        uint32         //活动代币与金币的换算比，活动结束将其折算成金币返还给玩家
	rewardDay           uint32         //领奖天数（活动结束后x天）
	rewardSecs          int64          //领奖天数（活动结束后x秒）
	mailDay             uint32         //奖励补发时间节点x天（活动结束x天后，不再处理补发逻辑）
	multipleDraw        uint32         //连抽对应的次数
	protectDay          uint32         //新服屏蔽天数
	regularGuarantee    uint32         //常规保底次数
	newServerIsOpen     uint32         //是否开启新手活动 不同地区规则不一样 0 不开启 1 开启
}

func newArtifactDebutConfigInfoManager(xmlData *XmlData) *ArtifactDebutConfigInfoManager {
	m := &ArtifactDebutConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArtifactDebutConfigInfoManager) name() string {
	return "ArtifactDebutConfigInfo"
}

func (m *ArtifactDebutConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArtifactDebutConfigInfoManager) checkData() error {
	return nil
}

func (m *ArtifactDebutConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &ArtifactDebutConfigInfos{}
	fileName := filepath.Join(dir, "artifact_debut_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	l4g.Debugf("ArtifactDebutConfigInfoM. data: %+v", tmp.Datas)
	m.Datas = make(map[string]*ArtifactDebutConfigInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Key == "PUZZLE_COST" && data.Type == 0 {
			panic(fmt.Sprintf("load %s fail: PUZZLE_COST err, type is 0. %v", fileName, data))
		}
		m.Datas[data.Key] = data
	}

	m.seniorDrawAddPoints = m.Datas["SENIOR_DRAW_ADD_POINTS"].Count
	m.juniorDrawAddPoints = m.Datas["JUNIOR_DRAW_ADD_POINTS"].Count
	m.puzzleCost = m.Datas["PUZZLE_COST"].ClRes
	m.pointExchange = m.Datas["POINT_EXCHANGE"].Count
	m.coinExchange = m.Datas["COIN_EXCHANGE"].Count
	m.rewardDay = m.Datas["REWARD_TIME"].Count
	m.rewardSecs = int64(m.rewardDay) * util.DaySecs
	m.mailDay = m.Datas["MAIL_TIME"].Count
	m.multipleDraw = m.Datas["MULTIPLE_DRAW"].Count
	m.protectDay = m.Datas["PROTECT_DAY"].Count
	m.regularGuarantee = m.Datas["REGULAR_GUARANTEE"].Count
	m.newServerIsOpen = m.Datas["NEW_SERVER_IS_OPEN"].Count

	//高级抽所加积分要大于初级抽
	if m.seniorDrawAddPoints < m.juniorDrawAddPoints {
		panic(fmt.Sprintf("load %s fail: seniorDrawAddPoints < juniorDrawAddPoints", fileName))
	}

	//连抽一次所得积分，必须少于单次积分兑换所需积分
	if m.seniorDrawAddPoints*m.multipleDraw >= m.pointExchange {
		panic(fmt.Sprintf("load %s fail: multipleDraw add points >= pointExchange need points", fileName))
	}

	//保底次数必须大于单次连抽次数
	if m.regularGuarantee <= m.multipleDraw {
		panic(fmt.Sprintf("load %s fail: regularGuarantee <= multipleDraw", fileName))
	}
	return nil
}

func (m *ArtifactDebutConfigInfoManager) Index(key string) *ArtifactDebutConfigInfo {
	return m.Datas[key]
}

func (m *ArtifactDebutConfigInfoManager) GetSeniorDrawAddPoints() uint32 {
	return m.seniorDrawAddPoints
}

func (m *ArtifactDebutConfigInfoManager) GetJuniorDrawAddPoints() uint32 {
	return m.juniorDrawAddPoints
}

func (m *ArtifactDebutConfigInfoManager) GetDrawAddPoints(category, count uint32) uint32 {
	if category == uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR) {
		return m.seniorDrawAddPoints * count
	}
	return m.juniorDrawAddPoints * count
}

func (m *ArtifactDebutConfigInfoManager) MakeDrawAddPointsRes(category, count uint32) []*cl.Resource {
	return []*cl.Resource{
		{
			Type:  uint32(common.RESOURCE_TOKEN),
			Value: uint32(common.TOKEN_TYPE_ARTIFACT_DEBUT_DRAW_POINTS),
			Count: m.GetDrawAddPoints(category, count),
		},
	}
}

func (m *ArtifactDebutConfigInfoManager) GetOpenPuzzleCost(count uint32) []*cl.Resource {
	cost := make([]*cl.Resource, 0, len(m.puzzleCost))
	for _, v := range m.puzzleCost {
		cost = append(cost, &cl.Resource{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count * count,
		})
	}
	return cost
}

func (m *ArtifactDebutConfigInfoManager) GetPointExchange() uint32 {
	return m.pointExchange
}

func (m *ArtifactDebutConfigInfoManager) GetExchangeCost(points uint32) (uint32, []*cl.Resource) {
	count := points / m.pointExchange
	if count == 0 {
		return 0, nil
	}

	return count, []*cl.Resource{
		{
			Type:  uint32(common.RESOURCE_TOKEN),
			Value: uint32(common.TOKEN_TYPE_ARTIFACT_DEBUT_DRAW_POINTS),
			Count: m.pointExchange * count,
		},
	}
}

func (m *ArtifactDebutConfigInfoManager) GetCoinExchange() uint32 {
	return m.coinExchange
}

// 新服活动保护天数，再此之后通服活动才可生效
func (m *ArtifactDebutConfigInfoManager) GetProtectDay() uint32 {
	return m.protectDay
}

func (m *ArtifactDebutConfigInfoManager) GetRewardDay() uint32 {
	return m.rewardDay
}

func (m *ArtifactDebutConfigInfoManager) GetRewardSeconds() int64 {
	return m.rewardSecs
}

func (m *ArtifactDebutConfigInfoManager) GetMultipleDraw() uint32 {
	return m.multipleDraw
}

func (m *ArtifactDebutConfigInfoManager) GetMailDay() uint32 {
	return m.mailDay
}

func (m *ArtifactDebutConfigInfoManager) NewServerIsOpen() bool {
	return m.newServerIsOpen == uint32(1)
}

func (m *ArtifactDebutConfigInfoManager) TransCoin2Gold(coin uint32) *cl.Resource {
	if coin == 0 {
		return nil
	}

	return &cl.Resource{
		Type:  uint32(common.RESOURCE_GOLD),
		Value: 0,
		Count: coin * m.coinExchange,
	}
}

func (m *ArtifactDebutConfigInfoManager) GetRegularGuarantee() uint32 {
	return m.regularGuarantee
}

// 是否触发常规保底
// @param uint32 count 当前保底计数
// @return bool 是否触发保底
func (m *ArtifactDebutConfigInfoManager) IsHitGuarantee(count uint32) bool {
	return (count + 1) == m.regularGuarantee
}

func (m *ArtifactDebutConfigInfoManager) LeftGuarantee(count uint32) uint32 {
	if m.regularGuarantee > count {
		return m.regularGuarantee - count
	}
	return 0
}
