package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SeasonDoorInfoManager struct {
	xmlData      *XmlData
	Datas        map[uint32]*SeasonDoorInfoExt
	DoorNum2Type map[uint32]uint32
}

type SeasonDoorInfoExt struct {
	Id            uint32
	DoorNum       uint32
	Formation     uint32
	DoorType      uint32
	Level         uint32
	Rest          uint32
	FailRest      uint32
	MonsterGroups []uint32
	DropGroups    []uint32
	WaveNum       uint32
	OilDropGroup  map[uint32]uint32
}

func newSeasonDoorInfoManager(xmlData *XmlData) *SeasonDoorInfoManager {
	m := &SeasonDoorInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SeasonDoorInfoManager) name() string {
	return "season_door_info.xml"
}

func (m *SeasonDoorInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SeasonDoorInfoManager) checkData() error {
	return nil
}

func (m *SeasonDoorInfoManager) load(dir string, show bool) error {
	tmp := &SeasonDoorInfos{}
	fileName := filepath.Join(dir, "season_door_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*SeasonDoorInfoExt, len(tmp.Datas))
	m.DoorNum2Type = make(map[uint32]uint32)

	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &SeasonDoorInfoExt{
			Id:           data.Id,
			DoorNum:      data.DoorNum,
			Formation:    data.Formation,
			DoorType:     data.DoorType,
			Level:        data.Level,
			Rest:         data.Rest,
			FailRest:     data.FailRest,
			WaveNum:      data.Wave,
			OilDropGroup: make(map[uint32]uint32),
		}
		m.Datas[data.Id] = dataExt
		if data.MonsterGroup1 > 0 {
			monsterGroupInfo := m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup1)
			if monsterGroupInfo == nil {
				panic(fmt.Sprintf("load config %s fail: MonsterGroup1:%d is nil. id:%d", fileName, data.MonsterGroup1, data.Id))
			}
			dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup1)
		}
		if data.MonsterGroup2 > 0 {
			monsterGroupInfo := m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup2)
			if monsterGroupInfo == nil {
				panic(fmt.Sprintf("load config %s fail: MonsterGroup2:%d is nil. id:%d", fileName, data.MonsterGroup2, data.Id))
			}
			dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup2)
		}
		if data.MonsterGroup3 > 0 {
			monsterGroupInfo := m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup3)
			if monsterGroupInfo == nil {
				panic(fmt.Sprintf("load config %s fail: MonsterGroup3:%d is nil. id:%d", fileName, data.MonsterGroup3, data.Id))
			}
			dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup3)
		}
		if data.MonsterGroup4 > 0 {
			monsterGroupInfo := m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup4)
			if monsterGroupInfo == nil {
				panic(fmt.Sprintf("load config %s fail: MonsterGroup4:%d is nil. id:%d", fileName, data.MonsterGroup4, data.Id))
			}
			dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup4)
		}
		if data.MonsterGroup5 > 0 {
			monsterGroupInfo := m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup5)
			if monsterGroupInfo == nil {
				panic(fmt.Sprintf("load config %s fail: MonsterGroup5:%d is nil. id:%d", fileName, data.MonsterGroup5, data.Id))
			}
			dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup5)
		}
		if data.DropGroup1 > 0 {
			if len(m.xmlData.DropGroupInfoM.Group(data.DropGroup1)) == 0 {
				panic(fmt.Sprintf("load config %s fail: DropGroup1:%d is nil. id:%d", fileName, data.DropGroup1, data.Id))
			}
			dataExt.DropGroups = append(dataExt.DropGroups, data.DropGroup1)
		}
		if data.DropGroup2 > 0 {
			if len(m.xmlData.DropGroupInfoM.Group(data.DropGroup2)) == 0 {
				panic(fmt.Sprintf("load config %s fail: DropGroup2:%d is nil. id:%d", fileName, data.DropGroup2, data.Id))
			}
			dataExt.DropGroups = append(dataExt.DropGroups, data.DropGroup2)
		}
		if data.DropGroup3 > 0 {
			if len(m.xmlData.DropGroupInfoM.Group(data.DropGroup3)) == 0 {
				panic(fmt.Sprintf("load config %s fail: DropGroup3:%d is nil. id:%d", fileName, data.DropGroup3, data.Id))
			}
			dataExt.DropGroups = append(dataExt.DropGroups, data.DropGroup3)
		}
		if data.DropGroup4 > 0 {
			if len(m.xmlData.DropGroupInfoM.Group(data.DropGroup4)) == 0 {
				panic(fmt.Sprintf("load config %s fail: DropGroup4:%d is nil. id:%d", fileName, data.DropGroup4, data.Id))
			}
			dataExt.DropGroups = append(dataExt.DropGroups, data.DropGroup4)
		}
		if data.DropGroup5 > 0 {
			if len(m.xmlData.DropGroupInfoM.Group(data.DropGroup5)) == 0 {
				panic(fmt.Sprintf("load config %s fail: DropGroup5:%d is nil. id:%d", fileName, data.DropGroup5, data.Id))
			}
			dataExt.DropGroups = append(dataExt.DropGroups, data.DropGroup5)
		}
		if len(dataExt.MonsterGroups) != 5 || len(dataExt.DropGroups) != 5 {
			panic(fmt.Sprintf("load config %s fail: MonsterGroups or DropGroups is not 5. id:%d", fileName, data.Id))
		}
		m.DoorNum2Type[data.DoorNum] = data.DoorType

		if data.OilId1 > 0 && data.OilDropGroup1 > 0 {
			dataExt.OilDropGroup[data.OilId1] = data.OilDropGroup1
		}
		if data.OilId2 > 0 && data.OilDropGroup2 > 0 {
			dataExt.OilDropGroup[data.OilId2] = data.OilDropGroup2
		}
		if data.OilId3 > 0 && data.OilDropGroup3 > 0 {
			dataExt.OilDropGroup[data.OilId3] = data.OilDropGroup3
		}
		if data.OilId4 > 0 && data.OilDropGroup4 > 0 {
			dataExt.OilDropGroup[data.OilId4] = data.OilDropGroup4
		}
		if data.OilId5 > 0 && data.OilDropGroup5 > 0 {
			dataExt.OilDropGroup[data.OilId5] = data.OilDropGroup5
		}
		if data.OilId6 > 0 && data.OilDropGroup6 > 0 {
			dataExt.OilDropGroup[data.OilId6] = data.OilDropGroup6
		}
	}
	return nil
}

func (m *SeasonDoorInfoManager) Index(key uint32) *SeasonDoorInfoExt {
	return m.Datas[key]
}

func (m *SeasonDoorInfoManager) GetDoorType(doorNum uint32) uint32 {
	return m.DoorNum2Type[doorNum]
}

func (m *SeasonDoorInfoManager) GetOilDropGroup(doorId, oilId uint32) uint32 {
	doorInfo, exist := m.Datas[doorId]
	if !exist || doorInfo == nil {
		return 0
	}
	return doorInfo.OilDropGroup[oilId]
}
