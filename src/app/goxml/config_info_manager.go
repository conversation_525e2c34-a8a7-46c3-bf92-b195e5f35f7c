package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

type ConfigInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]uint32

	FriendMaxNum              uint32 //好友数量
	FriendBlackMaxNum         uint32 //黑名单数量
	FriendRecvMaxCount        uint32 //好友点赞领取数
	FriendSendMaxCount        uint32 //好友点赞数
	RecommendFriendActiveDay  uint32 //推荐好友活跃玩家天数
	RecommendFriendRangeLevel uint32 //推荐好友等级范围
	FriendRecommendMinLevel   uint32 //好友推荐的最小等级
	FriendRecommendMaxLevel   uint32 //好友推荐的最大等级
	FriendRecommendNum        uint32 //好友推荐的数量
	FriendReceivePerGold      uint32 //好友领取友情点给的黄金
	FriendReceivePerPoint     uint32 //好友另起友情点给的友情点
	HeroSlotMinNum            uint32 //英雄栏位初始值
	HeroSlotPrice             uint32 //购买英雄栏位的价格 diamond
	HeroSlotAddNum            uint32 //每次购买，增加的栏位数量
	HeroSlotMaxNum            uint32 //英雄栏位最大值
	DungeonMaxTurn            uint32 //战役最大回合数
	HeroDecomposeMaxNum       uint32 //英雄分解最大数量
	ItemUseMaxNum             uint32 //道具单次使用上限
	ItemSellMaxNum            uint32 //道具单次出售上限
	GoodsBuyMaxNum            uint32 //商品单次购买上限
	// ArenaRefreshCD            uint32   //基础竞技场刷新对手CD（秒）
	// ArenaMaxDailyLikeNum      uint32   //基础竞技场每日最大点赞数量上限
	BotLvAddNum uint32 //基础竞技场机器人等级范围上限加成数（level + x）
	BotLvSubNum uint32 //基础竞技场机器人等级范围下限加成数 (level - x)
	// ArenaScoreDiffLimit       uint32   //基础竞技场分数差值上限
	// ArenaShowMinRank          uint32   //基础竞技场排行榜，展示名次的最小值
	UserNameMaxLength                    int      //用户名称的最大长度
	UserNameSetCost                      uint32   //修改用户昵称的固定消耗钻石数
	GemBagMaxLimitNum                    uint32   // 宝石背包最大上限
	EmblemBagMaxLimitNum                 uint32   // 纹章背包最大上限
	EquipBagMaxLimitNum                  uint32   // 装备背包最大上限
	DispatchInitTask                     []uint32 //玩家初始悬赏任务
	DispatchNewTaskNum                   uint32   // 悬赏任务每次刷新的任务条数
	DispatchSpeedRemainTime              uint32   // 悬赏任务剩余多少秒时加速免费
	DispatchForceRedCounts               uint32   // 悬赏任务保底刷出红色任务的累积次数
	ChatFrequencyTime                    int64    //聊天间隔时间
	HeroDisableOneKeyLvUpLv              uint32   //英雄到了这个等级关闭一键升10级
	HeroOneKeyLvUpNum                    uint32   //英雄一键升级最大10级
	BotFirstNameMax                      int      //机器人名字首位最大随机数
	BotSecondNameMax                     int      //机器人名字第二位最大随机数
	HandbookHeroAward                    uint32   //历史首次获得五星英雄可获得的图鉴奖励
	FriendRequestMaxNum                  uint32   //好友申请列表上限
	OnhookMaxTime                        uint32   //初始挂机最大时长
	WishListCD                           uint32   //心愿单重置周期(天)
	EmblemDeComposeRareLimit             uint32   // 符文分解稀有度限制
	WishlistResetValue                   uint32   //心愿单重置时保底计算参数
	OverAllRatingMultiple                uint32   // 全局评分属性加成
	WishListCDForNew                     uint32   //心愿单新手重置天数
	WishlistResetTimes                   uint32   //心愿单新手重置次数
	MaxGiftsSendNum                      uint32   // 最大赠送上限
	GoddessContractLevelUpItem           uint32   // 契约之所升级物品
	EnchantLimitRare                     uint32   // 装备附魔装备等级限制
	HeroSlotBuyGroup                     uint32   // 英雄背包购买组ID
	Link3UnlockHeroStar                  uint32   // 宝石羁绊激活的星级条件
	GoddessTouchLimit                    uint32   // 女武神触摸上限
	Link1UnlockHeroStar                  uint32   //英雄羁绊1的星级条件
	Link2UnlockHeroStar                  uint32   //英雄羁绊2的星级条件
	Link4UnlockHeroStar                  uint32   //纹章羁绊激活的星级条件
	artifactPointsExchangeCostNum        uint32   // 神器积分兑换消耗的字眼数量
	OssOpenLv                            uint32   //战报服务的开启等级
	heroTranslationReqCount              uint32   //英雄转换所需溢出英雄数量
	artifactGuaranteeID                  uint32   // 神器保底量表ID
	godPresentReduceHeroRare             uint32   // 神之馈赠抽卡降低的英雄品质
	EmblemSlotBuyGroup                   uint32   //符文背包栏位购买组id
	EmblemSlotAddNum                     uint32   //符文背包栏位每次增加个数
	ActivityTicketIncreaseDaily          uint32   //活动故事入场券每日免费数量
	ActivityTicketIncreaseLimit          uint32   //活动故事免费持有上限
	ActivityStorySheildDays              uint32   // 活动故事关闭不足天数
	AssistanceActivityNameNum            uint32   // 助力活动玩家名字个数
	FragmentComposeMaxUnitNum            uint32   // 碎片一键合成最大合成种类数量
	SeasonOpenServerDayLimit             uint32   // 解锁赛季的开服天数
	MirageBuyCountCostItemID             uint32   // 个人 boss - 道具value购买次数
	AwakenUniversalItem                  uint32   // 觉醒万能材料ID
	Link5UnlockHeroStar                  uint32   // 觉醒英雄解锁星级
	Link5Id                              uint32   // 觉醒英雄羁绊效果
	Link5RaisePS                         uint64   // 觉醒英雄羁绊技能ID
	SeasonReturnReceive                  uint32   // 赛季回流领奖解锁赛季主线ID
	SkinDecomposeDiamondLimit            uint32   //皮肤分解 - 钻石上限值
	SkinDecomposeGoldLimit               uint32   //皮肤分解 - 金币上限值
	AssistantShopChooseGoodsLimit        uint32   // 小助手-商店勾选上限
	Rite10RecyclePoint                   uint32   // 白色印记回收分数
	Rite20RecyclePoint                   uint32   // 绿色印记回收分数
	Rite30RecyclePoint                   uint32   // 蓝色印记回收分数
	Rite40RecyclePoint                   uint32   // 紫色印记回收分数
	Rite50RecyclePoint                   uint32   // 橙色印记回收分数
	Rite60RecyclePoint                   uint32   // 红色印记回收分数
	RitePowerRecyclePoint                uint32   // 威能回收分数
	SeasonLink10RareRecyclePoint         uint32   // 白色符石品质回收分数
	SeasonLink20RareRecyclePoint         uint32   // 绿色符石品质回收分数
	SeasonLink30RareRecyclePoint         uint32   // 蓝色符石品质回收分数
	SeasonLink40RareRecyclePoint         uint32   // 紫色符石品质回收分数
	SeasonLink50RareRecyclePoint         uint32   // 橙色符石品质回收分数
	SeasonLink60RareRecyclePoint         uint32   // 红色符石品质回收分数
	SeasonLink70RareRecyclePoint         uint32   // 粉色符石品质回收分数
	SeasonLink30LvRecyclePoint           uint32   // 蓝色符石等级回收分数
	SeasonLink40LvRecyclePoint           uint32   // 紫色符石等级回收分数
	SeasonLink50LvRecyclePoint           uint32   // 橙色符石等级回收分数
	SeasonLink60LvRecyclePoint           uint32   // 红色符石等级回收分数
	S1SeasonOpenSpecialTm                int64    // S1赛季开启的特殊判断时间
	NewYearActivitySheildDays            uint32   // 新年活动
	GstShieldTime                        int64    // 公会战屏蔽时间
	HeroConvertOpen                      uint32   // 溢出英雄转换开启要求
	HeroConvertNum                       uint32   // 溢出英雄转换数量
	TurnTableOpenBuyTicketDay            uint32   // 周年庆活动门票购买时间
	TurnTableRandomBuffNum               uint32   // 周年庆活动随机BUFF数量
	HotRankRefreshTime                   int64    // 热度榜刷新周期（秒）
	ComplianceOpenData                   uint32   // 冲榜活动开启开服天数时间
	ComplianceDuration                   uint32   // 冲榜活动周期
	ComplianceOpenServer                 int64    // 开服天数屏蔽
	ActivityRechargeDailyWish            uint32
	ActivityComplianceLikeDiamond        uint32
	TowerstarDynamicFormationOpenChapter uint32  // 条件爬塔动态阵容开启章节
	BlessedHeroPowerRate                 float64 // 赐福英雄战力折算万分比
	SocietyMailDiamondAward              uint32
	SocietyMailPushLevel                 uint32
	DuelOperateInterval                  uint32       // 切磋cd（分钟）
	ActivityTowerDuration                uint32       // 地宫冲榜 - 活动周期
	ActivityTowerRankRemain              uint32       // 地宫冲榜 - 展示期
	ActivityTowerLikeDiamond             uint32       // 地宫冲榜 - 点赞奖励
	ActivityMirageDuration               uint32       // 幻境冲榜 - 活动周期
	ActivityMirageRankRemain             uint32       // 幻境冲榜 - 展示期
	ActivityMirageLikeDiamond            uint32       // 幻境冲榜 - 点赞奖励
	ActivityTowerSeasonRankOpen          uint32       // 百塔冲榜 - 第几天开启
	ActivityTowerSeasonRankEnd           uint32       // 百塔冲榜 - 第几天结束
	ActivityTowerSeasonRankAwardEnd      uint32       // 百塔冲榜 - 第几天领奖结束
	ActivityTowerSeasonRankLikeDiamond   *cl.Resource // 百塔冲榜 - 每日点赞钻石奖励
	ActivityDropServerDate               int64
	ActivityTowerSeason2RankOpen         uint32 // 百塔冲榜第二轮 - 第几天开启
	ActivityTowerSeason2RankEnd          uint32 // 百塔冲榜第二轮 - 第几天结束
	ActivityTowerSeason2RankAwardEnd     uint32 // 百塔冲榜第二轮 - 第几天领奖结束
	RechargeRefundTime                   int64
	RechargeRefundDungeon                uint32
	RechargeByCouponCost                 uint32 // 使用代金券进行充值的消耗系数
	HeroAwakenConvertResLimit            uint32
	MailTimeLimit                        int64
	MailNumLimit                         uint32
}

func newConfigInfoManager(xmlData *XmlData) *ConfigInfoManager {
	m := &ConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ConfigInfoManager) name() string {
	return "ConfigInfo"
}

func (m *ConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ConfigInfoManager) checkData() error {
	if m.SocietyMailPushLevel == 0 {
		return nil
	}
	dungeonInfo := m.xmlData.DungeonConfigInfoM.Index(m.SocietyMailPushLevel)
	if dungeonInfo == nil {
		panic(fmt.Sprintf("load config fileName:%s fail: SocietyMailPushLevel id is:%d error",
			m.name(), m.SocietyMailPushLevel))
	}
	return nil
}

func (m *ConfigInfoManager) Load(dir string, isShow bool) error {
	return m.load(dir, isShow)
}

func (m *ConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &ConfigInfos{}
	fileName := filepath.Join(dir, "config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]uint32, len(tmp.Datas))
	for _, data := range tmp.Datas {
		m.Datas[data.Id] = data.ConfigValue
	}
	m.DispatchInitTask = make([]uint32, 0, 3)
	m.FriendMaxNum = m.Datas[uint32(common.CONFIG_FRIEND_MAX_NUM)]
	m.FriendBlackMaxNum = m.Datas[uint32(common.CONFIG_FRIEND_BLACK_MAX_NUM)]
	m.FriendRecvMaxCount = m.Datas[uint32(common.CONFIG_FRIEND_RECV_MAX_COUNT)]
	m.FriendSendMaxCount = m.Datas[uint32(common.CONFIG_FRIEND_SEND_MAX_COUNT)]
	m.RecommendFriendActiveDay = m.Datas[uint32(common.CONFIG_FRIEND_RECOMMEND_ACTIVE_DAY)]
	m.RecommendFriendRangeLevel = m.Datas[uint32(common.CONFIG_FRIEND_RECOMMEND_RANGE_LEVEL)]
	m.FriendRecommendMinLevel = m.Datas[uint32(common.CONFIG_FRIEND_RECOMMEND_MIN_LEVEL)]
	m.FriendRecommendMaxLevel = m.Datas[uint32(common.CONFIG_FRIEND_RECOMMEND_MAX_LEVEL)]
	m.FriendRecommendNum = m.Datas[uint32(common.CONFIG_FRIEND_RECOMMEND_NUM)]
	m.FriendReceivePerGold = m.Datas[uint32(common.CONFIG_FRIEND_RECEIVE_PER_GOLD)]
	m.FriendReceivePerPoint = m.Datas[uint32(common.CONFIG_FRIEND_RECEIVE_PER_POINT)]
	m.HeroSlotMinNum = m.Datas[uint32(common.CONFIG_HERO_SLOT_MIN_NUM)]
	m.HeroSlotPrice = m.Datas[uint32(common.CONFIG_HERO_SLOT_ADD_COST)]
	m.HeroSlotAddNum = m.Datas[uint32(common.CONFIG_HERO_SLOT_ADD_NUM)]
	m.HeroSlotMaxNum = m.Datas[uint32(common.CONFIG_HERO_SLOT_MAX_NUM)]
	m.DungeonMaxTurn = m.Datas[uint32(common.CONFIG_DUNGEON_MAX_TURN)]
	m.HeroDecomposeMaxNum = m.Datas[uint32(common.CONFIG_HERO_DISBAND_MAX_NUM)]
	m.ItemUseMaxNum = m.Datas[uint32(common.CONFIG_ITEM_USE_MAX_NUM)]
	m.ItemSellMaxNum = m.Datas[uint32(common.CONFIG_ITEM_SELL_MAX_NUM)]
	m.GoodsBuyMaxNum = m.Datas[uint32(common.CONFIG_GOODS_BUY_MAX_NUM)]
	// m.ArenaRefreshCD = m.Datas[uint32(common.CONFIG_ARENA_REFRESH_CD)]
	// m.ArenaMaxDailyLikeNum = m.Datas[uint32(common.CONFIG_ARENA_MAX_DAILY_LIKE_NUM)]
	m.BotLvAddNum = m.Datas[uint32(common.CONFIG_ARENA_BOT_LV_ADD_NUM)]
	m.BotLvSubNum = m.Datas[uint32(common.CONFIG_ARENA_BOT_LV_SUB_NUM)]
	// m.ArenaScoreDiffLimit = m.Datas[uint32(common.CONFIG_ARENA_SCORE_DIFF_LIMIT)]
	// m.ArenaShowMinRank = m.Datas[uint32(common.CONFIG_ARENA_SHOW_MIN_RANK)]
	m.UserNameMaxLength = int(m.Datas[uint32(common.CONFIG_USER_NAME_MAX_LENGTH)])
	m.UserNameSetCost = m.Datas[uint32(common.CONFIG_USER_NAME_SET_COST)]
	m.GemBagMaxLimitNum = m.Datas[GemBagMaxLimitId]
	m.EmblemBagMaxLimitNum = m.Datas[uint32(common.EMBLEM_CONFIG_BAG_MAX_LIMIT_ID)]
	m.EquipBagMaxLimitNum = m.Datas[uint32(common.CONFIG_EQUIP_CONFIG_BAG_MAX_LIMIT_ID)]
	m.DispatchInitTask = append(m.DispatchInitTask, m.Datas[uint32(common.CONFIG_DISPATCH_INITIAL_TASK1)])
	m.DispatchInitTask = append(m.DispatchInitTask, m.Datas[uint32(common.CONFIG_DISPATCH_INITIAL_TASK2)])
	m.DispatchInitTask = append(m.DispatchInitTask, m.Datas[uint32(common.CONFIG_DISPATCH_INITIAL_TASK3)])
	m.DispatchNewTaskNum = m.Datas[uint32(common.CONFIG_DISPATCH_NEW_TASK_NUM)]
	m.DispatchSpeedRemainTime = m.Datas[uint32(common.CONFIG_DISPATCH_SPEED_REMAIN_TIME)]
	m.DispatchForceRedCounts = m.Datas[uint32(common.CONFIG_DISPATCH_FORCE_RED_COUNTS)]
	m.ChatFrequencyTime = int64(m.Datas[uint32(common.CONFIG_CHAT_FREQUENCY_TIME)] * 1000)
	m.HeroDisableOneKeyLvUpLv = m.Datas[uint32(common.CONFIG_HERO_LEVEL_ONEKEY_OFF)]
	m.HeroOneKeyLvUpNum = m.Datas[uint32(common.CONFIG_HERO_LEVEL_ONEKEY_NUM)]
	m.BotFirstNameMax = int(m.Datas[uint32(common.CONFIG_BOT_NAME_A_MAX)])
	m.BotSecondNameMax = int(m.Datas[uint32(common.CONFIG_BOT_NAME_B_MAX)])
	m.HandbookHeroAward = m.Datas[uint32(common.CONFIG_HANDBOOK_HERO_AWARD)]
	m.FriendRequestMaxNum = m.Datas[uint32(common.CONFIG_FRIEND_REQUEST_MAX_NUM)]
	m.OnhookMaxTime = m.Datas[uint32(common.CONFIG_ONHOOK_MAX_TIME)]
	m.WishListCD = m.Datas[uint32(common.CONFIG_WISHLIST_CD)]
	if m.WishListCD == 0 {
		panic(fmt.Sprintf("load config %s fail: wishListCD config err. cd:%d",
			fileName, m.WishListCD))
	}
	if m.BotFirstNameMax < 1 || m.BotSecondNameMax < 1 {
		panic(fmt.Sprintf("load config %s fail: bot name max config err. firstNameMax:%d, secondNameMax:%d",
			fileName, m.BotFirstNameMax, m.BotSecondNameMax))
	}
	m.EmblemDeComposeRareLimit = m.Datas[uint32(common.CONFIG_EMBLEM_UMDECOMPOSE_RARE)]
	m.WishlistResetValue = m.Datas[uint32(common.CONFIG_WISHLIST_RESET_VALUE)]
	if m.WishlistResetValue == 0 {
		panic(fmt.Sprintf("load config %s fail: wishlistResetValue config err. value:%d",
			fileName, m.WishlistResetValue))
	}
	m.OverAllRatingMultiple = m.Datas[uint32(common.CONFIG_OVERALL_RATING_MULTIPLE)]
	m.WishListCDForNew = m.Datas[uint32(common.CONFIG_WISHLIST_CD_NEW)]
	if m.WishListCDForNew == 0 {
		panic(fmt.Sprintf("load config %s fail: wishListCDForNew config err. cd:%d",
			fileName, m.WishListCDForNew))
	}
	m.WishlistResetTimes = m.Datas[uint32(common.CONFIG_WISHLIST_RESET_TIMES)]
	m.MaxGiftsSendNum = m.Datas[uint32(common.CONFIG_MAX_GIFTS_SEND_NUM)]
	if m.MaxGiftsSendNum == 0 {
		panic(fmt.Sprintf("load config %s fail: MaxGiftsSendNum config err. cd:%d",
			fileName, m.MaxGiftsSendNum))
	}
	m.GoddessContractLevelUpItem = m.Datas[uint32(common.CONFIG_GODDESS_CONTRACT_LEVEL_UP_ITEM)]
	if m.GoddessContractLevelUpItem == 0 {
		panic(fmt.Sprintf("load config %s fail: GoddessContractLevelUpItem config err. cd:%d",
			fileName, m.MaxGiftsSendNum))
	}
	m.EnchantLimitRare = m.Datas[uint32(common.CONFIG_ENCHANT_LIMIT_RARE)]
	m.HeroSlotBuyGroup = m.Datas[uint32(common.CONFIG_HERO_BAG_BUY_GROUP)]
	if m.HeroSlotBuyGroup == 0 {
		panic(fmt.Sprintf("load config %s fail: HeroSlotBuyGroup config err. id is :%d",
			fileName, common.CONFIG_HERO_BAG_BUY_GROUP))
	}
	m.Link3UnlockHeroStar = m.Datas[uint32(common.CONFIG_LINK3_UNLOCK_HEROSTAR)]
	m.GoddessTouchLimit = m.Datas[uint32(common.CONFIG_GODDESS_TOUCH_LIMIT)]
	m.Link1UnlockHeroStar = m.Datas[uint32(common.CONFIG_LINK1_UNLOCK_HEROSTAR)]
	m.Link2UnlockHeroStar = m.Datas[uint32(common.CONFIG_LINK2_UNLOCK_HEROSTAR)]
	m.Link4UnlockHeroStar = m.Datas[uint32(common.CONFIG_LINK4_UNLOCK_HERO_STAR)]
	m.artifactPointsExchangeCostNum = m.Datas[uint32(common.CONFIG_ARTIFACT_EXCHANGE_COST_NUM)]
	if m.artifactPointsExchangeCostNum == 0 {
		panic(fmt.Sprintf("load config %s fail: artifactPointsExchangeCostNum is 0. id:%d",
			fileName, common.CONFIG_ARTIFACT_EXCHANGE_COST_NUM))
	}
	m.OssOpenLv = m.Datas[uint32(common.CONFIG_OSS_OPEN_LV)]
	m.heroTranslationReqCount = m.Datas[uint32(common.CONFIG_HERO_TRANSLATION)]
	if m.heroTranslationReqCount == 0 {
		panic(fmt.Sprintf("load config %s fail: heroTranslationReqCount is 0. id:%d",
			fileName, common.CONFIG_HERO_TRANSLATION))
	}
	m.artifactGuaranteeID = m.Datas[uint32(common.CONFIG_ARTIFACT_GUARANTEE_ID)]
	m.godPresentReduceHeroRare = m.Datas[uint32(common.CONFIG_GOD_PRESENT_HERO_REDUCE_RARE)]

	m.EmblemSlotBuyGroup = m.Datas[uint32(common.CONFIG_EMBLEM_SLOT_BUY_GROUP)]
	if m.EmblemSlotBuyGroup == 0 {
		panic(fmt.Sprintf("load config %s fail: EmblemSlotBuyGroup config err. id is :%d",
			fileName, common.CONFIG_EMBLEM_SLOT_BUY_GROUP))
	}
	m.EmblemSlotAddNum = m.Datas[uint32(common.CONFIG_EMBLEM_SLOT_ADD_NUM)]

	m.ActivityTicketIncreaseDaily = m.Datas[uint32(common.CONFIG_ACTIVITY_TICKET_INCREASE_DAILY)]
	m.ActivityTicketIncreaseLimit = m.Datas[uint32(common.CONFIG_ACTIVITY_TICKET_INCREASE_LIMIT)]
	m.ActivityStorySheildDays = m.Datas[uint32(common.CONFIG_ACTIVITY_STORY_SHEILD_DAYS)]
	m.AssistanceActivityNameNum = m.Datas[uint32(common.CONFIG_ASSISTANCE_ACTIVITY_NAME_NUM)]
	if m.AssistanceActivityNameNum == 0 {
		panic(fmt.Sprintf("load config %s fail: AssistanceActivityNameNum config err. id is :%d",
			fileName, common.CONFIG_ASSISTANCE_ACTIVITY_NAME_NUM))
	}
	m.FragmentComposeMaxUnitNum = m.Datas[uint32(common.CONFIG_FRAGMENT_COMPOSE_MAX_UNIT_NUM)]
	m.SeasonOpenServerDayLimit = m.Datas[uint32(common.CONFIG_SEASON_OPEN_SERVER_DAY_LIMIT)]
	if m.SeasonOpenServerDayLimit == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: seasonOpenServerDayLimit err. id is:%d",
			fileName, common.CONFIG_SEASON_OPEN_SERVER_DAY_LIMIT))
	}
	m.MirageBuyCountCostItemID = m.Datas[uint32(common.CONFIG_MIRAGE_BUY_COUNT_COST_ITEM_ID)]
	m.AwakenUniversalItem = m.Datas[uint32(common.CONFIG_AWAKEN_UNIVERSAL_ITEM)]
	m.Link5UnlockHeroStar = m.Datas[uint32(common.CONFIG_LINK5_UNLOCK_HEROSTAR)]
	m.Link5Id = m.Datas[uint32(common.CONFIG_LINK5_ID)]
	m.Link5RaisePS = 0
	m.SeasonReturnReceive = m.Datas[uint32(common.CONFIG_SEASON_RETURN_RECEIVE)]
	//皮肤分解
	m.SkinDecomposeDiamondLimit = m.Datas[uint32(common.CONFIG_SKIN_DECOMPOSE_DIAMOND_LIMIT)]
	m.SkinDecomposeGoldLimit = m.Datas[uint32(common.CONFIG_SKIN_DECOMPOSE_GOLD_LIMIT)]
	// 小助手
	m.AssistantShopChooseGoodsLimit = m.Datas[uint32(common.CONFIG_ASSISTANT_SHOP_CHOOSE_GOODS_LIMIT)]
	// 阵法回收
	m.Rite10RecyclePoint = m.Datas[uint32(common.CONFIG_RITE_10_RECYCLE_POINT)]
	m.Rite20RecyclePoint = m.Datas[uint32(common.CONFIG_RITE_20_RECYCLE_POINT)]
	m.Rite30RecyclePoint = m.Datas[uint32(common.CONFIG_RITE_30_RECYCLE_POINT)]
	m.Rite40RecyclePoint = m.Datas[uint32(common.CONFIG_RITE_40_RECYCLE_POINT)]
	m.Rite50RecyclePoint = m.Datas[uint32(common.CONFIG_RITE_50_RECYCLE_POINT)]
	m.Rite60RecyclePoint = m.Datas[uint32(common.CONFIG_RITE_60_RECYCLE_POINT)]
	m.RitePowerRecyclePoint = m.Datas[uint32(common.CONFIG_RITE_POWER_RECYCLE_POINT)]

	// 赛季羁绊回收
	m.SeasonLink10RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_10_RARE_RECYCLE_POINT)]
	m.SeasonLink20RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_20_RARE_RECYCLE_POINT)]
	m.SeasonLink30RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_30_RARE_RECYCLE_POINT)]
	m.SeasonLink40RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_40_RARE_RECYCLE_POINT)]
	m.SeasonLink50RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_50_RARE_RECYCLE_POINT)]
	m.SeasonLink60RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_60_RARE_RECYCLE_POINT)]
	m.SeasonLink70RareRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_70_RARE_RECYCLE_POINT)]
	m.SeasonLink30LvRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_30_LV_RECYCLE_POINT)]
	m.SeasonLink40LvRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_40_LV_RECYCLE_POINT)]
	m.SeasonLink50LvRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_50_LV_RECYCLE_POINT)]
	m.SeasonLink60LvRecyclePoint = m.Datas[uint32(common.CONFIG_SEASON_LINK_60_LV_RECYCLE_POINT)]

	s1SeasonOpenSpecialTime, err := StringDateToTime("2023-12-22 00:00:00")
	if err != nil {
		panic(fmt.Sprintf("load config %s fail: s1 season open special time error.", fileName))
	}
	m.S1SeasonOpenSpecialTm = s1SeasonOpenSpecialTime.Unix()
	m.NewYearActivitySheildDays = m.Datas[uint32(common.CONFIG_NEW_YEAR_ACTIVITY_SHEILD_DAY)]
	m.GstShieldTime = int64(m.Datas[uint32(common.CONFIG_ASIAN_GVG_SHIELD_TIME)])
	m.HeroConvertOpen = m.Datas[uint32(common.CONFIG_HERO_CONVERT_OPEN)]
	m.HeroConvertNum = m.Datas[uint32(common.CONFIG_HERO_CONVERT_NUM)]
	if m.HeroConvertNum == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: HeroConvertNum 0, id is:%d", fileName, common.CONFIG_HERO_CONVERT_NUM))
	}
	m.TurnTableOpenBuyTicketDay = m.Datas[uint32(common.CONFIG_TURNTABLE_TICKETS_BUY_DAY)]
	m.TurnTableRandomBuffNum = m.Datas[uint32(common.CONFIG_TURNTABLE_RANDOM_BUFF_NUM)]
	if m.TurnTableRandomBuffNum == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: TurnTableRandomBuffNum 0, id is:%d", fileName, common.CONFIG_TURNTABLE_RANDOM_BUFF_NUM))
	}
	m.ComplianceOpenData = m.Datas[uint32(common.CONFIG_COMPLIANCE_OPEN_DATE)]
	if m.ComplianceOpenData == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: ComplianceOpenData is 0, id is:%d", fileName, common.CONFIG_COMPLIANCE_OPEN_DATE))
	}
	m.ComplianceDuration = m.Datas[uint32(common.CONFIG_COMPLIANCE_DURATION)]
	if m.ComplianceDuration == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: ComplianceOpenData is 0, id is:%d", fileName, common.CONFIG_COMPLIANCE_DURATION))
	}
	m.HotRankRefreshTime = int64(m.Datas[uint32(common.CONFIG_HOT_RANK_REFRESH_TIME)])
	if m.HotRankRefreshTime == 0 {
		panic(fmt.Sprintf("load config fileName:%s faile: HotRankRefreshTime 0 id is:%d", fileName, common.CONFIG_HOT_RANK_REFRESH_TIME))
	}
	m.HotRankRefreshTime *= int64(util.DaySecs)

	m.ActivityComplianceLikeDiamond = m.Datas[uint32(common.CONFIG_COMPLIANCE_LIKE_DIAMOND)]
	if m.ActivityComplianceLikeDiamond == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: ComplianceLikeDiamond is 0, id is:%d", fileName, common.CONFIG_COMPLIANCE_LIKE_DIAMOND))
	}

	m.ComplianceOpenServer = int64(m.Datas[uint32(common.CONFIG_COMPLIANCE_OPEN_SERVER)])
	if m.ComplianceOpenServer == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: ComplianceOpenServer is 0, id is:%d", fileName, common.CONFIG_COMPLIANCE_OPEN_SERVER))
	}

	m.TowerstarDynamicFormationOpenChapter = m.Datas[uint32(common.CONFIG_TOWERSTAR_DYNAMIC_FORMATION_OPEN_CHAPTER)]
	if m.TowerstarDynamicFormationOpenChapter == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: TowerstarDynamicFormationOpenChapter is 0, id is:%d", fileName, common.CONFIG_TOWERSTAR_DYNAMIC_FORMATION_OPEN_CHAPTER))
	}

	m.BlessedHeroPowerRate = float64(m.Datas[uint32(common.CONFIG_BLESSED_HERO_POWER_RATE)]) / 10000.0
	if m.BlessedHeroPowerRate == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: BlessedHeroPowerRate is 0, id is:%d",
			fileName, common.CONFIG_BLESSED_HERO_POWER_RATE))
	}

	m.SocietyMailDiamondAward = m.Datas[uint32(common.CONFIG_SOCIETY_MAIL_DIAMOND_AWARD)]
	if m.SocietyMailDiamondAward == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: SocietyMailDiamondAward is 0, id is:%d",
			fileName, common.CONFIG_SOCIETY_MAIL_DIAMOND_AWARD))
	}
	m.SocietyMailPushLevel = m.Datas[uint32(common.CONFIG_SOCIETY_MAIL_PUSH_LEVEL)]
	m.DuelOperateInterval = m.Datas[uint32(common.CONFIG_DUEL_COOLDOWN_TIME)] * 60
	m.ActivityTowerDuration = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_DURATION)]
	m.ActivityTowerRankRemain = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_RANK_REMAIN)]
	m.ActivityTowerLikeDiamond = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_LIKE_DIAMOND)]
	m.ActivityMirageDuration = m.Datas[uint32(common.CONFIG_ACTIVITY_MIRAGE_DURATION)]
	m.ActivityMirageRankRemain = m.Datas[uint32(common.CONFIG_ACTIVITY_MIRAGE_RANK_REMAIN)]
	m.ActivityMirageLikeDiamond = m.Datas[uint32(common.CONFIG_ACTIVITY_MIRAGE_LIKE_DIAMOND)]
	m.ActivityTowerSeasonRankOpen = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_OPEN)]
	m.ActivityTowerSeasonRankEnd = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_END)]
	m.ActivityTowerSeasonRankAwardEnd = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_AWARD_END)]
	m.ActivityTowerSeasonRankLikeDiamond = GenSimpleResource(uint32(common.RESOURCE_DIAMOND), 0, m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_DAILY_LIKE_AWARD)])

	m.ActivityDropServerDate = int64(m.Datas[uint32(common.CONFIG_ACTIVITY_DROP_SERVER_DATA)])
	if m.ActivityDropServerDate == 0 {
		panic(fmt.Sprintf("load config fileName:%s fail: ActivityDropServerDate is 0, id is:%d", fileName, common.CONFIG_ACTIVITY_DROP_SERVER_DATA))
	}
	m.ActivityTowerSeason2RankOpen = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_OPEN_ROUND_2)]
	if m.ActivityTowerSeason2RankOpen <= m.ActivityTowerSeasonRankAwardEnd {
		panic(fmt.Sprintf("load config fileName:%s fail: activityTowerSeasonRank2 open error", fileName))
	}
	m.ActivityTowerSeason2RankEnd = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_END_ROUND_2)]
	if m.ActivityTowerSeason2RankEnd < m.ActivityTowerSeason2RankOpen {
		panic(fmt.Sprintf("load config fileName:%s fail: activityTowerSeasonRank2 end error", fileName))
	}
	m.ActivityTowerSeason2RankAwardEnd = m.Datas[uint32(common.CONFIG_ACTIVITY_TOWER_SEASON_RANK_AWARD_END_ROUND_2)]
	if m.ActivityTowerSeason2RankAwardEnd < m.ActivityTowerSeason2RankEnd {
		panic(fmt.Sprintf("load config fileName: %s fail: activityTowerSeasonRank2 award end error", fileName))
	}
	m.RechargeRefundTime = int64(m.Datas[uint32(common.CONFIG_RECHARGE_REFUND_TIME_CN)])
	m.RechargeRefundDungeon = m.Datas[uint32(common.CONFIG_RECHARGE_REFUND_DUNGEON_CN)]
	m.RechargeByCouponCost = m.Datas[uint32(common.CONFIG_RECHARGE_BY_COUPON_COST)]
	if m.RechargeByCouponCost == 0 {
		panic(fmt.Sprintf("load config fileName: %s fail: RechargeByCouponCost %s invalid", fileName, m.RechargeByCouponCost))
	}
	m.HeroAwakenConvertResLimit = m.Datas[uint32(common.CONFIG_HERO_AWAKEN_CONVERT_RES_LIMIT)]
	m.MailTimeLimit = int64(m.Datas[uint32(common.CONFIG_MAIL_TIME_LIMIT)])
	m.MailNumLimit = m.Datas[uint32(common.CONFIG_MAIL_NUM_LIMIT)]
	return nil
}

func (m *ConfigInfoManager) Index(key uint32) uint32 {
	return m.Datas[key]
}

func (m *ConfigInfoManager) GetHeroSlotMax(vipLv uint32) uint32 {
	return m.HeroSlotMaxNum + m.xmlData.VipPrivilegeInfoM.getHeroSlotMaxNumAdd(vipLv)
}

func (m *ConfigInfoManager) GetFriendMaxNum(vipLv uint32) uint32 {
	return m.FriendMaxNum + m.xmlData.VipPrivilegeInfoM.getFriendMaxNumAdd(vipLv)
}

func (m *ConfigInfoManager) GetOnhookMaxNum(vipLv uint32) uint32 {
	min := m.OnhookMaxTime + m.xmlData.VipPrivilegeInfoM.getMaxOnhookTimeAdd(vipLv)
	return min * 60 // 配置是分钟，转为秒
}

func (m *ConfigInfoManager) CalcRatingMultipleScore(score int64) (bool, int64) {
	isZero, multipleScore := util.DivFloor(BaseInt64, int64(m.OverAllRatingMultiple), score)
	if isZero {
		return false, 0
	}
	return true, multipleScore
}

func (m *ConfigInfoManager) GetGiftsNum() uint32 {
	return m.MaxGiftsSendNum
}

func (m *ConfigInfoManager) GetEnchantLimitRare() uint32 {
	return m.EnchantLimitRare
}

func (m *ConfigInfoManager) GetHeroSlotBuyGroup() uint32 {
	return m.HeroSlotBuyGroup
}

func (m *ConfigInfoManager) GetHeroSlotAddNum() uint32 {
	return m.HeroSlotAddNum
}

func (m *ConfigInfoManager) FreeGoddessTouchCount() uint32 {
	return m.GoddessTouchLimit
}

func (m *ConfigInfoManager) GetLink4UnLockHeroStar() uint32 {
	return m.Link4UnlockHeroStar
}

func (m *ConfigInfoManager) GetArtifactExchangeCost() []*cl.Resource {
	return []*cl.Resource{
		{
			Type:  uint32(common.RESOURCE_TOKEN),
			Value: ArtifactSummonGetPointValue,
			Count: m.artifactPointsExchangeCostNum,
		},
	}
}

func (m *ConfigInfoManager) GetHeroTranslationReqCount() uint32 {
	return m.heroTranslationReqCount
}

func (m *ConfigInfoManager) GetArtifactGuaranteeID() uint32 {
	return m.artifactGuaranteeID
}

func (m *ConfigInfoManager) GetGodPresentReduceHeroRare() uint32 {
	return m.godPresentReduceHeroRare
}

// 符文背包默认格位数量
func (m *ConfigInfoManager) GetEmblemSlotBaseCount() uint32 {
	return m.EmblemBagMaxLimitNum
}

// 符文背包格位上限
func (m *ConfigInfoManager) GetEmblemSlotMax(vipLv uint32) uint32 {
	return m.EmblemBagMaxLimitNum + m.xmlData.VipPrivilegeInfoM.GetEmblemSlotMaxAdd(vipLv)
}

func (m *ConfigInfoManager) GetEmblemSlotBuyGroup() uint32 {
	return m.EmblemSlotBuyGroup
}

func (m *ConfigInfoManager) GetEmblemSlotAddNum() uint32 {
	return m.EmblemSlotAddNum
}

func (m *ConfigInfoManager) GetActivityTicketIncreaseDaily() uint32 {
	return m.ActivityTicketIncreaseDaily
}

func (m *ConfigInfoManager) GetActivityTicketIncreaseLimit() uint32 {
	return m.ActivityTicketIncreaseLimit
}

func (m *ConfigInfoManager) GetActivityStorySheildDays() uint32 {
	return m.ActivityStorySheildDays
}

func (m *ConfigInfoManager) GetAssistanceActivityNameNum() uint32 {
	return m.AssistanceActivityNameNum
}

func (m *ConfigInfoManager) GetFragmentComposeMaxUnitNum() uint32 {
	return m.FragmentComposeMaxUnitNum
}

func (m *ConfigInfoManager) GetSeasonOpenServerDayLimit() uint32 {
	return m.SeasonOpenServerDayLimit
}

func (m *ConfigInfoManager) GetLink5RaisPS() uint64 {
	//ConfigInfoManager初始化比较靠前，无法在Load时获取Link5RaisePS，这里做缓存处理
	if m.Link5RaisePS > 0 {
		return m.Link5RaisePS
	}
	raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(m.Link5Id, 1)
	if raisePSInfo == nil {
		l4g.Errorf("Link5Id not found in RaisePassiveSkillInfoM. Link5Id:%d", m.Link5Id)
		return 0
	}
	m.Link5RaisePS = raisePSInfo.ID
	return m.Link5RaisePS
}

func (m *ConfigInfoManager) IsHeroAwakenStarUnlocked(star uint32) bool {
	return star >= m.Link5UnlockHeroStar
}

func (m *ConfigInfoManager) GetSeasonReturnReceive() uint32 {
	return m.SeasonReturnReceive
}

// 皮肤分解后的资源仅有两种，钻石和金币
// 后续如有扩展，需修改此处逻辑
func (m *ConfigInfoManager) GetSkinDecomposeLimit(typ uint32) uint32 {
	if typ == uint32(common.RESOURCE_DIAMOND) {
		return m.SkinDecomposeDiamondLimit
	} else {
		return m.SkinDecomposeGoldLimit
	}
}

func (m *ConfigInfoManager) GetAssistantShopChooseGoodsLimit() uint32 {
	return m.AssistantShopChooseGoodsLimit
}

func (m *ConfigInfoManager) GetRiteRareRecyclePoint(rare uint32) uint32 {
	switch rare {
	case uint32(common.QUALITY_GREY):
		return m.Rite10RecyclePoint
	case uint32(common.QUALITY_GREEN):
		return m.Rite20RecyclePoint
	case uint32(common.QUALITY_BLUE):
		return m.Rite30RecyclePoint
	case uint32(common.QUALITY_PURPLE):
		return m.Rite40RecyclePoint
	case uint32(common.QUALITY_ORANGE):
		return m.Rite50RecyclePoint
	case uint32(common.QUALITY_RED):
		return m.Rite60RecyclePoint
	}
	return 0
}

func (m *ConfigInfoManager) GetRitePowerRecyclePoint() uint32 {
	return m.RitePowerRecyclePoint
}

func (m *ConfigInfoManager) GetSeasonLinkRecyclePoint(rare, level uint32) uint32 {
	switch rare {
	case uint32(common.QUALITY_GREY):
		return m.SeasonLink10RareRecyclePoint
	case uint32(common.QUALITY_GREEN):
		return m.SeasonLink20RareRecyclePoint
	case uint32(common.QUALITY_BLUE):
		return m.SeasonLink30RareRecyclePoint + level*m.SeasonLink30LvRecyclePoint
	case uint32(common.QUALITY_PURPLE):
		return m.SeasonLink40RareRecyclePoint + level*m.SeasonLink40LvRecyclePoint
	case uint32(common.QUALITY_ORANGE):
		return m.SeasonLink50RareRecyclePoint + level*m.SeasonLink50LvRecyclePoint
	case uint32(common.QUALITY_RED):
		return m.SeasonLink60RareRecyclePoint + level*m.SeasonLink60LvRecyclePoint
	case uint32(common.QUALITY_GOLDEN):
		return m.SeasonLink70RareRecyclePoint
	}
	return 0
}

// IsServerSeasonSpecialOpen
// @Description:  S1赛季上线特殊判断，支持赛季解锁从22天改为29天
// @receiver m
// @param serviceStartTm 开服时间
// @return int64
func (m *ConfigInfoManager) IsServerSeasonSpecialOpen(serviceStartTm int64) bool {
	if serviceStartTm < m.S1SeasonOpenSpecialTm {
		return true
	}
	return false
}

func (m *ConfigInfoManager) GetNewYearActivitySheildDays() uint32 {
	return m.ActivityStorySheildDays
}

func (m *ConfigInfoManager) GetHeroConvertOpen() uint32 {
	return m.HeroConvertOpen
}

func (m *ConfigInfoManager) GetHeroConvertNum() uint32 {
	return m.HeroConvertNum
}

func (m *ConfigInfoManager) GetTurnTableOpenBuyTicketDay() uint32 {
	return m.TurnTableOpenBuyTicketDay
}

func (m *ConfigInfoManager) GetTurnTableRandomBuffNum() uint32 {
	return m.TurnTableRandomBuffNum
}

func (m *ConfigInfoManager) ActivityComplianceOpen(serverDay uint32, now int64) bool {
	if now <= m.ComplianceOpenServer {
		return false
	}
	return serverDay >= m.ComplianceOpenData && serverDay < m.ComplianceOpenData+m.ComplianceDuration
}

func (m *ConfigInfoManager) ActivityComplianceEnd(serverDay uint32) bool {
	return serverDay >= m.ComplianceOpenData+m.ComplianceDuration
}

func (m *ConfigInfoManager) GetHotRankRefreshTime() int64 {
	return m.HotRankRefreshTime
}

func (m *ConfigInfoManager) GetActivityComplianceLikeDiamondResource() *cl.Resource {
	return &cl.Resource{
		Type:  uint32(common.RESOURCE_DIAMOND),
		Count: m.ActivityComplianceLikeDiamond,
	}
}

func (m *ConfigInfoManager) GetTowerstarDynamicFormationOpenChapter() uint32 {
	return m.TowerstarDynamicFormationOpenChapter
}

func (m *ConfigInfoManager) GetBlessedHeroPowerRate() float64 {
	return m.BlessedHeroPowerRate
}

func (m *ConfigInfoManager) GetSocietyMailDiamondAward() uint32 {
	return m.SocietyMailDiamondAward
}

func (m *ConfigInfoManager) GetSocietyMailPushLevel() uint32 {
	return m.SocietyMailPushLevel
}

func (m *ConfigInfoManager) ActivityTowerEnd(serverDay uint32) bool {
	funcInfo := m.xmlData.FunctionInfoM.Index(uint32(common.FUNCID_MODULE_ACTIVITY_TOWER))
	if funcInfo == nil {
		l4g.Errorf("[ConfigInfoManager] ActivityTowerEnd: no funcInfo. ")
		return false
	}
	return serverDay >= funcInfo.ServerDay+m.ActivityTowerDuration
}

func (m *ConfigInfoManager) ActivityTowerOpen(serverDay, userLevel uint32) bool {
	funcInfo := m.xmlData.FunctionInfoM.Index(uint32(common.FUNCID_MODULE_ACTIVITY_TOWER))
	if funcInfo == nil {
		l4g.Errorf("[ConfigInfoManager] ActivityTowerOpen: no funcInfo. ")
		return false
	}

	if userLevel < funcInfo.Lv {
		return false
	}

	return serverDay >= funcInfo.ServerDay && serverDay < funcInfo.ServerDay+m.ActivityTowerDuration
}

func (m *ConfigInfoManager) ActivityTowerShow(serverDay, userLevel uint32) bool {
	funcInfo := m.xmlData.FunctionInfoM.Index(uint32(common.FUNCID_MODULE_ACTIVITY_TOWER))
	if funcInfo == nil {
		l4g.Errorf("[ConfigInfoManager] ActivityTowerOpen: no funcInfo. ")
		return false
	}

	if userLevel < funcInfo.Lv {
		return false
	}

	return serverDay >= funcInfo.ServerDay && serverDay < funcInfo.ServerDay+m.ActivityTowerDuration+m.ActivityTowerRankRemain
}

func (m *ConfigInfoManager) ActivityTowerLikeAwards() []*cl.Resource {
	return []*cl.Resource{GenSimpleResource(uint32(common.RESOURCE_DIAMOND), 0, m.ActivityTowerLikeDiamond)}
}

func (m *ConfigInfoManager) ActivityMirageEnd(serverDay uint32) bool {
	funcInfo := m.xmlData.FunctionInfoM.Index(uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE))
	if funcInfo == nil {
		l4g.Errorf("[ConfigInfoManager] ActivityMirageEnd: no funcInfo. ")
		return false
	}
	return serverDay >= funcInfo.ServerDay+m.ActivityMirageDuration
}

func (m *ConfigInfoManager) ActivityMirageOpen(serverDay, userLevel uint32) bool {
	funcInfo := m.xmlData.FunctionInfoM.Index(uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE))
	if funcInfo == nil {
		l4g.Errorf("[ConfigInfoManager] ActivityMirageOpen: no funcInfo. ")
		return false
	}

	if userLevel < funcInfo.Lv {
		return false
	}

	return serverDay >= funcInfo.ServerDay && serverDay < funcInfo.ServerDay+m.ActivityMirageDuration
}

func (m *ConfigInfoManager) ActivityMirageShow(serverDay, userLevel uint32) bool {
	funcInfo := m.xmlData.FunctionInfoM.Index(uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE))
	if funcInfo == nil {
		l4g.Errorf("[ConfigInfoManager] ActivityMirageOpen: no funcInfo. ")
		return false
	}

	if userLevel < funcInfo.Lv {
		return false
	}

	return serverDay >= funcInfo.ServerDay && serverDay < funcInfo.ServerDay+m.ActivityMirageDuration+m.ActivityMirageRankRemain
}

func (m *ConfigInfoManager) ActivityMirageLikeAwards() []*cl.Resource {
	return []*cl.Resource{GenSimpleResource(uint32(common.RESOURCE_DIAMOND), 0, m.ActivityMirageLikeDiamond)}
}

func (m *ConfigInfoManager) IsRankActivityTowerSeasonActive(seasonOpenDay uint32) bool {
	if seasonOpenDay >= m.ActivityTowerSeasonRankOpen && seasonOpenDay <= m.ActivityTowerSeasonRankEnd {
		return true
	}
	if seasonOpenDay >= m.ActivityTowerSeason2RankOpen && seasonOpenDay <= m.ActivityTowerSeason2RankEnd {
		return true
	}
	return false
}

func (m *ConfigInfoManager) IsRankActivityTowerSeasonOpen(seasonOpenDay uint32) bool {
	if seasonOpenDay >= m.ActivityTowerSeasonRankOpen && seasonOpenDay <= m.ActivityTowerSeasonRankAwardEnd {
		return true
	}
	if seasonOpenDay >= m.ActivityTowerSeason2RankOpen && seasonOpenDay <= m.ActivityTowerSeason2RankAwardEnd {
		return true
	}
	return false
}

func (m *ConfigInfoManager) GetRankActivityTowerSeasonDailyLikeAward() *cl.Resource {
	return m.ActivityTowerSeasonRankLikeDiamond
}

func (m *ConfigInfoManager) NewServerDropActivityOpen(openTime int64) bool {
	return openTime >= m.ActivityDropServerDate
}

func (m *ConfigInfoManager) CanReqRebase(now int64, dungeon uint32) bool {
	return now < m.RechargeRefundTime && dungeon >= m.RechargeRefundDungeon
}

func (m *ConfigInfoManager) IsMoreThanHeroConvertLimit(count uint32) bool {
	return count > m.HeroAwakenConvertResLimit
}
