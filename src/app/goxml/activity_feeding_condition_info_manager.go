package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityFeedingConditionInfoManager struct {
	xmlData    *XmlData
	ActIDCond  map[uint32]map[uint32]*ActivityFeedingConditionInfo
	ActMaxCond map[uint32]uint32
}

func newActivityFeedingConditionInfoManager(xmlData *XmlData) *ActivityFeedingConditionInfoManager {
	m := &ActivityFeedingConditionInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityFeedingConditionInfoManager) name() string {
	return "activity_feeding_condition_info.xml"
}

func (m *ActivityFeedingConditionInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityFeedingConditionInfoManager) checkData() error {
	return nil
}

func (m *ActivityFeedingConditionInfoManager) load(dir string, show bool) error {
	tmp := &ActivityFeedingConditionInfos{}
	fileName := filepath.Join(dir, "activity_feeding_condition_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	var CondCheck uint32
	var actID uint32
	m.ActIDCond = make(map[uint32]map[uint32]*ActivityFeedingConditionInfo)
	m.ActMaxCond = make(map[uint32]uint32)
	for _, data := range tmp.Datas {
		actInfo := m.xmlData.ActivityTurntableInfoM.Index(data.Id)
		if actInfo == nil {
			panic(fmt.Sprintf("load config %s fail: %d id in actInfo is nil", fileName, data.Id))
		}
		if actID != data.Id {
			if data.Condition != ActivitySumFeedDefaultCond {
				panic(fmt.Sprintf("load config %s fail: id %d Condition:%d default config is error %d", fileName, data.Id, data.Condition))
			}
			CondCheck = 0
		}
		actID = data.Id

		if CondCheck+1 != data.Condition {
			panic(fmt.Sprintf("load config %s fail: id %d Condition:%d level check fail", fileName, data.Id, data.Condition))
		}

		CondCheck = data.Condition

		_, exist := m.ActIDCond[data.Id]
		if !exist {
			m.ActIDCond[data.Id] = make(map[uint32]*ActivityFeedingConditionInfo)
		}
		m.ActIDCond[data.Id][data.Condition] = data

		if m.ActMaxCond[data.Id] < data.Condition {
			m.ActMaxCond[data.Id] = data.Condition
		}
	}
	return nil
}

func (m *ActivityFeedingConditionInfoManager) Index(actID uint32, cond uint32) *ActivityFeedingConditionInfo {
	return m.ActIDCond[actID][cond]
}

func (m *ActivityFeedingConditionInfoManager) ActMax(actID uint32) uint32 {
	return m.ActMaxCond[actID]
}

func (m *ActivityFeedingConditionInfo) IsFavorite(giftID uint32) bool {
	return m.Favorite1 == giftID || m.Favorite2 == giftID || m.Favorite3 == giftID
}
