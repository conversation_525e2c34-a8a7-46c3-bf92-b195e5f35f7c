package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

// 战役
const (
	DungeonCalcRate  uint32  = 10 //挂机奖励计算频率，最低不低于10秒
	DungeonAwardRate float64 = 60 //挂机奖励配表掉落频率
)

// 英雄
const (
	HeroRareLegend           uint32 = 50
	HeroMaxAwakeNum          uint32 = 6 //觉醒星级数量
	HeroAwakeAttrNum         uint32 = 4 //量表中，每个觉醒等级对应属性的最大数量
	HeroStageAttrNum         uint32 = 3 //量表中，每个突破等级对应属性的最大数量
	HeroStageSkillNum        uint32 = 4 //量表中，突破获得技能的数量
	HeroStageUpCostNum       uint32 = 2 //量表中,突破消耗资源类型最大数量
	HeroStarUpCostHeroNum    uint32 = 4 //量表中,升星消耗卡牌初始容量
	HeroDecomposeAwardResNum uint32 = 4 //量表中,英雄分解返还资源类型最大数量

	HeroSpecial uint32 = 1 //仅能被消耗的英雄，对应hero_info表中的special=1
	HeroCommon  uint32 = 0 //常规英雄，对应hero_info表中的special=0

	HeroShowManual uint32 = 1 //可上阵英雄

	HeroNoRaceDefaultStar uint32 = 9 //无种族英雄默认星级

	HeroStarUpCostTypeSelf uint32 = 1 //升星消耗英雄类型 - 相同的
	HeroStarUpCostTypeRace uint32 = 2 //升星消耗英雄类型 - 同一种族的
	HeroStarUpCostTypeAny  uint32 = 3 //升星消耗英雄类型 - 任意的

	HeroCanBackMinInitStar uint32 = 5 //英雄可回退的最小初始星级 --- 关系到英雄回退逻辑，只可以往大改（>5）
	HeroCanBack            uint32 = 1 //当back为1时，英雄可以回退

	HeroStarUpCostStar5 uint32 = 5 //英雄升星 - 消耗基础材料星级 - 5
	HeroStarUpCostStar6 uint32 = 6 //英雄升星 - 消耗基础材料星级 - 6
	HeroStarUpCostStar9 uint32 = 9 //英雄升星 - 消耗基础材料星级 - 9

	HeroRareNormal   uint32 = 20
	HeroRareFind     uint32 = 30
	HeroRareUnusual  uint32 = 40
	HeroRareFakeFive uint32 = 50
	HeroRareTrueFive uint32 = 60

	HeroGemNeedMinInitStar uint32 = 5  //英雄养成宝石需要的最小初始星级
	HeroConvertMaxNum      uint32 = 10 // 英雄单次转换数量上限
)

// 通用英雄（无种族）的合法星数
var HeroSpecialNoRaceLegalStar = map[uint32]bool{
	HeroNoRaceDefaultStar: true,
}

// 通用英雄（有种族）的合法星数
var HeroSpecialNormalRaceLegalStar = map[uint32]bool{
	5: true,
	6: true,
}

// 升星消耗卡牌合法类型
var HeroStarUpCostLegalType = map[uint32]bool{
	HeroStarUpCostTypeSelf: true,
	HeroStarUpCostTypeRace: true,
	HeroStarUpCostTypeAny:  true,
}

// 升星消耗卡牌合法星级
var HeroStarUpCostLegalStar = map[uint32]bool{
	HeroStarUpCostStar5: true,
	HeroStarUpCostStar6: true,
	HeroStarUpCostStar9: true,
}

// 掉落
const (
	OnhookRealRandomDropTime uint32  = 600 //挂机少于10分钟，则真随机
	BaseFloat                float64 = 10000.0
	BaseInt                  int     = 10000
	BaseInt64                int64   = 10000
	BaseUInt32               uint32  = 10000
	TypeDrop                 uint32  = 1 //掉落类型 - 必掉，不考虑权重
	TypeRandom               uint32  = 2 //掉落类型 - 多选一，需计算权重
)

// 竞技场
const (
	ArenaRankTypeDaily    uint32 = 1   //排名奖励类型 - 每日
	ArenaRankTypeSeason   uint32 = 2   //排名奖励类型 - 每周（赛季）
	ArenaRankRewardNum    int    = 2   //排名奖励数量
	ArenaFightWin         uint32 = 1   //竞技场战斗胜利
	ArenaFightLose        uint32 = 2   //竞技场战斗失败
	ArenaOpponentNum      int    = 3   //竞技场对手数量
	ArenaLogMaxNum        uint32 = 50  //竞技场战斗记录最大数量
	ArenaBotNumPerLv      int    = 3   //竞技场每个等级机器人数量
	ArenaDivisionUserCap  int    = 500 //未定义排名要求的段位，初始cap数量
	ArenaDivisionAwardCap int    = 5   //竞技场段位奖励种类数量
	ArenaMatchExtMaxNum   uint32 = 10  //扩展搜索方式的个数上限

	ArenaMatchExtModeNone       uint32 = 0 //扩展搜索方式类型 - 无
	ArenaMatchExtModeUpThenDown uint32 = 1 //扩展搜索方式类型 - 先向上再向下
	ArenaMatchExtModeDown       uint32 = 2 //扩展搜索方式类型 - 向下
)

// 竞技场发奖类型
var LegalArenaRewardType = []uint32{
	ArenaRankTypeDaily,
	ArenaRankTypeSeason,
}

// 竞技场 - 是否是合法的发奖类型
func IsLegalArenaRewardType(rewardType uint32) bool {
	for _, typ := range LegalArenaRewardType {
		if typ == rewardType {
			return true
		}
	}
	return false
}

// 竞技场匹配扩展类型
var LegalArenaMatchExtMode = []uint32{
	ArenaMatchExtModeNone,
	ArenaMatchExtModeUpThenDown,
	ArenaMatchExtModeDown,
}

// 竞技场 - 是否是合法的匹配扩展类型
func IsLegalArenaMatchExtMode(mode uint32) bool {
	for _, m := range LegalArenaMatchExtMode {
		if m == mode {
			return true
		}
	}
	return false
}

// 次数相关
const (
	NumRefreshForbid       uint32 = 0 //不刷新
	NumRefreshDaily        uint32 = 1 //日刷新
	NumRefreshWeekly       uint32 = 2 //周一刷新
	NumRefreshWeeklyFriday uint32 = 3 //周五刷新
	NumBuyForbid           uint32 = 0 //不能购买
	NumBuyAndUse           uint32 = 1 //使用时购买
	NumBuyOnly             uint32 = 2 //单独购买
	NumBuyNoLimit          uint32 = 2 //无限购买
)

func IsLegalNumRefreshType(refreshType uint32) bool {
	return refreshType == NumRefreshForbid || refreshType == NumRefreshDaily ||
		refreshType == NumRefreshWeekly || refreshType == NumRefreshWeeklyFriday
}

const (
	AvatarTypeIcon       uint32 = 1 //头像
	AvatarTypeFrame      uint32 = 2 //头像框
	AvatarTypeImage      uint32 = 3 //形象
	AvatarTypeChatBubble uint32 = 5 //聊天气泡
)

const (
	AvatarUnlockTypeNone       uint32 = 0 //默认拥有的图像
	AvatarUnlockTypeHero       uint32 = 1 //获得英雄的时候触发的头像
	AvatarUnlockTypeArenaLevel uint32 = 2 //竞技场结算触发
	AvatarUnlockTypeSkin       uint32 = 3 //拥有永久皮肤
)

const (
	AvatarTimeTypeForever  uint32 = 0 //永久头像框
	AvatarTimeTypeDuration uint32 = 1 //累加时间头像框
	AvatarTimeTypeEndTime  uint32 = 2 //固定截止时间
)

const (
	TitleTimeTypeForever  uint32 = iota //永久称号
	TitleTimeTypeDuration uint32 = 1    //累加时间称号
	TitleTimeTypeEndTime  uint32 = 2    //固定截止时间
)

// 图鉴
const (
	HandbookInitCap int = 10 //暂定的初始容量
)

// 排行成就
const (
	RankAchieveInitCap       int = 30 //暂定的初始容量
	RankAchieveAwardsInitCap int = 30 //暂定的初始容量
)

// 合法的通用排行榜类型id
var LegalCommonRankId = map[uint32]bool{
	PowerRankId:               true,
	DungeonRankId:             true,
	TowerRankId:               true,
	ArenaRankId:               true,
	MirageEmpireRankId:        true,
	MirageForestRankId:        true,
	MirageMoonRankId:          true,
	MirageProtossRankId:       true,
	MirageDemonRankId:         true,
	GuildDungeonChapterRankId: true,
	TowerstarRankId:           true,
	GuildLevelRankID:          true,
	//	GuildDungeonWeeklyDamageRankId: true,
	MirageSixRankId:     true,
	ActivityStoryRankID: true,
	SeasonDungeonRankID: true,
	SeasonPowerRankID:   true,
	//SeasonLink1RankID:         true,
	//SeasonLink2RankID:         true,
	//SeasonLink3RankID:         true,
	//SeasonLink4RankID:         true,
	//SeasonLink5RankID:         true,
	//SeasonLink6RankID:         true,
	//SeasonLink7RankID:         true,
	//SeasonLink8RankID:         true,
	SeasonRemainBookExpRankID: true,
	BossRush1RankID:           true,
	BossRush2RankID:           true,
	BossRush3RankID:           true,
	BossRush4RankID:           true,
	BossRush5RankID:           true,
	BossRush6RankID:           true,
	BossRush7RankID:           true,
	BossRush8RankID:           true,
	ActivityComplianceID:      true,
	ActivityTowerRankID:       true,
	ActivityMirageRankID:      true,
}

// 排行类型id与排行成就func_id的对应关系
var RankId2FuncId = map[uint32]uint32{
	PowerRankId:         uint32(common.FUNCID_MODULE_RANK_ACHIEVE_POWER),
	DungeonRankId:       uint32(common.FUNCID_MODULE_RANK_ACHIEVE_DUNGEON),
	TowerRankId:         uint32(common.FUNCID_MODULE_RANK_ACHIEVE_TOWER),
	ArenaRankId:         uint32(common.FUNCID_MODULE_RANK_ACHIEVE_ARENA),
	MirageEmpireRankId:  uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_EMPIRE),
	MirageForestRankId:  uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_FOREST),
	MirageMoonRankId:    uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_MOON),
	MirageProtossRankId: uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_PROTOSS),
	MirageDemonRankId:   uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_DEMON),
	MirageSixRankId:     uint32(common.FUNCID_MODULE_RANK_ACHIEVE_MIRAGE_SIX),
}

var HeroLegalRace = map[uint32]bool{
	RaceNone:    true,
	RaceEmpire:  true,
	RaceForest:  true,
	RaceMoon:    true,
	RaceProtoss: true,
	RaceDemon:   true,
}

// 种族类型
const (
	RaceNone uint32 = iota //通用卡
	RaceEmpire
	RaceForest
	RaceMoon
	RaceProtoss
	RaceDemon
)

// 种族类型分类
const (
	RaceTypeCommonThree  uint32 = 7 //三系英雄
	RaceTypeProtossDemon uint32 = 8 //神魔英雄
)

func IsRaceInhibit(attack, defence uint32) bool {
	switch attack {
	case RaceNone:
		return false
	case RaceEmpire:
		return defence == RaceForest
	case RaceForest:
		return defence == RaceMoon
	case RaceMoon:
		return defence == RaceEmpire
	case RaceProtoss:
		return defence == RaceDemon
	case RaceDemon:
		return defence == RaceProtoss
	}
	return false
}

// 星期
const (
	Monday     uint32 = 1
	Tuesday    uint32 = 2
	Wednesday  uint32 = 3
	Thursday   uint32 = 4
	Friday     uint32 = 5
	Saturday   uint32 = 6
	Sunday     uint32 = 7
	WeekDayNum int    = 7
)

// 个人boss
const (
	MirageInitAffixNum     int = 3 //初始词缀组词缀个数
	MirageInitAffixAttrNum int = 4 //初始词缀属性个数
)

const (
	MirageRaceEmpire  uint32 = 1 //
	MirageRaceForest  uint32 = 2 //
	MirageRaceMoon    uint32 = 3 //
	MirageRaceProtoss uint32 = 4 //
	MirageRaceDemon   uint32 = 5 //
	MirageRaceSix     uint32 = 6 // 第6个boss
)

// 个人boss副本与阵容id的映射
var MirageCopyID2FormationID = map[uint32]common.FORMATION_ID{
	MirageRaceEmpire:  common.FORMATION_ID_FI_MIRAGE_RACE_EMPIRE,
	MirageRaceForest:  common.FORMATION_ID_FI_MIRAGE_RACE_FOREST,
	MirageRaceMoon:    common.FORMATION_ID_FI_MIRAGE_RACE_MOON,
	MirageRaceProtoss: common.FORMATION_ID_FI_MIRAGE_RACE_PROTOSS,
	MirageRaceDemon:   common.FORMATION_ID_FI_MIRAGE_RACE_DEMON,
	MirageRaceSix:     common.FORMATION_ID_FI_MIRAGE_RACE_SIX,
}

var MirageLegalSkillTarget = map[uint32]bool{
	0: true, //1-5号位置
	1: true, //1号位
	2: true,
	3: true,
	4: true,
	5: true,
	6: true, //针对队伍
}

var LegalWeekDay = map[uint32]struct{}{
	Monday:    {},
	Tuesday:   {},
	Wednesday: {},
	Thursday:  {},
	Friday:    {},
	Saturday:  {},
	Sunday:    {},
}

var MirageLegalCopyID = map[uint32]bool{
	MirageRaceEmpire:  true,
	MirageRaceForest:  true,
	MirageRaceMoon:    true,
	MirageRaceProtoss: true,
	MirageRaceDemon:   true,
	MirageRaceSix:     true,
}

var MirageCopyID2RankId = map[uint32]uint32{
	MirageRaceEmpire:  MirageEmpireRankId,
	MirageRaceForest:  MirageForestRankId,
	MirageRaceMoon:    MirageMoonRankId,
	MirageRaceProtoss: MirageProtossRankId,
	MirageRaceDemon:   MirageDemonRankId,
	MirageRaceSix:     MirageSixRankId,
}

var MirageCopyID2TaskTypeId = map[uint32]uint32{
	MirageRaceEmpire:  uint32(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_EMPIRE_FLOOR),
	MirageRaceForest:  uint32(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_FOREST_FLOOR),
	MirageRaceMoon:    uint32(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_MOON_FLOOR),
	MirageRaceProtoss: uint32(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_PROTOSS_FLOOR),
	MirageRaceDemon:   uint32(common.TASK_TYPE_ID_TTYPE_ID_MIRAGE_PASS_DEMON_FLOOR),
}

// 迷宫maze_constant_info.xml id
const (
	MazeCurseHurtID                      uint32 = 7  // 诅咒地块
	MazeDanusHeartID                     uint32 = 10 // 达努之心，用于复活迷宫中所有的英雄
	MazeLifeCureID                       uint32 = 11 // 生命之泉治疗血量系数（万分比）
	MazeLifeRecoveryID                   uint32 = 12 // 生命之泉复活恢复生命系数（万分比）
	MazeMapRefreshCycleID                uint32 = 13 // 地图模板有效期
	MazeOpenBoxCostResID                 uint32 = 14 // 打开宝箱消耗的资源
	MazeSoulAlterBuffNumID               uint32 = 15 // 灵魂祭坛buff随机的数量
	MazeLimitEnemyMinNumID               uint32 = 17 // 开始极限将判定最少人数
	MazeLimitEnemyPowerRatioID           uint32 = 18 // 极限将战力占全体战力的比例
	MazeRoundNumMatchRobotID             uint32 = 20 // 每个玩家前n轮战斗事件匹配机器人
	MazeDemonSupervisorBuffID            uint32 = 21 // 恶魔督军加的buff
	MazeGoldenHandRateID                 uint32 = 23 // 点金手倍率加成
	MazeGuardMaxBattleRoundID            uint32 = 24 // 守卫最大战斗回合
	MazeGuardBattleCountID               uint32 = 25 // 守卫最大战斗次数 - 每轮迷宫
	MazeCommonLevelAlterLinkBuffNumID    uint32 = 29 // 普通等级祭坛联结buff保底数量
	MazeDifficultLevelAlterLinkBuffNumID uint32 = 30 // 困难等级祭坛联结buff保底数量
	MazeHellLevelAlterLinkBuffNumID      uint32 = 31 // 地狱等级祭坛联结buff保底数量
	MazeCommonLevelOpenSweepNeedNumID    uint32 = 32 // 普通等级开启扫荡需要通关的次数
	MazeDifficultLevelOpenSweepNeedNumID uint32 = 33 // 困难等级开启扫荡需要通关的次数
	MazeHellLevelOpenSweepNeedNumID      uint32 = 34 // 地狱等级开启扫荡需要通关的次数
	MazeMapNewRefreshCycleID             uint32 = 35
	MazeNewDaysID                        uint32 = 36
	Maze1Guess                           uint32 = 37
	Maze2Guess                           uint32 = 38
	Maze3Guess                           uint32 = 39
	Maze1BuffSuccess                     uint32 = 40
	Maze2BuffSuccess                     uint32 = 41
	Maze3BuffSuccess                     uint32 = 42
)

const (
	MazeSoulBuffType   uint32 = 1 // 表示灵魂祭坛的buff type
	MazeBuffCountLimit uint32 = 1 // 迷宫中的buff有场次限制; 1: 有次数限制 0: 没有限制
)

// 迷宫格子状态
const (
	MazeGridCompleted   uint32 = 1 // 格子已完成
	MazeGridNoCompleted uint32 = 0 // 格子未完成
)

// 迷宫buff tag
const (
	MazeBuffTagGain   uint32 = 1 // 增益buff
	MazeBuffTagReduce uint32 = 2 // 减益buff
)

// 迷宫生命之泉：生效类型
const (
	MazeLifeRecovery uint32 = 1
	MazeLifeCure     uint32 = 2
)

// 迷宫魔盒交互操作类型
const (
	MazeBoxQuit uint32 = 1 // 放弃
	MazeBoxOpen uint32 = 2 // 开启
)

// 迷宫任务等级奖励
const (
	MazeTaskLevelAwardTypeItem      uint32 = 1 // 道具
	MazeTaskLevelAwardTypePrivilege uint32 = 2 // 特权道具
)

// 迷宫模板等级
const (
	MazeMapLevelCommon    uint32 = 1 // 普通等级
	MazeMapLevelDifficult uint32 = 2 // 困难等级
	MazeMapLevelHell      uint32 = 3 // 地狱等级
)

// 迷宫
const (
	MazeGetPowerRankUserNum      uint32 = 10    // 从战力全局排行榜获取的人数
	MazeCycleOneHourSeconds             = 3600  // 每小时秒数
	MazeCycleOneDayHours                = 24    // 每天 24 小时
	MazeReceiveID                       = 10020 // 复生神像value
	MazePowerRatio                      = 1000  // 匹配到的敌人 clone 的主线整容战力超过它在全局排行榜中战力的系数
	MazeDemonSupervisorAppearTag uint32 = 1     // 恶魔督军出现tag
	MazeBlackMarketerShopID      uint32 = 301   // 迷宫黑市商人商店
	MazeToken                    uint32 = 9015  // 迷宫代币
)

// 迷宫buff修正属性关联类型
const (
	MazeAttrExploreBattle uint32 = 1 // 迷宫中的战斗事件(eventType: 3,4,5,11,13)
	MazeAttrExploreChoice uint32 = 2 // 迷宫中的选择事件(eventType: 12)
)

// 迷宫道具
const (
	MazeItemPurifyPotion uint32 = 60001 // 净化药水ID
	MazeItemRuleSword    uint32 = 60002 // 裁决之剑ID
	MazeItemTrueEye      uint32 = 60003 // 真视之眼ID
	MazeItemTrapRemover  uint32 = 60004 // 陷阱拆除器ID
	MazeItemTreasuryKey  uint32 = 60005 // 宝库钥匙ID
	MazeItemGoldenHand   uint32 = 60006 // 点金手ID
)

// 迷宫事件
const (
	MazeStartPoint       uint32 = 1  // 起点
	MazeRoad             uint32 = 2  // 路
	MazeLittleMonster    uint32 = 3  // 弱小敌人
	MazeCommonMonster    uint32 = 4  // 普通敌人
	MazeEliteMonster     uint32 = 5  // 精英敌人
	MazeLifeStream       uint32 = 6  // 生命之泉
	MazeSourAltar        uint32 = 7  // 灵魂祭坛
	MazeCurseLand        uint32 = 8  // 诅咒地块
	MazeScroll           uint32 = 9  // 古文残卷
	MazeTreasury         uint32 = 10 // 宝库
	MazeTreasuryGuard    uint32 = 11 // 宝库守卫
	MazeChoiceEvent      uint32 = 12 // 选择事件
	MazeBoss             uint32 = 13 // 大BOSS
	MazeBossAround       uint32 = 14 // BOSS周围的格子
	MazeObstacle         uint32 = 15 // 障碍物
	MazeGuessSilhouette  uint32 = 16 // 猜剪影
	MazeMagicBox         uint32 = 17 // 魔盒
	MazePurifyPotion     uint32 = 18 // 净化药水
	MazeRuleSword        uint32 = 19 // 裁决之剑
	MazeTrueEye          uint32 = 20 // 真视之眼
	MazeMagicCrystalOre  uint32 = 21 // 魔晶矿
	MazeTrapRemover      uint32 = 22 // 陷阱拆除器
	MazeBlessed          uint32 = 23 // 祝福者
	MazeDemonSupervisor  uint32 = 24 // 恶魔督军
	MazeGoldenHand       uint32 = 25 // 点金手
	MazeBlackMarketer    uint32 = 26 // 黑市商人
	MazeDifficultMonster uint32 = 27 // 极难敌人
)

var MazeEvent = map[uint32]struct{}{
	MazeStartPoint:       {},
	MazeRoad:             {},
	MazeLittleMonster:    {},
	MazeCommonMonster:    {},
	MazeEliteMonster:     {},
	MazeLifeStream:       {},
	MazeSourAltar:        {},
	MazeCurseLand:        {},
	MazeScroll:           {},
	MazeTreasury:         {},
	MazeTreasuryGuard:    {},
	MazeChoiceEvent:      {},
	MazeBoss:             {},
	MazeBossAround:       {},
	MazeObstacle:         {},
	MazeGuessSilhouette:  {},
	MazeMagicBox:         {},
	MazePurifyPotion:     {},
	MazeRuleSword:        {},
	MazeTrueEye:          {},
	MazeMagicCrystalOre:  {},
	MazeTrapRemover:      {},
	MazeBlessed:          {},
	MazeDemonSupervisor:  {},
	MazeGoldenHand:       {},
	MazeBlackMarketer:    {},
	MazeDifficultMonster: {},
}

var MazeEnemyEvent = map[uint32]struct{}{
	MazeLittleMonster:    {},
	MazeCommonMonster:    {},
	MazeEliteMonster:     {},
	MazeDifficultMonster: {},
}

// MazeExploreEvent 迷宫可探索事件
var MazeExploreEvent = map[uint32]struct{}{
	MazeLittleMonster:    {},
	MazeCommonMonster:    {},
	MazeEliteMonster:     {},
	MazeDifficultMonster: {},
	MazeTreasury:         {},
	MazeBoss:             {},
}

const (
	BattleParaSuckBloodMin   uint32 = 2013
	BattleParaSuckBloodMax   uint32 = 2014
	BattleParaHurtReflectMin uint32 = 2019
	BattleParaHurtReflectMax uint32 = 2020
)

// 境界(回忆)
const (
	MemoryChipNum uint32 = 5 // 回忆包括5个回忆点
	MemoryFirst   uint32 = 1 // 境界首个回忆
)

// 爬塔相关配置
const (
	TowerPassAwardsNum  int = 3
	TowerSweepAwardsNum int = 3
	TowerInitFloorCount int = 900

	TowerTypeMain uint32 = 1
)

// 合法的塔类型
var TowerLegalType = map[uint32]bool{
	TowerTypeMain: true,
}

// 技能pos
const (
	SkillPosOther int = iota - 1
	SkillPosNormal
	SkillPosActive1
	SkillPosActive2
	SkillPosPassive1
	SkillPosPassive2
	SkillPosPassive3
	SkillPosSeasonAddHero //赛季加成英雄技能
	SkillPosAwake         //觉醒技能
	SkillPosMax
)

// 神器
const (
	ArtifactInitialStar     = 1
	ArtifactInitialStrength = 1
	ArtifactInitialForge    = 0
)

// 宝石
const (
	GemBagMaxLimitId     uint32 = 36 // 宝石背包最大上限；config_info.xml: id=36 对应的 value
	GemLeftSlotUnlockID  uint32 = 41 // 宝石左槽位解锁ID；function_info.xml: id=41 对应的 value
	GemRightSlotUnlockID uint32 = 42 // 宝石右槽位解锁ID；function_info.xml: id=42 对应的 value
	GemComposeCostNum    uint32 = 3  // 宝石合成需要消耗材料宝石的数量
)

// 宝石属性类型
const (
	GemAttrTypeBasic    = 1 // 基础属性
	GemAttrTypeAdvanced = 2 // 高级属性
)

const (
	GemConvert             uint32 = 1 //  置换未保存
	GemConvertCleanTmpAttr uint32 = 2 //  置换后未保存，清除临时属性
	GemConvertAndSave      uint32 = 3 //  置换后保存

)

// 英雄职业
const (
	HeroJobAll       uint32 = 0 // 全职业
	HeroJobTank      uint32 = 1 // 坦克
	HeroJobMaster    uint32 = 2 // 法师
	HeroJobSoldier   uint32 = 3 // 战士
	HeroJobAuxiliary uint32 = 4 // 辅助
)

var LegalJob = map[uint32]struct{}{
	HeroJobAll:       {},
	HeroJobTank:      {},
	HeroJobMaster:    {},
	HeroJobSoldier:   {},
	HeroJobAuxiliary: {},
}

// 商店
const (
	ShopGoodsInitCap int = 8 //商品初始数量
)

type ShopExtType uint32

const (
	ShopExtTypeCarnival ShopExtType = 1 //商店扩展类型 - 嘉年华
	ShopExtTypeDebut    ShopExtType = 3 //商店扩展类型 - 英雄首发
)

type ShopLimitType uint32

const (
	ShopLimitTypeNo      ShopLimitType = 1 //限购类型 - 不限购
	ShopLimitTypeDaily   ShopLimitType = 2 //限购类型 - 日限购
	ShopLimitTypeWeekly  ShopLimitType = 3 //限购类型 - 周限购
	ShopLimitTypeForever ShopLimitType = 4 //限购类型 - 永久限购
	ShopLimitTypeMonth   ShopLimitType = 5 //限购类型 - 月限购
)

// 商店商品合法限购类型
var ShopLegalBuyLimitType = map[ShopLimitType]bool{
	ShopLimitTypeNo:      true,
	ShopLimitTypeDaily:   true,
	ShopLimitTypeWeekly:  true,
	ShopLimitTypeForever: true,
	ShopLimitTypeMonth:   true,
}

// 商品解锁的额外限制
const (
	ShopGoodsNoLimit                            uint32 = 0  // 商品没有额外限制
	ShopGoodsUserLevelLimit                     uint32 = 1  // 玩家等级
	ShopGoodsDungeonLimit                       uint32 = 2  // 商品主线限制
	ShopGoodsVipLevelLimit                      uint32 = 3  // vip等级限制
	ShopGoodsArtifactDebutLimit                 uint32 = 4  // 神器首发兑换商店
	ShopGoodsMirageTopLevelLimit                uint32 = 10 // 个人Boss最高通关层数限制
	ShopGoodsMirageEmpireTopLevelLimit          uint32 = 11 // 个人Boss帝国通关层数限制
	ShopGoodsMirageForestTopLevelLimit          uint32 = 12 // 个人Boss森林通关层数限制
	ShopGoodsMirageMoonTopLevelLimit            uint32 = 13 // 个人Boss月影通关层数限制
	ShopGoodsMirageProtossTopLevelLimit         uint32 = 14 // 个人Boss神使通关层数限制
	ShopGoodsMirageDemonTopLevelLimit           uint32 = 15 // 个人Boss魔裔通关层数限制
	ShopGoodsMirageSixTopLevelLimit             uint32 = 16 // 个人Boss第6个boss通关层数限制
	ShopGoodsGuildLevelLimit                    uint32 = 20 // 玩家公会等级
	ShopGoodsDisorderLandAllMapHurdleLevelLimit uint32 = 21 // 失序空间 - 所有地图战斗最高等级
	ShopGoodsSkinNotRepeatOwnership             uint32 = 22 // 皮肤 - 皮肤不能重复拥有
	ShopGoodsArtifactOwnershipAdvancedMazeToken uint32 = 23 // 神器 - 拥有高级迷宫币
	ShopGoodsSeasonDoorPassLevelLimit1          uint32 = 24 // 赛季通关装备门
	ShopGoodsSeasonDoorPassLevelLimit2          uint32 = 25 // 赛季通关材料门
	ShopGoodsSeasonMapExploreLimit              uint32 = 26 // 赛季地图探索限制
)

var shopGoodsLimit = map[uint32]struct{}{
	ShopGoodsNoLimit:                            {},
	ShopGoodsUserLevelLimit:                     {},
	ShopGoodsDungeonLimit:                       {},
	ShopGoodsVipLevelLimit:                      {},
	ShopGoodsMirageTopLevelLimit:                {},
	ShopGoodsMirageEmpireTopLevelLimit:          {},
	ShopGoodsMirageForestTopLevelLimit:          {},
	ShopGoodsMirageMoonTopLevelLimit:            {},
	ShopGoodsMirageProtossTopLevelLimit:         {},
	ShopGoodsMirageDemonTopLevelLimit:           {},
	ShopGoodsMirageSixTopLevelLimit:             {},
	ShopGoodsArtifactDebutLimit:                 {},
	ShopGoodsGuildLevelLimit:                    {},
	ShopGoodsDisorderLandAllMapHurdleLevelLimit: {},
	ShopGoodsSkinNotRepeatOwnership:             {},
	ShopGoodsArtifactOwnershipAdvancedMazeToken: {},
	ShopGoodsSeasonDoorPassLevelLimit1:          {},
	ShopGoodsSeasonDoorPassLevelLimit2:          {},
	ShopGoodsSeasonMapExploreLimit:              {},
}

var ShopGoodsLimitType2MirageCopyID = map[uint32]uint32{
	ShopGoodsMirageEmpireTopLevelLimit:  MirageRaceEmpire,
	ShopGoodsMirageForestTopLevelLimit:  MirageRaceForest,
	ShopGoodsMirageMoonTopLevelLimit:    MirageRaceMoon,
	ShopGoodsMirageProtossTopLevelLimit: MirageRaceProtoss,
	ShopGoodsMirageDemonTopLevelLimit:   MirageRaceDemon,
	ShopGoodsMirageSixTopLevelLimit:     MirageRaceSix,
}

// 嘉年华任务记录类型
const (
	CarnivalTaskEventRecordFromServiceOpen     uint32 = 0  // 从开服开始记录
	CarnivalTaskEventRecordFromCarnivalOpen    uint32 = 1  // 活动开始，开始记录
	CarnivalTaskEventRecordFromCarnivalDayOpen uint32 = 2  // 任务开启当天开始记录
	CarnivalTaskEventRecordFromSeasonEnter     uint32 = 3  // 玩家进入赛季首日开始记录
	CarnivalTaskScore                          uint32 = 59 // 嘉年华任务积分
	CarnivalSeasonEnterDay                     uint32 = 1  // 玩家进入赛季首日
)

// 成长大师类型
const (
	MasterEquipStrength  uint32 = 1 // 装备强化大师
	MasterEquipRefine    uint32 = 2 // 装备精炼大师
	MasterEmblemStrength uint32 = 3 // 纹章强化大师
	MasterEmblemBlessing uint32 = 4 // 纹章祝福大师
)

// 功勋任务记录类型
const (
	MedalTaskRecordTypeByServerOpen uint32 = 0 // 历史：从开服开始记录
	MedalTaskRecordTypeByLevelOpen  uint32 = 1 // 功勋下一级开始时记录
)

// 功勋领奖的类型
const (
	MedalReceiveAwardTypeByDaily uint32 = 1 // 每日奖励
	MedalReceiveAwardTypeByLevel uint32 = 2 // 功勋等级奖励
	MedalReceiveAwardTypeByTask  uint32 = 3 // task奖励
)

const MedalDailyAwardOpen = 1 // 功勋每日奖励开启

// 召唤(summon_type_info) group
const (
	BasicSummon             uint32 = 1  //普通召唤
	FriendshipSummon        uint32 = 2  //友情召唤
	AdvancedSummon          uint32 = 3  //高级召唤
	PointsSummon            uint32 = 4  //积分召唤
	AssocSummonEmpire       uint32 = 5  //先知召唤-帝国
	AssocSummonWoodland     uint32 = 6  //先知召唤-森林
	AssocSummonEclipse      uint32 = 7  //先知召唤-月影
	AssocSummonDemon        uint32 = 8  //先知召唤-魔裔
	AssocExchangeBaseOne    uint32 = 9  //先知兑换-基础种族1
	AssocExchangeBaseTwo    uint32 = 10 //先知兑换-基础种族2
	AssocExchangeBaseThree  uint32 = 11 //先知兑换-基础种族3
	AssocExchangeAdvanceOne uint32 = 12 //先知兑换-高级种族1
	AssocExchangeAdvanceTwo uint32 = 13 //先知兑换-高级种族2
	ArtifactSummon          uint32 = 14 //神器召唤
	ArtifactPoinsSummon     uint32 = 15 //神器积分召唤
	LinkSummon              uint32 = 17 //流派抽卡
)

const AdvancedSummonCount uint32 = 10 // 高级抽卡连抽次数

// 神器召唤
const (
	ArtifactFragmentGuaranteeCount uint32 = 5     // 神器抽卡碎片保底次数
	ArtifactFragmentGuaranteeClass uint32 = 11410 // 神器抽卡碎片保底卡池
	ArtifactSummonGetPointValue    uint32 = 9009  // 神器抽卡获得积分value
)

// 心愿单
const (
	WishListGroup             = AdvancedSummon //目前只有高级召唤有心愿单
	WishListSlotMaxCnt uint32 = 2              //心愿单格位数限制
)

// 抽卡group对应的funcID
var SummonGroupFuncID = map[uint32]uint32{
	BasicSummon:             uint32(common.FUNCID_MODULE_SUMMON),
	AdvancedSummon:          uint32(common.FUNCID_MODULE_SUMMON),
	FriendshipSummon:        uint32(common.FUNCID_MODULE_SUMMON),
	PointsSummon:            uint32(common.FUNCID_MODULE_SUMMON),
	ArtifactSummon:          uint32(common.FUNCID_MODULE_ARTIFACT_SUMMON),
	ArtifactPoinsSummon:     uint32(common.FUNCID_MODULE_ARTIFACT_SUMMON),
	AssocSummonEmpire:       uint32(common.FUNCID_MODULE_ASSOC),
	AssocSummonWoodland:     uint32(common.FUNCID_MODULE_ASSOC),
	AssocSummonEclipse:      uint32(common.FUNCID_MODULE_ASSOC),
	AssocSummonDemon:        uint32(common.FUNCID_MODULE_ASSOC),
	AssocExchangeBaseOne:    uint32(common.FUNCID_MODULE_ASSOC),
	AssocExchangeBaseTwo:    uint32(common.FUNCID_MODULE_ASSOC),
	AssocExchangeBaseThree:  uint32(common.FUNCID_MODULE_ASSOC),
	AssocExchangeAdvanceOne: uint32(common.FUNCID_MODULE_ASSOC),
	AssocExchangeAdvanceTwo: uint32(common.FUNCID_MODULE_ASSOC),
}

var SummonGroupRoundActivity = map[uint32]uint32{
	AdvancedSummon:      RoundActivityTypeAdvanced,
	AssocSummonEmpire:   RoundActivityTypeAssoc,
	AssocSummonWoodland: RoundActivityTypeAssoc,
	AssocSummonEclipse:  RoundActivityTypeAssoc,
	AssocSummonDemon:    RoundActivityTypeAssoc,
	ArtifactSummon:      RoundActivityTypeArtifact,
	LinkSummon:          RoundActivityTypeLink,
}

// 需要想前端显示剩余保底次数的分组
// 高级抽，先知抽，神魔抽
var SummonNeedShowLeftCountGroup = []uint32{AdvancedSummon, AssocSummonEmpire, AssocSummonWoodland, AssocSummonEclipse, AssocSummonDemon}

const (
	RoundActivityTypeAdvanced = 1
	RoundActivityTypeAssoc    = 2
	RoundActivityTypeArtifact = 3
	RoundActivityTypeLink     = 4
)

const (
	GrpcShopID = 102 // gm grpc 使用的商店id
)

// 水晶
const (
	ContractStageMaxCount = 15 //纹章缔约阶级最大数量
	ShareEmblemCount      = 2  //共享纹章属性的个数
	StarLimitMinStarLv    = 10 //不可随意改动！！！ 最小限制星级，同时也是水晶同时拥有类成就的最小星级
)

// 聊天
var (
	ChatLocalWorld   uint32 = 1 // 世界聊天
	ChatGuild        uint32 = 2 // 公会聊天
	ChatPrivate      uint32 = 3 // 私聊
	ChatSystem       uint32 = 4 // 系统频道
	ChatAllWorld     uint32 = 5 // 全服世界聊天
	ChatPartition    uint32 = 6 // 战区聊天
	ChatGuildDungeon uint32 = 7 // 公会副本聊天
	ChatGST          uint32 = 8 // 公会战聊天
	ChatSystemGuild  uint32 = 9 // 公会系统频道

	//ChatMinCD        uint32 = 2   // 最小CD时间
	//ChatMaxMsgLen    uint32 = 300 // 最大消息长度
	//ChatMaxRecordNum uint32 = 100 // 最大保存消息数量
)

// vip特权
const (
	VipPrivilegeShopId = 102 //神秘商店
)

const OneDaySeconds uint32 = 86400 // 1天的秒数

// 月卡 - 特权
const (
	MonthlyCardDungeonSpeed        uint32 = 1 // 加速挂机免费次数
	MonthlyCardDispatchPurple      uint32 = 2 // 悬赏任务必出紫任务次数
	MonthlyCardDispatchOrange      uint32 = 3 // 悬赏任务必出橙任务次数
	MonthlyCardForestBox           uint32 = 4 // 密林收获宝箱额外数量
	MonthlyCardMazeToken           uint32 = 5 // 迷宫代币额外收益
	MonthlyCardDispatchMaxQuality1 uint32 = 6 //第一次刷新当前组最高品质
	MonthlyCardDispatchMaxQuality2 uint32 = 7 //第二次刷新当前组最高品质
	MonthlyCardFlowerBox           uint32 = 8 // 新密林收获宝箱的额外数量
	MonthlyCardGoddessTouchCount   uint32 = 9 //
)

const (
	MonthlyCardMazeSweep      uint32 = 1 // 月卡拥有迷宫扫荡特权
	MonthlyCardAsistantUnlock uint32 = 1 // 月卡拥有解锁小助手特权
)

// 月卡 - 类型
const (
	MonthlyCardTypeGrowUp uint32 = 1 // 成长月卡
	MonthlyCardTypeSupply uint32 = 2 // 补给月卡
)

const MonthlyCardMailIntervalDay uint32 = 3 // 月卡即将过期发邮件临界值

// 战令
const (
	PassDungeon        uint32 = 1    // 主线战令
	PassTower          uint32 = 2    // 地宫战令
	PassDailyActive    uint32 = 11   // 日常活跃战令
	PassMazeActive     uint32 = 12   // 迷宫活跃战令
	PassDiamondActive  uint32 = 13   // 钻石活跃战令
	PassProphetActive  uint32 = 14   // 先知活跃战令
	PassActivityStory1 uint32 = 1000 // 人鱼战令1
	PassSeason         uint32 = 2001 // S0 永恒战令
	PassChristmas      uint32 = 1001 // 圣诞节战令
	PassSeasonS1       uint32 = 2002 // S1 赛季战令
)

// 战令 - 周期任务重置类型
const (
	PassCycleTaskResetDaily       = 1 // 每日重置
	PassCycleTaskResetEveryMonday = 2 // 每周一重置
	PassActivityStoryNoReset      = 3 // 活动期间不重置
)

const (
	PassTypeOnce       = 1
	PassTypeCycle      = 2
	PassTypeActivity   = 3
	PassTypeSeason     = 4
	PassTypeDungeon    = 5  // 主线战令
	PassTypeTower      = 6  // 地宫战令
	PassTypeOpenServer = 7  // 开服
	PassTypeCreateU    = 8  // 创角
	PassTypeUserLevel  = 9  // 玩家等级
	PassTypeSkin       = 10 // 皮肤战令
	PassTypeSeasonWeek = 11 // 赛季周期战令
)

var legalPassType = map[uint32]passCheck{
	PassTypeOnce:       passOnceCheck,
	PassTypeCycle:      passCycleCheck,
	PassTypeActivity:   passActivityStoryCheck,
	PassTypeSeason:     passSeasonCheck,
	PassTypeDungeon:    passOnceCheck,
	PassTypeTower:      passOnceCheck,
	PassTypeOpenServer: passOpenServerCheck,
	PassTypeCreateU:    passCreateUserCheck,
	PassTypeUserLevel:  passOnceCheck,
	PassTypeSkin:       passActivityStoryCheck,
	PassTypeSeasonWeek: passSeasonWeekCheck,
}

type passCheck func(*PassInfo, *PassInfoManager) *PassInfoExt

// 是否是周期战令
func IsCyclePass(ext *PassInfoExt) bool {
	if ext.PassType == PassTypeOnce || ext.PassType == PassTypeDungeon || ext.PassType == PassTypeTower || ext.PassType == PassTypeUserLevel {
		return false
	}
	return true
}

const PassBastFloat float64 = 100 // 等级购买计算消耗的系数

// 通用机器人
const (
	BotNumPerLevel int = 3 //每个等级机器人数量
)

const (
	SecondPerMinute = 60
)

const (
	TeamMaxPos int = 5
)

const (
	PushGiftRefreshTypeNone  = 0
	PushGiftRefreshTypeDaily = 1
)

const (
	PushGiftTypeSingleBuy = 1
	PushGiftTypeGroupBuy  = 2
)

var PushGiftType = map[uint32]struct{}{
	PushGiftTypeSingleBuy: {},
	PushGiftTypeGroupBuy:  {},
}

// 活动充值商店开启类型
const (
	ActivityRechargeUserDayOpen       uint32 = 1
	ActivityRechargeServerDayOpen     uint32 = 2
	ActivityRechargeArtifactDebut     uint32 = 3  // 神器首发
	ActivityRechargeDivineDemon       uint32 = 4  // 神魔抽卡
	ActivityRechargeLinkSummon        uint32 = 5  // 流派抽卡
	ActivityRechargeDropActivity      uint32 = 6  // 掉落活动
	ActivityRechargeActivityStory     uint32 = 7  // 夏日活动
	ActivityRechargeActivityReturn    uint32 = 8  // 回流活动
	ActivityRechargeNewServerPushGift uint32 = 9  // 新服推送礼包
	ActivityRechargePyramid           uint32 = 10 // 金字塔活动
	ActivityRechargeActivityTurnTable uint32 = 11 // 周年庆活动
	ActivityRechargeActivityPuzzle    uint32 = 12 // 活动拼图
	ActivityRechargeActivityDrop      uint32 = 13 // 活动集合 掉落活动
	ActivityRechargeActivitySelect    uint32 = 14 // 选择抽卡
	ActivityRechargeActivityFeed      uint32 = 15 // 喂养活动 - 圣诞节
	ActivityRechargeActivityShoot     uint32 = 16 // 射击活动
	ActivityRechargePokemonSummon     uint32 = 17 // 宠物抽卡
)

const (
	ActivityRechargeLinkSummonShopId uint32 = 301
)

// 活动充值商店 - 活动内购买限制类型
const (
	ActivityRechargeShopArtifactDebutDraw uint32 = 1 //神器首发累抽
	ActivityRechargeShopDivineDemonDraw   uint32 = 2 // 神魔抽卡累抽
)

const (
	ActivityRechargeGiftTypeNormal   uint32 = 0
	ActivityRechargeGiftTypeOneClick uint32 = 1
)

const (
	BaseFilterNoInherit            uint32 = 0
	BaseFilterInheritStage1Alive   uint32 = 1
	BaseFilterInheritStage2Alive   uint32 = 2
	BaseFilterInheritStage3Alive   uint32 = 3
	BaseFilterInheritStageAllAlive uint32 = 4
	BaseFilterInheritAttackAlive   uint32 = 5
	BaseFilterInheritDefenseAlive  uint32 = 6
	BaseFilterInheritPsStageAlive  uint32 = 7
)

var gSkillTargetInfoInherit = map[uint32]struct{}{
	BaseFilterNoInherit:            {},
	BaseFilterInheritStage1Alive:   {},
	BaseFilterInheritStage2Alive:   {},
	BaseFilterInheritStage3Alive:   {},
	BaseFilterInheritStageAllAlive: {},
	BaseFilterInheritAttackAlive:   {},
	BaseFilterInheritDefenseAlive:  {},
	BaseFilterInheritPsStageAlive:  {},
}

const (
	BaseFilterOpTeamAlive                     uint32 = 0
	BaseFilterSelfTeamAlive                   uint32 = 1
	BaseFilterOpTeamAliveNoSelf               uint32 = 2
	BaseFilterSelfTeamAll                     uint32 = 3
	BaseFilterSelfTeamDeadNoNoAliveBuff       uint32 = 4
	BaseFilterAlive                           uint32 = 5
	BaseFilterOpTeamArtifact                  uint32 = 6
	BaseFilterSelfTeamArtifact                uint32 = 7
	BaseFilterArtifact                        uint32 = 8
	BaseFilterSelfTeamDeadNoNoAliveBuffNoSelf uint32 = 9
	baseFilterOpTeamEmptyPos                  uint32 = 10
	baseFilterSelfTeamEmptyPos                uint32 = 11
	baseFilterOpTeamAllPos                    uint32 = 12
	baseFilterSelfTeamAllPos                  uint32 = 13
	baseFilterSelfTeamAllPosNoSelf            uint32 = 14
	baseFilterSelfTeamSameRowPosNoSelf        uint32 = 15
	baseFilterSelfTeamEmptyPosAndCall         uint32 = 16
	baseFilterSelfTeamHide                    uint32 = 17
	baseFilterOpTeamHide                      uint32 = 18
	baseFilterOpTeamAliveIncludeExile         uint32 = 19
	baseFilterBuffOwner                       uint32 = 20
	baseFilterDead                            uint32 = 21
	baseFilterHide                            uint32 = 22
)

var gSkillTargetInfoTargetType = map[uint32]struct{}{
	BaseFilterOpTeamAlive:                     {},
	BaseFilterSelfTeamAlive:                   {},
	BaseFilterOpTeamAliveNoSelf:               {},
	BaseFilterSelfTeamAll:                     {},
	BaseFilterSelfTeamDeadNoNoAliveBuff:       {},
	BaseFilterAlive:                           {},
	BaseFilterOpTeamArtifact:                  {},
	BaseFilterSelfTeamArtifact:                {},
	BaseFilterArtifact:                        {},
	BaseFilterSelfTeamDeadNoNoAliveBuffNoSelf: {},
	baseFilterOpTeamEmptyPos:                  {},
	baseFilterSelfTeamEmptyPos:                {},
	baseFilterOpTeamAllPos:                    {},
	baseFilterSelfTeamAllPos:                  {},
	baseFilterSelfTeamAllPosNoSelf:            {},
	baseFilterSelfTeamSameRowPosNoSelf:        {},
	baseFilterSelfTeamEmptyPosAndCall:         {},
	baseFilterSelfTeamHide:                    {},
	baseFilterOpTeamHide:                      {},
	baseFilterOpTeamAliveIncludeExile:         {},
	baseFilterBuffOwner:                       {},
	baseFilterDead:                            {},
	baseFilterHide:                            {},
}

const (
	BaseChooseConditionNo                    uint32 = 0
	BaseChooseConditionBuffType              uint32 = 1
	BaseChooseConditionRace                  uint32 = 2
	BaseChooseConditionJob                   uint32 = 3
	BaseChooseConditionPosition              uint32 = 4
	BaseChooseConditionControl               uint32 = 5
	BaseChooseConditionMonsterType           uint32 = 6
	BaseChooseConditionLink                  uint32 = 7
	BaseChooseConditionHeroSysId             uint32 = 8
	BaseChooseConditionExcludeHeroSysId      uint32 = 9
	BaseChooseConditionExcludeBuffType       uint32 = 10
	BaseChooseConditionExcludeAllCallMonster uint32 = 11
	BaseChooseConditionSex                   uint32 = 12
	baseChooseConditionExcludeAttrHigh       uint32 = 13
	baseChooseConditionExcludeAttrLow        uint32 = 14
)

var gSkillTargetInfoCondition = map[uint32]struct{}{
	BaseChooseConditionNo:                            {},
	BaseChooseConditionBuffType:                      {},
	BaseChooseConditionRace:                          {},
	BaseChooseConditionJob:                           {},
	BaseChooseConditionPosition:                      {},
	BaseChooseConditionControl:                       {},
	BaseChooseConditionMonsterType:                   {},
	BaseChooseConditionLink:                          {},
	BaseChooseConditionHeroSysId:                     {},
	BaseChooseConditionExcludeHeroSysId:              {},
	BaseChooseConditionExcludeBuffType:               {},
	BaseChooseConditionExcludeAllCallMonster:         {},
	BaseChooseConditionSex:                           {},
	baseChooseConditionExcludeAttrHigh:               {},
	baseChooseConditionExcludeAttrLow:                {},
	baseChooseConditionExcludeSysIDSameWithSelfAlive: {},
}

const (
	BaseChooseSortDefault                            uint32 = 0
	BaseChooseSortRandom                             uint32 = 1
	BaseChooseSortRandomExclude                      uint32 = 2
	BaseChooseSortByAttackPosition                   uint32 = 3
	BaseChooseSortAttrHighToLow                      uint32 = 4
	BaseChooseSortAttrLowToHigh                      uint32 = 5
	BaseChooseSortBuffNumHighToLow                   uint32 = 6
	BaseChooseSortBuffNumLowToHigh                   uint32 = 7
	BaseChooseSortDuplicatesRandom                   uint32 = 8
	BaseChooseSortAttackPriority                     uint32 = 9
	BaseChooseSortTeamPriority                       uint32 = 10
	baseChooseSortEmptyPosPriority                   uint32 = 11
	baseChooseSortCallPosPriority                    uint32 = 12
	BaseChooseSortBuffBigTypeNumHighToLow            uint32 = 13
	BaseChooseSortBuffBigTypeNumLowToHigh            uint32 = 14
	baseChooseConditionExcludeSysIDSameWithSelfAlive uint32 = 15
)

var gSkillTargetInfoSort = map[uint32]struct{}{
	BaseChooseSortDefault:                 {},
	BaseChooseSortRandom:                  {},
	BaseChooseSortRandomExclude:           {},
	BaseChooseSortByAttackPosition:        {},
	BaseChooseSortAttrHighToLow:           {},
	BaseChooseSortAttrLowToHigh:           {},
	BaseChooseSortBuffNumHighToLow:        {},
	BaseChooseSortBuffNumLowToHigh:        {},
	BaseChooseSortDuplicatesRandom:        {},
	BaseChooseSortAttackPriority:          {},
	BaseChooseSortTeamPriority:            {},
	baseChooseSortEmptyPosPriority:        {},
	baseChooseSortCallPosPriority:         {},
	BaseChooseSortBuffBigTypeNumHighToLow: {},
	BaseChooseSortBuffBigTypeNumLowToHigh: {},
}

const (
	ReviseAddCount      uint32 = 0
	ReviseSameRow       uint32 = 1
	ReviseSameRowNoSelf uint32 = 2
	ReviseNotSameRow    uint32 = 3
	ReviseAround        uint32 = 4
	ReviseAroundNoSelf  uint32 = 5
	ReviseNotAround     uint32 = 6
	ReviseBack          uint32 = 7
	ReviseFront         uint32 = 8
)

var gSkillTargetInfoAdded = map[uint32]struct{}{
	ReviseAddCount:      {},
	ReviseSameRow:       {},
	ReviseSameRowNoSelf: {},
	ReviseNotSameRow:    {},
	ReviseAround:        {},
	ReviseAroundNoSelf:  {},
	ReviseNotAround:     {},
	ReviseBack:          {},
	ReviseFront:         {},
}

const (
	ReviseSortRandom                    uint32 = 1
	ReviseSortByAttackPosition          uint32 = 2
	ReviseSortByPos                     uint32 = 3
	ReviseSortByBuffBigTypeNumHighToLow uint32 = 4
	ReviseSortByBuffBigTypeNumLowToHigh uint32 = 5
)

// 战斗参数key
const (
	BaseHitParaID                    uint32 = 1001 //基础命中
	BaseCritParaID                   uint32 = 1002 //基础暴击
	BaseCritDamParaID                uint32 = 1005 //基础爆伤
	PowerSpeedParaID                 uint32 = 3001 //速度战力系数
	PowerAttackParaID                uint32 = 3002 //攻击战力系数
	PowerFixedDamParaID              uint32 = 3003 //固定伤害战力系数
	PowerHpParaID                    uint32 = 3004 //生命战力系数
	PowerPhyDefParaID                uint32 = 3005 //物防战力系数
	PowerMagDefParaID                uint32 = 3006 //魔防战力系数
	PowerFixedDamReduceParaID        uint32 = 3007 //固定免伤战力系数
	PowerHitRateParaID               uint32 = 3008 //命中率战力系数
	PowerCritRateParaID              uint32 = 3009 //暴击率战力系数
	PowerDamAddParaID                uint32 = 3010 //伤害加成战力系数
	PowerControlRateParaID           uint32 = 3011 //控制战力系数
	PowerControlReduceRateParaID     uint32 = 3012 //免控战力系数
	PowerDodgeRateParaID             uint32 = 3013 //闪避率战力系数
	PowerCritReduceRateParaID        uint32 = 3014 //抗暴率战力系数
	PowerDamReduceParaID             uint32 = 3015 //伤害减免战力系数
	PowerHealRateParaID              uint32 = 3016 //治疗战力系数
	PowerGetHealRateParaID           uint32 = 3017 //受疗战力系数
	PowerSuckBloodRateParaID         uint32 = 3018 //吸血战力系数
	PowerSuckBloodReduceRateParaID   uint32 = 3019 //吸血抗性战力系数
	PowerCritDamAddRateParaID        uint32 = 3020 //爆伤战力系数
	PowerCritDamReduceRateParaID     uint32 = 3021 //爆伤抗性战力系数
	PowerDefPunctureRateParaID       uint32 = 3022 //无视防御战力系数
	PowerDefPunctureReduceRateParaID uint32 = 3023 //无视防御抗性战力系数
	PowerDamReflectRateParaID        uint32 = 3024 //伤害反弹战力系数
	PowerDamReflectReduceRateParaID  uint32 = 3025 //伤害反弹抗性战力系数
	PowerSpeedFixParaID              uint32 = 4001 //速度战力修正
	BattleReflectFixRate             uint32 = 1013 //反弹修正系数
	FixCritDamRateLow                uint32 = 2015 //暴伤倍率最低
	FixCritDamRateHigh               uint32 = 2016 //暴伤倍率最高
)

// 消息模板类型；对应chat_admin_info.xml的type字段
const (
	ChatTypeWorld                                uint32 = 1  // 世界聊天
	ChatTypeGuild                                uint32 = 2  // 公会聊天
	ChatTypePrivate                              uint32 = 3  // 私聊
	ChatTypeGuildJoin                            uint32 = 4  // 公会加入
	ChatTypeGuildRecruit                         uint32 = 5  // 公会招募
	ChatTypeAdvancedSummonSingle                 uint32 = 6  // 高级招募单抽
	ChatTypeAdvancedSummonMulti                  uint32 = 7  // 高级招募10连抽
	ChatTypeBasicSummonSingle                    uint32 = 8  // 基础招募单抽
	ChatTypeBasicSummonMulti                     uint32 = 9  // 基础招募10连抽
	ChatTypeArtifactSummonSingle                 uint32 = 10 // 神器招募单抽
	ChatTypeArtifactSummonMulti                  uint32 = 11 // 神器招募5连抽
	ChatTypeArtifactPointSummon                  uint32 = 12 // 神器积分招募
	ChatTypeArenaWin                             uint32 = 13 // 竞技场连胜10的倍数
	ChatTypeArenaRank                            uint32 = 14 // 竞技场排名第一
	ChatTypeMedalLevelUp                         uint32 = 15 // 功勋升级
	ChatTypeFirstCome                            uint32 = 16 // 占位
	ChatTypeDispatchMoreThanOrange               uint32 = 17 // 悬赏刷新橙色及以上任务
	ChatTypeDivineDemonSummon                    uint32 = 18 // 神魔抽卡 - 抽卡
	ChatTypeDebutShopBuyHero                     uint32 = 19 // 英雄首发 - 商店兑换英雄
	ChatTypeGuildDungeonBoxOpenAmazing           uint32 = 21 // 公会副本 - 宝箱开出大奖
	ChatTypeGuildDungeonChapterThrough           uint32 = 22 // 公会副本 - 章节通关
	ChatTypeFlowerOccupyBestFlowerbed            uint32 = 23 // 密林据点模式 - 占领最高级据点花坛
	ChatTypeFlowerOccupyBestJungle               uint32 = 24 // 密林据点模式 - 公会占领最高级据点丛林
	ChatTypeFlowerOccupyByAlly                   uint32 = 25 // 密林据点模式 - 公会成员占领任意据点花坛
	ChatTypeFlowerShareFlowerbed                 uint32 = 26 // 密林据点模式 - 分享花坛位置
	ChatTypeMonthTasksPoints                     uint32 = 27 // 全民无双 - 达到指定积分
	ChatTypeArtifactDebutDraw                    uint32 = 28 // 神器首发 - 抽到橙色/红色神器
	ChatTypeArtifactDebutPointsExchange          uint32 = 30 // 神器首发 - 积分兑换神器
	ChatTypeLinkSummonSingle                     uint32 = 31 //流派抽卡 - 单抽
	ChatTypeLinkSummonMulti                      uint32 = 32 //流派抽卡 - 连抽
	ChatGuildDungeonSeasonTop3Guild              uint32 = 34 // 公户副本 - 赛季结束最高公会
	ChatTypeGuildDungeonNewSeasonForArena        uint32 = 35 // 公会副本 - 战区进入新赛季
	ChatTypeGuildDungeonNewSeasonForGuild        uint32 = 36 // 公会副本 - 副本进入新赛季
	ChatGuildDungeonSeasonTopDivision            uint32 = 37 // 公会副本 - 赛季最高段位
	ChatGuildDungeonSetFocus                     uint32 = 38 // 公会副本 - 设置集火Id
	ChatGuildDungeonRoomRankUp                   uint32 = 39 // 公会副本 - 房间内排名上升
	ChatGuildDungeonRoomRankDown                 uint32 = 40 // 公会副本 - 房间内排名下降
	ChatGuildDungeonSignUp                       uint32 = 41 // 公会副本 - 报名
	ChatChestNewChest                            uint32 = 42 // 新公会 - 公会宝箱
	ChatWorldBossPartitionRankSettle             uint32 = 43 // 世界boss - 玩家对应的排行榜结算
	ChatWorldBossPartitionRankFirstChange        uint32 = 44 // 世界boss - 战区排行榜第1改变
	ChatGuildDungeonUseStrategyAddChallengeCount uint32 = 51 // 公会副本 - 使用秘技增加挑战次数
	ChatGuildDungeonUseStrategyRestoreBoss       uint32 = 52 // 公会副本 - 使用秘技复活Boss
	ChatGuildDungeonUseStrategyAddDamage         uint32 = 53 // 公会副本 - 使用秘技增加伤害
	ChatPeakTop1Change                           uint32 = 55 // 巅峰竞技场 - 赛季积分榜头名变动
	ChatPeakPhaseTop1                            uint32 = 56 // 巅峰竞技场 - 小周期第一名
	ChatPeakSeasonTop1                           uint32 = 57 // 巅峰竞技场 - 赛季第一名
	ChatGstGuildGetStronghold                    uint32 = 60 // 公会战 - 占领要塞
	ChatGstBlessGuildChange                      uint32 = 61 // 公会战 - 羁绊祈福公会改变
	ChatGstMainBuildLevel10                      uint32 = 62 // 公会战 - 王城升到10级
	ChatGstMainBuildLevel20                      uint32 = 63 // 公会战 - 王城升到20级
	ChatGuildGstBossSingleDie                    uint32 = 64 // 公会boss战 - 公会频道单个boss死亡
	ChatGstBossSingleDie                         uint32 = 65 // 公会boss战 - 公会战频道单个boss死亡
	ChatGstBossAllFail                           uint32 = 66 // 公会boss战 - 公会战频道战斗失败
	ChatGstFightWins                             uint32 = 68 // 公会战 - 地块玩家连胜推送
	ChatGstChallengeWins                         uint32 = 69 // 公会战-新擂台赛连胜推送
)

var ChatNeedChangeSendID = map[uint32]struct{}{
	ChatTypeGuildJoin:                            {},
	ChatGuildDungeonSeasonTop3Guild:              {},
	ChatTypeGuildDungeonNewSeasonForArena:        {},
	ChatTypeGuildDungeonNewSeasonForGuild:        {},
	ChatGuildDungeonSeasonTopDivision:            {},
	ChatGuildDungeonRoomRankUp:                   {},
	ChatGuildDungeonRoomRankDown:                 {},
	ChatWorldBossPartitionRankSettle:             {},
	ChatWorldBossPartitionRankFirstChange:        {},
	ChatGuildDungeonUseStrategyAddChallengeCount: {},
	ChatGuildDungeonUseStrategyRestoreBoss:       {},
	ChatGuildDungeonUseStrategyAddDamage:         {},
	ChatGstGuildGetStronghold:                    {},
	ChatGstBlessGuildChange:                      {},
	ChatGstMainBuildLevel10:                      {},
	ChatGstMainBuildLevel20:                      {},
	ChatGuildGstBossSingleDie:                    {},
	ChatGstBossSingleDie:                         {},
	ChatGstBossAllFail:                           {},
	ChatGstFightWins:                             {},
	ChatGstChallengeWins:                         {},
}

var chatTypePool = map[uint32]struct{}{
	ChatTypeWorld:                  {},
	ChatTypeGuild:                  {},
	ChatTypePrivate:                {},
	ChatTypeGuildJoin:              {},
	ChatTypeGuildRecruit:           {},
	ChatTypeAdvancedSummonSingle:   {},
	ChatTypeAdvancedSummonMulti:    {},
	ChatTypeBasicSummonSingle:      {},
	ChatTypeBasicSummonMulti:       {},
	ChatTypeArtifactSummonSingle:   {},
	ChatTypeArtifactSummonMulti:    {},
	ChatTypeArtifactPointSummon:    {},
	ChatTypeArenaWin:               {},
	ChatTypeArenaRank:              {},
	ChatTypeMedalLevelUp:           {},
	ChatTypeFirstCome:              {},
	ChatTypeDispatchMoreThanOrange: {},
	ChatTypeDivineDemonSummon:      {},
	ChatTypeDebutShopBuyHero:       {},
}

const RobotLowestRatio int64 = 100
const MaxTop5UpdateNum int = 10

const (
	DispatchLevelUpClear = 0
	DispatchLevelUpKeep  = 1
)

var DispatchTaskRecordType = map[uint32]uint32{
	uint32(common.TASK_TYPE_ID_TTYPE_ID_MAIN_DUNGEON_FLOOR):            DispatchLevelUpKeep,
	uint32(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_ANY_DISPATCH_TASK):    DispatchLevelUpClear,
	uint32(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_GREEN_DISPATCH_TASK):  DispatchLevelUpClear,
	uint32(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_BLUE_DISPATCH_TASK):   DispatchLevelUpClear,
	uint32(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_PURPLE_DISPATCH_TASK): DispatchLevelUpClear,
	uint32(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_ORANGE_DISPATCH_TASK): DispatchLevelUpClear,
	uint32(common.TASK_TYPE_ID_TTYPE_ID_COMPLETE_RED_DISPATCH_TASK):    DispatchLevelUpClear,
}

const (
	ServerStatusFluency  = 1 // 流畅
	ServerStatusHot      = 2 // 火热
	ServerStatusFull     = 3 // 爆满
	ServerStatusMaintain = 4 // 维护
)

const (
	CompareEqual    = 0
	CompareMoreThan = 1
)

const (
	TIME_LAYOUT = "2006-01-02 15:04:05"
)

// 神树争霸
const (
	WrestleCommonRoomCapacity  = 10  //普通房间（非顶级战场）人数限制
	WrestleTopFighterCount     = 100 //排行榜上榜人数
	WrestleTopRoomEnemyLimit   = 10  //顶级战场，可打人数上限
	WrestleTopRoomInitCapacity = 100 //顶级战场初始容量

	WrestleBotNumPerLevel int    = 10 //跨服战每个等级机器人数量
	WrestleLogMaxNum      uint32 = 10 //战斗记录最大数量
	WrestleSeasonDuration int64  = 7  //赛季周期 - 7天

	WrestleChampionAvatar1  uint32 = 1  //赛季冠军连胜特殊奖励avatar - 连胜1次
	WrestleChampionAvatar3  uint32 = 3  //赛季冠军连胜特殊奖励avatar - 连胜3次
	WrestleChampionAvatar10 uint32 = 10 //赛季冠军连胜特殊奖励avatar - 连胜10次

	WrestleRewardTypeDaily  = 1 //奖励类型 - 每日
	WrestleRewardTypeSeason = 2 //奖励类型 - 每赛季
)

// 神树争霸 - 赛季冠军连胜特殊奖励avatar，次数列表
var WrestleChampionAvatarList = []uint32{
	WrestleChampionAvatar1,
	WrestleChampionAvatar3,
	WrestleChampionAvatar10,
}

// 神树争霸 - 发奖类型
var WrestleRewardType = []uint32{
	WrestleRewardTypeDaily,
	WrestleRewardTypeSeason,
}

// 契约之所
const (
	GoddessInitLevel         = 0
	GoddessContractInitLevel = 0
)

// OpenType : 英雄首发开启类型
type OpenType uint32

const (
	DebutHaveHall OpenType = 1 // 有活动大厅
	DebutNoHall   OpenType = 2 // 没有活动大厅
)

const (
	DebutSummonGroupID uint32 = 16  // 英雄抽卡 group
	DebutBagShopType          = 201 // 英雄首发的礼包商店type
)

// 养成被动技能类型
const (
	RaisePsAttrTypePs           uint32 = 0 //被动技能
	RaisePsAttrTypeLink         uint32 = 1 //联结技能等级
	RaisePsAttrTypeArtifactLink uint32 = 2 //神器联结技能等级
)

const (
	EquipmentEnchantRandomLen = 10
)

const GuildTalentRootNode = 1 // 公会天赋树根节点

// 公会天赋树第二层6个主分支
const (
	GuildTalentSecondFloorFirst  = 2
	GuildTalentSecondFloorSecond = 3
	GuildTalentSecondFloorThird  = 4
	GuildTalentSecondFloorFourth = 5
	GuildTalentSecondFloorFifth  = 6
	GuildTalentSecondFloorSixth  = 7
)

const (
	GuildTalentConnectTypeAnd uint32 = 1 // 且，前置节点都要满足限制条件
	GuildTalentConnectTypeOr  uint32 = 2 // 或，有一个满足即可
)

// 天赋树层数
const (
	GuildTalentFloorFirst  uint32 = 1
	GuildTalentFloorSecond uint32 = 2
	GuildTalentFloorThird  uint32 = 3
)

// 密林 - 0.9.8
const (
	FlowerDefaultFirstLv uint32 = 1
	FlowerLogMaxNum      uint32 = 20 //日志数量上限
	FlowerGuideStep      uint32 = 3
	FlowerLootResColNum         = 2 //flower_plant_reward_info.xml中奖励的资源，仅前两组可被掠夺
	FlowerRobotPlantRare uint32 = 4 //机器人花的等级
	FlowerPlantRareCount        = 4 //花品质种类

	//密林升级方式
	FlowerLvUpTypeCostExp   = 1
	FlowerLvUpTypeBeatGuard = 2

	//花坛pos
	FlowerbedPosMin    uint32 = 0
	FlowerbedPosFirst  uint32 = 1
	FlowerbedPosSecond uint32 = 2
	FlowerbedPosThird  uint32 = 3
	FlowerbedPosFourth uint32 = 4
	FlowerbedPosFifth  uint32 = 5
	FlowerbedPosMax    uint32 = 6

	//公会加成类型
	FlowerGuildBuffThree uint32 = 3
	FlowerGuildBuffFour  uint32 = 4
	FlowerGuildBuffFive  uint32 = 5
)

// 密林 - 据点模式 - 花坛pos是否合法
func IsLegalFlowerbedPos(pos uint32) bool {
	if pos > FlowerbedPosMin && pos < FlowerbedPosMax {
		return true
	}
	return false
}

func FlowerbedPosTotalCount() uint32 {
	return FlowerbedPosMax - 1
}

// 密林 - 据点模式 - 公会加成类型合法性
func IsLegalFlowerbedGuildBuff(count uint32) bool {
	if count == FlowerGuildBuffThree || count == FlowerGuildBuffFour || count == FlowerGuildBuffFive {
		return true
	}
	return false
}

func FlowerGuildBuffMinAllyCountRequest() uint32 {
	return FlowerGuildBuffThree
}

// 密林宝箱
const (
	FlowerAddItem1 uint32 = 10052
	FlowerAddItem2 uint32 = 10053
	FlowerAddItem3 uint32 = 10054
	FlowerAddItem4 uint32 = 10055
)

const (
	GoddessAddEXPFeed  = 1
	GoddessAddEXPTouch = 2
)

const (
	EmblemPosNone  = 0
	EmblemPosStart = 1
	EmblemPos1     = 1
	EmblemPos2     = 2
	EmblemPos3     = 3
	EmblemPos4     = 4
	EmblemPosMax   = 4
)

var EmblemLegalPos = map[uint32]struct{}{
	EmblemPos1: {},
	EmblemPos2: {},
	EmblemPos3: {},
	EmblemPos4: {},
}

const (
	EmblemSkillTypeNone     = 0
	EmblemSkillTypeNormal   = 1
	EmblemSkillTypeAdditive = 2
)

const (
	BlessingAttrCount          int    = 3
	EmblemBlessingResCostCount uint32 = 2
)

// 全民无双
const (
	RecordStartBeforeActivityOpen = 0
	RecordStartAfterActivityOpen  = 1
)

const (
	EmblemCustomizeRareBase        uint64 = 10000000
	EmblemCustomizeRaceBase        uint64 = 1000000
	EmblemCustomizeTypeBase        uint64 = 1000
	EmblemCustomizePosBase         uint64 = 100
	EmblemCustomizeIsHeroGroupBase uint64 = 10
)

const (
	EmblemCustomizeRandom = 98
	EmblemCustomizePick   = 99
)

var EmblemCustomizeLegalType = map[uint32]struct{}{
	EmblemCustomizeRandom: {},
	EmblemCustomizePick:   {},
}

var EmblemCustomizeLegalPos = map[uint32]struct{}{
	EmblemPos1:            {},
	EmblemPos2:            {},
	EmblemPos3:            {},
	EmblemPos4:            {},
	EmblemCustomizeRandom: {},
	EmblemCustomizePick:   {},
}

const (
	EmblemNoExistSkill = 0
	EmblemExistSkill   = 1
)

var EmblemLegalSkill = map[uint32]struct{}{
	EmblemNoExistSkill: {},
	EmblemExistSkill:   {},
}

var EmblemCustomizeLegalSkill = map[uint32]struct{}{
	EmblemNoExistSkill:    {},
	EmblemExistSkill:      {},
	EmblemCustomizeRandom: {},
}

// 阵容
const (
	FormationOneTeam      = 1 // 单队阵容
	FormationTwoTeam      = 2 // 双队阵容
	FormationTeamOneIndex = 0 //第一队下标

	MaxTeamCount               = 9 //单阵容，最多队伍数量
	TwoTeamRequireMinHeroCount = 6 //双队所需最少英雄数量
)

const (
	LinkSummonPoolSize = 6
)

const (
	LinkSummonPurpleGuaranteeIndex = iota //紫色保底下标
	LinkSummonOrangeGuaranteeIndex        //橙色保底下标
	LinkSummonRedGuaranteeIndex           //红色保底下标
	LinkSummonMaxGuaranteeLen
)

const (
	DivineDemonOrangeFragmentGuaranteeIndex = iota
	DivineDemonPurpleGuaranteeIndex         //紫色保底下标
	DivineDemonOrangeGuaranteeIndex         //橙色保底下标
	DivineDemonRedGuaranteeIndex            //红色保底下标
	DivineDemonMaxGuaranteeLen
)

const (
	SelectSummonRedFragmentGuaranteeIndex = iota
	SelectSummonPurpleGuaranteeIndex      //紫色保底下标
	SelectSummonOrangeGuaranteeIndex      //橙色保底下标
	SelectSummonRedGuaranteeIndex         //红色保底下标
	SelectSummonMaxGuaranteeLen
)

// 神器首发
// XXX 谨慎修改此配置
// XXX 如需调整，要审慎检查活动是否有重叠，是否需要修复数据
const ArtifactDebutProtectDay uint32 = 28 //新服保护期，在此之前按新服活动走，此后按通服活动走

// XXX 落地数据是按位(64)存储的，这里不可修改，
const BinaryUint64Max = 63 //64位按位存储时，最大可存id

// 存储时使用一个字段保存唯一id，为了避免新服和通服活动的id重复，限制了gm过来id的最小值
const ArtifactDebutGMMinUniqID uint32 = 100

const (
	ArtifactDebutDrawCategoryCount = 2 //抽卡分类数量
	ArtifactDebutPuzzleMaxAwardID  = 7 //拼图活动最大奖励id
)

// 神器首发 - 抽卡掉落类型
const (
	ArtifactDebutDrawKindCommon               uint32 = 0 //常规
	ArtifactDebutDrawKindWishArtifactFragment uint32 = 1 //特殊 - 心愿神器的碎片
	ArtifactDebutDrawKindWishArtifact         uint32 = 2 //特殊 - 心愿神器
	ArtifactDebutDrawRechargeCountCap                = 6 //累抽活动可购礼包数量

	ArtifactDebutJuniorDrawMinGropID uint32 = 2000 //初级抽卡，最小卡组id

	ArtifactDebutTaskTypeAchieve = 0 //任务类型 - 成就
	ArtifactDebutTaskTypeDaily   = 1 //任务类型 - 每日

	ArtifactDebutJuniorDrawItemID uint32 = 10060 //初级抽卡券
	ArtifactDebutSeniorDrawItemID uint32 = 10061 //高级抽卡券
	ArtifactDebutPuzzleItemID     uint32 = 10062 //拼图碎片
)

// 神器首发 - 合法抽卡掉落类型
var ArtifactDebutDrawLegalKind = map[uint32]bool{
	ArtifactDebutDrawKindCommon:               true,
	ArtifactDebutDrawKindWishArtifact:         true,
	ArtifactDebutDrawKindWishArtifactFragment: true,
}

// 神器首发 - 全部格子id
var ArtifactDebutPuzzleAllPos = []int{1, 2, 3, 4, 5, 6, 7, 8, 9}

// 神器首发 - 拼图奖励与条件 - 九宫格固定对应关系
var ArtifactDebutPuzzleAwardID2Condition = map[uint32][]int{
	1: {1, 2, 3},
	2: {4, 5, 6},
	3: {7, 8, 9},
	4: {3, 6, 9},
	5: {2, 5, 8},
	6: {1, 4, 7},
	7: ArtifactDebutPuzzleAllPos,
}

// 品质
const (
	RareRed    = 60
	RareOrange = 50
	RarePurple = 40
	RareBlue   = 30
	RareGreen  = 20
)

// 神魔抽卡 - 活动类型
const (
	DivineDemonTypeDebut   uint32 = 1 // 首发
	DivineDemonTypeNoDebut uint32 = 0 // 无首发
)

// 神魔抽卡
const (
	DivineDemonActivityTypeNewServer         uint32 = 1 // 新服活动
	DivineDemonActivityTypeCommonServer      uint32 = 2 // 通服活动
	DivineDemonActivityTypeCommonServerDebut uint32 = 3 // 通服首发活动
)

// 神魔抽卡 - grade类型
const (
	DivineDemonGradeUpRedHero   uint32 = 1 // up概率红卡英雄
	DivineDemonGradeNoUpRedHero uint32 = 2 // 非up概率红卡英雄
	DivineDemonGradeTypeOther   uint32 = 3 // 其他奖励
)

// 神魔抽卡 - 必出Up红卡的次数
const (
	DivineDemonUpRedCardCount  = 2
	DivineDemonSummonNoRedCard = 0 // 未抽中红卡
	DivineDemonSummonRedCard   = 1 // 抽中红卡
	DivineDemonSummonUpRedCard = 2 // 抽中概率Up红卡
)

// 神魔抽卡 - SC状态（SC: super chance）
const (
	DivineDemonSCDefault uint32 = 0
	DivineDemonSCOne     uint32 = 1
	DivineDemonSCTwo     uint32 = 2
	DivineDemonSCThree   uint32 = 3
)

var DivineDemonSCStatus = map[uint32]struct{}{
	DivineDemonSCDefault: {},
	DivineDemonSCOne:     {},
	DivineDemonSCTwo:     {},
	DivineDemonSCThree:   {},
}

// 日志记录使用
const (
	GemLink               int = iota // 英雄的宝石羁绊是否激活
	GemPasSkill1                     // 宝石的被动技能1是否激活
	GemPasSkill2                     // 宝石的被动技能2是否激活
	EmblemLink                       // 英雄的纹章羁绊是否激活
	EmblemPasSkill1                  // 纹章技能1是否激活
	EmblemPasSkill2                  // 纹章技能2是否激活
	EmblemPasSkill3                  // 纹章技能3是否激活
	EmblemTwoSuit                    // 纹章两件套是否激活
	EmblemFourSuit                   // 纹章四件套是否激活
	EmblemExclusive1                 // 专属纹章有1个
	EmblemExclusive2                 // 专属纹章有2个
	EmblemExclusive3                 // 专属纹章有3个
	EmblemExclusive4                 // 专属纹章有4个
	EmblemExclusiveSkill1            //专属符文技能1
	EmblemExclusiveSkill2            //专属符文技能2
	EmblemExclusiveSkill3            //专属符文技能3
)

// 女武神藏品加资源方式
const (
	GoddessCollectionAddTypePct        uint32 = 1 // 按万分比加成
	GoddessCollectionAddTypeFixedValue uint32 = 2 // 按固定value加成
)

// 女武神藏品加资源种类
const (
	GoddessCollectionTypeOnhook uint32 = 1 // 挂机奖励
	GoddessCollectionTypeBox    uint32 = 2 // 宝箱

	GoddessCollectionBoxDaily  uint32 = 1 // 日常宝箱
	GoddessCollectionBoxWeekly uint32 = 2 // 周常宝箱
)

const (
	AddResourcesForFree     uint64 = 0
	AddResourcesForRecharge uint64 = 1
)

// 英雄图鉴的属性类型
const (
	HeroHandbookAttrTypeStar  = 1 // 英雄图鉴属性类型为星级属性
	HeroHandbookAttrTypeLink2 = 2 // 英雄图鉴属性类型为link2
	HeroHandbookAttrTypeLink3 = 3 // 英雄图鉴属性类型为link3
	HeroHandbookAttrTypeLink4 = 4 // 英雄图鉴属性类型为link4
	HeroHandbookAttrTypeLink5 = 5 // 英雄图鉴属性类型为link5
)

// HeroHandbookLinkAttrBit: 英雄图鉴羁绊属性解锁对应的位
var HeroHandbookLinkAttrBit = map[uint32]int{
	HeroHandbookAttrTypeStar:  0,
	HeroHandbookAttrTypeLink2: 1,
	HeroHandbookAttrTypeLink3: 2,
	HeroHandbookAttrTypeLink4: 3,
	HeroHandbookAttrTypeLink5: 4,
}

// 获取英雄种族类型（7-三系和8-神魔）
func GetHeroRaceType(race uint32) uint32 {
	if race == RaceEmpire || race == RaceForest || race == RaceMoon {
		return RaceTypeCommonThree
	}
	return RaceTypeProtossDemon
}

// 英雄种族是否符合要求
// @param uint32 race 英雄种族
// @param uint32 reqRace 要求的英雄种族（包含7-三系和8-神魔）
// @return bool
func IsHeroExchangeRaceMatch(race, reqRace uint32) bool {
	if race == reqRace {
		return true
	}

	return GetHeroRaceType(race) == reqRace
}

// 英雄转换类型是否合法
func IsLegalHeroExchangeRaceType(race uint32) bool {
	if race == RaceEmpire || race == RaceForest || race == RaceMoon || race == RaceProtoss ||
		race == RaceDemon || race == RaceTypeCommonThree || race == RaceTypeProtossDemon {
		return true
	}
	return false
}

const (
	SummonActivityNormal = 0
	SummonActivityNew    = 1
)

const (
	DispatchHeroConditionTypeRace = 1
	DispatchHeroConditionTypeLink = 2
)

var GuildRank = map[uint32]struct{}{
	//GuildDungeonWeeklyDamageRankId: {},
	GuildActivityRankID:          {},
	GuildDivisionRankID:          {},
	GuildLevelRankID:             {},
	GuildDungeonChapterRankId:    {},
	GuildDungeonUserDamageRankId: {},
}

// 777抽活动
const (
	GodPresentSummonCountPerTime = 10 //单次抽卡次数
	GodPresentGroupFirst         = 1  // 第一期活动
	GodPresentGroupSecond        = 2  // 第二期活动
	GodPresentGroupThird         = 3  // 第三期活动
)

// h5保存桌面
const (
	H5DesktopRewardID uint32 = 1
)

// ClientLanguage 客户端语言常量
var ClientLanguage = map[string]uint32{
	"zh": 1,
	"en": 2,
	"th": 3,
	"fr": 4,
	"de": 5,
	"tw": 6,
	"it": 7,
	"es": 8,
	"pt": 9,
	"ru": 10,
}

const IdMaxLanguage = "ru"

// 公会的加入类型
const (
	GuildJoinOpen      uint32 = iota // 自由加入
	GuildJoinNeedApply               // 需要审核
	GuildJoinClose                   // 拒绝加入
)

// 玩法子类型
const (
	SubFuncNone         uint32 = 0 //默认值 - 无子玩法
	SubFuncFlowerPlat   uint32 = 1 //密林 - 种花
	SubFuncFlowerOccupy uint32 = 2 //密林 - 占矿
)

// 掉落活动
const (
	DropActivityFuncCount = 5 //关联玩法个数，用于初始化切片cap
)

// 掉落活动 - subFunction
var DropActivitySubFunc = map[uint32][]uint32{
	uint32(common.FUNCID_MODULE_FLOWER): {SubFuncFlowerPlat, SubFuncFlowerOccupy},
}

func IsDropActivitySubFuncLegal(funcID, subFunc uint32) bool {
	if datas, exist := DropActivitySubFunc[funcID]; exist {
		for _, v := range datas {
			if v == subFunc {
				return true
			}
		}
	}
	return false
}

// 累登
const (
	DailyAttendanceEachGroupMinDay uint32 = 1  // 每轮最小天数
	DailyAttendanceEachGroupMaxDay uint32 = 30 // 每轮最大天数
)

// 每日特惠
const (
	DailySpecialScoreAwardCount int = 3
)

// AB服
const (
	ActivitySumTypeRoundAdvanced      uint32 = 1 //活动类型 - 轮次高抽
	ActivitySumTypeRoundAssoc         uint32 = 2 //活动类型 - 轮次先知抽高抽
	ActivitySumTypeRoundArtifact      uint32 = 3 //活动类型 - 轮次神器抽
	ActivitySumTypeRoundLink          uint32 = 4 //活动类型 - 轮次流派抽
	ActivitySumTypeRoundArtifactDebut uint32 = 5 //活动类型 - 轮次神器首发
	ActivitySumTypeDivineDemonSummon  uint32 = 6 //活动类型 - 神魔抽
	ActivitySumTypeNewServerPushGift  uint32 = 8 //活动类型 - 新服推送礼包

	ActivitySumMinCheckTm int64 = 1577808000 //最小时间检查点 - 北京时间2020-01-01
)

// AB服活动类型 - 轮次活动
var ActivitySumTypeRound = []uint32{
	ActivitySumTypeRoundAdvanced,
	ActivitySumTypeRoundAssoc,
	ActivitySumTypeRoundArtifact,
	ActivitySumTypeRoundLink,
	ActivitySumTypeRoundArtifactDebut,
}

// AB服活动类型 - 全部活动
var ActivitySumAllTypes = append(ActivitySumTypeRound,
	[]uint32{ActivitySumTypeDivineDemonSummon, ActivitySumTypeNewServerPushGift}...)

// 世界boss - 战斗消耗类型
const (
	WorldBossFightCostTypeFightCount uint32 = 1 // 挑战次数
	WorldBossFightCostTypeFightRoll  uint32 = 2 // 挑战卷
)

// WorldBossGetRoomLogMaxNum 世界boss - 请求房间日志最大数量
const WorldBossGetRoomLogMaxNum = 100

// 羁绊类型
const (
	LinkTypeCommon uint32 = 1 // 基础羁绊
	LinkTypeCamp   uint32 = 2 // 阵营羁绊
	LinkTypeEmblem uint32 = 3 // 符文羁绊
	LinkTypeSeason uint32 = 4 // 赛季羁绊
	LinkTypeChange uint32 = 5 // 可变羁绊
)

func isLegalLinkType(typ uint32) bool {
	switch typ {
	case LinkTypeCommon, LinkTypeCamp, LinkTypeEmblem, LinkTypeSeason, LinkTypeChange:
		return true
	}
	return false
}

// 公会副本秘技获取途径
const (
	GuildDungeonStrategyWeekResetGet      uint32 = 1 // 周结算获得
	GuildDungeonStrategyThroughChapterGet uint32 = 2 // 章节通关获得
)

// 公会副本秘技效果常量(对应guild_dungeon_strategy_skill_info表的skill_effect)
const (
	GuildDungeonStrategyAddChallengeTimes uint32 = 1001 // 增加成员挑战次数
	GuildDungeonStrategyBossRestoreHpPct  uint32 = 2101 // 随机一个死掉的Boss恢复血量万分比
	GuildDungeonStrategyDamageAddPct      uint32 = 2201 // 下次挑战伤害提升万分比
)

// 公会副本秘技-对自己公会生效
var GuildDungeonStrategyForMyGuild = map[uint32]bool{
	GuildDungeonStrategyAddChallengeTimes: true,
}

// 公会副本秘技-对其它公会生效
var GuildDungeonStrategyForOtherGuild = map[uint32]bool{
	GuildDungeonStrategyBossRestoreHpPct: true,
	GuildDungeonStrategyDamageAddPct:     true,
}

const (
	GuildDungeonStrategySeasonReset = 1
	GuildDungeonStrategyWeeklyReset = 2
)

const (
	ActivityStoryLayerTypeHero uint32 = 1
	ActivityStoryLayerTypeSuit uint32 = 2
)

const (
	SeasonDungeonLayerTypeHero uint32 = 1
	SeasonDungeonLayerTypeSuit uint32 = 2
)

const (
	GuildDungeonRecvBoxMessageBroadCastRare uint32 = 1
)

type Buff_Condition int

const (
	BuffCondNone    Buff_Condition = iota //无条件
	BuffCondAlive                         //判断某些目标存活
	BuffCondOnEvent                       //监听事件触发
	BuffCondMax
)

type Buff_Effect int //事件触发时的effect

const (
	BuffEffectNone      Buff_Effect = iota
	BuffEffectActive                //设置为active状态
	BuffEffectNotActive             //设置为非active状态
	BuffEffectBeRemove              //删除buff
	BuffEffectDecrLayer             //减少层数
	BuffEffectMax
)

// 赛季等级升级消耗的token ID
const SeasonLevelUpToken uint32 = 9061

// 赛季等级任务记录类型
const (
	SeasonLeveTaskEventRecordFromSeasonOpen     uint32 = 0 // 赛季开始，开始记录
	SeasonLevelTaskEventRecordFromSeasonDayOpen uint32 = 1 // 赛季开始后的X天开始记录
)

// 觉醒技能
const (
	HeroAwakenSkillMaxLevel = 5 //觉醒技能最大等级
	HeroAwakenLinkLevel     = 6 //觉醒羁绊对应等级
)

// 资源类型-代金券
const ResourceTypeCoupon uint32 = 20

const (
	EmblemRareOrange uint32 = 50 // 符文品质 - 橙色
	EmblemRareRed    uint32 = 60 // 符文品质 - 红色
)

const (
	EmblemExclusiveNumOne   uint32 = 1 //1件专属符文
	EmblemExclusiveNumTwo   uint32 = 2 //2件专属符文
	EmblemExclusiveNumThree uint32 = 3 //3件专属符文
	EmblemExclusiveNumFour  uint32 = 4 //4件专属符文
)

// 材料本
const (
	TrialOnhookAwardCount = 3 //材料本挂机奖励数量
)

const (
	GuildSandTableLandTypeNone       uint32 = iota
	GuildSandTableLandTypeMainBase          //主基地
	GuildSandTableLandTypeEmpty             //空地
	GuildSandTableLandTypeFence             //阻挡栅栏
	GuildSandTableLandTypeStronghold        //据点
	GuildSandTableLandTypeBarrier           //屏障
	GuildSandTableLandTypeMonster           //怪物地
	GuildSandTableLandTypeSenior            //高级地
	GuildSandTableLandTypeArena      = 99   //擂台
)

var DisorderLandFormationPool = map[uint32]uint32{
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_A): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_A),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_B): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_B),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_C): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_C),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_D): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_D),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_E): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_E),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_F): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_F),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_G): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_G),
	uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_H): uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_H),
}

// buff组类型
const (
	BuffGroupTypeSeparate = 0 //0-走单独概率
	BuffGroupTypeTogether = 1 //1-走施加时的概率
)

// 个人Boss战斗加成类型
const (
	MirageFightEffectNone             uint32 = iota
	MirageFightEffectHeroLink                // 英雄羁绊
	MirageFightEffectArtifactLink            // 神器羁绊
	MirageFightEffectArtifactRareStar        // 指定品质的总星数
)

// 金字塔活动参数
const (
	ActivityPyramidInitialRound               = 1 // 初始轮次
	ActivityPyramidInitialFloor               = 1 // 初始层数
	ActivityPyramidLatticeNormalPrize         = 1 // 普通格位
	ActivityPyramidLatticeRisePrizeOne        = 2 // 上升格位1
	ActivityPyramidLatticeChoosePrize         = 4 // 自选格位
	ActivityPyramidLatticeBigPrize            = 5 // 大奖格位
	ActivityPyramidLatticeNormalPrizeDrawMax  = 1 // 普通格位抽取次数
	ActivityPyramidLatticeRisePrizeOneDrawMax = 1 // 上升格位一抽取次数
	ActivityPyramidLatticeChoosePrizeDrawMax  = 1 // 自选格位抽取次数
	ActivityPyramidLatticeBigPrizeDrawMax     = 1 // 大奖格位抽取次数
	ActivityPyramidTestDrawResultNum          = 20
)

// 屏蔽参数
const (
	ShieldTypeHero             = 1 // 英雄
	ShieldTypeArtifact         = 2 // 神器
	ShieldTypeHeroFragment     = 3 // 英雄碎片
	ShieldTypeArtifactFragment = 4 // 神器碎片
)

// 赛季加成类型
const (
	SeasonAddTypeNone          uint32 = iota
	SeasonAddTypeFirst                //  一级属性加成
	SeasonAddTypeSecond               // 二级属性加成
	SeasonAddTypeGvgFightTimes        // gvg增加次数
)

// 赛季加成图鉴类型
const (
	SeasonAddHandbookTypeNone              uint32 = iota
	SeasonAddHandbookHeroStar                     // 英雄星级
	SeasonAddHandbookArtifactStar                 // 神器星级
	SeasonAddHandbookHeroEmblemExclusiveLv        // 英雄符文专属技能等级
	SeasonAddHandbookPokemonStar                  // 宠物星级
)

// 公会战建筑ID
const (
	GstGuildBuildMain  = 1
	GstGuildBuildTrees = 2
	GstGuildBuildStone = 3
	GstGuildBuildIron  = 4
)

// GstGroupRank2NumberType 公会战组内排行榜及对应的点赞次数
var GstGroupRank2NumberType = map[uint32]uint32{
	GstGroupUsersContributionRank: uint32(common.PURCHASEID_GST_CONTRIBUTION_RANK_LIKE_COUNT),
	GstGroupUsersKillRank:         uint32(common.PURCHASEID_GST_KILL_RANK_LIKE_COUNT),
	GstGroupUsersTripleKillRank:   uint32(common.PURCHASEID_GST_TRIPLE_KILL_RANK_LIKE_COUNT),
}

// GuildMedalAddType   公会勋章加成类型
const (
	GuildMedalAddTrial     = 1 // 材料本挂机加成
	GuildMedalDisorderLand = 2 // 失序空间加成
	GuildMedalAddFlower    = 3 // 密林种花加成
	GuildMedalAddGst       = 4 // 公会战挂机加成
	GuildMedalAddBossRush  = 5 // Boss挑战掉落加成
	GuildMedalAddSeasonMap = 6 // 地图玩法掉落加成
)

// GuildMedalActivityType  公会勋章的激活类型
const (
	GuildMedalActivityTypeTowerSeasonFloor    uint32 = 1
	GuildMedalActivityTypePeakRank                   = 2
	GuildMedalActivityTypeWorldBossAreaRank          = 3
	GuildMedalActivityTypeWorldBossRoomRank          = 4
	GuildMedalActivityTypeSeasonArenaDivision        = 5
	GuildMedalActivityTypeSeasonArenaRank            = 6
)

// 赛季羁绊linkId优先级
const (
	SeasonLinkPriorityNone = 0
	SeasonLinkPriorityOne  = 1
	SeasonLinkPriorityTwo  = 2
)

const (
	SeasonArenaLogMaxNum               uint32 = 20 //赛季竞技场战斗记录最大数量
	SeasonArenaOpponentNum             uint32 = 3  //竞技场对手数量
	SeasonArenaOptionPlayer            uint32 = 0  //玩家
	SeasonArenaOptionRobot             uint32 = 1  //机器人
	SeasonArenaDivisionRewardTypeClick uint32 = 1
	SeasonArenaDivisionRewardTypeEmail uint32 = 2
	SeasonArenaFameSimpleCount         uint32 = 10
	SeasonArenaFameComplexCount        uint32 = 100
	SeasonArenaDivisionRewardTypeRecv  uint32 = 1
	SeasonArenaDivisionRewardTypeMail  uint32 = 2
)

var SeasonArenaDefender2Attack = map[uint32]uint32{
	uint32(common.FORMATION_ID_FI_SEASON_ARENA_THREE_DEFENSE): uint32(common.FORMATION_ID_FI_SEASON_ARENA_THREE_ATTACK),
	uint32(common.FORMATION_ID_FI_SEASON_ARENA_FIVE_DEFENSE):  uint32(common.FORMATION_ID_FI_SEASON_ARENA_FIVE_ATTACK),
	uint32(common.FORMATION_ID_FI_SEASON_ARENA_SEVEN_DEFENSE): uint32(common.FORMATION_ID_FI_SEASON_ARENA_SEVEN_ATTACK),
	uint32(common.FORMATION_ID_FI_SEASON_ARENA_NINE_DEFENSE):  uint32(common.FORMATION_ID_FI_SEASON_ARENA_NINE_ATTACK),
}

// 特殊次数类型相关配置与方法
var SpecialNumberTypePool = map[uint32]struct{}{
	uint32(common.PURCHASEID_ARENA_FIGHT_COUNT):         {},
	uint32(common.PURCHASEID_MIRAGE_SKY_FIGHT_COUNT):    {},
	uint32(common.PURCHASEID_FLOWER_PLANT_DAILY_COUNT):  {},
	uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT): {},
}

func MakeSpecialNumInitData(numberType, userLevel, flowerLevel uint32) *cl.NumInfo {
	initNum := getRealNumMax(numberType, userLevel, flowerLevel)
	return &cl.NumInfo{
		NumMax:  initNum,
		LeftNum: initNum,
	}
}

func getRealNumMax(numberType, userLevel, flowerLevel uint32) uint32 {
	switch numberType {
	case uint32(common.PURCHASEID_ARENA_FIGHT_COUNT):
		return getDefaultNumMax(numberType)
	case uint32(common.PURCHASEID_MIRAGE_SKY_FIGHT_COUNT):
		return calcNumMax(numberType, userLevel)
	case uint32(common.PURCHASEID_FLOWER_PLANT_DAILY_COUNT):
		return calcNumMax(numberType, flowerLevel)
	case uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT):
		return calcNumMax(numberType, flowerLevel)
	}

	return 0
}

func calcNumMax(numberType, level uint32) uint32 {
	numMax := getDefaultNumMax(numberType)
	maxInfos := GetData().NumberMaxAddTaskInfoM.GetMaxNumInfos(numberType)
	for _, maxInfo := range maxInfos {
		if uint64(level) >= maxInfo.Value {
			if maxInfo.NumMax > numMax {
				numMax = maxInfo.NumMax
			}
		}
	}
	return numMax
}

func getDefaultNumMax(numberType uint32) uint32 {
	maxInfo := GetData().NumberMaxInfoM.Index(numberType)
	if maxInfo == nil {
		l4g.Errorf("getDefaultNumMax: numberMaxInfo not exist. numberType: %d", numberType)
		return 0
	}

	return maxInfo.DefaultMax
}

const (
	SERVER_TYPE_DEFAULT = ""   //默认欧美
	SERVER_TYPE_TW      = "tw" //台湾地区
	SERVER_TYPE_CN      = "cn" //37地区
)

// 首充调整
const (
	FirstGiftTypeSingleReward = 1 // 单次奖励发放
	FirstGiftTypeMultiReward  = 2 // 多次奖励发放
	FirstGiftLoginMax         = 3 // 累登上限
)

const (
	TurnTableNormal     = 0 // 放回
	TurnTableDisposable = 1 // 不放回
)

const (
	TurnTableEffectNone            = 0
	TurnTableEffectTargetId        = 1
	TurnTableEffectDropGroupRandom = 2
	TurnTableEffectMulti           = 3
	TurnTableEffectAward           = 4
	TurnTableEffectAddGuarantee    = 5
	TurnTableEffectMax             = 6
)

const (
	RemainTypePVE = 1
	RemainTypePVP = 2
	RemainTypeAll = 3
)

const (
	DragonShowPosInit                     = 1 // 龙战默认展示龙位置
	DragonContinuousFailedCountMatchRobot = 1 // 龙战触发匹配机器人公会的连败次数
)

// 战斗 - 目标类型
const (
	BattleAttackTargetTypeSingle = 1 // 单体伤害
)

// 是否是单体伤害技能
func IsSingleTargetAttack(typ uint32) bool {
	return typ == BattleAttackTargetTypeSingle
}

const (
	HotRankScoreTypeRank  = 1
	HotRankScoreTypeRatio = 2
)

const (
	HotRankHero            = 1010
	HotRankRedEmblem       = 1020
	HotRankOrangeElem      = 1021
	HotRankReaArtifacts    = 2030
	HotRankOrangeArtifacts = 2031
)

const (
	// 策划不会动开始时间故写死在常量中
	HotRankBeginTimeString = "2024-05-01 00:00:00"
)

const TotalRecoveryReward = uint32(100)

// Boss挑战阵容
var BossRushFormationPool = map[uint32]struct{}{
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_1): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_2): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_3): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_4): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_5): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_6): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_7): {},
	uint32(common.FORMATION_ID_FI_BOSS_RUSH_8): {},
}

// 天赋树 - node类型
const (
	TalentTreeRoot        = 1 // 根节点
	TalentTreeSeasonTeam  = 2 // 赛季队伍加成节点
	TalentTreeFunc        = 3 // 功能加成节点
	TalentTreeEggBlessing = 4 // 化形祝福节点
	TalentTreeSeasonMap   = 5 // 太古大陆节点
)

// 天赋树 - node加成类型
const (
	TalentTreeNodeLevelAdditionPassive                 = 1  // 被动技能加成
	TalentTreeNodeLevelAdditionGstTeamNum              = 2  // GST队伍数量加成
	TalentTreeNodeLevelAdditionLinkHeroNumReduce       = 3  // 羁绊所需英雄数量减少
	TalentTreeNodeLevelAdditionGstDragonTeamNum        = 4  // GST龙战队伍数量加成
	TalentTreeNodeLevelAdditionSeasonDoorLineNum       = 5  // 赛季开门队列数量加成
	TalentTreeNodeLevelAdditionSeasonJewelry           = 6  // 赛季装备基础属性加成
	TalentTreeNodeLevelAdditionSeasonMapStaminaMax     = 7  // 赛季地图体力上限
	TalentTreeNodeLevelAdditionSeasonMapStaminaRecover = 8  // 赛季地图体力恢复速度
	TalentTreeNodeLevelAdditionSeasonMapTokenRecover   = 9  // 赛季地图货币每日回复量增加
	TalentTreeNodeLevelAdditionSeasonMapSellPriceUp    = 10 // 赛季地图卖出所有货物价格提升(万分比)
	TalentTreeNodeLevelAdditionSeasonMapSellPriceDown  = 11 // 赛季地图买入所有货物价格下降(万分比)
	TalentTreeNodeLevelAdditionSeasonMapFight          = 12 // 赛季地图战斗被动加成
)

const (
	HeroAttrCalTypeByDefault   int8 = 0 //英雄属性默认的计算方式
	HeroAttrCalTypeBySeason    int8 = 1 //英雄属性赛季的默认计算方式
	HeroAttrCalTypeByWorldBoss int8 = 2 //英雄属性世界boss的计算方式
	HeroAttrCalTypeByMonster   int8 = 3 //存粹的怪物覆盖, 夏日活动
)

const (
	ActivitySumTurnTable uint32 = 1
	ActivitySumPuzzle    uint32 = 2
	ActivitySumDrop      uint32 = 3
	ActivitySumFeed      uint32 = 4
	ActivitySumShoot     uint32 = 5
	ActivityMaxIndex     uint32 = 6
)

const (
	ActivitySumFeedDefaultLevel uint32 = 1
	ActivitySumFeedDefaultCond  uint32 = 1
)

type PuzzleModel struct {
	MaxX uint32
	MaxY uint32
}

var PuzzleModelM = map[uint32]*PuzzleModel{
	1: {
		MaxX: 2,
		MaxY: 2,
	},
	2: {
		MaxX: 3,
		MaxY: 3,
	}, 3: {
		MaxX: 4,
		MaxY: 4,
	},
}

const (
	GuildTechAddType              uint32 = iota
	GuildTechAddTypeOreProduction        // 矿产量
	GuildTechAddTypeGlobalPassive        // 地块,擂台,占矿战斗. 给全局对象加成
	GuildTechAddTypeSkillTimes           // 技能加次数
	GuildTechAddTypeReward               // 奖励(发放全体成员)
	GuildTechAddTypeOreOccupyNum         // 矿占领数量
	GuildTechAddTypeMemberPassive        // 占矿专用. 给所有成员对象加成
)

const (
	ItemExpiredNone     = 0
	ItemExpiredTogether = 1
	ItemExpiredInBag    = 2
)

// 阵容队伍锁类型
const (
	FormationLockType1 = 1
	FormationLockType2 = 2
)

// 根据阵容队伍锁类型获取锁定位置列表
func getLockPosListByLockType(lockType uint32) []uint32 {
	switch lockType {
	case FormationLockType1:
		return []uint32{2, 5}
	case FormationLockType2:
		return []uint32{2}
	}
	return nil
}

const (
	GSTChallengeBuffRebirth     = 1 // 复活
	GSTChallengeBuffAddPct      = 2 // 增加血量
	GSTChallengeBuffRandomSkill = 3 // 随机被动技能
)

const ChallengeLogMaxNum uint32 = 200 //战斗记录最大数量

const (
	GSTChallengeTeamNotMatch = iota // 新擂台赛-队伍未匹配对手
	GSTChallengeTeamNotFight        // 新擂台赛-队伍未战斗
	GSTChallengeTeamWin             // 新擂台赛-队伍胜利
	GSTChallengeTeamLose            // 新擂台赛-队伍失败
	GSTChallengeTeamExpire          // 新擂台赛-队伍失效
)

// 嘉年华类型
const (
	CarnivalTypeNormal = 1
	CarnivalTypeSeason = 2
)

const (
	MazeInitPowerDungeonID = 10315
)

// 获取发布状态
const (
	OpStatusRelease uint32 = 1 //发布
	OpStatusOff     uint32 = 2 //下架
)

var SeasonComplianceRankIDs = []uint32{
	SeasonComplianceTotalID,
	SeasonComplianceHeroID,
	SeasonComplianceChallengeID,
	SeasonComplianceArtifactID,
	SeasonComplianceEmblemID,
}

var SeasonComplianceStageRank = map[uint32]uint32{
	SeasonComplianceHeroStage:      SeasonComplianceHeroID,
	SeasonComplianceChallengeStage: SeasonComplianceChallengeID,
	SeasonComplianceArtifactStage:  SeasonComplianceArtifactID,
	SeasonComplianceEmblemStage:    SeasonComplianceEmblemID,
	SeasonComplianceTotalStage:     SeasonComplianceTotalID,
}

var StageRankSeasonCompliance = map[uint32]uint32{
	SeasonComplianceHeroID:      SeasonComplianceHeroStage,
	SeasonComplianceChallengeID: SeasonComplianceChallengeStage,
	SeasonComplianceArtifactID:  SeasonComplianceArtifactStage,
	SeasonComplianceEmblemID:    SeasonComplianceEmblemStage,
	SeasonComplianceTotalID:     SeasonComplianceTotalStage,
}

type SeasonComplianceRankMailAndScoreMail struct {
	StageID   uint32
	RankMail  uint32
	ScoreMail uint32
}

const (
	MailIDSeasonComplianceTotalRankReward     uint32 = 1200 // 赛季冲榜总排行排名奖励
	MailIDSeasonComplianceStageID1RankReward  uint32 = 1201 // 赛季冲榜阶段1排行奖励
	MailIDSeasonComplianceStageID1ScoreReward uint32 = 1202 // 赛季冲榜阶段1积分奖励
	MailIDSeasonComplianceStageID2RankReward  uint32 = 1203 // 赛季冲榜阶段2排行奖励
	MailIDSeasonComplianceStageID2ScoreReward uint32 = 1204 // 赛季冲榜阶段2积分奖励
	MailIDSeasonComplianceStageID3RankReward  uint32 = 1205 // 赛季冲榜阶段3排行奖励
	MailIDSeasonComplianceStageID3ScoreReward uint32 = 1206 // 赛季冲榜阶段3积分奖励
	MailIDSeasonComplianceStageID4RankReward  uint32 = 1207 // 赛季冲榜阶段4排行奖励
	MailIDSeasonComplianceStageID4ScoreReward uint32 = 1208 // 赛季冲榜阶段4排行奖励
)

var SeasonComplianceMail = map[uint32]*SeasonComplianceRankMailAndScoreMail{
	SeasonComplianceHeroStage: {
		StageID:   SeasonComplianceHeroStage,
		RankMail:  MailIDSeasonComplianceStageID1RankReward,
		ScoreMail: MailIDSeasonComplianceStageID1ScoreReward,
	},
	SeasonComplianceChallengeStage: {
		StageID:   SeasonComplianceChallengeStage,
		RankMail:  MailIDSeasonComplianceStageID2RankReward,
		ScoreMail: MailIDSeasonComplianceStageID2ScoreReward,
	},
	SeasonComplianceArtifactStage: {
		StageID:   SeasonComplianceArtifactStage,
		RankMail:  MailIDSeasonComplianceStageID3RankReward,
		ScoreMail: MailIDSeasonComplianceStageID3ScoreReward,
	},
	SeasonComplianceEmblemStage: {
		StageID:   SeasonComplianceEmblemStage,
		RankMail:  MailIDSeasonComplianceStageID4RankReward,
		ScoreMail: MailIDSeasonComplianceStageID4ScoreReward,
	},
}

const (
	SeasonComplianceTotalStage     uint32 = 0
	SeasonComplianceHeroStage      uint32 = 1
	SeasonComplianceChallengeStage uint32 = 2
	SeasonComplianceArtifactStage  uint32 = 3
	SeasonComplianceEmblemStage    uint32 = 4
)

var SeasonComplianceEmblemRare2StageID = map[uint32]uint32{
	uint32(common.QUALITY_ORANGE): SeasonComplianceEmblemOrangeScoreType,
	uint32(common.QUALITY_RED):    SeasonComplianceEmblemRedScoreType,
}

const (
	SeasonComplianceEmblemOrangeScoreType uint32 = 22
	SeasonComplianceEmblemRedScoreType    uint32 = 23
)

const (
	DivineSummonStageID                   uint32 = 1
	DivineHeroStageID                     uint32 = 2
	DivineSeasonHeroStageID               uint32 = 3
	NormalSeasonHeroStageID               uint32 = 4
	DragonFightStageID                    uint32 = 5
	BossRushStageID                       uint32 = 6
	GuildDungeonStageID                   uint32 = 7
	ArtifactDebut                         uint32 = 8
	RedArtifactStageID                    uint32 = 9
	RedArtifactFragmentStageID            uint32 = 10
	RedSeasonArtifactStageID              uint32 = 11
	RedSeasonArtifactFragmentStageID      uint32 = 12
	MirageFightStageID                    uint32 = 13
	CommonThreeOrangeEmblemStageID        uint32 = 14
	ProtossDemonOrangeEmblemStageID       uint32 = 15
	CommonThreeRedEmblemStageID           uint32 = 16
	ProtossDemonRedEmblemStageID          uint32 = 17
	CommonThreeSeasonOrangeEmblemStageID  uint32 = 18
	ProtossDemonSeasonOrangeEmblemStageID uint32 = 19
	CommonThreeSeasonRedEmblemStageID     uint32 = 20
	ProtossDemonSeasonRedEmblemStageID    uint32 = 21
	HaveMoreThanOrangeEmblemStageID       uint32 = 22
	HaveMoreThanRedEmblemStageID          uint32 = 23
	PyramidDrawNumStageID                 uint32 = 24
	SeasonMapFightStageID                 uint32 = 25
)

const (
	LoginTaskTypeID uint32 = 1000001
)

// 赛季装备
const (
	SeasonJewelrySkillRandomTypeLevelUp uint32 = 1   //可强化词条
	SeasonJewelrySkillRandomTypeClassUp uint32 = 2   //可升阶词条
	SeasonJewelryWearHeroNumMax         uint32 = 50  //英雄一键穿脱数量限制
	SeasonJewelryPosMin                 uint32 = 1   //最小位置
	SeasonJewelryPosMax                 uint32 = 4   //最大位置
	SeasonJewelrySkillPosMin            uint32 = 1   //词条最小位置
	SeasonJewelrySkillPosMax            uint32 = 3   //词条最大位置
	SeasonJewelryDecomposeTypeActive    uint32 = 1   //主动分解
	SeasonJewelryDecomposeTypePassive   uint32 = 2   //被动分解
	SeasonJewelryMaxSendNumPerPack      int    = 800 //一个包最多发送多少件赛季装备
	SeasonJewelryRewardTypeRest         uint32 = 1   //挂机奖励
	SeasonJewelryRewardTypeMail         uint32 = 2   //邮件奖励
)

const (
	LongTimeout int = 365 * 24 * 3600
)

const (
	RebaseOperatedAwardAndSet = 1
	RebaseOperatedSet         = 2
)

const (
	ActivityCouponFree     = 1
	ActivityCouponDiamond  = 2
	ActivityCouponRecharge = 3
)

var ActivityCouponType = map[uint32]struct{}{
	ActivityCouponFree:     {},
	ActivityCouponDiamond:  {},
	ActivityCouponRecharge: {},
}

var MuteAccountType = map[uint32]uint32{
	MuteTypeOneDay:   MuteOneDay,
	MuteTypeSevenDay: MuteSevenDay,
	MuteTypeForever:  MuteForever,
}

const (
	MuteTypeOneDay   = 1
	MuteTypeSevenDay = 2
	MuteTypeForever  = 3
)

const (
	MuteOneDay   = 1
	MuteSevenDay = 7
	MuteForever  = 2000
)

const (
	PokemonInitialStar                         = 1 // 初始星级
	PokemonInitialPotentialLevel               = 1 // 初始潜能等级
	PokemonInitialMasterLevel                  = 1 // 初始大师等级
	PokemonSkillTypeTowerSeasonLinkSkillActive = 3 // 宠物技能类型：百塔羁绊直接生效
)

const (
	PokemonSummonPurpleFragmentGuaranteeIndex = iota //紫色碎片保底下标
	PokemonSummonOrangeFragmentGuaranteeIndex        //橙色碎片保底下标
	PokemonSummonRedFragmentGuaranteeIndex           //红色碎片保底下标
	PokemonSummonBlueGuaranteeIndex                  //蓝色保底下标
	PokemonSummonPurpleGuaranteeIndex                //紫色保底下标
	PokemonSummonOrangeGuaranteeIndex                //橙色保底下标
	PokemonSummonRedGuaranteeIndex                   //红色保底下标
	PokemonSummonMaxGuaranteeLen
)
