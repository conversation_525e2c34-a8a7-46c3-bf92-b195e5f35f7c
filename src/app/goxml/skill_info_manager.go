package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	//import "app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type SkillInfoExt struct {
	Id                  uint32 //
	Type                uint32 //
	Type2               uint32 //技能类型2 1-单体 非1-群体
	AttackType          uint32 //攻击类型 1 伤害 2 治疗 3特殊。0无
	SkillGroup          uint32
	Energy              uint32   //神器技能需要消耗的能量
	FirstEnergy         uint32   //首次释放的消耗
	FirstCd             uint32   //开始cd
	CdRound             uint32   //cd回合
	Priority            uint32   //按照优先级排序好在执行, 越小的越排在前面
	EffectPassiveSkills []uint32 //技能的临时被动技能id
	Effects             []*SkillEffectInfo
	PassiveSkill1       uint32 //主动技能也可以配置被动技能
	PassiveSkill2       uint32
	PassiveSkill3       uint32
	PassiveSkill4       uint32
	PassiveSkill5       uint32
	Level               uint32
	Attrs               []SkillAttr
	RaisePSs            []uint64 //养成被动技能id
	SeasonPassiveSkill1 uint32   //指定赛季生效的被动技能 v2.0神器技能增加
	SeasonPassiveSkill2 uint32
	SeasonPassiveSkill3 uint32
	Power               uint32
}

func (e *SkillInfoExt) GetEffects() []*SkillEffectInfo {
	return e.Effects
}

type SkillEffectInfo struct {
	Target    uint32
	Rate      uint32
	Type      uint32
	Formula   uint32
	Param1    uint32
	Param2    uint32
	Param3    uint32
	Param4    uint32
	MaxDamage int64
	MaxDefHp  uint32
	MaxAttAtk uint32
	Condition uint32 //执行条件0默认，1前面一阶段没执行成功才执行, 2 前一阶段执行了才执行
	Pvp       bool   //是否pvp才生效
}

type SkillAttr struct {
	Type  uint32
	Value int64
}

type SkillInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*SkillInfoExt
	Groups  map[uint32]map[uint32]*SkillInfoExt
}

func newSkillInfoManager(xmlData *XmlData) *SkillInfoManager {
	m := &SkillInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SkillInfoManager) name() string {
	return "SkillInfo"
}

func (m *SkillInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SkillInfoManager) checkData() error {
	return nil
}

func (m *SkillInfoManager) load(dir string, isShow bool) error {
	tmp := &SkillInfos{}
	fileName := filepath.Join(dir, "skill_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*SkillInfoExt, len(tmp.Datas))
	}
	m.Groups = make(map[uint32]map[uint32]*SkillInfoExt)
	for _, data := range tmp.Datas {
		dataExt := &SkillInfoExt{}
		var effects []*SkillEffectInfo
		if data.St1Target > 0 {
			effectInfo := SkillEffectInfo{
				Target:    data.St1Target,
				Rate:      data.St1ConditionProb,
				Type:      data.St1AttackType,
				Formula:   data.St1Formula,
				Param1:    data.St1Param1,
				Param2:    data.St1Param2,
				Param3:    data.St1Param3,
				Param4:    data.St1Param4,
				MaxDefHp:  data.St1DamageThresholdHp,
				MaxAttAtk: data.St1DamageThresholdAtk,
				Condition: 0,
				Pvp:       data.IsPvp1 == 1,
			}
			effects = append(effects, &effectInfo)
		}
		if data.St2Target > 0 {
			effectInfo := SkillEffectInfo{
				Target:    data.St2Target,
				Rate:      data.St2ConditionProb,
				Type:      data.St2AttackType,
				Formula:   data.St2Formula,
				Param1:    data.St2Param1,
				Param2:    data.St2Param2,
				Param3:    data.St2Param3,
				Param4:    data.St2Param4,
				MaxDefHp:  data.St2DamageThresholdHp,
				MaxAttAtk: data.St2DamageThresholdAtk,
				Condition: data.St2Judge,
				Pvp:       data.IsPvp2 == 1,
			}
			effects = append(effects, &effectInfo)
		}
		if data.St3Target > 0 {
			effectInfo := SkillEffectInfo{
				Target:    data.St3Target,
				Rate:      data.St3ConditionProb,
				Type:      data.St3AttackType,
				Formula:   data.St3Formula,
				Param1:    data.St3Param1,
				Param2:    data.St3Param2,
				Param3:    data.St3Param3,
				Param4:    data.St3Param4,
				MaxDefHp:  data.St3DamageThresholdHp,
				MaxAttAtk: data.St3DamageThresholdAtk,
				Condition: data.St3Judge,
				Pvp:       data.IsPvp3 == 1,
			}
			effects = append(effects, &effectInfo)
		}
		if data.St4Target > 0 {
			effectInfo := SkillEffectInfo{
				Target:    data.St4Target,
				Rate:      data.St4ConditionProb,
				Type:      data.St4AttackType,
				Formula:   data.St4Formula,
				Param1:    data.St4Param1,
				Param2:    data.St4Param2,
				Param3:    data.St4Param3,
				Param4:    data.St4Param4,
				MaxDefHp:  data.St4DamageThresholdHp,
				MaxAttAtk: data.St4DamageThresholdAtk,
				Condition: data.St4Judge,
				Pvp:       data.IsPvp4 == 1,
			}
			effects = append(effects, &effectInfo)
		}
		if (data.Type == 1 || data.Type == 2) && effects == nil {
			panic(fmt.Sprintf("skill info gen effect error %d %s", data.Id, fileName))
		}

		var attrs []SkillAttr
		if data.AttrType1 > 0 && data.AttrType1 < uint32(AttrMaxNum) {
			attrInfo := SkillAttr{
				Type:  data.AttrType1,
				Value: int64(data.AttrValue1),
			}
			attrs = append(attrs, attrInfo)
		}
		if data.AttrType2 > 0 && data.AttrType2 < uint32(AttrMaxNum) {
			attrInfo := SkillAttr{
				Type:  data.AttrType2,
				Value: int64(data.AttrValue2),
			}
			attrs = append(attrs, attrInfo)
		}
		if data.AttrType3 > 0 && data.AttrType3 < uint32(AttrMaxNum) {
			attrInfo := SkillAttr{
				Type:  data.AttrType3,
				Value: int64(data.AttrValue3),
			}
			attrs = append(attrs, attrInfo)
		}
		if data.DecAttrType1 > 0 && data.DecAttrType1 < uint32(AttrMaxNum) {
			attrInfo := SkillAttr{
				Type:  data.DecAttrType1,
				Value: data.DecAttrValue1,
			}
			attrs = append(attrs, attrInfo)
		}
		if data.RaisePassive1 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassive1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassive1))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassive2 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassive2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassive2))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassive3 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassive3, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassive3))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		dataExt.Id = data.Id
		dataExt.Type = data.Type
		dataExt.AttackType = data.AttackType
		dataExt.SkillGroup = data.SkillGroup
		dataExt.FirstCd = data.FirstCd
		dataExt.CdRound = data.Cd
		dataExt.Type2 = data.Type2
		if data.Passive1 > 0 {
			dataExt.EffectPassiveSkills = append(dataExt.EffectPassiveSkills, data.Passive1)
		}
		if data.Passive2 > 0 {
			dataExt.EffectPassiveSkills = append(dataExt.EffectPassiveSkills, data.Passive2)
		}
		if data.Passive3 > 0 {
			dataExt.EffectPassiveSkills = append(dataExt.EffectPassiveSkills, data.Passive3)
		}
		dataExt.Effects = effects
		dataExt.PassiveSkill1 = data.PassiveSkill1
		dataExt.PassiveSkill2 = data.PassiveSkill2
		dataExt.PassiveSkill3 = data.PassiveSkill3
		dataExt.PassiveSkill4 = data.PassiveSkill4
		dataExt.PassiveSkill5 = data.PassiveSkill5
		dataExt.Level = data.Level
		dataExt.Attrs = attrs
		dataExt.Priority = data.Priority
		dataExt.Power = data.Power

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
		if _, exist := m.Groups[dataExt.SkillGroup]; !exist {
			m.Groups[dataExt.SkillGroup] = make(map[uint32]*SkillInfoExt)
		}
		m.Groups[dataExt.SkillGroup][dataExt.Level] = dataExt

	}
	return nil
}

func (m *SkillInfoManager) GetRecordById(id uint32) *SkillInfoExt {
	return m.Datas[id]
}

func (m *SkillInfoManager) Index(key uint32) *SkillInfoExt {
	return m.Datas[key]
}

func (m *SkillInfoManager) Group(key uint32) map[uint32]*SkillInfoExt {
	return m.Groups[key]
}

func (m *SkillInfoManager) GroupLevel(group, level uint32) *SkillInfoExt {
	if info, exist := m.Groups[group]; exist {
		return info[level]
	}
	return nil
}
