package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SeasonMapTradeInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*SeasonMapTradeInfoExt
}

type SeasonMapTradeInfoExt struct {
	TradeGroupId uint32
	TradeGoods   []*SeasonMapTradeInfo
}

func (s *SeasonMapTradeInfoExt) GetTradeGoodsInfo(goodsID uint32) *SeasonMapTradeInfo {
	for _, tradeGoods := range s.TradeGoods {
		if tradeGoods.GoodsId == goodsID {
			return tradeGoods
		}
	}
	return nil
}

func newSeasonMapTradeInfoManager(xmlData *XmlData) *SeasonMapTradeInfoManager {
	m := &SeasonMapTradeInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SeasonMapTradeInfoManager) name() string {
	return "season_map_trade_info.xml"
}

func (m *SeasonMapTradeInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SeasonMapTradeInfoManager) checkData() error {
	return nil
}

func (m *SeasonMapTradeInfoManager) load(dir string, show bool) error {
	tmp := &SeasonMapTradeInfos{}
	fileName := filepath.Join(dir, "season_map_trade_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*SeasonMapTradeInfoExt, len(tmp.Datas))

	for _, data := range tmp.Datas {
		data.prepare()
		if dataExt, exist := m.Datas[data.TradeGroupId]; exist {
			dataExt.TradeGoods = append(dataExt.TradeGoods, data)
		} else {
			dataExt = &SeasonMapTradeInfoExt{
				TradeGroupId: data.TradeGroupId,
			}
			dataExt.TradeGoods = append(dataExt.TradeGoods, data)
			m.Datas[data.TradeGroupId] = dataExt
		}
	}
	return nil
}

func (m *SeasonMapTradeInfoManager) Index(key uint32) *SeasonMapTradeInfoExt {
	return m.Datas[key]
}
