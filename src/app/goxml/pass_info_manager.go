package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type PassInfoManager struct {
	xmlData            *XmlData
	Datas              map[uint32]*PassInfoExt
	CycleBeReplaceInfo map[uint32]uint32 // 被替换的战令=> 替换的战令(循环战令根据开启条件进行替换)
	AllCyclePass       []*PassInfoExt    // 所有的循环战令
}

type PassInfoExt struct {
	Id                uint32
	SdkPid            string
	SdkPid2           string
	ModuleFunctionId  uint32
	BounsPoint        uint32
	PassType          uint32
	PreRestriction    uint32
	MailId            uint32
	OpenDay           int64
	EndDay            int64
	CloseServerDay    int64
	CloseServerDay2   int64
	UnlockDay         uint32
	CloseDay          uint32
	ActiveCloseDay    uint32
	Cycle             uint32
	BuylevelUnlockDay uint32
	LevelCap          uint32
	BuylevelRatio     uint32
	InternalId        uint32
	InternalId2       uint32
	VipClRes          []*cl.Resource
	VipClRes2         []*cl.Resource
	SeasonID          uint32
	Replace           uint32
}

func newPassInfoManager(xmlData *XmlData) *PassInfoManager {
	m := &PassInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *PassInfoManager) name() string {
	return "PassInfo"
}

func (m *PassInfoManager) ignoreForCheck() bool {
	return false
}

func (m *PassInfoManager) checkData() error {
	return nil
}

func (m *PassInfoManager) load(dir string, isShow bool) error {
	tmp := &PassInfos{}
	fileName := filepath.Join(dir, "pass_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*PassInfoExt, len(tmp.Datas))
	m.CycleBeReplaceInfo = make(map[uint32]uint32)
	m.AllCyclePass = make([]*PassInfoExt, 0)

	for _, data := range tmp.Datas {
		data.prepare()
		if len(data.VipClRes) == 0 {
			panic(fmt.Sprintf("file %s config error. vip count is zero. id: %d", fileName, data.Id))
		}

		checkF := legalPassType[data.PassType]
		if checkF == nil {
			panic(fmt.Sprintf("file %s config error, data passType:%d is error", fileName, data.Id))
		}

		_, exist := m.Datas[data.Id]
		if exist {
			panic(fmt.Sprintf("file %s config error, data id:%d is repeated", fileName, data.Id))
		}
		m.Datas[data.Id] = checkF(data, m)
	}
	for beReplace, replace := range m.CycleBeReplaceInfo {
		beReplaceInfo := m.Index(beReplace)
		if beReplaceInfo == nil {
			panic(fmt.Sprintf("file %s config error, data id:%d cant find", fileName, beReplace))
		}
		replaceInfo := m.Index(replace)
		if replaceInfo == nil {
			panic(fmt.Sprintf("file %s config error, data id:%d cant find", fileName, replace))
		}
		if replaceInfo.UnlockDay <= beReplaceInfo.UnlockDay {
			panic(fmt.Sprintf("file %s config error, id:%d cant replace id:%d UnlockDay error", fileName, replace, beReplace))
		}
		if (replaceInfo.UnlockDay-beReplaceInfo.UnlockDay)%beReplaceInfo.Cycle != 0 {
			panic(fmt.Sprintf("file %s config error, id:%d cant replace id:%d UnlockDay error", fileName, replace, beReplace))
		}
	}
	return nil
}

func (m *PassInfoManager) GetRecordById(id uint32) *PassInfoExt {
	return m.Datas[id]
}

func (m *PassInfoManager) Index(key uint32) *PassInfoExt {
	return m.Datas[key]
}

func (m *PassInfoManager) Len() int {
	return len(m.Datas)
}

func (m *PassInfoManager) GetUnlockDay(passID uint32) uint32 {
	if passInfo, exist := m.Datas[passID]; exist {
		return passInfo.UnlockDay
	}

	return 0
}

func passOnceCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	if info == nil {
		panic(fmt.Sprintf("pass_info.xml info is nil"))
	}

	if info.Cycle != 0 {
		panic(fmt.Sprintf("pass_info.xml config error. id: %d once pass Check error cycle is zero", info.Id))
	}

	return &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		PreRestriction:    info.PreRestriction,
		MailId:            info.MailId,
		OpenDay:           0,
		EndDay:            0,
		CloseServerDay:    0,
		CloseServerDay2:   0,
		UnlockDay:         info.UnlockDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
	}
}

func passCycleCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	if info == nil {
		panic("pass_info.xml info is nil")
	}

	if info.Cycle == 0 {
		panic(fmt.Sprintf("pass_info.xml config error. cycle day is zero. id: %d", info.Id))
	}
	if info.BuylevelRatio == 0 {
		panic(fmt.Sprintf("pass_info.xml config error. BuylevelValue is zero. id: %d", info.Id))
	}
	if info.Replace != 0 {
		m.CycleBeReplaceInfo[info.Replace] = info.Id
	}

	passInfo := &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		MailId:            info.MailId,
		OpenDay:           0,
		EndDay:            0,
		CloseServerDay:    0,
		CloseServerDay2:   0,
		UnlockDay:         info.UnlockDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
		Replace:           info.Replace,
	}
	m.AllCyclePass = append(m.AllCyclePass, passInfo)
	return passInfo
}

func passActivityStoryCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	openTime, err := time.ParseInLocation(TIME_LAYOUT, info.OpenDay, time.Local)
	if err != nil {
		panic(fmt.Sprintf("pass_info data:%d openTime failed: %v err:%s", info.Id, info.OpenDay, err))
	}

	endTime, err := time.ParseInLocation(TIME_LAYOUT, info.EndDay, time.Local)
	if err != nil {
		panic(fmt.Sprintf("pass_info data:%d endTime failed: %v err:%s", info.Id, info.EndDay, err))
	}

	closeServerUnix := int64(0)
	if info.CloseServerDate != "" {
		tm, err := time.ParseInLocation(TIME_LAYOUT, info.CloseServerDate, time.Local)
		if err != nil {
			panic(fmt.Sprintf("pass_info data:%d close server date failed: %v err:%s", info.Id, info.CloseServerDate, err))
		}
		closeServerUnix = tm.Unix()
	}

	closeServerUnix2 := int64(0)
	if info.CloseServerDate2 != "" {
		tm, err := time.ParseInLocation(TIME_LAYOUT, info.CloseServerDate2, time.Local)
		if err != nil {
			panic(fmt.Sprintf("pass_info data:%d close server date failed: %v err:%s", info.Id, info.CloseServerDate, err))
		}
		closeServerUnix2 = tm.Unix()
	}

	return &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		MailId:            info.MailId,
		OpenDay:           openTime.Unix(),
		EndDay:            endTime.Unix(),
		CloseServerDay:    closeServerUnix,
		CloseServerDay2:   closeServerUnix2,
		UnlockDay:         info.UnlockDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
	}
}

func passSeasonCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	openTime, err := time.ParseInLocation(TIME_LAYOUT, info.OpenDay, time.Local)
	if err != nil {
		panic(fmt.Sprintf("pass_info data:%d openTime failed: %v err:%s", info.Id, info.OpenDay, err))
	}

	endTime, err := time.ParseInLocation(TIME_LAYOUT, info.EndDay, time.Local)
	if err != nil {
		panic(fmt.Sprintf("pass_info data:%d endTime failed: %v err:%s", info.Id, info.EndDay, err))
	}

	closeServerUnix := int64(0)
	if info.CloseServerDate != "" {
		tm, err := time.ParseInLocation(TIME_LAYOUT, info.CloseServerDate, time.Local)
		if err != nil {
			panic(fmt.Sprintf("pass_info data:%d close server date failed: %v err:%s", info.Id, info.CloseServerDate, err))
		}
		closeServerUnix = tm.Unix()
	}

	closeServerUnix2 := int64(0)
	if info.CloseServerDate2 != "" {
		tm, err := time.ParseInLocation(TIME_LAYOUT, info.CloseServerDate2, time.Local)
		if err != nil {
			panic(fmt.Sprintf("pass_info data:%d close server date failed: %v err:%s", info.Id, info.CloseServerDate, err))
		}
		closeServerUnix2 = tm.Unix()
	}
	return &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		MailId:            info.MailId,
		OpenDay:           openTime.Unix(),
		EndDay:            endTime.Unix(),
		CloseServerDay:    closeServerUnix,
		CloseServerDay2:   closeServerUnix2,
		UnlockDay:         info.UnlockDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
	}
}

func passCreateUserCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	if info == nil {
		panic(fmt.Sprintf("pass_info.xml info is nil"))
	}

	if info.Cycle != 0 {
		panic(fmt.Sprintf("pass_info.xml config error. id: %d pass Check error cycle is zero", info.Id))
	}

	if info.UnlockDay >= info.CloseDay {
		panic(fmt.Sprintf("pass_info.xml config error. id:%d pass unlock day:%d more than info closeDay:%d", info.Id, info.UnlockDay, info.CloseDay))
	}

	return &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		PreRestriction:    info.PreRestriction,
		MailId:            info.MailId,
		OpenDay:           0,
		EndDay:            0,
		CloseServerDay:    0,
		CloseServerDay2:   0,
		UnlockDay:         info.UnlockDay,
		CloseDay:          info.CloseDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
	}
}

func passOpenServerCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	if info == nil {
		panic(fmt.Sprintf("pass_info.xml info is nil"))
	}

	if info.Cycle != 0 {
		panic(fmt.Sprintf("pass_info.xml config error. id: %d pass Check error cycle is zero", info.Id))
	}

	if info.UnlockDay >= info.CloseDay {
		panic(fmt.Sprintf("pass_info.xml config error. id:%d pass unlock day:%d more than info closeDay:%d", info.Id, info.UnlockDay, info.CloseDay))
	}

	return &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		PreRestriction:    info.PreRestriction,
		MailId:            info.MailId,
		OpenDay:           0,
		EndDay:            0,
		CloseServerDay:    0,
		CloseServerDay2:   0,
		UnlockDay:         info.UnlockDay,
		CloseDay:          info.CloseDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
		ActiveCloseDay:    info.ActiveCloseDay,
	}
}

func passSeasonWeekCheck(info *PassInfo, m *PassInfoManager) *PassInfoExt {
	openTime, err := time.ParseInLocation(TIME_LAYOUT, info.OpenDay, time.Local)
	if err != nil {
		panic(fmt.Sprintf("pass_info data:%d openTime failed: %v err:%s", info.Id, info.OpenDay, err))
	}

	endTime, err := time.ParseInLocation(TIME_LAYOUT, info.EndDay, time.Local)
	if err != nil {
		panic(fmt.Sprintf("pass_info data:%d endTime failed: %v err:%s", info.Id, info.EndDay, err))
	}

	closeServerUnix := int64(0)
	if info.CloseServerDate != "" {
		tm, err := time.ParseInLocation(TIME_LAYOUT, info.CloseServerDate, time.Local)
		if err != nil {
			panic(fmt.Sprintf("pass_info data:%d close server date failed: %v err:%s", info.Id, info.CloseServerDate, err))
		}
		closeServerUnix = tm.Unix()
	}

	closeServerUnix2 := int64(0)
	if info.CloseServerDate2 != "" {
		tm, err := time.ParseInLocation(TIME_LAYOUT, info.CloseServerDate2, time.Local)
		if err != nil {
			panic(fmt.Sprintf("pass_info data:%d close server date failed: %v err:%s", info.Id, info.CloseServerDate, err))
		}
		closeServerUnix2 = tm.Unix()
	}

	return &PassInfoExt{
		Id:                info.Id,
		SdkPid:            info.SdkPid,
		SdkPid2:           info.SdkPid2,
		ModuleFunctionId:  info.ModuleFunctionId,
		BounsPoint:        info.BounsPoint,
		PassType:          info.PassType,
		MailId:            info.MailId,
		OpenDay:           openTime.Unix(),
		EndDay:            endTime.Unix(),
		CloseServerDay:    closeServerUnix,
		CloseServerDay2:   closeServerUnix2,
		UnlockDay:         info.UnlockDay,
		Cycle:             info.Cycle,
		BuylevelUnlockDay: info.BuylevelUnlockDay,
		LevelCap:          info.LevelCap,
		BuylevelRatio:     info.BuylevelRatio,
		InternalId:        info.InternalId,
		VipClRes:          info.VipClRes,
		InternalId2:       info.Internal2Id,
		VipClRes2:         info.Vip2ClRes,
		SeasonID:          info.SeasonId,
	}
}

func (e *PassInfoExt) SeasonCheck(seasonID uint32) bool {
	if e.SeasonID == 0 {
		return true
	}
	return e.SeasonID == seasonID
}

func (m *PassInfoManager) GetCycleBeReplace(passID uint32) uint32 {
	return m.CycleBeReplaceInfo[passID]
}

func (m *PassInfoManager) GetAllCyclePass() []*PassInfoExt {
	return m.AllCyclePass
}
