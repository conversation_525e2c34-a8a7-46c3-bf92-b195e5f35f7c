package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SeasonMapTradeGoodsInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*SeasonMapTradeGoodsInfo
	ResetsGoods []*SeasonMapTradeGoodsInfo // 每日重置价格
}

func newSeasonMapTradeGoodsInfoManager(xmlData *XmlData) *SeasonMapTradeGoodsInfoManager {
	m := &SeasonMapTradeGoodsInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SeasonMapTradeGoodsInfoManager) name() string {
	return "season_map_trade_goods_info.xml"
}

func (m *SeasonMapTradeGoodsInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SeasonMapTradeGoodsInfoManager) checkData() error {
	return nil
}

func (m *SeasonMapTradeGoodsInfoManager) load(dir string, show bool) error {
	tmp := &SeasonMapTradeGoodsInfos{}
	fileName := filepath.Join(dir, "season_map_trade_goods_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*SeasonMapTradeGoodsInfo, len(tmp.Datas))
	m.ResetsGoods = make([]*SeasonMapTradeGoodsInfo, 0)

	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
		if data.IfReset == 1 {
			m.ResetsGoods = append(m.ResetsGoods, data)
		}
	}
	return nil
}

func (m *SeasonMapTradeGoodsInfoManager) Index(key uint32) *SeasonMapTradeGoodsInfo {
	return m.Datas[key]
}

func (s *SeasonMapTradeGoodsInfo) FixPrice(price uint32, isSell bool) uint32 {
	if price < s.MinPrice {
		price = s.MinPrice
	}
	if !isSell {
		if price > s.BuyMaxPrice {
			price = s.BuyMaxPrice
		}
	} else if price > s.MaxPrice {
		price = s.MaxPrice
	}

	return price
}
