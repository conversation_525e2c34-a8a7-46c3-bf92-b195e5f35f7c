package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildConfigInfoManager struct {
	xmlData *XmlData
	Datas   map[string]*GuildConfigInfo

	NameLengthMax                            uint32         //公会名的字符数上限
	DeclarationLengthMax                     uint32         //公会宣言的字符数上限
	NoticeLengthMax                          uint32         //公会公告的字符数上限
	MailTitleLengthMax                       uint32         //全员邮件标题的字符数上限
	MailContentLengthMax                     uint32         //全员邮件内容的字符数上限
	CreateCost                               []*cl.Resource //公会创建消耗500钻
	NameChangeCost                           []*cl.Resource //修改公会名消耗500钻
	NameChangeDelay                          int64          //修改公会名的冷却时间3600秒
	LeaveJoinDelay                           int64          //手动退出、解散公会后再次加会、建会的冷却3600秒
	MailSendDelay                            int64          //每个人每次发送全员邮件有3600秒CD
	ViceCountMax                             uint32         //公会的副会长人数上限2人
	PresidentCheckActiveDays                 int64          //原会长离线时间>= 10天，触发自动转让
	MemberCheckActiveDays                    int64          //离线时间<7天的玩家，可以被自动转让会长
	ApplyingCountMax                         uint32         //玩家同一时间申请中的公会数上限为5个
	ApplyingReceiveMax                       uint32         //公会可接受的申请数上限为50个
	SignGetMoney                             []*cl.Resource //签到后可领取500公会贡献币
	SignGetExp                               uint32         //签到后可增加100点公会经验值
	ListGetOnce                              uint32         //公会列表一次拉取的数量
	DungeonStarBuffType                      uint32         //公会副本英雄每个星级增加的属性类型
	DungeonStarBuffValue                     uint32         //公会副本英雄每个星级增加的伤害加深属性
	DefTypeCoefficient                       uint32         //10%，公会副本monster_type=1时，怪物物防*110%，魔防*90%；公会副本monster_type=2时，怪物物防*90%，魔防*110%
	DungeonMaxChallengeTimes                 uint32         // 公会副本 - 可拥有的最大挑战次数
	DungeonBaseChallengeTimes                uint32         // 公会副本 - 每天的免费挑战次数
	DungeonChallengeTimesRecoverIntervalHour uint32         // 公会副本 - 挑战次数恢复间隔小时
	DungeonChallengeTimesBuyGroup            uint32         // 公会副本 - 购买次数价格组
	DungeonChallengeTimesBuyLimit            uint32         // 公会副本 - 购买上限
	messageBoardLengthMax                    uint32         // 公会副本 - 留言板单条留言字符上限
	guildDungeonChapterRankLikeAward         []*cl.Resource // 公会副本 - 章节排行榜点赞奖励
	guildDungeonChapterRankLikeMaxCount      uint32         // 公会副本 - 章节排行榜点赞上限
	guildDonateCount                         uint32         // 公会捐赠 - 每日捐献上限
	guildDungeonPoint                        uint32         // 公会副本 - 攻打活跃度
	guildDungeonRound                        uint32         // 公会副本 - 赛季的轮次   注意：改轮次的配置，必须对应修改guildDungeonOpenTime！！！！
	guildDungeonOpenTime                     int64          // 公会副本 - guildDungeonRound配置的轮次开始生效的赛季，次赛季的开始时间。
	guildDungeonDivisionLimit                uint32         // 公会副本 - 掉段限制，达到此段位后不再掉段
	guildDungeonNeedExtendDivision           uint32         // 公会副本 - 匹配房间需要扩展的段位
	guildDungeonExtendEndDivision            uint32         // 公会副本 - 匹配房间扩展截止的段位
	guildRecommendSameLanguageManyMemberNum  uint32         // 公会 - 推荐列表自己语言人数多的公会显示数量
	guildRecommendSameLanguageFewMemberNum   uint32         // 公会 - 推荐列表自己语言人数少的公会显示数量
	guildRecommendGuildMemberCntSplitNum     uint32         // 公会 - 推荐列表区分自己语言的公会人数多少的值（剩余多少空位，比这个多的算人数少）
	guildChestReceivePreWeek                 uint32         // 公会宝箱 - 每周领取次数
	guildChestFlowerID                       *cl.Resource   // 公会宝箱 - 回赠花朵的的道具ID
	guildChestFlowerTokenID                  *cl.Resource   // 公会宝箱 - 领取的花的ID
	guildChestFlowerRebate1                  uint32         // 公会宝箱 - 回赠花朵数量1
	guildChestFlowerRebate2                  uint32         // 公会宝箱 - 回赠花朵数量1
	guildChestMaxNum                         uint32         // 工会宝箱 - 发放中的宝箱数量上限
	guildChestReceiveMultiple                uint32         // 公会宝箱 - 单次领取上限倍率
	// guildChestMinToken                       uint32         // 公会宝箱 - 单次最少领奖数量

	guildRecActive                      uint32 // 公会推荐列表 - 优先推荐的活跃度要求
	guildRecPassActive                  uint32 // 公会推荐列表 - 不满员的老公会中剔除的活跃度要求
	guildRecNew                         uint32 // 公会推荐列表 - 符合新公会的天数要求
	guildRecNewCount                    uint32 // 公会推荐列表 - 不满员的新公会每页显示数量（相同语言）
	guildRecOldCount                    uint32 // 公会推荐列表 - 不满员的老公会每页显示数量（相同语言）
	guildRecOtherLanguageCount          uint32 // 公会推荐列表 - 其它语言公会每页显示数量
	guildRecFullCount                   uint32 // 公会推荐列表 - 满员公会每页显示数量（相同语言）
	guildKickOutLimit                   uint32 //公会每日踢人上限
	dungeonFirstWeekScoreChapterPer     uint32 // 副本第一周通关每个章节获得的分数
	dungeonFirstWeekScoreChapterStep    uint32 // 副本第一周可以获得积分的章节进度万分比
	dungeonFirstWeekScoreChapterStepPer uint32 // 副本第一周每个章节进度百分比可以获得的积分
	dungeonChapterScoreStartValue       uint32 //副本可获得积分的起始章节
	dungeonChapterScoreChapterPer       uint32 //副本没章节可获得的积分
	regimentCountMax                    uint32 //公会的团长人数上限2人

	medalOpenLevel        uint32 // 公会功勋开启等级
	guildMedalFlowerCount uint32 // 公会功勋 - 赠送花朵的数量

	strategyChapterRestoreBossLimit uint32 // 秘籍复活同一章节Boss的次数限制

	guildRecYesterdayNum     uint32 // 公会推荐列表 - 优先推荐的前日活跃人数要求
	guildRecPassYesterdayNum uint32 // 公会推荐列表 - 不满员的前日活跃人数活跃度要求

	guildTransferOpenCreateDayLimit uint32 // 转会推荐开启要求公会创建天数限制
	guildTransferOpenMemberLimit    uint32 // 转会推荐开启要求公会活跃人数限制
	guildTransferListMemberLimit    uint32 // 转会推荐列表中要求公会活跃人数
	guildQuickJoinYesterdayNumLimit uint32 // 快速加入老公前一日活跃人数限制
	guildCombineOpenDay             uint32 // 公会合并功能要求创建公会时间限制
	guildCombineApplySendMax        uint32 //公会合并发送申请数量上限
	guildCombineApplyReceiveMax     uint32 // 公会合并申请列表数量上限
	guildCombineApplyExpireTime     uint32 // 公会合并申请列表过期时间
	GuildMobilizationGuildLevel     uint32 // 开启所需公会等级
	GuildMobTaskLimitTime           int64  // 接取任务的限时
	GuildMobTaskLimit               uint32 // 个人同时接取任务的数量限制
	GuildMobTaskAcceptTimes         uint32 // 个人初始接取任务的次数
	GuildMobTaskFreshTimes          uint32 // 公会管理每天可刷新公会任务的次数
	GuildMobRankID                  uint32 // 公会竞赛排行榜id
	GuildMobAcceptBuyGroup          uint32 // 公会竞赛购买接取任务次数
	GuildMobFreshBuyGroup           uint32 // 公会竞赛购买刷新任务次数
	GuildMobTaskLogMax              uint32 // 公会竞赛完成任务日志的最大条数
	GuildMobDeleteTimes             int64  // 公会竞赛剩余多少时间可以删除
}

func newGuildConfigInfoManager(xmlData *XmlData) *GuildConfigInfoManager {
	m := &GuildConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildConfigInfoManager) name() string {
	return "GuildConfigInfo"
}

func (m *GuildConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildConfigInfoManager) checkData() error {
	if m.GetGuildDungeonOpenTime() != m.xmlData.SeasonInfoM.firstSeasonOpenTime {
		return errors.New("guildDungeonOpenTime not equal to firstSeasonOpenTime")
	}
	return nil
}

func (m *GuildConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildConfigInfos{}
	fileName := filepath.Join(dir, "guild_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))

	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}

	}
	m.Datas = make(map[string]*GuildConfigInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		/*if data.Count == 0 {
			panic(fmt.Sprintf("check data error %s %s", fileName, data.Key))
		}*/
		if (data.Key == "CREATE_COST" || data.Key == "NAME_CHANGE_COST" || data.Key == "SIGN_GET_MONEY" || data.Key == "TALENT_RESET_COST") && data.Type == 0 && data.Count == 0 {
			panic(fmt.Sprintf("check data error %s %s", fileName, data.Key))
		}
		if data.Key == "GUILD_DUNGEON_OPEN" && data.Date == "" {
			panic(fmt.Sprintf("check data error %s %s: guildDungeon open time is nil!!", fileName, data.Key))
		}
		if data.Key == "GUILD_DUNGEON_ROUND" && data.Count == 0 {
			panic(fmt.Sprintf("check data error %s %s: guildDungeon round 0!!!", fileName, data.Key))
		}
		if data.Key == "GUILD_KICK_OUT_LIMIT" && data.Count == 0 {
			panic(fmt.Sprintf("check data error %s %s: GUILD_KICK_OUT_LIMIT is 0", fileName, data.Key))
		}
		data.prepare()
		m.Datas[data.Key] = data
	}
	m.NameLengthMax = m.Datas["NAME_LENGH_MAX"].Count
	m.DeclarationLengthMax = m.Datas["DECLARATION_LENGH_MAX"].Count
	m.NoticeLengthMax = m.Datas["NOTICE_LENGH_MAX"].Count
	m.MailTitleLengthMax = m.Datas["MAIL_TITLE_LENGH_MAX"].Count
	m.MailContentLengthMax = m.Datas["MAIL_CONTENT_LENGH_MAX"].Count
	m.CreateCost = m.Datas["CREATE_COST"].ClRes
	m.NameChangeCost = m.Datas["NAME_CHANGE_COST"].ClRes
	m.NameChangeDelay = int64(m.Datas["NAME_CHANGE_DELAY"].Count)
	m.LeaveJoinDelay = int64(m.Datas["LEAVE_JOIN_DELAY"].Count)
	m.MailSendDelay = int64(m.Datas["MAIL_SEND_DELAY"].Count)
	m.ViceCountMax = m.Datas["VICE_COUNT_MAX"].Count
	m.PresidentCheckActiveDays = int64(m.Datas["PRESIDENT_CHECK_ACTIVE_DAYS"].Count)
	m.MemberCheckActiveDays = int64(m.Datas["MEMBER_CHECK_ACTIVE_DAYS"].Count) * 24 * 60 * 60
	m.ApplyingCountMax = m.Datas["APPLYING_COUNT_MAX"].Count
	m.ApplyingReceiveMax = m.Datas["APPLYING_RECEIVE_MAX"].Count
	m.SignGetMoney = m.Datas["SIGN_GET_MONEY"].ClRes
	m.SignGetExp = m.Datas["SIGN_GET_EXP"].Count
	m.ListGetOnce = m.Datas["LIST_GET_ONCE"].Count
	m.DungeonStarBuffType = m.Datas["DUNGEON_STAR_BUFF_TYPE"].Count
	m.DungeonStarBuffValue = m.Datas["DUNGEON_STAR_BUFF_VALUE"].Count
	m.DefTypeCoefficient = m.Datas["DEF_TYPE_COEFFICIENT"].Count
	m.DungeonMaxChallengeTimes = m.Datas["DUNGEON_MAX_TIME"].Count
	m.DungeonBaseChallengeTimes = m.Datas["DUNGEON_BASE_TIME"].Count
	m.DungeonChallengeTimesRecoverIntervalHour = m.Datas["DUNGEON_RECOVER_HOUR"].Count
	m.DungeonChallengeTimesBuyGroup = m.Datas["DUNGEON_TIME_BUY"].Count
	m.DungeonChallengeTimesBuyLimit = m.Datas["DUNGEON_TIME_BUY_LIMIT"].Count
	m.messageBoardLengthMax = m.Datas["ANNOUNCE_MAX"].Count
	m.guildDungeonChapterRankLikeAward = m.Datas["GUILD_VOTE_REWARD"].ClRes
	m.guildDungeonChapterRankLikeMaxCount = m.Datas["GUILD_VOTE_TIME"].Count
	m.guildDonateCount = m.Datas["GUILD_DONATE_COUNT"].Count
	m.guildDungeonPoint = m.Datas["GUILD_DUNGEON_POINT"].Count
	m.guildDungeonRound = m.Datas["GUILD_DUNGEON_ROUND"].Count
	t, err := time.ParseInLocation(TIME_LAYOUT, strings.Trim(m.Datas["GUILD_DUNGEON_OPEN"].Date, " "), time.Local)
	if err != nil {
		panic(fmt.Sprintf("check data error %s guildDungeon open time error. err:%s ", fileName, err))
	}
	thisRoundOpenTm := t.Unix()

	if thisRoundOpenTm != int64(util.WeekdayZeroByTime(thisRoundOpenTm, 5)) {
		panic(fmt.Sprintf("data check error file: %s Date:%s not Monday", fileName, m.Datas["GUILD_DUNGEON_OPEN"].Date))
	}
	m.guildDungeonOpenTime = thisRoundOpenTm
	m.guildDungeonDivisionLimit = m.Datas["GUILD_DUNGEON_LIMIT"].Count
	m.guildDungeonNeedExtendDivision = m.Datas["GUILD_DUNGEON_DAN1"].Count
	m.guildDungeonExtendEndDivision = m.Datas["GUILD_DUNGEON_DAN2"].Count
	m.guildRecommendSameLanguageManyMemberNum = m.Datas["GUILD_REC_GROUP1"].Count
	m.guildRecommendSameLanguageFewMemberNum = m.Datas["GUILD_REC_GROUP2"].Count
	if m.guildRecommendSameLanguageManyMemberNum+m.guildRecommendSameLanguageFewMemberNum >= 20 {
		panic(fmt.Sprintf("data check error file: %s guild recommend many and few error. many:%d few:%d",
			fileName, m.Datas["GUILD_REC_GROUP1"].Count, m.Datas["GUILD_REC_GROUP2"].Count))
	}
	m.guildRecommendGuildMemberCntSplitNum = m.Datas["GUILD_REC_GROUP3"].Count
	m.guildChestReceivePreWeek = m.Datas["GUILD_CHEST_RECEIVE_PER_WEEK"].Count
	tokenInfo := m.xmlData.ItemTokenInfoM.Index(m.Datas["GUILD_CHEST_FLOWER_ID"].Value)
	if tokenInfo == nil {
		panic(fmt.Sprintf("data check error file: %s GUILD_CHEST_FLOWER_ID token id:%d is nil",
			fileName, m.Datas["GUILD_CHEST_FLOWER_ID"].Value))
	}
	m.guildChestFlowerID = &cl.Resource{
		Type:  uint32(common.RESOURCE_TOKEN),
		Value: m.Datas["GUILD_CHEST_FLOWER_ID"].Value,
	}

	tokenInfo = m.xmlData.ItemTokenInfoM.Index(m.Datas["GUILD_CHEST_FLOWER_TOKEN_ID"].Value)
	if tokenInfo == nil {
		panic(fmt.Sprintf("data check error file: %s GUILD_CHEST_FLOWER_TOKEN_ID token id:%d is nil",
			fileName, m.Datas["GUILD_CHEST_FLOWER_TOKEN_ID"].Value))
	}
	m.guildChestFlowerTokenID = &cl.Resource{
		Type:  uint32(common.RESOURCE_TOKEN),
		Value: m.Datas["GUILD_CHEST_FLOWER_TOKEN_ID"].Value,
	}

	m.guildChestFlowerRebate1 = m.Datas["GUILD_CHEST_FLOWER_REBATE_1"].Count
	if m.guildChestFlowerRebate1 == 0 {
		panic(fmt.Sprintf("data check error file: %s GUILD_CHEST_FLOWER_REBATE_1 must more than 0",
			fileName))
	}
	m.guildChestFlowerRebate2 = m.Datas["GUILD_CHEST_FLOWER_REBATE_2"].Count
	if m.guildChestFlowerRebate2 == 0 {
		panic(fmt.Sprintf("data check error file: %s GUILD_CHEST_FLOWER_REBATE_2 must more than 0",
			fileName))
	}
	m.guildChestMaxNum = m.Datas["GUILD_CHEST_MAX_NUM"].Count
	m.guildChestReceiveMultiple = m.Datas["GUILD_CHEST_RECEIVE_MULTIPLE"].Count
	if m.guildChestReceiveMultiple == 0 {
		panic(fmt.Sprintf("data check error file: %s GUILD_CHEST_RECEIVE_MULTIPLE must more than 0",
			fileName))
	}
	// m.guildChestMinToken = m.Datas["GUILD_CHEST_MIN_TOKEN"].Count

	m.guildRecActive = m.Datas["GUILD_REC_ACTIVE_1"].Count
	m.guildRecPassActive = m.Datas["GUILD_REC_ACTIVE_2"].Count
	m.guildRecNew = m.Datas["GUILD_REC_NEW_TIME"].Count
	m.guildRecNewCount = m.Datas["GUILD_REC_NEW_COUNT"].Count
	m.guildRecOtherLanguageCount = m.Datas["GUILD_REC_LANGUAGE_COUNT"].Count
	m.guildRecFullCount = m.Datas["GUILD_REC_MAX_COUNT"].Count
	m.guildRecOldCount = m.Datas["GUILD_REC_OLD_COUNT"].Count
	m.guildKickOutLimit = m.Datas["GUILD_KICK_OUT_LIMIT"].Count
	if m.guildRecNewCount+m.guildRecOtherLanguageCount+m.guildRecFullCount+m.guildRecOldCount != 20 {
		panic(fmt.Sprintf("data check error file: %s guild recommend num error", fileName))
	}

	m.dungeonFirstWeekScoreChapterStepPer = m.Datas["WAR_FIRST_SCORE_STEP_PER"].Count
	m.dungeonFirstWeekScoreChapterPer = m.Datas["WAR_FIRST_SCORE_CHAPTER_PER"].Count
	m.dungeonFirstWeekScoreChapterStep = m.Datas["WAR_FIRST_SCORE_STEP"].Count
	m.dungeonChapterScoreStartValue = m.Datas["CHAPTER_SCORE_START_VALUE"].Count
	m.dungeonChapterScoreChapterPer = m.Datas["CHAPTER_SCORE_CHAPTER_PER"].Count
	if m.dungeonFirstWeekScoreChapterStep == 0 || m.dungeonFirstWeekScoreChapterPer == 0 ||
		m.dungeonFirstWeekScoreChapterStepPer == 0 || m.dungeonChapterScoreStartValue == 0 ||
		m.dungeonChapterScoreChapterPer == 0 {
		panic(fmt.Sprintf("data check error file: %s chapter add point error.", fileName))
	}
	m.regimentCountMax = m.Datas["REGIMENT_COUNT_MAX"].Count

	m.medalOpenLevel = m.Datas["MEDAL_OPEN_LEVEL"].Count
	if m.medalOpenLevel == 0 {
		panic(fmt.Sprintf("data check error file: %s MEDAL_OPEN_LEVEL is 0.", fileName))
	}

	m.guildMedalFlowerCount = m.Datas["MEDAL_FLOWER"].Count
	if m.guildMedalFlowerCount == 0 {
		panic(fmt.Sprintf("data check error file: %s guildMedalFlowerCount is 0.", fileName))
	}

	m.strategyChapterRestoreBossLimit = m.Datas["DUNGEON_STRATEGY_REVIVE_LIMIT_NUM"].Count
	if m.strategyChapterRestoreBossLimit == 0 {
		panic(fmt.Sprintf("data check error file: %s DUNGEON_STRATEGY_REVIVE_LIMIT_NUM is 0.", fileName))
	}

	m.guildRecYesterdayNum = m.Datas["GUILD_REC_ACTIVE_3"].Count
	m.guildRecPassYesterdayNum = m.Datas["GUILD_REC_ACTIVE_4"].Count
	m.guildQuickJoinYesterdayNumLimit = m.Datas["GUILD_REC_JOIN_FAIL"].Count

	m.guildTransferOpenCreateDayLimit = m.Datas["CHANGE_RECO_OPEN_DAY"].Count
	m.guildTransferOpenMemberLimit = m.Datas["CHANGE_RECO_OPEN_PLAYER_MIN"].Count
	m.guildTransferListMemberLimit = m.Datas["CHANGE_RECO_GUILD_LIST_PLAYER"].Count
	if m.guildTransferOpenCreateDayLimit == 0 || m.guildTransferOpenMemberLimit == 0 || m.guildTransferListMemberLimit == 0 {
		panic(fmt.Sprintf("data check error file: %s CHANGE_RECO_OPEN_DAY or CHANGE_RECO_OPEN_PLAYER_MIN or CHANGE_RECO_GUILD_LIST_PLAYER is 0.", fileName))
	}
	m.guildCombineOpenDay = m.Datas["GUILD_COMBINE_OPEN"].Count
	m.guildCombineApplyExpireTime = m.Datas["COMBINE_APPLY_OVERTIME"].Count
	m.guildCombineApplySendMax = m.Datas["COMBINE_APPLY_SEND_MAX"].Count
	m.guildCombineApplyReceiveMax = m.Datas["COMBINE_APPLY_RECEIVE_MAX"].Count
	m.GuildMobilizationGuildLevel = m.Datas["MOBILIZATION_UNLOCK_GUILD_LEVEL"].Count
	m.GuildMobTaskLimitTime = int64(m.Datas["MOBILIZATION_TASK_COMPLETE"].Count)
	m.GuildMobTaskLimit = m.Datas["MOBILIZATION_TASK_PERSEN_ACCEPT_NUM"].Count
	m.GuildMobTaskAcceptTimes = m.Datas["MOBILIZATION_TASK_ACCEPT_TIMES"].Count
	m.GuildMobTaskFreshTimes = m.Datas["MOBILIZATION_FLASH_TIMES_RECOVER"].Count
	m.GuildMobRankID = m.Datas["MOBILIZATION_TASK_RANKING"].Count
	m.GuildMobAcceptBuyGroup = m.Datas["MOBILIZATION_TASK_ACCEPT_BUY_PRICE"].Count
	m.GuildMobFreshBuyGroup = m.Datas["MOBILIZATION_TASK_FLASH_BUY_PRICE"].Count
	m.GuildMobTaskLogMax = m.Datas["MOBILIZATION_TASK_LOG_MAX"].Count
	m.GuildMobDeleteTimes = int64(m.Datas["MOBILIZATION_TASK_DELETE_TIME"].Count)
	return nil
}

func (m *GuildConfigInfoManager) Index(key string) *GuildConfigInfo {
	return m.Datas[key]
}

func (m *GuildConfigInfoManager) GetDungeonChallengeMaxCount() uint32 {
	return m.DungeonMaxChallengeTimes
}

func (m *GuildConfigInfoManager) GetDungeonChallengeFreeCount() uint32 {
	return m.DungeonBaseChallengeTimes
}

func (m *GuildConfigInfoManager) GetChallengeTimesRecoverIntervalHours() uint32 {
	return m.DungeonChallengeTimesRecoverIntervalHour
}

func (m *GuildConfigInfoManager) GetBuyCountGroup() uint32 {
	return m.DungeonChallengeTimesBuyGroup
}

func (m *GuildConfigInfoManager) GetBuyLimit() uint32 {
	return m.DungeonChallengeTimesBuyLimit
}

func (m *GuildConfigInfoManager) GetMessageBoardLengthMax() uint32 {
	return m.messageBoardLengthMax
}

func (m *GuildConfigInfoManager) GetGuildDungeonChapterRankLikeAwards() []*cl.Resource {
	return m.guildDungeonChapterRankLikeAward
}

func (m *GuildConfigInfoManager) GetGuildDungeonLikeMaxCount() uint32 {
	return m.guildDungeonChapterRankLikeMaxCount
}

func (m *GuildConfigInfoManager) GetGuildDonateCount() uint32 {
	return m.guildDonateCount
}

func (m *GuildConfigInfoManager) GetGuildDungeonActivityPoint() uint32 {
	return m.guildDungeonPoint
}

func (m *GuildConfigInfoManager) GetRoomCapacity() uint32 {
	return 8
}

func (m *GuildConfigInfoManager) GetGuildDungeonSeasonRound() uint32 {
	return m.guildDungeonRound
}

func (m *GuildConfigInfoManager) GetGuildDungeonOpenTime() int64 {
	return m.guildDungeonOpenTime
}

func (m *GuildConfigInfoManager) GetGuildDungeonDivisionLimit() uint32 {
	return m.guildDungeonDivisionLimit
}

func (m *GuildConfigInfoManager) GetGuildDungeonNeedExtendDivision() uint32 {
	return m.guildDungeonNeedExtendDivision
}

func (m *GuildConfigInfoManager) GetGuildDungeonExtendEndDivision() uint32 {
	return m.guildDungeonExtendEndDivision
}

func (m *GuildConfigInfoManager) GetGuildRecommendSameLanguageManyMemberNum() uint32 {
	return m.guildRecommendSameLanguageManyMemberNum
}

func (m *GuildConfigInfoManager) GetGuildRecommendSameLanguageFewMemberNum() uint32 {
	return m.guildRecommendSameLanguageFewMemberNum
}

func (m *GuildConfigInfoManager) GetGuildRecommendGuildMemberCntSplitNum() uint32 {
	return m.guildRecommendGuildMemberCntSplitNum
}

func (m *GuildConfigInfoManager) GetGuildChestReceivePreWeek() uint32 {
	return m.guildChestReceivePreWeek
}

func (m *GuildConfigInfoManager) GetGuildChestFlowerRebate1() uint32 {
	return m.guildChestFlowerRebate1
}

func (m *GuildConfigInfoManager) GetGuildChestFlowerRebate2() uint32 {
	return m.guildChestFlowerRebate2
}

func (m *GuildConfigInfoManager) GetGuildChestMaxNum() uint32 {
	return m.guildChestMaxNum
}

func (m *GuildConfigInfoManager) GetGuildChestReceiveMultiple() uint32 {
	return m.guildChestReceiveMultiple
}

func (m *GuildConfigInfoManager) GetSetLikeReduceRes(likeType uint32) *cl.Resource {
	var ret *cl.Resource
	switch likeType {
	case uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_NORMAL):
		ret = m.guildChestFlowerID.Clone()
		ret.Count = m.guildChestFlowerRebate1
	case uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_SPECIFICAL):
		ret = m.guildChestFlowerID.Clone()
		ret.Count = m.guildChestFlowerRebate2
	}
	return ret
}

func (m *GuildConfigInfoManager) GetRecvLikeRes(count uint32) *cl.Resource {
	ret := m.guildChestFlowerTokenID.Clone()
	ret.Count = count
	return ret
}

func (m *GuildConfigInfoManager) GetLikeTypeFlowerCount(likeType uint32) uint32 {
	switch likeType {
	case uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_NORMAL):
		return m.guildChestFlowerRebate1
	case uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_SPECIFICAL):
		return m.guildChestFlowerRebate2
	}
	return 0
}

// GetGuildRecPriorActive
// @Description:  获取优先推荐的活跃度要求
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetGuildRecPriorActive() uint32 {
	return m.guildRecActive
}

// GetGuildRecPassActive
// @Description:  获取不满员的老公会需要去掉的活跃度要求
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetGuildRecPassActive() uint32 {
	return m.guildRecPassActive
}

// GetRecNewGuildDay
// @Description: 获取推荐列表新公会的创建天数要求
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetRecNewGuildDay() uint32 {
	return m.guildRecNew
}

// GetRecNotFullNewGuildCnt
// @Description: 获取推荐列表不满员的新公会每页显示数量（相同语言）
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetRecNotFullNewGuildCnt() int {
	return int(m.guildRecNewCount)
}

// GetRecNotFullOldGuildCnt
// @Description:  获取推荐列表不满员的老公会每页显示数量（相同语言）
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetRecNotFullOldGuildCnt() int {
	return int(m.guildRecOldCount)
}

// GetRecNotFullGuildCnt
// @Description:  获取推荐列表不满员公会每页显示数量（相同语言）
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetRecNotFullGuildCnt() int {
	return m.GetRecNotFullNewGuildCnt() + m.GetRecNotFullOldGuildCnt()
}

// GetRecFullGuildCnt
// @Description:  获取满员公会每页显示数量（相同语言）
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetRecFullGuildCnt() uint32 {
	return m.guildRecFullCount
}

// GetRecOtherLanguageCnt
// @Description:  获取其它语言公会每页显示数量
// @receiver g
// @return uint32
func (m *GuildConfigInfoManager) GetRecOtherLanguageCnt() uint32 {
	return m.guildRecOtherLanguageCount
}

func (m *GuildConfigInfoManager) GetGuildKickOutLimit() uint32 {
	return m.guildKickOutLimit
}

func (m *GuildConfigInfoManager) GetDungeonFirstWeekScoreChapterPer() uint32 {
	return m.dungeonFirstWeekScoreChapterPer
}

func (m *GuildConfigInfoManager) GetDungeonFirstWeekScoreChapterStep() uint32 {
	return m.dungeonFirstWeekScoreChapterStep
}

func (m *GuildConfigInfoManager) GetDungeonFirstWeekScoreChapterStepPer() uint32 {
	return m.dungeonFirstWeekScoreChapterStepPer
}

func (m *GuildConfigInfoManager) GetDungeonChapterScoreChapterPer() uint32 {
	return m.dungeonChapterScoreChapterPer
}

func (m *GuildConfigInfoManager) GetDungeonChapterScoreChapter() uint32 {
	return m.dungeonChapterScoreStartValue
}

func (m *GuildConfigInfoManager) IsGuildDungeonSuspended(now int64) bool {
	if now < m.GetGuildDungeonOpenTime() {
		return true
	}
	return false
}

func (m *GuildConfigInfoManager) RegimentCountMax() uint32 {
	return m.regimentCountMax
}

func (m *GuildConfigInfoManager) GetGuildMedalFlowerCount() uint32 {
	return m.guildMedalFlowerCount
}

func (m *GuildConfigInfoManager) GetGuildMedalLikeCosts() []*cl.Resource {
	costs := make([]*cl.Resource, 0, 1)
	flower := m.guildChestFlowerID.Clone()
	if flower != nil {
		flower.Count = m.GetGuildMedalFlowerCount()
		costs = append(costs, flower)
	}
	return costs
}

func (m *GuildConfigInfoManager) GetGuildMedalOpenLevel() uint32 {
	return m.medalOpenLevel
}

func (m *GuildConfigInfoManager) GetStrategyChapterRestoreBossLimit() uint32 {
	return m.strategyChapterRestoreBossLimit
}

func (m *GuildConfigInfoManager) GetGuildRecYesterdayNum() uint32 {
	return m.guildRecYesterdayNum
}
func (m *GuildConfigInfoManager) GetGuildRecPassYesterdayNum() uint32 {
	return m.guildRecPassYesterdayNum
}

func (m *GuildConfigInfoManager) GetTransferOpenCreateDayLimit() uint32 {
	return m.guildTransferOpenCreateDayLimit
}

func (m *GuildConfigInfoManager) GetTransferOpenMemberLimit() uint32 {
	return m.guildTransferOpenMemberLimit
}

func (m *GuildConfigInfoManager) GetTransferListMemberLimit() uint32 {
	return m.guildTransferListMemberLimit
}

func (m *GuildConfigInfoManager) GetQuickJoinYesterdayNumLimit() uint32 {
	return m.guildQuickJoinYesterdayNumLimit
}

func (m *GuildConfigInfoManager) GetGuildCombineOpenDay() uint32 {
	return m.guildCombineOpenDay
}

func (m *GuildConfigInfoManager) GetGuildCombineApplyDuration() int64 {
	return int64(m.guildCombineApplyExpireTime)
}

func (m *GuildConfigInfoManager) GetGuildCombineApplySendMax() uint32 {
	return m.guildCombineApplySendMax
}

func (m *GuildConfigInfoManager) GetGuildCombineApplyReceiveMax() uint32 {
	return m.guildCombineApplyReceiveMax
}

func (m *GuildConfigInfoManager) GetLeaderCheckActiveDay() int {
	return int(m.PresidentCheckActiveDays)
}

func (m *GuildConfigInfoManager) GuildMobilizationCheckLevel(guildLv uint32) bool {
	return guildLv >= m.GuildMobilizationGuildLevel
}

func (m *GuildConfigInfoManager) GetGuildMobDeleteTimes() int64 {
	return m.GuildMobDeleteTimes
}
