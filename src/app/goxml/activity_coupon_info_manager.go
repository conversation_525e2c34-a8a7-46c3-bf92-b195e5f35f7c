package goxml

/*import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityCouponInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*AwardGroup
}

type AwardGroup struct {
	SysID        uint32
	BeginTime    int64
	EndTime      int64
	DayCostAward [][]*DayCostAward
	Template     uint32
}

type DayCostAward struct {
	AwardType uint32
	Cost      []*cl.Resource
	Award     []*cl.Resource
	InternaID uint32
	SdkPID    string
	Off       string
}

func (ag *AwardGroup) GetAwardInfo(day, awardType uint32) *DayCostAward {
	if int(day) > len(ag.DayCostAward) {
		return nil
	}
	if int(awardType) > len(ag.DayCostAward[day-1]) {
		return nil
	}
	return ag.DayCostAward[day-1][awardType-1]
}

func (ag *AwardGroup) ConvertXml() *cl.ActivityCouponXml {
	if ag == nil {
		return nil
	}
	couponXML := &cl.ActivityCouponXml{
		ActId:    ag.SysID,
		OpenDay:  ag.BeginTime,
		CloseDay: ag.EndTime,
		Template: ag.Template,
	}
	for day, dayAwards := range ag.DayCostAward {
		for _, dayAward := range dayAwards {
			var cloneAwardClone []*cl.Resource
			for _, award := range dayAward.Award {
				cloneAwardClone = append(cloneAwardClone, award.Clone())
			}
			var costAwardClone []*cl.Resource
			for _, cost := range dayAward.Cost {
				costAwardClone = append(costAwardClone, cost.Clone())
			}
			resDay := uint32(day + 1)
			couponXML.Res = append(couponXML.Res, &cl.ActivityCouponRes{
				Day:        resDay,
				Award:      cloneAwardClone,
				Cost:       costAwardClone,
				AwardType:  dayAward.AwardType,
				InternalId: dayAward.InternaID,
				SdkPid:     dayAward.SdkPID,
				Off:        dayAward.Off,
			})
		}
	}
	return couponXML
}

func newActivityCouponInfoManager(xmlData *XmlData) *ActivityCouponInfoManager {
	m := &ActivityCouponInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityCouponInfoManager) name() string {
	return "activity_coupon_info.xml"
}

func (m *ActivityCouponInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityCouponInfoManager) checkData() error {
	return nil
}

func (m *ActivityCouponInfoManager) load(dir string, show bool) error {
	tmp := &ActivityCouponInfos{}
	fileName := filepath.Join(dir, "activity_coupon_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*AwardGroup, len(tmp.Datas))
	dayCheck := uint32(1)
	TypeCheck := uint32(ActivityCouponFree)
	dataLen := len(tmp.Datas)
	dayLen := dataLen / 3
	if dataLen%3 != 0 {
		panic(fmt.Sprintf("conifg(%s): data len is error", fileName))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		group, exist := m.Datas[data.ActivityId]
		if !exist {
			time, err := StringDateToTime(data.Date)
			if err != nil {
				panic(fmt.Sprintf("config:%s date %s fail: %s", fileName, data.Date, err))
			}
			m.Datas[data.ActivityId] = &AwardGroup{
				SysID:        data.ActivityId,
				BeginTime:    time.Unix(),
				DayCostAward: make([][]*DayCostAward, dayLen),
				Template:     data.Template,
			}
			group = m.Datas[data.ActivityId]
		}
		if data.Day != dayCheck || data.Type != TypeCheck || data.Template != group.Template {
			panic(fmt.Sprintf("config:%s data id:%d day:%d or type:%d template:%d is error", fileName, data.Id, data.Day, data.Type, data.Template))
		}

		if data.Type == ActivityCouponRecharge {
			for _, res := range data.RewardClRes {
				if res.Type != uint32(common.RESOURCE_COUPON) {
					panic(fmt.Sprintf("config:%s data id:%d award type id error", fileName, data.Id))
				}
			}
		}

		tmpAward := &DayCostAward{
			AwardType: data.Type,
			Award:     data.RewardClRes,
			InternaID: data.InternalId,
			SdkPID:    data.SdkPid,
			Off:       data.Off,
		}
		if data.Cost > 0 {
			tmpAward.Cost = []*cl.Resource{
				{
					Type:  uint32(common.RESOURCE_DIAMOND),
					Count: data.Cost,
				},
			}
		}

		group.DayCostAward[data.Day-1] = append(group.DayCostAward[data.Day-1], tmpAward)
		TypeCheck++
		if TypeCheck > ActivityCouponRecharge {
			dayCheck++
			TypeCheck = ActivityCouponFree
		}
	}
	for _, data := range m.Datas {
		data.EndTime = data.BeginTime + int64(util.DaySecs*len(data.DayCostAward))
	}
	return nil
}

func (m *ActivityCouponInfoManager) Index(key uint32) *AwardGroup {
	return m.Datas[key]
}

func (m *ActivityCouponInfoManager) CheckOpenActivity(now int64) *AwardGroup {
	var ret *AwardGroup
	for _, data := range m.Datas {
		if now >= data.BeginTime && now < data.EndTime {
			ret = data
			break
		}
	}
	return ret
}
*/
