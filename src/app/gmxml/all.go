package gmxml

var (
	QuestionnaireInfoM     = new(QuestionnaireInfoManager)
	GiftCodeInfoM          = new(GiftCodeInfoManager)
	GmConfigInfoM          = new(GmConfigInfoManager)
	OperateActivityInfoM   = new(OperateActivityInfoManager)
	OperateGiftInfoM       = new(OperateGiftInfoManager)
	PromotionGiftInfoM     = new(PromotionGiftInfoManager)
	OperateTaskInfoM       = new(OperateTaskInfoManager)
	DivineDemonInfoM       = new(DivineDemonInfoManager)
	AnnouncementInfoM      = new(AnnouncementInfoManager)
	DailyWishAwardInfoM    = new(DailyWishAwardInfoManager)
	DailyWishActivityInfoM = new(DailyWishActivityInfoManager)
	ArtifactDebutInfoM     = new(ArtifactDebutInfoManager)
	DropActivityInfoM      = new(DropActivityInfoManager)
	PyramidActivityInfoM   = new(PyramidActivityInfoManager)
	SelectSummonInfoM      = new(SelectSummonInfoManager)
	ActivityCouponInfoM    = new(ActivityCouponInfoManager)
	PokemonSummonInfoM     = new(PokemonSummonInfoManager)
)

// xml加载模式
//
//true:gmxml false:策划配表xml
var (
// drawInfoIsGmXml = true
)

func Load(dir string, show bool) {
	GiftCodeInfoM.Load(dir, show)
	QuestionnaireInfoM.Load(dir, show)
	GmConfigInfoM.Load(dir, show)
	OperateActivityInfoM.Load(dir, show)
	OperateGiftInfoM.Load(dir, show)
	OperateTaskInfoM.Load(dir, show)
	PromotionGiftInfoM.Load(dir, show)
	DivineDemonInfoM.Load(dir, show)
	AnnouncementInfoM.Load(dir, show)
	DailyWishAwardInfoM.Load(dir, show)
	DailyWishActivityInfoM.Load(dir, show)
	ArtifactDebutInfoM.Load(dir, show)
	DropActivityInfoM.Load(dir, show)
	PyramidActivityInfoM.Load(dir, show)
	SelectSummonInfoM.Load(dir, show)
	ActivityCouponInfoM.Load(dir, show)
	PokemonSummonInfoM.Load(dir, show)
}

//目的：
//对于从后台配置的文件，统一goxml和gmxml,对逻辑层直接提供gmxml的访问
//同时在开发阶段(后台发布gmxml未实现时)，通过此函数加载goxml的配置
/*
func LoadDevXml(dir string, show bool) {
	if !drawInfoIsGmXml {
		loadDraw(dir, show)
	}
}
*/
