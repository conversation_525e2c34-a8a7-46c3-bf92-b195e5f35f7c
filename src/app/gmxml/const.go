package gmxml

import "gitlab.qdream.com/kit/sea/util"

const (
	OperateTaskEventRecordNone             = 0
	OperateTaskEventRecordFromServiceOpen  = 1
	OperateTaskEventRecordFromActivityOpen = 2
)

const (
	OperateActivityDeleteDelay = util.DaySecs * OperateActivityDeleteDay
	ArtifactDebutDeleteDelay   = util.DaySecs * 30
)

const (
	OperateActivityDeleteDay = 14
)

const (
	OperateActivityDeleteCountLimit = 20
)

const (
	DailyWishActivityNone    = 0
	DailyWishActivityOnline  = 1
	DailyWishActivityOffline = 2
)

// 获取发布状态
const (
	OpStatusRelease uint32 = 1 //发布
	OpStatusOff     uint32 = 2 //下架
)

const (
	OperateActivityTask      uint32 = 3 //配置任务
	OperateActivityGift      uint32 = 4 //配置礼包
	PromotionGiftRebate      uint32 = 6 //折扣
	PromotionGiftPick        uint32 = 7 //自选
	PromotionGiftProgression uint32 = 8 //连续
)

const (
	OperateActivityTaskNormal uint32 = 0 //正常任务
	OperateActivityTaskRound  uint32 = 1 //轮次任务
)

// 掉落活动
const (
	DropActivityDeleteDelay = util.DaySecs * 30 //30天后清理过期数据
	DropActivityFlushLimit  = 5                 //获取掉落活动数量上限
)

// 金字塔活动
const (
	PyramidActivityDeleteDelay = util.DaySecs * 30 //30天后清理过期数据
	PyramidActivityFlushLimit  = 5                 //获取活动数量上限
)

const (
	CouponActivityDeleteDelay = util.DaySecs * 30
	CouponActivityFlushLimit  = 5
)
