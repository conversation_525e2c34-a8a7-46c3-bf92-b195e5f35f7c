package gmxml

import (
	"app/protos/out/cl"
	"fmt"
	"os"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityCouponInfos struct {
	Datas []*ActivityCouponInfo `xml:"data"`
}

type ActivityCouponInfoManager struct {
	fileName string
	datas    []*ActivityCouponInfoExt
}

func (dm *ActivityCouponInfoManager) Load(air string, show bool) {
	dm.fileName = filepath.Join(air, "coupon_activity_info.xml")
	if _, err := os.Stat(dm.fileName); err != nil && os.IsNotExist(err) {
		l4g.Infof("coupon activity config(%s) not exist", dm.fileName)
		return
	}

	tmp := &ActivityCouponInfos{}
	if err := util.LoadConfig(dm.fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", dm.fileName, err))
	} else {
		if show {
			for _, data := range tmp.Datas {
				l4g.Debug("config(%s): %+v", dm.fileName, data)
			}
		}
	}

	//初始化数据&清理过期数据
	dm.initDatas(tmp.Datas)

	for _, data := range dm.datas {
		//检查时间重叠，避免xml文件被手动误修改，造成的错误
		if data.State == OpStatusRelease && !dm.checkTimeOverlap(data.ActId, data.OpenDay, data.CloseDay) {
			panic(fmt.Sprintf("load config %s fail: checkTimeOverlap failed", dm.fileName))
		}
		l4g.Debugf("CouponActivityInfoM.load: data:%+v", data)
	}
}

// 初始化数据&清理过期数据
func (dm *ActivityCouponInfoManager) initDatas(datas []*ActivityCouponInfo) {
	dm.datas = make([]*ActivityCouponInfoExt, 0, len(datas))
	now := time.Now().Unix()
	var needSave bool
	for _, data := range datas {
		if data.CloseDay+CouponActivityDeleteDelay < now {
			needSave = true
			l4g.Infof("ActivityCouponInfoManager.initDatas: data:%+v", data)
			continue
		}

		res := couponRulesJson2Slice(data.Res)
		if res == nil {
			panic(fmt.Sprintf("ActivityCouponInfoManager.initDatas: rulesJson2Slice failed, id:%d", data.ActID))
		}

		dataExt := &ActivityCouponInfoExt{
			ActId:    data.ActID,
			OpenDay:  data.OpenDay,
			CloseDay: data.CloseDay,
			State:    data.State,
			Res:      res,
		}
		dm.datas = append(dm.datas, dataExt)
	}

	if needSave {
		if !dm.save() {
			panic("ActivityCouponInfoM.initDatas: save failed")
		}
	}
}

// 检查时间重叠
func (dm *ActivityCouponInfoManager) checkTimeOverlap(id uint32, startTime, endTime int64) bool {
	if endTime <= startTime {
		l4g.Errorf("ActivityCouponInfoManager.checkTimeOverlap: time illegal, startTime:%d, endTime:%d",
			startTime, endTime)
		return false
	}

	now := time.Now().Unix()
	for _, v := range dm.datas {
		//不对自身做检查
		if v.ActId == id {
			continue
		}

		//加载时，过滤掉过期和下线的活动
		if v.IsDisable(now) {
			continue
		}

		//验证时间区间
		if startTime > v.CloseDay || endTime < v.OpenDay {
			continue
		}

		l4g.Errorf("ActivityCouponInfoM.checkTimeOverlap: failed, startTime:%d, endTime:%d, data:%+v",
			startTime, endTime, v)
		return false
	}

	return true
}

func (dm *ActivityCouponInfoManager) saveUpdate() bool {
	return dm.save()
}

func (dm *ActivityCouponInfoManager) saveAdd(gmData *cl.ActivityCouponXml) bool {
	dm.datas = append(dm.datas, (*ActivityCouponInfoExt)(gmData))
	success := dm.save()
	if !success {
		dm.delete(gmData.ActId)
		l4g.Errorf("ActivityCouponInfoManager.saveAdd failed, id: %d", gmData.ActId)
	}
	return success
}

func (dm *ActivityCouponInfoManager) delete(id uint32) {
	for k, v := range dm.datas {
		if v.ActId == id {
			dm.datas = append(dm.datas[:k], dm.datas[k+1:]...)
			break
		}
	}
}

func (dm *ActivityCouponInfoManager) save() bool {
	saveDatas, isPass := dm.clone()
	if !isPass {
		l4g.Errorf("ActivityCouponInfoManager.save: clone failed")
		return false
	}

	if err := util.SaveConfig(dm.fileName, saveDatas); err != nil {
		l4g.Errorf("ActivityCouponInfoManager.save: config %s fail: %s", dm.fileName, err)
		return false
	}
	return true
}

func (dm *ActivityCouponInfoManager) clone() (*ActivityCouponInfos, bool) {
	tmp := &ActivityCouponInfos{
		Datas: make([]*ActivityCouponInfo, 0, len(dm.datas)),
	}
	for _, data := range dm.datas {
		v := data.clone()
		if v == nil {
			return nil, false
		}
		tmp.Datas = append(tmp.Datas, v)
	}
	return tmp, true
}

func (dm *ActivityCouponInfoManager) Get(id uint32) *ActivityCouponInfoExt {
	for _, v := range dm.datas {
		if id == v.ActId {
			return v
		}
	}
	return nil
}

// 同步活动数据
func (dm *ActivityCouponInfoManager) SyncActivity(data *cl.ActivityCouponXml, now int64) bool {
	activity := dm.Get(data.ActId)
	if activity == nil {
		//新活动，必须是发布状态
		if data.State == OpStatusOff {
			l4g.Errorf("ActivityCouponInfoM.SyncActivity: new activity OpStatus illegal, data:%+v", data)
			return false
		}
		return dm.addActivity(data)
	}

	return dm.updateActivity(data, activity, now)
}

// 添加新活动
func (dm *ActivityCouponInfoManager) addActivity(data *cl.ActivityCouponXml) bool {
	if !dm.checkTimeOverlap(data.ActId, data.OpenDay, data.CloseDay) {
		l4g.Errorf("ActivityCouponInfoM.addActivity: checkTimeOverlap failed, data:%+v", data)
		return false
	}

	return dm.saveAdd(data)
}

// 更新已有活动
func (dm *ActivityCouponInfoManager) updateActivity(data *cl.ActivityCouponXml,
	activity *ActivityCouponInfoExt, now int64) bool {
	//不允许修改已过期或已下架的活动
	if activity.IsDisable(now) {
		l4g.Errorf("ActivityCouponInfoM.updateActivity: activity has disabled, data:%+v", data)
		return false
	}

	if activity.IsActive(now) {
		//正在运行中的活动，仅可修改结束时间和下架
		if data.State == OpStatusOff {
			//下架
			activity.SetOff()
		} else {
			//修改结束时间
			if !dm.checkTimeOverlap(activity.ActId, activity.OpenDay, data.CloseDay) {
				l4g.Errorf("ActivityCouponInfoM.updateActivity: checkTimeOverlap failed, data:%+v", data)
				return false
			}
			activity.UpdateEndTime(data.CloseDay)
		}
	} else {
		//未开始的活动
		if !dm.checkTimeOverlap(activity.ActId, data.OpenDay, data.CloseDay) {
			l4g.Errorf("ActivityCouponInfoM.updateActivity: checkTimeOverlap failed, data:%+v", data)
			return false
		}
		activity.UpdateBeforeRelease(data)
	}
	return dm.saveUpdate()
}

// 获取全部有效的活动基础数据
// 修正处于新服保护期内的活动开始时间
// @param int64 serverOpenTm 服务器开服时间
// @return []*cl.ActivityCouponXml
func (dm *ActivityCouponInfoManager) Flush() []*cl.ActivityCouponXml {
	now := time.Now().Unix()
	ret := make([]*cl.ActivityCouponXml, 0, len(dm.datas))

	for _, v := range dm.datas {
		if v.IsDisable(now) {
			continue
		}

		xml := v.trans2CouponActivityBase()

		ret = append(ret, xml)
		if len(ret) >= CouponActivityFlushLimit {
			break
		}
	}
	return ret
}

// 根据时间，获取当前活动
// tips: 同一时段，只会有一个活动
// @param int64 serverOpenTm 服务器开服时间
// @retrun *cl.ActivityCouponXml
func (dm *ActivityCouponInfoManager) GetCurrentActivity() *cl.ActivityCouponXml {
	now := time.Now().Unix()

	for _, v := range dm.datas {
		if v.IsActive(now) {
			return v.trans2CouponActivityBase()
		}
	}

	return nil
}

// 根据唯一id，获取活动
func (dm *ActivityCouponInfoManager) GetActivityByUniqID(id uint32) *cl.ActivityCouponXml {
	for _, v := range dm.datas {
		if v.ActId == id {
			return v.trans2CouponActivityBase()
		}
	}
	return nil
}
