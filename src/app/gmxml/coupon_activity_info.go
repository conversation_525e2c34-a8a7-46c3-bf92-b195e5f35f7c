package gmxml

import (
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityCouponInfo struct {
	ActID    uint32 `xml:"act_id,attr"`    //活动唯一id，由gm生成
	OpenDay  int64  `xml:"open_day,attr"`  //开始时间
	CloseDay int64  `xml:"close_day,attr"` //结束时间
	Res      string `xml:"res,attr"`       //资源
	Template uint32 `xml:"template,attr"`  //模版ID
	State    uint32 `xml:"state,attr"`     //状态
}

func couponRulesJson2Slice(rulesStr string) []*cl.ActivityCouponRes {
	var rules []*cl.ActivityCouponRes
	err := betterJson.Unmarshal([]byte(rulesStr), &rules)
	if err != nil {
		l4g.Errorf("couponRulesJson2Slice.rulesJson2Slice: unmarshal error: %v %s", err, rulesStr)
		return nil
	}
	return rules
}

func couponRulesSlice2Json(rules []*cl.ActivityCouponRes) string {
	bytes, err := betterJson.Marshal(rules)
	if err != nil {
		l4g.Errorf("couponRulesSlice2Json.rulesSlice2Json: marshal error:%v %v", err, rules)
		return ""
	}

	str := util.String(bytes)

	l4g.Debugf("couponRulesSlice2Json.rulesSlice2Json: str:%s", str)
	return str
}

type ActivityCouponInfoExt cl.ActivityCouponXml

func (a *ActivityCouponInfoExt) clone() *ActivityCouponInfo {
	resStr := couponRulesSlice2Json(a.Res)
	if resStr == "" {
		l4g.Errorf("couponRulesSlice2Json.clone: rulesSlice2Json failed, id:%d",
			a.ActId)
		return nil
	}
	return &ActivityCouponInfo{
		ActID:    a.ActId,
		OpenDay:  a.OpenDay,
		CloseDay: a.CloseDay,
		Res:      resStr,
		Template: a.Template,
		State:    a.State,
	}
}

func (a *ActivityCouponInfoExt) trans2CouponActivityBase() *cl.ActivityCouponXml {
	return &cl.ActivityCouponXml{
		ActId:    a.ActId,
		OpenDay:  a.OpenDay,
		CloseDay: a.CloseDay,
		Res:      a.Res,
		Template: a.Template,
		State:    a.State,
	}
}

// 是否是已废弃的活动（下架或过期）
func (d *ActivityCouponInfoExt) IsDisable(tm int64) bool {
	if d.isOff() {
		return true
	}
	return d.isExpired(tm)
}

// 是否是正在运营的活动
func (d *ActivityCouponInfoExt) IsActive(tm int64) bool {
	if d.isOff() {
		return false
	}
	return d.isInTime(tm)
}

// 是否处于活动时间内
func (a *ActivityCouponInfoExt) isInTime(tm int64) bool {
	return tm >= a.OpenDay && tm <= a.CloseDay
}

// 是否过期了
func (a *ActivityCouponInfoExt) isExpired(tm int64) bool {
	return tm > a.CloseDay
}

// 是否已下架
func (a *ActivityCouponInfoExt) isOff() bool {
	return a.State == OpStatusOff
}

// 活动未开始前，可以修改全部数据
func (a *ActivityCouponInfoExt) UpdateBeforeRelease(data *cl.ActivityCouponXml) {
	a.ActId = data.ActId
	a.OpenDay = data.OpenDay
	a.CloseDay = data.CloseDay
	a.State = data.State
	a.Template = data.Template
	a.Res = data.Res
}

// 处于活动时段内，仅能修改结束时间，且不能早于当前时间
func (a *ActivityCouponInfoExt) UpdateEndTime(endTime int64) {
	a.CloseDay = endTime
}

// 设置下架
func (a *ActivityCouponInfoExt) SetOff() {
	a.State = OpStatusOff
}
