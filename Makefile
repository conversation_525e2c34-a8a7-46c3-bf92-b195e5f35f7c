PROJECT_PATH := $(shell pwd)

SPLIT_LINE := "\033[44;32m >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> \033[0m "

export PROJECT_PATH

CURRENT_TIME := $(shell date +"%Y%m%d%H%M%S")

TARGET_VERSION ?= $(strip ${CURRENT_TIME})

all: app

all-race: app-race

kit: download xmlc redisop protoclone

download:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd tools && ./download_kit.sh

download_knil:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd tools && ./download_knil.sh

xmlc:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/kit/xmlc && GOOS=darwin GOARCH=amd64 go build -o $(PROJECT_PATH)/tools/xmlc

knil: download_knil
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cp src/knil/knil/knil_config.yaml src/app/
	cd src/knil/knil/cmd/singleknil && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/src/app/knil

redisop:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/kit/redisop/cmd &&  go build -o $(PROJECT_PATH)/tools/redisop

protoclone:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/kit/protoclone &&  go build -o $(PROJECT_PATH)/tools/protoclone

app: xml protos db version service repair

appi-race: xml protos db version service-race repair

init: xml_git protos_git db version service tools

init-race: xml_git protos_git db version service-race tools
init-linter: xml_git protos_git db version linter

hotfix:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	./tools/build_hotfix.sh

hotfix_simple:
	#make hotfix_simple PARAM=friend
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	./tools/build_hotfix_simple.sh $(PARAM)

xml:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd tools && ./build_xml_data_check_repeat.sh
	./tools/build_xml_data.sh

xml_git:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	./tools/build_xml_data.sh -int

.PHONY: protos version
protos:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd deps && ./build.sh
	./tools/build_proto_go.sh

protos_git:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd deps && ./build.sh
	./tools/build_proto_go.sh -init

db:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	./tools/build_db_data.sh
	./tools/build_cr_data.sh

version:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	bash version
	bash tools/codefmt.sh

linter:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	bash tools/golinter.sh

service-fast: xml_git protos_git db serviceFast

serviceFast:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/service app/service/cmd

service-plugin:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	@echo target${TARGET_VERSION}=0
	mkdir src/app/service/cmd${TARGET_VERSION}
	cp src/app/service/cmd/service.go src/app/service/cmd${TARGET_VERSION}/
	cd src/app && GOOS=linux GOARCH=amd64 go build -buildmode=plugin -o $(PROJECT_PATH)/bin/service${TARGET_VERSION}.so app/service/cmd${TARGET_VERSION}
	rm src/app/service/cmd${TARGET_VERSION} -rf

service:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	bash tools/codefmt.sh
	#bash tools/logcheck.sh
	bash tools/check_user_save_clone.sh
	#bash tools/check_db_save.sh
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/service app/service/cmd

service-race:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	bash tools/codefmt.sh
	bash tools/logcheck.sh
	bash tools/check_user_save_clone.sh
	cd src/app && GOOS=linux GOARCH=amd64 go build -race -o $(PROJECT_PATH)/bin/service app/service/cmd


.PHONY: tools
tools:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	find src/app/tools -iname "*.go" | xargs gofmt -w -s
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/robot app/tools/robot
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/mirror app/tools/mirror
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/cross-robot app/tools/cross-robot
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/redis-pb-cli app/tools/redis-pb-cli/cli
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/repair app/tools/repair
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/crossRepair app/tools/crossRepair
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/mockRobot app/tools/mockRobot
	cd src/app && GOOS=windows GOARCH=amd64 go build -ldflags="-H windowsgui -w -s" -o $(PROJECT_PATH)/bin/data-check.exe app/tools/data-check
	cd src/app && GOOS=windows GOARCH=amd64 go build -ldflags="-H windowsgui -w -s" -o $(PROJECT_PATH)/bin/link-summon-simulation.exe app/tools/link-summon-simulation
	cd src/app/tools/data-check/tools && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/data-check-linux
	cd src/app/tools/benchmarkagent && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/benchmark
	cd src/app/tools/treerobot && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/treerobot
	cd src/app/tools/log-consumer && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/log-consumer

repair:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/repair app/tools/repair
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/crossRepair app/tools/crossRepair

crossgrpc:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/crossgrpc app/tools/crossgrpc

monitor-log:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/monitor-log app/tools/monitor-log

cross-robot:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/cross-robot app/tools/cross-robot

data-check:
	cd src/app && GOOS=windows GOARCH=amd64 go build -ldflags="-H windowsgui -w -s" -o $(PROJECT_PATH)/bin/data-check.exe app/tools/data-check

test:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && go test ./...

race:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -race -o $(PROJECT_PATH)/bin/service app/service/cmd

gateway:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	-cd src/kit/gateway && make deps && make
	-rm bin/gateway2
	-cp src/kit/gateway/src/gateway bin/gateway2
	-cp src/kit/gateway/config/gateway.xml config/

clean: cleangateway

cleangateway:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/kit/gateway && make clean

eagle:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/kit/eagle && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/eagle

parrot:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/kit/parrot/cmd && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/parrot

battle:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app/logic/battle/cmd && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/battle

git_hook:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cp tools/commit-msg .git/hooks/commit-msg
	cp tools/commit-msg protos/out/.git/hooks/commit-msg

logcheck:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app/tools/logcheck &&  go build -o $(PROJECT_PATH)/tools/logcheck

data-check-linux: xml protos db
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app/tools/data-check/tools && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/data-check-linux

robot:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/robot app/tools/robot

mockRobot:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/mockRobot app/tools/mockRobot


divineRobot:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app && GOOS=linux GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/divineRobot app/tools/divine_demon_summon


cross-partition:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app/tools/cross-partition &&  go build -o $(PROJECT_PATH)/tools/cross-partition

db-save-check:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app/tools/db-save-check &&  go build -o $(PROJECT_PATH)/tools/db-save-check

treerobot:
	@echo -e $(SPLIT_LINE)"\033[33m$@\033[0m"
	cd src/app/tools/treerobot && GOOS=darwin GOARCH=amd64 go build -o $(PROJECT_PATH)/bin/treerobot
